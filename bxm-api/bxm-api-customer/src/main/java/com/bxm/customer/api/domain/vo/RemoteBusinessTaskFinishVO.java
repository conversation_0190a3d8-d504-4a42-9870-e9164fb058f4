package com.bxm.customer.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:05
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteBusinessTaskFinishVO {

    private Long id;

    @ApiModelProperty(value = "完成结果")
    private Integer finishResult;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty(value = "备注")
    private String remark;

    private Long userId;

    private Long deptId;

    private String operName;
}
