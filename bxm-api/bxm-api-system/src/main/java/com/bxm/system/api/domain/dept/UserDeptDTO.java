package com.bxm.system.api.domain.dept;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDeptDTO {

    /** 是否管理员 */
    private Boolean isAdmin;

    /** 部门类型，1-业务，2-工厂 */
    private Integer deptType;

    /** 可见的部门id列表 */
    private List<Long> deptIds;
}
