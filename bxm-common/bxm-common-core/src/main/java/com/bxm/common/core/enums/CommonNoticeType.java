package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonNoticeType {

    INCOME(1, "income"),
    INVOICE(2, "invoice"),
    IN_ACCOUNT(3, "inAccount"),
    IN_ACCOUNT_V2(4, "inAccountV2"),
    TAX_CHECK(5, "taxCheck"),
    INSURANCE_CHECK(6, "insuranceCheck"),
    INVOICE_UPDATE(7, "invoiceUpdate"),
    IN_ACCOUNT_V3(8, "inAccountV3"),
    GET_BANK_RECEIPT_FILE(9, "getBankReceiptFile"),
    CHECK_BANK_RECEIPT_FILE(10, "checkBankReceiptFile"),
    GENERATE_VOUCHER(11, "generateVoucher"),
    BANK_RECEIPT_PAPER_FILE_UPLOAD(12, "bankReceiptPaperFileUpload"),
    PERSONAL_INCOME_TAX_DECLARATION(13, "personalIncomeTtaxDeclaration"),
    PERSONAL_INCOME_TAX_DEDUCTION(14, "personalIncomeTtaxDeduction"),
    PERSONAL_INCOME_TAX_CHECK(15, "personalIncomeTtaxCheck"),
    PERSONAL_INCOME_TAX_DOWNLOAD(16, "personalIncomeTtaxDownload"),
    PERSONAL_INCOME_TAX_CHECK_STATUS(17, "personalIncomeTtaxCheckStatus"),
    PERSONAL_INCOME_TAX_DOWNLOAD_CHECK(18, "personalIncomeTtaxDownloadCheck"),
    QUALITY_TESTING(19, "qualityTesting"),
    UN_KNOW(99, "un_know")
    ;

    private final Integer type;

    private final String noticeCode;

    public static CommonNoticeType getByNoticeCode(String noticeCode) {
        for (CommonNoticeType noticeType : CommonNoticeType.values()) {
            if (noticeType.getNoticeCode().equals(noticeCode)) {
                return noticeType;
            }
        }
        return UN_KNOW;
    }
}
