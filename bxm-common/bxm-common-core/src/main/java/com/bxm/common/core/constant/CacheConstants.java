package com.bxm.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    public static final String RPA_EXCEPTION_LIST = "rpa:exceptionList:";

    public static final String RPA_ORIGIN_LIST = "rpa:originList:";

    public static final String RPA_TOTAL_DATA_COUNT = "rpa:totalDataCount:";

    public static final String RPA_TOTAL_FILE_COUNT = "rpa:totalFileCount:";

    public static final String RPA_COMPLETE_FILE_COUNT = "rpa:completeFileCount:";

    public static final String RPA_CHECK_RESULT = "rpa:checkResult:";

    public static final String RPA_TYPE = "rpa:type:";

    public static final String BATCH_DELIVER_CHECK_RESULT = "batchDeliver:checkResult:";

    public static final String BATCH_DELIVER_CONFIRM_RESULT = "batchDeliver:confirmResult:";

    public static final String BATCH_DELIVER_CHECK_LIST_RESULT = "batchDeliver:checkResult:list:";

    public static final String BATCH_DELIVER_TOTAL_FILE_COUNT = "batchDeliver:totalFileCount:";

    public static final String BATCH_DELIVER_COMPLETE_FILE_COUNT = "batchDeliver:completeFileCount:";

    public static final String BATCH_DELIVER_CONFIRM_SUCCESS_COUNT = "batchDeliver:confirmSuccessCount:";

    public static final String BATCH_DELIVER_CONFIRM_DEAL_COUNT = "batchDeliver:confirmDealCount:";

    public static final String BATCH_DELIVER_CONFIRM_FAIL_COUNT = "batchDeliver:confirmFailCount:";

    public static final String BATCH_TRANSFER_CHECK_RESULT = "transfer:batchTransfer:checkResult:";


    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    public static final String COMPANY_WECHAT_ACCESS_TOKEN = "company:wechat:accessToken:";
    public static final String WECHAT_APP_CONFIG_CACHE_KEY = "wechat:app:config";
    public static final String CITY_INFO = "cityInfo";
    public static final String URGE_LOCK_KEY = "urge:deliver:";
    public static final String SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT = "settlementOrder:upload:period:checkResult:";
    public static final String SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT_LIST = "settlementOrder:upload:period:checkResult:list:";
    public static final String SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_DATA_COUNT = "settlementOrder:upload:period:checkData:count:";
    public static final String SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT = "settlementOrder:upload:customerService:checkResult:";
    public static final String SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT_LIST = "settlementOrder:upload:customerService:checkResult:list:";
    public static final String SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_DATA_COUNT = "settlementOrder:upload:customerService:checkData:count:";
    public static final String CUSTOMER_BATCH_SEARCH_RESULT_LIST = "customer:batch:search:result:list:";
    public static final String MATERIAL_DELIVER_OPERATE_ERROR_RECORD = "materialDeliver:operate:error:";
    public static final String ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD = "accountingCashier:operate:error:";
    public static final String QUALITY_CHECKING_RESULT_OPERATE_ERROR_RECORD = "qualityCheckingResult:operate:error:";
    public static final String QUALITY_CHECKING_RECORD_OPERATE_ERROR_RECORD = "qualityCheckingRecord:operate:error:";
    public static final String PERIOD_MONTH_OPERATE_ERROR_RECORD = "periodMonth:operate:error:";
    public static final String MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY = "material:deliver:push:preview:list:";
    public static final String MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY = "material:deliver:push:preview:error:list:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_COUNT = "material:deliver:retry:analysis:total:count:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT = "material:deliver:retry:analysis:complete:count:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT = "material:deliver:retry:analysis:success:count:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT = "material:deliver:retry:analysis:fail:count:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_IS_COMPLETE = "material:deliver:retry:analysis:isComplete:";
    public static final String MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_DATA_COUNT = "material:deliver:retry:analysis:totalData:count:";
    public static final String PERSON_TAX_REPORT_KEY = "personTax:rpa:%s";
    public static final String PERSON_TAX_DEDUCTION_KEY = "personTax:rpa:%s";
    public static final String PERSON_TAX_CHECK_KEY = "personTax:rpa:%s";
    public static final String PERSON_TAX_TABLE_DOWNLOAD_KEY = "personTax:rpa:%s";
    public static final String PERSON_TAX_STATUS_SEARCH_KEY = "personTax:rpa:%s";
    public static final String RPA_GET_PROFIT_KEY = "rpa:getProfit:%s";
    public static final String RPA_INVOICE_UPDATE_KEY = "rpa:invoiceUpdate:%s";
    public static final String RPA_BANK_FLOW_KEY = "rpa:bankFlow:%s";
    public static final String ERROR_CODE_KEY = "errorCode:";
    public static final String NOTICE_URL = "ysbNoticeUrl";

    /**
     * 批量操作异常数据缓存前缀
     */
    public static final String BATCH_OPERATION_ERROR_RECORD = "ValueAddedBatchOperation:error:";

    /**
     * 员工批量导入校验成功数据缓存前缀
     */
    public static final String EMPLOYEE_BATCH_VALIDATION_SUCCESS = "EmployeeBatchValidation:success:";

    /**
     * 员工批量导入校验失败数据缓存前缀
     */
    public static final String EMPLOYEE_BATCH_VALIDATION_FAIL = "EmployeeBatchValidation:fail:";
}
