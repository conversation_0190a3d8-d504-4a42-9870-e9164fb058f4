package com.bxm.common.core.enums.workOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Getter
@AllArgsConstructor
public enum WorkOrderType {

    // 工单类型，1-装订账本，2-找材料，3-对账，4-申报更正，5-申报表，6-做账要求，7-统计资料，8-催账，9-催库存，10-添加实名人员，11-补录初期，12-材料补充，13-账务问题，14-其他提醒，15-系统需求，16-改账，17-回单，18-信息维护，99-未知
    UNKNOWN(99, "未知"),
    BIND_BOOK(1, "装订账本"),
    FIND_MATERIAL(2, "找材料"),
    CHECK_ACCOUNT(3, "对账"),
    CORRECT_REPORT(4, "申报更正"),
    REPORT_FORM(5, "申报表"),
    ACCOUNT_REQUIREMENT(6, "做账要求"),
    STATISTICAL_DATA(7, "统计资料"),
    COLLECT_DEBT(8, "催账"),
    COLLECT_STOCK(9, "催库存"),
    ADD_REAL_NAME(10, "添加实名人员"),
    SUPPLEMENT_INITIAL(11, "补录初期"),
    SUPPLEMENT_MATERIAL(12, "材料补充"),
    ACCOUNTING_PROBLEM(13, "账务问题"),
    OTHER_REMINDER(14, "其他提醒"),
    SYSTEM_REQUIREMENT(15, "系统需求"),
    CHANGE_ACCOUNT(16, "改账"),
    REPLY_BILL(17, "回单"),
    INFORMATION_MAINTENANCE(18, "信息维护"),
    COMPLAINT(19, "投诉"),
    DOUBLE_REPORT_REPAIR(20, "双报补报"),
    ;

    private final Integer code;

    private final String desc;

    public static WorkOrderType getByCode(Integer code) {
        for (WorkOrderType workOrderType : WorkOrderType.values()) {
            if (workOrderType.getCode().equals(code)) {
                return workOrderType;
            }
        }
        return UNKNOWN;
    }

    // 需要分配给装订组的类型
    public static List<Integer> zdzTypes() {
//        return Arrays.asList(BIND_BOOK.getCode());
        return Collections.emptyList();
    }

    // 需要分配给材料组的类型
    public static List<Integer> clzTypes() {
        return Arrays.asList(FIND_MATERIAL.getCode());
    }

    // 需要分配给产品运营组的类型
    public static List<Integer> cpyyTypes() {
        return Arrays.asList(SYSTEM_REQUIREMENT.getCode());
    }

    // 回单中心组
    public static List<Integer> hdzxTypes() {
        return Arrays.asList(REPLY_BILL.getCode());
    }

    // 客服组
    public static List<Integer> kfzTypes() {
        return Arrays.asList(COMPLAINT.getCode());
    }

    // 需要分配给服务会计组的类型
    public static List<Integer> customerAccountingTypes() {
        // 对账 至 催库存
        return Arrays.asList(BIND_BOOK.getCode(), CHECK_ACCOUNT.getCode(), CORRECT_REPORT.getCode(), REPORT_FORM.getCode(), ACCOUNT_REQUIREMENT.getCode(),
                STATISTICAL_DATA.getCode(), COLLECT_DEBT.getCode(), COLLECT_STOCK.getCode(), CHANGE_ACCOUNT.getCode(), DOUBLE_REPORT_REPAIR.getCode());
    }

    // 需要分配给服务顾问组的类型
    public static List<Integer> customerAdvisorTypes() {
        // 添加实名人员 至 其他提醒
        return Arrays.asList(ADD_REAL_NAME.getCode(), SUPPLEMENT_INITIAL.getCode(), SUPPLEMENT_MATERIAL.getCode(), ACCOUNTING_PROBLEM.getCode(),
                INFORMATION_MAINTENANCE.getCode());
    }

    // 根据发起方决定接收方，工厂发起，服务顾问接收；业务公司发起，会计接收
    public static List<Integer> customerAccountingAdvisorTypes() {
        return Arrays.asList(OTHER_REMINDER.getCode());
    }

    public static List<Integer> needCustomerServiceTypes() {
        return Arrays.asList(BIND_BOOK.getCode(), FIND_MATERIAL.getCode(), CHECK_ACCOUNT.getCode(), CORRECT_REPORT.getCode(), REPORT_FORM.getCode(), ACCOUNT_REQUIREMENT.getCode(),
                STATISTICAL_DATA.getCode(), COLLECT_DEBT.getCode(), COLLECT_STOCK.getCode(), ADD_REAL_NAME.getCode(), SUPPLEMENT_INITIAL.getCode(), SUPPLEMENT_MATERIAL.getCode(), ACCOUNTING_PROBLEM.getCode(), OTHER_REMINDER.getCode(),
                CHANGE_ACCOUNT.getCode(), INFORMATION_MAINTENANCE.getCode(), DOUBLE_REPORT_REPAIR.getCode());
    }

    public static List<Integer> needPeriodTypes() {
        return Arrays.asList(BIND_BOOK.getCode(), CHECK_ACCOUNT.getCode(), CORRECT_REPORT.getCode(), REPORT_FORM.getCode(), STATISTICAL_DATA.getCode(), COLLECT_DEBT.getCode(), COLLECT_STOCK.getCode(), SUPPLEMENT_MATERIAL.getCode(), CHANGE_ACCOUNT.getCode());
    }

    public static List<Integer> needRemarkTypes() {
        return Arrays.asList(BIND_BOOK.getCode(), FIND_MATERIAL.getCode(), CORRECT_REPORT.getCode(), REPORT_FORM.getCode(), COLLECT_DEBT.getCode(), ADD_REAL_NAME.getCode(), SUPPLEMENT_MATERIAL.getCode(), INFORMATION_MAINTENANCE.getCode(), OTHER_REMINDER.getCode(), COMPLAINT.getCode(), DOUBLE_REPORT_REPAIR.getCode());
    }
}
