package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessLogBusinessType {

    CUSTOMER_SERVICE(1, "客户服务"),
    CUSTOMER_SERVICE_DELIVER(2, "交付单"),
    CUSTOMER_SERVICE_PERIOD(3, "账期"),
    CUSTOMER_SERVICE_PERIOD_INCOME(4, "收入"),
    // 新户流转 5
    NEW_CUSTOMER_TRANSFER(5, "新户流转"),
    BORROW_ORDER(6, "材料借阅单"),

    SETTLEMENT_ORDER(7, "结算单"),
    BILL(8, "账单"),
    CUSTOMER_SERVICE_PERIOD_YEAR(9, "年度账期"),
    WORK_ORDER(10, "任务工单"),
    MATERIAL_DELIVER(11, "材料交接单"),
    ACCOUNTING_CASHIER(12, "账务"),
    ACCOUNTING_CASHIER_ATTACHMENT(13, "材料交接单-文件"),
    MATERIAL_DELIVER_PERIOD_INVENTORY(14, "材料交接单-账期清单"),
    BUNDLE(15, "套餐"),
    DEPT(16, "组织"),
    ROLE(17, "角色"),
    ACCOUNT(18, "账号"),
    QUALITY_CHECKING_RESULT(19, "质检结果"),
    QUALITY_CHECKING_RECORD(20, "质检记录"),

    DOC_HANDOVER(50, "材料交接"),
    IN_ACCOUNT(51, "入账交付"),
    REPAIR_ACCOUNT(52, "补账服务"),
    TASK(53, "任务"),
    ;

    private final Integer code;

    private final String name;
}
