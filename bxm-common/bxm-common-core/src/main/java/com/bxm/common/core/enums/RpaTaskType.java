package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RpaTaskType {

    // 新户初始化,申报表下载,月结账下载报表,银行账号更新,全年结账,银行流水银企下载通知,查询银企入账结果,电子材料导入通知,查询电子材料入账结果,发票入账结果查询,个税申报,个税扣款,国税申报,材料处理,利润取数,发票入账,凭证生成
    NEW_USER_INITIALIZATION("newUserInitialization", "新户初始化"),
    MONTHLY_STATEMENT_DOWNLOAD("monthlyStatementDownload", "月结账下载报表"),
    BANK_ACCOUNT_UPDATE("bankAccountUpdate", "银行账号更新"),
    ANNUAL_STATEMENT("annualStatement", "全年结账"),
    BANK_STATEMENT_DOWNLOAD("bankStatementDownload", "银行流水银企下载通知"),
    QUERY_BANK_STATEMENT_RESULT("queryBankStatementResult", "查询银企入账结果"),
    ELECTRONIC_MATERIALS_IMPORT("electronicMaterialsImport", "电子材料导入通知"),
    QUERY_ELECTRONIC_MATERIALS_RESULT("queryElectronicMaterialsResult", "查询电子材料入账结果"),
    INVOICE_INTO_ACCOUNT("invoiceIntoAccount", "发票入账结果查询"),
    PERSONAL_TAX_DECLARATION("personalTaxDeclaration", "个税申报"),
    PERSONAL_TAX_DEDUCTION("personalTaxDeduction", "个税扣款"),
    PERSONAL_TAX_CHECK("personalTaxCheck", "个税检查"),
    PERSONAL_TAX_DECLARATION_TABLE_DOWNLOAD("personTaxDeclarationTableDownload", "个税申报表下载"),
    PERSONAL_TAX_STATUS_SEARCH("personTaxStatusSearch", "个税状态查询"),
    PERSONAL_TAX_REPORT_STATUS_SEARCH("personTaxReportStatusSearch", "个税申报状态查询"),
    PERSONAL_TAX_DEDUCTION_STATUS_SEARCH("personTaxDeductionStatusSearch", "个税扣款状态查询"),
    PERSONAL_TAX_CHECK_STATUS_SEARCH("personTaxCheckStatusSearch", "个税检查状态查询"),
    GOVERNMENT_TAX_DECLARATION("governmentTaxDeclaration", "国税申报"),
    MATERIAL_PROCESSING("materialProcessing", "材料处理"),
    PROFIT_QUERY("profitQuery", "利润取数"),
    INVOICE_INTO_ACCOUNT_QUERY("invoiceIntoAccountQuery", "发票入账"),
    VOUCHER_GENERATION("voucherGeneration", "凭证生成"),
    SEARCH_TASK_STATUS("searchTaskStatus", "查询任务状态"),
    REPORT_TABLE_DOWNLOAD_SEARCH("reportTableDownloadSearch", "申报表下载查询"),
    SEARCH_COMPANY_INFO("searchCompanyInfo", "查询企业信息"),
    REPORT_DEDUCTION_LIST("reportDeductionList", "查询已申报已扣款列表"),
    QUALITY_CHECKING("qualityChecking", "质检"),
    YSB_NOTICE("ysbNotice", "通知医社保"),
    ;

    private final String code;

    private final String taskTypeName;

    public static String getTypeNameByCode(String code) {
        for (RpaTaskType taskType : values()) {
            if (taskType.getCode().equals(code)) {
                return taskType.getTaskTypeName();
            }
        }
        return "";
    }
}
