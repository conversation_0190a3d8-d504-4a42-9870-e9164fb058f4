package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OpenApiAppRelations {

    XQY("k8m9uniy93imsqoz8x2o", "6S2hnGJ2Gnn0Y8HGXcL3Dc7j", "xqy", "鑫启易", 1),
    COMMON_2("1d011gprj97q9cjifg5l", "9Xqg0PaYcsnZTyGOUh3DDSlW", "common", "医社保", 2),
    XQY_SELF("h7ou5JSY2FxPvimMV59E", "df9912KDsasdsSDDS1SFDdfa", "xqy", "鑫启易", 3),
    TEST("vs3ii6TyfHhaHm33qFSB", "iwz9byl0CPjBsSjAyc2FpF28", "test", "测试专用", 4),
    ;

    private final String appId;

    private final String appSecret;

    private final String tag;

    private final String name;

    private final Integer id;

    public static OpenApiAppRelations getByAppId(String appId) {
        for (OpenApiAppRelations openApiAppRelations : OpenApiAppRelations.values()) {
            if (openApiAppRelations.getAppId().equals(appId)) {
                return openApiAppRelations;
            }
        }
        return null;
    }
}
