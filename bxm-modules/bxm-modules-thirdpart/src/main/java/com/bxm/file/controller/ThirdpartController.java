package com.bxm.file.controller;

import com.bxm.common.core.domain.R;
import com.bxm.common.core.domain.Result;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.file.dto.CompanyInfoDTO;
import com.bxm.file.service.ThirdpartService;
import com.bxm.thirdpart.api.domain.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/thirdpart")
public class ThirdpartController {

    @Autowired
    private ThirdpartService thirdpartService;

    @GetMapping("/getCompanyInfoByKeyWord")
    @ApiOperation("企查查-模糊查询企业信息")
    public R<List<CompanyInfoDTO>> getCompanyInfoByKeyWord(@RequestParam("keyWord") String keyWord) {
        return R.ok(thirdpartService.searchCompanyByKeyWord(keyWord));
    }

    @GetMapping("/getCompanyWechatUserInfo")
    public Result<WechatUserInfo> getCompanyWechatUserInfo(@RequestParam("code") String code,
                                                           @RequestParam("corpId") String corpId,
                                                           @RequestParam("corpSecret") String corpSecret) {
        return Result.ok(thirdpartService.getCompanyWechatUserInfo(code, corpId, corpSecret));
    }

    @PostMapping("/sendCompanyWechatMessage")
    public Result sendCompanyWechatMessage(@RequestBody WechatSendMessageVO vo) {
        thirdpartService.sendCompanyWechatMessage(vo.getCorpId(), vo.getCorpSecret(), vo.getContent());
        return Result.ok();
    }

    @PostMapping("/uploadFileToCompanyWechat")
    public Result<String> uploadFileToCompanyWechat(@RequestParam("file") MultipartFile file,
                                                    @RequestParam("corpId") String corpId,
                                                    @RequestParam("corpSecret") String corpSecret) {
        return Result.ok(thirdpartService.uploadFileToCompanyWechat(file, corpId, corpSecret));
    }

    @GetMapping("/getCompanyWechatAccessToken")
    public Result<String> getCompanyWechatAccessToken(@RequestParam("corpId") String corpId,
                                                            @RequestParam("corpSecret") String corpSecret) {
        return Result.ok(thirdpartService.getCompanyWechatAccessToken(corpId, corpSecret));
    }

    @GetMapping("/xqyQueryTaxDeclarationData")
    public Result<List<Map<String, Object>>> xqyQueryTaxDeclarationData(@RequestParam("taxNumber") String taxNumber, @RequestParam("declareMonth") String declareMonth) {
        return Result.ok(thirdpartService.xqyQueryTaxDeclarationData(taxNumber, declareMonth));
    }

    @GetMapping("/xqySocialSecurityStatementDetailsQuery")
    public Result<List<Map<String, Object>>> xqySocialSecurityStatementDetailsQuery(@RequestParam("taxNumber") String taxNumber, @RequestParam("belongMonth") String belongMonth) {
        return Result.ok(thirdpartService.xqySocialSecurityStatementDetailsQuery(taxNumber, belongMonth));
    }

    @GetMapping("/xqyTaxItemConfirmQuery")
    public Result<List<Map<String, Object>>> xqyTaxItemConfirmQuery(@RequestParam("taxNumber") String taxNumber) {
        return Result.ok(thirdpartService.xqyTaxItemConfirmQuery(taxNumber));
    }

    @GetMapping("/xqyQueryVATData")
    public Result<Map<String, Object>> xqyQueryVATData(@RequestParam("taxNumber") String taxNumber, @RequestParam("belongMonth") String belongMonth, @RequestParam(value = "purchaseInvoiceScope", required = false) String purchaseInvoiceScope) {
        return Result.ok(thirdpartService.xqyQueryVATData(taxNumber, belongMonth, purchaseInvoiceScope));
    }

    @GetMapping("/xqyInvoiceStatistic")
    public Result<Map<String, Object>> xqyInvoiceStatistic(@RequestParam("taxNumber") String taxNumber, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        return Result.ok(thirdpartService.xqyInvoiceStatistic(taxNumber, startDate, endDate));
    }

    @PostMapping("/xqySetCustomerStatus")
    @InnerAuth
    public Result<Boolean> xqySetCustomerStatus(@RequestBody XqySetCustomerStatusVO vo) {
        return Result.ok(thirdpartService.xqySetCustomerStatus(vo));
    }

    @PostMapping("/xqySetCustomerServiceUser")
    @InnerAuth
    public Result<Boolean> xqySetCustomerServiceUser(@RequestBody XqySetCustomerServiceUser vo) {
        return Result.ok(thirdpartService.xqySetCustomerServiceUser(vo));
    }

    @PostMapping("/rpaInAccount")
    @InnerAuth
    public Result<Boolean> rpaInAccount(@RequestBody RpaInAccountVO vo) {
        return Result.ok(thirdpartService.rpaInAccount(vo));
    }

    @PostMapping("/accountingCashierGetProfit")
    @InnerAuth
    public Result<Boolean> accountingCashierGetProfit(@RequestBody RpaInAccountVO vo) {
        return Result.ok(thirdpartService.accountingCashierGetProfit(vo));
    }

    @PostMapping("/incomeOutput")
    @InnerAuth
    public Result<Boolean> incomeOutput(@RequestBody IncomeOutputVO vo) {
        return Result.ok(thirdpartService.incomeOutput(vo));
    }

    /**
     * 银企-提取
     * @param vo
     * @return
     */
    @PostMapping("/banksEnterprisesExtract")
    @InnerAuth
    public Result<BanksEnterprisesExtractDTO> banksEnterprisesExtract(@RequestBody BanksEnterprisesExtractVO vo) {
        return Result.ok(thirdpartService.banksEnterprisesExtract(vo));
    }

    /**
     * 检验文件(纸质和银企通用)
     * @param vo
     * @return
     */
    @PostMapping("/checkBankReceiptFile")
    @InnerAuth
    public Result<CheckFilesDTO> checkBankReceiptFile(@RequestBody CheckFilesVO vo) {
        return Result.ok(thirdpartService.checkBankReceiptFile(vo));
    }

    /**
     * 凭证生成(纸质和银企通用)
     * @param vo
     * @return
     */
    @PostMapping("/generateVoucher")
    @InnerAuth
    public Result<GenerateVoucherDTO> generateVoucher(@RequestBody GenerateVoucherVO vo) {
        return Result.ok(thirdpartService.generateVoucher(vo));
    }

    /**
     * 纸质回单上传（新增银行账号）
     * @param vo
     * @return
     */
    @PostMapping("/bankReceiptPaperFileUpload")
    @InnerAuth
    public Result<BankReceiptPaperFileUploadDTO> bankReceiptPaperFileUpload(@RequestBody BankReceiptPaperFileUploadVO vo) {
        return Result.ok(thirdpartService.bankReceiptPaperFileUpload(vo));
    }

    /**
     * 查询任务状态
     * @param uuid
     * @return
     */
    @GetMapping("/searchTaskStatus")
    @InnerAuth
    public Result<String> searchTaskStatus(@RequestParam("uuid") String uuid) {
        return Result.ok(thirdpartService.searchTaskStatus(uuid));
    }

    // 个税申报
    @PostMapping("/personTaxReport")
    @InnerAuth
    public Result<Boolean> personTaxReport(@RequestBody PersonTaxRpaVO vo) {
        return Result.ok(thirdpartService.personTaxReport(vo));
    }

    // 个税扣款
    @PostMapping("/personTaxDeduction")
    @InnerAuth
    public Result<Boolean> personTaxDeduction(@RequestBody PersonTaxRpaVO vo) {
        return Result.ok(thirdpartService.personTaxDeduction(vo));
    }

    // 个税检查
    @PostMapping("/personTaxCheck")
    @InnerAuth
    public Result<Boolean> personTaxCheck(@RequestBody PersonTaxRpaVO vo) {
        return Result.ok(thirdpartService.personTaxCheck(vo));
    }

    // 个税申报表下载
    @PostMapping("/personTaxReportDownload")
    @InnerAuth
    public Result<Boolean> personTaxReportDownload(@RequestBody PersonTaxRpaVO vo) {
        return Result.ok(thirdpartService.personTaxReportDownload(vo));
    }

    // 个税状态查询
    @PostMapping("/personTaxStatusSearch")
    @InnerAuth
    public Result<Boolean> personTaxStatusSearch(@RequestBody PersonTaxRpaVO vo) {
        return Result.ok(thirdpartService.personTaxStatusSearch(vo));
    }

    @PostMapping("/rePush")
    @InnerAuth
    public Result<Boolean> rePush(@RequestBody String noticeContent) {
        return Result.ok(thirdpartService.rePush(noticeContent));
    }

    @PostMapping("/personalIncomeTtaxDownloadCheck")
    @InnerAuth
    public Result<Boolean> personalIncomeTtaxDownloadCheck(@RequestBody ReportTableDownloadSearchVO vo) {
        return Result.ok(thirdpartService.personalIncomeTtaxDownloadCheck(vo));
    }

    @GetMapping("/getCompanyInfoByTaxNumberYsb")
    @InnerAuth
    public Result<YsbCompanyInfoDTO> getCompanyInfoByTaxNumberYsb(@RequestParam("taxNumber") String taxNumber) {
        return Result.ok(thirdpartService.getCompanyInfoByTaxNumberYsb(taxNumber));
    }

    @PostMapping("/getReportDeductionList")
    @InnerAuth
    public Result<ReportDeductionGetDTO> getReportDeductionList(@RequestBody ReportDeductionGetVO vo) {
        return Result.ok(thirdpartService.getReportDeductionList(vo));
    }

    /**
     * 质检
     * @param vo
     * @return
     */
    @PostMapping("/qualityChecking")
    @InnerAuth
    public Result<Boolean> qualityChecking(@RequestBody QualityCheckingVO vo) {
        return Result.ok(thirdpartService.qualityChecking(vo));
    }

    @PostMapping("/ysbNotice")
    @InnerAuth
    public Result ysbNotice(@RequestBody YsbNoticeVO vo) {
        thirdpartService.ysbNotice(vo);
        return Result.ok();
    }
}
