package com.bxm.file.service;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.ThirdPartUrlConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.RpaTaskType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.file.domain.OpenApiNoticeRecord;
import com.bxm.file.dto.CompanyInfoDTO;
import com.bxm.file.properties.XqyProperties;
import com.bxm.system.api.domain.LoginVO;
import com.bxm.thirdpart.api.domain.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ThirdpartService {

    public static final String PROD_NOTIFY_URL = "https://zbb.sikilab.com/bxmOpenApi/openapi/commonNotice";

    public static final String TEST_NOTIFY_URL = "https://bxmtest.sikilab.com/bxmOpenApi/openapi/commonNotice";

    public static final Map<String, String> NOTIFY_URL_MAP;

    static {
        Map<String, String> tempMap = new HashMap<>();
        tempMap.put("test", TEST_NOTIFY_URL);
        tempMap.put("dev", TEST_NOTIFY_URL);
        tempMap.put("prod", PROD_NOTIFY_URL);
        NOTIFY_URL_MAP = Collections.unmodifiableMap(tempMap);
    }

    @Autowired
    private RedisService redisService;

    @Value("${qcc.appkey}")
    private String appkey;

    @Value("${qcc.appsecret}")
    private String appsecret;

    @Value("${spring.profiles.active}")
    private String environment;

    @Autowired
    private XqyProperties xqyProperties;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    public List<CompanyInfoDTO> searchCompanyByKeyWord(String keyword) {
        String reqInterNme = ThirdPartUrlConstants.QCC_COMPANY_SEARCH_URL;
        String paramStr = "searchKey=" + keyword + "&pageSize=20";
        String status = "";
        try {
            String[] autherHeader = RandomAuthentHeader();
            Map<String, String> headers = new HashMap<>();
            headers.put("Token", autherHeader[0]);
            headers.put("Timespan", autherHeader[1]);
            String reqUri = reqInterNme.concat("?key=").concat(appkey).concat("&").concat(paramStr);
            log.info(String.format("==========================>企查查模糊查询url:{%s}", reqUri));
            HttpRequest request = HttpRequest.get(reqUri);
            request.addHeaders(headers);
            String body = request.execute().body();
            log.info(String.format("==========================>企查查模糊查询response:{%s}", body));

            JSONObject jObject = JSONObject.parseObject(body);
            status = jObject.getString("Status");
            log.info(String.format("==========================>企查查模糊查询Status:{%s}", status));
            if (!Objects.equals(status, "200")) {
                log.error(String.format("==================企查查模糊查询请求失败,msg:{%s}", FormartJson(body, "Message")));
                return Lists.newArrayList();
            }
            String result = jObject.getString("Result");
            if (StringUtils.isEmpty(result)) {
                return Lists.newArrayList();
            }
            return JSONObject.parseArray(result, CompanyInfoDTO.class);
        } catch (Exception e1) {
            e1.printStackTrace();
            return Lists.newArrayList();
        }
    }

    // 获取Auth Code
    private String[] RandomAuthentHeader() {
        String timeSpan = String.valueOf(System.currentTimeMillis() / 1000);
        String[] authentHeaders = new String[] { DigestUtils.md5Hex(appkey.concat(timeSpan).concat(appsecret)).toUpperCase(), timeSpan };
        return authentHeaders;
    }

    // 解析JSON
    private String FormartJson(String jsonString, String key) {
        JSONObject jObject = JSONObject.parseObject(jsonString);
        return (String) jObject.get(key);
    }

    public WechatUserInfo getCompanyWechatUserInfo(String code, String corpId, String corpSecret) {
        String accessToken = getGlobalCompanyWechatAccessToken(corpId, corpSecret);
        String url = String.format("https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=%s&code=%s", accessToken, code);
        log.info("获取用户userId请求url：{}", url);
        HttpRequest request = HttpRequest.get(url);
        WechatUserInfoResponse response = JSONObject.parseObject(request.execute().body(), WechatUserInfoResponse.class);
        if (response != null && response.getErrcode() == 0) {
            return response;
        } else {
            log.error("获取用户userId失败：{}", null == response ? "response is null" : response.getErrmsg());
            throw new ServiceException("获取用户userId失败 ");
        }
    }

    private String getAccessToken(String corpId, String corpSecret) {
        String accessToken = redisService.getCacheObject(CacheConstants.COMPANY_WECHAT_ACCESS_TOKEN + corpId);
        if (!StringUtils.isEmpty(accessToken)) {
            return accessToken;
        }
        String url = UriComponentsBuilder.fromHttpUrl("https://qyapi.weixin.qq.com/cgi-bin/gettoken")
                .queryParam("corpid", corpId)
                .queryParam("corpsecret", corpSecret)
                .toUriString();

        WechatTokenResponse response = restTemplate.getForObject(url, WechatTokenResponse.class);
        if (response != null && response.getErrcode() == 0) {
            redisService.setCacheObject(CacheConstants.COMPANY_WECHAT_ACCESS_TOKEN + corpId, response.getAccess_token(), (long) response.getExpires_in(), TimeUnit.SECONDS);
            return response.getAccess_token();
        } else {
            log.error("获取accessToken失败：{}", null == response ? "response is null" : response.getErrmsg());
            throw new ServiceException("获取accessToken失败");
        }
    }

    public void sendCompanyWechatMessage(String corpId, String corpSecret, String content) {
        String accessToken = getGlobalCompanyWechatAccessToken(corpId, corpSecret);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;

        String response = HttpRequest.post(url).body(content).execute().body();
        if (null == response) {
            log.error("消息发送失败：response is null");
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
            log.error("消息发送失败：{}", jsonObject.getString("errmsg"));
        }
    }

    private String getSign(String timestamp, String bizData) throws Exception {
        return getSign(timestamp, xqyProperties.getAppid(), bizData, xqyProperties.getAppsecret());
    }

    private static String getSignV2(String timestamp, String bizData, String appId, String appsecret) throws Exception {
        return getSign(timestamp, appId, bizData, appsecret);
    }

    private static String getSign(String timestamp, String appId, String bizData, String appSecret) throws Exception {
        Map<String, String> map = new TreeMap<>();
        map.put("timestamp", timestamp);
        map.put("appid", appId);
        map.put("bizData", bizData);
        map.put("appsecret", appSecret);

        String signData = getSignData(map);
//        log.info("==========================>signData:{}", signData);
        return hmacSHA256(signData, appSecret);
    }

    private static String hmacSHA256(String data, String secret) throws Exception {
        Mac sha256Mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
        sha256Mac.init(secretKey);
        byte[] encryptedBytes = sha256Mac.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }



    private static String getSignData(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        map.values().forEach(value -> {
            if (!StringUtils.isEmpty(value)) {
                builder.append(value.trim());
            }
        });
        return builder.toString();
    }

    private String getGlobalCompanyWechatAccessToken(String corpId, String corpSecret) {
        String response = HttpRequest.get(String.format("https://zbb.sikilab.com/bxmThirdpart/thirdpart/getCompanyWechatAccessToken?corpId=%s&corpSecret=%s", corpId, corpSecret)).execute().body();
        Result result = JSONObject.parseObject(response, Result.class);
        if (!Objects.isNull(result) && Objects.equals(result.getCode(), Result.SUCCESS)) {
            return result.getData().toString();
        }
        return "";
    }

    public String getCompanyWechatAccessToken(String corpId, String corpSecret) {
        return getAccessToken(corpId, corpSecret);
    }

    public String uploadFileToCompanyWechat(MultipartFile file, String corpId, String corpSecret) {
        String accessToken = getGlobalCompanyWechatAccessToken(corpId, corpSecret);
        if (StringUtils.isEmpty(accessToken)) {
            throw new ServiceException("获取accessToken失败，请稍后重试");
        }
        String url = String.format("https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=%s&type=file", accessToken);
        String body;
        try {
            body = HttpRequest.post(url).form("media", file.getBytes(), file.getOriginalFilename()).execute().body();
        } catch (IOException e) {
            log.error("上传素材失败：{}", e.getMessage());
            throw new ServiceException("上传素材失败，请稍后重试");
        }
        if (StringUtils.isEmpty(body)) {
            throw new ServiceException("上传素材失败，请稍后重试");
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        if (!Objects.equals(jsonObject.getInteger("errcode"), 0)) {
            throw new ServiceException(jsonObject.getString("errmsg"));
        }
        return jsonObject.getString("media_id");
    }

    public List<Map<String, Object>> xqyQueryTaxDeclarationData(String taxNumber, String declareMonth) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/TaxDeclaration/914/QueryTaxDeclarationData");
        Map<String, Object> params = new HashMap<>();
        params.put("declareMonth", declareMonth);
        params.put("taxNumber", taxNumber);
        params.put("onlyDeclareMonthData", "1");

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        log.info("鑫启易查询结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("鑫启易查询申报情况失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        return JSONObject.parseObject(jsonObject.getString("Value"), List.class);
    }

    public List<Map<String, Object>> xqySocialSecurityStatementDetailsQuery(String taxNumber, String belongMonth) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/TaxServices/914/SocialSecurityStatementDetailsQuery");
        Map<String, Object> params = new HashMap<>();
        params.put("belongMonth", belongMonth);
        params.put("taxNumber", taxNumber);

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        log.info("鑫启易社保对账单查询结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("鑫启易社保对账单查询申报情况失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        return JSONObject.parseObject(jsonObject.getString("Value"), List.class);
    }

    public List<Map<String, Object>> xqyTaxItemConfirmQuery(String taxNumber) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/TaxDeclaration/914/QueryTaxItemConfirm");
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumber", taxNumber);

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        log.info("鑫启易税种认定查询结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("鑫启易税种认定查询申报情况失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        return JSONObject.parseObject(jsonObject.getString("Value"), List.class);
    }

    public Map<String, Object> xqyQueryVATData(String taxNumber, String belongMonth, String purchaseInvoiceScope) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/TaxServices/914/QueryVATData");
        Map<String, Object> params = new HashMap<>();
        params.put("belongMonth", belongMonth);
        params.put("taxNumber", taxNumber);
        params.put("redSalesInvoiceScope", "2");
        params.put("purchaseInvoiceScope", StringUtils.isEmpty(purchaseInvoiceScope) ? "2" : purchaseInvoiceScope);

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        log.info("鑫启易增值税涉税分析查询结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("鑫启易增值税涉税分析查询失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        List<Map<String, Object>> result = JSONObject.parseObject(jsonObject.getString("Value"), List.class);
        return ObjectUtils.isEmpty(result) ? new HashMap<>() : result.get(0);
    }

    public Map<String, Object> xqyInvoiceStatistic(String taxNumber, String startDate, String endDate) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/TaxServices/914/InvoiceStatistics");
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("invoiceTypeID", "0");
        params.put("invoiceStateID", "0");
        params.put("containsInvoiceList", "0");
        params.put("taxNumber", taxNumber);

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        log.info("鑫启易发票统计查询结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("鑫启易发票统计查询失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        List<Map<String, Object>> result = JSONObject.parseObject(jsonObject.getString("Value"), List.class);
        return ObjectUtils.isEmpty(result) ? new HashMap<>() : result.get(0);
    }

    public static void main(String[] args) throws Exception {
        String taxNumber = "91350981MA8RDDLW84";
        HttpRequest request = HttpRequest.get("https://cs.comicani.com/appapi/api/Message/info/" + taxNumber);
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        log.info("查询企业信息,taxNumber:{}", taxNumber);
        Map<String, String> param = new HashMap<>();
        param.put("taxNumber", taxNumber);
        String body = null;
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        YsbCompanyInfoDTO companyInfoDTO;
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            companyInfoDTO = JSONObject.parseObject(jsonObject.getString("data"), YsbCompanyInfoDTO.class);
            System.out.println(companyInfoDTO);
        } else {
            log.error("接口返回错误:{}", body);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean xqySetCustomerStatus(XqySetCustomerStatusVO vo) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/baseSet/914/SetCustomerStatus");
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumber", vo.getTaxNumber());
        params.put("agentStatus", vo.getAgentStatus());

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("鑫启易").setNoticeFunction("客户代理状态设置").setNoticeContent(bizData).setNoticeResult(response).setIsDel(false));
        log.info("客户代理状态设置结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("客户代理状态设置失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        return true;
    }

    public Boolean xqySetCustomerServiceUser(XqySetCustomerServiceUser vo) {
        HttpRequest request = HttpRequest.post("http://api.xmxqy.com:2699/baseSet/914/SetCustomerServiceUser");
        Map<String, Object> params = new HashMap<>();
        params.put("taxNumber", vo.getTaxNumber());
        params.put("jobFunctionName", vo.getJobFunctionName());
        params.put("userName", vo.getUserName());
        params.put("operationType", vo.getOperationType());

        String timestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        String bizData = JSONObject.toJSONString(params);
        String signature;
        try {
            signature = getSign(timestamp, bizData);
        } catch (Exception e) {
            log.error("签名错误:{}", e.getMessage());
            throw new ServiceException("签名错误");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("timestamp", timestamp);
        headers.put("appid", xqyProperties.getAppid());
        headers.put("signature", signature);
        request.addHeaders(headers);
        request.body(bizData);

        String response = request.execute().body();
        openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("鑫启易").setNoticeFunction("客户服务人员设置").setNoticeContent(bizData).setNoticeResult(response).setIsDel(false));
        log.info("客户服务人员设置结果:{}", response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        boolean success = jsonObject.getBoolean("Success");
        if (!success) {
            String errorMsg = jsonObject.getString("Msg");
            log.error("客户服务人员设置失败:{}", errorMsg);
            if (Objects.equals(errorMsg, "并发请求数超限")) {
                // 重试3次 每次等待1s
                for (int i = 0; i < 3; i++) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程中断:{}", e.getMessage());
                    }
                    response = request.execute().body();
                    jsonObject = JSONObject.parseObject(response);
                    success = jsonObject.getBoolean("Success");
                    if (success) {
                        break;
                    } else {
                        errorMsg = jsonObject.getString("Msg");
                        if (!Objects.equals(errorMsg, "并发请求数超限")) {
                            throw new ServiceException(errorMsg);
                        }
                    }
                }
                if (!success) {
                    throw new ServiceException("并发数超限后重试三次仍超限，不处理");
                }
            } else {
                throw new ServiceException(errorMsg);
            }
        }
        return true;
    }

    public Boolean rpaInAccount(RpaInAccountVO vo) {
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", "b7686140747d422d85707de702e3a5e5");
        Map<String, Object> param = new HashMap<>();
        param.put("platType", vo.getPlatType());
        param.put("groupName", vo.getGroupName());
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("creditCode", vo.getCreditCode());
        param.put("operator", vo.getOperator());
        param.put("period", vo.getPeriod());
        param.put("noticeCode", "inAccountV3");
        param.put("operType", vo.getOperType());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("userId", vo.getUserId());
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("发起利润取数,params:{}", bizData);
        String body;
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            return true;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean accountingCashierGetProfit(RpaInAccountVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("platType", vo.getPlatType());
        param.put("groupName", vo.getGroupName());
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("creditCode", vo.getCreditCode());
        param.put("operator", vo.getOperator());
        param.put("period", vo.getPeriod());
        param.put("noticeCode", "inAccountV3");
        param.put("operType", vo.getOperType());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("uuid", uuid);
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("发起利润取数,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("医社保平台").setNoticeFunction("利润取数").setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.PROFIT_QUERY.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
            openApiNoticeRecord.setNoticeResult(body);
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.save(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return true;
        } else {
            log.error("接口返回错误:{}", jsonObject);
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean incomeOutput(IncomeOutputVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("platType", vo.getPlatType());
        param.put("groupName", vo.getGroupName());
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("creditCode", vo.getCreditCode());
        param.put("operator", vo.getOperator());
        param.put("period", vo.getPeriod());
        param.put("noticeCode", "invoiceUpdate");
        param.put("outputFileLink", vo.getOutputFileLink());
        param.put("incomeFileLink", vo.getIncomeFileLink());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("uuid", uuid);
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("发票更新完成,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("医社保平台").setNoticeFunction("发票更新").setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.INVOICE_INTO_ACCOUNT_QUERY.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return true;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public BanksEnterprisesExtractDTO banksEnterprisesExtract(BanksEnterprisesExtractVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "getBankReceiptFile");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("bankNumber", vo.getBankNumber());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("businessTaskId", vo.getBusinessTaskId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("银企-提取,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.BANK_STATEMENT_DOWNLOAD.getTaskTypeName()).setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.BANK_STATEMENT_DOWNLOAD.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return JSONObject.parseObject(body, BanksEnterprisesExtractDTO.class);
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public CheckFilesDTO checkBankReceiptFile(CheckFilesVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "checkBankReceiptFile");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("checkType", vo.getCheckType());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("businessTaskId", vo.getBusinessTaskId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("bankNumber", vo.getBankNumber());
        param.put("taskId", vo.getTaskId());
        param.put("firstRunTime", vo.getFirstRunTime());
        param.put("rpaTaskId", vo.getRpaTaskId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("检验文件,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(vo.getCheckType() == 1 ? RpaTaskType.QUERY_BANK_STATEMENT_RESULT.getTaskTypeName() : RpaTaskType.QUERY_ELECTRONIC_MATERIALS_RESULT.getTaskTypeName()).setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(vo.getCheckType() == 1 ? RpaTaskType.QUERY_BANK_STATEMENT_RESULT.getCode() : RpaTaskType.QUERY_ELECTRONIC_MATERIALS_RESULT.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return JSONObject.parseObject(body, CheckFilesDTO.class);
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public GenerateVoucherDTO generateVoucher(GenerateVoucherVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "generateVoucher");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("businessTaskId", vo.getBusinessTaskId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("bankNumber", vo.getBankNumber());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        param.put("checkType", vo.getCheckType());
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("凭证生成,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.VOUCHER_GENERATION.getTaskTypeName()).setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.VOUCHER_GENERATION.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return JSONObject.parseObject(body, GenerateVoucherDTO.class);
        } else {
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public BankReceiptPaperFileUploadDTO bankReceiptPaperFileUpload(BankReceiptPaperFileUploadVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "bankReceiptPaperFileUpload");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("statementFileLink", vo.getStatementFileLink());
        param.put("platType", vo.getPlatType());
        param.put("bankReceiptFileLink", vo.getBankReceiptFileLink());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("bankNumber", vo.getBankNumber());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("businessTaskId", vo.getBusinessTaskId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("纸质回单上传,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.ELECTRONIC_MATERIALS_IMPORT.getTaskTypeName()).setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.ELECTRONIC_MATERIALS_IMPORT.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return JSONObject.parseObject(body, BankReceiptPaperFileUploadDTO.class);
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public ReportDeductionGetDTO getReportDeductionList(ReportDeductionGetVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.get(String.format("https://cs.comicani.com/appapi/api/Message/gssbkk/list?tfn=%s&month=%s", vo.getTaxNumber(), DateUtils.periodToYeaMonth(vo.getPeriod())));

        String bizData = JSONObject.toJSONString(vo);
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        log.info("获取已申报已扣款列表,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.REPORT_DEDUCTION_LIST.getTaskTypeName()).setNoticeContent(bizData).setNoticeResult(body).setIsDel(false)
                .setIsRpa(true).setBatchNo(uuid).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.REPORT_DEDUCTION_LIST.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        openApiNoticeRecord.setNoticeResult(body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(1);
            openApiNoticeRecord.setSysDeliverResult(1);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return JSONObject.parseObject(jsonObject.getString("data"), ReportDeductionGetDTO.class);
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return null;
        }
    }

    public String searchTaskStatus(String uuid) {
        HttpRequest request = HttpRequest.get("https://cs.comicani.com/appapi/api/Message/ygy/status/" + uuid);
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        log.info("查询任务状态,uuid:{}", uuid);
        Map<String, String> param = new HashMap<>();
        param.put("uuid", uuid);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeContent(JSONObject.toJSONString(param)).setNoticeFunction(RpaTaskType.SEARCH_TASK_STATUS.getTaskTypeName()).setIsDel(false)
                .setIsRpa(false).setTaskType(RpaTaskType.SEARCH_TASK_STATUS.getCode()).setSource("系统").setUuid(StringUtils.getUuid()).setNoticeCondition(true);
        try {
            body = request.execute().body();
            openApiNoticeRecord.setNoticeResult(body);
            openApiNoticeRecord.setRpaDealResult(1);
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        TaskStatusRespDTO taskStatusRespDTO = JSONObject.parseObject(body, TaskStatusRespDTO.class);
        Boolean success = taskStatusRespDTO.getSuccess();
        if (success) {
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return taskStatusRespDTO.getMessage();
        } else {
            log.error("接口返回错误:{}", taskStatusRespDTO.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(taskStatusRespDTO.getMessage());
        }
    }

    public Boolean personTaxReport(PersonTaxRpaVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "personalIncomeTtaxDeclaration");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("platType", vo.getPlatType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("个税申报,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.PERSONAL_TAX_DECLARATION.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.PERSONAL_TAX_DECLARATION.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean personTaxDeduction(PersonTaxRpaVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "personalIncomeTtaxDeduction");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("platType", vo.getPlatType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("个税扣款,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.PERSONAL_TAX_DEDUCTION.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.PERSONAL_TAX_DEDUCTION.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean personTaxCheck(PersonTaxRpaVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "personalIncomeTtaxCheck");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("platType", vo.getPlatType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("个税检查,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.PERSONAL_TAX_CHECK.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.PERSONAL_TAX_CHECK.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean personTaxReportDownload(PersonTaxRpaVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "personalIncomeTtaxDownload");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("platType", vo.getPlatType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("个税申报表下载,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.PERSONAL_TAX_DECLARATION_TABLE_DOWNLOAD.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.PERSONAL_TAX_DECLARATION_TABLE_DOWNLOAD.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean personTaxStatusSearch(PersonTaxRpaVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "personalIncomeTtaxCheckStatus");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
        param.put("platType", vo.getPlatType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("type", vo.getType());
        param.put("uuid", uuid);
        param.put("deliverId", vo.getDeliverId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("taskId", vo.getTaskId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("个税状态查询,params:{}", bizData);
        String body = null;
        RpaTaskType taskType;
        if (vo.getType() == 1) {
            taskType = RpaTaskType.PERSONAL_TAX_REPORT_STATUS_SEARCH;
        } else if (vo.getType() == 2) {
            taskType = RpaTaskType.PERSONAL_TAX_DEDUCTION_STATUS_SEARCH;
        } else {
            taskType = RpaTaskType.PERSONAL_TAX_CHECK_STATUS_SEARCH;
        }
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(taskType.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(taskType.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean rePush(String noticeContent) {
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(noticeContent);
        log.info("重推参数,params:{}", noticeContent);
        String body = null;
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean personalIncomeTtaxDownloadCheck(ReportTableDownloadSearchVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("noticeCode", "personalIncomeTtaxDownloadCheck");
        param.put("uuid", uuid);
        param.put("downloadIds", vo.getDownloadIds());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("申报表下载查询,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.REPORT_TABLE_DOWNLOAD_SEARCH.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(false).setTaskType(RpaTaskType.REPORT_TABLE_DOWNLOAD_SEARCH.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public YsbCompanyInfoDTO getCompanyInfoByTaxNumberYsb(String taxNumber) {
        HttpRequest request = HttpRequest.get(String.format("https://cs.comicani.com/appapi/api/Message/gsxx?tfn=%s", taxNumber));
        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        log.info("查询企业信息,taxNumber:{}", taxNumber);
        Map<String, String> param = new HashMap<>();
        param.put("taxNumber", taxNumber);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("医社保平台").setNoticeContent(JSONObject.toJSONString(param)).setNoticeFunction(RpaTaskType.SEARCH_COMPANY_INFO.getTaskTypeName()).setIsDel(false)
                .setIsRpa(false).setTaskType(RpaTaskType.SEARCH_COMPANY_INFO.getCode()).setSource("系统").setUuid(StringUtils.getUuid()).setNoticeCondition(true);
        try {
            body = request.execute().body();
            openApiNoticeRecord.setNoticeResult(body);
            openApiNoticeRecord.setRpaDealResult(1);
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        YsbCompanyInfoDTO companyInfoDTO;
        JSONObject jsonObject = JSONObject.parseObject(body);
        openApiNoticeRecord.setNoticeResult(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(1);
            openApiNoticeRecord.setSysDeliverResult(1);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            companyInfoDTO = JSONObject.parseObject(jsonObject.getString("data"), YsbCompanyInfoDTO.class);
            return StringUtils.isEmpty(companyInfoDTO.getName()) ? null : companyInfoDTO;
        } else {
            log.error("接口返回错误:{}", body);
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public Boolean qualityChecking(QualityCheckingVO vo) {
        String uuid = StringUtils.getUuid();
        HttpRequest request = HttpRequest.post("https://cs.comicani.com/appapi/api/Message/ygy");
        Map<String, Object> params = new HashMap<>();
        params.put("key", uuid);
        Map<String, Object> param = new HashMap<>();
        param.put("groupName", vo.getGroupName());
        param.put("period", vo.getPeriod());
        param.put("platType", vo.getPlatType());
        param.put("creditCode", vo.getCreditCode());
        param.put("noticeCode", "qualityTesting");
        param.put("groupId", vo.getGroupId());
        param.put("taxNumber", vo.getTaxNumber());
//        param.put("qualityType", vo.getQualityType());
        param.put("customerName", vo.getCustomerName());
        param.put("operator", vo.getOperator());
        param.put("uuid", uuid);
//        param.put("qualityCheckingResultId", vo.getQualityCheckingResultId());
//        param.put("qualityCheckingRecordId", vo.getQualityCheckingRecordId());
        param.put("customerServiceId", vo.getCustomerServiceId());
        param.put("customerServicePeriodMonthId", vo.getCustomerServicePeriodMonthId());
        param.put("userId", vo.getUserId());
        param.put("deptId", vo.getDeptId());
        param.put("notifyUrl", NOTIFY_URL_MAP.get(environment));
        params.put("noticeJson", JSONObject.toJSONString(param));
        params.put("noticeParameter", JSONObject.toJSONString(new HashMap<>()));

        String bizData = JSONObject.toJSONString(params);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("发起质检检查,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("RPA").setNoticeFunction(RpaTaskType.QUALITY_CHECKING.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(true).setBatchNo(vo.getBatchNo()).setCustomerName(vo.getCustomerName()).setCustomerServiceId(vo.getCustomerServiceId()).setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId()).setPeriod(vo.getPeriod()).setTaskType(RpaTaskType.QUALITY_CHECKING.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(0);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            return Boolean.TRUE;
        } else {
            log.error("接口返回错误:uuid:{},message:{}", uuid, jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }

    public void ysbNotice(YsbNoticeVO vo) {
        String uuid = StringUtils.getUuid();
        String url = redisService.getCacheObject(CacheConstants.NOTICE_URL);
        url = StringUtils.isEmpty(url) ? "http://qwcallback.zindall.com/webhook/notice" : url;
        log.info("通知回调地址:{}", url);
        HttpRequest request = HttpRequest.post(url);

        String bizData = JSONObject.toJSONString(vo);

        Map<String, String> headers = new HashMap<>();
        request.addHeaders(headers);
        request.body(bizData);
        log.info("通知医社保,params:{}", bizData);
        String body = null;
        OpenApiNoticeRecord openApiNoticeRecord = new OpenApiNoticeRecord().setNoticeSource("慧进账").setNoticeTarget("医社保平台").setNoticeFunction(RpaTaskType.YSB_NOTICE.getTaskTypeName()).setNoticeContent(bizData).setIsDel(false)
                .setIsRpa(false).setTaskType(RpaTaskType.YSB_NOTICE.getCode()).setSource("系统").setUuid(uuid).setNoticeCondition(true);
        try {
            body = request.execute().body();
        } catch (Exception e) {
            log.error("请求异常:{}", e.getMessage());
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException("请求异常:" + e.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(body);
        openApiNoticeRecord.setNoticeResult(body);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            openApiNoticeRecord.setRpaDealResult(1);
            openApiNoticeRecord.setSysDeliverResult(1);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
        } else {
            log.error("接口返回错误:{}", jsonObject.getString("message"));
            openApiNoticeRecord.setRpaDealResult(2);
            openApiNoticeRecordService.saveRecord(openApiNoticeRecord);
            throw new ServiceException(jsonObject.getString("message"));
        }
    }
}
