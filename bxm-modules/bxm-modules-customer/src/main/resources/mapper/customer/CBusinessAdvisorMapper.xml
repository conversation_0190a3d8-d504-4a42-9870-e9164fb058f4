<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CBusinessAdvisorMapper">
    
    <resultMap type="com.bxm.customer.domain.CBusinessAdvisor" id="CBusinessAdvisorResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="userId"    column="user_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCBusinessAdvisorVo">
        select id, business_type, business_id, user_id, employee_id, dept_id, create_by, create_time, update_by, update_time from c_business_advisor
    </sql>

    <select id="selectCBusinessAdvisorList" parameterType="com.bxm.customer.domain.CBusinessAdvisor" resultMap="CBusinessAdvisorResult">
        <include refid="selectCBusinessAdvisorVo"/>
        <where>  
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectCBusinessAdvisorById" parameterType="Long" resultMap="CBusinessAdvisorResult">
        <include refid="selectCBusinessAdvisorVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCBusinessAdvisor" parameterType="com.bxm.customer.domain.CBusinessAdvisor" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_advisor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCBusinessAdvisor" parameterType="com.bxm.customer.domain.CBusinessAdvisor">
        update c_business_advisor
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCBusinessAdvisorById" parameterType="Long">
        delete from c_business_advisor where id = #{id}
    </delete>

    <delete id="deleteCBusinessAdvisorByIds" parameterType="String">
        delete from c_business_advisor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>