<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverFileInventoryMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliverFileInventory" id="MaterialDeliverFileInventoryResult">
        <result property="id"    column="id"    />
        <result property="materialDeliverId"    column="material_deliver_id"    />
        <result property="materialFileName"    column="material_file_name"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccountNumber"    column="bank_account_number"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="period"    column="period"    />
        <result property="fileNumber"    column="file_number"    />
        <result property="remark"    column="remark"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="analysisResult"    column="analysis_result"    />
        <result property="pushResult"    column="push_result"    />
        <result property="repeatId"    column="repeat_id"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverFileInventoryVo">
        select id, material_deliver_id, material_file_name, customer_name, credit_code, bank_name, bank_account_number, start_date, end_date, period, file_number, remark, file_url, file_size, file_name, customer_service_id, error_msg, analysis_result, push_result, repeat_id, is_del, create_by, create_time, update_by, update_time from c_material_deliver_file_inventory
    </sql>

    <select id="selectMaterialDeliverFileInventoryList" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventory" resultMap="MaterialDeliverFileInventoryResult">
        <include refid="selectMaterialDeliverFileInventoryVo"/>
        <where>  
            <if test="materialDeliverId != null "> and material_deliver_id = #{materialDeliverId}</if>
            <if test="materialFileName != null  and materialFileName != ''"> and material_file_name like concat('%', #{materialFileName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankAccountNumber != null  and bankAccountNumber != ''"> and bank_account_number = #{bankAccountNumber}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="fileNumber != null  and fileNumber != ''"> and file_number = #{fileNumber}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="errorMsg != null  and errorMsg != ''"> and error_msg = #{errorMsg}</if>
            <if test="analysisResult != null "> and analysis_result = #{analysisResult}</if>
            <if test="pushResult != null "> and push_result = #{pushResult}</if>
            <if test="repeatId != null "> and repeat_id = #{repeatId}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverFileInventoryById" parameterType="Long" resultMap="MaterialDeliverFileInventoryResult">
        <include refid="selectMaterialDeliverFileInventoryVo"/>
        where id = #{id}
    </select>
    <select id="selectCanNotPushFileInventory" resultType="com.bxm.customer.domain.MaterialDeliverFileInventory">
        select * from c_material_deliver_file_inventory
        where is_del = 0
        and material_deliver_id in
            <foreach collection="materialDeliverIds" item="materialDeliverId" separator="," open="(" close=")">
                #{materialDeliverId}
            </foreach>
        and (customer_service_id is null or (start_date is null and (period is null or period = '')))
    </select>

    <insert id="insertMaterialDeliverFileInventory" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventory" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver_file_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id,</if>
            <if test="materialFileName != null and materialFileName != ''">material_file_name,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankAccountNumber != null">bank_account_number,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="period != null">period,</if>
            <if test="fileNumber != null">file_number,</if>
            <if test="remark != null">remark,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="analysisResult != null">analysis_result,</if>
            <if test="pushResult != null">push_result,</if>
            <if test="repeatId != null">repeat_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">#{materialDeliverId},</if>
            <if test="materialFileName != null and materialFileName != ''">#{materialFileName},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankAccountNumber != null">#{bankAccountNumber},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="period != null">#{period},</if>
            <if test="fileNumber != null">#{fileNumber},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="analysisResult != null">#{analysisResult},</if>
            <if test="pushResult != null">#{pushResult},</if>
            <if test="repeatId != null">#{repeatId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliverFileInventory" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventory">
        update c_material_deliver_file_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id = #{materialDeliverId},</if>
            <if test="materialFileName != null and materialFileName != ''">material_file_name = #{materialFileName},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankAccountNumber != null">bank_account_number = #{bankAccountNumber},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="period != null">period = #{period},</if>
            <if test="fileNumber != null">file_number = #{fileNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="analysisResult != null">analysis_result = #{analysisResult},</if>
            <if test="pushResult != null">push_result = #{pushResult},</if>
            <if test="repeatId != null">repeat_id = #{repeatId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverFileInventoryById" parameterType="Long">
        delete from c_material_deliver_file_inventory where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverFileInventoryByIds" parameterType="String">
        delete from c_material_deliver_file_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>