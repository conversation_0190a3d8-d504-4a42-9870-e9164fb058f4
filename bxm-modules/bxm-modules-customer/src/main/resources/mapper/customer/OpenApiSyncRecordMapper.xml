<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiSyncRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiSyncRecord" id="OpenApiSyncRecordResult">
        <result property="id"    column="id"    />
        <result property="sourceType"    column="source_type"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="period"    column="period"    />
        <result property="totalDataCount"    column="total_data_count"    />
        <result property="successDataCount"    column="success_data_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiSyncRecordVo">
        select id, source_type, batch_no, period, total_data_count, success_data_count, create_by, create_time, update_by, update_time from c_open_api_sync_record
    </sql>

    <select id="selectOpenApiSyncRecordList" parameterType="com.bxm.customer.domain.OpenApiSyncRecord" resultMap="OpenApiSyncRecordResult">
        <include refid="selectOpenApiSyncRecordVo"/>
        <where>  
            <if test="sourceType != null "> and source_type = #{sourceType}</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="totalDataCount != null "> and total_data_count = #{totalDataCount}</if>
            <if test="successDataCount != null "> and success_data_count = #{successDataCount}</if>
        </where>
    </select>
    
    <select id="selectOpenApiSyncRecordById" parameterType="Long" resultMap="OpenApiSyncRecordResult">
        <include refid="selectOpenApiSyncRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiSyncRecord" parameterType="com.bxm.customer.domain.OpenApiSyncRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_sync_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sourceType != null">source_type,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="period != null">period,</if>
            <if test="totalDataCount != null">total_data_count,</if>
            <if test="successDataCount != null">success_data_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sourceType != null">#{sourceType},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="period != null">#{period},</if>
            <if test="totalDataCount != null">#{totalDataCount},</if>
            <if test="successDataCount != null">#{successDataCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiSyncRecord" parameterType="com.bxm.customer.domain.OpenApiSyncRecord">
        update c_open_api_sync_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="period != null">period = #{period},</if>
            <if test="totalDataCount != null">total_data_count = #{totalDataCount},</if>
            <if test="successDataCount != null">success_data_count = #{successDataCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiSyncRecordById" parameterType="Long">
        delete from c_open_api_sync_record where id = #{id}
    </delete>

    <delete id="deleteOpenApiSyncRecordByIds" parameterType="String">
        delete from c_open_api_sync_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>