<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.QualityCheckingResultMapper">
    
    <resultMap type="com.bxm.customer.domain.QualityCheckingResult" id="QualityCheckingResultResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="period"    column="period"    />
        <result property="qualityCheckingItemId"    column="quality_checking_item_id"    />
        <result property="status"    column="status"    />
        <result property="checkingResult"    column="checking_result"    />
        <result property="lastCheckingTime"    column="last_checking_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQualityCheckingResultVo">
        select id, customer_service_id, customer_service_period_month_id, period, quality_checking_item_id, status, checking_result, last_checking_time, remark, create_by, create_time, update_by, update_time from c_quality_checking_result
    </sql>

    <select id="selectQualityCheckingResultList" parameterType="com.bxm.customer.domain.QualityCheckingResult" resultMap="QualityCheckingResultResult">
        <include refid="selectQualityCheckingResultVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="qualityCheckingItemId != null "> and quality_checking_item_id = #{qualityCheckingItemId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="checkingResult != null "> and checking_result = #{checkingResult}</if>
            <if test="lastCheckingTime != null "> and last_checking_time = #{lastCheckingTime}</if>
        </where>
    </select>
    
    <select id="selectQualityCheckingResultById" parameterType="Long" resultMap="QualityCheckingResultResult">
        <include refid="selectQualityCheckingResultVo"/>
        where id = #{id}
    </select>
    <select id="selectExceptionStatistic"
            resultType="com.bxm.customer.domain.dto.workBench.QualityExceptionStatisticDTO">
        SELECT
            qcr.quality_checking_type AS qualityCheckingType,
            COUNT(qcr.id) AS exceptionCount
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.checking_result = 2 AND qcr.is_del = 0
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        GROUP BY qcr.quality_checking_type
    </select>
    <select id="qualityCheckingExceptionAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.advisor_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.checking_result = 2 AND qcr.is_del = 0 AND ccspm.advisor_dept_id is not null
        <if test="qualityCheckingType != null">
            and qcr.quality_checking_type = #{qualityCheckingType}
        </if>
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        GROUP BY ccspm.advisor_dept_id
    </select>
    <select id="qualityCheckingExceptionAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.accounting_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.checking_result = 2 AND qcr.is_del = 0 AND ccspm.accounting_dept_id is not null
        <if test="qualityCheckingType != null">
            and qcr.quality_checking_type = #{qualityCheckingType}
        </if>
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        GROUP BY ccspm.accounting_dept_id
    </select>
    <select id="selectExceptionMiniList"
            resultType="com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO">
        SELECT
            qcr.id AS qualityCheckingResultId,
            ccspm.customer_service_id AS customerServiceId,
            ccspm.id AS customerServicePeriodMonthId,
            ccspm.customer_name AS customerName,
            qcr.quality_checking_item_id AS qualityCheckingItemId,
            ccspm.period AS period,
            ccspm.advisor_dept_id AS advisorDeptId,
            ccspm.accounting_dept_id AS accountingDeptId
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.checking_result = 2 AND qcr.is_del = 0
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="queryDeptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="vo.qualityCheckingType != null">
            AND qcr.quality_checking_type = #{vo.qualityCheckingType}
        </if>
        <if test="vo.keyWord != null and vo.keyWord != ''">
            and (
                ccspm.customer_name LIKE CONCAT('%',#{vo.keyWord},'%')
                OR ccspm.credit_code = #{vo.keyWord}
            )
        </if>
        <if test="vo.batchNo != null and vo.batchNo != ''">
            and ccspm.customer_service_id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="customerServiceId" close=")" open="(">
                #{customerServiceId}
            </foreach>
        </if>
        <if test="vo.qualityCheckingItemIds != null and vo.qualityCheckingItemIds != ''">
            and qcr.quality_checking_item_id in (${vo.qualityCheckingItemIds})
        </if>
        <if test="vo.periodStart != null">
            and ccspm.period &gt;= #{vo.periodStart}
        </if>
        <if test="vo.periodEnd != null">
            and ccspm.period &lt;= #{vo.periodEnd}
        </if>
        <if test="vo.advisorDeptId != null">
            and ccspm.advisor_dept_id = #{vo.advisorDeptId}
        </if>
        <if test="vo.accountingDeptId != null">
            and ccspm.accounting_dept_id = #{vo.accountingDeptId}
        </if>
        order by qcr.period desc, qcr.customer_service_id, qcr.id desc
    </select>
    <select id="qualityCheckingResultAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.advisor_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.is_del = 0 AND ccspm.advisor_dept_id is not null
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="vo.periodStart != null">
            and ccspm.period &gt;= #{vo.periodStart}
        </if>
        <if test="vo.periodEnd != null">
            and ccspm.period &lt;= #{vo.periodEnd}
        </if>
        GROUP BY ccspm.advisor_dept_id
    </select>
    <select id="qualityCheckingResultAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.accounting_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.is_del = 0 AND ccspm.accounting_dept_id is not null
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="vo.periodStart != null">
            and ccspm.period &gt;= #{vo.periodStart}
        </if>
        <if test="vo.periodEnd != null">
            and ccspm.period &lt;= #{vo.periodEnd}
        </if>
        GROUP BY ccspm.accounting_dept_id
    </select>
    <select id="qualityCheckResultPageList"
            resultType="com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO">
        select
            qcr.id as qualityCheckingResultId,
            ccspm.customer_service_id as customerServiceId,
            ccspm.id as customerServicePeriodMonthId,
            ccspm.customer_name as customerName,
            ccspm.credit_code as creditCode,
            ccspm.period as period,
            ccspm.business_top_dept_id as periodBusinessTopDeptId,
            ccspm.business_dept_id as periodBusinessDeptId,
            ccspm.advisor_dept_id as periodAdvisorDeptId,
            ccspm.accounting_top_dept_id as periodAccountingTopDeptId,
            ccspm.accounting_dept_id as periodAccountingDeptId,
            ccspm.tax_type as periodTaxType,
            qcr.quality_checking_type as qualityCheckingType,
            qcr.quality_checking_item_id as qualityCheckingItemId,
            qcr.quality_checking_cycle as qualityCheckingCycle,
            qcr.`status` as status,
            qcr.checking_result as checkingResult,
            qcr.last_checking_time as lastCheckingTime
            from c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagType == 1">
                    <if test="vo.periodTagIncludeFlag == 0">
                        LEFT JOIN (
                            SELECT DISTINCT br.business_id
                            FROM c_business_tag_relation br
                            INNER JOIN c_tag t ON br.tag_id = t.id
                            WHERE t.is_del = 0
                              AND t.tag_name like CONCAT('%',#{vo.periodTagName},'%')
                              AND br.business_type = 2
                        ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                    </if>
                    <if test="vo.periodTagIncludeFlag == 1">
                        JOIN (
                        SELECT DISTINCT br.business_id
                        FROM c_business_tag_relation br
                        INNER JOIN c_tag t ON br.tag_id = t.id
                        WHERE t.is_del = 0
                        AND t.tag_name like CONCAT('%',#{vo.periodTagName},'%')
                        AND br.business_type = 2
                        ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                    </if>
                </if>
                <if test="vo.periodTagType == 2">
                    <if test="vo.periodTagIncludeFlag == 0">
                        LEFT JOIN (
                        SELECT DISTINCT br.business_id
                        FROM c_business_tag_relation br
                        INNER JOIN c_tag t ON br.tag_id = t.id
                        WHERE t.is_del = 0
                        AND t.tag_name in
                        <foreach collection="periodTagNameList" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                        AND br.business_type = 2
                        ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                    </if>
                    <if test="vo.periodTagIncludeFlag == 1">
                        JOIN (
                            SELECT DISTINCT br.business_id
                            FROM c_business_tag_relation br
                            INNER JOIN c_tag t ON br.tag_id = t.id
                            WHERE t.is_del = 0
                            AND t.tag_name in
                            <foreach collection="periodTagNameList" open="(" close=")" item="item" separator=",">
                                #{item}
                            </foreach>
                            AND br.business_type = 2
                            GROUP BY br.business_id HAVING COUNT(DISTINCT br.tag_id) = ${vo.periodTagSize}
                        ) period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id
                    </if>
                </if>
            </if>
        <where>
            qcr.is_del = 0
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                ccspm.customer_name like concat('%',#{vo.keyWord},'%')
                or ccspm.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccspm.customer_service_id in
                <foreach collection="batchSearchCustomerServiceIds" separator="," item="customerServiceId" close=")" open="(">
                    #{customerServiceId}
                </foreach>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag == 0">
                    and period_tag_filter.business_id is null
                </if>
                <if test="vo.periodTagIncludeFlag == 1">
                    and period_tag_filter.business_id is not null
                </if>
            </if>
            <if test="vo.periodStart != null">
                and ccspm.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and ccspm.period &lt;= #{vo.periodEnd}
            </if>
            <if test="userDept.deptType == 1">
                and qcr.quality_checking_type = 2
            </if>
            <if test="vo.qualityCheckingType != null">
                and qcr.quality_checking_type = #{vo.qualityCheckingType}
            </if>
            <if test="vo.qualityCheckingItemIds != null and vo.qualityCheckingItemIds != ''">
                and qcr.quality_checking_item_id in (${vo.qualityCheckingItemIds})
            </if>
            <if test="vo.qualityCheckingCycle != null">
                and qcr.quality_checking_cycle = #{vo.qualityCheckingCycle}
            </if>
            <if test="vo.status != null">
                and qcr.`status` = #{vo.status}
            </if>
            <if test="vo.checkingResult != null">
                and qcr.checking_result = #{vo.checkingResult}
            </if>
            <if test="vo.lastCheckingTimeStart != null and vo.lastCheckingTimeStart != ''">
                and qcr.last_checking_time &gt;= #{vo.lastCheckingTimeStart}
            </if>
            <if test="vo.lastCheckingTimeEnd != null and vo.lastCheckingTimeEnd != ''">
                and qcr.last_checking_time &lt;= #{vo.lastCheckingTimeEnd}
            </if>
            <if test="vo.advisorDeptId != null">
                and ccspm.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccspm.accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        order by qcr.period desc, qcr.customer_service_id, qcr.id desc
    </select>
    <select id="qualityCheckResultListByIds"
            resultType="com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO">
        select
            qcr.id as qualityCheckingResultId,
            ccspm.customer_service_id as customerServiceId,
            ccspm.id as customerServicePeriodMonthId,
            ccspm.customer_name as customerName,
            ccspm.credit_code as creditCode,
            ccspm.period as period,
            ccspm.business_top_dept_id as periodBusinessTopDeptId,
            ccspm.business_dept_id as periodBusinessDeptId,
            ccspm.advisor_dept_id as periodAdvisorDeptId,
            ccspm.accounting_top_dept_id as periodAccountingTopDeptId,
            ccspm.accounting_dept_id as periodAccountingDeptId,
            ccspm.tax_type as periodTaxType,
            qcr.quality_checking_type as qualityCheckingType,
            qcr.quality_checking_item_id as qualityCheckingItemId,
            qcr.quality_checking_cycle as qualityCheckingCycle,
            qcr.`status` as status,
            qcr.checking_result as checkingResult,
            qcr.last_checking_time as lastCheckingTime
        from c_quality_checking_result qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        where qcr.id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        order by qcr.period desc, qcr.customer_service_id, qcr.id desc
    </select>

    <insert id="insertQualityCheckingResult" parameterType="com.bxm.customer.domain.QualityCheckingResult" useGeneratedKeys="true" keyProperty="id">
        insert into c_quality_checking_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="qualityCheckingItemId != null">quality_checking_item_id,</if>
            <if test="status != null">status,</if>
            <if test="checkingResult != null">checking_result,</if>
            <if test="lastCheckingTime != null">last_checking_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="qualityCheckingItemId != null">#{qualityCheckingItemId},</if>
            <if test="status != null">#{status},</if>
            <if test="checkingResult != null">#{checkingResult},</if>
            <if test="lastCheckingTime != null">#{lastCheckingTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="createNewPeriodQualityCheckingResult">
        INSERT INTO c_quality_checking_result (customer_service_id,customer_service_period_month_id,period,quality_checking_item_id,quality_checking_type,quality_checking_cycle,create_by)
        SELECT
            ccspm.customer_service_id,
            ccspm.id,
            ccspm.period,
            ct.id,
            ct.quality_checking_type,
            ct.quality_checking_cycle,
            '系统'
        FROM c_customer_service_period_month ccspm JOIN c_quality_checking_item ct ON ct.`status` = 1
        WHERE ccspm.period = #{period} and ccspm.service_type = 1
    </insert>
    <insert id="createNewPeriodQualityCheckingOperationRecord">
        INSERT INTO sys_business_log (business_type,business_id,oper_type,oper_user_id,oper_name)
        SELECT 19,id,'新建',1,'系统'
        FROM c_quality_checking_result WHERE is_del = 0 AND period = #{period}
    </insert>

    <update id="updateQualityCheckingResult" parameterType="com.bxm.customer.domain.QualityCheckingResult">
        update c_quality_checking_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="qualityCheckingItemId != null">quality_checking_item_id = #{qualityCheckingItemId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="checkingResult != null">checking_result = #{checkingResult},</if>
            <if test="lastCheckingTime != null">last_checking_time = #{lastCheckingTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQualityCheckingResultById" parameterType="Long">
        delete from c_quality_checking_result where id = #{id}
    </delete>

    <delete id="deleteQualityCheckingResultByIds" parameterType="String">
        delete from c_quality_checking_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>