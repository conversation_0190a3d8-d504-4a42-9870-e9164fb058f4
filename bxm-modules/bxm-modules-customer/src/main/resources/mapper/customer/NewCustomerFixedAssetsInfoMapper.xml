<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerFixedAssetsInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerFixedAssetsInfo" id="NewCustomerFixedAssetsInfoResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="occurrenceYear"    column="occurrence_year"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerFixedAssetsInfoVo">
        select id, customer_id, asset_name, occurrence_year, create_by, create_time, update_by, update_time from c_new_customer_fixed_assets_info
    </sql>

    <select id="selectNewCustomerFixedAssetsInfoList" parameterType="com.bxm.customer.domain.NewCustomerFixedAssetsInfo" resultMap="NewCustomerFixedAssetsInfoResult">
        <include refid="selectNewCustomerFixedAssetsInfoVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="occurrenceYear != null "> and occurrence_year = #{occurrenceYear}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerFixedAssetsInfoById" parameterType="Long" resultMap="NewCustomerFixedAssetsInfoResult">
        <include refid="selectNewCustomerFixedAssetsInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerFixedAssetsInfo" parameterType="com.bxm.customer.domain.NewCustomerFixedAssetsInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_fixed_assets_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="occurrenceYear != null">occurrence_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="occurrenceYear != null">#{occurrenceYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerFixedAssetsInfo" parameterType="com.bxm.customer.domain.NewCustomerFixedAssetsInfo">
        update c_new_customer_fixed_assets_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="occurrenceYear != null">occurrence_year = #{occurrenceYear},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerFixedAssetsInfoById" parameterType="Long">
        delete from c_new_customer_fixed_assets_info where id = #{id}
    </delete>

    <delete id="deleteNewCustomerFixedAssetsInfoByIds" parameterType="String">
        delete from c_new_customer_fixed_assets_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>