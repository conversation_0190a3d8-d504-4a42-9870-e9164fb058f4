<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiSyncStatementDetailMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiSyncStatementDetail" id="OpenApiSyncStatementDetailResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="customerId"    column="customer_id"    />
        <result property="accountingMonth"    column="accounting_month"    />
        <result property="paymentMonth"    column="payment_month"    />
        <result property="personalId"    column="personal_id"    />
        <result property="name"    column="name"    />
        <result property="insuredIdentity"    column="insured_identity"    />
        <result property="totalInsuredMonths"    column="total_insured_months"    />
        <result property="totalPaymentAmount"    column="total_payment_amount"    />
        <result property="companyBearAmount"    column="company_bear_amount"    />
        <result property="personalBearAmount"    column="personal_bear_amount"    />
        <result property="medicalPersonalBear"    column="medical_personal_bear"    />
        <result property="pensionPersonalBear"    column="pension_personal_bear"    />
        <result property="unemploymentPersonalBear"    column="unemployment_personal_bear"    />
        <result property="maternityPersonalBear"    column="maternity_personal_bear"    />
        <result property="employeeMedicalInsurancePersonal"    column="employee_medical_insurance_personal"    />
        <result property="occupationalAnnuityPersonal"    column="occupational_annuity_personal"    />
        <result property="governmentAgencyPensionPersonal"    column="government_agency_pension_personal"    />
        <result property="civilServantMedicalSubsidyPersonal"    column="civil_servant_medical_subsidy_personal"    />
        <result property="majorMedicalExpenseSubsidyPersonal"    column="major_medical_expense_subsidy_personal"    />
        <result property="longTermCareInsurancePersonal"    column="long_term_care_insurance_personal"    />
        <result property="medicalCompanyBear"    column="medical_company_bear"    />
        <result property="pensionCompanyBear"    column="pension_company_bear"    />
        <result property="injuryCompanyBear"    column="injury_company_bear"    />
        <result property="unemploymentCompanyBear"    column="unemployment_company_bear"    />
        <result property="maternityCompanyBear"    column="maternity_company_bear"    />
        <result property="employeeMajorDiseaseMedicalSubsidyCompanyBear"    column="employee_major_disease_medical_subsidy_company_bear"    />
        <result property="employeeMedicalInsuranceCompany"    column="employee_medical_insurance_company"    />
        <result property="employeeAdditionalMedicalInsuranceCompany"    column="employee_additional_medical_insurance_company"    />
        <result property="occupationalAnnuityCompany"    column="occupational_annuity_company"    />
        <result property="governmentAgencyPensionCompany"    column="government_agency_pension_company"    />
        <result property="civilServantMedicalSubsidyCompany"    column="civil_servant_medical_subsidy_company"    />
        <result property="majorMedicalExpenseSubsidyCompany"    column="major_medical_expense_subsidy_company"    />
        <result property="employeeMajorMedicalMutualInsuranceCompany"    column="employee_major_medical_mutual_insurance_company"    />
        <result property="longTermCareInsuranceCompany"    column="long_term_care_insurance_company"    />
        <result property="employeeMajorDiseaseMutualInsuranceCompany"    column="employee_major_disease_mutual_insurance_company"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiSyncStatementDetailVo">
        select id, uid, tax_number, customer_id, accounting_month, payment_month, personal_id, name, insured_identity, total_insured_months, total_payment_amount, company_bear_amount, personal_bear_amount, medical_personal_bear, pension_personal_bear, unemployment_personal_bear, maternity_personal_bear, employee_medical_insurance_personal, occupational_annuity_personal, government_agency_pension_personal, civil_servant_medical_subsidy_personal, major_medical_expense_subsidy_personal, long_term_care_insurance_personal, medical_company_bear, pension_company_bear, injury_company_bear, unemployment_company_bear, maternity_company_bear, employee_major_disease_medical_subsidy_company_bear, employee_medical_insurance_company, employee_additional_medical_insurance_company, occupational_annuity_company, government_agency_pension_company, civil_servant_medical_subsidy_company, major_medical_expense_subsidy_company, employee_major_medical_mutual_insurance_company, long_term_care_insurance_company, employee_major_disease_mutual_insurance_company, create_by, create_time, update_by, update_time from c_open_api_sync_statement_detail
    </sql>

    <select id="selectOpenApiSyncStatementDetailList" parameterType="com.bxm.customer.domain.OpenApiSyncStatementDetail" resultMap="OpenApiSyncStatementDetailResult">
        <include refid="selectOpenApiSyncStatementDetailVo"/>
        <where>  
            <if test="uid != null  and uid != ''"> and uid = #{uid}</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="accountingMonth != null "> and accounting_month = #{accountingMonth}</if>
            <if test="paymentMonth != null "> and payment_month = #{paymentMonth}</if>
            <if test="personalId != null  and personalId != ''"> and personal_id = #{personalId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="insuredIdentity != null  and insuredIdentity != ''"> and insured_identity = #{insuredIdentity}</if>
            <if test="totalInsuredMonths != null "> and total_insured_months = #{totalInsuredMonths}</if>
            <if test="totalPaymentAmount != null "> and total_payment_amount = #{totalPaymentAmount}</if>
            <if test="companyBearAmount != null "> and company_bear_amount = #{companyBearAmount}</if>
            <if test="personalBearAmount != null "> and personal_bear_amount = #{personalBearAmount}</if>
            <if test="medicalPersonalBear != null "> and medical_personal_bear = #{medicalPersonalBear}</if>
            <if test="pensionPersonalBear != null "> and pension_personal_bear = #{pensionPersonalBear}</if>
            <if test="unemploymentPersonalBear != null "> and unemployment_personal_bear = #{unemploymentPersonalBear}</if>
            <if test="maternityPersonalBear != null "> and maternity_personal_bear = #{maternityPersonalBear}</if>
            <if test="employeeMedicalInsurancePersonal != null "> and employee_medical_insurance_personal = #{employeeMedicalInsurancePersonal}</if>
            <if test="occupationalAnnuityPersonal != null "> and occupational_annuity_personal = #{occupationalAnnuityPersonal}</if>
            <if test="governmentAgencyPensionPersonal != null "> and government_agency_pension_personal = #{governmentAgencyPensionPersonal}</if>
            <if test="civilServantMedicalSubsidyPersonal != null "> and civil_servant_medical_subsidy_personal = #{civilServantMedicalSubsidyPersonal}</if>
            <if test="majorMedicalExpenseSubsidyPersonal != null "> and major_medical_expense_subsidy_personal = #{majorMedicalExpenseSubsidyPersonal}</if>
            <if test="longTermCareInsurancePersonal != null "> and long_term_care_insurance_personal = #{longTermCareInsurancePersonal}</if>
            <if test="medicalCompanyBear != null "> and medical_company_bear = #{medicalCompanyBear}</if>
            <if test="pensionCompanyBear != null "> and pension_company_bear = #{pensionCompanyBear}</if>
            <if test="injuryCompanyBear != null "> and injury_company_bear = #{injuryCompanyBear}</if>
            <if test="unemploymentCompanyBear != null "> and unemployment_company_bear = #{unemploymentCompanyBear}</if>
            <if test="maternityCompanyBear != null "> and maternity_company_bear = #{maternityCompanyBear}</if>
            <if test="employeeMajorDiseaseMedicalSubsidyCompanyBear != null "> and employee_major_disease_medical_subsidy_company_bear = #{employeeMajorDiseaseMedicalSubsidyCompanyBear}</if>
            <if test="employeeMedicalInsuranceCompany != null "> and employee_medical_insurance_company = #{employeeMedicalInsuranceCompany}</if>
            <if test="employeeAdditionalMedicalInsuranceCompany != null "> and employee_additional_medical_insurance_company = #{employeeAdditionalMedicalInsuranceCompany}</if>
            <if test="occupationalAnnuityCompany != null "> and occupational_annuity_company = #{occupationalAnnuityCompany}</if>
            <if test="governmentAgencyPensionCompany != null "> and government_agency_pension_company = #{governmentAgencyPensionCompany}</if>
            <if test="civilServantMedicalSubsidyCompany != null "> and civil_servant_medical_subsidy_company = #{civilServantMedicalSubsidyCompany}</if>
            <if test="majorMedicalExpenseSubsidyCompany != null "> and major_medical_expense_subsidy_company = #{majorMedicalExpenseSubsidyCompany}</if>
            <if test="employeeMajorMedicalMutualInsuranceCompany != null "> and employee_major_medical_mutual_insurance_company = #{employeeMajorMedicalMutualInsuranceCompany}</if>
            <if test="longTermCareInsuranceCompany != null "> and long_term_care_insurance_company = #{longTermCareInsuranceCompany}</if>
            <if test="employeeMajorDiseaseMutualInsuranceCompany != null "> and employee_major_disease_mutual_insurance_company = #{employeeMajorDiseaseMutualInsuranceCompany}</if>
        </where>
    </select>
    
    <select id="selectOpenApiSyncStatementDetailById" parameterType="Long" resultMap="OpenApiSyncStatementDetailResult">
        <include refid="selectOpenApiSyncStatementDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiSyncStatementDetail" parameterType="com.bxm.customer.domain.OpenApiSyncStatementDetail" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_sync_statement_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uid != null">uid,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="accountingMonth != null">accounting_month,</if>
            <if test="paymentMonth != null">payment_month,</if>
            <if test="personalId != null">personal_id,</if>
            <if test="name != null">name,</if>
            <if test="insuredIdentity != null">insured_identity,</if>
            <if test="totalInsuredMonths != null">total_insured_months,</if>
            <if test="totalPaymentAmount != null">total_payment_amount,</if>
            <if test="companyBearAmount != null">company_bear_amount,</if>
            <if test="personalBearAmount != null">personal_bear_amount,</if>
            <if test="medicalPersonalBear != null">medical_personal_bear,</if>
            <if test="pensionPersonalBear != null">pension_personal_bear,</if>
            <if test="unemploymentPersonalBear != null">unemployment_personal_bear,</if>
            <if test="maternityPersonalBear != null">maternity_personal_bear,</if>
            <if test="employeeMedicalInsurancePersonal != null">employee_medical_insurance_personal,</if>
            <if test="occupationalAnnuityPersonal != null">occupational_annuity_personal,</if>
            <if test="governmentAgencyPensionPersonal != null">government_agency_pension_personal,</if>
            <if test="civilServantMedicalSubsidyPersonal != null">civil_servant_medical_subsidy_personal,</if>
            <if test="majorMedicalExpenseSubsidyPersonal != null">major_medical_expense_subsidy_personal,</if>
            <if test="longTermCareInsurancePersonal != null">long_term_care_insurance_personal,</if>
            <if test="medicalCompanyBear != null">medical_company_bear,</if>
            <if test="pensionCompanyBear != null">pension_company_bear,</if>
            <if test="injuryCompanyBear != null">injury_company_bear,</if>
            <if test="unemploymentCompanyBear != null">unemployment_company_bear,</if>
            <if test="maternityCompanyBear != null">maternity_company_bear,</if>
            <if test="employeeMajorDiseaseMedicalSubsidyCompanyBear != null">employee_major_disease_medical_subsidy_company_bear,</if>
            <if test="employeeMedicalInsuranceCompany != null">employee_medical_insurance_company,</if>
            <if test="employeeAdditionalMedicalInsuranceCompany != null">employee_additional_medical_insurance_company,</if>
            <if test="occupationalAnnuityCompany != null">occupational_annuity_company,</if>
            <if test="governmentAgencyPensionCompany != null">government_agency_pension_company,</if>
            <if test="civilServantMedicalSubsidyCompany != null">civil_servant_medical_subsidy_company,</if>
            <if test="majorMedicalExpenseSubsidyCompany != null">major_medical_expense_subsidy_company,</if>
            <if test="employeeMajorMedicalMutualInsuranceCompany != null">employee_major_medical_mutual_insurance_company,</if>
            <if test="longTermCareInsuranceCompany != null">long_term_care_insurance_company,</if>
            <if test="employeeMajorDiseaseMutualInsuranceCompany != null">employee_major_disease_mutual_insurance_company,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uid != null">#{uid},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="accountingMonth != null">#{accountingMonth},</if>
            <if test="paymentMonth != null">#{paymentMonth},</if>
            <if test="personalId != null">#{personalId},</if>
            <if test="name != null">#{name},</if>
            <if test="insuredIdentity != null">#{insuredIdentity},</if>
            <if test="totalInsuredMonths != null">#{totalInsuredMonths},</if>
            <if test="totalPaymentAmount != null">#{totalPaymentAmount},</if>
            <if test="companyBearAmount != null">#{companyBearAmount},</if>
            <if test="personalBearAmount != null">#{personalBearAmount},</if>
            <if test="medicalPersonalBear != null">#{medicalPersonalBear},</if>
            <if test="pensionPersonalBear != null">#{pensionPersonalBear},</if>
            <if test="unemploymentPersonalBear != null">#{unemploymentPersonalBear},</if>
            <if test="maternityPersonalBear != null">#{maternityPersonalBear},</if>
            <if test="employeeMedicalInsurancePersonal != null">#{employeeMedicalInsurancePersonal},</if>
            <if test="occupationalAnnuityPersonal != null">#{occupationalAnnuityPersonal},</if>
            <if test="governmentAgencyPensionPersonal != null">#{governmentAgencyPensionPersonal},</if>
            <if test="civilServantMedicalSubsidyPersonal != null">#{civilServantMedicalSubsidyPersonal},</if>
            <if test="majorMedicalExpenseSubsidyPersonal != null">#{majorMedicalExpenseSubsidyPersonal},</if>
            <if test="longTermCareInsurancePersonal != null">#{longTermCareInsurancePersonal},</if>
            <if test="medicalCompanyBear != null">#{medicalCompanyBear},</if>
            <if test="pensionCompanyBear != null">#{pensionCompanyBear},</if>
            <if test="injuryCompanyBear != null">#{injuryCompanyBear},</if>
            <if test="unemploymentCompanyBear != null">#{unemploymentCompanyBear},</if>
            <if test="maternityCompanyBear != null">#{maternityCompanyBear},</if>
            <if test="employeeMajorDiseaseMedicalSubsidyCompanyBear != null">#{employeeMajorDiseaseMedicalSubsidyCompanyBear},</if>
            <if test="employeeMedicalInsuranceCompany != null">#{employeeMedicalInsuranceCompany},</if>
            <if test="employeeAdditionalMedicalInsuranceCompany != null">#{employeeAdditionalMedicalInsuranceCompany},</if>
            <if test="occupationalAnnuityCompany != null">#{occupationalAnnuityCompany},</if>
            <if test="governmentAgencyPensionCompany != null">#{governmentAgencyPensionCompany},</if>
            <if test="civilServantMedicalSubsidyCompany != null">#{civilServantMedicalSubsidyCompany},</if>
            <if test="majorMedicalExpenseSubsidyCompany != null">#{majorMedicalExpenseSubsidyCompany},</if>
            <if test="employeeMajorMedicalMutualInsuranceCompany != null">#{employeeMajorMedicalMutualInsuranceCompany},</if>
            <if test="longTermCareInsuranceCompany != null">#{longTermCareInsuranceCompany},</if>
            <if test="employeeMajorDiseaseMutualInsuranceCompany != null">#{employeeMajorDiseaseMutualInsuranceCompany},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiSyncStatementDetail" parameterType="com.bxm.customer.domain.OpenApiSyncStatementDetail">
        update c_open_api_sync_statement_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="accountingMonth != null">accounting_month = #{accountingMonth},</if>
            <if test="paymentMonth != null">payment_month = #{paymentMonth},</if>
            <if test="personalId != null">personal_id = #{personalId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="insuredIdentity != null">insured_identity = #{insuredIdentity},</if>
            <if test="totalInsuredMonths != null">total_insured_months = #{totalInsuredMonths},</if>
            <if test="totalPaymentAmount != null">total_payment_amount = #{totalPaymentAmount},</if>
            <if test="companyBearAmount != null">company_bear_amount = #{companyBearAmount},</if>
            <if test="personalBearAmount != null">personal_bear_amount = #{personalBearAmount},</if>
            <if test="medicalPersonalBear != null">medical_personal_bear = #{medicalPersonalBear},</if>
            <if test="pensionPersonalBear != null">pension_personal_bear = #{pensionPersonalBear},</if>
            <if test="unemploymentPersonalBear != null">unemployment_personal_bear = #{unemploymentPersonalBear},</if>
            <if test="maternityPersonalBear != null">maternity_personal_bear = #{maternityPersonalBear},</if>
            <if test="employeeMedicalInsurancePersonal != null">employee_medical_insurance_personal = #{employeeMedicalInsurancePersonal},</if>
            <if test="occupationalAnnuityPersonal != null">occupational_annuity_personal = #{occupationalAnnuityPersonal},</if>
            <if test="governmentAgencyPensionPersonal != null">government_agency_pension_personal = #{governmentAgencyPensionPersonal},</if>
            <if test="civilServantMedicalSubsidyPersonal != null">civil_servant_medical_subsidy_personal = #{civilServantMedicalSubsidyPersonal},</if>
            <if test="majorMedicalExpenseSubsidyPersonal != null">major_medical_expense_subsidy_personal = #{majorMedicalExpenseSubsidyPersonal},</if>
            <if test="longTermCareInsurancePersonal != null">long_term_care_insurance_personal = #{longTermCareInsurancePersonal},</if>
            <if test="medicalCompanyBear != null">medical_company_bear = #{medicalCompanyBear},</if>
            <if test="pensionCompanyBear != null">pension_company_bear = #{pensionCompanyBear},</if>
            <if test="injuryCompanyBear != null">injury_company_bear = #{injuryCompanyBear},</if>
            <if test="unemploymentCompanyBear != null">unemployment_company_bear = #{unemploymentCompanyBear},</if>
            <if test="maternityCompanyBear != null">maternity_company_bear = #{maternityCompanyBear},</if>
            <if test="employeeMajorDiseaseMedicalSubsidyCompanyBear != null">employee_major_disease_medical_subsidy_company_bear = #{employeeMajorDiseaseMedicalSubsidyCompanyBear},</if>
            <if test="employeeMedicalInsuranceCompany != null">employee_medical_insurance_company = #{employeeMedicalInsuranceCompany},</if>
            <if test="employeeAdditionalMedicalInsuranceCompany != null">employee_additional_medical_insurance_company = #{employeeAdditionalMedicalInsuranceCompany},</if>
            <if test="occupationalAnnuityCompany != null">occupational_annuity_company = #{occupationalAnnuityCompany},</if>
            <if test="governmentAgencyPensionCompany != null">government_agency_pension_company = #{governmentAgencyPensionCompany},</if>
            <if test="civilServantMedicalSubsidyCompany != null">civil_servant_medical_subsidy_company = #{civilServantMedicalSubsidyCompany},</if>
            <if test="majorMedicalExpenseSubsidyCompany != null">major_medical_expense_subsidy_company = #{majorMedicalExpenseSubsidyCompany},</if>
            <if test="employeeMajorMedicalMutualInsuranceCompany != null">employee_major_medical_mutual_insurance_company = #{employeeMajorMedicalMutualInsuranceCompany},</if>
            <if test="longTermCareInsuranceCompany != null">long_term_care_insurance_company = #{longTermCareInsuranceCompany},</if>
            <if test="employeeMajorDiseaseMutualInsuranceCompany != null">employee_major_disease_mutual_insurance_company = #{employeeMajorDiseaseMutualInsuranceCompany},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiSyncStatementDetailById" parameterType="Long">
        delete from c_open_api_sync_statement_detail where id = #{id}
    </delete>

    <delete id="deleteOpenApiSyncStatementDetailByIds" parameterType="String">
        delete from c_open_api_sync_statement_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>