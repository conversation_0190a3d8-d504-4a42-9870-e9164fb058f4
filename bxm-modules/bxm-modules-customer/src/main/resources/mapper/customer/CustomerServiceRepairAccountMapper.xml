<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceRepairAccountMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceRepairAccount" id="CustomerServiceRepairAccountResult">
        <result property="id"    column="id"    />
        <result property="isDel"    column="is_del"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="serviceNumber"    column="service_number"    />
        <result property="taxType"    column="tax_type"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="businessDeptName"    column="business_dept_name"    />
        <result property="businessTopDeptId"    column="business_top_dept_id"    />
        <result property="advisorDeptId"    column="advisor_dept_id"    />
        <result property="advisorDeptName"    column="advisor_dept_name"    />
        <result property="advisorTopDeptId"    column="advisor_top_dept_id"    />
        <result property="accountingDeptId"    column="accounting_dept_id"    />
        <result property="accountingDeptName"    column="accounting_dept_name"    />
        <result property="accountingTopDeptId"    column="accounting_top_dept_id"    />
        <result property="verificationEmployeeType"    column="verification_employee_type"    />
        <result property="status"    column="status"    />
        <result property="startPeriod"    column="start_period"    />
        <result property="endPeriod"    column="end_period"    />
        <result property="remark"    column="remark"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="submitEmployeeDeptId"    column="submit_employee_dept_id"    />
        <result property="submitEmployeeDeptName"    column="submit_employee_dept_name"    />
        <result property="submitEmployeeId"    column="submit_employee_id"    />
        <result property="submitEmployeeName"    column="submit_employee_name"    />
        <result property="assignTime"    column="assign_time"    />
        <result property="assignEmployeeDeptId"    column="assign_employee_dept_id"    />
        <result property="assignEmployeeDeptName"    column="assign_employee_dept_name"    />
        <result property="assignEmployeeId"    column="assign_employee_id"    />
        <result property="assignEmployeeName"    column="assign_employee_name"    />
        <result property="assignRemark"    column="assign_remark"    />
        <result property="assignAccountingDeptId"    column="assign_accounting_dept_id"    />
        <result property="backTime"    column="back_time"    />
        <result property="backEmployeeDeptId"    column="back_employee_dept_id"    />
        <result property="backEmployeeDeptName"    column="back_employee_dept_name"    />
        <result property="backEmployeeId"    column="back_employee_id"    />
        <result property="backEmployeeName"    column="back_employee_name"    />
        <result property="backRemark"    column="back_remark"    />
        <result property="deliverStatus"    column="deliver_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceRepairAccountVo">
        select id, is_del, customer_service_id, customer_name, credit_code, tax_number, service_number, tax_type, business_dept_id, business_dept_name, business_top_dept_id, advisor_dept_id, advisor_dept_name, advisor_top_dept_id, accounting_dept_id, accounting_dept_name, accounting_top_dept_id, verification_employee_type, status, start_period, end_period, remark, submit_time, submit_employee_dept_id, submit_employee_dept_name, submit_employee_id, submit_employee_name, assign_time, assign_employee_dept_id, assign_employee_dept_name, assign_employee_id, assign_employee_name, assign_remark, assign_accounting_dept_id, back_time, back_employee_dept_id, back_employee_dept_name, back_employee_id, back_employee_name, back_remark, deliver_status, create_by, create_time, update_by, update_time from c_customer_service_repair_account
    </sql>

    <select id="selectCustomerServiceRepairAccountList" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccount" resultMap="CustomerServiceRepairAccountResult">
        <include refid="selectCustomerServiceRepairAccountVo"/>
        <where>  
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="serviceNumber != null  and serviceNumber != ''"> and service_number = #{serviceNumber}</if>
            <if test="taxType != null "> and tax_type = #{taxType}</if>
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="businessDeptName != null  and businessDeptName != ''"> and business_dept_name like concat('%', #{businessDeptName}, '%')</if>
            <if test="businessTopDeptId != null "> and business_top_dept_id = #{businessTopDeptId}</if>
            <if test="advisorDeptId != null "> and advisor_dept_id = #{advisorDeptId}</if>
            <if test="advisorDeptName != null  and advisorDeptName != ''"> and advisor_dept_name like concat('%', #{advisorDeptName}, '%')</if>
            <if test="advisorTopDeptId != null "> and advisor_top_dept_id = #{advisorTopDeptId}</if>
            <if test="accountingDeptId != null "> and accounting_dept_id = #{accountingDeptId}</if>
            <if test="accountingDeptName != null  and accountingDeptName != ''"> and accounting_dept_name like concat('%', #{accountingDeptName}, '%')</if>
            <if test="accountingTopDeptId != null "> and accounting_top_dept_id = #{accountingTopDeptId}</if>
            <if test="verificationEmployeeType != null "> and verification_employee_type = #{verificationEmployeeType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="startPeriod != null "> and start_period = #{startPeriod}</if>
            <if test="endPeriod != null "> and end_period = #{endPeriod}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="submitEmployeeDeptId != null "> and submit_employee_dept_id = #{submitEmployeeDeptId}</if>
            <if test="submitEmployeeDeptName != null  and submitEmployeeDeptName != ''"> and submit_employee_dept_name like concat('%', #{submitEmployeeDeptName}, '%')</if>
            <if test="submitEmployeeId != null "> and submit_employee_id = #{submitEmployeeId}</if>
            <if test="submitEmployeeName != null  and submitEmployeeName != ''"> and submit_employee_name like concat('%', #{submitEmployeeName}, '%')</if>
            <if test="assignTime != null "> and assign_time = #{assignTime}</if>
            <if test="assignEmployeeDeptId != null "> and assign_employee_dept_id = #{assignEmployeeDeptId}</if>
            <if test="assignEmployeeDeptName != null  and assignEmployeeDeptName != ''"> and assign_employee_dept_name like concat('%', #{assignEmployeeDeptName}, '%')</if>
            <if test="assignEmployeeId != null "> and assign_employee_id = #{assignEmployeeId}</if>
            <if test="assignEmployeeName != null  and assignEmployeeName != ''"> and assign_employee_name like concat('%', #{assignEmployeeName}, '%')</if>
            <if test="assignRemark != null  and assignRemark != ''"> and assign_remark = #{assignRemark}</if>
            <if test="assignAccountingDeptId != null"> and assign_accounting_dept_id = #{assignAccountingDeptId}</if>
            <if test="backTime != null "> and back_time = #{backTime}</if>
            <if test="backEmployeeDeptId != null "> and back_employee_dept_id = #{backEmployeeDeptId}</if>
            <if test="backEmployeeDeptName != null  and backEmployeeDeptName != ''"> and back_employee_dept_name like concat('%', #{backEmployeeDeptName}, '%')</if>
            <if test="backEmployeeId != null "> and back_employee_id = #{backEmployeeId}</if>
            <if test="backEmployeeName != null  and backEmployeeName != ''"> and back_employee_name like concat('%', #{backEmployeeName}, '%')</if>
            <if test="backRemark != null  and backRemark != ''"> and back_remark = #{backRemark}</if>
            <if test="deliverStatus != null  and deliverStatus != ''"> and deliver_status = #{deliverStatus}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceRepairAccountById" parameterType="Long" resultMap="CustomerServiceRepairAccountResult">
        <include refid="selectCustomerServiceRepairAccountVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceRepairAccount" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccount" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="serviceNumber != null">service_number,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="businessDeptName != null">business_dept_name,</if>
            <if test="businessTopDeptId != null">business_top_dept_id,</if>
            <if test="advisorDeptId != null">advisor_dept_id,</if>
            <if test="advisorDeptName != null">advisor_dept_name,</if>
            <if test="advisorTopDeptId != null">advisor_top_dept_id,</if>
            <if test="accountingDeptId != null">accounting_dept_id,</if>
            <if test="accountingDeptName != null">accounting_dept_name,</if>
            <if test="accountingTopDeptId != null">accounting_top_dept_id,</if>
            <if test="verificationEmployeeType != null">verification_employee_type,</if>
            <if test="status != null">status,</if>
            <if test="startPeriod != null">start_period,</if>
            <if test="endPeriod != null">end_period,</if>
            <if test="remark != null">remark,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id,</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name,</if>
            <if test="submitEmployeeId != null">submit_employee_id,</if>
            <if test="submitEmployeeName != null">submit_employee_name,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="assignEmployeeDeptId != null">assign_employee_dept_id,</if>
            <if test="assignEmployeeDeptName != null">assign_employee_dept_name,</if>
            <if test="assignEmployeeId != null">assign_employee_id,</if>
            <if test="assignEmployeeName != null">assign_employee_name,</if>
            <if test="assignRemark != null">assign_remark,</if>
            <if test="assignAccountingDeptId != null">assign_accounting_dept_id,</if>
            <if test="backTime != null">back_time,</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id,</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name,</if>
            <if test="backEmployeeId != null">back_employee_id,</if>
            <if test="backEmployeeName != null">back_employee_name,</if>
            <if test="backRemark != null">back_remark,</if>
            <if test="deliverStatus != null">deliver_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="serviceNumber != null">#{serviceNumber},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="businessDeptName != null">#{businessDeptName},</if>
            <if test="businessTopDeptId != null">#{businessTopDeptId},</if>
            <if test="advisorDeptId != null">#{advisorDeptId},</if>
            <if test="advisorDeptName != null">#{advisorDeptName},</if>
            <if test="advisorTopDeptId != null">#{advisorTopDeptId},</if>
            <if test="accountingDeptId != null">#{accountingDeptId},</if>
            <if test="accountingDeptName != null">#{accountingDeptName},</if>
            <if test="accountingTopDeptId != null">#{accountingTopDeptId},</if>
            <if test="verificationEmployeeType != null">#{verificationEmployeeType},</if>
            <if test="status != null">#{status},</if>
            <if test="startPeriod != null">#{startPeriod},</if>
            <if test="endPeriod != null">#{endPeriod},</if>
            <if test="remark != null">#{remark},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="submitEmployeeDeptId != null">#{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">#{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">#{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">#{submitEmployeeName},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="assignEmployeeDeptId != null">#{assignEmployeeDeptId},</if>
            <if test="assignEmployeeDeptName != null">#{assignEmployeeDeptName},</if>
            <if test="assignEmployeeId != null">#{assignEmployeeId},</if>
            <if test="assignEmployeeName != null">#{assignEmployeeName},</if>
            <if test="assignRemark != null">#{assignRemark},</if>
            <if test="assignAccountingDeptId != null">#{assignAccountingDeptId},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="backEmployeeDeptId != null">#{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">#{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">#{backEmployeeId},</if>
            <if test="backEmployeeName != null">#{backEmployeeName},</if>
            <if test="backRemark != null">#{backRemark},</if>
            <if test="deliverStatus != null">#{deliverStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceRepairAccount" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccount">
        update c_customer_service_repair_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="serviceNumber != null">service_number = #{serviceNumber},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="businessDeptName != null">business_dept_name = #{businessDeptName},</if>
            <if test="businessTopDeptId != null">business_top_dept_id = #{businessTopDeptId},</if>
            <if test="advisorDeptId != null">advisor_dept_id = #{advisorDeptId},</if>
            <if test="advisorDeptName != null">advisor_dept_name = #{advisorDeptName},</if>
            <if test="advisorTopDeptId != null">advisor_top_dept_id = #{advisorTopDeptId},</if>
            <if test="accountingDeptId != null">accounting_dept_id = #{accountingDeptId},</if>
            <if test="accountingDeptName != null">accounting_dept_name = #{accountingDeptName},</if>
            <if test="accountingTopDeptId != null">accounting_top_dept_id = #{accountingTopDeptId},</if>
            <if test="verificationEmployeeType != null">verification_employee_type = #{verificationEmployeeType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startPeriod != null">start_period = #{startPeriod},</if>
            <if test="endPeriod != null">end_period = #{endPeriod},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id = #{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name = #{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">submit_employee_id = #{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">submit_employee_name = #{submitEmployeeName},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="assignEmployeeDeptId != null">assign_employee_dept_id = #{assignEmployeeDeptId},</if>
            <if test="assignEmployeeDeptName != null">assign_employee_dept_name = #{assignEmployeeDeptName},</if>
            <if test="assignEmployeeId != null">assign_employee_id = #{assignEmployeeId},</if>
            <if test="assignEmployeeName != null">assign_employee_name = #{assignEmployeeName},</if>
            <if test="assignRemark != null">assign_remark = #{assignRemark},</if>
            <if test="assignAccountingDeptId != null">assign_accounting_dept_id = #{assignAccountingDeptId},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id = #{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name = #{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">back_employee_id = #{backEmployeeId},</if>
            <if test="backEmployeeName != null">back_employee_name = #{backEmployeeName},</if>
            <if test="backRemark != null">back_remark = #{backRemark},</if>
            <if test="deliverStatus != null">deliver_status = #{deliverStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceRepairAccountById" parameterType="Long">
        delete from c_customer_service_repair_account where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceRepairAccountByIds" parameterType="String">
        delete from c_customer_service_repair_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="searchAccounting" resultType="long">
        select distinct c_customer_service_repair_account.id
        from c_customer_service_repair_account
                 left join sys_dept on sys_dept.dept_id = c_customer_service_repair_account.accounting_dept_id
                 left join sys_employee on sys_employee.dept_id = sys_dept.dept_id
        where sys_dept.dept_name like concat('%', #{accountingEmployee}, '%') or sys_employee.employee_name like concat('%', #{accountingEmployee}, '%')
    </select>


    <select id="searchDeliverStatus" resultType="long">
        select distinct c_customer_service_repair_account.id
        from c_customer_service_repair_account


        /*
        1-待交付、2-交付中、3-已完成
        * 当补账还是待分派时，交付状态=待交付
        * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
        * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
        * 其他状态为交付中
        */

       <!-- <where>
            <if test="deliverStatus == 1">
                and c_customer_service_repair_account.status = 3
                or (
                    c_customer_service_repair_account.status = 4
                )
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_1 = temp.whole_level_count)
            </if>
            <if test="deliverStatus == 2">
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_3 = 0 and temp.case_2 &gt; 0)
            </if>
            <if test="deliverStatus == 3">
                and temp.data_count &gt; 0
                and (temp.whole_level_count &lt; temp.data_count or temp.case_3 &gt; 0)
            </if>
        </where>-->
    </select>


    <select id="countWaitDispatchByDeptAndStatus" resultType="java.lang.Long">
        select count(*)
        from c_customer_service_repair_account
        <where>
            is_del = 0 and status = #{status}
<!--            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">-->
<!--                <if test="userDept.deptType == 1">-->
<!--                    and (-->
<!--                    advisor_dept_id in-->
<!--                    <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">-->
<!--                        #{deptId}-->
<!--                    </foreach>-->
<!--                    or-->
<!--                    advisor_top_dept_id in-->
<!--                    <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">-->
<!--                        #{deptId}-->
<!--                    </foreach>-->
<!--                    )-->
<!--                </if>-->
<!--                <if test="userDept.deptType == 2">-->
<!--                    and (-->
<!--                    accounting_dept_id in-->
<!--                    <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">-->
<!--                        #{deptId}-->
<!--                    </foreach>-->
<!--                    or-->
<!--                    accounting_top_dept_id in-->
<!--                    <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">-->
<!--                        #{deptId}-->
<!--                    </foreach>-->
<!--                    )-->
<!--                </if>-->
<!--            </if>-->
        </where>
    </select>

    <select id="selectRepairAccountList" resultType="com.bxm.customer.domain.dto.repairAccount.RepairAccountDTO">
        select c_customer_service_repair_account.*,
        greatest(ifnull(c_customer_service_repair_account.submit_time, STR_TO_DATE('2000-01-01 11:11:11', '%Y-%m-%d %H:%i:%s')), c_customer_service_repair_account.create_time) as sort_num
        from c_customer_service_repair_account
        <where>
            is_del = 0
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                customer_name like concat('%', #{vo.keyWord}, '%')
                or credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and customer_service_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and customer_service_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.serviceNumber != null and vo.serviceNumber != ''">
                and service_number = #{vo.serviceNumber}
            </if>
            <if test="vo.submitEmployee != null and vo.submitEmployee != ''">
                and (
                submit_employee_dept_name like concat('%', #{vo.submitEmployee}, '%')
                or submit_employee_name like concat('%', #{vo.submitEmployee}, '%')
                )
            </if>
            <if test="vo.submitTimeStart != null">
                and submit_time &gt;= #{vo.submitTimeStart}
            </if>
            <if test="vo.submitTimeEnd != null">
                and submit_time &lt;= #{vo.submitTimeEnd}
            </if>
            <if test="commonIdsSearchVO.needSearch != null and commonIdsSearchVO.needSearch == true">
                <if test="commonIdsSearchVO.fail == null or commonIdsSearchVO.fail == false">
                    and id in
                    <foreach collection="commonIdsSearchVO.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="commonIdsSearchVO.fail != null and commonIdsSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.status != null">
                and status = #{vo.status}
            </if>
            <if test="vo.deliverStatus != null">
                and deliver_status = #{vo.deliverStatus}
            </if>
            <if test="vo.source != null and vo.source == 1">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2">
                        and assign_accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    </if>
                </if>
            </if>
        </where>
        order by sort_num desc
    </select>

</mapper>