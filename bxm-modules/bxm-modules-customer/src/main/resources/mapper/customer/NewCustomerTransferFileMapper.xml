<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerTransferFileMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerTransferFile" id="NewCustomerTransferFileResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerTransferFileVo">
        select id, customer_id, file_name, file_url, file_type, is_del, create_by, create_time, update_by, update_time from c_new_customer_transfer_file
    </sql>

    <select id="selectNewCustomerTransferFileList" parameterType="com.bxm.customer.domain.NewCustomerTransferFile" resultMap="NewCustomerTransferFileResult">
        <include refid="selectNewCustomerTransferFileVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerTransferFileById" parameterType="Long" resultMap="NewCustomerTransferFileResult">
        <include refid="selectNewCustomerTransferFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerTransferFile" parameterType="com.bxm.customer.domain.NewCustomerTransferFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_transfer_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null">file_type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerTransferFile" parameterType="com.bxm.customer.domain.NewCustomerTransferFile">
        update c_new_customer_transfer_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerTransferFileById" parameterType="Long">
        delete from c_new_customer_transfer_file where id = #{id}
    </delete>

    <delete id="deleteNewCustomerTransferFileByIds" parameterType="String">
        delete from c_new_customer_transfer_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>