<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.QualityCheckingFileMapper">
    
    <resultMap type="com.bxm.customer.domain.QualityCheckingFile" id="QualityCheckingFileResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQualityCheckingFileVo">
        select id, business_type, bysiness_id, file_url, file_size, file_name, file_type, is_del, create_by, create_time, update_by, update_time from c_quality_checking_file
    </sql>

    <select id="selectQualityCheckingFileList" parameterType="com.bxm.customer.domain.QualityCheckingFile" resultMap="QualityCheckingFileResult">
        <include refid="selectQualityCheckingFileVo"/>
        <where>  
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and bysiness_id = #{businessId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectQualityCheckingFileById" parameterType="Long" resultMap="QualityCheckingFileResult">
        <include refid="selectQualityCheckingFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertQualityCheckingFile" parameterType="com.bxm.customer.domain.QualityCheckingFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_quality_checking_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">bysiness_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQualityCheckingFile" parameterType="com.bxm.customer.domain.QualityCheckingFile">
        update c_quality_checking_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">bysiness_id = #{businessId},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQualityCheckingFileById" parameterType="Long">
        delete from c_quality_checking_file where id = #{id}
    </delete>

    <delete id="deleteQualityCheckingFileByIds" parameterType="String">
        delete from c_quality_checking_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>