<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceCashierAccountingMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceCashierAccounting" id="CustomerServiceCashierAccountingResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="period"    column="period"    />
        <result property="type"    column="type"    />
        <result property="mattersNotes"    column="matters_notes"    />
        <result property="deliverRequire"    column="deliver_require"    />
        <result property="materialMedia"    column="material_media"    />
        <result property="deliverStatus"    column="deliver_status"    />
        <result property="deliverResult"    column="deliver_result"    />
        <result property="hasChanged"    column="has_changed"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="inAccountRemark"    column="in_account_remark"    />
        <result property="majorIncomeTotal"    column="major_income_total"    />
        <result property="majorCostTotal"    column="major_cost_total"    />
        <result property="profitTotal"    column="profit_total"    />
        <result property="priorYearExpenseIncrease"    column="prior_year_expense_increase"    />
        <result property="taxReportCount"    column="tax_report_count"    />
        <result property="taxReportSalaryTotal"    column="tax_report_salary_total"    />
        <result property="rpaExeResult"    column="rpa_exe_result"    />
        <result property="tableStatusBalance"    column="table_status_balance"    />
        <result property="rpaSearchTime"    column="rpa_search_time"    />
        <result property="rpaRemark"    column="rpa_remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceCashierAccountingVo">
        select id, customer_service_id, customer_service_period_month_id, period, type, matters_notes, deliver_require, material_media, deliver_status, deliver_result, has_changed, complete_time, in_account_remark, major_income_total, major_cost_total, profit_total, prior_year_expense_increase, tax_report_count, tax_report_salary_total, rpa_exe_result, table_status_balance, rpa_search_time, rpa_remark, is_del, create_by, create_time, update_by, update_time from c_customer_service_cashier_accounting
    </sql>

    <select id="selectCustomerServiceCashierAccountingList" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccounting" resultMap="CustomerServiceCashierAccountingResult">
        <include refid="selectCustomerServiceCashierAccountingVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="mattersNotes != null  and mattersNotes != ''"> and matters_notes = #{mattersNotes}</if>
            <if test="deliverRequire != null  and deliverRequire != ''"> and deliver_require = #{deliverRequire}</if>
            <if test="materialMedia != null "> and material_media = #{materialMedia}</if>
            <if test="deliverStatus != null "> and deliver_status = #{deliverStatus}</if>
            <if test="deliverResult != null "> and deliver_result = #{deliverResult}</if>
            <if test="hasChanged != null "> and has_changed = #{hasChanged}</if>
            <if test="completeTime != null "> and complete_time = #{completeTime}</if>
            <if test="inAccountRemark != null  and inAccountRemark != ''"> and in_account_remark = #{inAccountRemark}</if>
            <if test="majorIncomeTotal != null "> and major_income_total = #{majorIncomeTotal}</if>
            <if test="majorCostTotal != null "> and major_cost_total = #{majorCostTotal}</if>
            <if test="profitTotal != null "> and profit_total = #{profitTotal}</if>
            <if test="priorYearExpenseIncrease != null  and priorYearExpenseIncrease != ''"> and prior_year_expense_increase = #{priorYearExpenseIncrease}</if>
            <if test="taxReportCount != null "> and tax_report_count = #{taxReportCount}</if>
            <if test="taxReportSalaryTotal != null "> and tax_report_salary_total = #{taxReportSalaryTotal}</if>
            <if test="rpaExeResult != null "> and rpa_exe_result = #{rpaExeResult}</if>
            <if test="tableStatusBalance != null  and tableStatusBalance != ''"> and table_status_balance = #{tableStatusBalance}</if>
            <if test="rpaSearchTime != null "> and rpa_search_time = #{rpaSearchTime}</if>
            <if test="rpaRemark != null  and rpaRemark != ''"> and rpa_remark = #{rpaRemark}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceCashierAccountingById" parameterType="Long" resultMap="CustomerServiceCashierAccountingResult">
        <include refid="selectCustomerServiceCashierAccountingVo"/>
        where id = #{id}
    </select>
    <select id="accountingCashierList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO">
        WITH tmp_max_task_ids AS (
        SELECT MAX(id) AS id, biz_id, bank_account_number
        FROM c_business_task
        WHERE `type` = 1 AND item_type = 1 AND is_del = 0 AND bank_account_number is not null
        GROUP BY biz_id, bank_account_number
        )
        SELECT
        cca.id AS id,
        cca.customer_service_id AS customerServiceId,
        cca.customer_service_period_month_id AS customerServicePeriodMonthId,
        ccs.customer_name AS customerName,
        ccs.credit_code AS creditCode,
        cca.period AS period,
        ccs.tax_type AS customerServiceTaxType,
        cspm.tax_type AS periodTaxType,
        ccs.business_dept_id AS customerServiceBusinessDeptId,
        ccs.accounting_top_dept_id AS customerServiceAccountingTopDeptId,
        ccs.advisor_dept_id AS customerServiceAdvisorDeptId,
        ccs.accounting_dept_id AS customerServiceAccountingDeptId,
        cspm.accounting_dept_id AS periodAccountingDeptId,
        cspm.advisor_dept_id as periodAdvisorDeptId,
        cca.has_bank_payment AS hasBankPayment,
        cca.has_ticket AS hasTicket,
        cca.bank_name AS bankName,
        cca.bank_account_number AS bankAccountNumber,
        CASE
        WHEN cmo.matters_notes IS NULL OR cmo.matters_notes = '' THEN 0
        ELSE 1
        END AS hasMattersNotes,
        cca.deliver_require AS deliverRequire,
        cca.material_media AS materialMedia,
        cbt.id AS taskId,
        cbt.`status` AS taskStatus,
        cca.deliver_status AS deliverStatus,
        cca.deliver_result AS deliverResult,
        cca.deliver_remark AS deliverRemark,
        cca.complete_time AS completeTime,
        cca.bank_payment_result AS bankPaymentResult,
        cca.settle_account_status AS settleAccountStatus,
        cca.end_time AS endTime,
        cca.profit_get_time AS profitGetTime,
        cca.major_income_total AS majorIncomeTotal,
        cca.major_cost_total AS majorCostTotal,
        cca.profit_total AS profitTotal,
        cca.prior_year_expense_increase AS priorYearExpenseIncrease,
        cca.tax_report_count AS taxReportCount,
        cca.tax_report_salary_total AS taxReportSalaryTotal,
        cca.table_status_balance AS tableStatusBalance,
        cca.rpa_exe_result AS rpaExeResult,
        cca.rpa_search_time AS rpaSearchTime,
        cca.rpa_remark AS rpaRemark,
        cca.has_changed AS hasChanged,
        cca.`type` AS `type`,
        cca.material_integrity as materialIntegrity,
        cca.material_supplement_status as materialSupplementStatus
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service ccs ON cca.customer_service_id = ccs.id AND ccs.is_del = 0
        JOIN
        c_customer_service_period_month cspm ON cca.customer_service_period_month_id = cspm.id
        LEFT JOIN
        c_customer_matters_notes cmo ON cca.customer_service_id = cmo.customer_service_id
        AND ((cca.`type` = 1 AND cmo.item_type = 10) OR (cca.`type` = 2 AND cmo.item_type = 9) OR (cca.`type` = 3 AND cmo.item_type = 99))
        LEFT JOIN
        (SELECT c_business_task.biz_id, c_business_task.bank_account_number, c_business_task.id, c_business_task.`status`
        FROM c_business_task
        JOIN tmp_max_task_ids ON c_business_task.id = tmp_max_task_ids.id) cbt
        ON
        cca.customer_service_period_month_id = cbt.biz_id and cca.bank_account_number = cbt.bank_account_number
        <where>
            cca.is_del = 0
            <if test="vo.materialIntegrityList != null and vo.materialIntegrityList != ''">
                <if test='vo.materialIntegrityList.contains("0")'>
                    and (cca.material_integrity is null or cca.material_integrity in (${vo.materialIntegrityList}))
                </if>
                <if test='!vo.materialIntegrityList.contains("0")'>
                    and cca.material_integrity in (${vo.materialIntegrityList})
                </if>
            </if>
            <if test="vo.materialSupplementStatus != null">
                and cca.material_supplement_status = #{vo.materialSupplementStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != '' and ids != null and ids.size > 0">
                and cca.customer_service_id in
                <foreach collection="ids" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id not in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="vo.type != null">
                        <if test="vo.type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="vo.type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
            <if test="vo.type != null">
                and cca.`type` = #{vo.type}
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%',#{vo.keyWord},'%')
                    or ccs.credit_code = #{vo.keyWord})
            </if>
            <if test="vo.customerServiceTaxType != null">
                and ccs.tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.periodTaxType != null">
                and cspm.tax_type = #{vo.periodTaxType}
            </if>
            <if test="vo.customerServiceBusinessDeptId != null">
                and ccs.business_dept_id = #{vo.customerServiceBusinessDeptId}
            </if>
            <if test="vo.customerServiceAdvisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
            </if>
            <if test="vo.customerServiceAccountingTopDeptId != null">
                and ccs.accounting_top_dept_id = #{vo.customerServiceAccountingTopDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
            </if>
            <if test="vo.periodAccountingTopDeptId != null">
                and cspm.accounting_top_dept_id = #{vo.periodAccountingTopDeptId}
            </if>
            <if test="vo.periodAccountingDeptId != null">
                and cspm.accounting_dept_id = #{vo.periodAccountingDeptId}
            </if>
            <if test="vo.periodMin != null">
                and cca.period &gt;= #{vo.periodMin}
            </if>
            <if test="vo.periodMax != null">
                and cca.period &lt;= #{vo.periodMax}
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.hasTicket != null">
                and cca.has_ticket = #{vo.hasTicket}
            </if>
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and cca.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.hasBankPayment != null">
                and cca.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="vo.hasDeliverRequire != null">
                <if test="vo.hasDeliverRequire == 0">
                    and (cca.deliver_require is null or cca.deliver_require = '')
                </if>
                <if test="vo.hasDeliverRequire == 1">
                    and cca.deliver_require is not null and cca.deliver_require != ''
                </if>
            </if>
            <if test="vo.materialMediaList != null and vo.materialMediaList != ''">
                and cca.material_media in (${vo.materialMediaList})
            </if>
            <if test="vo.taskStatusList != null and vo.taskStatusList != ''">
                <if test='vo.taskStatusList.contains("0")'>
                    and (cbt.id is null or cbt.`status` in (${vo.taskStatusList}))
                </if>
                <if test='!vo.taskStatusList.contains("0")'>
                    and cbt.`status` in (${vo.taskStatusList})
                </if>
            </if>
            <if test="vo.deliverStatusList != null and vo.deliverStatusList != ''">
                and cca.deliver_status in (${vo.deliverStatusList})
            </if>
            <if test="vo.deliverResultList != null and vo.deliverResultList != ''">
                and cca.deliver_result in (${vo.deliverResultList})
            </if>
            <if test="vo.completeTimeStart != null and vo.completeTimeStart != ''">
                and cca.complete_time &gt;= #{vo.completeTimeStart}
            </if>
            <if test="vo.completeTimeEnd != null and vo.completeTimeEnd != ''">
                and cca.complete_time &lt;= #{vo.completeTimeEnd}
            </if>
            <if test="vo.bankPaymentResultList != null and vo.bankPaymentResultList != ''">
                and cca.bank_payment_result in (${vo.bankPaymentResultList})
            </if>
            <if test="vo.settleAccountStatusList != null and vo.settleAccountStatusList != ''">
                and cca.settle_account_status in (${vo.settleAccountStatusList})
            </if>
            <if test="vo.endTimeStart != null and vo.endTimeStart != ''">
                and cca.end_time &gt;= #{vo.endTimeStart}
            </if>
            <if test="vo.endTimeEnd != null and vo.endTimeEnd != ''">
                and cca.end_time &lt;= #{vo.endTimeEnd}
            </if>
            <if test="vo.tableStatusBalance != null">
                and cca.table_status_balance = #{vo.tableStatusBalance}
            </if>
        </where>
        order by cca.period desc, cca.id desc
    </select>
    <select id="accountingCashierPeriodAccountingDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
            cspm.accounting_dept_id as deptId,
            count(1) as dataCount
        from c_customer_service_cashier_accounting cca
                 join c_customer_service ccs on cca.customer_service_id = ccs.id and ccs.is_del = 0
                 join c_customer_service_period_month cspm on cca.customer_service_period_month_id = cspm.id
        <where>
            cca.is_del = 0 and cspm.accounting_dept_id is not null and cca.period >= 202401
            <if test="type != null">
                and cca.`type` = #{type}
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="type != null">
                        <if test="type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
        </where>
        group by cspm.accounting_dept_id
    </select>
    <select id="accountingCashierCustomerAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_cashier_accounting cca
        join c_customer_service ccs on cca.customer_service_id = ccs.id and ccs.is_del = 0
        join c_customer_service_period_month cspm on cca.customer_service_period_month_id = cspm.id
        <where>
            cca.is_del = 0 and ccs.advisor_dept_id is not null and cca.period >= 202401
            <if test="type != null">
                and cca.`type` = #{type}
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="type != null">
                        <if test="type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
        </where>
        group by ccs.advisor_dept_id
    </select>
    <select id="accountingCashierListByIds"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO">
        select
            cca.id as id,
            cca.customer_service_id as customerServiceId,
            cca.customer_service_period_month_id as customerServicePeriodMonthId,
            ccs.customer_name as customerName,
            ccs.credit_code as creditCode,
            cca.period as period,
            ccs.tax_type as customerServiceTaxType,
            cspm.tax_type as periodTaxType,
            ccs.business_dept_id as customerServiceBusinessDeptId,
            ccs.accounting_top_dept_id as customerServiceAccountingTopDeptId,
            ccs.advisor_dept_id as customerServiceAdvisorDeptId,
            ccs.accounting_dept_id as customerServiceAccountingDeptId,
            cspm.accounting_dept_id as periodAccountingDeptId,
            cca.has_bank_payment as hasBankPayment,
            cca.has_ticket as hasTicket,
            cca.bank_name as bankName,
            cca.bank_account_number as bankAccountNumber,
            if(cmo.matters_notes is null or cmo.matters_notes = '', 0, 1) as hasMattersNotes,
            cca.deliver_require as deliverRequire,
            cca.material_media as materialMedia,
            cbt.id as taskId,
            cbt.`status` as taskStatus,
            cca.deliver_status as deliverStatus,
            cca.deliver_result as deliverResult,
            cca.deliver_remark as deliverRemark,
            cca.complete_time as completeTime,
            cca.deliver_remark as deliverResult,
            cca.profit_get_time as profitGetTime,
            cca.major_income_total as majorIncomeTotal,
            cca.major_cost_total as majorCostTotal,
            cca.profit_total as profitTotal,
            cca.prior_year_expense_increase as priorYearExpenseIncrease,
            cca.tax_report_count as taxReportCount,
            cca.tax_report_salary_total as taxReportSalaryTotal,
            cca.table_status_balance as tableStatusBalance,
            cca.rpa_exe_result as rpaExeResult,
            cca.rpa_search_time as rpaSearchTime,
            cca.rpa_remark as rpaRemark,
            cca.has_changed as hasChanged
        from c_customer_service_cashier_accounting cca
                 join c_customer_service ccs on cca.customer_service_id = ccs.id and ccs.is_del = 0
                 join c_customer_service_period_month cspm on cca.customer_service_period_month_id = cspm.id
                 left join c_customer_matters_notes cmo on cca.customer_service_id = cmo.customer_service_id
            and ((cca.`type` = 1 and cmo.item_type = 10) or (cca.`type` = 2 and cmo.item_type = 9) or (cca.`type` = 3 and cmo.item_type = 99))
                 left join (
            SELECT c_business_task.biz_id,c_business_task.id,c_business_task.`status` FROM c_business_task JOIN (
                SELECT MAX(id) AS id
                FROM c_business_task
                where `type` = 1 and item_type = 1 AND is_del = 0
                GROUP BY biz_id) a ON c_business_task.id = a.id) cbt on cca.customer_service_period_month_id = cbt.biz_id
        <where>
            cca.is_del = 0
            <if test="ids != null and ids.size > 0">
                and cca.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ids == null or ids.size == 0">
                and 1 = 0
            </if>
        </where>
    </select>
    <select id="getFileCountByPeriodMonthIds"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.CustomerServicePeriodMonthFileCountDTO">
        SELECT
            c_customer_service_cashier_accounting.customer_service_period_month_id AS customerServicePeriodMonthId,
            COUNT(1) AS fileCount
        FROM c_customer_service_cashier_accounting JOIN c_customer_service_cashier_accounting_file ON c_customer_service_cashier_accounting.id = c_customer_service_cashier_accounting_file.customer_service_cashier_accounting_id AND c_customer_service_cashier_accounting_file.is_del = 0 AND c_customer_service_cashier_accounting_file.file_type = #{fileType}
        WHERE c_customer_service_cashier_accounting.is_del = 0
            and c_customer_service_cashier_accounting.customer_service_period_month_id in
        <foreach collection="customerServicePeriodMonthIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        GROUP BY c_customer_service_cashier_accounting.customer_service_period_month_id
    </select>
    <select id="selectLastInAccountIdByCustomerServiceIdAndPeriod" resultType="java.lang.Long">
        select id from c_customer_service_cashier_accounting
        where is_del = 0 and customer_service_id = #{customerServiceId}
        <if test="startPeriod != null">
            and period &gt;= #{startPeriod}
        </if>
        <if test="endPeriod != null">
            and period &lt;= #{endPeriod}
        </if>
        and `type` = 1
        and (major_income_total is not null or major_cost_total is not null or profit_total is not null or prior_year_expense_increase is not null or tax_report_count is not null or tax_report_salary_total is not null)
        order by period desc
        limit 1
    </select>
    <select id="materialFilesByPeriodId"
            resultType="com.bxm.customer.domain.vo.accoutingCashier.MaterialFileSimpleVO">
        select
        ccaf.id as id,
        ccaf.file_name as fileName,
        ccaf.file_url as fileUrl,
        ccaf.file_size as fileSize,
        cca.`type` as fileType,
        cca.bank_name as bankName,
        cca.bank_account_number as bankAccountNumber,
        ccaf.file_no as fileNo,
        ccaf.file_remark as fileRemark
        from c_customer_service_cashier_accounting_file ccaf join c_customer_service_cashier_accounting cca
        left join c_customer_service_bank_account ccba on cca.customer_service_id = ccba.customer_service_id and cca.bank_account_number = ccba.bank_account_number
        on ccaf.customer_service_cashier_accounting_id = cca.id
        where ccaf.is_del = 0 and cca.is_del = 0 and ccaf.file_type = 1
        and cca.customer_service_period_month_id = #{customerServicePeriodMonthId}
        <if test="fileType != null">
            and cca.`type` = #{fileType}
        </if>
        <if test="bankName != null and bankName != ''">
            and cca.bank_name = #{bankName}
        </if>
        <if test="fileRemark != null and fileRemark != ''">
            and ccaf.file_remark like concat('%', #{fileRemark}, '%')
        </if>
        order by
        FIELD(cca.`type`, 2, 1, 3),
        ccba.create_time asc,
        CASE WHEN ccaf.file_no IS NULL THEN 1 ELSE 0 END, -- NULL 的排在后面
        ccaf.file_no ASC,
        ccaf.id ASC
    </select>
    <select id="materialFileBankSelect" resultType="java.lang.String">
        select
        distinct cca.bank_name
        from c_customer_service_cashier_accounting_file ccaf join c_customer_service_cashier_accounting cca
        where ccaf.is_del = 0 and cca.is_del = 0 and ccaf.file_type = 1
        and cca.customer_service_period_month_id = #{customerServicePeriodMonthId}
        and cca.`type` = 2
    </select>
    <select id="materialFilesByIds"
            resultType="com.bxm.customer.domain.vo.accoutingCashier.MaterialFileSimpleVO">
        select
        ccaf.id as id,
        ccaf.file_name as fileName,
        ccaf.file_url as fileUrl,
        ccaf.file_size as fileSize,
        cca.`type` as fileType,
        cca.bank_name as bankName,
        cca.bank_account_number as bankAccountNumber,
        ccaf.file_no as fileNo,
        ccaf.file_remark as fileRemark
        from c_customer_service_cashier_accounting_file ccaf join c_customer_service_cashier_accounting cca
        left join c_customer_service_bank_account ccba on cca.customer_service_id = ccba.customer_service_id and cca.bank_account_number = ccba.bank_account_number
        on ccaf.customer_service_cashier_accounting_id = cca.id
        where ccaf.is_del = 0 and cca.is_del = 0
        and ccaf.id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        order by FIELD(cca.`type`, 2, 1, 3),ccba.create_time asc, ccaf.file_no ASC,ccaf.id ASC
    </select>
    <select id="selectAccountingCashierDeliverStatusDataCount"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDeliverStatusDataCount">
        SELECT
            cca.deliver_status AS deliverStatus,
            COUNT(1) AS dataCount
        FROM
            c_customer_service_cashier_accounting cca
                JOIN
            c_customer_service_period_month spm
            ON
                cca.customer_service_period_month_id = spm.id
        WHERE
            cca.is_del = 0 AND cca.period >= 202401 AND cca.`type` = #{accountingCashierType}
            and cca.has_changed = 0
            AND cca.deliver_status IN
        <foreach collection="deliverStatusList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
        </if>
        GROUP BY cca.deliver_status
    </select>
    <select id="selectHasChangeCount" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0 AND cca.period >= 202401 AND cca.`type` = #{accountingCashierType}
        AND cca.has_changed = 1
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or cs.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or cs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </if>
    </select>
    <select id="selectAccountingCashierDeliverList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO">
        SELECT
            cca.id as customerServiceCashierAccountingId,
            cca.customer_service_id as customerServiceId,
            spm.id as customerServicePeriodMonthId,
            cs.customer_name as customerName,
            cs.credit_code as creditCode,
            spm.business_dept_id as businessDeptId,
            cca.period as period,
            cca.bank_name as bankName,
            cca.bank_account_number as bankAccountNumber,
            cca.material_media as materialMedia,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            spm.advisor_dept_id as periodAdvisorDeptId,
            spm.accounting_dept_id as periodAccountingDeptId,
            cca.has_bank_payment as hasBankPayment,
            cca.has_ticket as hasTicket,
            cca.deliver_status as deliverStatus,
            cca.has_changed as hasChanged
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        JOIN
        c_customer_service cs
        ON
        cca.customer_service_id = cs.id AND cs.is_del = 0
        WHERE
        cca.is_del = 0
        AND cca.period >= 202401
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="hasChanged != null">
            AND cca.has_changed = #{hasChanged}
        </if>
        <if test="vo.deliverStatusList != null and vo.deliverStatusList != ''">
            AND cca.deliver_status in (${vo.deliverStatusList})
        </if>
        <if test="vo.deliverStatusList == null or vo.deliverStatusList == ''">
            <if test="deliverStatus != null">
                AND cca.deliver_status = #{deliverStatus}
            </if>
        </if>
        <if test="materialIntegrity != null">
            and cca.material_integrity = #{materialIntegrity}
        </if>
        <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
            <if test="vo.customerServiceTagIncludeFlag == 1 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagIncludeFlag == 0 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id not in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="vo.periodTagName != null and vo.periodTagName != ''">
            <if test="vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id not in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="batchSearchCustomerServiceIds != null and batchSearchCustomerServiceIds.size > 0">
            and cs.id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.credit_code = #{vo.customerName})
        </if>
        <if test="vo.customerServiceTaxType != null">
            and cs.tax_type = #{vo.customerServiceTaxType}
        </if>
        <if test="vo.periodTaxType != null">
            and spm.tax_type = #{vo.periodTaxType}
        </if>
        <if test="vo.periodMin != null">
            and spm.period &gt;= #{vo.periodMin}
        </if>
        <if test="vo.periodMax != null">
            and spm.period &lt;= #{vo.periodMax}
        </if>
        <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
            and cca.bank_account_number = #{vo.bankAccountNumber}
        </if>
        <if test="vo.customerServiceAdvisorDeptId != null">
            and cs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
        </if>
        <if test="vo.customerServiceAccountingDeptId != null">
            and cs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
        </if>
        <if test="vo.periodAdvisorDeptId != null">
            and spm.advisor_dept_id = #{vo.periodAdvisorDeptId}
        </if>
        <if test="vo.periodAccountingDeptId != null">
            and spm.accounting_dept_id = #{vo.periodAccountingDeptId}
        </if>
        order by cca.period desc, cca.id desc
    </select>
    <select id="selectAccountingCashierInAccountWaitCreateList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO">
        SELECT
            spm.customer_service_id as customerServiceId,
            spm.id as customerServicePeriodMonthId,
            cs.customer_name as customerName,
            cs.credit_code as creditCode,
            spm.business_dept_id as businessDeptId,
            spm.period as period,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            spm.advisor_dept_id as periodAdvisorDeptId,
            spm.accounting_dept_id as periodAccountingDeptId,
            '待创建' as itemResult
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE spm.in_account_status = 0 AND spm.period >= 202401
        <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
            <if test="vo.customerServiceTagIncludeFlag == 1 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagIncludeFlag == 0 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id not in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="vo.periodTagName != null and vo.periodTagName != ''">
            <if test="vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id not in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="batchSearchCustomerServiceIds != null and batchSearchCustomerServiceIds.size > 0">
            and cs.id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.credit_code = #{vo.customerName})
        </if>
        <if test="vo.customerServiceTaxType != null">
            and cs.tax_type = #{vo.customerServiceTaxType}
        </if>
        <if test="vo.periodTaxType != null">
            and spm.tax_type = #{vo.periodTaxType}
        </if>
        <if test="vo.periodMin != null">
            and spm.period &gt;= #{vo.periodMin}
        </if>
        <if test="vo.periodMax != null">
            and spm.period &lt;= #{vo.periodMax}
        </if>
        <if test="vo.customerServiceAdvisorDeptId != null">
            and cs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
        </if>
        <if test="vo.customerServiceAccountingDeptId != null">
            and cs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
        </if>
        <if test="vo.periodAdvisorDeptId != null">
            and spm.advisor_dept_id = #{vo.periodAdvisorDeptId}
        </if>
        <if test="vo.periodAccountingDeptId != null">
            and spm.accounting_dept_id = #{vo.periodAccountingDeptId}
        </if>
        order by spm.period desc, cs.id desc
    </select>
    <select id="selectAccountingCashierBankWaitCreateList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO">
        SELECT
            cs.id as customerServiceId,
            spm.id as customerServicePeriodMonthId,
            cs.customer_name as customerName,
            spm.period as period,
            cs.credit_code as creditCode,
            spm.business_dept_id as businessDeptId,
            bank.id as bankId,
            bank.bank_name as bankName,
            bank.bank_account_number as bankAccountNumber,
            bank.deposit_name as depositName,
            bank.receipt_account_number as receiptAccountNumber,
            bank.receipt_status as receiptStatus,
            bank.bank_direct as bankDirect,
            bank.password as password,
            bank.remarks as remarks,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            spm.advisor_dept_id as periodAdvisorDeptId,
            spm.accounting_dept_id as periodAccountingDeptId,
            <if test="vo.statisticType == 2">
                '待创建' as itemResult
            </if>
            <if test="vo.statisticType == 9">
                '待顾问创建' as itemResult
            </if>
            <if test="vo.statisticType == 10">
                '待回单中心创建' as itemResult
            </if>
            <if test="vo.statisticType == 11">
                '银企待创建' as itemResult
            </if>
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
        bank.id is not null
        <if test="vo.statisticType == 9">
            and (bank.bank_direct is null or bank.bank_direct = 2) and (bank.receipt_status is null or bank.receipt_status = 2)
        </if>
        <if test="vo.statisticType == 10">
            and (bank.bank_direct is null or bank.bank_direct = 2) and bank.receipt_status = 1
        </if>
        <if test="vo.statisticType == 11">
            and bank.bank_direct = 1
        </if>
        and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        AND spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
            <if test="vo.customerServiceTagIncludeFlag == 1 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagIncludeFlag == 0 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id not in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="vo.periodTagName != null and vo.periodTagName != ''">
            <if test="vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id not in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="batchSearchCustomerServiceIds != null and batchSearchCustomerServiceIds.size > 0">
            and cs.id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.credit_code = #{vo.customerName})
        </if>
        <if test="vo.customerServiceTaxType != null">
            and cs.tax_type = #{vo.customerServiceTaxType}
        </if>
        <if test="vo.periodTaxType != null">
            and spm.tax_type = #{vo.periodTaxType}
        </if>
        <if test="vo.periodMin != null">
            and spm.period &gt;= #{vo.periodMin}
        </if>
        <if test="vo.periodMax != null">
            and spm.period &lt;= #{vo.periodMax}
        </if>
        <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
            and bank.bank_account_number = #{vo.bankAccountNumber}
        </if>
        <if test="vo.customerServiceAdvisorDeptId != null">
            and cs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
        </if>
        <if test="vo.customerServiceAccountingDeptId != null">
            and cs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
        </if>
        <if test="vo.periodAdvisorDeptId != null">
            and spm.advisor_dept_id = #{vo.periodAdvisorDeptId}
        </if>
        <if test="vo.periodAccountingDeptId != null">
            and spm.accounting_dept_id = #{vo.periodAccountingDeptId}
        </if>
        order by spm.period desc, cs.id desc, bank.id asc
    </select>
    <select id="selectAccountingCashierBankPatrialMisssList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO">
        SELECT
            cs.id as customerServiceId,
            spm.id as customerServicePeriodMonthId,
            cs.customer_name as customerName,
            cs.credit_code as creditCode,
            spm.business_dept_id as businessDeptId,
            spm.period as period,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            spm.advisor_dept_id as periodAdvisorDeptId,
            spm.accounting_dept_id as periodAccountingDeptId,
            case spm.bank_payment_result
            when 1 then '待创建'
            when 2 then '银行部分缺'
            else ''
            end  as itemResult
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE
        spm.bank_payment_result in (1, 2)
          and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
            <if test="vo.customerServiceTagIncludeFlag == 1 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagIncludeFlag == 0 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id not in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="vo.periodTagName != null and vo.periodTagName != ''">
            <if test="vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                and spm.id not in
                <foreach collection="customerServicePeriodMonthIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="batchSearchCustomerServiceIds != null and batchSearchCustomerServiceIds.size > 0">
            and cs.id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.credit_code = #{vo.customerName})
        </if>
        <if test="vo.customerServiceTaxType != null">
            and cs.tax_type = #{vo.customerServiceTaxType}
        </if>
        <if test="vo.periodTaxType != null">
            and spm.tax_type = #{vo.periodTaxType}
        </if>
        <if test="vo.periodMin != null">
            and spm.period &gt;= #{vo.periodMin}
        </if>
        <if test="vo.periodMax != null">
            and spm.period &lt;= #{vo.periodMax}
        </if>
        <if test="vo.customerServiceAdvisorDeptId != null">
            and cs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
        </if>
        <if test="vo.customerServiceAccountingDeptId != null">
            and cs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
        </if>
        <if test="vo.periodAdvisorDeptId != null">
            and spm.advisor_dept_id = #{vo.periodAdvisorDeptId}
        </if>
        <if test="vo.periodAccountingDeptId != null">
            and spm.accounting_dept_id = #{vo.periodAccountingDeptId}
        </if>
        order by spm.period desc, cs.id desc
    </select>
    <select id="selectAccountingCashierBankUnOpenList"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO">
        SELECT
            cs.id as customerServiceId,
            cs.customer_name as customerName,
            cs.credit_code as creditCode,
            cs.business_dept_id as businessDeptId,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            '未开户' as itemResult
        FROM c_customer_service cs
        WHERE cs.is_del = 0
        AND (cs.service_status = 1
        OR (cs.service_status = 2 AND cs.end_account_period = #{prePeriod}))
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_bank_account
        WHERE cs.id = c_customer_service_bank_account.customer_service_id
        )
        <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
            <if test="vo.customerServiceTagIncludeFlag == 1 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagIncludeFlag == 0 and tagCustomerServiceIds != null and tagCustomerServiceIds.size > 0">
                and cs.id not in
                <foreach collection="tagCustomerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="batchSearchCustomerServiceIds != null and batchSearchCustomerServiceIds.size > 0">
            and cs.id in
            <foreach collection="batchSearchCustomerServiceIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="vo.customerName != null and vo.customerName != ''">
            and (cs.customer_name like concat('%',#{vo.customerName},'%') or cs.credit_code = #{vo.customerName})
        </if>
        <if test="vo.customerServiceTaxType != null">
            and cs.tax_type = #{vo.customerServiceTaxType}
        </if>
        <if test="vo.customerServiceAdvisorDeptId != null">
            and cs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
        </if>
        <if test="vo.customerServiceAccountingDeptId != null">
            and cs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
        </if>
        order by cs.id desc
    </select>
    <select id="selectAccountingCashierBankUnOpenAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
            cs.accounting_dept_id as deptId,
            count(1) as dataCount
        FROM c_customer_service cs
        WHERE cs.is_del = 0
        AND (cs.service_status = 1
        OR (cs.service_status = 2 AND cs.end_account_period = #{prePeriod}))
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_bank_account
        WHERE cs.id = c_customer_service_bank_account.customer_service_id
        )
        and cs.accounting_dept_id is not null
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cs.accounting_dept_id
    </select>
    <select id="selectAccountingCashierBankWaitCreateAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
            spm.accounting_dept_id as deptId,
            count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
            bank.id is not null
        <if test="statisticType == 9">
            and (bank.bank_direct is null or bank.bank_direct = 2) and (bank.receipt_status is null or bank.receipt_status = 2)
        </if>
        <if test="statisticType == 10">
            and (bank.bank_direct is null or bank.bank_direct = 2) and bank.receipt_status = 1
        </if>
        <if test="statisticType == 11">
            and bank.bank_direct = 1
        </if>
        and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        and spm.accounting_dept_id is not null
        AND spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.accounting_dept_id
    </select>
    <select id="selectAccountingCashierBankPatrialMisssAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
            spm.accounting_dept_id as deptId,
            count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        WHERE
        spm.accounting_dept_id is not null
        AND spm.bank_payment_result in (1, 2)
          and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.accounting_dept_id
    </select>
    <select id="selectAccountingCashierDeliverAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0
        and spm.accounting_dept_id is not null and cca.period >= 202401
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="hasChanged != null">
            AND cca.has_changed = #{hasChanged}
        </if>
        <if test="deliverStatus != null">
            AND cca.deliver_status = #{deliverStatus}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </if>
        group by spm.accounting_dept_id
    </select>
    <select id="selectAccountingCashierInAccountWaitCreateAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
            spm.accounting_dept_id as deptId,
            count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        WHERE spm.in_account_status = 0
        and spm.accounting_dept_id is not null and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.accounting_dept_id
    </select>
    <select id="selectAccountingCashierBankUnOpenAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        cs.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM c_customer_service cs
        WHERE cs.is_del = 0
        AND (cs.service_status = 1
        OR (cs.service_status = 2 AND cs.end_account_period = #{prePeriod}))
        AND NOT EXISTS (
        SELECT 1
        FROM c_customer_service_bank_account
        WHERE cs.id = c_customer_service_bank_account.customer_service_id
        )
        and cs.advisor_dept_id is not null
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (cs.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                or cs.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or cs.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cs.advisor_dept_id
    </select>
    <select id="selectAccountingCashierBankWaitCreateAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
        bank.id is not null
        <if test="statisticType == 9">
            and (bank.bank_direct is null or bank.bank_direct = 2) and (bank.receipt_status is null or bank.receipt_status = 2)
        </if>
        <if test="statisticType == 10">
            and (bank.bank_direct is null or bank.bank_direct = 2) and bank.receipt_status = 1
        </if>
        <if test="statisticType == 11">
            and bank.bank_direct = 1
        </if>
        and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        and spm.advisor_dept_id is not null
        AND spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.advisor_dept_id
    </select>
    <select id="selectAccountingCashierBankPatrialMisssAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        WHERE
        spm.advisor_dept_id is not null
        AND spm.bank_payment_result in (1, 2)
          and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.advisor_dept_id
    </select>
    <select id="selectAccountingCashierDeliverAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0
        and spm.advisor_dept_id is not null and cca.period >= 202401
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="hasChanged != null">
            AND cca.has_changed = #{hasChanged}
        </if>
        <if test="deliverStatus != null">
            AND cca.deliver_status = #{deliverStatus}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (spm.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </if>
        group by spm.advisor_dept_id
    </select>
    <select id="selectAccountingCashierInAccountWaitCreateAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
    SELECT
        spm.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        WHERE spm.in_account_status = 0
        and spm.advisor_dept_id is not null and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.advisor_dept_id
    </select>
    <select id="selectCustomerInAccountMaxPeriod"
            resultType="com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO">
        SELECT
        customer_service_id AS customerServiceId,
        MAX(period) AS maxPeriod
        FROM c_customer_service_cashier_accounting
        WHERE is_del = 0 AND in_time IS NOT null and `type` = 1
        and customer_service_id in
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        GROUP BY customer_service_id
    </select>
    <select id="selectBatchByCustomerServicePeriodIdAndBankAccountNumber"
            resultType="com.bxm.customer.domain.CustomerServiceCashierAccounting">
        select * from
            c_customer_service_cashier_accounting
        where is_del = 0
        and `type` = 2
        and (customer_service_period_month_id, bank_account_number) in
        <foreach collection="param" separator="," item="item" close=")" open="(">
            (#{item.customerServicePeriodMonthId}, #{item.bankAccountNumber})
        </foreach>
    </select>
    <select id="accountingCashierPeriodAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cspm.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_cashier_accounting cca
        join c_customer_service ccs on cca.customer_service_id = ccs.id and ccs.is_del = 0
        join c_customer_service_period_month cspm on cca.customer_service_period_month_id = cspm.id
        <where>
            cca.is_del = 0 and cspm.advisor_dept_id is not null and cca.period >= 202401
            <if test="type != null">
                and cca.`type` = #{type}
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="type != null">
                        <if test="type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
        </where>
        group by cspm.advisor_dept_id
    </select>
    <select id="accountingCashierCustomerAccountingDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.accounting_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_cashier_accounting cca
        join c_customer_service ccs on cca.customer_service_id = ccs.id and ccs.is_del = 0
        join c_customer_service_period_month cspm on cca.customer_service_period_month_id = cspm.id
        <where>
            cca.is_del = 0 and ccs.accounting_dept_id is not null and cca.period >= 202401
            <if test="type != null">
                and cca.`type` = #{type}
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="type != null">
                        <if test="type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
        </where>
        group by ccs.accounting_dept_id
    </select>
    <select id="selectAccountingCashierInAccountWaitCreateCustomerAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE spm.in_account_status = 0
        and cs.accounting_dept_id is not null
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cs.accounting_dept_id
    </select>
    <select id="selectAccountingCashierDeliverCustomerAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        JOIN
        c_customer_service cs
        ON
        cca.customer_service_id = cs.id AND cs.is_del = 0
        WHERE
        cca.is_del = 0
        and cs.accounting_dept_id is not null
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="hasChanged != null">
            AND cca.has_changed = #{hasChanged}
        </if>
        <if test="deliverStatus != null">
            AND cca.deliver_status = #{deliverStatus}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                <if test="accountingCashierType != null">
                    <if test="accountingCashierType == 3">
                        and (cs.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cs.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="accountingCashierType != 3">
                        and (spm.accounting_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            <if test="accountingCashierType != null">
                <if test="accountingCashierType == 3">
                    and (cs.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or cs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or cs.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or cs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="accountingCashierType != 3">
                    and (cs.advisor_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or cs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or spm.accounting_dept_id in
                    <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                    or spm.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </if>
        group by cs.accounting_dept_id
    </select>
    <select id="selectAccountingCashierBankWaitCreateCustomerAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">SELECT
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        LEFT JOIN
        c_customer_service_bank_account bank
        ON
        spm.customer_service_id = bank.customer_service_id
        AND (bank.account_open_date IS NULL OR spm.period &gt;= DATE_FORMAT(bank.account_open_date, '%Y%m'))
        AND (bank.account_close_date IS NULL OR spm.period &lt;= DATE_FORMAT(bank.account_close_date, '%Y%m'))
        WHERE
        bank.id is not null and
        NOT EXISTS (
        SELECT 1
        FROM c_customer_service_cashier_accounting cca
        WHERE cca.customer_service_period_month_id = spm.id
        AND cca.is_del = 0
        AND cca.type = 2
        AND cca.bank_account_number = bank.bank_account_number
        )
        and cs.accounting_dept_id is not null
        AND spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cs.accounting_dept_id
    </select>
    <select id="selectAccountingCashierBankPatrialMisssCustomerAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        cs.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_period_month spm
        JOIN
        c_customer_service cs
        ON
        spm.customer_service_id = cs.id AND cs.is_del = 0
        WHERE
        cs.accounting_dept_id is not null
        AND spm.bank_payment_result in (1, 2)
          and spm.period != #{nowPeriod}
        and spm.period >= 202401
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (cs.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or cs.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (cs.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or cs.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by cs.accounting_dept_id
    </select>
    <select id="selectRepeatFile" resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialRepeatDTO">
        select
            c_customer_service_cashier_accounting_file.id as id,
            c_customer_service_cashier_accounting_file.file_name as fileName,
            c_customer_service_cashier_accounting_file.file_url as fileUrl,
            c_customer_service_cashier_accounting_file.file_size as fileSize,
            c_customer_service_cashier_accounting.type as accountingCashierType,
            c_customer_service_cashier_accounting.period as period,
            c_customer_service_cashier_accounting.customer_service_period_month_id as customerServicePeriodMonthId,
            c_customer_service_cashier_accounting.customer_service_id as customerServiceId,
            c_customer_service_cashier_accounting.bank_name as bankName,
            c_customer_service_cashier_accounting.bank_account_number as bankAccountNumber,
            c_customer_service_cashier_accounting_file.file_remark as fileRemark
        from c_customer_service_cashier_accounting_file
        join c_customer_service_cashier_accounting on c_customer_service_cashier_accounting_file.customer_service_cashier_accounting_id = c_customer_service_cashier_accounting.id
            and c_customer_service_cashier_accounting.is_del = 0
        where c_customer_service_cashier_accounting_file.is_del = 0
          and c_customer_service_cashier_accounting.customer_service_id = #{customerServiceId}
          and c_customer_service_cashier_accounting_file.file_name = #{fileName}
          AND c_customer_service_cashier_accounting_file.file_type = 1
        order by c_customer_service_cashier_accounting.period desc,c_customer_service_cashier_accounting.id desc
    </select>
    <select id="getAccountingCashierByPeriodIdsAndAccountingCashierType"
            resultType="com.bxm.customer.api.domain.dto.RemoteAccountingCashierSimpleDTO">
        select
            cca.id as id,
            cca.customer_service_id as customerServiceId,
            cca.customer_service_period_month_id as customerServicePeriodMonthId,
            cca.period as period,
            cs.advisor_dept_id as customerServiceAdvisorDeptId,
            cs.accounting_dept_id as customerServiceAccountingDeptId,
            spm.advisor_dept_id as periodAdvisorDeptId,
            spm.accounting_dept_id as periodAccountingDeptId,
            cca.bank_name as bankName,
            cca.bank_account_number as bankAccountNumber,
            cca.deliver_status as deliverStatus,
            cca.material_supplement_status as materialSupplementStatus,
            cca.material_media as materialMedia
            from c_customer_service_cashier_accounting cca
        join c_customer_service_period_month spm on cca.customer_service_period_month_id = spm.id
        join c_customer_service cs on cca.customer_service_id = cs.id and cs.is_del = 0
        where
            cca.is_del = 0
        <if test="vo.accountingCashierType != null">
            and cca.`type` = #{vo.accountingCashierType}
        </if>
        <if test="vo.periodIds != null and vo.periodIds.size > 0">
            and cca.customer_service_period_month_id in
            <foreach collection="vo.periodIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber"
            resultType="com.bxm.customer.domain.CustomerServiceCashierAccounting">
        select * from c_customer_service_cashier_accounting
        where is_del = 0 and `type` = 2
        and (customer_service_period_month_id, bank_account_number) in
        <foreach collection="dtoList" open="(" close=")" item="item" separator=",">
            (#{item.bizId}, #{item.bankAccountNumber})
        </foreach>
    </select>
    <select id="selectAccountingCashierLackOfMaterialCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0 AND cca.period >= 202401 AND cca.`type` = #{accountingCashierType} and cca.material_integrity = 2
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </select>
    <select id="selectAccountingCashierLakOfMaterialDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.accounting_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0 and cca.material_integrity = 2
        and spm.accounting_dept_id is not null and cca.period >= 202401
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.accounting_dept_id
    </select>
    <select id="selectAccountingCashierLakOfMaterialAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        spm.advisor_dept_id as deptId,
        count(1) as dataCount
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service_period_month spm
        ON
        cca.customer_service_period_month_id = spm.id
        WHERE
        cca.is_del = 0 and cca.material_integrity = 2
        and spm.advisor_dept_id is not null and cca.period >= 202401
        <if test="accountingCashierType != null">
            AND cca.`type` = #{accountingCashierType}
        </if>
        <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
            <if test="userDeptDTO.deptType == 1">
                and (spm.advisor_dept_id in
                <foreach collection="userDeptDTO.deptIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
                or spm.advisor_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userDeptDTO.deptType == 2">
                and (spm.accounting_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or spm.accounting_top_dept_id in
                <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>)
            </if>
        </if>
        <if test="queryDeptIds != null and queryDeptIds.size > 0">
            and (spm.advisor_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.advisor_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            or spm.accounting_dept_id in
            <foreach collection="queryDeptIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
            or spm.accounting_top_dept_id in
            <foreach collection="queryDeptIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        group by spm.advisor_dept_id
    </select>
    <select id="selectAccountCashierCount" resultType="java.lang.Long">
        <if test="vo.taskStatusList != null and vo.taskStatusList != ''">
            WITH tmp_max_task_ids AS (
            SELECT MAX(id) AS id, biz_id, bank_account_number
            FROM c_business_task
            WHERE `type` = 1 AND item_type = 1 AND is_del = 0 AND bank_account_number is not null
            GROUP BY biz_id, bank_account_number
            )
        </if>
        SELECT
        count(1)
        FROM
        c_customer_service_cashier_accounting cca
        <if test="vo.taskStatusList != null and vo.taskStatusList != ''">
            LEFT JOIN
            (SELECT c_business_task.biz_id, c_business_task.bank_account_number, c_business_task.id, c_business_task.`status`
            FROM c_business_task
            JOIN tmp_max_task_ids ON c_business_task.id = tmp_max_task_ids.id) cbt
            ON
            cca.customer_service_period_month_id = cbt.biz_id and cca.bank_account_number = cbt.bank_account_number
        </if>
        <where>
            cca.is_del = 0 and cca.period >= 202401
            <if test="vo.materialIntegrityList != null and vo.materialIntegrityList != ''">
                <if test='vo.materialIntegrityList.contains("0")'>
                    and (cca.material_integrity is null or cca.material_integrity in (${vo.materialIntegrityList}))
                </if>
                <if test='!vo.materialIntegrityList.contains("0")'>
                    and cca.material_integrity in (${vo.materialIntegrityList})
                </if>
            </if>
            <if test="vo.materialSupplementStatus != null">
                and cca.material_supplement_status = #{vo.materialSupplementStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != '' and ids != null and ids.size > 0">
                and cca.customer_service_id in
                <foreach collection="ids" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id not in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists(
                    select 1 from c_customer_service_period_month cspm
                    where cspm.id = cca.customer_service_period_month_id
                    and (cspm.advisor_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cspm.advisor_top_dept_id in
                        <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    )
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists(
                    select 1 from c_customer_service_period_month cspm
                    where cspm.id = cca.customer_service_period_month_id
                    and (cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                    )
                </if>
            </if>
            <if test="vo.type != null">
                and cca.`type` = #{vo.type}
            </if>
            <if test="(vo.keyWord != null and vo.keyWord != '') or (vo.customerServiceTaxType != null) or (vo.customerServiceBusinessDeptId != null) or (vo.customerServiceAdvisorDeptId != null) or (vo.customerServiceAccountingTopDeptId != null) or (vo.customerServiceAccountingDeptId != null)">
                and exists(
                    select 1 from c_customer_service ccs where ccs.is_del = 0 and ccs.id = cca.customer_service_id
                    <if test="vo.keyWord != null and vo.keyWord != ''">
                        and (ccs.customer_name like concat('%',#{vo.keyWord},'%')
                        or ccs.credit_code = #{vo.keyWord})
                    </if>
                    <if test="vo.customerServiceTaxType != null">
                        and ccs.tax_type = #{vo.customerServiceTaxType}
                    </if>
                    <if test="vo.customerServiceBusinessDeptId != null">
                        and ccs.business_dept_id = #{vo.customerServiceBusinessDeptId}
                    </if>
                    <if test="vo.customerServiceAdvisorDeptId != null">
                        and ccs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
                    </if>
                    <if test="vo.customerServiceAccountingTopDeptId != null">
                        and ccs.accounting_top_dept_id = #{vo.customerServiceAccountingTopDeptId}
                    </if>
                    <if test="vo.customerServiceAccountingDeptId != null">
                        and ccs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
                    </if>
                )
            </if>
            <if test="(vo.periodTaxType != null) or (vo.periodAccountingTopDeptId != null) or (vo.periodAccountingDeptId != null)">
                and exists(
                    select 1 from c_customer_service_period_month cspm where cspm.id = cca.customer_service_period_month_id
                    <if test="vo.periodTaxType != null">
                        and cspm.tax_type = #{vo.periodTaxType}
                    </if>
                    <if test="vo.periodAccountingTopDeptId != null">
                        and cspm.accounting_top_dept_id = #{vo.periodAccountingTopDeptId}
                    </if>
                    <if test="vo.periodAccountingDeptId != null">
                        and cspm.accounting_dept_id = #{vo.periodAccountingDeptId}
                    </if>
                )
            </if>
            <if test="vo.periodMin != null">
                and cca.period &gt;= #{vo.periodMin}
            </if>
            <if test="vo.periodMax != null">
                and cca.period &lt;= #{vo.periodMax}
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and not exists(
                    select 1 from c_customer_matters_notes cmo where cmo.customer_service_id = cca.customer_service_id and ((cmo.item_type = 10 and cca.type = 1) or (cmo.item_type = 9 and cca.type = 2) or (cmo.item_type = 99 and cca.type = 3))
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                    )
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and exists(
                    select 1 from c_customer_matters_notes cmo where cmo.customer_service_id = cca.customer_service_id and ((cmo.item_type = 10 and cca.type = 1) or (cmo.item_type = 9 and cca.type = 2) or (cmo.item_type = 99 and cca.type = 3))
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                    )
                </if>
            </if>
            <if test="vo.hasTicket != null">
                and cca.has_ticket = #{vo.hasTicket}
            </if>
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and cca.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.hasBankPayment != null">
                and cca.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="vo.hasDeliverRequire != null">
                <if test="vo.hasDeliverRequire == 0">
                    and (cca.deliver_require is null or cca.deliver_require = '')
                </if>
                <if test="vo.hasDeliverRequire == 1">
                    and cca.deliver_require is not null and cca.deliver_require != ''
                </if>
            </if>
            <if test="vo.materialMediaList != null and vo.materialMediaList != ''">
                and cca.material_media in (${vo.materialMediaList})
            </if>
            <if test="vo.taskStatusList != null and vo.taskStatusList != ''">
                <if test='vo.taskStatusList.contains("-1")'>
                    and (cbt.id is null or cbt.`status` in (${vo.taskStatusList}))
                </if>
                <if test='!vo.taskStatusList.contains("-1")'>
                    and cbt.`status` in (${vo.taskStatusList})
                </if>
            </if>
            <if test="vo.deliverStatusList != null and vo.deliverStatusList != ''">
                and cca.deliver_status in (${vo.deliverStatusList})
            </if>
            <if test="vo.deliverResultList != null and vo.deliverResultList != ''">
                and cca.deliver_result in (${vo.deliverResultList})
            </if>
            <if test="vo.completeTimeStart != null and vo.completeTimeStart != ''">
                and cca.complete_time &gt;= #{vo.completeTimeStart}
            </if>
            <if test="vo.completeTimeEnd != null and vo.completeTimeEnd != ''">
                and cca.complete_time &lt;= #{vo.completeTimeEnd}
            </if>
            <if test="vo.bankPaymentResultList != null and vo.bankPaymentResultList != ''">
                and cca.bank_payment_result in (${vo.bankPaymentResultList})
            </if>
            <if test="vo.settleAccountStatusList != null and vo.settleAccountStatusList != ''">
                and cca.settle_account_status in (${vo.settleAccountStatusList})
            </if>
            <if test="vo.endTimeStart != null and vo.endTimeStart != ''">
                and cca.end_time &gt;= #{vo.endTimeStart}
            </if>
            <if test="vo.endTimeEnd != null and vo.endTimeEnd != ''">
                and cca.end_time &lt;= #{vo.endTimeEnd}
            </if>
            <if test="vo.tableStatusBalance != null">
                and cca.table_status_balance = #{vo.tableStatusBalance}
            </if>
        </where>
    </select>
    <select id="accountingCashierListPage"
            resultType="com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO">
        WITH tmp_max_task_ids AS (
        SELECT MAX(id) AS id, biz_id, bank_account_number
        FROM c_business_task
        WHERE `type` = 1 AND item_type = 1 AND is_del = 0 AND bank_account_number is not null
        GROUP BY biz_id, bank_account_number
        )
        SELECT
        cca.id AS id,
        cca.customer_service_id AS customerServiceId,
        cca.customer_service_period_month_id AS customerServicePeriodMonthId,
        ccs.customer_name AS customerName,
        ccs.credit_code AS creditCode,
        cca.period AS period,
        ccs.tax_type AS customerServiceTaxType,
        cspm.tax_type AS periodTaxType,
        ccs.business_dept_id AS customerServiceBusinessDeptId,
        ccs.accounting_top_dept_id AS customerServiceAccountingTopDeptId,
        ccs.advisor_dept_id AS customerServiceAdvisorDeptId,
        ccs.accounting_dept_id AS customerServiceAccountingDeptId,
        cspm.accounting_dept_id AS periodAccountingDeptId,
        cspm.advisor_dept_id as periodAdvisorDeptId,
        cca.has_bank_payment AS hasBankPayment,
        cca.has_ticket AS hasTicket,
        cca.bank_name AS bankName,
        cca.bank_account_number AS bankAccountNumber,
        CASE
        WHEN cmo.matters_notes IS NULL OR cmo.matters_notes = '' THEN 0
        ELSE 1
        END AS hasMattersNotes,
        cca.deliver_require AS deliverRequire,
        cca.material_media AS materialMedia,
        cbt.id AS taskId,
        cbt.`status` AS taskStatus,
        cca.deliver_status AS deliverStatus,
        cca.deliver_result AS deliverResult,
        cca.deliver_remark AS deliverRemark,
        cca.complete_time AS completeTime,
        cca.bank_payment_result AS bankPaymentResult,
        cca.settle_account_status AS settleAccountStatus,
        cca.end_time AS endTime,
        cca.profit_get_time AS profitGetTime,
        cca.major_income_total AS majorIncomeTotal,
        cca.major_cost_total AS majorCostTotal,
        cca.profit_total AS profitTotal,
        cca.prior_year_expense_increase AS priorYearExpenseIncrease,
        cca.tax_report_count AS taxReportCount,
        cca.tax_report_salary_total AS taxReportSalaryTotal,
        cca.profit as profit,
        cca.tempesti as tempesti,
        cca.welfare as welfare,
        cca.enterain as enterain,
        cca.raw_material as rawMaterial,
        cca.labor_wages as laborWages,
        cca.cost as cost,
        cca.period_cost as periodCost,
        cca.table_status_balance AS tableStatusBalance,
        cca.rpa_exe_result AS rpaExeResult,
        cca.rpa_search_time AS rpaSearchTime,
        cca.rpa_remark AS rpaRemark,
        cca.has_changed AS hasChanged,
        cca.`type` AS `type`,
        cca.material_integrity as materialIntegrity,
        cca.material_supplement_status as materialSupplementStatus,
        cca.last_in_time as lastInTime,
        cca.last_end_time as lastEndTime,
        cca.create_time as createTime,
        cca.create_by as createBy,
        cca.last_oper_name as lastOperName,
        cca.last_oper_time as lastOperTime,
        cca.last_oper_type as lastOperType,
        cca.last_oper_remark as lastOperRemark,
        cca.ddl as ddl
        FROM
        c_customer_service_cashier_accounting cca
        JOIN
        c_customer_service ccs ON cca.customer_service_id = ccs.id AND ccs.is_del = 0
        JOIN
        c_customer_service_period_month cspm ON cca.customer_service_period_month_id = cspm.id
        LEFT JOIN
        c_customer_matters_notes cmo ON cca.customer_service_id = cmo.customer_service_id
        AND ((cca.`type` = 1 AND cmo.item_type = 10) OR (cca.`type` = 2 AND cmo.item_type = 9) OR (cca.`type` = 3 AND cmo.item_type = 99))
        LEFT JOIN
        (SELECT c_business_task.biz_id, c_business_task.bank_account_number, c_business_task.id, c_business_task.`status`
        FROM c_business_task
        JOIN tmp_max_task_ids ON c_business_task.id = tmp_max_task_ids.id) cbt
        ON
        cca.customer_service_period_month_id = cbt.biz_id and cca.bank_account_number = cbt.bank_account_number
        <where>
            cca.is_del = 0 and cca.period >= 202401
            <if test="vo.ddlStart != null and vo.ddlStart != ''">
                and cca.ddl &gt;= #{vo.ddlStart}
            </if>
            <if test="vo.ddlEnd != null and vo.ddlEnd != ''">
                and cca.ddl &lt;= #{vo.ddlEnd}
            </if>
            <if test="vo.materialIntegrityList != null and vo.materialIntegrityList != ''">
                <if test='vo.materialIntegrityList.contains("0")'>
                    and (cca.material_integrity is null or cca.material_integrity in (${vo.materialIntegrityList}))
                </if>
                <if test='!vo.materialIntegrityList.contains("0")'>
                    and cca.material_integrity in (${vo.materialIntegrityList})
                </if>
            </if>
            <if test="vo.materialSupplementStatus != null">
                and cca.material_supplement_status = #{vo.materialSupplementStatus}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != '' and ids != null and ids.size > 0">
                and cca.customer_service_id in
                <foreach collection="ids" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cca.customer_service_id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 1 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 0 and customerServicePeriodMonthIds != null and customerServicePeriodMonthIds.size > 0">
                    and cca.customer_service_period_month_id not in
                    <foreach collection="customerServicePeriodMonthIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="userDeptDTO.isAdmin != null and userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and (cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDeptDTO.deptType != null and userDeptDTO.deptType == 2 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    <if test="vo.type != null">
                        <if test="vo.type == 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                        <if test="vo.type != 3">
                            and (cspm.accounting_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>
                            or cspm.accounting_top_dept_id in
                            <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                                #{deptId}
                            </foreach>)
                        </if>
                    </if>
                </if>
            </if>
            <if test="vo.type != null">
                and cca.`type` = #{vo.type}
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%',#{vo.keyWord},'%')
                or ccs.credit_code = #{vo.keyWord})
            </if>
            <if test="vo.customerServiceTaxType != null">
                and ccs.tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.periodTaxType != null">
                and cspm.tax_type = #{vo.periodTaxType}
            </if>
            <if test="vo.customerServiceBusinessDeptId != null">
                and ccs.business_dept_id = #{vo.customerServiceBusinessDeptId}
            </if>
            <if test="vo.customerServiceAdvisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
            </if>
            <if test="vo.customerServiceAccountingTopDeptId != null">
                and ccs.accounting_top_dept_id = #{vo.customerServiceAccountingTopDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
            </if>
            <if test="vo.periodAccountingTopDeptId != null">
                and cspm.accounting_top_dept_id = #{vo.periodAccountingTopDeptId}
            </if>
            <if test="vo.periodAccountingDeptId != null">
                and cspm.accounting_dept_id = #{vo.periodAccountingDeptId}
            </if>
            <if test="vo.periodMin != null">
                and cca.period &gt;= #{vo.periodMin}
            </if>
            <if test="vo.periodMax != null">
                and cca.period &lt;= #{vo.periodMax}
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.hasTicket != null">
                and cca.has_ticket = #{vo.hasTicket}
            </if>
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and cca.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.bankName != null and vo.bankName != ''">
                and cca.bank_name like concat('%',#{vo.bankName},'%')
            </if>
            <if test="vo.hasBankPayment != null">
                and cca.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="vo.hasDeliverRequire != null">
                <if test="vo.hasDeliverRequire == 0">
                    and (cca.deliver_require is null or cca.deliver_require = '')
                </if>
                <if test="vo.hasDeliverRequire == 1">
                    and cca.deliver_require is not null and cca.deliver_require != ''
                </if>
            </if>
            <if test="vo.materialMediaList != null and vo.materialMediaList != ''">
                and cca.material_media in (${vo.materialMediaList})
            </if>
            <if test="vo.taskStatusList != null and vo.taskStatusList != ''">
                <if test='vo.taskStatusList.contains("-1")'>
                    and (cbt.id is null or cbt.`status` in (${vo.taskStatusList}))
                </if>
                <if test='!vo.taskStatusList.contains("-1")'>
                    and cbt.`status` in (${vo.taskStatusList})
                </if>
            </if>
            <if test="vo.deliverStatusList != null and vo.deliverStatusList != ''">
                and cca.deliver_status in (${vo.deliverStatusList})
            </if>
            <if test="vo.deliverResultList != null and vo.deliverResultList != ''">
                and cca.deliver_result in (${vo.deliverResultList})
            </if>
            <if test="vo.completeTimeStart != null and vo.completeTimeStart != ''">
                and cca.complete_time &gt;= #{vo.completeTimeStart}
            </if>
            <if test="vo.completeTimeEnd != null and vo.completeTimeEnd != ''">
                and cca.complete_time &lt;= #{vo.completeTimeEnd}
            </if>
            <if test="vo.bankPaymentResultList != null and vo.bankPaymentResultList != ''">
                and cca.bank_payment_result in (${vo.bankPaymentResultList})
            </if>
            <if test="vo.settleAccountStatusList != null and vo.settleAccountStatusList != ''">
                and cca.settle_account_status in (${vo.settleAccountStatusList})
            </if>
            <if test="vo.endTimeStart != null and vo.endTimeStart != ''">
                and cca.end_time &gt;= #{vo.endTimeStart}
            </if>
            <if test="vo.endTimeEnd != null and vo.endTimeEnd != ''">
                and cca.end_time &lt;= #{vo.endTimeEnd}
            </if>
            <if test="vo.tableStatusBalance != null">
                and cca.table_status_balance = #{vo.tableStatusBalance}
            </if>
        </where>
        order by cca.period desc, cca.id desc
        limit #{start}, #{limit}
    </select>
    <select id="selectBatchByPeriodIdAndBankAccountNumber"
            resultType="com.bxm.customer.domain.CustomerServiceCashierAccountingFile">
        select
        ccaf.id as id,
        cca.customer_service_period_month_id as customerServicePeriodMonthId,
        cca.bank_account_number as bankAccountNumber,
        ccaf.file_name as fileName,
        ccaf.file_url as fileUrl,
        ccaf.file_size as fileSize
            from
            c_customer_service_cashier_accounting cca join c_customer_service_cashier_accounting_file ccaf on cca.id = ccaf.customer_service_cashier_accounting_id
        <where>
            cca.is_del = 0 and ccaf.is_del = 0 and cca.`type` = 2 and ccaf.file_type = 1
            and (cca.customer_service_period_month_id, cca.bank_account_number) in
                <foreach collection="voList" item="item" open="(" separator="," close=")">
                    (#{item.periodId}, #{item.bankAccountNumber})
                </foreach>
        </where>
    </select>
    <insert id="saveNewPeriodInAccount">
        insert into c_customer_service_cashier_accounting (customer_service_id, customer_service_period_month_id, period,title, type, has_ticket,material_media,deliver_status,bank_payment_result,settle_account_status,last_oper_type,last_oper_name,last_oper_time,create_oper_type,is_del,create_by)
        select
            customer_service_id,
            id,
            period,
            concat('入账交付单【', period, '】'),
            1,0,3,1,bank_payment_result,1,'系统创建','系统',now(),'自动创建',0,'系统'
            from c_customer_service_period_month where period = #{period}
        and in_account_status = 1
        and not exists (
                select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.id and business_type = 2
                )
       and not exists (
                select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.customer_service_id and business_type = 1
            )
        and not exists(
            select 1 from c_customer_service_cashier_accounting where customer_service_period_month_id = c_customer_service_period_month.id and type = 1 and is_del = 0
            )
    </insert>

    <insert id="insertCustomerServiceCashierAccounting" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccounting" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_cashier_accounting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="type != null">type,</if>
            <if test="mattersNotes != null">matters_notes,</if>
            <if test="deliverRequire != null">deliver_require,</if>
            <if test="materialMedia != null">material_media,</if>
            <if test="deliverStatus != null">deliver_status,</if>
            <if test="deliverResult != null">deliver_result,</if>
            <if test="hasChanged != null">has_changed,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="inAccountRemark != null">in_account_remark,</if>
            <if test="majorIncomeTotal != null">major_income_total,</if>
            <if test="majorCostTotal != null">major_cost_total,</if>
            <if test="profitTotal != null">profit_total,</if>
            <if test="priorYearExpenseIncrease != null">prior_year_expense_increase,</if>
            <if test="taxReportCount != null">tax_report_count,</if>
            <if test="taxReportSalaryTotal != null">tax_report_salary_total,</if>
            <if test="rpaExeResult != null">rpa_exe_result,</if>
            <if test="tableStatusBalance != null">table_status_balance,</if>
            <if test="rpaSearchTime != null">rpa_search_time,</if>
            <if test="rpaRemark != null">rpa_remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="type != null">#{type},</if>
            <if test="mattersNotes != null">#{mattersNotes},</if>
            <if test="deliverRequire != null">#{deliverRequire},</if>
            <if test="materialMedia != null">#{materialMedia},</if>
            <if test="deliverStatus != null">#{deliverStatus},</if>
            <if test="deliverResult != null">#{deliverResult},</if>
            <if test="hasChanged != null">#{hasChanged},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="inAccountRemark != null">#{inAccountRemark},</if>
            <if test="majorIncomeTotal != null">#{majorIncomeTotal},</if>
            <if test="majorCostTotal != null">#{majorCostTotal},</if>
            <if test="profitTotal != null">#{profitTotal},</if>
            <if test="priorYearExpenseIncrease != null">#{priorYearExpenseIncrease},</if>
            <if test="taxReportCount != null">#{taxReportCount},</if>
            <if test="taxReportSalaryTotal != null">#{taxReportSalaryTotal},</if>
            <if test="rpaExeResult != null">#{rpaExeResult},</if>
            <if test="tableStatusBalance != null">#{tableStatusBalance},</if>
            <if test="rpaSearchTime != null">#{rpaSearchTime},</if>
            <if test="rpaRemark != null">#{rpaRemark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveExceptionPeriodInAccount">
        insert into c_customer_service_cashier_accounting (customer_service_id, customer_service_period_month_id, period,title, type, has_ticket,material_media,deliver_status,deliver_result,bank_payment_result,settle_account_status,last_oper_type,last_oper_name,last_oper_time,create_oper_type,is_del,create_by)
        select
            customer_service_id,
            id,
            period,
            concat('【入账交付单', period, '】'),
            1,1,3,3,4,bank_payment_result,1,'系统创建','系统',now(),'自动创建',0,'系统'
        from c_customer_service_period_month where period = #{period}
                                               and in_account_status = 3
                                               AND (exists (
                select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.id and business_type = 2
            ) OR
                                                    exists (
                                                        select 1 from c_business_tag_relation where tag_id = 6 and business_id = c_customer_service_period_month.customer_service_id and business_type = 1
                                                    ))
                                               and not exists(
                select 1 from c_customer_service_cashier_accounting where customer_service_period_month_id = c_customer_service_period_month.id and type = 1 and is_del = 0
            )
    </insert>
    <insert id="createFlowAccountingCashier">
        insert into c_customer_service_cashier_accounting (customer_service_id, customer_service_period_month_id, period,title, type, bank_name,bank_account_number,has_bank_payment,material_media,deliver_status,last_oper_type,last_oper_name,last_oper_time,create_oper_type,is_del,create_time,create_by)
        select
        spm.customer_service_id,
        spm.id,
        spm.period,
        concat('流水交付单【', cb.bank_name, '-', cb.bank_account_number, '-', spm.period,'】'),
        2,cb.bank_name,cb.bank_account_number,1,5,1,'系统创建','系统',#{createTime},'自动创建',0,#{createTime},'系统'
        from c_customer_service_period_month spm
        JOIN c_customer_service_bank_account cb ON spm.customer_service_id = cb.customer_service_id AND (cb.account_open_date IS NULL OR DATE_FORMAT(cb.account_open_date, '%Y%m') &lt;= spm.period)
        AND (cb.account_close_date IS NULL OR DATE_FORMAT(cb.account_close_date, '%Y%m') &gt;= spm.period) AND cb.bank_direct = 1
        <where>
            not exists(
            select 1 from c_customer_service_cashier_accounting where customer_service_period_month_id = spm.id and `type` = 2 and is_del = 0 AND bank_account_number = cb.bank_account_number
            )
            <if test="period != null">
                and spm.period = #{period}
            </if>
            <if test="customerServicePeriodMonthId != null">
                and spm.id = #{customerServicePeriodMonthId}
            </if>
        </where>
    </insert>

    <update id="updateCustomerServiceCashierAccounting" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccounting">
        update c_customer_service_cashier_accounting
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="type != null">type = #{type},</if>
            <if test="mattersNotes != null">matters_notes = #{mattersNotes},</if>
            <if test="deliverRequire != null">deliver_require = #{deliverRequire},</if>
            <if test="materialMedia != null">material_media = #{materialMedia},</if>
            <if test="deliverStatus != null">deliver_status = #{deliverStatus},</if>
            <if test="deliverResult != null">deliver_result = #{deliverResult},</if>
            <if test="hasChanged != null">has_changed = #{hasChanged},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="inAccountRemark != null">in_account_remark = #{inAccountRemark},</if>
            <if test="majorIncomeTotal != null">major_income_total = #{majorIncomeTotal},</if>
            <if test="majorCostTotal != null">major_cost_total = #{majorCostTotal},</if>
            <if test="profitTotal != null">profit_total = #{profitTotal},</if>
            <if test="priorYearExpenseIncrease != null">prior_year_expense_increase = #{priorYearExpenseIncrease},</if>
            <if test="taxReportCount != null">tax_report_count = #{taxReportCount},</if>
            <if test="taxReportSalaryTotal != null">tax_report_salary_total = #{taxReportSalaryTotal},</if>
            <if test="rpaExeResult != null">rpa_exe_result = #{rpaExeResult},</if>
            <if test="tableStatusBalance != null">table_status_balance = #{tableStatusBalance},</if>
            <if test="rpaSearchTime != null">rpa_search_time = #{rpaSearchTime},</if>
            <if test="rpaRemark != null">rpa_remark = #{rpaRemark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceCashierAccountingById" parameterType="Long">
        delete from c_customer_service_cashier_accounting where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceCashierAccountingByIds" parameterType="String">
        delete from c_customer_service_cashier_accounting where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>