<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiPersonTaxStatusSearchRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord" id="OpenApiPersonTaxStatusSearchRecordResult">
        <result property="id"    column="id"    />
        <result property="deliverId"    column="deliver_id"    />
        <result property="searchStatusParam"    column="search_status_param"    />
        <result property="searchTime"    column="search_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiPersonTaxStatusSearchRecordVo">
        select id, deliver_id, search_status_param, search_time, status, create_by, create_time, update_by, update_time from c_open_api_person_tax_status_search_record
    </sql>

    <select id="selectOpenApiPersonTaxStatusSearchRecordList" parameterType="com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord" resultMap="OpenApiPersonTaxStatusSearchRecordResult">
        <include refid="selectOpenApiPersonTaxStatusSearchRecordVo"/>
        <where>  
            <if test="deliverId != null "> and deliver_id = #{deliverId}</if>
            <if test="searchStatusParam != null  and searchStatusParam != ''"> and search_status_param = #{searchStatusParam}</if>
            <if test="searchTime != null "> and search_time = #{searchTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOpenApiPersonTaxStatusSearchRecordById" parameterType="Long" resultMap="OpenApiPersonTaxStatusSearchRecordResult">
        <include refid="selectOpenApiPersonTaxStatusSearchRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiPersonTaxStatusSearchRecord" parameterType="com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_person_tax_status_search_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deliverId != null">deliver_id,</if>
            <if test="searchStatusParam != null and searchStatusParam != ''">search_status_param,</if>
            <if test="searchTime != null">search_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deliverId != null">#{deliverId},</if>
            <if test="searchStatusParam != null and searchStatusParam != ''">#{searchStatusParam},</if>
            <if test="searchTime != null">#{searchTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiPersonTaxStatusSearchRecord" parameterType="com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord">
        update c_open_api_person_tax_status_search_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliverId != null">deliver_id = #{deliverId},</if>
            <if test="searchStatusParam != null and searchStatusParam != ''">search_status_param = #{searchStatusParam},</if>
            <if test="searchTime != null">search_time = #{searchTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiPersonTaxStatusSearchRecordById" parameterType="Long">
        delete from c_open_api_person_tax_status_search_record where id = #{id}
    </delete>

    <delete id="deleteOpenApiPersonTaxStatusSearchRecordByIds" parameterType="String">
        delete from c_open_api_person_tax_status_search_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>