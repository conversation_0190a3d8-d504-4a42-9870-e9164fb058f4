<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceDocHandoverMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceDocHandover" id="CustomerServiceDocHandoverResult">
        <result property="id"    column="id"    />
        <result property="isDel"    column="is_del"    />
        <result property="isEffect"    column="is_effect"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="period"    column="period"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="taxType"    column="tax_type"    />
        <result property="title"    column="title"    />
        <result property="batchNum"    column="batch_num"    />
        <result property="status"    column="status"    />
        <result property="isVoucherEntry"    column="is_voucher_entry"    />
        <result property="remark"    column="remark"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="submitEmployeeDeptId"    column="submit_employee_dept_id"    />
        <result property="submitEmployeeDeptName"    column="submit_employee_dept_name"    />
        <result property="submitEmployeeId"    column="submit_employee_id"    />
        <result property="submitEmployeeName"    column="submit_employee_name"    />
        <result property="verificationEmployeeType"    column="verification_employee_type"    />
        <result property="verificationTime"    column="verification_time"    />
        <result property="verificationEmployeeDeptId"    column="verification_employee_dept_id"    />
        <result property="verificationEmployeeDeptName"    column="verification_employee_dept_name"    />
        <result property="verificationEmployeeId"    column="verification_employee_id"    />
        <result property="verificationEmployeeName"    column="verification_employee_name"    />
        <result property="wholeLevel"    column="whole_level"    />
        <result property="verificationRemark"    column="verification_remark"    />
        <result property="backTime"    column="back_time"    />
        <result property="backEmployeeDeptId"    column="back_employee_dept_id"    />
        <result property="backEmployeeDeptName"    column="back_employee_dept_name"    />
        <result property="backEmployeeId"    column="back_employee_id"    />
        <result property="backEmployeeName"    column="back_employee_name"    />
        <result property="backRemark"    column="back_remark"    />
        <result property="bankInstrumentCount"    column="bank_instrument_count"    />
        <result property="bankInstrumentUnHasCount"    column="bank_instrument_un_has_count"    />
        <result property="hasTaxTicket"    column="has_tax_ticket"    />
        <result property="hasOtherTicket"    column="has_other_ticket"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceDocHandoverVo">
        select id, is_del, is_effect, customer_service_period_month_id, period, customer_service_id, customer_name, credit_code, tax_type, title, batch_num, status, is_voucher_entry, remark, submit_time, submit_employee_dept_id, submit_employee_dept_name, submit_employee_id, submit_employee_name, verification_employee_type, verification_time, verification_employee_dept_id, verification_employee_dept_name, verification_employee_id, verification_employee_name, whole_level, verification_remark, back_time, back_employee_dept_id, back_employee_dept_name, back_employee_id, back_employee_name, back_remark, bank_instrument_count, bank_instrument_un_has_count, has_tax_ticket, has_other_ticket, create_by, create_time, update_by, update_time from c_customer_service_doc_handover
    </sql>

    <select id="selectCustomerServiceDocHandoverList" parameterType="com.bxm.customer.domain.CustomerServiceDocHandover" resultMap="CustomerServiceDocHandoverResult">
        <include refid="selectCustomerServiceDocHandoverVo"/>
        <where>  
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="isEffect != null "> and is_effect = #{isEffect}</if>
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code like concat('%', #{creditCode}, '%')</if>
            <if test="taxType != null  and taxType != ''"> and tax_type like concat('%', #{taxType}, '%')</if>
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="batchNum != null  and batchNum != ''"> and batch_num = #{batchNum}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isVoucherEntry != null "> and is_voucher_entry = #{isVoucherEntry}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="submitEmployeeDeptId != null "> and submit_employee_dept_id = #{submitEmployeeDeptId}</if>
            <if test="submitEmployeeDeptName != null "> and submit_employee_dept_name = #{submitEmployeeDeptName}</if>
            <if test="submitEmployeeId != null "> and submit_employee_id = #{submitEmployeeId}</if>
            <if test="submitEmployeeName != null  and submitEmployeeName != ''"> and submit_employee_name like concat('%', #{submitEmployeeName}, '%')</if>
            <if test="verificationEmployeeType != null"> and verification_employee_type = #{verificationEmployeeType}</if>
            <if test="verificationTime != null "> and verification_time = #{verificationTime}</if>
            <if test="verificationEmployeeDeptId != null "> and verification_employee_dept_id = #{verificationEmployeeDeptId}</if>
            <if test="verificationEmployeeDeptName != null "> and verification_employee_dept_name = #{verificationEmployeeDeptName}</if>
            <if test="verificationEmployeeId != null "> and verification_employee_id = #{verificationEmployeeId}</if>
            <if test="verificationEmployeeName != null  and verificationEmployeeName != ''"> and verification_employee_name like concat('%', #{verificationEmployeeName}, '%')</if>
            <if test="wholeLevel != null "> and whole_level = #{wholeLevel}</if>
            <if test="verificationRemark != null  and verificationRemark != ''"> and verification_remark = #{verificationRemark}</if>
            <if test="backTime != null "> and back_time = #{backTime}</if>
            <if test="backEmployeeDeptId != null "> and back_employee_dept_id = #{backEmployeeDeptId}</if>
            <if test="backEmployeeDeptName != null "> and back_employee_dept_name = #{backEmployeeDeptName}</if>
            <if test="backEmployeeId != null "> and back_employee_id = #{backEmployeeId}</if>
            <if test="backEmployeeName != null  and backEmployeeName != ''"> and back_employee_name like concat('%', #{backEmployeeName}, '%')</if>
            <if test="backRemark != null  and backRemark != ''"> and back_remark = #{backRemark}</if>
            <if test="bankInstrumentCount != null  and bankInstrumentCount != ''"> and bank_instrument_count = #{bankInstrumentCount}</if>
            <if test="bankInstrumentUnHasCount != null  and bankInstrumentUnHasCount != ''"> and bank_instrument_un_has_count = #{bankInstrumentUnHasCount}</if>
            <if test="hasTaxTicket != null  and hasTaxTicket != ''"> and has_tax_ticket = #{hasTaxTicket}</if>
            <if test="hasOtherTicket != null  and hasOtherTicket != ''"> and has_other_ticket = #{hasOtherTicket}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceDocHandoverById" parameterType="Long" resultMap="CustomerServiceDocHandoverResult">
        <include refid="selectCustomerServiceDocHandoverVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceDocHandover" parameterType="com.bxm.customer.domain.CustomerServiceDocHandover" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_doc_handover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="isEffect != null">is_effect,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="title != null">title,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="status != null">status,</if>
            <if test="isVoucherEntry != null">is_voucher_entry,</if>
            <if test="remark != null">remark,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id,</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name,</if>
            <if test="submitEmployeeId != null">submit_employee_id,</if>
            <if test="submitEmployeeName != null">submit_employee_name,</if>
            <if test="verificationEmployeeType != null">verification_employee_type,</if>
            <if test="verificationTime != null">verification_time,</if>
            <if test="verificationEmployeeDeptId != null">verification_employee_dept_id,</if>
            <if test="verificationEmployeeDeptName != null">verification_employee_dept_name,</if>
            <if test="verificationEmployeeId != null">verification_employee_id,</if>
            <if test="verificationEmployeeName != null">verification_employee_name,</if>
            <if test="wholeLevel != null">whole_level,</if>
            <if test="verificationRemark != null">verification_remark,</if>
            <if test="backTime != null">back_time,</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id,</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name,</if>
            <if test="backEmployeeId != null">back_employee_id,</if>
            <if test="backEmployeeName != null">back_employee_name,</if>
            <if test="backRemark != null">back_remark,</if>
            <if test="bankInstrumentCount != null">bank_instrument_count,</if>
            <if test="bankInstrumentUnHasCount != null">bank_instrument_un_has_count,</if>
            <if test="hasTaxTicket != null">has_tax_ticket,</if>
            <if test="hasOtherTicket != null">has_other_ticket,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="isEffect != null">#{isEffect},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="title != null">#{title},</if>
            <if test="batchNum != null">#{batchNum},</if>
            <if test="status != null">#{status},</if>
            <if test="isVoucherEntry != null">#{isVoucherEntry},</if>
            <if test="remark != null">#{remark},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="submitEmployeeDeptId != null">#{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">#{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">#{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">#{submitEmployeeName},</if>
            <if test="verificationEmployeeType != null">#{verificationEmployeeType},</if>
            <if test="verificationTime != null">#{verificationTime},</if>
            <if test="verificationEmployeeDeptId != null">#{verificationEmployeeDeptId},</if>
            <if test="verificationEmployeeDeptName != null">#{verificationEmployeeDeptName},</if>
            <if test="verificationEmployeeId != null">#{verificationEmployeeId},</if>
            <if test="verificationEmployeeName != null">#{verificationEmployeeName},</if>
            <if test="wholeLevel != null">#{wholeLevel},</if>
            <if test="verificationRemark != null">#{verificationRemark},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="backEmployeeDeptId != null">#{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">#{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">#{backEmployeeId},</if>
            <if test="backEmployeeName != null">#{backEmployeeName},</if>
            <if test="backRemark != null">#{backRemark},</if>
            <if test="bankInstrumentCount != null">#{bankInstrumentCount},</if>
            <if test="bankInstrumentUnHasCount != null">#{bankInstrumentUnHasCount},</if>
            <if test="hasTaxTicket != null">#{hasTaxTicket},</if>
            <if test="hasOtherTicket != null">#{hasOtherTicket},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceDocHandover" parameterType="com.bxm.customer.domain.CustomerServiceDocHandover">
        update c_customer_service_doc_handover
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isEffect != null">is_effect = #{isEffect},</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="title != null">title = #{title},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isVoucherEntry != null">is_voucher_entry = #{isVoucherEntry},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id = #{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name = #{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">submit_employee_id = #{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">submit_employee_name = #{submitEmployeeName},</if>
            <if test="verificationEmployeeType != null">verification_employee_type = #{verificationEmployeeType},</if>
            <if test="verificationTime != null">verification_time = #{verificationTime},</if>
            <if test="verificationEmployeeDeptId != null">verification_employee_dept_id = #{verificationEmployeeDeptId},</if>
            <if test="verificationEmployeeDeptName != null">verification_employee_dept_name = #{verificationEmployeeDeptName},</if>
            <if test="verificationEmployeeId != null">verification_employee_id = #{verificationEmployeeId},</if>
            <if test="verificationEmployeeName != null">verification_employee_name = #{verificationEmployeeName},</if>
            <if test="wholeLevel != null">whole_level = #{wholeLevel},</if>
            <if test="verificationRemark != null">verification_remark = #{verificationRemark},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id = #{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name = #{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">back_employee_id = #{backEmployeeId},</if>
            <if test="backEmployeeName != null">back_employee_name = #{backEmployeeName},</if>
            <if test="backRemark != null">back_remark = #{backRemark},</if>
            <if test="bankInstrumentCount != null">bank_instrument_count = #{bankInstrumentCount},</if>
            <if test="bankInstrumentUnHasCount != null">bank_instrument_un_has_count = #{bankInstrumentUnHasCount},</if>
            <if test="hasTaxTicket != null">has_tax_ticket = #{hasTaxTicket},</if>
            <if test="hasOtherTicket != null">has_other_ticket = #{hasOtherTicket},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceDocHandoverById" parameterType="Long">
        delete from c_customer_service_doc_handover where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceDocHandoverByIds" parameterType="String">
        delete from c_customer_service_doc_handover where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectDocHandoverList" resultType="com.bxm.customer.domain.dto.docHandover.DocHandoverDTO">
        select c_customer_service_doc_handover.*,
        greatest(ifnull(c_customer_service_doc_handover.submit_time, STR_TO_DATE('2000-01-01 11:11:11', '%Y-%m-%d %H:%i:%s')), c_customer_service_doc_handover.create_time) as sort_num
        from c_customer_service_doc_handover
        <where>
            is_del = 0 and is_effect = 1
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                customer_name like concat('%', #{vo.keyWord}, '%')
                or credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and customer_service_period_month_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and customer_service_period_month_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and tax_type = #{vo.taxType}
            </if>
            <if test="vo.submitEmployee != null and vo.submitEmployee != ''">
                and (
                submit_employee_dept_name like concat('%', #{vo.submitEmployee}, '%')
                or submit_employee_name like concat('%', #{vo.submitEmployee}, '%')
                )
            </if>
            <if test="vo.submitTimeStart != null">
                and submit_time &gt;= #{vo.submitTimeStart}
            </if>
            <if test="vo.submitTimeEnd != null">
                and submit_time &lt;= #{vo.submitTimeEnd}
            </if>
            <if test="vo.periodStart != null">
                and period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.isVoucherEntry != null">
                and is_voucher_entry = #{vo.isVoucherEntry}
            </if>
            <if test="commonIdsSearchVO.needSearch != null and commonIdsSearchVO.needSearch == true">
                <if test="commonIdsSearchVO.fail == null or commonIdsSearchVO.fail == false">
                    and customer_service_period_month_id in
                    <foreach collection="commonIdsSearchVO.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="commonIdsSearchVO.fail != null and commonIdsSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.status != null">
                and status = #{vo.status}
            </if>
            <if test="vo.wholeLevel != null">
                and whole_level = #{vo.wholeLevel}
            </if>
        </where>
        order by sort_num desc
    </select>


    <select id="selectDocHandoverListV2" resultType="com.bxm.customer.domain.dto.docHandover.DocHandoverDTO">
        select c_customer_service_doc_handover.*,
        greatest(ifnull(c_customer_service_doc_handover.submit_time, STR_TO_DATE('2000-01-01 11:11:11', '%Y-%m-%d %H:%i:%s')), c_customer_service_doc_handover.create_time) as sort_num
        from c_customer_service_doc_handover
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_customer_service_doc_handover.customer_service_period_month_id
        <where>
            c_customer_service_doc_handover.is_del = 0 and c_customer_service_doc_handover.is_effect = 1

            <if test="userDept.isAdmin == null or userDept.isAdmin == 0">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_doc_handover.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_customer_service_doc_handover.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_customer_service_doc_handover.customer_service_period_month_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_customer_service_doc_handover.customer_service_period_month_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_doc_handover.tax_type = #{vo.taxType}
            </if>
            <if test="vo.submitEmployee != null and vo.submitEmployee != ''">
                and (
                c_customer_service_doc_handover.submit_employee_dept_name like concat('%', #{vo.submitEmployee}, '%')
                or c_customer_service_doc_handover.submit_employee_name like concat('%', #{vo.submitEmployee}, '%')
                )
            </if>
            <if test="vo.submitTimeStart != null">
                and c_customer_service_doc_handover.submit_time &gt;= #{vo.submitTimeStart}
            </if>
            <if test="vo.submitTimeEnd != null">
                and c_customer_service_doc_handover.submit_time &lt;= #{vo.submitTimeEnd}
            </if>
            <if test="vo.periodStart != null">
                and c_customer_service_doc_handover.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_customer_service_doc_handover.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.isVoucherEntry != null">
                and c_customer_service_doc_handover.is_voucher_entry = #{vo.isVoucherEntry}
            </if>
            <if test="commonIdsSearchVO.needSearch != null and commonIdsSearchVO.needSearch == true">
                <if test="commonIdsSearchVO.fail == null or commonIdsSearchVO.fail == false">
                    and c_customer_service_doc_handover.customer_service_period_month_id in
                    <foreach collection="commonIdsSearchVO.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="commonIdsSearchVO.fail != null and commonIdsSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.status != null">
                and c_customer_service_doc_handover.status = #{vo.status}
            </if>
            <if test="vo.wholeLevel != null">
                and c_customer_service_doc_handover.whole_level = #{vo.wholeLevel}
            </if>
        </where>
        order by sort_num desc
    </select>


    <insert id="insertCustomerServiceDocHandoverWithBatchNum" parameterType="com.bxm.customer.domain.CustomerServiceDocHandover" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_doc_handover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="isEffect != null">is_effect,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="title != null">title,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="status != null">status,</if>
            <if test="isVoucherEntry != null">is_voucher_entry,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="isEffect != null">#{isEffect},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="title != null">#{title},</if>
            <if test="batchNum != null">(select ifnull(temp.maxId, 0) + #{batchNum} as maxId from (select max(batch_num) as maxId from c_customer_service_doc_handover) as temp),</if>
            <if test="status != null">#{status},</if>
            <if test="isVoucherEntry != null">#{isVoucherEntry},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <select id="searchWholeLevelTotal" resultType="long">
        select temp.c_customer_service_period_month_id
        from (
                 select c_customer_service_period_month.id                     as c_customer_service_period_month_id,
                        count(c_customer_service_doc_handover.id)              as data_count,
                        count(c_customer_service_doc_handover.whole_level)     as whole_level_count,
                        count(c_customer_service_doc_handover.whole_level = 1 or null) as case_1,
                        count(c_customer_service_doc_handover.whole_level = 2 or null) as case_2,
                        count(c_customer_service_doc_handover.whole_level = 3 or null) as case_3
                 from c_customer_service_period_month
                          left join c_customer_service_doc_handover on c_customer_service_doc_handover.customer_service_period_month_id = c_customer_service_period_month.id
                 where c_customer_service_doc_handover.is_del = 0 and c_customer_service_doc_handover.is_effect = 1
                 group by c_customer_service_period_month.id
                 order by c_customer_service_period_month.id
             ) as temp
        <where>
            <if test="wholeLevel == 0">
                and temp.data_count = 0
            </if>
            <if test="wholeLevel == 1">
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_1 = temp.whole_level_count)
            </if>
            <if test="wholeLevel == 2">
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_3 = 0 and temp.case_2 &gt; 0)
            </if>
            <if test="wholeLevel == 3">
                and temp.data_count &gt; 0
                and (temp.whole_level_count &lt; temp.data_count or temp.case_3 &gt; 0)
            </if>
        </where>
    </select>

<!--    /*-->
<!--    * 账期材料完整度，是根据账期下关联的多个材料交接单逻辑计算的，逻辑和状态展示顺序如下：-->
<!--    *-->
<!--    * 未提交材料：无材料交接单-->
<!--    * 待核验：有至少一条交接单是待核验-->
<!--    * 无材料：所有交接单都是已核验，且都是无材料-->

<!--    * 有缺失：所有交接单都是已核验，且有一条是有缺失-->
<!--    * 缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐-->
<!--    * 已完整：所有交接单都是已核验，且所有交接单都是已完整-->

<!--    材料缺失：1-已完整、2-缺但齐、3-有缺失，  -2是未提交材料  4是待核验  5是无材料-->
<!--    */-->
    <select id="searchWholeLevelTotalV2" resultType="long">
        select temp.c_customer_service_period_month_id
        from (
        select c_customer_service_period_month.id as c_customer_service_period_month_id,
        count(c_customer_service_doc_handover.id) as data_count,
        count(c_customer_service_doc_handover.whole_level) as whole_level_count,
        count(c_customer_service_doc_handover.whole_level = 1 or null) as case_1,
        count(c_customer_service_doc_handover.whole_level = 2 or null) as case_2,
        count(c_customer_service_doc_handover.whole_level = 3 or null) as case_3
        from c_customer_service_period_month
        left join c_customer_service_doc_handover on c_customer_service_doc_handover.customer_service_period_month_id = c_customer_service_period_month.id
        where c_customer_service_doc_handover.is_del = 0 and c_customer_service_doc_handover.is_effect = 1
          and c_customer_service_doc_handover.status = 3
        group by c_customer_service_period_month.id
        order by c_customer_service_period_month.id
        ) as temp
        <where>
            <if test="wholeLevel == 1">
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_1 = temp.whole_level_count)
            </if>
            <if test="wholeLevel == 2">
                and temp.data_count &gt; 0
                and (temp.whole_level_count = temp.data_count and temp.case_3 = 0 and temp.case_2 &gt; 0)
            </if>
            <if test="wholeLevel == 3">
                and temp.data_count &gt; 0
                and (temp.whole_level_count &lt; temp.data_count or temp.case_3 &gt; 0)
            </if>
        </where>
    </select>

    <select id="selectDocHandoverStatisticByYearAndUserDept" resultMap="CustomerServiceDocHandoverResult">
        select
        cs.id,
        cs.period,
        cs.whole_level
        from c_customer_service_doc_handover cs join c_customer_service_period_month cspm on cs.customer_service_period_month_id = cspm.id
        <where>
            cs.is_del = 0
            <if test="year != null">
                and cs.period like concat(#{year}, '%')
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    and cspm.id in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType} and cspm.id not in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>