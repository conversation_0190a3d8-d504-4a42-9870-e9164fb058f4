<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceCashierAccountingFileMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceCashierAccountingFile" id="CustomerServiceCashierAccountingFileResult">
        <result property="id"    column="id"    />
        <result property="customerServiceCashierAccountingId"    column="customer_service_cashier_accounting_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceCashierAccountingFileVo">
        select id, customer_service_cashier_accounting_id, file_url, file_size, file_name, file_type, is_del, create_by, create_time, update_by, update_time from c_customer_service_cashier_accounting_file
    </sql>

    <select id="selectCustomerServiceCashierAccountingFileList" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccountingFile" resultMap="CustomerServiceCashierAccountingFileResult">
        <include refid="selectCustomerServiceCashierAccountingFileVo"/>
        <where>  
            <if test="customerServiceCashierAccountingId != null "> and customer_service_cashier_accounting_id = #{customerServiceCashierAccountingId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceCashierAccountingFileById" parameterType="Long" resultMap="CustomerServiceCashierAccountingFileResult">
        <include refid="selectCustomerServiceCashierAccountingFileVo"/>
        where id = #{id}
    </select>
    <select id="selectByBatchCustomerServiceIdAndFileName"
            resultType="com.bxm.customer.domain.CustomerServiceCashierAccountingFile">
        select
        c_customer_service_cashier_accounting_file.id as id,
        c_customer_service_cashier_accounting_file.file_name as fileName,
        c_customer_service_cashier_accounting.customer_service_id as customerServiceId
        from c_customer_service_cashier_accounting_file
        join c_customer_service_cashier_accounting on c_customer_service_cashier_accounting_file.customer_service_cashier_accounting_id = c_customer_service_cashier_accounting.id
        and c_customer_service_cashier_accounting.is_del = 0
        where c_customer_service_cashier_accounting_file.is_del = 0
        and c_customer_service_cashier_accounting_file.file_type = 1
        and (c_customer_service_cashier_accounting.customer_service_id, c_customer_service_cashier_accounting_file.file_name) in
        <foreach collection="voList" item="vo" separator="," open="(" close=")">
            (#{vo.customerServiceId}, #{vo.fileName})
        </foreach>
    </select>

    <insert id="insertCustomerServiceCashierAccountingFile" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccountingFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_cashier_accounting_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceCashierAccountingId != null">customer_service_cashier_accounting_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceCashierAccountingId != null">#{customerServiceCashierAccountingId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceCashierAccountingFile" parameterType="com.bxm.customer.domain.CustomerServiceCashierAccountingFile">
        update c_customer_service_cashier_accounting_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceCashierAccountingId != null">customer_service_cashier_accounting_id = #{customerServiceCashierAccountingId},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceCashierAccountingFileById" parameterType="Long">
        delete from c_customer_service_cashier_accounting_file where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceCashierAccountingFileByIds" parameterType="String">
        delete from c_customer_service_cashier_accounting_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>