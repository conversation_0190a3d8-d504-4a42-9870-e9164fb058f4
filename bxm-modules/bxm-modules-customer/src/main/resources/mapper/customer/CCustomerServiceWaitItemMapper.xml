<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CCustomerServiceWaitItemMapper">
    
    <resultMap type="com.bxm.customer.domain.CCustomerServiceWaitItem" id="CCustomerServiceWaitItemResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="itemType"    column="item_type"    />
        <result property="itemContent"    column="item_content"    />
        <result property="isValid"    column="is_valid"    />
        <result property="doneStatus"    column="done_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCCustomerServiceWaitItemVo">
        select id, customer_service_id, item_type, item_content, is_valid, done_status, create_by, create_time, update_by, update_time from c_customer_service_wait_item
    </sql>

    <select id="selectCCustomerServiceWaitItemList" parameterType="com.bxm.customer.domain.CCustomerServiceWaitItem" resultMap="CCustomerServiceWaitItemResult">
        <include refid="selectCCustomerServiceWaitItemVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="itemType != null "> and item_type = #{itemType}</if>
            <if test="itemContent != null  and itemContent != ''"> and item_content = #{itemContent}</if>
            <if test="isValid != null "> and is_valid = #{isValid}</if>
            <if test="doneStatus != null "> and done_status = #{doneStatus}</if>
        </where>
    </select>
    
    <select id="selectCCustomerServiceWaitItemById" parameterType="Long" resultMap="CCustomerServiceWaitItemResult">
        <include refid="selectCCustomerServiceWaitItemVo"/>
        where id = #{id}
    </select>
    <select id="customerServiceWaitItemList"
            resultType="com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO">
        select
            csw.id as id,
            csw.customer_service_id as customerServiceId,
            cs.customer_name as customerName,
            cs.customer_company_name as customerCompanyName,
            cs.service_number as serviceNumber,
            cs.tax_type as taxType,
            csw.item_content as itemContent,
            sd1.dept_name as businessDeptName,
            sd2.dept_id as advisorDeptId,
            sd2.dept_name as advisorDeptName,
            sd3.dept_id as accountingDeptId,
            sd3.dept_name as accountingDeptName,
            cs.business_dept_id as businessDeptId,
            cs.business_top_dept_id as businessTopDeptId,
            cs.accounting_top_dept_id as accountingTopDeptId
            from c_customer_service_wait_item csw join c_customer_service cs on csw.customer_service_id = cs.id and cs.is_del = 0
            left join sys_dept sd1 on cs.business_dept_id = sd1.dept_id
            left join sys_dept sd2 on cs.advisor_dept_id = sd2.dept_id
            left join sys_dept sd3 on cs.accounting_dept_id = sd3.dept_id
        <where>
            cs.is_del = 0 and csw.is_valid = 1 and csw.done_status = 0
            <if test="tagName != null and tagName != ''">
                <if test="tagIncludeFlag != null and tagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cs.id in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="tagIncludeFlag != null and tagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and cs.id not in
                    <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="taxType != null">
                and cs.tax_type = #{taxType}
            </if>
            <if test="itemType != null">
                and csw.item_type = #{itemType}
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (cs.customer_name like concat('%',#{keyWord},'%') or cs.customer_company_name like concat('%',#{keyWord},'%') or cs.credit_code like concat('%',#{keyWord},'%'))
            </if>
            <if test="advisorSearchDeptIds != null and advisorSearchDeptIds.size > 0">
                and (cs.advisor_dept_id in
                <foreach collection="advisorSearchDeptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                or
                cs.advisor_top_dept_id in
                <foreach collection="advisorSearchDeptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="accountingSearchDeptIds != null and accountingSearchDeptIds.size > 0">
                and (cs.accounting_dept_id in
                <foreach collection="accountingSearchDeptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                or
                cs.accounting_top_dept_id in
                <foreach collection="accountingSearchDeptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>)
            </if>
            <if test="jumpType == null">
                <if test="isAdmin == 0 and deptIds != null and deptIds.size > 0">
                    and (cs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="queryDeptIds != null and queryDeptIds.size > 0">
                    and (cs.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="jumpType != null and jumpType == 1">
                <if test="isAdmin == 0 and deptIds != null and deptIds.size > 0">
                    and (cs.advisor_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="queryDeptIds != null and queryDeptIds.size > 0">
                    and (cs.advisor_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="jumpType != null and jumpType == 2">
                <if test="isAdmin == 0 and deptIds != null and deptIds.size > 0">
                    and (cs.accounting_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="queryDeptIds != null and queryDeptIds.size > 0">
                    and (cs.accounting_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="queryDeptIds" item="deptId" separator="," close=")" open="(">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
        </where>
        <if test="itemType != null">
            <if test="itemType == 1 or itemType == 2">
                order by cs.advisor_dept_id
            </if>
            <if test="itemType != 1 and itemType != 2">
                order by csw.create_time desc
            </if>
        </if>
        <if test="itemType == null">
            order by csw.create_time desc
        </if>
    </select>
    <select id="selectByDeptIdsAndItemType" resultType="com.bxm.customer.domain.CCustomerServiceWaitItem">
        select
            cst.id as id,
            cst.item_type as itemType,
            ccs.accounting_dept_id as accountingDeptId
            from c_customer_service_wait_item cst join c_customer_service ccs on cst.customer_service_id = ccs.id
        <where>
            ccs.is_del = 0 and cst.is_valid = 1 and cst.done_status = 0
            <if test="itemType != null">
                and cst.item_type = #{itemType}
            </if>
            <if test="deptIds != null and deptIds.size > 0">
                and (ccs.advisor_dept_id in
                <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                or
                ccs.advisor_top_dept_id in
                <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                or
                ccs.accounting_dept_id in
                <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                or
                ccs.accounting_top_dept_id in
                <foreach collection="deptIds" item="deptId" separator="," close=")" open="(">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <insert id="insertCCustomerServiceWaitItem" parameterType="com.bxm.customer.domain.CCustomerServiceWaitItem" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_wait_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="itemType != null">item_type,</if>
            <if test="itemContent != null">item_content,</if>
            <if test="isValid != null">is_valid,</if>
            <if test="doneStatus != null">done_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="itemContent != null">#{itemContent},</if>
            <if test="isValid != null">#{isValid},</if>
            <if test="doneStatus != null">#{doneStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCCustomerServiceWaitItem" parameterType="com.bxm.customer.domain.CCustomerServiceWaitItem">
        update c_customer_service_wait_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="itemContent != null">item_content = #{itemContent},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="doneStatus != null">done_status = #{doneStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCCustomerServiceWaitItemById" parameterType="Long">
        delete from c_customer_service_wait_item where id = #{id}
    </delete>

    <delete id="deleteCCustomerServiceWaitItemByIds" parameterType="String">
        delete from c_customer_service_wait_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>