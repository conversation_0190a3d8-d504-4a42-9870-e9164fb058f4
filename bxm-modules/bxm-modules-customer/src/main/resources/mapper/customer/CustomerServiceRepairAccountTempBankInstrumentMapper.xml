<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceRepairAccountTempBankInstrumentMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument" id="CustomerServiceRepairAccountTempBankInstrumentResult">
        <result property="id"    column="id"    />
        <result property="customerServiceRepairAccountId"    column="customer_service_repair_account_id"    />
        <result property="period"    column="period"    />
        <result property="bankName"    column="bank_name"    />
        <result property="has"    column="has"    />
        <result property="hasPayment"    column="has_payment"    />
        <result property="hasCheckTicket"    column="has_check_ticket"    />
        <result property="checkTicketContent"    column="check_ticket_content"    />
        <result property="hasBackTicket"    column="has_back_ticket"    />
        <result property="backTicketContent"    column="back_ticket_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceRepairAccountTempBankInstrumentVo">
        select id, customer_service_repair_account_id, period, bank_name, has, has_payment, has_check_ticket, check_ticket_content, has_back_ticket, back_ticket_content, create_by, create_time, update_by, update_time from c_customer_service_repair_account_temp_bank_instrument
    </sql>

    <select id="selectCustomerServiceRepairAccountTempBankInstrumentList" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument" resultMap="CustomerServiceRepairAccountTempBankInstrumentResult">
        <include refid="selectCustomerServiceRepairAccountTempBankInstrumentVo"/>
        <where>  
            <if test="customerServiceRepairAccountId != null "> and customer_service_repair_account_id = #{customerServiceRepairAccountId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="has != null "> and has = #{has}</if>
            <if test="hasPayment != null "> and has_payment = #{hasPayment}</if>
            <if test="hasCheckTicket != null "> and has_check_ticket = #{hasCheckTicket}</if>
            <if test="checkTicketContent != null "> and check_ticket_content = #{checkTicketContent}</if>
            <if test="hasBackTicket != null "> and has_back_ticket = #{hasBackTicket}</if>
            <if test="backTicketContent != null "> and back_ticket_content = #{backTicketContent}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceRepairAccountTempBankInstrumentById" parameterType="Long" resultMap="CustomerServiceRepairAccountTempBankInstrumentResult">
        <include refid="selectCustomerServiceRepairAccountTempBankInstrumentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceRepairAccountTempBankInstrument" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account_temp_bank_instrument
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id,</if>
            <if test="period != null">period,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="has != null">has,</if>
            <if test="hasPayment != null">has_payment,</if>
            <if test="hasCheckTicket != null">has_check_ticket,</if>
            <if test="checkTicketContent != null">check_ticket_content,</if>
            <if test="hasBackTicket != null">has_back_ticket,</if>
            <if test="backTicketContent != null">back_ticket_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">#{customerServiceRepairAccountId},</if>
            <if test="period != null">#{period},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="has != null">#{has},</if>
            <if test="hasPayment != null">#{hasPayment},</if>
            <if test="hasCheckTicket != null">#{hasCheckTicket},</if>
            <if test="checkTicketContent != null">#{checkTicketContent},</if>
            <if test="hasBackTicket != null">#{hasBackTicket},</if>
            <if test="backTicketContent != null">#{backTicketContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceRepairAccountTempBankInstrument" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument">
        update c_customer_service_repair_account_temp_bank_instrument
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id = #{customerServiceRepairAccountId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="has != null">has = #{has},</if>
            <if test="hasPayment != null">has_payment = #{hasPayment},</if>
            <if test="hasCheckTicket != null">has_check_ticket = #{hasCheckTicket},</if>
            <if test="checkTicketContent != null">check_ticket_content = #{checkTicketContent},</if>
            <if test="hasBackTicket != null">has_back_ticket = #{hasBackTicket},</if>
            <if test="backTicketContent != null">back_ticket_content = #{backTicketContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceRepairAccountTempBankInstrumentById" parameterType="Long">
        delete from c_customer_service_repair_account_temp_bank_instrument where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceRepairAccountTempBankInstrumentByIds" parameterType="String">
        delete from c_customer_service_repair_account_temp_bank_instrument where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>