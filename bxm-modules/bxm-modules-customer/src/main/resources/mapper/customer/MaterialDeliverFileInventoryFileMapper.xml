<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverFileInventoryFileMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliverFileInventoryFile" id="MaterialDeliverFileInventoryFileResult">
        <result property="id"    column="id"    />
        <result property="fileInventoryId"    column="file_inventory_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverFileInventoryFileVo">
        select id, file_inventory_id, file_url, file_size, file_name, file_type, is_del, create_by, create_time, update_by, update_time from c_material_deliver_file_inventory_file
    </sql>

    <select id="selectMaterialDeliverFileInventoryFileList" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventoryFile" resultMap="MaterialDeliverFileInventoryFileResult">
        <include refid="selectMaterialDeliverFileInventoryFileVo"/>
        <where>  
            <if test="fileInventoryId != null "> and file_inventory_id = #{fileInventoryId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverFileInventoryFileById" parameterType="Long" resultMap="MaterialDeliverFileInventoryFileResult">
        <include refid="selectMaterialDeliverFileInventoryFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMaterialDeliverFileInventoryFile" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventoryFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver_file_inventory_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileInventoryId != null">file_inventory_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileInventoryId != null">#{fileInventoryId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliverFileInventoryFile" parameterType="com.bxm.customer.domain.MaterialDeliverFileInventoryFile">
        update c_material_deliver_file_inventory_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileInventoryId != null">file_inventory_id = #{fileInventoryId},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverFileInventoryFileById" parameterType="Long">
        delete from c_material_deliver_file_inventory_file where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverFileInventoryFileByIds" parameterType="String">
        delete from c_material_deliver_file_inventory_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>