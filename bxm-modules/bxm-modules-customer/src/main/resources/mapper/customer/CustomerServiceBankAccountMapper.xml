<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceBankAccountMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceBankAccount" id="CustomerServiceBankAccountResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccountNumber"    column="bank_account_number"    />
        <result property="password"    column="password"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="accountOpenDate"    column="account_open_date"    />
        <result property="accountCloseDate"    column="account_close_date"    />
        <result property="receiptStatus"    column="receipt_status"    />
        <result property="bankDirect"    column="bank_direct"    />
        <result property="status"    column="status"    />
        <result property="remarks"    column="remarks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceBankAccountVo">
        select id, customer_service_id, bank_name, bank_account_number, password, phone_number, account_open_date, account_close_date, receipt_status, bank_direct, status, remarks, create_by, create_time, update_by, update_time from c_customer_service_bank_account
    </sql>

    <select id="selectCustomerServiceBankAccountList" parameterType="com.bxm.customer.domain.CustomerServiceBankAccount" resultMap="CustomerServiceBankAccountResult">
        <include refid="selectCustomerServiceBankAccountVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankAccountNumber != null  and bankAccountNumber != ''"> and bank_account_number = #{bankAccountNumber}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="accountOpenDate != null "> and account_open_date = #{accountOpenDate}</if>
            <if test="accountCloseDate != null "> and account_close_date = #{accountCloseDate}</if>
            <if test="receiptStatus != null "> and receipt_status = #{receiptStatus}</if>
            <if test="bankDirect != null "> and bank_direct = #{bankDirect}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceBankAccountById" parameterType="Long" resultMap="CustomerServiceBankAccountResult">
        <include refid="selectCustomerServiceBankAccountVo"/>
        where id = #{id}
    </select>
    <select id="getBusinessTopDeptBankAccountCount" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM c_customer_service
        JOIN c_customer_service_bank_account ON c_customer_service.id = c_customer_service_bank_account.customer_service_id
        WHERE c_customer_service.is_del = 0 AND c_customer_service.business_top_dept_id = #{businessTopDeptId}
        AND c_customer_service_bank_account.bank_account_number = #{account}
        and c_customer_service.credit_code != #{creditCode}
        <if test="id != null">
            and c_customer_service_bank_account.id != #{id}
        </if>
    </select>
    <select id="getCustomerBankAccountNumberByBankNumbers"
            resultType="com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO">
        SELECT
        c_customer_service_bank_account.bank_account_number as bankAccountNumber,
        c_customer_service.customer_name as customerName
        FROM c_customer_service
        JOIN c_customer_service_bank_account ON c_customer_service.id = c_customer_service_bank_account.customer_service_id
        WHERE c_customer_service.is_del = 0 AND c_customer_service.business_top_dept_id = #{vo.businessTopDeptId}
        AND c_customer_service_bank_account.bank_account_number IN
        <foreach collection="vo.bankAccountNumbers" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId"
            resultType="com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO">
        SELECT
        c_customer_service_bank_account.id as id,
        c_customer_service.id as customerServiceId,
        c_customer_service_bank_account.bank_account_number as bankAccountNumber,
        c_customer_service.customer_name as customerName,
        c_customer_service.credit_code as creditCode
        FROM c_customer_service
        JOIN c_customer_service_bank_account ON c_customer_service.id = c_customer_service_bank_account.customer_service_id
        WHERE c_customer_service.is_del = 0 AND c_customer_service.business_top_dept_id = #{businessTopDeptId}
        AND c_customer_service_bank_account.bank_account_number = #{bankAccountNumber}
        limit 1
    </select>

    <insert id="insertCustomerServiceBankAccount" parameterType="com.bxm.customer.domain.CustomerServiceBankAccount" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_bank_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankAccountNumber != null and bankAccountNumber != ''">bank_account_number,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="accountOpenDate != null">account_open_date,</if>
            <if test="accountCloseDate != null">account_close_date,</if>
            <if test="receiptStatus != null">receipt_status,</if>
            <if test="bankDirect != null">bank_direct,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankAccountNumber != null and bankAccountNumber != ''">#{bankAccountNumber},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="accountOpenDate != null">#{accountOpenDate},</if>
            <if test="accountCloseDate != null">#{accountCloseDate},</if>
            <if test="receiptStatus != null">#{receiptStatus},</if>
            <if test="bankDirect != null">#{bankDirect},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceBankAccount" parameterType="com.bxm.customer.domain.CustomerServiceBankAccount">
        update c_customer_service_bank_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankAccountNumber != null and bankAccountNumber != ''">bank_account_number = #{bankAccountNumber},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="accountOpenDate != null">account_open_date = #{accountOpenDate},</if>
            <if test="accountCloseDate != null">account_close_date = #{accountCloseDate},</if>
            <if test="receiptStatus != null">receipt_status = #{receiptStatus},</if>
            <if test="bankDirect != null">bank_direct = #{bankDirect},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceBankAccountById" parameterType="Long">
        delete from c_customer_service_bank_account where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceBankAccountByIds" parameterType="String">
        delete from c_customer_service_bank_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>