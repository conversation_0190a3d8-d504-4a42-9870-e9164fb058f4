<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerDeliverMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerDeliver" id="CustomerDeliverResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="title"    column="title"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="employeeName"    column="employee_name"    />
        <result property="deliverType"    column="deliver_type"    />
        <result property="period"    column="period"    />
        <result property="status"    column="status"    />
        <result property="hasPersonChange"    column="has_person_change"    />
        <result property="personChangeInfo"    column="person_change_info"    />
        <result property="normalTicketAmount"    column="normal_ticket_amount"    />
        <result property="normalTicketTaxAmount"    column="normal_ticket_tax_amount"    />
        <result property="specialTicketAmount"    column="special_ticket_amount"    />
        <result property="specialTicketTaxAmount"    column="special_ticket_tax_amount"    />
        <result property="noTicketAmount"    column="no_ticket_amount"    />
        <result property="noTicketTaxAmount"    column="no_ticket_tax_amount"    />
        <result property="simpleAmount"    column="simple_amount"    />
        <result property="simpleTaxAmount"    column="simple_tax_amount"    />
        <result property="incomeTaxAmount"    column="income_tax_amount"    />
        <result property="lastMonthPurposeTaxAmount"    column="last_month_purpose_tax_amount"    />
        <result property="outputAmount"    column="output_amount"    />
        <result property="outputTaxAmount"    column="output_tax_amount"    />
        <result property="purposeTaxAmount"    column="purpose_tax_amount"    />
        <result property="thisMonthTaxAmount"    column="this_month_tax_amount"    />
        <result property="thisMonthPurposeTaxAmount"    column="this_month_purpose_tax_amount"    />
        <result property="thisMonthTaxBurden"    column="this_month_tax_burden"    />
        <result property="lastYearHouseAmount"    column="last_year_house_amount"    />
        <result property="valueAddTaxAmount"    column="value_add_tax_amount"    />
        <result property="additionalTaxAmount"    column="additional_tax_amount"    />
        <result property="stampDutyTaxAmount"    column="stamp_duty_tax_amount"    />
        <result property="otherTaxAmount"    column="other_tax_amount"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="reportAmount"    column="report_amount"    />
        <result property="reportRemark"    column="report_remark"    />
        <result property="deductionStatus"    column="deduction_status"    />
        <result property="deductionRemark"    column="deduction_remark"    />
        <result property="exceptionStatus"    column="exception_status"    />
        <result property="exceptionRemark"    column="exception_remark"    />
        <result property="createRemark"    column="create_remark"    />
        <result property="authStatus"    column="auth_status"    />
        <result property="authRemark"    column="auth_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerDeliverVo">
        select id, customer_service_id, title, batch_no, employee_id, employee_name, deliver_type, period, status, has_person_change, person_change_info, normal_ticket_amount, normal_ticket_tax_amount, special_ticket_amount, special_ticket_tax_amount, no_ticket_amount, no_ticket_tax_amount, simple_amount, simple_tax_amount, income_tax_amount, last_month_purpose_tax_amount, output_amount, output_tax_amount, purpose_tax_amount, this_month_tax_amount, this_month_purpose_tax_amount, this_month_tax_burden, last_year_house_amount, value_add_tax_amount, additional_tax_amount, stamp_duty_tax_amount, other_tax_amount, report_status, report_amount, report_remark, deduction_status, deduction_remark, exception_status, exception_remark, create_remark, auth_status, auth_remark, create_by, create_time, update_by, update_time from c_customer_deliver
    </sql>

    <select id="selectCustomerDeliverList" parameterType="com.bxm.customer.domain.CustomerDeliver" resultMap="CustomerDeliverResult">
        <include refid="selectCustomerDeliverVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="employeeName != null  and employeeName != ''"> and employee_name like concat('%', #{employeeName}, '%')</if>
            <if test="deliverType != null "> and deliver_type = #{deliverType}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="hasPersonChange != null "> and has_person_change = #{hasPersonChange}</if>
            <if test="personChangeInfo != null  and personChangeInfo != ''"> and person_change_info = #{personChangeInfo}</if>
            <if test="normalTicketAmount != null "> and normal_ticket_amount = #{normalTicketAmount}</if>
            <if test="normalTicketTaxAmount != null "> and normal_ticket_tax_amount = #{normalTicketTaxAmount}</if>
            <if test="specialTicketAmount != null "> and special_ticket_amount = #{specialTicketAmount}</if>
            <if test="specialTicketTaxAmount != null "> and special_ticket_tax_amount = #{specialTicketTaxAmount}</if>
            <if test="noTicketAmount != null "> and no_ticket_amount = #{noTicketAmount}</if>
            <if test="noTicketTaxAmount != null "> and no_ticket_tax_amount = #{noTicketTaxAmount}</if>
            <if test="simpleAmount != null "> and simple_amount = #{simpleAmount}</if>
            <if test="simpleTaxAmount != null "> and simple_tax_amount = #{simpleTaxAmount}</if>
            <if test="incomeTaxAmount != null "> and income_tax_amount = #{incomeTaxAmount}</if>
            <if test="lastMonthPurposeTaxAmount != null "> and last_month_purpose_tax_amount = #{lastMonthPurposeTaxAmount}</if>
            <if test="outputAmount != null "> and output_amount = #{outputAmount}</if>
            <if test="outputTaxAmount != null "> and output_tax_amount = #{outputTaxAmount}</if>
            <if test="purposeTaxAmount != null "> and purpose_tax_amount = #{purposeTaxAmount}</if>
            <if test="thisMonthTaxAmount != null "> and this_month_tax_amount = #{thisMonthTaxAmount}</if>
            <if test="thisMonthPurposeTaxAmount != null "> and this_month_purpose_tax_amount = #{thisMonthPurposeTaxAmount}</if>
            <if test="thisMonthTaxBurden != null "> and this_month_tax_burden = #{thisMonthTaxBurden}</if>
            <if test="lastYearHouseAmount != null "> and last_year_house_amount = #{lastYearHouseAmount}</if>
            <if test="valueAddTaxAmount != null "> and value_add_tax_amount = #{valueAddTaxAmount}</if>
            <if test="additionalTaxAmount != null "> and additional_tax_amount = #{additionalTaxAmount}</if>
            <if test="stampDutyTaxAmount != null "> and stamp_duty_tax_amount = #{stampDutyTaxAmount}</if>
            <if test="otherTaxAmount != null "> and other_tax_amount = #{otherTaxAmount}</if>
            <if test="reportStatus != null "> and report_status = #{reportStatus}</if>
            <if test="reportAmount != null "> and report_amount = #{reportAmount}</if>
            <if test="reportRemark != null  and reportRemark != ''"> and report_remark = #{reportRemark}</if>
            <if test="deductionStatus != null "> and deduction_status = #{deductionStatus}</if>
            <if test="deductionRemark != null  and deductionRemark != ''"> and deduction_remark = #{deductionRemark}</if>
            <if test="exceptionStatus != null "> and exception_status = #{exceptionStatus}</if>
            <if test="exceptionRemark != null  and exceptionRemark != ''"> and exception_remark = #{exceptionRemark}</if>
            <if test="createRemark != null  and createRemark != ''"> and create_remark = #{createRemark}</if>
            <if test="authStatus != null "> and auth_status = #{authStatus}</if>
            <if test="authRemark != null  and authRemark != ''"> and auth_remark = #{authRemark}</if>
        </where>
    </select>
    
    <select id="selectCustomerDeliverById" parameterType="Long" resultMap="CustomerDeliverResult">
        <include refid="selectCustomerDeliverVo"/>
        where id = #{id}
    </select>
    <select id="selectDeliverList" resultType="com.bxm.customer.domain.dto.CustomerDeliverDTO">
        select
            ccd.id as id,
            ccs.customer_name as customerName,
            ccs.customer_company_name as customerCompanyName,
            ccs.credit_code as creditCode,
            ccd.customer_service_id as customerServiceId,
            ccd.customer_service_period_month_id as customerServicePeriodMonthId,
            ccd.employee_name as employeeName,
            ccd.period as period,
            ccd.create_time as createTime,
            ccd.title as title,
            ccd.deliver_type as deliverType,
            if(ccd.has_person_change = 1, '有', '无') as hasPersonChange,
            ccd.person_change_info as personChangeInfo,
            ccd.create_remark as createRemark,
            if(ccd.is_tutor = 1, '是', '否') as isTutor,
            ifnull(ccd.report_amount, '-') as reportAmount,
            ccd.`status` as `status`,
            ccspm.business_dept_id as businessDeptId,
--             ifnull(ccd.normal_ticket_amount, '-') as normalTicketAmount,
--             ifnull(ccd.normal_ticket_tax_amount, '-') as normalTicketTaxAmount,
--             ifnull(ccd.special_ticket_amount, '-') as specialTicketAmount,
--             ifnull(ccd.special_ticket_tax_amount, '-') as specialTicketTaxAmount,
--             ifnull(ccd.no_ticket_amount, '-') as noTicketAmount,
--             ifnull(ccd.no_ticket_tax_amount, '-') as noTicketTaxAmount,
--             ifnull(ccd.simple_amount, '-') as simpleAmount,
--             ifnull(ccd.simple_tax_amount, '-') as simpleTaxAmount,
--             ifnull(ccd.income_tax_amount, '-') as incomeTaxAmount,
--             ifnull(ccd.last_month_purpose_tax_amount, '-') as lastMonthPurposeTaxAmount,
--             ifnull(ccd.output_amount, '-') as outputAmount,
--             ifnull(ccd.output_tax_amount, '-') as outputTaxAmount,
--             ifnull(ccd.purpose_tax_amount, '-') as purposeTaxAmount,
--             ifnull(ccd.this_month_tax_amount, '-') as thisMonthTaxAmount,
--             ifnull(ccd.this_month_purpose_tax_amount, '-') as thisMonthPurposeTaxAmount,
--             ifnull(ccd.this_month_tax_burden, '-') as thisMonthTaxBurden,
--             ifnull(ccd.this_month_tax_burden_tax_amount, '-') as thisMonthTaxBurdenTaxAmount,
--             ifnull(ccd.this_month_tax_burden_tax_amount_sub, '-') as thisMonthTaxBurdenTaxAmountSub,
            ifnull(ccd.value_add_tax_amount, '-') as valueAddTaxAmount,
            ifnull(ccd.additional_tax_amount, '-') as additionalTaxAmount,
            ifnull(ccd.stamp_duty_tax_amount, '-') as stampDutyTaxAmount,
            ifnull(ccd.other_tax_amount, '-') as otherTaxAmount,
            ifnull(ccd.total_tax_amount, '-') as totalTaxAmount,
            ifnull(ccd.current_period_amount, '-') as currentPeriodAmount,
            ifnull(ccd.overdue_amount, '-') as overdueAmount,
            ifnull(ccd.supplement_amount, '-') as supplementAmount,
            ccd.last_year_house_amount as lastYearHouseAmount,
            ccd.pre_auth_info as preAuthInfo,
            ccspm.advisor_dept_id as advisorDeptId,
--             ccs.advisor_dept_id as advisorDeptId,
            sd1.dept_name as advisorDeptName,
            ccspm.accounting_dept_id as accountingDeptId,
--             ccs.accounting_dept_id as accountingDeptId,
            sd2.dept_name as accountingDeptName,
            ccd.report_remark as reportRemark,
            ccd.deduction_remark as deductionRemark,
            ccd.has_changed as hasChanged,
            ccd.last_oper_type as lastOperType,
            ccd.last_oper_name as lastOperName,
            ccd.last_oper_time as lastOperTime,
            ccd.last_oper_remark as lastOperRemark,
            cmo.matters_notes as mattersNotesDetail,
            ccd.liaison_phone as liaisonPhone,
            ccd.liaison_certificate_number as liaisonCertificateNumber,
            ccd.is_sync_liaison as isSyncLiaison,
            ccd.pre_auth_no_ticket_income as preAuthNoTicketIncome,
            ccd.pre_auth_input_tax as preAuthInputTax,
            ccd.create_by as createBy,
            ccd.tax_check_type as taxCheckType,
            ccd.ddl as ddl
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        left join sys_dept sd1 on ccspm.advisor_dept_id = sd1.dept_id and sd1.del_flag = '0'
        left join sys_dept sd2 on ccspm.accounting_dept_id = sd2.dept_id and sd2.del_flag = '0'
        left join c_customer_matters_notes cmo on ccd.customer_service_id = cmo.customer_service_id
        and ((ccd.deliver_type &lt;= 2 and cmo.item_type = 2) or (ccd.deliver_type > 2 and ccd.deliver_type &lt;= 8 and ccd.deliver_type = cmo.item_type) or (ccd.deliver_type = 9 and cmo.item_type = 15) or (ccd.deliver_type = 10 and cmo.item_type = 16))
        <where>
            ccd.is_del = 0
            <if test="vo.ddlStart != null and vo.ddlStart != ''">
                and ccd.ddl &gt;= #{vo.ddlStart}
            </if>
            <if test="vo.ddlEnd != null and vo.ddlEnd != ''">
                and ccd.ddl &lt;= #{vo.ddlEnd}
            </if>
            <if test="vo.taxCheckType != null and vo.taxCheckType != ''">
                and ccd.tax_check_type like concat('%', #{vo.taxCheckType}, '%')
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
                <if test="vo.customerServiceTagIncludeFlag != null and vo.customerServiceTagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id not in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 1 and periodIds != null and periodIds.size > 0">
                    and ccd.customer_service_period_month_id in
                    <foreach collection="periodIds" item="periodId" open="(" separator="," close=")">
                        #{periodId}
                    </foreach>
                </if>
                <if test="vo.periodTagIncludeFlag != null and vo.periodTagIncludeFlag == 0 and periodIds != null and periodIds.size > 0">
                    and ccd.customer_service_period_month_id not in
                    <foreach collection="periodIds" item="periodId" open="(" separator="," close=")">
                        #{periodId}
                    </foreach>
                </if>
            </if>
            <if test="vo.customerServiceTaxType != null">
                and ccs.tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.periodTaxType != null">
                and ccspm.tax_type = #{vo.periodTaxType}
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccd.customer_service_id in
                <foreach collection="ids" item="customerServiceId" open="(" separator="," close=")">
                    #{customerServiceId}
                </foreach>
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.statusList != null and vo.statusList != ''">
                and ccd.status in (${vo.statusList})
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and (ccs.customer_name like concat('%',#{vo.customerName},'%')
                or ccs.customer_company_name like concat('%',#{vo.customerName},'%')
                or ccs.credit_code like concat('%',#{vo.customerName},'%')
                or ccs.tax_number like concat('%',#{vo.customerName},'%'))
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.employeeName != null and vo.employeeName != ''">
                and ccd.employee_name like concat('%',#{vo.employeeName},'%')
            </if>
            <if test="vo.submitDateStart != null and vo.submitDateStart != '' and vo.submitDateEnd != null and vo.submitDateEnd != ''">
                and ccd.create_time &gt;= #{vo.submitDateStart} and ccd.create_time &lt;= #{vo.submitDateEnd}
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.deliverType != null">
                and ccd.deliver_type = #{vo.deliverType}
            </if>
            <if test="vo.hasPersonChange != null">
                and ccd.has_person_change = #{vo.hasPersonChange} and ccd.deliver_type in (1,2,3)
            </if>
            <if test="vo.status != null">
                and ccd.`status` = #{vo.status}
            </if>
            <if test="vo.reportAmountMin != null">
                and ((ccd.deliver_type != 4 and ccd.report_amount &gt;= #{vo.reportAmountMin}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &gt;= #{vo.reportAmountMin}))
            </if>
            <if test="vo.reportAmountMax != null">
                and ((ccd.deliver_type != 4 and ccd.report_amount &lt;= #{vo.reportAmountMax}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &lt;= #{vo.reportAmountMax}))
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.customerServiceAdvisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.customerServiceAccountingDeptId}
            </if>
            <if test="vo.periodAdvisorDeptId != null">
                and ccspm.advisor_dept_id = #{vo.periodAdvisorDeptId}
            </if>
            <if test="vo.periodAccountingDeptId != null">
                and ccspm.accounting_dept_id = #{vo.periodAccountingDeptId}
            </if>
            <if test="vo.lastOperName != null and vo.lastOperName != ''">
                and ccd.last_oper_name like concat('%',#{vo.lastOperName},'%')
            </if>
            <if test="vo.lastOperTimeStart != null and vo.lastOperTimeStart != ''">
                and ccd.last_oper_time &gt;= #{vo.lastOperTimeStart}
            </if>
            <if test="vo.lastOperTimeEnd != null and vo.lastOperTimeEnd != ''">
                and ccd.last_oper_time &lt;= #{vo.lastOperTimeEnd}
            </if>
            <if test="vo.year != null">
                and ccd.period = #{vo.year}
            </if>
            <if test="vo.hasDeliverRequire != null">
                <if test="vo.hasDeliverRequire == 0">
                    and (ccd.create_remark is null or ccd.create_remark = '')
                </if>
                <if test="vo.hasDeliverRequire == 1">
                    and ccd.create_remark is not null and ccd.create_remark != ''
                </if>
            </if>
            <if test="vo.preAuthNoTicketIncome != null">
                <if test="vo.preAuthNoTicketIncome == 0">
                    and ccd.pre_auth_no_ticket_income = 0
                </if>
                <if test="vo.preAuthNoTicketIncome == 1">
                    and ccd.pre_auth_no_ticket_income &gt; 0
                </if>
            </if>
            <if test="vo.preAuthInputTax != null">
                and ccd.pre_auth_input_tax = #{vo.preAuthInputTax}
            </if>
        </where>
        order by ccd.period desc, ccd.id desc
    </select>

    <insert id="insertCustomerDeliver" parameterType="com.bxm.customer.domain.CustomerDeliver" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_deliver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="employeeName != null">employee_name,</if>
            <if test="deliverType != null">deliver_type,</if>
            <if test="period != null">period,</if>
            <if test="status != null">status,</if>
            <if test="hasPersonChange != null">has_person_change,</if>
            <if test="personChangeInfo != null">person_change_info,</if>
            <if test="normalTicketAmount != null">normal_ticket_amount,</if>
            <if test="normalTicketTaxAmount != null">normal_ticket_tax_amount,</if>
            <if test="specialTicketAmount != null">special_ticket_amount,</if>
            <if test="specialTicketTaxAmount != null">special_ticket_tax_amount,</if>
            <if test="noTicketAmount != null">no_ticket_amount,</if>
            <if test="noTicketTaxAmount != null">no_ticket_tax_amount,</if>
            <if test="simpleAmount != null">simple_amount,</if>
            <if test="simpleTaxAmount != null">simple_tax_amount,</if>
            <if test="incomeTaxAmount != null">income_tax_amount,</if>
            <if test="lastMonthPurposeTaxAmount != null">last_month_purpose_tax_amount,</if>
            <if test="outputAmount != null">output_amount,</if>
            <if test="outputTaxAmount != null">output_tax_amount,</if>
            <if test="purposeTaxAmount != null">purpose_tax_amount,</if>
            <if test="thisMonthTaxAmount != null">this_month_tax_amount,</if>
            <if test="thisMonthPurposeTaxAmount != null">this_month_purpose_tax_amount,</if>
            <if test="thisMonthTaxBurden != null">this_month_tax_burden,</if>
            <if test="lastYearHouseAmount != null">last_year_house_amount,</if>
            <if test="valueAddTaxAmount != null">value_add_tax_amount,</if>
            <if test="additionalTaxAmount != null">additional_tax_amount,</if>
            <if test="stampDutyTaxAmount != null">stamp_duty_tax_amount,</if>
            <if test="otherTaxAmount != null">other_tax_amount,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="reportAmount != null">report_amount,</if>
            <if test="reportRemark != null">report_remark,</if>
            <if test="deductionStatus != null">deduction_status,</if>
            <if test="deductionRemark != null">deduction_remark,</if>
            <if test="exceptionStatus != null">exception_status,</if>
            <if test="exceptionRemark != null">exception_remark,</if>
            <if test="createRemark != null">create_remark,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="authRemark != null">auth_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="employeeName != null">#{employeeName},</if>
            <if test="deliverType != null">#{deliverType},</if>
            <if test="period != null">#{period},</if>
            <if test="status != null">#{status},</if>
            <if test="hasPersonChange != null">#{hasPersonChange},</if>
            <if test="personChangeInfo != null">#{personChangeInfo},</if>
            <if test="normalTicketAmount != null">#{normalTicketAmount},</if>
            <if test="normalTicketTaxAmount != null">#{normalTicketTaxAmount},</if>
            <if test="specialTicketAmount != null">#{specialTicketAmount},</if>
            <if test="specialTicketTaxAmount != null">#{specialTicketTaxAmount},</if>
            <if test="noTicketAmount != null">#{noTicketAmount},</if>
            <if test="noTicketTaxAmount != null">#{noTicketTaxAmount},</if>
            <if test="simpleAmount != null">#{simpleAmount},</if>
            <if test="simpleTaxAmount != null">#{simpleTaxAmount},</if>
            <if test="incomeTaxAmount != null">#{incomeTaxAmount},</if>
            <if test="lastMonthPurposeTaxAmount != null">#{lastMonthPurposeTaxAmount},</if>
            <if test="outputAmount != null">#{outputAmount},</if>
            <if test="outputTaxAmount != null">#{outputTaxAmount},</if>
            <if test="purposeTaxAmount != null">#{purposeTaxAmount},</if>
            <if test="thisMonthTaxAmount != null">#{thisMonthTaxAmount},</if>
            <if test="thisMonthPurposeTaxAmount != null">#{thisMonthPurposeTaxAmount},</if>
            <if test="thisMonthTaxBurden != null">#{thisMonthTaxBurden},</if>
            <if test="lastYearHouseAmount != null">#{lastYearHouseAmount},</if>
            <if test="valueAddTaxAmount != null">#{valueAddTaxAmount},</if>
            <if test="additionalTaxAmount != null">#{additionalTaxAmount},</if>
            <if test="stampDutyTaxAmount != null">#{stampDutyTaxAmount},</if>
            <if test="otherTaxAmount != null">#{otherTaxAmount},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="reportAmount != null">#{reportAmount},</if>
            <if test="reportRemark != null">#{reportRemark},</if>
            <if test="deductionStatus != null">#{deductionStatus},</if>
            <if test="deductionRemark != null">#{deductionRemark},</if>
            <if test="exceptionStatus != null">#{exceptionStatus},</if>
            <if test="exceptionRemark != null">#{exceptionRemark},</if>
            <if test="createRemark != null">#{createRemark},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="authRemark != null">#{authRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerDeliver" parameterType="com.bxm.customer.domain.CustomerDeliver">
        update c_customer_deliver
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="deliverType != null">deliver_type = #{deliverType},</if>
            <if test="period != null">period = #{period},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hasPersonChange != null">has_person_change = #{hasPersonChange},</if>
            <if test="personChangeInfo != null">person_change_info = #{personChangeInfo},</if>
            <if test="normalTicketAmount != null">normal_ticket_amount = #{normalTicketAmount},</if>
            <if test="normalTicketTaxAmount != null">normal_ticket_tax_amount = #{normalTicketTaxAmount},</if>
            <if test="specialTicketAmount != null">special_ticket_amount = #{specialTicketAmount},</if>
            <if test="specialTicketTaxAmount != null">special_ticket_tax_amount = #{specialTicketTaxAmount},</if>
            <if test="noTicketAmount != null">no_ticket_amount = #{noTicketAmount},</if>
            <if test="noTicketTaxAmount != null">no_ticket_tax_amount = #{noTicketTaxAmount},</if>
            <if test="simpleAmount != null">simple_amount = #{simpleAmount},</if>
            <if test="simpleTaxAmount != null">simple_tax_amount = #{simpleTaxAmount},</if>
            <if test="incomeTaxAmount != null">income_tax_amount = #{incomeTaxAmount},</if>
            <if test="lastMonthPurposeTaxAmount != null">last_month_purpose_tax_amount = #{lastMonthPurposeTaxAmount},</if>
            <if test="outputAmount != null">output_amount = #{outputAmount},</if>
            <if test="outputTaxAmount != null">output_tax_amount = #{outputTaxAmount},</if>
            <if test="purposeTaxAmount != null">purpose_tax_amount = #{purposeTaxAmount},</if>
            <if test="thisMonthTaxAmount != null">this_month_tax_amount = #{thisMonthTaxAmount},</if>
            <if test="thisMonthPurposeTaxAmount != null">this_month_purpose_tax_amount = #{thisMonthPurposeTaxAmount},</if>
            <if test="thisMonthTaxBurden != null">this_month_tax_burden = #{thisMonthTaxBurden},</if>
            <if test="lastYearHouseAmount != null">last_year_house_amount = #{lastYearHouseAmount},</if>
            <if test="valueAddTaxAmount != null">value_add_tax_amount = #{valueAddTaxAmount},</if>
            <if test="additionalTaxAmount != null">additional_tax_amount = #{additionalTaxAmount},</if>
            <if test="stampDutyTaxAmount != null">stamp_duty_tax_amount = #{stampDutyTaxAmount},</if>
            <if test="otherTaxAmount != null">other_tax_amount = #{otherTaxAmount},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="reportAmount != null">report_amount = #{reportAmount},</if>
            <if test="reportRemark != null">report_remark = #{reportRemark},</if>
            <if test="deductionStatus != null">deduction_status = #{deductionStatus},</if>
            <if test="deductionRemark != null">deduction_remark = #{deductionRemark},</if>
            <if test="exceptionStatus != null">exception_status = #{exceptionStatus},</if>
            <if test="exceptionRemark != null">exception_remark = #{exceptionRemark},</if>
            <if test="createRemark != null">create_remark = #{createRemark},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="authRemark != null">auth_remark = #{authRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerDeliverById" parameterType="Long">
        delete from c_customer_deliver where id = #{id}
    </delete>

    <delete id="deleteCustomerDeliverByIds" parameterType="String">
        delete from c_customer_deliver where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCustomerDeliverForCustomerServiceDetailByCustomerServiceId" resultType="com.bxm.customer.domain.dto.CustomerDeliverForCustomerServiceDetailSourceDTO">
        select id, deliver_type, period, status, value_add_tax_amount, additional_tax_amount, stamp_duty_tax_amount, other_tax_amount, report_amount, create_time, total_tax_amount
        from c_customer_deliver
        where is_del = 0 and customer_service_id = #{customerServiceId}
    </select>
    <select id="selectByDeliverTypeAndPeriodAndMonthConditionSql"
            resultType="com.bxm.customer.domain.CustomerDeliver">
        select id, customer_service_id, deliver_type, period, status, value_add_tax_amount, additional_tax_amount, stamp_duty_tax_amount, other_tax_amount, report_amount, create_time, has_changed
        from c_customer_deliver
        <where>
            is_del = 0
            <if test="deliverType != null">
                and deliver_type = #{deliverType}
            </if>
            <if test="period != null">
                and `period` = #{period}
            </if>
            <if test="monthConditionSql != null and monthConditionSql != ''">
                and customer_service_period_month_id in (${monthConditionSql})
            </if>
        </where>
    </select>
    <select id="selectCompleteByDeliverTypeAndPeriodAndMonthConditionSql"
            resultType="com.bxm.customer.domain.CustomerDeliver">
        select id, customer_service_id, deliver_type, period, status, value_add_tax_amount, additional_tax_amount, stamp_duty_tax_amount, other_tax_amount, report_amount, create_time
        from c_customer_deliver
        <where>
            is_del = 0 and `status` in (5, 104)
            <if test="deliverType != null">
                and deliver_type = #{deliverType}
            </if>
            <if test="period != null">
                and `period` = #{period}
            </if>
            <if test="monthConditionSql != null and monthConditionSql != ''">
                and customer_service_period_month_id in (${monthConditionSql})
            </if>
        </where>
    </select>
    <select id="selectDeliverAccountDeptCountList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.accounting_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccs.accounting_dept_id is not null
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id not in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.statusList != null and vo.statusList != ''">
                and ccd.status in (${vo.statusList})
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and (ccs.customer_name like concat('%',#{vo.customerName},'%')
                or ccs.customer_company_name like concat('%',#{vo.customerName},'%')
                or ccs.credit_code like concat('%',#{vo.customerName},'%')
                or ccs.tax_number like concat('%',#{vo.customerName},'%'))
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.employeeName != null and vo.employeeName != ''">
                and ccd.employee_name like concat('%',#{vo.employeeName},'%')
            </if>
            <if test="vo.submitDateStart != null and vo.submitDateStart != '' and vo.submitDateEnd != null and vo.submitDateEnd != ''">
                and ccd.create_time &gt;= #{vo.submitDateStart} and ccd.create_time &lt;= #{vo.submitDateEnd}
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.deliverType != null">
                and ccd.deliver_type = #{vo.deliverType}
            </if>
            <if test="vo.hasPersonChange != null">
                and ccd.has_person_change = #{vo.hasPersonChange}
            </if>
            <if test="vo.status != null">
                and ccd.`status` = #{vo.status}
            </if>
            <if test="vo.reportAmountMin != null">
                and ((ccd.deliver_type in (1,2,3) and ccd.report_amount &gt;= #{vo.reportAmountMin}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &gt;= #{vo.reportAmountMin}))
            </if>
            <if test="vo.reportAmountMax != null">
                and ((ccd.deliver_type in (1,2,3) and ccd.report_amount &lt;= #{vo.reportAmountMax}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &lt;= #{vo.reportAmountMax}))
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.lastOperName != null and vo.lastOperName != ''">
                and ccd.last_oper_name like concat('%',#{vo.lastOperName},'%')
            </if>
            <if test="vo.lastOperTimeStart != null and vo.lastOperTimeStart != ''">
                and ccd.last_oper_time &gt;= #{vo.lastOperTimeStart}
            </if>
            <if test="vo.lastOperTimeEnd != null and vo.lastOperTimeEnd != ''">
                and ccd.last_oper_time &lt;= #{vo.lastOperTimeEnd}
            </if>
        </where>
        group by ccs.accounting_dept_id
    </select>
    <select id="selectDeliverAdvisorDeptCountList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.advisor_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccs.advisor_dept_id is not null
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 1 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 0 and customerServiceIds != null and customerServiceIds.size > 0">
                    and ccd.customer_service_id not in
                    <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
            </if>
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.statusList != null and vo.statusList != ''">
                and ccd.status in (${vo.statusList})
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and (ccs.customer_name like concat('%',#{vo.customerName},'%')
                or ccs.customer_company_name like concat('%',#{vo.customerName},'%')
                or ccs.credit_code like concat('%',#{vo.customerName},'%')
                or ccs.tax_number like concat('%',#{vo.customerName},'%'))
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.employeeName != null and vo.employeeName != ''">
                and ccd.employee_name like concat('%',#{vo.employeeName},'%')
            </if>
            <if test="vo.submitDateStart != null and vo.submitDateStart != '' and vo.submitDateEnd != null and vo.submitDateEnd != ''">
                and ccd.create_time &gt;= #{vo.submitDateStart} and ccd.create_time &lt;= #{vo.submitDateEnd}
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.deliverType != null">
                and ccd.deliver_type = #{vo.deliverType}
            </if>
            <if test="vo.hasPersonChange != null">
                and ccd.has_person_change = #{vo.hasPersonChange}
            </if>
            <if test="vo.status != null">
                and ccd.`status` = #{vo.status}
            </if>
            <if test="vo.reportAmountMin != null">
                and ((ccd.deliver_type in (1,2,3) and ccd.report_amount &gt;= #{vo.reportAmountMin}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &gt;= #{vo.reportAmountMin}))
            </if>
            <if test="vo.reportAmountMax != null">
                and ((ccd.deliver_type in (1,2,3) and ccd.report_amount &lt;= #{vo.reportAmountMax}) or (ccd.deliver_type = 4 and ccd.total_tax_amount &lt;= #{vo.reportAmountMax}))
            </if>
            <if test="vo.advisorDeptId != null">
                and ccs.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccs.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.lastOperName != null and vo.lastOperName != ''">
                and ccd.last_oper_name like concat('%',#{vo.lastOperName},'%')
            </if>
            <if test="vo.lastOperTimeStart != null and vo.lastOperTimeStart != ''">
                and ccd.last_oper_time &gt;= #{vo.lastOperTimeStart}
            </if>
            <if test="vo.lastOperTimeEnd != null and vo.lastOperTimeEnd != ''">
                and ccd.last_oper_time &lt;= #{vo.lastOperTimeEnd}
            </if>
        </where>
        group by ccs.advisor_dept_id
    </select>
    <select id="selectByDeliverTypeAndPeriodAndMonthCondition"
            resultType="com.bxm.customer.domain.CustomerDeliver">
        select c_customer_deliver.id, c_customer_deliver.customer_service_id, c_customer_deliver.deliver_type, c_customer_deliver.period, c_customer_deliver.status,
               c_customer_deliver.value_add_tax_amount, c_customer_deliver.additional_tax_amount, c_customer_deliver.stamp_duty_tax_amount, c_customer_deliver.other_tax_amount, c_customer_deliver.report_amount,
               c_customer_deliver.create_time, c_customer_deliver.has_changed
        from c_customer_deliver
            join c_customer_service ccs on c_customer_deliver.customer_service_id = ccs.id and ccs.is_del = 0
                 JOIN c_customer_service_period_month ON c_customer_deliver.customer_service_period_month_id = c_customer_service_period_month.id
        where
            c_customer_deliver.is_del = 0
          and c_customer_deliver.deliver_type = #{deliverType}
          and c_customer_service_period_month.service_status != 3
          <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
              <if test="userDeptDTO.deptType == 1">
                  and (
                  c_customer_service_period_month.advisor_dept_id in
                  <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                      #{deptId}
                  </foreach>
                  or c_customer_service_period_month.advisor_top_dept_id in
                  <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                      #{deptId}
                  </foreach>
                  )
              </if>
              <if test="userDeptDTO.deptType == 2">
                  and (
                  c_customer_service_period_month.accounting_dept_id in
                  <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                      #{deptId}
                  </foreach>
                  or c_customer_service_period_month.accounting_top_dept_id in
                  <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                      #{deptId}
                  </foreach>
                  )
              </if>
          </if>
          <if test="queryDeptId != null">
            <if test="queryDeptIds == null or queryDeptIds.size == 0">
                and 1 = 0
            </if>
              <if test="queryDeptIds != null and queryDeptIds.size > 0">
                  <if test="queryDeptType == 1">
                      and (
                      c_customer_service_period_month.advisor_dept_id in
                      <foreach item="deptId" collection="queryDeptIds" open="(" separator="," close=")">
                          #{deptId}
                      </foreach>
                      or c_customer_service_period_month.advisor_top_dept_id in
                      <foreach item="deptId" collection="queryDeptIds" open="(" separator="," close=")">
                          #{deptId}
                      </foreach>
                      )
                  </if>
                  <if test="queryDeptType == 2">
                      and (
                      c_customer_service_period_month.accounting_dept_id in
                      <foreach item="deptId" collection="queryDeptIds" open="(" separator="," close=")">
                          #{deptId}
                      </foreach>
                      or c_customer_service_period_month.accounting_top_dept_id in
                      <foreach item="deptId" collection="queryDeptIds" open="(" separator="," close=")">
                          #{deptId}
                      </foreach>
                      )
                  </if>
              </if>
          </if>
    </select>
    <select id="selectDeliverCustomerServiceAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.advisor_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccs.advisor_dept_id is not null
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.year != null">
                and ccd.period = #{vo.year}
            </if>
        </where>
        group by ccs.advisor_dept_id
    </select>
    <select id="selectDeliverCustomerServiceAccountDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccs.accounting_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccs.accounting_dept_id is not null
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.year != null">
                and ccd.period = #{vo.year}
            </if>
        </where>
        group by ccs.accounting_dept_id
    </select>
    <select id="selectDeliverPeriodAdvisorDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccspm.advisor_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccspm.advisor_dept_id is not null
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.year != null">
                and ccd.period = #{vo.year}
            </if>
        </where>
        group by ccspm.advisor_dept_id
    </select>
    <select id="selectDeliverPeriodAccountDeptCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        ccspm.accounting_dept_id as deptId,
        count(ccd.id) as dataCount
        from c_customer_deliver ccd join c_customer_service ccs  on ccd.customer_service_id = ccs.id and ccs.is_del = 0
        JOIN c_customer_service_period_month ccspm ON ccd.customer_service_period_month_id = ccspm.id
        <where>
            ccd.is_del = 0 and ccspm.accounting_dept_id is not null
            <if test="isAdmin != null and isAdmin == 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="vo.tabType != null">
                <if test="vo.tabType == 2">
                    and ccd.deliver_type in (1, 2)
                </if>
                <if test="vo.tabType != 2">
                    and ccd.deliver_type = #{vo.tabType}
                </if>
            </if>
            <if test="vo.periodStart != null and vo.periodEnd != null">
                and ccd.period &gt;= #{vo.periodStart} and ccd.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.year != null">
                and ccd.period = #{vo.year}
            </if>
        </where>
        group by ccspm.accounting_dept_id
    </select>
</mapper>