<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BillMapper">
    
    <resultMap type="com.bxm.customer.domain.Bill" id="BillResult">
        <result property="id"    column="id"    />
        <result property="billNo"    column="bill_no"    />
        <result property="billTitle"    column="bill_title"    />
        <result property="businessTopDeptId"    column="business_top_dept_id"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="status"    column="status"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="discountPrice"    column="discount_price"    />
        <result property="deductionPrice"    column="deduction_price"    />
        <result property="oughtPrice"    column="ought_price"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillVo">
        select id, bill_no, bill_title, business_top_dept_id, business_dept_id, status, total_price, discount_price, deduction_price, ought_price, is_del, create_by, create_time, update_by, update_time from c_bill
    </sql>

    <select id="selectBillList" parameterType="com.bxm.customer.domain.Bill" resultMap="BillResult">
        <include refid="selectBillVo"/>
        <where>  
            <if test="billNo != null  and billNo != ''"> and bill_no = #{billNo}</if>
            <if test="billTitle != null  and billTitle != ''"> and bill_title = #{billTitle}</if>
            <if test="businessTopDeptId != null "> and business_top_dept_id = #{businessTopDeptId}</if>
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="totalPrice != null "> and total_price = #{totalPrice}</if>
            <if test="discountPrice != null "> and discount_price = #{discountPrice}</if>
            <if test="deductionPrice != null "> and deduction_price = #{deductionPrice}</if>
            <if test="oughtPrice != null "> and ought_price = #{oughtPrice}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectBillById" parameterType="Long" resultMap="BillResult">
        <include refid="selectBillVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBill" parameterType="com.bxm.customer.domain.Bill" useGeneratedKeys="true" keyProperty="id">
        insert into c_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billNo != null and billNo != ''">bill_no,</if>
            <if test="billTitle != null and billTitle != ''">bill_title,</if>
            <if test="businessTopDeptId != null">business_top_dept_id,</if>
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="status != null">status,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="discountPrice != null">discount_price,</if>
            <if test="deductionPrice != null">deduction_price,</if>
            <if test="oughtPrice != null">ought_price,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billNo != null and billNo != ''">#{billNo},</if>
            <if test="billTitle != null and billTitle != ''">#{billTitle},</if>
            <if test="businessTopDeptId != null">#{businessTopDeptId},</if>
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="status != null">#{status},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="discountPrice != null">#{discountPrice},</if>
            <if test="deductionPrice != null">#{deductionPrice},</if>
            <if test="oughtPrice != null">#{oughtPrice},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBill" parameterType="com.bxm.customer.domain.Bill">
        update c_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="billNo != null and billNo != ''">bill_no = #{billNo},</if>
            <if test="billTitle != null and billTitle != ''">bill_title = #{billTitle},</if>
            <if test="businessTopDeptId != null">business_top_dept_id = #{businessTopDeptId},</if>
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="discountPrice != null">discount_price = #{discountPrice},</if>
            <if test="deductionPrice != null">deduction_price = #{deductionPrice},</if>
            <if test="oughtPrice != null">ought_price = #{oughtPrice},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillById" parameterType="Long">
        delete from c_bill where id = #{id}
    </delete>

    <delete id="deleteBillByIds" parameterType="String">
        delete from c_bill where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>