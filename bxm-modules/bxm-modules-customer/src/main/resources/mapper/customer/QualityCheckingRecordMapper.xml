<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.QualityCheckingRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.QualityCheckingRecord" id="QualityCheckingRecordResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="period"    column="period"    />
        <result property="qualityCheckingItemId"    column="quality_checking_item_id"    />
        <result property="status"    column="status"    />
        <result property="checkingResult"    column="checking_result"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="closeTime"    column="close_time"    />
        <result property="remark"    column="remark"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQualityCheckingRecordVo">
        select id, batch_no, customer_service_id, customer_service_period_month_id, period, quality_checking_item_id, status, checking_result, finish_time, close_time, remark, create_id, create_by, create_time, update_by, update_time from c_quality_checking_record
    </sql>

    <select id="selectQualityCheckingRecordList" parameterType="com.bxm.customer.domain.QualityCheckingRecord" resultMap="QualityCheckingRecordResult">
        <include refid="selectQualityCheckingRecordVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="qualityCheckingItemId != null "> and quality_checking_item_id = #{qualityCheckingItemId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="checkingResult != null "> and checking_result = #{checkingResult}</if>
            <if test="finishTime != null "> and finish_time = #{finishTime}</if>
            <if test="closeTime != null "> and close_time = #{closeTime}</if>
            <if test="createId != null "> and create_id = #{createId}</if>
        </where>
    </select>
    
    <select id="selectQualityCheckingRecordById" parameterType="Long" resultMap="QualityCheckingRecordResult">
        <include refid="selectQualityCheckingRecordVo"/>
        where id = #{id}
    </select>
    <select id="qualityCheckingRecordAdvisorDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.advisor_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_record qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.is_del = 0 AND ccspm.advisor_dept_id is not null
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="vo.periodStart != null">
            and ccspm.period &gt;= #{vo.periodStart}
        </if>
        <if test="vo.periodEnd != null">
            and ccspm.period &lt;= #{vo.periodEnd}
        </if>
        GROUP BY ccspm.advisor_dept_id
    </select>
    <select id="qualityCheckingRecordAccountingDeptList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.accounting_dept_id AS deptId,
        count(qcr.id) as dataCount
        FROM c_quality_checking_record qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        WHERE qcr.is_del = 0 AND ccspm.accounting_dept_id is not null
        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
            <if test="userDept.deptType == 1">
                and (ccspm.advisor_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.advisor_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="userDept.deptType == 2">
                and (ccspm.accounting_top_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                or ccspm.accounting_dept_id in
                <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                    #{deptId}
                </foreach>
                )
            </if>
        </if>
        <if test="vo.periodStart != null">
            and ccspm.period &gt;= #{vo.periodStart}
        </if>
        <if test="vo.periodEnd != null">
            and ccspm.period &lt;= #{vo.periodEnd}
        </if>
        GROUP BY ccspm.accounting_dept_id
    </select>
    <select id="qualityCheckingRecordPageList"
            resultType="com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO">
        select
            qcr.batch_no as batchNo,
            qcr.id as qualityCheckingRecordId,
            ccspm.customer_service_id as customerServiceId,
            ccspm.id as customerServicePeriodMonthId,
            ccspm.customer_name as customerName,
            ccspm.credit_code as creditCode,
            ccspm.period as period,
            ccspm.business_top_dept_id as periodBusinessTopDeptId,
            ccspm.business_dept_id as periodBusinessDeptId,
            ccspm.advisor_dept_id as periodAdvisorDeptId,
            ccspm.accounting_top_dept_id as periodAccountingTopDeptId,
            ccspm.accounting_dept_id as periodAccountingDeptId,
            ccspm.tax_type as periodTaxType,
            qcr.quality_checking_type as qualityCheckingType,
            qcr.quality_checking_item_id as qualityCheckingItemId,
            qcr.quality_checking_cycle as qualityCheckingCycle,
            qcr.`status` as status,
            qcr.checking_result as checkingResult,
            qcr.create_by as createUserName,
            qcr.create_time as publishTime,
            qcr.finish_time as callbackTime
            from c_quality_checking_record qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        <if test="vo.periodTagName != null and vo.periodTagName != ''">
            <if test="vo.periodTagType == 1">
                <if test="vo.periodTagIncludeFlag == 0">
                    LEFT JOIN (
                    SELECT DISTINCT br.business_id
                    FROM c_business_tag_relation br
                    INNER JOIN c_tag t ON br.tag_id = t.id
                    WHERE t.is_del = 0
                    AND t.tag_name like CONCAT('%',#{vo.periodTagName},'%')
                    AND br.business_type = 2
                    ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                </if>
                <if test="vo.periodTagIncludeFlag == 1">
                    JOIN (
                    SELECT DISTINCT br.business_id
                    FROM c_business_tag_relation br
                    INNER JOIN c_tag t ON br.tag_id = t.id
                    WHERE t.is_del = 0
                    AND t.tag_name like CONCAT('%',#{vo.periodTagName},'%')
                    AND br.business_type = 2
                    ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                </if>
            </if>
            <if test="vo.periodTagType == 2">
                <if test="vo.periodTagIncludeFlag == 0">
                    LEFT JOIN (
                    SELECT DISTINCT br.business_id
                    FROM c_business_tag_relation br
                    INNER JOIN c_tag t ON br.tag_id = t.id
                    WHERE t.is_del = 0
                    AND t.tag_name in
                    <foreach collection="periodTagNameList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    AND br.business_type = 2
                    ) period_tag_filter ON ccspm.id = period_tag_filter.business_id
                </if>
                <if test="vo.periodTagIncludeFlag == 1">
                    JOIN (
                    SELECT DISTINCT br.business_id
                    FROM c_business_tag_relation br
                    INNER JOIN c_tag t ON br.tag_id = t.id
                    WHERE t.is_del = 0
                    AND t.tag_name in
                    <foreach collection="periodTagNameList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    AND br.business_type = 2
                    GROUP BY br.business_id HAVING COUNT(DISTINCT br.tag_id) = ${vo.periodTagSize}
                    ) period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id
                </if>
            </if>
        </if>
        <where>
            qcr.is_del = 0
            <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_dept_id in
                    <foreach collection="userDept.deptIds" open="(" close=")" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                ccspm.customer_name like concat('%',#{vo.keyWord},'%')
                or ccspm.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and ccspm.customer_service_id in
                <foreach collection="batchSearchCustomerServiceIds" separator="," item="customerServiceId" close=")" open="(">
                    #{customerServiceId}
                </foreach>
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag == 0">
                    and period_tag_filter.business_id is null
                </if>
                <if test="vo.periodTagIncludeFlag == 1">
                    and period_tag_filter.business_id is not null
                </if>
            </if>
            <if test="vo.periodStart != null">
                and ccspm.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and ccspm.period &lt;= #{vo.periodEnd}
            </if>
            <if test="userDept.deptType == 1">
                and qcr.quality_checking_type = 2
            </if>
            <if test="vo.qualityCheckingType != null">
                and qcr.quality_checking_type = #{vo.qualityCheckingType}
            </if>
            <if test="vo.qualityCheckingItemIds != null and vo.qualityCheckingItemIds != ''">
                and qcr.quality_checking_item_id in (${vo.qualityCheckingItemIds})
            </if>
            <if test="vo.qualityCheckingCycle != null">
                and qcr.quality_checking_cycle = #{vo.qualityCheckingCycle}
            </if>
            <if test="vo.status != null and vo.status != ''">
                and qcr.`status` in (${vo.status})
            </if>
            <if test="vo.checkingResult != null">
                and qcr.checking_result = #{vo.checkingResult}
            </if>
            <if test="vo.createTimeStart != null and vo.createTimeStart != ''">
                and qcr.create_time &gt;= #{vo.createTimeStart}
            </if>
            <if test="vo.createTimeEnd != null and vo.createTimeEnd != ''">
                and qcr.create_time &lt;= #{vo.createTimeEnd}
            </if>
            <if test="vo.advisorDeptId != null">
                and ccspm.advisor_dept_id = #{vo.advisorDeptId}
            </if>
            <if test="vo.accountingDeptId != null">
                and ccspm.accounting_dept_id = #{vo.accountingDeptId}
            </if>
        </where>
        order by qcr.batch_no desc, qcr.period desc, qcr.customer_service_id, qcr.id desc
    </select>
    <select id="qualityCheckingRecordListByIds"
            resultType="com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO">
        select
        qcr.batch_no as batchNo,
        qcr.id as qualityCheckingRecordId,
        ccspm.customer_service_id as customerServiceId,
        ccspm.id as customerServicePeriodMonthId,
        ccspm.customer_name as customerName,
        ccspm.credit_code as creditCode,
        ccspm.period as period,
        ccspm.business_top_dept_id as periodBusinessTopDeptId,
        ccspm.business_dept_id as periodBusinessDeptId,
        ccspm.advisor_dept_id as periodAdvisorDeptId,
        ccspm.accounting_top_dept_id as periodAccountingTopDeptId,
        ccspm.accounting_dept_id as periodAccountingDeptId,
        ccspm.tax_type as periodTaxType,
        qcr.quality_checking_type as qualityCheckingType,
        qcr.quality_checking_item_id as qualityCheckingItemId,
        qcr.quality_checking_cycle as qualityCheckingCycle,
        qcr.`status` as status,
        qcr.checking_result as checkingResult,
        qcr.create_by as createUserName,
        qcr.create_time as publishTime,
        qcr.finish_time as callbackTime
        from c_quality_checking_record qcr JOIN c_customer_service_period_month ccspm ON qcr.customer_service_period_month_id = ccspm.id
        where qcr.id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        order by qcr.batch_no desc, qcr.period desc, qcr.customer_service_id, qcr.id desc
    </select>

    <insert id="insertQualityCheckingRecord" parameterType="com.bxm.customer.domain.QualityCheckingRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_quality_checking_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="qualityCheckingItemId != null">quality_checking_item_id,</if>
            <if test="status != null">status,</if>
            <if test="checkingResult != null">checking_result,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="closeTime != null">close_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="qualityCheckingItemId != null">#{qualityCheckingItemId},</if>
            <if test="status != null">#{status},</if>
            <if test="checkingResult != null">#{checkingResult},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="closeTime != null">#{closeTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQualityCheckingRecord" parameterType="com.bxm.customer.domain.QualityCheckingRecord">
        update c_quality_checking_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="qualityCheckingItemId != null">quality_checking_item_id = #{qualityCheckingItemId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="checkingResult != null">checking_result = #{checkingResult},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="closeTime != null">close_time = #{closeTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQualityCheckingRecordById" parameterType="Long">
        delete from c_quality_checking_record where id = #{id}
    </delete>

    <delete id="deleteQualityCheckingRecordByIds" parameterType="String">
        delete from c_quality_checking_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>