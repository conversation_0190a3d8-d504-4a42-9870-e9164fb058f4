<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerMattersNotesMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerMattersNotes" id="CustomerMattersNotesResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="itemType"    column="item_type"    />
        <result property="mattersNotes"    column="matters_notes"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerMattersNotesVo">
        select id, customer_service_id, item_type, matters_notes, create_by, create_time, update_by, update_time from c_customer_matters_notes
    </sql>

    <select id="selectCustomerMattersNotesList" parameterType="com.bxm.customer.domain.CustomerMattersNotes" resultMap="CustomerMattersNotesResult">
        <include refid="selectCustomerMattersNotesVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="itemType != null "> and item_type = #{itemType}</if>
            <if test="mattersNotes != null  and mattersNotes != ''"> and matters_notes = #{mattersNotes}</if>
        </where>
    </select>
    
    <select id="selectCustomerMattersNotesById" parameterType="Long" resultMap="CustomerMattersNotesResult">
        <include refid="selectCustomerMattersNotesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerMattersNotes" parameterType="com.bxm.customer.domain.CustomerMattersNotes" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_matters_notes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="itemType != null">item_type,</if>
            <if test="mattersNotes != null">matters_notes,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="mattersNotes != null">#{mattersNotes},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerMattersNotes" parameterType="com.bxm.customer.domain.CustomerMattersNotes">
        update c_customer_matters_notes
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="mattersNotes != null">matters_notes = #{mattersNotes},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerMattersNotesById" parameterType="Long">
        delete from c_customer_matters_notes where id = #{id}
    </delete>

    <delete id="deleteCustomerMattersNotesByIds" parameterType="String">
        delete from c_customer_matters_notes where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>