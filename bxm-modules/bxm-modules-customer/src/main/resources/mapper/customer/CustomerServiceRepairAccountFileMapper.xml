<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceRepairAccountFileMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceRepairAccountFile" id="CustomerServiceRepairAccountFileResult">
        <result property="id"    column="id"    />
        <result property="customerServiceRepairAccountId"    column="customer_service_repair_account_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="subFileType"    column="sub_file_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceRepairAccountFileVo">
        select id, customer_service_repair_account_id, file_url, file_name, file_type, sub_file_type, create_by, create_time, update_by, update_time from c_customer_service_repair_account_file
    </sql>

    <select id="selectCustomerServiceRepairAccountFileList" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountFile" resultMap="CustomerServiceRepairAccountFileResult">
        <include refid="selectCustomerServiceRepairAccountFileVo"/>
        <where>  
            <if test="customerServiceRepairAccountId != null "> and customer_service_repair_account_id = #{customerServiceRepairAccountId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="subFileType != null  and subFileType != ''"> and sub_file_type = #{subFileType}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceRepairAccountFileById" parameterType="Long" resultMap="CustomerServiceRepairAccountFileResult">
        <include refid="selectCustomerServiceRepairAccountFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceRepairAccountFile" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">#{customerServiceRepairAccountId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="subFileType != null and subFileType != ''">#{subFileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceRepairAccountFile" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountFile">
        update c_customer_service_repair_account_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id = #{customerServiceRepairAccountId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type = #{subFileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceRepairAccountFileById" parameterType="Long">
        delete from c_customer_service_repair_account_file where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceRepairAccountFileByIds" parameterType="String">
        delete from c_customer_service_repair_account_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>