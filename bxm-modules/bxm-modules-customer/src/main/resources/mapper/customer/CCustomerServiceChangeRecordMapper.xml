<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CCustomerServiceChangeRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.CCustomerServiceChangeRecord" id="CCustomerServiceChangeRecordResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="validPeriod"    column="valid_period"    />
        <result property="changeType"    column="change_type"    />
        <result property="changeContent"    column="change_content"    />
        <result property="isValid"    column="is_valid"    />
        <result property="isDone"    column="is_done"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCCustomerServiceChangeRecordVo">
        select id, customer_service_id, valid_period, change_type, change_content, is_valid, is_done, create_by, create_time, update_by, update_time from c_customer_service_change_record
    </sql>

    <select id="selectCCustomerServiceChangeRecordList" parameterType="com.bxm.customer.domain.CCustomerServiceChangeRecord" resultMap="CCustomerServiceChangeRecordResult">
        <include refid="selectCCustomerServiceChangeRecordVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="validPeriod != null "> and valid_period = #{validPeriod}</if>
            <if test="changeType != null "> and change_type = #{changeType}</if>
            <if test="changeContent != null  and changeContent != ''"> and change_content = #{changeContent}</if>
            <if test="isValid != null "> and is_valid = #{isValid}</if>
            <if test="isDone != null "> and is_done = #{isDone}</if>
        </where>
    </select>
    
    <select id="selectCCustomerServiceChangeRecordById" parameterType="Long" resultMap="CCustomerServiceChangeRecordResult">
        <include refid="selectCCustomerServiceChangeRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCCustomerServiceChangeRecord" parameterType="com.bxm.customer.domain.CCustomerServiceChangeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_change_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="validPeriod != null">valid_period,</if>
            <if test="changeType != null">change_type,</if>
            <if test="changeContent != null and changeContent != ''">change_content,</if>
            <if test="isValid != null">is_valid,</if>
            <if test="isDone != null">is_done,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="validPeriod != null">#{validPeriod},</if>
            <if test="changeType != null">#{changeType},</if>
            <if test="changeContent != null and changeContent != ''">#{changeContent},</if>
            <if test="isValid != null">#{isValid},</if>
            <if test="isDone != null">#{isDone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCCustomerServiceChangeRecord" parameterType="com.bxm.customer.domain.CCustomerServiceChangeRecord">
        update c_customer_service_change_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="validPeriod != null">valid_period = #{validPeriod},</if>
            <if test="changeType != null">change_type = #{changeType},</if>
            <if test="changeContent != null and changeContent != ''">change_content = #{changeContent},</if>
            <if test="isValid != null">is_valid = #{isValid},</if>
            <if test="isDone != null">is_done = #{isDone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCCustomerServiceChangeRecordById" parameterType="Long">
        delete from c_customer_service_change_record where id = #{id}
    </delete>

    <delete id="deleteCCustomerServiceChangeRecordByIds" parameterType="String">
        delete from c_customer_service_change_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>