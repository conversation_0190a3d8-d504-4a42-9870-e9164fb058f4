<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerTaxTypeCheckMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerTaxTypeCheck" id="CustomerTaxTypeCheckResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="reportType"    column="report_type"    />
        <result property="taxType"    column="tax_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerTaxTypeCheckVo">
        select id, customer_service_id, report_type, tax_type, create_by, create_time, update_by, update_time from c_customer_tax_type_check
    </sql>

    <select id="selectCustomerTaxTypeCheckList" parameterType="com.bxm.customer.domain.CustomerTaxTypeCheck" resultMap="CustomerTaxTypeCheckResult">
        <include refid="selectCustomerTaxTypeCheckVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="reportType != null "> and report_type = #{reportType}</if>
            <if test="taxType != null  and taxType != ''"> and tax_type = #{taxType}</if>
        </where>
    </select>
    
    <select id="selectCustomerTaxTypeCheckById" parameterType="Long" resultMap="CustomerTaxTypeCheckResult">
        <include refid="selectCustomerTaxTypeCheckVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerTaxTypeCheck" parameterType="com.bxm.customer.domain.CustomerTaxTypeCheck" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_tax_type_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="reportType != null">report_type,</if>
            <if test="taxType != null and taxType != ''">tax_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="taxType != null and taxType != ''">#{taxType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerTaxTypeCheck" parameterType="com.bxm.customer.domain.CustomerTaxTypeCheck">
        update c_customer_tax_type_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="taxType != null and taxType != ''">tax_type = #{taxType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerTaxTypeCheckById" parameterType="Long">
        delete from c_customer_tax_type_check where id = #{id}
    </delete>

    <delete id="deleteCustomerTaxTypeCheckByIds" parameterType="String">
        delete from c_customer_tax_type_check where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>