<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiSyncItemMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiSyncItem" id="OpenApiSyncItemResult">
        <result property="id"    column="id"    />
        <result property="sycRecordId"    column="syc_record_id"    />
        <result property="syncCustomerId"    column="sync_customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="itemCategoryName"    column="item_category_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="actualPayTaxAmount"    column="actual_pay_tax_amount"    />
        <result property="isReport"    column="is_report"    />
        <result property="isPaid"    column="is_paid"    />
        <result property="taxPeriodStart"    column="tax_period_start"    />
        <result property="taxPeriodEnd"    column="tax_period_end"    />
        <result property="reportPeriod"    column="report_period"    />
        <result property="reportType"    column="report_type"    />
        <result property="reportDate"    column="report_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiSyncItemVo">
        select id, syc_record_id, sync_customer_id, customer_name, tax_number, item_category_name, item_name, actual_pay_tax_amount, is_report, is_paid, tax_period_start, tax_period_end, report_period, report_type, report_date, create_by, create_time, update_by, update_time from c_open_api_sync_item
    </sql>

    <select id="selectOpenApiSyncItemList" parameterType="com.bxm.customer.domain.OpenApiSyncItem" resultMap="OpenApiSyncItemResult">
        <include refid="selectOpenApiSyncItemVo"/>
        <where>  
            <if test="sycRecordId != null "> and syc_record_id = #{sycRecordId}</if>
            <if test="syncCustomerId != null "> and sync_customer_id = #{syncCustomerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="itemCategoryName != null  and itemCategoryName != ''"> and item_category_name like concat('%', #{itemCategoryName}, '%')</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="actualPayTaxAmount != null "> and actual_pay_tax_amount = #{actualPayTaxAmount}</if>
            <if test="isReport != null  and isReport != ''"> and is_report = #{isReport}</if>
            <if test="isPaid != null  and isPaid != ''"> and is_paid = #{isPaid}</if>
            <if test="taxPeriodStart != null  and taxPeriodStart != ''"> and tax_period_start = #{taxPeriodStart}</if>
            <if test="taxPeriodEnd != null  and taxPeriodEnd != ''"> and tax_period_end = #{taxPeriodEnd}</if>
            <if test="reportPeriod != null  and reportPeriod != ''"> and report_period = #{reportPeriod}</if>
            <if test="reportType != null  and reportType != ''"> and report_type = #{reportType}</if>
            <if test="reportDate != null  and reportDate != ''"> and report_date = #{reportDate}</if>
        </where>
    </select>
    
    <select id="selectOpenApiSyncItemById" parameterType="Long" resultMap="OpenApiSyncItemResult">
        <include refid="selectOpenApiSyncItemVo"/>
        where id = #{id}
    </select>
    <select id="waitReportDeliverCount" resultType="java.lang.Long">
        SELECT COUNT(ccspm.id)
        FROM c_open_api_sync_item coasi
                 JOIN (
            SELECT tax_number, MAX(syc_record_id) AS max_id
            FROM c_open_api_sync_item
            GROUP BY tax_number
        ) t ON coasi.tax_number = t.tax_number AND coasi.syc_record_id = t.max_id
                 JOIN c_customer_service_period_month ccspm ON coasi.tax_number = ccspm.tax_number AND ccspm.period = #{period}
        JOIN c_customer_service ccs ON ccspm.customer_service_id = ccs.id AND ccs.is_del = 0
        <where>
            tax_period_end_month = #{reportPeriod} and coasi.is_report = '否' and coasi.report_type != '次'
            AND coasi.item_category_name NOT LIKE '个人所得税%'
            AND coasi.item_category_name NOT LIKE '社保%'
            AND coasi.item_category_name NOT LIKE '医保%'
            <if test="userDept.isAdmin == false">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (ccspm.advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDept.deptType == 2">
                        and (ccspm.accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>
    <select id="waitDeductionDeliverCount" resultType="java.lang.Long">
        SELECT COUNT(ccspm.id)
        FROM c_open_api_sync_item coasi
                 JOIN (
            SELECT tax_number, MAX(syc_record_id) AS max_id
            FROM c_open_api_sync_item
            GROUP BY tax_number
        ) t ON coasi.tax_number = t.tax_number AND coasi.syc_record_id = t.max_id
                 JOIN c_customer_service_period_month ccspm ON coasi.tax_number = ccspm.tax_number AND ccspm.period = #{period}
        JOIN c_customer_service ccs ON ccspm.customer_service_id = ccs.id AND ccs.is_del = 0
        <where>
            tax_period_end_month = #{reportPeriod} and coasi.is_report = '是' and coasi.is_paid = '否' and coasi.report_type != '次'
            AND coasi.item_category_name NOT LIKE '个人所得税%'
            AND coasi.item_category_name NOT LIKE '社保%'
            AND coasi.item_category_name NOT LIKE '医保%'
            <if test="userDept.isAdmin == false">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (ccspm.advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDept.deptType == 2">
                        and (ccspm.accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>
    <select id="selectSyncItemPageList" resultType="com.bxm.customer.domain.dto.workBench.SyncItemSearchDTO">
        select
            ccs.id as customerServiceId,
            ccspm.id as customerServicePeriodMonthId,
            ccs.customer_name as customerName,
            ccs.customer_company_name as customerCompanyName,
            coasi.item_category_name as categoryName,
            coasi.item_name as itemName,
            coasi.tax_period_start as reportPeriodStart,
            coasi.tax_period_end as reportPeriodEnd,
            coasi.report_type as reportType,
            coasi.actual_pay_tax_amount as actualPayTaxAmount,
            ccs.credit_code as creditCode,
            ccspm.tax_type as taxType,
            ccspm.advisor_dept_id as advisorDeptId,
            ccspm.accounting_dept_id as accountingDeptId
        FROM c_open_api_sync_item coasi
                 JOIN (
            SELECT tax_number, MAX(syc_record_id) AS max_id
            FROM c_open_api_sync_item
            GROUP BY tax_number
        ) t ON coasi.tax_number = t.tax_number AND coasi.syc_record_id = t.max_id
                 JOIN c_customer_service_period_month ccspm ON coasi.tax_number = ccspm.tax_number AND ccspm.period = #{vo.period}
                 JOIN c_customer_service ccs ON ccspm.customer_service_id = ccs.id AND ccs.is_del = 0
        <where>
            <if test="vo.type == 1">
                and coasi.tax_period_end_month = #{vo.periodStr} and coasi.is_report = '否' and coasi.report_type != '次'
            </if>
            <if test="vo.type == 2">
                and coasi.tax_period_end_month = #{vo.periodStr} and coasi.is_report = '是' and coasi.is_paid = '否' and coasi.report_type != '次'
            </if>
            AND coasi.item_category_name NOT LIKE '个人所得税%'
            AND coasi.item_category_name NOT LIKE '社保%'
            AND coasi.item_category_name NOT LIKE '医保%'
            <if test="userDept.isAdmin == false">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (ccspm.advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDept.deptType == 2">
                        and (ccspm.accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (ccspm.accounting_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="customerServiceIds != null and customerServiceIds.size > 0">
                and ccs.id in (
                <foreach item="customerServiceId" collection="customerServiceIds" separator=",">
                    #{customerServiceId}
                </foreach>
                )
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag == 1">
                    <if test="periodIds != null and periodIds.size > 0">
                        and ccspm.id in (
                        <foreach item="periodId" collection="periodIds" separator=",">
                            #{periodId}
                        </foreach>
                        )
                    </if>
                </if>
                <if test="vo.periodTagIncludeFlag == 0">
                    <if test="periodIds != null and periodIds.size > 0">
                        and ccspm.id not in (
                        <foreach item="periodId" collection="periodIds" separator=",">
                            #{periodId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%',#{vo.keyWord},'%') or ccs.credit_code = #{vo.keyWord})
            </if>
            <if test="vo.periodAdvisorDeptId != null">
                and ccspm.advisor_dept_id = #{vo.periodAdvisorDeptId}
            </if>
            <if test="vo.periodAccountingDeptId != null">
                and ccspm.accounting_dept_id = #{vo.periodAccountingDeptId}
            </if>
            <if test="vo.periodTaxType != null">
                and ccspm.tax_type = #{vo.periodTaxType}
            </if>
        </where>
        order by coasi.id desc
    </select>
    <select id="syncItemAdvisorDeptList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.advisor_dept_id as deptId,
        count(ccspm.id) as dataCount
        FROM c_open_api_sync_item coasi
        JOIN (
        SELECT tax_number, MAX(syc_record_id) AS max_id
        FROM c_open_api_sync_item
        GROUP BY tax_number
        ) t ON coasi.tax_number = t.tax_number AND coasi.syc_record_id = t.max_id
        JOIN c_customer_service_period_month ccspm ON coasi.tax_number = ccspm.tax_number AND ccspm.period = #{period}
        JOIN c_customer_service ccs ON ccspm.customer_service_id = ccs.id AND ccs.is_del = 0
        <where>
            ccspm.advisor_dept_id IS NOT NULL
            <if test="type == 1">
                and tax_period_end_month = #{periodStr} and coasi.is_report = '否' and coasi.report_type != '次'
            </if>
            <if test="type == 2">
                and tax_period_end_month = #{periodStr} and coasi.is_report = '是' and coasi.is_paid = '否' and coasi.report_type != '次'
            </if>
            AND coasi.item_category_name NOT LIKE '个人所得税%'
            AND coasi.item_category_name NOT LIKE '社保%'
            AND coasi.item_category_name NOT LIKE '医保%'
            <if test="userDept.isAdmin == false">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (ccspm.advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDept.deptType == 2">
                        and (ccspm.accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 1">
                    and (ccspm.accounting_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        group by ccspm.advisor_dept_id
    </select>
    <select id="syncItemAccountingDeptList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        ccspm.accounting_dept_id as deptId,
        count(ccspm.id) as dataCount
        FROM c_open_api_sync_item coasi
        JOIN (
        SELECT tax_number, MAX(syc_record_id) AS max_id
        FROM c_open_api_sync_item
        GROUP BY tax_number
        ) t ON coasi.tax_number = t.tax_number AND coasi.syc_record_id = t.max_id
        JOIN c_customer_service_period_month ccspm ON coasi.tax_number = ccspm.tax_number AND ccspm.period = #{period}
        JOIN c_customer_service ccs ON ccspm.customer_service_id = ccs.id AND ccs.is_del = 0
        <where>
            ccspm.accounting_dept_id IS NOT NULL
            <if test="type == 1">
                and tax_period_end_month = #{periodStr} and coasi.is_report = '否' and coasi.report_type != '次'
            </if>
            <if test="type == 2">
                and tax_period_end_month = #{periodStr} and coasi.is_report = '是' and coasi.is_paid = '否' and coasi.report_type != '次'
            </if>
            AND coasi.item_category_name NOT LIKE '个人所得税%'
            AND coasi.item_category_name NOT LIKE '社保%'
            AND coasi.item_category_name NOT LIKE '医保%'
            <if test="userDept.isAdmin == false">
                <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                    <if test="userDept.deptType == 1">
                        and (ccspm.advisor_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDept.deptType == 2">
                        and (ccspm.accounting_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        or ccspm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDept.deptIds" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (ccspm.advisor_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.advisor_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 1">
                    and (ccspm.accounting_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccspm.accounting_top_dept_id in
                    <foreach item="deptId" collection="queryDeptIds" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        group by ccspm.accounting_dept_id
    </select>

    <insert id="insertOpenApiSyncItem" parameterType="com.bxm.customer.domain.OpenApiSyncItem" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_sync_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sycRecordId != null">syc_record_id,</if>
            <if test="syncCustomerId != null">sync_customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="itemCategoryName != null">item_category_name,</if>
            <if test="itemName != null">item_name,</if>
            <if test="actualPayTaxAmount != null">actual_pay_tax_amount,</if>
            <if test="isReport != null">is_report,</if>
            <if test="isPaid != null">is_paid,</if>
            <if test="taxPeriodStart != null">tax_period_start,</if>
            <if test="taxPeriodEnd != null">tax_period_end,</if>
            <if test="reportPeriod != null">report_period,</if>
            <if test="reportType != null">report_type,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sycRecordId != null">#{sycRecordId},</if>
            <if test="syncCustomerId != null">#{syncCustomerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="itemCategoryName != null">#{itemCategoryName},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="actualPayTaxAmount != null">#{actualPayTaxAmount},</if>
            <if test="isReport != null">#{isReport},</if>
            <if test="isPaid != null">#{isPaid},</if>
            <if test="taxPeriodStart != null">#{taxPeriodStart},</if>
            <if test="taxPeriodEnd != null">#{taxPeriodEnd},</if>
            <if test="reportPeriod != null">#{reportPeriod},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiSyncItem" parameterType="com.bxm.customer.domain.OpenApiSyncItem">
        update c_open_api_sync_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="sycRecordId != null">syc_record_id = #{sycRecordId},</if>
            <if test="syncCustomerId != null">sync_customer_id = #{syncCustomerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="itemCategoryName != null">item_category_name = #{itemCategoryName},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="actualPayTaxAmount != null">actual_pay_tax_amount = #{actualPayTaxAmount},</if>
            <if test="isReport != null">is_report = #{isReport},</if>
            <if test="isPaid != null">is_paid = #{isPaid},</if>
            <if test="taxPeriodStart != null">tax_period_start = #{taxPeriodStart},</if>
            <if test="taxPeriodEnd != null">tax_period_end = #{taxPeriodEnd},</if>
            <if test="reportPeriod != null">report_period = #{reportPeriod},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiSyncItemById" parameterType="Long">
        delete from c_open_api_sync_item where id = #{id}
    </delete>

    <delete id="deleteOpenApiSyncItemByIds" parameterType="String">
        delete from c_open_api_sync_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>