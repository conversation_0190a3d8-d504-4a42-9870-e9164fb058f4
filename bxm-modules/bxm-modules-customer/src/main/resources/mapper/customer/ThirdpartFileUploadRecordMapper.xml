<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.ThirdpartFileUploadRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.ThirdpartFileUploadRecord" id="ThirdpartFileUploadRecordResult">
        <result property="id"    column="id"    />
        <result property="thirdpartFileUrl"    column="thirdpart_file_url"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="officalFileName"    column="offical_file_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectThirdpartFileUploadRecordVo">
        select id, thirdpart_file_url, file_url, file_name, offical_file_name, create_by, create_time, update_by, update_time from c_thirdpart_file_upload_record
    </sql>

    <select id="selectThirdpartFileUploadRecordList" parameterType="com.bxm.customer.domain.ThirdpartFileUploadRecord" resultMap="ThirdpartFileUploadRecordResult">
        <include refid="selectThirdpartFileUploadRecordVo"/>
        <where>  
            <if test="thirdpartFileUrl != null  and thirdpartFileUrl != ''"> and thirdpart_file_url = #{thirdpartFileUrl}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="officalFileName != null  and officalFileName != ''"> and offical_file_name like concat('%', #{officalFileName}, '%')</if>
        </where>
    </select>
    
    <select id="selectThirdpartFileUploadRecordById" parameterType="Long" resultMap="ThirdpartFileUploadRecordResult">
        <include refid="selectThirdpartFileUploadRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertThirdpartFileUploadRecord" parameterType="com.bxm.customer.domain.ThirdpartFileUploadRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_thirdpart_file_upload_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdpartFileUrl != null and thirdpartFileUrl != ''">thirdpart_file_url,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="officalFileName != null and officalFileName != ''">offical_file_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="thirdpartFileUrl != null and thirdpartFileUrl != ''">#{thirdpartFileUrl},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="officalFileName != null and officalFileName != ''">#{officalFileName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateThirdpartFileUploadRecord" parameterType="com.bxm.customer.domain.ThirdpartFileUploadRecord">
        update c_thirdpart_file_upload_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="thirdpartFileUrl != null and thirdpartFileUrl != ''">thirdpart_file_url = #{thirdpartFileUrl},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="officalFileName != null and officalFileName != ''">offical_file_name = #{officalFileName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteThirdpartFileUploadRecordById" parameterType="Long">
        delete from c_thirdpart_file_upload_record where id = #{id}
    </delete>

    <delete id="deleteThirdpartFileUploadRecordByIds" parameterType="String">
        delete from c_thirdpart_file_upload_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>