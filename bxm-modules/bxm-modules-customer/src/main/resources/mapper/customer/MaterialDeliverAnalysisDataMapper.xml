<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverAnalysisDataMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliverAnalysisData" id="MaterialDeliverAnalysisDataResult">
        <result property="id"    column="id"    />
        <result property="materialDeliverId"    column="material_deliver_id"    />
        <result property="materialFileName"    column="material_file_name"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="bankAccountNumber"    column="bank_account_number"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="period"    column="period"    />
        <result property="fileNumber"    column="file_number"    />
        <result property="remark"    column="remark"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverAnalysisDataVo">
        select id, material_deliver_id, material_file_name, customer_name, credit_code, bank_account_number, start_date, end_date, period, file_number, remark, file_url, file_size, file_name, create_by, create_time, update_by, update_time from c_material_deliver_analysis_data
    </sql>

    <select id="selectMaterialDeliverAnalysisDataList" parameterType="com.bxm.customer.domain.MaterialDeliverAnalysisData" resultMap="MaterialDeliverAnalysisDataResult">
        <include refid="selectMaterialDeliverAnalysisDataVo"/>
        <where>  
            <if test="materialDeliverId != null "> and material_deliver_id = #{materialDeliverId}</if>
            <if test="materialFileName != null  and materialFileName != ''"> and material_file_name like concat('%', #{materialFileName}, '%')</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="bankAccountNumber != null  and bankAccountNumber != ''"> and bank_account_number = #{bankAccountNumber}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="period != null  and period != ''"> and period = #{period}</if>
            <if test="fileNumber != null  and fileNumber != ''"> and file_number = #{fileNumber}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverAnalysisDataById" parameterType="Long" resultMap="MaterialDeliverAnalysisDataResult">
        <include refid="selectMaterialDeliverAnalysisDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMaterialDeliverAnalysisData" parameterType="com.bxm.customer.domain.MaterialDeliverAnalysisData" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver_analysis_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id,</if>
            <if test="materialFileName != null and materialFileName != ''">material_file_name,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="bankAccountNumber != null">bank_account_number,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="period != null">period,</if>
            <if test="fileNumber != null">file_number,</if>
            <if test="remark != null">remark,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">#{materialDeliverId},</if>
            <if test="materialFileName != null and materialFileName != ''">#{materialFileName},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="bankAccountNumber != null">#{bankAccountNumber},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="period != null">#{period},</if>
            <if test="fileNumber != null">#{fileNumber},</if>
            <if test="remark != null">#{remark},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliverAnalysisData" parameterType="com.bxm.customer.domain.MaterialDeliverAnalysisData">
        update c_material_deliver_analysis_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id = #{materialDeliverId},</if>
            <if test="materialFileName != null and materialFileName != ''">material_file_name = #{materialFileName},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="bankAccountNumber != null">bank_account_number = #{bankAccountNumber},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="period != null">period = #{period},</if>
            <if test="fileNumber != null">file_number = #{fileNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverAnalysisDataById" parameterType="Long">
        delete from c_material_deliver_analysis_data where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverAnalysisDataByIds" parameterType="String">
        delete from c_material_deliver_analysis_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>