<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceRepairAccountTempTaxInstrumentMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument" id="CustomerServiceRepairAccountTempTaxInstrumentResult">
        <result property="id"    column="id"    />
        <result property="customerServiceRepairAccountId"    column="customer_service_repair_account_id"    />
        <result property="period"    column="period"    />
        <result property="bizType"    column="biz_type"    />
        <result property="name"    column="name"    />
        <result property="rowNum"    column="row_num"    />
        <result property="paperCount"    column="paper_count"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceRepairAccountTempTaxInstrumentVo">
        select id, customer_service_repair_account_id, period, biz_type, name, row_num, paper_count, remark, create_by, create_time, update_by, update_time from c_customer_service_repair_account_temp_tax_instrument
    </sql>

    <select id="selectCustomerServiceRepairAccountTempTaxInstrumentList" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument" resultMap="CustomerServiceRepairAccountTempTaxInstrumentResult">
        <include refid="selectCustomerServiceRepairAccountTempTaxInstrumentVo"/>
        <where>  
            <if test="customerServiceRepairAccountId != null "> and customer_service_repair_account_id = #{customerServiceRepairAccountId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="bizType != null "> and biz_type = #{bizType}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="rowNum != null "> and row_num = #{rowNum}</if>
            <if test="paperCount != null "> and paper_count = #{paperCount}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceRepairAccountTempTaxInstrumentById" parameterType="Long" resultMap="CustomerServiceRepairAccountTempTaxInstrumentResult">
        <include refid="selectCustomerServiceRepairAccountTempTaxInstrumentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceRepairAccountTempTaxInstrument" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account_temp_tax_instrument
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id,</if>
            <if test="period != null">period,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="rowNum != null">row_num,</if>
            <if test="paperCount != null">paper_count,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">#{customerServiceRepairAccountId},</if>
            <if test="period != null">#{period},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="rowNum != null">#{rowNum},</if>
            <if test="paperCount != null">#{paperCount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceRepairAccountTempTaxInstrument" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument">
        update c_customer_service_repair_account_temp_tax_instrument
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id = #{customerServiceRepairAccountId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="rowNum != null">row_num = #{rowNum},</if>
            <if test="paperCount != null">paper_count = #{paperCount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceRepairAccountTempTaxInstrumentById" parameterType="Long">
        delete from c_customer_service_repair_account_temp_tax_instrument where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceRepairAccountTempTaxInstrumentByIds" parameterType="String">
        delete from c_customer_service_repair_account_temp_tax_instrument where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>