<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceInsuranceFundStatusMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceInsuranceFundStatus" id="CustomerServiceInsuranceFundStatusResult">
        <result property="id"    column="id"    />
        <result property="insuranceFundId"    column="insurance_fund_id"    />
        <result property="month"    column="month"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceInsuranceFundStatusVo">
        select id, insurance_fund_id, month, status, create_by, create_time, update_by, update_time from c_customer_service_insurance_fund_status
    </sql>

    <select id="selectCustomerServiceInsuranceFundStatusList" parameterType="com.bxm.customer.domain.CustomerServiceInsuranceFundStatus" resultMap="CustomerServiceInsuranceFundStatusResult">
        <include refid="selectCustomerServiceInsuranceFundStatusVo"/>
        <where>  
            <if test="insuranceFundId != null "> and insurance_fund_id = #{insuranceFundId}</if>
            <if test="month != null "> and month = #{month}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceInsuranceFundStatusById" parameterType="Long" resultMap="CustomerServiceInsuranceFundStatusResult">
        <include refid="selectCustomerServiceInsuranceFundStatusVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceInsuranceFundStatus" parameterType="com.bxm.customer.domain.CustomerServiceInsuranceFundStatus" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_insurance_fund_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="insuranceFundId != null">insurance_fund_id,</if>
            <if test="month != null">month,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="insuranceFundId != null">#{insuranceFundId},</if>
            <if test="month != null">#{month},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceInsuranceFundStatus" parameterType="com.bxm.customer.domain.CustomerServiceInsuranceFundStatus">
        update c_customer_service_insurance_fund_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="insuranceFundId != null">insurance_fund_id = #{insuranceFundId},</if>
            <if test="month != null">month = #{month},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceInsuranceFundStatusById" parameterType="Long">
        delete from c_customer_service_insurance_fund_status where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceInsuranceFundStatusByIds" parameterType="String">
        delete from c_customer_service_insurance_fund_status where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>