<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerTaxTypeCheckMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerTaxTypeCheck" id="NewCustomerTaxTypeCheckResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="reportType"    column="report_type"    />
        <result property="taxType"    column="tax_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerTaxTypeCheckVo">
        select id, customer_id, report_type, tax_type, create_by, create_time, update_by, update_time from c_new_customer_tax_type_check
    </sql>

    <select id="selectNewCustomerTaxTypeCheckList" parameterType="com.bxm.customer.domain.NewCustomerTaxTypeCheck" resultMap="NewCustomerTaxTypeCheckResult">
        <include refid="selectNewCustomerTaxTypeCheckVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="reportType != null "> and report_type = #{reportType}</if>
            <if test="taxType != null  and taxType != ''"> and tax_type = #{taxType}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerTaxTypeCheckById" parameterType="Long" resultMap="NewCustomerTaxTypeCheckResult">
        <include refid="selectNewCustomerTaxTypeCheckVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerTaxTypeCheck" parameterType="com.bxm.customer.domain.NewCustomerTaxTypeCheck" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_tax_type_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="reportType != null">report_type,</if>
            <if test="taxType != null and taxType != ''">tax_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="taxType != null and taxType != ''">#{taxType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerTaxTypeCheck" parameterType="com.bxm.customer.domain.NewCustomerTaxTypeCheck">
        update c_new_customer_tax_type_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="taxType != null and taxType != ''">tax_type = #{taxType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerTaxTypeCheckById" parameterType="Long">
        delete from c_new_customer_tax_type_check where id = #{id}
    </delete>

    <delete id="deleteNewCustomerTaxTypeCheckByIds" parameterType="String">
        delete from c_new_customer_tax_type_check where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>