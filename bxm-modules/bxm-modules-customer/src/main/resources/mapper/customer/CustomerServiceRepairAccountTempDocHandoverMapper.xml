<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceRepairAccountTempDocHandoverMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover" id="CustomerServiceRepairAccountTempDocHandoverResult">
        <result property="id"    column="id"    />
        <result property="customerServiceRepairAccountId"    column="customer_service_repair_account_id"    />
        <result property="period"    column="period"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="title"    column="title"    />
        <result property="batchNum"    column="batch_num"    />
        <result property="status"    column="status"    />
        <result property="isVoucherEntry"    column="is_voucher_entry"    />
        <result property="remark"    column="remark"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="submitEmployeeDeptId"    column="submit_employee_dept_id"    />
        <result property="submitEmployeeDeptName"    column="submit_employee_dept_name"    />
        <result property="submitEmployeeId"    column="submit_employee_id"    />
        <result property="submitEmployeeName"    column="submit_employee_name"    />
        <result property="verificationEmployeeType"    column="verification_employee_type"    />
        <result property="verificationTime"    column="verification_time"    />
        <result property="verificationEmployeeDeptId"    column="verification_employee_dept_id"    />
        <result property="verificationEmployeeDeptName"    column="verification_employee_dept_name"    />
        <result property="verificationEmployeeId"    column="verification_employee_id"    />
        <result property="verificationEmployeeName"    column="verification_employee_name"    />
        <result property="wholeLevel"    column="whole_level"    />
        <result property="verificationRemark"    column="verification_remark"    />
        <result property="backTime"    column="back_time"    />
        <result property="backEmployeeDeptId"    column="back_employee_dept_id"    />
        <result property="backEmployeeDeptName"    column="back_employee_dept_name"    />
        <result property="backEmployeeId"    column="back_employee_id"    />
        <result property="backEmployeeName"    column="back_employee_name"    />
        <result property="backRemark"    column="back_remark"    />
        <result property="hasTaxTicket"    column="has_tax_ticket"    />
        <result property="hasOtherTicket"    column="has_other_ticket"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceRepairAccountTempDocHandoverVo">
        select id, customer_service_repair_account_id, period, customer_service_id, customer_name, credit_code, title, batch_num, status, is_voucher_entry, remark, submit_time, submit_employee_dept_id, submit_employee_dept_name, submit_employee_id, submit_employee_name, verification_employee_type, verification_time, verification_employee_dept_id, verification_employee_dept_name, verification_employee_id, verification_employee_name, whole_level, verification_remark, back_time, back_employee_dept_id, back_employee_dept_name, back_employee_id, back_employee_name, back_remark, has_tax_ticket, has_other_ticket, create_by, create_time, update_by, update_time from c_customer_service_repair_account_temp_doc_handover
    </sql>

    <select id="selectCustomerServiceRepairAccountTempDocHandoverList" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover" resultMap="CustomerServiceRepairAccountTempDocHandoverResult">
        <include refid="selectCustomerServiceRepairAccountTempDocHandoverVo"/>
        <where>
            <if test="customerServiceRepairAccountId != null "> and customer_service_repair_account_id = #{customerServiceRepairAccountId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code like concat('%', #{creditCode}, '%')</if>
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="batchNum != null  and batchNum != ''"> and batch_num = #{batchNum}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isVoucherEntry != null "> and is_voucher_entry = #{isVoucherEntry}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="submitEmployeeDeptId != null "> and submit_employee_dept_id = #{submitEmployeeDeptId}</if>
            <if test="submitEmployeeDeptName != null "> and submit_employee_dept_name = #{submitEmployeeDeptName}</if>
            <if test="submitEmployeeId != null "> and submit_employee_id = #{submitEmployeeId}</if>
            <if test="submitEmployeeName != null  and submitEmployeeName != ''"> and submit_employee_name like concat('%', #{submitEmployeeName}, '%')</if>
            <if test="verificationEmployeeType != null"> and verification_employee_type = #{verificationEmployeeType}</if>
            <if test="verificationTime != null "> and verification_time = #{verificationTime}</if>
            <if test="verificationEmployeeDeptId != null "> and verification_employee_dept_id = #{verificationEmployeeDeptId}</if>
            <if test="verificationEmployeeDeptName != null "> and verification_employee_dept_name = #{verificationEmployeeDeptName}</if>
            <if test="verificationEmployeeId != null "> and verification_employee_id = #{verificationEmployeeId}</if>
            <if test="verificationEmployeeName != null  and verificationEmployeeName != ''"> and verification_employee_name like concat('%', #{verificationEmployeeName}, '%')</if>
            <if test="wholeLevel != null "> and whole_level = #{wholeLevel}</if>
            <if test="verificationRemark != null  and verificationRemark != ''"> and verification_remark = #{verificationRemark}</if>
            <if test="backTime != null "> and back_time = #{backTime}</if>
            <if test="backEmployeeDeptId != null "> and back_employee_dept_id = #{backEmployeeDeptId}</if>
            <if test="backEmployeeDeptName != null "> and back_employee_dept_name = #{backEmployeeDeptName}</if>
            <if test="backEmployeeId != null "> and back_employee_id = #{backEmployeeId}</if>
            <if test="backEmployeeName != null  and backEmployeeName != ''"> and back_employee_name like concat('%', #{backEmployeeName}, '%')</if>
            <if test="backRemark != null  and backRemark != ''"> and back_remark = #{backRemark}</if>
            <if test="hasTaxTicket != null  and hasTaxTicket != ''"> and has_tax_ticket = #{hasTaxTicket}</if>
            <if test="hasOtherTicket != null  and hasOtherTicket != ''"> and has_other_ticket = #{hasOtherTicket}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceRepairAccountTempDocHandoverById" parameterType="Long" resultMap="CustomerServiceRepairAccountTempDocHandoverResult">
        <include refid="selectCustomerServiceRepairAccountTempDocHandoverVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceRepairAccountTempDocHandover" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account_temp_doc_handover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="title != null">title,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="status != null">status,</if>
            <if test="isVoucherEntry != null">is_voucher_entry,</if>
            <if test="remark != null">remark,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id,</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name,</if>
            <if test="submitEmployeeId != null">submit_employee_id,</if>
            <if test="submitEmployeeName != null">submit_employee_name,</if>
            <if test="verificationEmployeeType != null">verification_employee_type,</if>
            <if test="verificationTime != null">verification_time,</if>
            <if test="verificationEmployeeDeptId != null">verification_employee_dept_id,</if>
            <if test="verificationEmployeeDeptName != null">verification_employee_dept_name,</if>
            <if test="verificationEmployeeId != null">verification_employee_id,</if>
            <if test="verificationEmployeeName != null">verification_employee_name,</if>
            <if test="wholeLevel != null">whole_level,</if>
            <if test="verificationRemark != null">verification_remark,</if>
            <if test="backTime != null">back_time,</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id,</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name,</if>
            <if test="backEmployeeId != null">back_employee_id,</if>
            <if test="backEmployeeName != null">back_employee_name,</if>
            <if test="backRemark != null">back_remark,</if>
            <if test="hasTaxTicket != null">has_tax_ticket,</if>
            <if test="hasOtherTicket != null">has_other_ticket,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">#{customerServiceRepairAccountId},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="title != null">#{title},</if>
            <if test="batchNum != null">#{batchNum},</if>
            <if test="status != null">#{status},</if>
            <if test="isVoucherEntry != null">#{isVoucherEntry},</if>
            <if test="remark != null">#{remark},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="submitEmployeeDeptId != null">#{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">#{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">#{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">#{submitEmployeeName},</if>
            <if test="verificationEmployeeType != null">#{verificationEmployeeType},</if>
            <if test="verificationTime != null">#{verificationTime},</if>
            <if test="verificationEmployeeDeptId != null">#{verificationEmployeeDeptId},</if>
            <if test="verificationEmployeeDeptName != null">#{verificationEmployeeDeptName},</if>
            <if test="verificationEmployeeId != null">#{verificationEmployeeId},</if>
            <if test="verificationEmployeeName != null">#{verificationEmployeeName},</if>
            <if test="wholeLevel != null">#{wholeLevel},</if>
            <if test="verificationRemark != null">#{verificationRemark},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="backEmployeeDeptId != null">#{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">#{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">#{backEmployeeId},</if>
            <if test="backEmployeeName != null">#{backEmployeeName},</if>
            <if test="backRemark != null">#{backRemark},</if>
            <if test="hasTaxTicket != null">#{hasTaxTicket},</if>
            <if test="hasOtherTicket != null">#{hasOtherTicket},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceRepairAccountTempDocHandover" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover">
        update c_customer_service_repair_account_temp_doc_handover
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id = #{customerServiceRepairAccountId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="title != null">title = #{title},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isVoucherEntry != null">is_voucher_entry = #{isVoucherEntry},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="submitEmployeeDeptId != null">submit_employee_dept_id = #{submitEmployeeDeptId},</if>
            <if test="submitEmployeeDeptName != null">submit_employee_dept_name = #{submitEmployeeDeptName},</if>
            <if test="submitEmployeeId != null">submit_employee_id = #{submitEmployeeId},</if>
            <if test="submitEmployeeName != null">submit_employee_name = #{submitEmployeeName},</if>
            <if test="verificationEmployeeType != null">verification_employee_type = #{verificationEmployeeType},</if>
            <if test="verificationTime != null">verification_time = #{verificationTime},</if>
            <if test="verificationEmployeeDeptId != null">verification_employee_dept_id = #{verificationEmployeeDeptId},</if>
            <if test="verificationEmployeeDeptName != null">verification_employee_dept_name = #{verificationEmployeeDeptName},</if>
            <if test="verificationEmployeeId != null">verification_employee_id = #{verificationEmployeeId},</if>
            <if test="verificationEmployeeName != null">verification_employee_name = #{verificationEmployeeName},</if>
            <if test="wholeLevel != null">whole_level = #{wholeLevel},</if>
            <if test="verificationRemark != null">verification_remark = #{verificationRemark},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="backEmployeeDeptId != null">back_employee_dept_id = #{backEmployeeDeptId},</if>
            <if test="backEmployeeDeptName != null">back_employee_dept_name = #{backEmployeeDeptName},</if>
            <if test="backEmployeeId != null">back_employee_id = #{backEmployeeId},</if>
            <if test="backEmployeeName != null">back_employee_name = #{backEmployeeName},</if>
            <if test="backRemark != null">back_remark = #{backRemark},</if>
            <if test="hasTaxTicket != null">has_tax_ticket = #{hasTaxTicket},</if>
            <if test="hasOtherTicket != null">has_other_ticket = #{hasOtherTicket},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceRepairAccountTempDocHandoverById" parameterType="Long">
        delete from c_customer_service_repair_account_temp_doc_handover where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceRepairAccountTempDocHandoverByIds" parameterType="String">
        delete from c_customer_service_repair_account_temp_doc_handover where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="insertCustomerServiceRepairAccountTempDocHandoverWithBatchNum" parameterType="com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_repair_account_temp_doc_handover
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">customer_service_repair_account_id,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="title != null">title,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="status != null">status,</if>
            <if test="isVoucherEntry != null">is_voucher_entry,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceRepairAccountId != null">#{customerServiceRepairAccountId},</if>
            <if test="period != null">#{period},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="title != null">#{title},</if>
            <if test="batchNum != null">(select ifnull(temp.maxId, 0) + #{batchNum} as maxId from (select max(batch_num) as maxId from c_customer_service_repair_account_temp_doc_handover) as temp),</if>
            <if test="status != null">#{status},</if>
            <if test="isVoucherEntry != null">#{isVoucherEntry},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
</mapper>