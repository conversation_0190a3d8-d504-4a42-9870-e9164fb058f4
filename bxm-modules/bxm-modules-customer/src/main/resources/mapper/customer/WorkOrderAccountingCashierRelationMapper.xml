<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.WorkOrderAccountingCashierRelationMapper">
    
    <resultMap type="com.bxm.customer.domain.WorkOrderAccountingCashierRelation" id="WorkOrderAccountingCashierRelationResult">
        <result property="id"    column="id"    />
        <result property="workOrderId"    column="work_order_id"    />
        <result property="cashierAccountingId"    column="cashier_accounting_id"    />
        <result property="cashierAccountingDeliverStatus"    column="cashier_accounting_deliver_status"    />
        <result property="cashierAccountingDeliverResult"    column="cashier_accounting_deliver_result"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWorkOrderAccountingCashierRelationVo">
        select id, work_order_id, cashier_accounting_id, cashier_accounting_deliver_status, cashier_accounting_deliver_result, is_del, create_by, create_time, update_by, update_time from c_work_order_accounting_cashier_relation
    </sql>

    <select id="selectWorkOrderAccountingCashierRelationList" parameterType="com.bxm.customer.domain.WorkOrderAccountingCashierRelation" resultMap="WorkOrderAccountingCashierRelationResult">
        <include refid="selectWorkOrderAccountingCashierRelationVo"/>
        <where>  
            <if test="workOrderId != null "> and work_order_id = #{workOrderId}</if>
            <if test="cashierAccountingId != null "> and cashier_accounting_id = #{cashierAccountingId}</if>
            <if test="cashierAccountingDeliverStatus != null "> and cashier_accounting_deliver_status = #{cashierAccountingDeliverStatus}</if>
            <if test="cashierAccountingDeliverResult != null "> and cashier_accounting_deliver_result = #{cashierAccountingDeliverResult}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectWorkOrderAccountingCashierRelationById" parameterType="Long" resultMap="WorkOrderAccountingCashierRelationResult">
        <include refid="selectWorkOrderAccountingCashierRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWorkOrderAccountingCashierRelation" parameterType="com.bxm.customer.domain.WorkOrderAccountingCashierRelation" useGeneratedKeys="true" keyProperty="id">
        insert into c_work_order_accounting_cashier_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id,</if>
            <if test="cashierAccountingId != null">cashier_accounting_id,</if>
            <if test="cashierAccountingDeliverStatus != null">cashier_accounting_deliver_status,</if>
            <if test="cashierAccountingDeliverResult != null">cashier_accounting_deliver_result,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderId != null">#{workOrderId},</if>
            <if test="cashierAccountingId != null">#{cashierAccountingId},</if>
            <if test="cashierAccountingDeliverStatus != null">#{cashierAccountingDeliverStatus},</if>
            <if test="cashierAccountingDeliverResult != null">#{cashierAccountingDeliverResult},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWorkOrderAccountingCashierRelation" parameterType="com.bxm.customer.domain.WorkOrderAccountingCashierRelation">
        update c_work_order_accounting_cashier_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderId != null">work_order_id = #{workOrderId},</if>
            <if test="cashierAccountingId != null">cashier_accounting_id = #{cashierAccountingId},</if>
            <if test="cashierAccountingDeliverStatus != null">cashier_accounting_deliver_status = #{cashierAccountingDeliverStatus},</if>
            <if test="cashierAccountingDeliverResult != null">cashier_accounting_deliver_result = #{cashierAccountingDeliverResult},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkOrderAccountingCashierRelationById" parameterType="Long">
        delete from c_work_order_accounting_cashier_relation where id = #{id}
    </delete>

    <delete id="deleteWorkOrderAccountingCashierRelationByIds" parameterType="String">
        delete from c_work_order_accounting_cashier_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>