<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerInsuranceFundStatusMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerInsuranceFundStatus" id="NewCustomerInsuranceFundStatusResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="month"    column="month"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerInsuranceFundStatusVo">
        select id, customer_id, month, status, create_by, create_time, update_by, update_time from c_new_customer_insurance_fund_status
    </sql>

    <select id="selectNewCustomerInsuranceFundStatusList" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundStatus" resultMap="NewCustomerInsuranceFundStatusResult">
        <include refid="selectNewCustomerInsuranceFundStatusVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="month != null "> and month = #{month}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerInsuranceFundStatusById" parameterType="Long" resultMap="NewCustomerInsuranceFundStatusResult">
        <include refid="selectNewCustomerInsuranceFundStatusVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerInsuranceFundStatus" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundStatus" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_insurance_fund_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="month != null">month,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="month != null">#{month},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerInsuranceFundStatus" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundStatus">
        update c_new_customer_insurance_fund_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="month != null">month = #{month},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerInsuranceFundStatusById" parameterType="Long">
        delete from c_new_customer_insurance_fund_status where id = #{id}
    </delete>

    <delete id="deleteNewCustomerInsuranceFundStatusByIds" parameterType="String">
        delete from c_new_customer_insurance_fund_status where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>