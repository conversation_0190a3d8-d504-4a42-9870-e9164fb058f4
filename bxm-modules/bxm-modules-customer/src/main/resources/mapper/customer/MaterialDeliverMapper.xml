<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliver" id="MaterialDeliverResult">
        <result property="id"    column="id"    />
        <result property="materialDeliverNumber"    column="material_deliver_number"    />
        <result property="materialDeliverType"    column="material_deliver_type"    />
        <result property="analysisStatus"    column="analysis_status"    />
        <result property="analysisResult"    column="analysis_result"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="commitDeptId"    column="commit_dept_id"    />
        <result property="commitUserId"    column="commit_user_id"    />
        <result property="commitUserNickName"    column="commit_user_nick_name"    />
        <result property="lastOperType"    column="last_oper_type"    />
        <result property="lastOperName"    column="last_oper_name"    />
        <result property="lastOperTime"    column="last_oper_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverVo">
        select id, material_deliver_number, material_deliver_type, analysis_status, analysis_result, push_status, commit_dept_id, commit_user_id, commit_user_nick_name, last_oper_type, last_oper_name, last_oper_time, create_by, create_time, update_by, update_time from c_material_deliver
    </sql>

    <select id="selectMaterialDeliverList" parameterType="com.bxm.customer.domain.MaterialDeliver" resultMap="MaterialDeliverResult">
        <include refid="selectMaterialDeliverVo"/>
        <where>  
            <if test="materialDeliverNumber != null  and materialDeliverNumber != ''"> and material_deliver_number = #{materialDeliverNumber}</if>
            <if test="materialDeliverType != null "> and material_deliver_type = #{materialDeliverType}</if>
            <if test="analysisStatus != null "> and analysis_status = #{analysisStatus}</if>
            <if test="analysisResult != null "> and analysis_result = #{analysisResult}</if>
            <if test="pushStatus != null "> and push_status = #{pushStatus}</if>
            <if test="commitDeptId != null "> and commit_dept_id = #{commitDeptId}</if>
            <if test="commitUserId != null "> and commit_user_id = #{commitUserId}</if>
            <if test="commitUserNickName != null  and commitUserNickName != ''"> and commit_user_nick_name like concat('%', #{commitUserNickName}, '%')</if>
            <if test="lastOperType != null  and lastOperType != ''"> and last_oper_type = #{lastOperType}</if>
            <if test="lastOperName != null  and lastOperName != ''"> and last_oper_name like concat('%', #{lastOperName}, '%')</if>
            <if test="lastOperTime != null "> and last_oper_time = #{lastOperTime}</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverById" parameterType="Long" resultMap="MaterialDeliverResult">
        <include refid="selectMaterialDeliverVo"/>
        where id = #{id}
    </select>
    <select id="materialDeliverList"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverDTO">
        SELECT
        c_material_deliver.id AS id,
        c_material_deliver.title as title,
        c_material_deliver.material_deliver_number AS materialDeliverNumber,
        c_material_deliver.material_deliver_type AS materialDeliverType,
        c_material_deliver.analysis_status AS materialDeliverAnalysisStatus,
        c_material_deliver.analysis_result AS materialDeliverAnalysisResult,
        c_material_deliver.push_status AS materialDeliverPushStatus,
        c_material_deliver.commit_dept_id AS commitDeptId,
        c_material_deliver.commit_user_nick_name AS commitUserNickName,
        c_material_deliver.create_time AS createTime,
        c_material_deliver.last_oper_name AS lastOperName,
        c_material_deliver.last_oper_type AS lastOperType,
        c_material_deliver.last_oper_time AS lastOperTime
        FROM c_material_deliver
        <where>
            c_material_deliver.is_del = 0
            <if test="userDept.isAdmin == false and userDept.deptIds != null and userDept.deptIds.size > 0">
                and c_material_deliver.commit_dept_id in
                <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.title != null and vo.title != ''">
                and c_material_deliver.title like concat('%', #{vo.title}, '%')
            </if>
            <if test="vo.materialDeliverNumber != null and vo.materialDeliverNumber != ''">
                and c_material_deliver.material_deliver_number = #{vo.materialDeliverNumber}
            </if>
            <if test="vo.materialDeliverType != null and vo.materialDeliverType != ''">
                and c_material_deliver.material_deliver_type in (${vo.materialDeliverType})
            </if>
            <if test="vo.materialDeliverAnalysisStatus != null and vo.materialDeliverAnalysisStatus != ''">
                and c_material_deliver.analysis_status in (${vo.materialDeliverAnalysisStatus})
            </if>
            <if test="vo.materialDeliverAnalysisResult != null and vo.materialDeliverAnalysisResult != ''">
                and c_material_deliver.analysis_result in (${vo.materialDeliverAnalysisResult})
            </if>
            <if test="vo.materialDeliverPushStatus != null and vo.materialDeliverPushStatus != ''">
                and c_material_deliver.push_status in (${vo.materialDeliverPushStatus})
            </if>
            <if test="vo.commitDeptId != null">
                and c_material_deliver.commit_dept_id = #{vo.commitDeptId}
            </if>
            <if test="vo.createTimeStart != null and vo.createTimeStart != ''">
                and c_material_deliver.create_time &gt;= #{vo.createTimeStart}
            </if>
            <if test="vo.createTimeEnd != null and vo.createTimeEnd != ''">
                and c_material_deliver.create_time &lt;= #{vo.createTimeEnd}
            </if>
            <if test="vo.lastOperName != null and vo.lastOperName != ''">
                and c_material_deliver.last_oper_name like concat('%', #{vo.lastOperName}, '%')
            </if>
            <if test="vo.lastOperTimeStart != null and vo.lastOperTimeStart != ''">
                and c_material_deliver.last_oper_time &gt;= #{vo.lastOperTimeStart}
            </if>
            <if test="vo.lastOperTimeEnd != null and vo.lastOperTimeEnd != ''">
                and c_material_deliver.last_oper_time &lt;= #{vo.lastOperTimeEnd}
            </if>
        </where>
        order by c_material_deliver.id desc
    </select>
    <select id="materialDeliverStatistic"
            resultType="com.bxm.customer.domain.dto.workBench.MaterialDeliverStatisticDTO">
        SELECT
        count(case when c_material_deliver.analysis_status = 3 then 1 end) AS analysisFailCount,
        count(case when c_material_deliver.analysis_status = 2 and c_material_deliver.analysis_result = 2 then 1 end) AS analysisExceptionCount,
        count(case when c_material_deliver.analysis_status = 4 then 1 end) AS analysisStopCount,
        count(case when c_material_deliver.analysis_status = 2 and c_material_deliver.analysis_result = 1 and c_material_deliver.push_status = 1 then 1 end) AS waitPushCount
        FROM c_material_deliver
        <where>
            is_del = 0
            <if test="userDept.isAdmin == false and userDept.deptIds != null and userDept.deptIds.size > 0">
                and commit_dept_id in
                <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="materialCommitDeptCountList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        c_material_deliver.commit_dept_id as deptId,
        count(1) as dataCount
        FROM c_material_deliver
        <where>
            c_material_deliver.is_del = 0
            <if test="userDept.isAdmin == false and userDept.deptIds != null and userDept.deptIds.size > 0">
                and c_material_deliver.commit_dept_id in
                <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.materialDeliverType != null and vo.materialDeliverType != ''">
                and c_material_deliver.material_deliver_type in ${vo.materialDeliverType}
            </if>
            <if test="vo.materialDeliverAnalysisStatus != null and vo.materialDeliverAnalysisStatus != ''">
                and c_material_deliver.analysis_status in ${vo.materialDeliverAnalysisStatus}
            </if>
            <if test="vo.materialDeliverAnalysisResult != null and vo.materialDeliverAnalysisResult != ''">
                and c_material_deliver.analysis_result in ${vo.materialDeliverAnalysisResult}
            </if>
            <if test="vo.materialDeliverPushStatus != null and vo.materialDeliverPushStatus != ''">
                and c_material_deliver.push_status in ${vo.materialDeliverPushStatus}
            </if>
        </where>
        group by c_material_deliver.commit_dept_id
    </select>
    <select id="materialDeliverInventoryDetailList"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverInventoryDetailDTO">
        select
            id,
            file_name as fileName,
            file_url as fileUrl,
            file_size as fileSize,
            analysis_result as analysisResult,
            error_msg as errorMsg,
            repeat_id as repeatId,
            customer_name as customerName,
            customer_service_id as customerServiceId,
            bank_name as bankName,
            bank_account_number as bankAccountNumber,
            remark as fileRemark,
            start_date as startDate,
            end_date as endDate,
            file_number as fileNumber,
            period
            from c_material_deliver_file_inventory
        where is_del = 0
        and material_deliver_id = #{vo.materialDeliverId}
        <if test="vo.analysisResult != null">
            and analysis_result = #{vo.analysisResult}
        </if>
        <if test="vo.errorMsgList != null and vo.errorMsgList.size > 0">
            AND (
            <foreach collection="vo.errorMsgList" item="errorMsg" separator=" OR ">
                error_msg LIKE CONCAT('%', #{errorMsg}, '%')
            </foreach>
            )
        </if>
        <if test="vo.isRepeat != null">
            <if test="vo.isRepeat == 0">
                and (#{pushStatus} = 2 or not exists (
                    select 1 from c_customer_service_cashier_accounting_file
                             join c_customer_service_cashier_accounting on c_customer_service_cashier_accounting_file.customer_service_cashier_accounting_id = c_customer_service_cashier_accounting.id
                             and c_customer_service_cashier_accounting.is_del = 0
                             where c_customer_service_cashier_accounting_file.is_del = 0
                             and c_customer_service_cashier_accounting_file.file_type = 1
                             and c_customer_service_cashier_accounting.customer_service_id = c_material_deliver_file_inventory.customer_service_id
                             and c_customer_service_cashier_accounting_file.file_name = c_material_deliver_file_inventory.file_name
                ))
            </if>
            <if test="vo.isRepeat == 1">
                and #{pushStatus} != 2 and exists (
                select 1 from c_customer_service_cashier_accounting_file
                join c_customer_service_cashier_accounting on c_customer_service_cashier_accounting_file.customer_service_cashier_accounting_id = c_customer_service_cashier_accounting.id
                and c_customer_service_cashier_accounting.is_del = 0
                where c_customer_service_cashier_accounting_file.is_del = 0
                and c_customer_service_cashier_accounting_file.file_type = 1
                and c_customer_service_cashier_accounting.customer_service_id = c_material_deliver_file_inventory.customer_service_id
                and c_customer_service_cashier_accounting_file.file_name = c_material_deliver_file_inventory.file_name
                )
            </if>
        </if>
        <if test="vo.isCustomerNameNotSame != null">
            <if test="vo.isCustomerNameNotSame == 0">
                and (customer_service_id is null or exists (
                select 1 from c_customer_service
                where is_del = 0 and id = c_material_deliver_file_inventory.customer_service_id and customer_name = c_material_deliver_file_inventory.customer_name
                ))
            </if>
            <if test="vo.isCustomerNameNotSame == 1">
                and customer_service_id is not null and not exists (
                select 1 from c_customer_service
                where is_del = 0 and id = c_material_deliver_file_inventory.customer_service_id and customer_name = c_material_deliver_file_inventory.customer_name
                )
            </if>
        </if>
        <if test="type == 1">
            order by customer_name asc, bank_account_number asc, start_date asc, id asc
        </if>
        <if test="type != 1">
            order by customer_name asc, period asc, id asc
        </if>
    </select>
    <select id="materialDeliverInventoryDetailListByIds"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverInventoryDetailDTO">
        select
        id,
        file_name as fileName,
        file_url as fileUrl,
        file_size as fileSize,
        analysis_result as analysisResult,
        error_msg as errorMsg,
        repeat_id as repeatId,
        customer_name as customerName,
        customer_service_id as customerServiceId,
        bank_name as bankName,
        bank_account_number as bankAccountNumber,
        remark as fileRemark,
        start_date as startDate,
        end_date as endDate,
        file_number as fileNumber,
        period
        from c_material_deliver_file_inventory
        where is_del = 0
        and id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="materialDeliverInventoryDetailListV2"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryV2DTO">
        select
        id,
        analysis_result as analysisResult,
        error_msg as errorMsg,
        customer_name as customerName,
        customer_service_id as customerServiceId,
        bank_name as bankName,
        bank_account_number as bankAccountNumber,
        date_format(start_date, '%Y%m') as startDate,
        date_format(end_date, '%Y%m') as endDate,
        period,
        push_result as pushResult
        from c_material_deliver_file_inventory
        where is_del = 0
        and material_deliver_id = #{vo.materialDeliverId}
        <if test="vo.analysisResult != null">
            and analysis_result = #{vo.analysisResult}
        </if>
        <if test="vo.errorMsgList != null and vo.errorMsgList.size > 0">
            AND (
            <foreach collection="vo.errorMsgList" item="errorMsg" separator=" OR ">
                error_msg LIKE CONCAT('%', #{errorMsg}, '%')
            </foreach>
            )
        </if>
        <if test="vo.isCustomerNameNotSame != null">
            <if test="vo.isCustomerNameNotSame == 0">
                and (customer_service_id is null or exists (
                select 1 from c_customer_service
                where is_del = 0 and id = c_material_deliver_file_inventory.customer_service_id and customer_name = c_material_deliver_file_inventory.customer_name
                ))
            </if>
            <if test="vo.isCustomerNameNotSame == 1">
                and customer_service_id is not null and not exists (
                select 1 from c_customer_service
                where is_del = 0 and id = c_material_deliver_file_inventory.customer_service_id and customer_name = c_material_deliver_file_inventory.customer_name
                )
            </if>
        </if>
        <if test="type == 1">
            order by customer_name asc, bank_account_number asc, start_date asc, id asc
        </if>
        <if test="type != 1">
            order by customer_name asc, period asc, id asc
        </if>
    </select>
    <select id="materialDeliverInventoryDetailListByIdsV2"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryV2DTO">
        select
        id,
        analysis_result as analysisResult,
        error_msg as errorMsg,
        customer_name as customerName,
        customer_service_id as customerServiceId,
        bank_name as bankName,
        bank_account_number as bankAccountNumber,
        date_format(start_date, '%Y%m') as startDate,
        date_format(end_date, '%Y%m') as endDate,
        period,
        push_result as pushResult
        from c_material_deliver_file_inventory
        where is_del = 0
        and id in
        <foreach collection="ids" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>

    <insert id="insertMaterialDeliver" parameterType="com.bxm.customer.domain.MaterialDeliver" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialDeliverNumber != null and materialDeliverNumber != ''">material_deliver_number,</if>
            <if test="materialDeliverType != null">material_deliver_type,</if>
            <if test="analysisStatus != null">analysis_status,</if>
            <if test="analysisResult != null">analysis_result,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="commitDeptId != null">commit_dept_id,</if>
            <if test="commitUserId != null">commit_user_id,</if>
            <if test="commitUserNickName != null">commit_user_nick_name,</if>
            <if test="lastOperType != null">last_oper_type,</if>
            <if test="lastOperName != null">last_oper_name,</if>
            <if test="lastOperTime != null">last_oper_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialDeliverNumber != null and materialDeliverNumber != ''">#{materialDeliverNumber},</if>
            <if test="materialDeliverType != null">#{materialDeliverType},</if>
            <if test="analysisStatus != null">#{analysisStatus},</if>
            <if test="analysisResult != null">#{analysisResult},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="commitDeptId != null">#{commitDeptId},</if>
            <if test="commitUserId != null">#{commitUserId},</if>
            <if test="commitUserNickName != null">#{commitUserNickName},</if>
            <if test="lastOperType != null">#{lastOperType},</if>
            <if test="lastOperName != null">#{lastOperName},</if>
            <if test="lastOperTime != null">#{lastOperTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliver" parameterType="com.bxm.customer.domain.MaterialDeliver">
        update c_material_deliver
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialDeliverNumber != null and materialDeliverNumber != ''">material_deliver_number = #{materialDeliverNumber},</if>
            <if test="materialDeliverType != null">material_deliver_type = #{materialDeliverType},</if>
            <if test="analysisStatus != null">analysis_status = #{analysisStatus},</if>
            <if test="analysisResult != null">analysis_result = #{analysisResult},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="commitDeptId != null">commit_dept_id = #{commitDeptId},</if>
            <if test="commitUserId != null">commit_user_id = #{commitUserId},</if>
            <if test="commitUserNickName != null">commit_user_nick_name = #{commitUserNickName},</if>
            <if test="lastOperType != null">last_oper_type = #{lastOperType},</if>
            <if test="lastOperName != null">last_oper_name = #{lastOperName},</if>
            <if test="lastOperTime != null">last_oper_time = #{lastOperTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverById" parameterType="Long">
        delete from c_material_deliver where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverByIds" parameterType="String">
        delete from c_material_deliver where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>