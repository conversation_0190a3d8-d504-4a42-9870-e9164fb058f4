<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerSysAccountMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerSysAccount" id="NewCustomerSysAccountResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="sysType"    column="sys_type"    />
        <result property="sysTypeName"    column="sys_type_name"    />
        <result property="account"    column="account"    />
        <result property="password"    column="password"    />
        <result property="loginType"    column="login_type"    />
        <result property="contact"    column="contact"    />
        <result property="contactMobile"    column="contact_mobile"    />
        <result property="isSameWithCreditCode"    column="is_same_with_credit_code"    />
        <result property="idNumber"    column="id_number"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerSysAccountVo">
        select id, customer_id, sys_type, sys_type_name, account, password, login_type, contact, contact_mobile, is_same_with_credit_code, id_number, remark, is_del, create_by, create_time, update_by, update_time from c_new_customer_sys_account
    </sql>

    <select id="selectNewCustomerSysAccountList" parameterType="com.bxm.customer.domain.NewCustomerSysAccount" resultMap="NewCustomerSysAccountResult">
        <include refid="selectNewCustomerSysAccountVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="sysType != null "> and sys_type = #{sysType}</if>
            <if test="sysTypeName != null  and sysTypeName != ''"> and sys_type_name like concat('%', #{sysTypeName}, '%')</if>
            <if test="account != null  and account != ''"> and account = #{account}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="loginType != null  and loginType != ''"> and login_type = #{loginType}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="contactMobile != null  and contactMobile != ''"> and contact_mobile = #{contactMobile}</if>
            <if test="isSameWithCreditCode != null "> and is_same_with_credit_code = #{isSameWithCreditCode}</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerSysAccountById" parameterType="Long" resultMap="NewCustomerSysAccountResult">
        <include refid="selectNewCustomerSysAccountVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerSysAccount" parameterType="com.bxm.customer.domain.NewCustomerSysAccount" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_sys_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="sysType != null">sys_type,</if>
            <if test="sysTypeName != null">sys_type_name,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="loginType != null">login_type,</if>
            <if test="contact != null">contact,</if>
            <if test="contactMobile != null">contact_mobile,</if>
            <if test="isSameWithCreditCode != null">is_same_with_credit_code,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="sysType != null">#{sysType},</if>
            <if test="sysTypeName != null">#{sysTypeName},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="loginType != null">#{loginType},</if>
            <if test="contact != null">#{contact},</if>
            <if test="contactMobile != null">#{contactMobile},</if>
            <if test="isSameWithCreditCode != null">#{isSameWithCreditCode},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerSysAccount" parameterType="com.bxm.customer.domain.NewCustomerSysAccount">
        update c_new_customer_sys_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="sysType != null">sys_type = #{sysType},</if>
            <if test="sysTypeName != null">sys_type_name = #{sysTypeName},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="loginType != null">login_type = #{loginType},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="contactMobile != null">contact_mobile = #{contactMobile},</if>
            <if test="isSameWithCreditCode != null">is_same_with_credit_code = #{isSameWithCreditCode},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerSysAccountById" parameterType="Long">
        delete from c_new_customer_sys_account where id = #{id}
    </delete>

    <delete id="deleteNewCustomerSysAccountByIds" parameterType="String">
        delete from c_new_customer_sys_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>