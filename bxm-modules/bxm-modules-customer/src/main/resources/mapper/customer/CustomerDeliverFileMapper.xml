<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerDeliverFileMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerDeliverFile" id="CustomerDeliverFileResult">
        <result property="id"    column="id"    />
        <result property="customerDeliverId"    column="customer_deliver_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerDeliverFileVo">
        select id, customer_deliver_id, file_url, file_type, create_by, create_time, update_by, update_time from c_customer_deliver_file
    </sql>

    <select id="selectCustomerDeliverFileList" parameterType="com.bxm.customer.domain.CustomerDeliverFile" resultMap="CustomerDeliverFileResult">
        <include refid="selectCustomerDeliverFileVo"/>
        <where>  
            <if test="customerDeliverId != null "> and customer_deliver_id = #{customerDeliverId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
        </where>
    </select>
    
    <select id="selectCustomerDeliverFileById" parameterType="Long" resultMap="CustomerDeliverFileResult">
        <include refid="selectCustomerDeliverFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerDeliverFile" parameterType="com.bxm.customer.domain.CustomerDeliverFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_deliver_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerDeliverId != null">customer_deliver_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileType != null">file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerDeliverId != null">#{customerDeliverId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerDeliverFile" parameterType="com.bxm.customer.domain.CustomerDeliverFile">
        update c_customer_deliver_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerDeliverId != null">customer_deliver_id = #{customerDeliverId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerDeliverFileById" parameterType="Long">
        delete from c_customer_deliver_file where id = #{id}
    </delete>

    <delete id="deleteCustomerDeliverFileByIds" parameterType="String">
        delete from c_customer_deliver_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>