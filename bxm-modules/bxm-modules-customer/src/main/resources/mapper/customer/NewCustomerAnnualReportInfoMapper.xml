<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerAnnualReportInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerAnnualReportInfo" id="NewCustomerAnnualReportInfoResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="status"    column="status"    />
        <result property="eBusinessLicense"    column="e_business_license"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactIdNumber"    column="contact_id_number"    />
        <result property="notes"    column="notes"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerAnnualReportInfoVo">
        select id, customer_id, status, e_business_license, contact_name, contact_id_number, notes, create_by, create_time, update_by, update_time from c_new_customer_annual_report_info
    </sql>

    <select id="selectNewCustomerAnnualReportInfoList" parameterType="com.bxm.customer.domain.NewCustomerAnnualReportInfo" resultMap="NewCustomerAnnualReportInfoResult">
        <include refid="selectNewCustomerAnnualReportInfoVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="eBusinessLicense != null "> and e_business_license = #{eBusinessLicense}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactIdNumber != null  and contactIdNumber != ''"> and contact_id_number = #{contactIdNumber}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerAnnualReportInfoById" parameterType="Long" resultMap="NewCustomerAnnualReportInfoResult">
        <include refid="selectNewCustomerAnnualReportInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerAnnualReportInfo" parameterType="com.bxm.customer.domain.NewCustomerAnnualReportInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_annual_report_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="status != null">status,</if>
            <if test="eBusinessLicense != null">e_business_license,</if>
            <if test="contactName != null">contact_name,</if>
            <if test="contactIdNumber != null">contact_id_number,</if>
            <if test="notes != null">notes,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="status != null">#{status},</if>
            <if test="eBusinessLicense != null">#{eBusinessLicense},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactIdNumber != null">#{contactIdNumber},</if>
            <if test="notes != null">#{notes},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerAnnualReportInfo" parameterType="com.bxm.customer.domain.NewCustomerAnnualReportInfo">
        update c_new_customer_annual_report_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="eBusinessLicense != null">e_business_license = #{eBusinessLicense},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="contactIdNumber != null">contact_id_number = #{contactIdNumber},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerAnnualReportInfoById" parameterType="Long">
        delete from c_new_customer_annual_report_info where id = #{id}
    </delete>

    <delete id="deleteNewCustomerAnnualReportInfoByIds" parameterType="String">
        delete from c_new_customer_annual_report_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>