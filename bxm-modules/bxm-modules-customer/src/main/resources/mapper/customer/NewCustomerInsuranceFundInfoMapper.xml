<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerInsuranceFundInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerInsuranceFundInfo" id="NewCustomerInsuranceFundInfoResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="socialSecurityPeople"    column="social_security_people"    />
        <result property="socialSecurityBase"    column="social_security_base"    />
        <result property="totalContribution"    column="total_contribution"    />
        <result property="injuryFee"    column="injury_fee"    />
        <result property="injuryRate"    column="injury_rate"    />
        <result property="medicalPeople"    column="medical_people"    />
        <result property="medicalBase"    column="medical_base"    />
        <result property="medicalFee"    column="medical_fee"    />
        <result property="medicalRate"    column="medical_rate"    />
        <result property="fundPeople"    column="fund_people"    />
        <result property="fundBase"    column="fund_base"    />
        <result property="fundFee"    column="fund_fee"    />
        <result property="fundRate"    column="fund_rate"    />
        <result property="socialSecurityContact"    column="social_security_contact"    />
        <result property="medicalContact"    column="medical_contact"    />
        <result property="fundContact"    column="fund_contact"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerInsuranceFundInfoVo">
        select id, customer_id, social_security_people, social_security_base, total_contribution, injury_fee, injury_rate, medical_people, medical_base, medical_fee, medical_rate, fund_people, fund_base, fund_fee, fund_rate, social_security_contact, medical_contact, fund_contact, create_by, create_time, update_by, update_time from c_new_customer_insurance_fund_info
    </sql>

    <select id="selectNewCustomerInsuranceFundInfoList" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundInfo" resultMap="NewCustomerInsuranceFundInfoResult">
        <include refid="selectNewCustomerInsuranceFundInfoVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="socialSecurityPeople != null "> and social_security_people = #{socialSecurityPeople}</if>
            <if test="socialSecurityBase != null "> and social_security_base = #{socialSecurityBase}</if>
            <if test="totalContribution != null "> and total_contribution = #{totalContribution}</if>
            <if test="injuryFee != null "> and injury_fee = #{injuryFee}</if>
            <if test="injuryRate != null "> and injury_rate = #{injuryRate}</if>
            <if test="medicalPeople != null "> and medical_people = #{medicalPeople}</if>
            <if test="medicalBase != null "> and medical_base = #{medicalBase}</if>
            <if test="medicalFee != null "> and medical_fee = #{medicalFee}</if>
            <if test="medicalRate != null "> and medical_rate = #{medicalRate}</if>
            <if test="fundPeople != null "> and fund_people = #{fundPeople}</if>
            <if test="fundBase != null "> and fund_base = #{fundBase}</if>
            <if test="fundFee != null "> and fund_fee = #{fundFee}</if>
            <if test="fundRate != null "> and fund_rate = #{fundRate}</if>
            <if test="socialSecurityContact != null  and socialSecurityContact != ''"> and social_security_contact = #{socialSecurityContact}</if>
            <if test="medicalContact != null  and medicalContact != ''"> and medical_contact = #{medicalContact}</if>
            <if test="fundContact != null  and fundContact != ''"> and fund_contact = #{fundContact}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerInsuranceFundInfoById" parameterType="Long" resultMap="NewCustomerInsuranceFundInfoResult">
        <include refid="selectNewCustomerInsuranceFundInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerInsuranceFundInfo" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_insurance_fund_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="socialSecurityPeople != null">social_security_people,</if>
            <if test="socialSecurityBase != null">social_security_base,</if>
            <if test="totalContribution != null">total_contribution,</if>
            <if test="injuryFee != null">injury_fee,</if>
            <if test="injuryRate != null">injury_rate,</if>
            <if test="medicalPeople != null">medical_people,</if>
            <if test="medicalBase != null">medical_base,</if>
            <if test="medicalFee != null">medical_fee,</if>
            <if test="medicalRate != null">medical_rate,</if>
            <if test="fundPeople != null">fund_people,</if>
            <if test="fundBase != null">fund_base,</if>
            <if test="fundFee != null">fund_fee,</if>
            <if test="fundRate != null">fund_rate,</if>
            <if test="socialSecurityContact != null">social_security_contact,</if>
            <if test="medicalContact != null">medical_contact,</if>
            <if test="fundContact != null">fund_contact,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="socialSecurityPeople != null">#{socialSecurityPeople},</if>
            <if test="socialSecurityBase != null">#{socialSecurityBase},</if>
            <if test="totalContribution != null">#{totalContribution},</if>
            <if test="injuryFee != null">#{injuryFee},</if>
            <if test="injuryRate != null">#{injuryRate},</if>
            <if test="medicalPeople != null">#{medicalPeople},</if>
            <if test="medicalBase != null">#{medicalBase},</if>
            <if test="medicalFee != null">#{medicalFee},</if>
            <if test="medicalRate != null">#{medicalRate},</if>
            <if test="fundPeople != null">#{fundPeople},</if>
            <if test="fundBase != null">#{fundBase},</if>
            <if test="fundFee != null">#{fundFee},</if>
            <if test="fundRate != null">#{fundRate},</if>
            <if test="socialSecurityContact != null">#{socialSecurityContact},</if>
            <if test="medicalContact != null">#{medicalContact},</if>
            <if test="fundContact != null">#{fundContact},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerInsuranceFundInfo" parameterType="com.bxm.customer.domain.NewCustomerInsuranceFundInfo">
        update c_new_customer_insurance_fund_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="socialSecurityPeople != null">social_security_people = #{socialSecurityPeople},</if>
            <if test="socialSecurityBase != null">social_security_base = #{socialSecurityBase},</if>
            <if test="totalContribution != null">total_contribution = #{totalContribution},</if>
            <if test="injuryFee != null">injury_fee = #{injuryFee},</if>
            <if test="injuryRate != null">injury_rate = #{injuryRate},</if>
            <if test="medicalPeople != null">medical_people = #{medicalPeople},</if>
            <if test="medicalBase != null">medical_base = #{medicalBase},</if>
            <if test="medicalFee != null">medical_fee = #{medicalFee},</if>
            <if test="medicalRate != null">medical_rate = #{medicalRate},</if>
            <if test="fundPeople != null">fund_people = #{fundPeople},</if>
            <if test="fundBase != null">fund_base = #{fundBase},</if>
            <if test="fundFee != null">fund_fee = #{fundFee},</if>
            <if test="fundRate != null">fund_rate = #{fundRate},</if>
            <if test="socialSecurityContact != null">social_security_contact = #{socialSecurityContact},</if>
            <if test="medicalContact != null">medical_contact = #{medicalContact},</if>
            <if test="fundContact != null">fund_contact = #{fundContact},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerInsuranceFundInfoById" parameterType="Long">
        delete from c_new_customer_insurance_fund_info where id = #{id}
    </delete>

    <delete id="deleteNewCustomerInsuranceFundInfoByIds" parameterType="String">
        delete from c_new_customer_insurance_fund_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>