<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceDocHandoverTaxInstrumentMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument" id="CustomerServiceDocHandoverTaxInstrumentResult">
        <result property="id"    column="id"    />
        <result property="customerServiceDocId"    column="customer_service_doc_id"    />
        <result property="bizType"    column="biz_type"    />
        <result property="name"    column="name"    />
        <result property="rowNum"    column="row_num"    />
        <result property="paperCount"    column="paper_count"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceDocHandoverTaxInstrumentVo">
        select id, customer_service_doc_id, biz_type, name, row_num, paper_count, remark, create_by, create_time, update_by, update_time from c_customer_service_doc_handover_tax_instrument
    </sql>

    <select id="selectCustomerServiceDocHandoverTaxInstrumentList" parameterType="com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument" resultMap="CustomerServiceDocHandoverTaxInstrumentResult">
        <include refid="selectCustomerServiceDocHandoverTaxInstrumentVo"/>
        <where>  
            <if test="customerServiceDocId != null "> and customer_service_doc_id = #{customerServiceDocId}</if>
            <if test="bizType != null "> and biz_type = #{bizType}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="rowNum != null "> and row_num = #{rowNum}</if>
            <if test="paperCount != null "> and paper_count = #{paperCount}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceDocHandoverTaxInstrumentById" parameterType="Long" resultMap="CustomerServiceDocHandoverTaxInstrumentResult">
        <include refid="selectCustomerServiceDocHandoverTaxInstrumentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceDocHandoverTaxInstrument" parameterType="com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_doc_handover_tax_instrument
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceDocId != null">customer_service_doc_id,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="rowNum != null">row_num,</if>
            <if test="paperCount != null">paper_count,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceDocId != null">#{customerServiceDocId},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="rowNum != null">#{rowNum},</if>
            <if test="paperCount != null">#{paperCount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceDocHandoverTaxInstrument" parameterType="com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument">
        update c_customer_service_doc_handover_tax_instrument
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceDocId != null">customer_service_doc_id = #{customerServiceDocId},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="rowNum != null">row_num = #{rowNum},</if>
            <if test="paperCount != null">paper_count = #{paperCount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceDocHandoverTaxInstrumentById" parameterType="Long">
        delete from c_customer_service_doc_handover_tax_instrument where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceDocHandoverTaxInstrumentByIds" parameterType="String">
        delete from c_customer_service_doc_handover_tax_instrument where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>