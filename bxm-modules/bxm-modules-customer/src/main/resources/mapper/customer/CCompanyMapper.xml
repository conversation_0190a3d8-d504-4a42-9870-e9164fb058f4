<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CCompanyMapper">
    
    <resultMap type="com.bxm.customer.domain.CCompany" id="CCompanyResult">
        <result property="id"    column="id"    />
        <result property="thirdKeyNo"    column="third_key_no"    />
        <result property="companyName"    column="company_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="startDate"    column="start_date"    />
        <result property="operName"    column="oper_name"    />
        <result property="status"    column="status"    />
        <result property="registerNo"    column="register_no"    />
        <result property="registerAddress"    column="register_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCCompanyVo">
        select id, third_key_no, company_name, credit_code, start_date, oper_name, status, register_no, register_address, create_by, create_time, update_by, update_time from c_company
    </sql>

    <select id="selectCCompanyList" parameterType="com.bxm.customer.domain.CCompany" resultMap="CCompanyResult">
        <include refid="selectCCompanyVo"/>
        <where>  
            <if test="thirdKeyNo != null  and thirdKeyNo != ''"> and third_key_no = #{thirdKeyNo}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="startDate != null  and startDate != ''"> and start_date = #{startDate}</if>
            <if test="operName != null  and operName != ''"> and oper_name like concat('%', #{operName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="registerNo != null  and registerNo != ''"> and register_no = #{registerNo}</if>
            <if test="registerAddress != null  and registerAddress != ''"> and register_address = #{registerAddress}</if>
        </where>
    </select>
    
    <select id="selectCCompanyById" parameterType="Long" resultMap="CCompanyResult">
        <include refid="selectCCompanyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCCompany" parameterType="com.bxm.customer.domain.CCompany" useGeneratedKeys="true" keyProperty="id">
        insert into c_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdKeyNo != null">third_key_no,</if>
            <if test="companyName != null">company_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="startDate != null">start_date,</if>
            <if test="operName != null">oper_name,</if>
            <if test="status != null">status,</if>
            <if test="registerNo != null">register_no,</if>
            <if test="registerAddress != null">register_address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="thirdKeyNo != null">#{thirdKeyNo},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="operName != null">#{operName},</if>
            <if test="status != null">#{status},</if>
            <if test="registerNo != null">#{registerNo},</if>
            <if test="registerAddress != null">#{registerAddress},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCCompany" parameterType="com.bxm.customer.domain.CCompany">
        update c_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="thirdKeyNo != null">third_key_no = #{thirdKeyNo},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="registerNo != null">register_no = #{registerNo},</if>
            <if test="registerAddress != null">register_address = #{registerAddress},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCCompanyById" parameterType="Long">
        delete from c_company where id = #{id}
    </delete>

    <delete id="deleteCCompanyByIds" parameterType="String">
        delete from c_company where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>