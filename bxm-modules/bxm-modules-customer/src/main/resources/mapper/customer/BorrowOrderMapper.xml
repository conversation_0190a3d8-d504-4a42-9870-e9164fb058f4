<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BorrowOrderMapper">
    
    <resultMap type="com.bxm.customer.domain.BorrowOrder" id="BorrowOrderResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="employeeName"    column="employee_name"    />
        <result property="borrowAmount"    column="borrow_amount"    />
        <result property="returnSetting"    column="return_setting"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBorrowOrderVo">
        select id, customer_service_id, dept_id, dept_name, employee_id, employee_name, borrow_amount, return_setting, remark, status, submit_time, create_by, create_time, update_by, update_time from c_borrow_order
    </sql>

    <select id="selectBorrowOrderList" parameterType="com.bxm.customer.domain.BorrowOrder" resultMap="BorrowOrderResult">
        <include refid="selectBorrowOrderVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="employeeName != null  and employeeName != ''"> and employee_name like concat('%', #{employeeName}, '%')</if>
            <if test="borrowAmount != null "> and borrow_amount = #{borrowAmount}</if>
            <if test="returnSetting != null "> and return_setting = #{returnSetting}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
        </where>
    </select>
    
    <select id="selectBorrowOrderById" parameterType="Long" resultMap="BorrowOrderResult">
        <include refid="selectBorrowOrderVo"/>
        where id = #{id}
    </select>
    <select id="borrowOrderList" resultType="com.bxm.customer.domain.dto.borrow.BorrowOrderDTO">
        select
            cbo.id as id,
            cbo.customer_service_id as customerServiceId,
            ccs.customer_name as customerName,
            ccs.customer_company_name as customerCompanyName,
            cbo.dept_id as deptId,
            sd.dept_name as deptName,
            cbo.employee_id as employeeId,
            se.employee_name as employeeName,
            cbo.submit_time as submitTime,
            cbo.status as status,
            cbo.borrow_amount as borrowAmount,
            cbo.remark as remark
            from c_borrow_order cbo
            left join c_customer_service ccs on cbo.customer_service_id = ccs.id and ccs.is_del = 0
            left join sys_dept sd on cbo.dept_id = sd.dept_id and sd.del_flag = '0'
            left join sys_employee se on cbo.employee_id = se.employee_id
        <where>
             cbo.is_del = 0
            <if test="isHeadquarters != null and isHeadquarters == true">
                and cbo.status != 5
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (ccs.customer_name like concat('%', #{vo.keyWord}, '%') or ccs.customer_company_name like concat('%', #{vo.keyWord}, '%') or ccs.credit_code like concat('%', #{vo.keyWord}, '%'))
            </if>
            <if test="vo.tagName != null and vo.tagName != ''">
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 1 and vo.customerServiceIds != null and vo.customerServiceIds.size > 0">
                    and ccs.id in
                    <foreach collection="vo.customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="vo.tagIncludeFlag != null and vo.tagIncludeFlag == 0 and vo.customerServiceIds != null and vo.customerServiceIds.size > 0">
                    and ccs.id not in
                    <foreach collection="vo.customerServiceIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="vo.deptInfo != null and vo.deptInfo != ''">
                and (sd.dept_name like concat('%', #{vo.deptInfo}, '%') or se.employee_name like concat('%', #{vo.deptInfo}, '%'))
            </if>
            <if test="vo.submitDateMin != null and vo.submitDateMin != ''">
                and cbo.submit_time &gt;= #{vo.submitDateMin}
            </if>
            <if test="vo.submitDateMax != null and vo.submitDateMax != ''">
                and cbo.submit_time &lt;= #{vo.submitDateMax}
            </if>
            <if test="vo.status != null">
                and cbo.status = #{vo.status}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and cbo.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
        order by cbo.submit_time desc
    </select>

    <insert id="insertBorrowOrder" parameterType="com.bxm.customer.domain.BorrowOrder" useGeneratedKeys="true" keyProperty="id">
        insert into c_borrow_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="employeeName != null">employee_name,</if>
            <if test="borrowAmount != null">borrow_amount,</if>
            <if test="returnSetting != null">return_setting,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="employeeName != null">#{employeeName},</if>
            <if test="borrowAmount != null">#{borrowAmount},</if>
            <if test="returnSetting != null">#{returnSetting},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBorrowOrder" parameterType="com.bxm.customer.domain.BorrowOrder">
        update c_borrow_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="borrowAmount != null">borrow_amount = #{borrowAmount},</if>
            <if test="returnSetting != null">return_setting = #{returnSetting},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBorrowOrderById" parameterType="Long">
        delete from c_borrow_order where id = #{id}
    </delete>

    <delete id="deleteBorrowOrderByIds" parameterType="String">
        delete from c_borrow_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>