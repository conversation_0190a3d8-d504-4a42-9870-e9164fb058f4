<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CCustomerServicePeriodEmployeeMapper">
    
    <resultMap type="com.bxm.customer.domain.CCustomerServicePeriodEmployee" id="CCustomerServicePeriodEmployeeResult">
        <result property="id"    column="id"    />
        <result property="periodId"    column="period_id"    />
        <result property="periodEmployeeType"    column="period_employee_type"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="employeeName"    column="employee_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCCustomerServicePeriodEmployeeVo">
        select id, period_id, period_employee_type, employee_id, employee_name, create_by, create_time, update_by, update_time from c_customer_service_period_employee
    </sql>

    <select id="selectCCustomerServicePeriodEmployeeList" parameterType="com.bxm.customer.domain.CCustomerServicePeriodEmployee" resultMap="CCustomerServicePeriodEmployeeResult">
        <include refid="selectCCustomerServicePeriodEmployeeVo"/>
        <where>  
            <if test="periodId != null "> and period_id = #{periodId}</if>
            <if test="periodEmployeeType != null "> and period_employee_type = #{periodEmployeeType}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="employeeName != null  and employeeName != ''"> and employee_name like concat('%', #{employeeName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCCustomerServicePeriodEmployeeById" parameterType="Long" resultMap="CCustomerServicePeriodEmployeeResult">
        <include refid="selectCCustomerServicePeriodEmployeeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCCustomerServicePeriodEmployee" parameterType="com.bxm.customer.domain.CCustomerServicePeriodEmployee" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_period_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodId != null">period_id,</if>
            <if test="periodEmployeeType != null">period_employee_type,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="employeeName != null">employee_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodId != null">#{periodId},</if>
            <if test="periodEmployeeType != null">#{periodEmployeeType},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="employeeName != null">#{employeeName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodAdvisorEmployee">
        insert into c_customer_service_period_employee
        select null,ccspm.id,1,se.employee_id,se.employee_name,'',now(),'',now()
        from c_customer_service_period_month ccspm join sys_employee se on ccspm.advisor_dept_id = se.dept_id
        where ccspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodAccountingEmployee">
        insert into c_customer_service_period_employee
        select null,ccspm.id,2,se.employee_id,se.employee_name,'',now(),'',now()
        from c_customer_service_period_month ccspm join sys_employee se on ccspm.accounting_dept_id = se.dept_id
        where ccspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodAdvisorEmployeeByCustomerServiceIds">
        insert into c_customer_service_period_employee
        select null,ccspm.id,1,se.employee_id,se.employee_name,'',now(),'',now()
        from c_customer_service_period_month ccspm join sys_employee se on ccspm.advisor_dept_id = se.dept_id
        where ccspm.customer_service_id in
        <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
            #{customerServiceId}
        </foreach>
        <if test="periodStart != null">
            and ccspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and ccspm.period &lt;= ${periodEnd}
        </if>
    </insert>
    <insert id="saveNewPeriodAccountingEmployeeByCustomerServiceIds">
        insert into c_customer_service_period_employee
        select null,ccspm.id,2,se.employee_id,se.employee_name,'',now(),'',now()
        from c_customer_service_period_month ccspm join sys_employee se on ccspm.accounting_dept_id = se.dept_id
        where ccspm.customer_service_id in
        <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
            #{customerServiceId}
        </foreach>
        <if test="periodStart != null">
            and ccspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and ccspm.period &lt;= ${periodEnd}
        </if>
    </insert>

    <update id="updateCCustomerServicePeriodEmployee" parameterType="com.bxm.customer.domain.CCustomerServicePeriodEmployee">
        update c_customer_service_period_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodId != null">period_id = #{periodId},</if>
            <if test="periodEmployeeType != null">period_employee_type = #{periodEmployeeType},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCCustomerServicePeriodEmployeeById" parameterType="Long">
        delete from c_customer_service_period_employee where id = #{id}
    </delete>

    <delete id="deleteCCustomerServicePeriodEmployeeByIds" parameterType="String">
        delete from c_customer_service_period_employee where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>