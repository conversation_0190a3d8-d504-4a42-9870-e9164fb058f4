<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.WorkOrderTypeDeptRelationMapper">
    
    <resultMap type="com.bxm.customer.domain.WorkOrderTypeDeptRelation" id="WorkOrderTypeDeptRelationResult">
        <result property="id"    column="id"    />
        <result property="workOrderTypeId"    column="work_order_type_id"    />
        <result property="workOrderType"    column="work_order_type"    />
        <result property="deptType"    column="dept_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWorkOrderTypeDeptRelationVo">
        select id, work_order_type_id, work_order_type, dept_type, create_by, create_time, update_by, update_time from c_work_order_type_dept_relation
    </sql>

    <select id="selectWorkOrderTypeDeptRelationList" parameterType="com.bxm.customer.domain.WorkOrderTypeDeptRelation" resultMap="WorkOrderTypeDeptRelationResult">
        <include refid="selectWorkOrderTypeDeptRelationVo"/>
        <where>  
            <if test="workOrderTypeId != null "> and work_order_type_id = #{workOrderTypeId}</if>
            <if test="workOrderType != null "> and work_order_type = #{workOrderType}</if>
            <if test="deptType != null "> and dept_type = #{deptType}</if>
        </where>
    </select>
    
    <select id="selectWorkOrderTypeDeptRelationById" parameterType="Long" resultMap="WorkOrderTypeDeptRelationResult">
        <include refid="selectWorkOrderTypeDeptRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWorkOrderTypeDeptRelation" parameterType="com.bxm.customer.domain.WorkOrderTypeDeptRelation" useGeneratedKeys="true" keyProperty="id">
        insert into c_work_order_type_dept_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderTypeId != null">work_order_type_id,</if>
            <if test="workOrderType != null">work_order_type,</if>
            <if test="deptType != null">dept_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderTypeId != null">#{workOrderTypeId},</if>
            <if test="workOrderType != null">#{workOrderType},</if>
            <if test="deptType != null">#{deptType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWorkOrderTypeDeptRelation" parameterType="com.bxm.customer.domain.WorkOrderTypeDeptRelation">
        update c_work_order_type_dept_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderTypeId != null">work_order_type_id = #{workOrderTypeId},</if>
            <if test="workOrderType != null">work_order_type = #{workOrderType},</if>
            <if test="deptType != null">dept_type = #{deptType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkOrderTypeDeptRelationById" parameterType="Long">
        delete from c_work_order_type_dept_relation where id = #{id}
    </delete>

    <delete id="deleteWorkOrderTypeDeptRelationByIds" parameterType="String">
        delete from c_work_order_type_dept_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>