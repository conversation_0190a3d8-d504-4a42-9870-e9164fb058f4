<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.SettlementOrderFileMapper">
    
    <resultMap type="com.bxm.customer.domain.SettlementOrderFile" id="SettlementOrderFileResult">
        <result property="id"    column="id"    />
        <result property="settlementOrderId"    column="settlement_order_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSettlementOrderFileVo">
        select id, settlement_order_id, file_url, file_name, is_del, create_by, create_time, update_by, update_time from c_settlement_order_file
    </sql>

    <select id="selectSettlementOrderFileList" parameterType="com.bxm.customer.domain.SettlementOrderFile" resultMap="SettlementOrderFileResult">
        <include refid="selectSettlementOrderFileVo"/>
        <where>  
            <if test="settlementOrderId != null "> and settlement_order_id = #{settlementOrderId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectSettlementOrderFileById" parameterType="Long" resultMap="SettlementOrderFileResult">
        <include refid="selectSettlementOrderFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSettlementOrderFile" parameterType="com.bxm.customer.domain.SettlementOrderFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_settlement_order_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="settlementOrderId != null">settlement_order_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="settlementOrderId != null">#{settlementOrderId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSettlementOrderFile" parameterType="com.bxm.customer.domain.SettlementOrderFile">
        update c_settlement_order_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="settlementOrderId != null">settlement_order_id = #{settlementOrderId},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettlementOrderFileById" parameterType="Long">
        delete from c_settlement_order_file where id = #{id}
    </delete>

    <delete id="deleteSettlementOrderFileByIds" parameterType="String">
        delete from c_settlement_order_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>