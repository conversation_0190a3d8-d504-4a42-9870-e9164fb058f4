<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceOtherInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceOtherInfo" id="CustomerServiceOtherInfoResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="taxSubmissionStatus"    column="tax_submission_status"    />
        <result property="preTaxProfit"    column="pre_tax_profit"    />
        <result property="nextYearSupplement"    column="next_year_supplement"    />
        <result property="notes"    column="notes"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceOtherInfoVo">
        select id, customer_service_id, tax_submission_status, pre_tax_profit, next_year_supplement, notes, create_by, create_time, update_by, update_time from c_customer_service_other_info
    </sql>

    <select id="selectCustomerServiceOtherInfoList" parameterType="com.bxm.customer.domain.CustomerServiceOtherInfo" resultMap="CustomerServiceOtherInfoResult">
        <include refid="selectCustomerServiceOtherInfoVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="taxSubmissionStatus != null "> and tax_submission_status = #{taxSubmissionStatus}</if>
            <if test="preTaxProfit != null "> and pre_tax_profit = #{preTaxProfit}</if>
            <if test="nextYearSupplement != null "> and next_year_supplement = #{nextYearSupplement}</if>
            <if test="notes != null  and notes != ''"> and notes = #{notes}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceOtherInfoById" parameterType="Long" resultMap="CustomerServiceOtherInfoResult">
        <include refid="selectCustomerServiceOtherInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceOtherInfo" parameterType="com.bxm.customer.domain.CustomerServiceOtherInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_other_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="taxSubmissionStatus != null">tax_submission_status,</if>
            <if test="preTaxProfit != null">pre_tax_profit,</if>
            <if test="nextYearSupplement != null">next_year_supplement,</if>
            <if test="notes != null">notes,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="taxSubmissionStatus != null">#{taxSubmissionStatus},</if>
            <if test="preTaxProfit != null">#{preTaxProfit},</if>
            <if test="nextYearSupplement != null">#{nextYearSupplement},</if>
            <if test="notes != null">#{notes},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceOtherInfo" parameterType="com.bxm.customer.domain.CustomerServiceOtherInfo">
        update c_customer_service_other_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="taxSubmissionStatus != null">tax_submission_status = #{taxSubmissionStatus},</if>
            <if test="preTaxProfit != null">pre_tax_profit = #{preTaxProfit},</if>
            <if test="nextYearSupplement != null">next_year_supplement = #{nextYearSupplement},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceOtherInfoById" parameterType="Long">
        delete from c_customer_service_other_info where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceOtherInfoByIds" parameterType="String">
        delete from c_customer_service_other_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>