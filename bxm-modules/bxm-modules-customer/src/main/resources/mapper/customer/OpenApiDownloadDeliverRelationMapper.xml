<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiDownloadDeliverRelationMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiDownloadDeliverRelation" id="OpenApiDownloadDeliverRelationResult">
        <result property="id"    column="id"    />
        <result property="deliverId"    column="deliver_id"    />
        <result property="downloadId"    column="download_id"    />
        <result property="dwonloadUrl"    column="dwonload_url"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiDownloadDeliverRelationVo">
        select id, deliver_id, download_id, dwonload_url, status, create_by, create_time, update_by, update_time from c_open_api_download_deliver_relation
    </sql>

    <select id="selectOpenApiDownloadDeliverRelationList" parameterType="com.bxm.customer.domain.OpenApiDownloadDeliverRelation" resultMap="OpenApiDownloadDeliverRelationResult">
        <include refid="selectOpenApiDownloadDeliverRelationVo"/>
        <where>  
            <if test="deliverId != null "> and deliver_id = #{deliverId}</if>
            <if test="downloadId != null  and downloadId != ''"> and download_id = #{downloadId}</if>
            <if test="dwonloadUrl != null  and dwonloadUrl != ''"> and dwonload_url = #{dwonloadUrl}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOpenApiDownloadDeliverRelationById" parameterType="Long" resultMap="OpenApiDownloadDeliverRelationResult">
        <include refid="selectOpenApiDownloadDeliverRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiDownloadDeliverRelation" parameterType="com.bxm.customer.domain.OpenApiDownloadDeliverRelation" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_download_deliver_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deliverId != null">deliver_id,</if>
            <if test="downloadId != null and downloadId != ''">download_id,</if>
            <if test="dwonloadUrl != null">dwonload_url,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deliverId != null">#{deliverId},</if>
            <if test="downloadId != null and downloadId != ''">#{downloadId},</if>
            <if test="dwonloadUrl != null">#{dwonloadUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiDownloadDeliverRelation" parameterType="com.bxm.customer.domain.OpenApiDownloadDeliverRelation">
        update c_open_api_download_deliver_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliverId != null">deliver_id = #{deliverId},</if>
            <if test="downloadId != null and downloadId != ''">download_id = #{downloadId},</if>
            <if test="dwonloadUrl != null">dwonload_url = #{dwonloadUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiDownloadDeliverRelationById" parameterType="Long">
        delete from c_open_api_download_deliver_relation where id = #{id}
    </delete>

    <delete id="deleteOpenApiDownloadDeliverRelationByIds" parameterType="String">
        delete from c_open_api_download_deliver_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>