<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverFileMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliverFile" id="MaterialDeliverFileResult">
        <result property="id"    column="id"    />
        <result property="materialDeliverId"    column="material_deliver_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverFileVo">
        select id, material_deliver_id, file_url, file_size, file_name, file_type, create_by, create_time, update_by, update_time from c_material_deliver_file
    </sql>

    <select id="selectMaterialDeliverFileList" parameterType="com.bxm.customer.domain.MaterialDeliverFile" resultMap="MaterialDeliverFileResult">
        <include refid="selectMaterialDeliverFileVo"/>
        <where>  
            <if test="materialDeliverId != null "> and material_deliver_id = #{materialDeliverId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverFileById" parameterType="Long" resultMap="MaterialDeliverFileResult">
        <include refid="selectMaterialDeliverFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMaterialDeliverFile" parameterType="com.bxm.customer.domain.MaterialDeliverFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">#{materialDeliverId},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliverFile" parameterType="com.bxm.customer.domain.MaterialDeliverFile">
        update c_material_deliver_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id = #{materialDeliverId},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverFileById" parameterType="Long">
        delete from c_material_deliver_file where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverFileByIds" parameterType="String">
        delete from c_material_deliver_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>