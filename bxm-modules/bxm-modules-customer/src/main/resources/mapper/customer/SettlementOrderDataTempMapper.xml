<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.SettlementOrderDataTempMapper">
    
    <resultMap type="com.bxm.customer.domain.SettlementOrderDataTemp" id="SettlementOrderDataTempResult">
        <result property="id"    column="id"    />
        <result property="settlementOrderBatchNo"    column="settlement_order_batch_no"    />
        <result property="businessId"    column="business_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerServiceTaxType"    column="customer_service_tax_type"    />
        <result property="customerServiceAdvisorDeptId"    column="customer_service_advisor_dept_id"    />
        <result property="customerServiceAdvisorDeptName"    column="customer_service_advisor_dept_name"    />
        <result property="customerServiceAdvisorEmployeeName"    column="customer_service_advisor_employee_name"    />
        <result property="customerServiceAccountingDeptId"    column="customer_service_accounting_dept_id"    />
        <result property="customerServiceAccountingDeptName"    column="customer_service_accounting_dept_name"    />
        <result property="customerServiceAccountingEmployeeName"    column="customer_service_accounting_employee_name"    />
        <result property="customerServiceFirstAccountPeriod"    column="customer_service_first_account_period"    />
        <result property="customerServiceAccountingRemark"    column="customer_service_accounting_remark"    />
        <result property="period"    column="period"    />
        <result property="periodServiceType"    column="period_service_type"    />
        <result property="periodTaxType"    column="period_tax_type"    />
        <result property="periodAdvisorDeptId"    column="period_advisor_dept_id"    />
        <result property="periodAdvisorDeptName"    column="period_advisor_dept_name"    />
        <result property="periodAdvisorEmployeeName"    column="period_advisor_employee_name"    />
        <result property="periodAccountingDeptId"    column="period_accounting_dept_id"    />
        <result property="periodAccountingDeptName"    column="period_accounting_dept_name"    />
        <result property="periodAccountingEmployeeName"    column="period_accounting_employee_name"    />
        <result property="periodAccountingStatus"    column="period_accounting_status"    />
        <result property="periodInAccountDeliverResult"    column="period_in_account_deliver_result"    />
        <result property="periodInAccountBankPaymentInputTime"    column="period_in_account_bank_payment_input_time"    />
        <result property="periodInAccountInTime"    column="period_in_account_in_time"    />
        <result property="periodInAccountEndTime"    column="period_in_account_end_time"    />
        <result property="periodInAccountRpaRemark"    column="period_in_account_rpa_remark"    />
        <result property="createDeptId"    column="create_dept_id"    />
        <result property="createDeptName"    column="create_dept_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSettlementOrderDataVo">
        select id, settlement_order_batch_no, business_id, business_type, customer_service_id, customer_service_tax_type, customer_service_advisor_dept_id, customer_service_advisor_dept_name, customer_service_advisor_employee_name, customer_service_accounting_dept_id, customer_service_accounting_dept_name, customer_service_accounting_employee_name, customer_service_first_account_period, customer_service_accounting_remark, period, period_service_type, period_tax_type, period_advisor_dept_id, period_advisor_dept_name, period_advisor_employee_name, period_accounting_dept_id, period_accounting_dept_name, period_accounting_employee_name, period_accounting_status, period_in_account_deliver_result, period_in_account_bank_payment_input_time, period_in_account_in_time, period_in_account_end_time, period_in_account_rpa_remark, create_dept_id, create_dept_name, create_by, create_time, update_by, update_time from c_settlement_order_data_temp
    </sql>
    <insert id="insertBySql">
        ${insertSql}
    </insert>
    <insert id="saveByPeriodIds">
        insert into c_settlement_order_data_temp
        select null,#{batchNo},c_customer_service_period_month.id,2,${businessDeptId},c_customer_service.id,c_customer_service.tax_type,c_customer_service.advisor_dept_id,sd3.dept_name,se3.employee_name,
                c_customer_service.accounting_dept_id,sd4.dept_name,se4.employee_name,c_customer_service.first_account_period,c_customer_service.accounting_remark,
                c_customer_service_period_month.period,c_customer_service_period_month.service_type,c_customer_service_period_month.tax_type,c_customer_service_period_month.advisor_dept_id,sd1.dept_name,se1.employee_name,
                c_customer_service_period_month.accounting_dept_id,sd2.dept_name,se2.employee_name,c_customer_service_period_month.accounting_status,c_customer_service_cashier_accounting.deliver_result,c_customer_service_period_month.bank_payment_result,c_customer_service_cashier_accounting.deliver_status,c_customer_service_cashier_accounting.deliver_result,null,c_customer_service_cashier_accounting.in_time,c_customer_service_cashier_accounting.end_time,c_customer_service_cashier_accounting.rpa_remark,c_customer_service_period_month.settlement_status,c_customer_service_period_month.prepay_status,
                null,null,'',NOW(),'',NOW()
                from c_customer_service_period_month left join c_customer_service_cashier_accounting on c_customer_service_period_month.id = c_customer_service_cashier_accounting.customer_service_period_month_id and c_customer_service_cashier_accounting.is_del = 0 and c_customer_service_cashier_accounting.`type` = 1 join c_customer_service on c_customer_service_period_month.customer_service_id = c_customer_service.id
                left join sys_dept sd1 on c_customer_service_period_month.advisor_dept_id = sd1.dept_id and sd1.del_flag = 0
                left join sys_dept sd2 on c_customer_service_period_month.accounting_dept_id = sd2.dept_id and sd2.del_flag = 0
                left join sys_dept sd3 on c_customer_service.advisor_dept_id = sd3.dept_id and sd3.del_flag = 0
                left join sys_dept sd4 on c_customer_service.accounting_dept_id = sd4.dept_id and sd4.del_flag = 0
                left join (SELECT period_id,GROUP_CONCAT(employee_name) as employee_name FROM c_customer_service_period_employee WHERE period_employee_type = 1 GROUP BY period_id) se1 on se1.period_id = c_customer_service_period_month.id
                left join (SELECT period_id,GROUP_CONCAT(employee_name) as employee_name FROM c_customer_service_period_employee WHERE period_employee_type = 2 GROUP BY period_id) se2 on se2.period_id = c_customer_service_period_month.id
                left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se3 on c_customer_service.advisor_dept_id = se3.dept_id
                left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se4 on c_customer_service.accounting_dept_id = se4.dept_id
                where c_customer_service_period_month.id in
                <foreach collection="periodMonthIds" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
    </insert>
    <insert id="saveByCustomerIds">
        insert into c_settlement_order_data_temp
        select null,#{batchNo},c_customer_service.id,1,${businessDeptId},c_customer_service.id,c_customer_service.tax_type,c_customer_service.advisor_dept_id,sd1.dept_name,se1.employee_name,
        c_customer_service.accounting_dept_id,sd2.dept_name,se2.employee_name,c_customer_service.first_account_period,c_customer_service.accounting_remark,
        null,null,null,null,null,null,
        null,null,null,null,null,null,null,null,null,null,null,null,null,null,
        null,null,'',NOW(),'',NOW()
        from c_customer_service
        left join sys_dept sd1 on c_customer_service.advisor_dept_id = sd1.dept_id and sd1.del_flag = 0
        left join sys_dept sd2 on c_customer_service.accounting_dept_id = sd2.dept_id and sd2.del_flag = 0
        left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se1 on c_customer_service.advisor_dept_id = se1.dept_id
        left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se2 on c_customer_service.accounting_dept_id = se2.dept_id
        where c_customer_service.id in
        <foreach collection="customerServiceIds" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </insert>
    <select id="settlementOrderDataListByBatchNo" resultType="com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO">
        select
            csdt.id,
            csdt.business_type as businessType,
            csdt.business_id as businessId,
            csdt.customer_service_id as customerServiceId,
            ccs.customer_name as customerName,
            ccs.credit_code as creditCode,
            csdt.customer_service_tax_type as customerServiceTaxType,
            csdt.customer_service_advisor_dept_id as customerServiceAdvisorDeptId,
            csdt.customer_service_advisor_dept_name as customerServiceAdvisorDeptName,
            csdt.customer_service_advisor_employee_name as customerServiceAdvisorEmployeeName,
            csdt.customer_service_accounting_dept_id as customerServiceAccountingDeptId,
            csdt.customer_service_accounting_dept_name as customerServiceAccountingDeptName,
            csdt.customer_service_accounting_employee_name as customerServiceAccountingEmployeeName,
            csdt.customer_service_first_account_period as customerServiceFirstAccountPeriod,
            csdt.period as period,
            csdt.period_service_type as periodServiceType,
            csdt.period_tax_type as periodTaxType,
            csdt.period_advisor_dept_id as periodAdvisorDeptId,
            csdt.period_advisor_dept_name as periodAdvisorDeptName,
            csdt.period_advisor_employee_name as periodAdvisorEmployeeName,
            csdt.period_accounting_dept_id as periodAccountingDeptId,
            csdt.period_accounting_dept_name as periodAccountingDeptName,
            csdt.period_accounting_employee_name as periodAccountingEmployeeName,
            csdt.period_accounting_status as periodAccountStatus,
            csdt.period_in_account_deliver_result as periodInAccountDeliverResult,
            csdt.period_bank_payment_input_result as periodBankPaymentInputResult,
            csdt.period_in_account_status as periodInAccountStatus,
            csdt.period_in_account_result as periodInAccountResult,
            csdt.period_in_account_bank_payment_input_time as periodInAccountBankPaymentInputTime,
            csdt.period_in_account_in_time as periodInAccountInTime,
            csdt.period_in_account_end_time as periodInAccountEndTime,
            csdt.customer_service_accounting_remark as customerServiceAccountingRemark,
            csdt.period_in_account_rpa_remark as periodInAccountRpaRemark,
            csdt.period_settlement_status as periodSettlementStatus,
            csdt.period_prepay_status as periodPrepayStatus,
            csdt.create_dept_id as createDeptId,
            csdt.create_dept_name as createDeptName,
            csdt.create_by as createBy,
            csdt.create_time as createTime
            from c_settlement_order_data_temp csdt join c_customer_service ccs on csdt.customer_service_id = ccs.id and ccs.is_del = 0
            left join c_customer_service_period_month ccspm on csdt.business_type = 2 and csdt.business_id = ccspm.id
        <where>
            csdt.settlement_order_batch_no = #{vo.batchNo}
            <if test="vo.businessDeptId != null">
                and csdt.business_dept_id = #{vo.businessDeptId}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and ccs.customer_name like concat('%',#{vo.customerName},'%')
            </if>
            <if test="vo.customerServiceTagName != null and vo.customerServiceTagName != ''">
                <if test="vo.customerServiceTagIncludeFlag == 1">
                    <if test="vo.customerServiceTagType == 1">
                        and ccs.id in (select distinct business_id from c_business_tag_relation where business_type = 1 and tag_id in (select id from c_tag where tag_name like concat('%', #{vo.customerServiceTagNames}, '%')))
                    </if>
                    <if test="vo.customerServiceTagType == 2">
                        and ccs.id in ( SELECT distinct business_id FROM c_business_tag_relation WHERE business_type = 1 and tag_id IN ( SELECT id FROM c_tag WHERE tag_name IN (${vo.customerServiceTagNames}) GROUP BY business_id HAVING COUNT(DISTINCT tag_id) = ${vo.customerTagCount}))
                    </if>
                    <if test="vo.customerServiceTagType == 3">
                        and ccs.id in ( SELECT DISTINCT business_id FROM c_business_tag_relation WHERE tag_id IN (SELECT id FROM c_tag WHERE tag_name IN (${vo.customerServiceTagNames})) AND business_type = 1)
                    </if>
                </if>
                <if test="vo.customerServiceTagIncludeFlag == 0">
                    <if test="vo.customerServiceTagType == 1">
                        and ccs.id not in (select distinct business_id from c_business_tag_relation where business_type = 1 and tag_id in (select id from c_tag where tag_name like concat('%', #{vo.customerServiceTagNames}, '%')))
                    </if>
                    <if test="vo.customerServiceTagType == 2">
                        and ccs.id not in ( SELECT distinct business_id FROM c_business_tag_relation WHERE business_type = 1 and tag_id IN ( SELECT id FROM c_tag WHERE tag_name IN (${vo.customerServiceTagNames}) GROUP BY business_id HAVING COUNT(DISTINCT tag_id) = ${vo.customerTagCount}))
                    </if>
                    <if test="vo.customerServiceTagType == 3">
                        and ccs.id not in ( SELECT DISTINCT business_id FROM c_business_tag_relation WHERE tag_id IN (SELECT id FROM c_tag WHERE tag_name IN (${vo.customerServiceTagNames})) AND business_type = 1)
                    </if>
                </if>
            </if>
            <if test="vo.customerServiceTaxType != null">
                and csdt.customer_service_tax_type = #{vo.customerServiceTaxType}
            </if>
            <if test="vo.customerServiceAdvisorDeptId != null">
                and csdt.customer_service_advisor_dept_id = #{vo.customerServiceAdvisorDeptId}
            </if>
            <if test="vo.customerServiceAccountingDeptId != null">
                and csdt.customer_service_accounting_dept_id = #{vo.customerServiceAccountingDeptId}
            </if>
            <if test="vo.periodStart != null and vo.periodStart != ''">
                and csdt.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null and vo.periodEnd != ''">
                and csdt.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.periodTaxType != null">
                and csdt.period_tax_type = #{vo.periodTaxType}
            </if>
            <if test="vo.periodAdvisorDeptId != null">
                and csdt.period_advisor_dept_id = #{vo.periodAdvisorDeptId}
            </if>
            <if test="vo.periodAccountingDeptId != null">
                and csdt.period_accounting_dept_id = #{vo.periodAccountingDeptId}
            </if>
            <if test="vo.periodAccountingStatus != null">
                and csdt.period_accounting_status = #{vo.periodAccountingStatus}
            </if>
            <if test="vo.periodInAccountDeliverResult != null">
                and csdt.period_in_account_deliver_result = #{vo.periodInAccountDeliverResult}
            </if>
<!--            <if test="vo.periodInAccountResult != null">-->
<!--                and csdt.period_in_account_result = #{vo.periodInAccountResult}-->
<!--            </if>-->
<!--            <if test="vo.periodBankPaymentInputResult != null">-->
<!--                and csdt.period_bank_payment_input_result = #{vo.periodBankPaymentInputResult}-->
<!--            </if>-->
            <if test="vo.periodBankPaymentInputResult != null and vo.periodBankPaymentInputResult != ''">
                <if test='vo.periodBankPaymentInputResult.contains("0")'>
                    and (csdt.period_bank_payment_input_result is null or csdt.period_bank_payment_input_result in (${vo.periodBankPaymentInputResult}))
                </if>
                <if test='!vo.periodBankPaymentInputResult.contains("0")'>
                    and csdt.period_bank_payment_input_result in (${vo.periodBankPaymentInputResult})
                </if>
            </if>
            <if test="vo.periodInAccountStatus != null and vo.periodInAccountStatus != ''">
                <if test='vo.periodInAccountStatus.contains("0")'>
                    and (csdt.period_in_account_status is null or csdt.period_in_account_status in (${vo.periodInAccountStatus}))
                </if>
                <if test='!vo.periodInAccountStatus.contains("0")'>
                    and csdt.period_in_account_status in (${vo.periodInAccountStatus})
                </if>
            </if>
            <if test="vo.periodInAccountResult != null and vo.periodInAccountResult != ''">
                <if test='vo.periodInAccountResult.contains("0")'>
                    and (csdt.period_in_account_result is null or csdt.period_in_account_result in (${vo.periodInAccountResult}))
                </if>
                <if test='!vo.periodInAccountResult.contains("0")'>
                    and csdt.period_in_account_result in (${vo.periodInAccountResult})
                </if>
            </if>
            <if test="vo.periodInAccountBankPaymentInputTimeStart != null and vo.periodInAccountBankPaymentInputTimeStart != ''">
                and csdt.period_in_account_bank_payment_input_time &gt;= #{vo.periodInAccountBankPaymentInputTimeStart}
            </if>
            <if test="vo.periodInAccountBankPaymentInputTimeEnd != null and vo.periodInAccountBankPaymentInputTimeEnd != ''">
                and csdt.period_in_account_bank_payment_input_time &lt;= #{vo.periodInAccountBankPaymentInputTimeEnd}
            </if>
            <if test="vo.periodInAccountInTimeStart != null and vo.periodInAccountInTimeStart != ''">
                and csdt.period_in_account_in_time &gt;= #{vo.periodInAccountInTimeStart}
            </if>
            <if test="vo.periodInAccountInTimeEnd != null and vo.periodInAccountInTimeEnd != ''">
                and csdt.period_in_account_in_time &lt;= #{vo.periodInAccountInTimeEnd}
            </if>
            <if test="vo.periodInAccountEndTimeStart != null and vo.periodInAccountEndTimeStart != ''">
                and csdt.period_in_account_end_time &gt;= #{vo.periodInAccountEndTimeStart}
            </if>
            <if test="vo.periodInAccountEndTimeEnd != null and vo.periodInAccountEndTimeEnd != ''">
                and csdt.period_in_account_end_time &lt;= #{vo.periodInAccountEndTimeEnd}
            </if>
            <if test="vo.periodTagName != null and vo.periodTagName != ''">
                <if test="vo.periodTagIncludeFlag == 1">
                    <if test="vo.periodTagType == 1">
                        and ccspm.id in (select distinct business_id from c_business_tag_relation where business_type = 2 and tag_id in (select id from c_tag where tag_name like concat('%', #{vo.periodTagNames}, '%')))
                    </if>
                    <if test="vo.periodTagType == 2">
                        and ccspm.id in ( SELECT distinct business_id FROM c_business_tag_relation WHERE business_type = 2 and tag_id IN ( SELECT id FROM c_tag WHERE tag_name IN (${vo.periodTagNames}) GROUP BY business_id HAVING COUNT(DISTINCT tag_id) = ${vo.periodTagCount}))
                    </if>
                    <if test="vo.periodTagType == 3">
                        and ccspm.id in ( SELECT DISTINCT business_id FROM c_business_tag_relation WHERE tag_id IN (SELECT id FROM c_tag WHERE tag_name IN (${vo.periodTagNames})) AND business_type = 2)
                    </if>
                </if>
                <if test="vo.periodTagIncludeFlag == 0">
                    <if test="vo.periodTagType == 1">
                        and ccspm.id not in (select distinct business_id from c_business_tag_relation where business_type = 2 and tag_id in (select id from c_tag where tag_name like concat('%', #{vo.periodTagNames}, '%')))
                    </if>
                    <if test="vo.periodTagType == 2">
                        and ccspm.id not in ( SELECT distinct business_id FROM c_business_tag_relation WHERE business_type = 2 and tag_id IN ( SELECT id FROM c_tag WHERE tag_name IN (${vo.periodTagNames}) GROUP BY business_id HAVING COUNT(DISTINCT tag_id) = ${vo.periodTagCount}))
                    </if>
                    <if test="vo.periodTagType == 3">
                        and ccspm.id not in ( SELECT DISTINCT business_id FROM c_business_tag_relation WHERE tag_id IN (SELECT id FROM c_tag WHERE tag_name IN (${vo.periodTagNames})) AND business_type = 2)
                    </if>
                </if>
            </if>
            <if test="vo.customerServiceFirstPeriodStart != null and vo.customerServiceFirstPeriodStart != ''">
                and csdt.customer_service_first_account_period &gt;= #{vo.customerServiceFirstPeriodStart}
            </if>
            <if test="vo.customerServiceFirstPeriodEnd != null and vo.customerServiceFirstPeriodEnd != ''">
                and csdt.customer_service_first_account_period &lt;= #{vo.customerServiceFirstPeriodEnd}
            </if>
            <if test="vo.createDeptId != null">
                and csdt.create_dept_id = #{createDeptId}
            </if>
            <if test="vo.customerServiceCreateTimeStart != null and vo.customerServiceCreateTimeStart != ''">
                and csdt.create_time &gt;= #{vo.customerServiceCreateTimeStart}
            </if>
            <if test="vo.customerServiceCreateTimeEnd != null and vo.customerServiceCreateTimeEnd != ''">
                and csdt.create_time &lt;= #{vo.customerServiceCreateTimeEnd}
            </if>
            <if test="vo.periodServiceType != null">
                and csdt.period_service_type = #{vo.periodServiceType}
            </if>
            <if test="vo.periodSettlementStatus != null">
                <if test="vo.periodSettlementStatus == 4">
                    and csdt.period_settlement_status in (4, 5, 6)
                </if>
                <if test="vo.periodSettlementStatus != 4">
                    and csdt.period_settlement_status = #{vo.periodSettlementStatus}
                </if>
            </if>
            <if test="vo.periodPrepayStatus != null">
                and csdt.period_prepay_status = #{vo.periodPrepayStatus}
            </if>
        </where>
        order by id desc
    </select>
</mapper>