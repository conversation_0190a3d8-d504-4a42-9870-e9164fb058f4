<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerDeliverTemplateMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerDeliverTemplate" id="CustomerDeliverTemplateResult">
        <result property="id"    column="id"    />
        <result property="deliverType"    column="deliver_type"    />
        <result property="operType"    column="oper_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerDeliverTemplateVo">
        select id, deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time from c_customer_deliver_template
    </sql>

    <select id="selectCustomerDeliverTemplateList" parameterType="com.bxm.customer.domain.CustomerDeliverTemplate" resultMap="CustomerDeliverTemplateResult">
        <include refid="selectCustomerDeliverTemplateVo"/>
        <where>  
            <if test="deliverType != null "> and deliver_type = #{deliverType}</if>
            <if test="operType != null "> and oper_type = #{operType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>
    
    <select id="selectCustomerDeliverTemplateById" parameterType="Long" resultMap="CustomerDeliverTemplateResult">
        <include refid="selectCustomerDeliverTemplateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerDeliverTemplate" parameterType="com.bxm.customer.domain.CustomerDeliverTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_deliver_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deliverType != null">deliver_type,</if>
            <if test="operType != null">oper_type,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deliverType != null">#{deliverType},</if>
            <if test="operType != null">#{operType},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerDeliverTemplate" parameterType="com.bxm.customer.domain.CustomerDeliverTemplate">
        update c_customer_deliver_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliverType != null">deliver_type = #{deliverType},</if>
            <if test="operType != null">oper_type = #{operType},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerDeliverTemplateById" parameterType="Long">
        delete from c_customer_deliver_template where id = #{id}
    </delete>

    <delete id="deleteCustomerDeliverTemplateByIds" parameterType="String">
        delete from c_customer_deliver_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>