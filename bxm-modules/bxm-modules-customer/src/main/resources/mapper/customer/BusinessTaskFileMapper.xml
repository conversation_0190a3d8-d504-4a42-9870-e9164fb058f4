<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BusinessTaskFileMapper">
    
    <resultMap type="com.bxm.customer.domain.BusinessTaskFile" id="BusinessTaskFileResult">
        <result property="id"    column="id"    />
        <result property="mainId"    column="main_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="subFileType"    column="sub_file_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBusinessTaskFileVo">
        select id, main_id, file_url, file_name, file_type, sub_file_type, create_by, create_time, update_by, update_time from c_business_task_file
    </sql>

    <select id="selectBusinessTaskFileList" parameterType="com.bxm.customer.domain.BusinessTaskFile" resultMap="BusinessTaskFileResult">
        <include refid="selectBusinessTaskFileVo"/>
        <where>  
            <if test="mainId != null "> and main_id = #{mainId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="subFileType != null  and subFileType != ''"> and sub_file_type = #{subFileType}</if>
        </where>
    </select>
    
    <select id="selectBusinessTaskFileById" parameterType="Long" resultMap="BusinessTaskFileResult">
        <include refid="selectBusinessTaskFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBusinessTaskFile" parameterType="com.bxm.customer.domain.BusinessTaskFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_task_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainId != null">main_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainId != null">#{mainId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="subFileType != null and subFileType != ''">#{subFileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBusinessTaskFile" parameterType="com.bxm.customer.domain.BusinessTaskFile">
        update c_business_task_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="mainId != null">main_id = #{mainId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type = #{subFileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessTaskFileById" parameterType="Long">
        delete from c_business_task_file where id = #{id}
    </delete>

    <delete id="deleteBusinessTaskFileByIds" parameterType="String">
        delete from c_business_task_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>