<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiCheckFileRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiCheckFileRecord" id="OpenApiCheckFileRecordResult">
        <result property="id"    column="id"    />
        <result property="businessTaskId"    column="business_task_id"    />
        <result property="customerServiceCashierAccountingId"    column="customer_service_cashier_accounting_id"    />
        <result property="checkFileParam"    column="check_file_param"    />
        <result property="searchTime"    column="search_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiCheckFileRecordVo">
        select id, business_task_id, customer_service_cashier_accounting_id, check_file_param, search_time, status, create_by, create_time, update_by, update_time from c_open_api_check_file_record
    </sql>

    <select id="selectOpenApiCheckFileRecordList" parameterType="com.bxm.customer.domain.OpenApiCheckFileRecord" resultMap="OpenApiCheckFileRecordResult">
        <include refid="selectOpenApiCheckFileRecordVo"/>
        <where>  
            <if test="businessTaskId != null "> and business_task_id = #{businessTaskId}</if>
            <if test="customerServiceCashierAccountingId != null "> and customer_service_cashier_accounting_id = #{customerServiceCashierAccountingId}</if>
            <if test="checkFileParam != null  and checkFileParam != ''"> and check_file_param = #{checkFileParam}</if>
            <if test="searchTime != null "> and search_time = #{searchTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOpenApiCheckFileRecordById" parameterType="Long" resultMap="OpenApiCheckFileRecordResult">
        <include refid="selectOpenApiCheckFileRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiCheckFileRecord" parameterType="com.bxm.customer.domain.OpenApiCheckFileRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_check_file_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessTaskId != null">business_task_id,</if>
            <if test="customerServiceCashierAccountingId != null">customer_service_cashier_accounting_id,</if>
            <if test="checkFileParam != null and checkFileParam != ''">check_file_param,</if>
            <if test="searchTime != null">search_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessTaskId != null">#{businessTaskId},</if>
            <if test="customerServiceCashierAccountingId != null">#{customerServiceCashierAccountingId},</if>
            <if test="checkFileParam != null and checkFileParam != ''">#{checkFileParam},</if>
            <if test="searchTime != null">#{searchTime},</if>
            <if test="isClosed != null">#{isClosed},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiCheckFileRecord" parameterType="com.bxm.customer.domain.OpenApiCheckFileRecord">
        update c_open_api_check_file_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessTaskId != null">business_task_id = #{businessTaskId},</if>
            <if test="customerServiceCashierAccountingId != null">customer_service_cashier_accounting_id = #{customerServiceCashierAccountingId},</if>
            <if test="checkFileParam != null and checkFileParam != ''">check_file_param = #{checkFileParam},</if>
            <if test="searchTime != null">search_time = #{searchTime},</if>
            <if test="status != null">status = #{isClosed},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiCheckFileRecordById" parameterType="Long">
        delete from c_open_api_check_file_record where id = #{id}
    </delete>

    <delete id="deleteOpenApiCheckFileRecordByIds" parameterType="String">
        delete from c_open_api_check_file_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>