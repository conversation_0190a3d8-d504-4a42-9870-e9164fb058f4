<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.ReportTableTaxConfigMapper">
    
    <resultMap type="com.bxm.customer.domain.ReportTableTaxConfig" id="ReportTableTaxConfigResult">
        <result property="id"    column="id"    />
        <result property="tableName"    column="table_name"    />
        <result property="categoryName"    column="category_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="reportType"    column="report_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectReportTableTaxConfigVo">
        select id, table_name, category_name, item_name, report_type, create_by, create_time, update_by, update_time from c_report_table_tax_config
    </sql>

    <select id="selectReportTableTaxConfigList" parameterType="com.bxm.customer.domain.ReportTableTaxConfig" resultMap="ReportTableTaxConfigResult">
        <include refid="selectReportTableTaxConfigVo"/>
        <where>  
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="reportType != null  and reportType != ''"> and report_type = #{reportType}</if>
        </where>
    </select>
    
    <select id="selectReportTableTaxConfigById" parameterType="Long" resultMap="ReportTableTaxConfigResult">
        <include refid="selectReportTableTaxConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertReportTableTaxConfig" parameterType="com.bxm.customer.domain.ReportTableTaxConfig" useGeneratedKeys="true" keyProperty="id">
        insert into c_report_table_tax_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="itemName != null">item_name,</if>
            <if test="reportType != null">report_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateReportTableTaxConfig" parameterType="com.bxm.customer.domain.ReportTableTaxConfig">
        update c_report_table_tax_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReportTableTaxConfigById" parameterType="Long">
        delete from c_report_table_tax_config where id = #{id}
    </delete>

    <delete id="deleteReportTableTaxConfigByIds" parameterType="String">
        delete from c_report_table_tax_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>