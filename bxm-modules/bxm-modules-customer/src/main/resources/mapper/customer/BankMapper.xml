<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BankMapper">
    
    <resultMap type="com.bxm.customer.domain.Bank" id="BankResult">
        <result property="id"    column="id"    />
        <result property="bankName"    column="bank_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCTagVo">
        select id, bank_name, create_by, create_time, update_by, update_time from c_bank
    </sql>
</mapper>