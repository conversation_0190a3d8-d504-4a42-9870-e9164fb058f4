<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceInAccountFileMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceInAccountFile" id="CustomerServiceInAccountFileResult">
        <result property="id"    column="id"    />
        <result property="customerServiceInAccountId"    column="customer_service_in_account_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="subFileType"    column="sub_file_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceInAccountFileVo">
        select id, customer_service_in_account_id, file_url, file_name, file_type, sub_file_type, create_by, create_time, update_by, update_time from c_customer_service_in_account_file
    </sql>

    <select id="selectCustomerServiceInAccountFileList" parameterType="com.bxm.customer.domain.CustomerServiceInAccountFile" resultMap="CustomerServiceInAccountFileResult">
        <include refid="selectCustomerServiceInAccountFileVo"/>
        <where>  
            <if test="customerServiceInAccountId != null "> and customer_service_in_account_id = #{customerServiceInAccountId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="subFileType != null  and subFileType != ''"> and sub_file_type = #{subFileType}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceInAccountFileById" parameterType="Long" resultMap="CustomerServiceInAccountFileResult">
        <include refid="selectCustomerServiceInAccountFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceInAccountFile" parameterType="com.bxm.customer.domain.CustomerServiceInAccountFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_in_account_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceInAccountId != null">customer_service_in_account_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileType != null">file_type,</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceInAccountId != null">#{customerServiceInAccountId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="subFileType != null and subFileType != ''">#{subFileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceInAccountFile" parameterType="com.bxm.customer.domain.CustomerServiceInAccountFile">
        update c_customer_service_in_account_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceInAccountId != null">customer_service_in_account_id = #{customerServiceInAccountId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="subFileType != null and subFileType != ''">sub_file_type = #{subFileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceInAccountFileById" parameterType="Long">
        delete from c_customer_service_in_account_file where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceInAccountFileByIds" parameterType="String">
        delete from c_customer_service_in_account_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>