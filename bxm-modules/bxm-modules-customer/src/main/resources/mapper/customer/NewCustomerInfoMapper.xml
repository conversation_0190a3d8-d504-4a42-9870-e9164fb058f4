<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerInfo" id="NewCustomerInfoResult">
        <result property="id"    column="id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="registrationDate"    column="registration_date"    />
        <result property="registrationRegion"    column="registration_region"    />
        <result property="industry"    column="industry"    />
        <result property="taxType"    column="tax_type"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="businessTopDeptId"    column="business_top_dept_id"    />
        <result property="advisorDeptId"    column="advisor_dept_id"    />
        <result property="advisorTopDeptId"    column="advisor_top_dept_id"    />
        <result property="accountingDeptId"    column="accounting_dept_id"    />
        <result property="accountingTopDeptId"    column="accounting_top_dept_id"    />
        <result property="firstAccountPeriod"    column="first_account_period"    />
        <result property="status"    column="status"    />
        <result property="isNewCustomer"    column="is_new_customer"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerInfoVo">
        select id, customer_name, credit_code, tax_number, registration_date, registration_region, industry, tax_type, business_dept_id, business_top_dept_id, advisor_dept_id, advisor_top_dept_id, accounting_dept_id, accounting_top_dept_id, first_account_period, status, is_new_customer, is_del, create_by, create_time, update_by, update_time from c_new_customer_info
    </sql>

    <select id="selectNewCustomerInfoList" parameterType="com.bxm.customer.domain.NewCustomerInfo" resultMap="NewCustomerInfoResult">
        <include refid="selectNewCustomerInfoVo"/>
        <where>  
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="registrationDate != null  and registrationDate != ''"> and registration_date = #{registrationDate}</if>
            <if test="registrationRegion != null  and registrationRegion != ''"> and registration_region = #{registrationRegion}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="taxType != null "> and tax_type = #{taxType}</if>
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="businessTopDeptId != null "> and business_top_dept_id = #{businessTopDeptId}</if>
            <if test="advisorDeptId != null "> and advisor_dept_id = #{advisorDeptId}</if>
            <if test="advisorTopDeptId != null "> and advisor_top_dept_id = #{advisorTopDeptId}</if>
            <if test="accountingDeptId != null "> and accounting_dept_id = #{accountingDeptId}</if>
            <if test="accountingTopDeptId != null "> and accounting_top_dept_id = #{accountingTopDeptId}</if>
            <if test="firstAccountPeriod != null "> and first_account_period = #{firstAccountPeriod}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isNewCustomer != null "> and is_new_customer = #{isNewCustomer}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerInfoById" parameterType="Long" resultMap="NewCustomerInfoResult">
        <include refid="selectNewCustomerInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectNewCustomerTransferList"
            resultType="com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferDTO">
        select
            cnci.id as id,
            cnci.customer_name as customerName,
            cnci.credit_code as creditCode,
            cnci.`status` as `status`,
            cnci.tax_type as taxType,
            cnci.business_top_dept_id as businessTopDeptId,
            cnci.business_dept_id as businessDeptId,
            sd1.dept_name as businessDeptName,
            cnci.advisor_dept_id as advisorDeptId,
            sd2.dept_name as advisorDeptName,
            cnci.first_account_period as firstPeriod,
            cnci.first_accounting_period as firstAccountingPeriodInt,
            cnci.create_remark as createRemark,
            cnci.submit_time as submitTime
        from c_new_customer_info cnci
            <if test="userDeptDTO.deptType == 2">
                left join c_customer_service ccs on cnci.id = ccs.new_customer_id and ccs.is_del = 0
            </if>
                 left join sys_dept sd1 on cnci.business_dept_id = sd1.dept_id and sd1.del_flag = '0'
                 left join sys_dept sd2 on cnci.advisor_dept_id = sd2.dept_id and sd2.del_flag = '0'
        <where>
            cnci.is_del = 0
            <if test="userDeptDTO.isAdmin == false">
                <if test="userDeptDTO.deptType == 1 and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size() > 0">
                    and (
                    cnci.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or cnci.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2 and isHeadquarters == false and userDeptDTO.deptIds != null and userDeptDTO.deptIds.size() > 0">
                    and (
                    ccs.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="businessDeptIdList != null and businessDeptIdList.size > 0">
                and cnci.business_dept_id in
                <foreach collection="businessDeptIdList" item="businessDeptId" open="(" separator="," close=")">
                    #{businessDeptId}
                </foreach>
            </if>
            <if test="firstPeriodStart != null">
                and cnci.first_account_period &gt;= #{firstPeriodStart}
            </if>
            <if test="firstPeriodEnd != null">
                and cnci.first_account_period &lt;= #{firstPeriodEnd}
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (cnci.customer_name like concat('%', #{keyWord}, '%') or cnci.credit_code like concat('%', #{keyWord}, '%'))
            </if>
            <if test="status != null">
                and cnci.`status` = #{status}
            </if>
            <if test="taxType != null">
                and cnci.tax_type = #{taxType}
            </if>
            <if test="tagName != null and tagName != ''">
                <if test="tagIncludeFlag != null and tagIncludeFlag == 1 and customerIds != null and customerIds.size > 0">
                    and cnci.id in
                    <foreach collection="customerIds" item="customerId" separator="," close=")" open="(">
                        #{customerId}
                    </foreach>
                </if>
                <if test="tagIncludeFlag != null and tagIncludeFlag == 0 and customerIds != null and customerIds.size > 0">
                    and cnci.id not in
                    <foreach collection="customerIds" item="customerId" separator="," close=")" open="(">
                        #{customerId}
                    </foreach>
                </if>
            </if>
            <if test="queryDeptId != null">
                <if test="userDeptDTO.deptType == 1">
                    and cnci.create_dept_id = #{queryDeptId}
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and ccs.accounting_dept_id = #{queryDeptId}
                </if>
            </if>
            <if test="isHeadquarters != null and isHeadquarters == 1">
                and cnci.`status` in (3, 4)
            </if>
        </where>
        order by cnci.sort_time desc
    </select>

    <insert id="insertNewCustomerInfo" parameterType="com.bxm.customer.domain.NewCustomerInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="taxNumber != null and taxNumber != ''">tax_number,</if>
            <if test="registrationDate != null and registrationDate != ''">registration_date,</if>
            <if test="registrationRegion != null and registrationRegion != ''">registration_region,</if>
            <if test="industry != null and industry != ''">industry,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="businessTopDeptId != null">business_top_dept_id,</if>
            <if test="advisorDeptId != null">advisor_dept_id,</if>
            <if test="advisorTopDeptId != null">advisor_top_dept_id,</if>
            <if test="accountingDeptId != null">accounting_dept_id,</if>
            <if test="accountingTopDeptId != null">accounting_top_dept_id,</if>
            <if test="firstAccountPeriod != null">first_account_period,</if>
            <if test="status != null">status,</if>
            <if test="isNewCustomer != null">is_new_customer,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="taxNumber != null and taxNumber != ''">#{taxNumber},</if>
            <if test="registrationDate != null and registrationDate != ''">#{registrationDate},</if>
            <if test="registrationRegion != null and registrationRegion != ''">#{registrationRegion},</if>
            <if test="industry != null and industry != ''">#{industry},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="businessTopDeptId != null">#{businessTopDeptId},</if>
            <if test="advisorDeptId != null">#{advisorDeptId},</if>
            <if test="advisorTopDeptId != null">#{advisorTopDeptId},</if>
            <if test="accountingDeptId != null">#{accountingDeptId},</if>
            <if test="accountingTopDeptId != null">#{accountingTopDeptId},</if>
            <if test="firstAccountPeriod != null">#{firstAccountPeriod},</if>
            <if test="status != null">#{status},</if>
            <if test="isNewCustomer != null">#{isNewCustomer},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerInfo" parameterType="com.bxm.customer.domain.NewCustomerInfo">
        update c_new_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="taxNumber != null and taxNumber != ''">tax_number = #{taxNumber},</if>
            <if test="registrationDate != null and registrationDate != ''">registration_date = #{registrationDate},</if>
            <if test="registrationRegion != null and registrationRegion != ''">registration_region = #{registrationRegion},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="businessTopDeptId != null">business_top_dept_id = #{businessTopDeptId},</if>
            <if test="advisorDeptId != null">advisor_dept_id = #{advisorDeptId},</if>
            <if test="advisorTopDeptId != null">advisor_top_dept_id = #{advisorTopDeptId},</if>
            <if test="accountingDeptId != null">accounting_dept_id = #{accountingDeptId},</if>
            <if test="accountingTopDeptId != null">accounting_top_dept_id = #{accountingTopDeptId},</if>
            <if test="firstAccountPeriod != null">first_account_period = #{firstAccountPeriod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isNewCustomer != null">is_new_customer = #{isNewCustomer},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerInfoById" parameterType="Long">
        delete from c_new_customer_info where id = #{id}
    </delete>

    <delete id="deleteNewCustomerInfoByIds" parameterType="String">
        delete from c_new_customer_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>