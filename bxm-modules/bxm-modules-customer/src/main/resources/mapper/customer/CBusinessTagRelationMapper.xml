<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CBusinessTagRelationMapper">
    
    <resultMap type="com.bxm.customer.domain.CBusinessTagRelation" id="CBusinessTagRelationResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="tagId"    column="tag_id"    />
        <result property="tagParamValue"    column="tag_param_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCBusinessTagRelationVo">
        select id, business_type, business_id, tag_id, tag_param_value, create_by, create_time, update_by, update_time from c_business_tag_relation
    </sql>

    <select id="selectCBusinessTagRelationList" parameterType="com.bxm.customer.domain.CBusinessTagRelation" resultMap="CBusinessTagRelationResult">
        <include refid="selectCBusinessTagRelationVo"/>
        <where>  
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="tagId != null "> and tag_id = #{tagId}</if>
            <if test="tagParamValue != null  and tagParamValue != ''"> and tag_param_value = #{tagParamValue}</if>
        </where>
    </select>
    
    <select id="selectCBusinessTagRelationById" parameterType="Long" resultMap="CBusinessTagRelationResult">
        <include refid="selectCBusinessTagRelationVo"/>
        where id = #{id}
    </select>
    <select id="selectTagsByBusinessTypeAndBusinessIds"
            resultType="com.bxm.customer.domain.dto.BusinessTagDTO">
        select
            cb.business_id as businessId,
            ct.id as tagId,
            ct.tag_name as tagName
            from c_business_tag_relation cb join c_tag ct on cb.tag_id = ct.id and ct.is_del = 0
        <where>
            <if test="businessIds != null and businessIds.size > 0">
                and cb.business_id in
                <foreach item="businessId" collection="businessIds" open="(" separator="," close=")">
                    #{businessId}
                </foreach>
            </if>
            <if test="businessType != null">
                and cb.business_type = #{businessType}
            </if>
        </where>
    </select>

    <insert id="insertCBusinessTagRelation" parameterType="com.bxm.customer.domain.CBusinessTagRelation" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_tag_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="tagParamValue != null">tag_param_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="tagParamValue != null">#{tagParamValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodTagRelation">
        insert into c_business_tag_relation
        select null,2,ccspm.id,cbtr.tag_id,cbtr.tag_param_value,'',now(),'',now()
        from c_customer_service_period_month ccspm join c_business_tag_relation cbtr on ccspm.customer_service_id = cbtr.business_id and cbtr.business_type = 1
        where ccspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodTagRelationByCustomerServiceIdsAndPeriod">
        insert into c_business_tag_relation
        select null,2,ccspm.id,cbtr.tag_id,cbtr.tag_param_value,'',now(),'',now()
        from c_customer_service_period_month ccspm join c_business_tag_relation cbtr on ccspm.customer_service_id = cbtr.business_id and cbtr.business_type = 1
        where ccspm.customer_service_id in
        <foreach item="customerServiceId" collection="customerServiceIds" open="(" separator="," close=")">
            #{customerServiceId}
        </foreach>
        <if test="startPeriod != null">
            and ccspm.period &gt;= ${startPeriod}
        </if>
        <if test="endPeriod != null">
            and ccspm.period &lt;= ${endPeriod}
        </if>
    </insert>

    <update id="updateCBusinessTagRelation" parameterType="com.bxm.customer.domain.CBusinessTagRelation">
        update c_business_tag_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="tagParamValue != null">tag_param_value = #{tagParamValue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCBusinessTagRelationById" parameterType="Long">
        delete from c_business_tag_relation where id = #{id}
    </delete>

    <delete id="deleteCBusinessTagRelationByIds" parameterType="String">
        delete from c_business_tag_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="removeNoIncomeTagByBusinessIdsAndBusinessType">
        delete from c_business_tag_relation WHERE business_type = #{businessType} AND business_id IN
         <foreach collection="businessIds" separator="," item="item" close=")" open="(">
             #{item}
         </foreach>
         AND tag_id IN (1,13)
    </delete>
</mapper>