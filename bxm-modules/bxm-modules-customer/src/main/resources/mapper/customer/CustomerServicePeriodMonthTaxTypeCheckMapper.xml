<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServicePeriodMonthTaxTypeCheckMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck" id="CustomerServicePeriodMonthTaxTypeCheckResult">
        <result property="id"    column="id"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="reportType"    column="report_type"    />
        <result property="taxType"    column="tax_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServicePeriodMonthTaxTypeCheckVo">
        select id, customer_service_period_month_id, report_type, tax_type, create_by, create_time, update_by, update_time from c_customer_service_period_month_tax_type_check
    </sql>

    <select id="selectCustomerServicePeriodMonthTaxTypeCheckList" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck" resultMap="CustomerServicePeriodMonthTaxTypeCheckResult">
        <include refid="selectCustomerServicePeriodMonthTaxTypeCheckVo"/>
        <where>  
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="reportType != null "> and report_type = #{reportType}</if>
            <if test="taxType != null  and taxType != ''"> and tax_type = #{taxType}</if>
        </where>
    </select>
    
    <select id="selectCustomerServicePeriodMonthTaxTypeCheckById" parameterType="Long" resultMap="CustomerServicePeriodMonthTaxTypeCheckResult">
        <include refid="selectCustomerServicePeriodMonthTaxTypeCheckVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServicePeriodMonthTaxTypeCheck" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_period_month_tax_type_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="reportType != null">report_type,</if>
            <if test="taxType != null and taxType != ''">tax_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="taxType != null and taxType != ''">#{taxType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodTaxCheckByCustomer">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from c_customer_service ccs
                 join c_customer_service_period_month cspm on ccs.id = cspm.customer_service_id
                 join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
            AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
            AND cctt.tax_type not like '社保%'
        where cspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodTaxCheckBySmallCustomer">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 1) a
                 join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
                 join c_customer_tax_type_check cctt on cctt.customer_service_id = 0
            AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
            AND cctt.tax_type not like '社保%'
        where cspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodTaxCheckByCommlyCustomer">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 2) a
                 join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
                 join c_customer_tax_type_check cctt on cctt.customer_service_id = -1
            AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
            AND cctt.tax_type not like '社保%'
        where cspm.period = ${nowPeriod}
    </insert>
    <insert id="saveNewPeriodTaxCheckByByCustomerServiceIds">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from c_customer_service ccs
        join c_customer_service_period_month cspm on ccs.id = cspm.customer_service_id
        join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
        AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
        AND cctt.tax_type not like '社保%'
        where cspm.customer_service_id in
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="periodStart != null">
            and cspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and cspm.period &lt;= ${periodEnd}
        </if>
    </insert>
    <insert id="saveNewPeriodTaxCheckBySmallCustomerAndCustomerServiceId">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 1) a
        join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
        join c_customer_tax_type_check cctt on cctt.customer_service_id = 0
        AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
        AND cctt.tax_type not like '社保%'
        where cspm.customer_service_id in
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="periodStart != null">
            and cspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and cspm.period &lt;= ${periodEnd}
        </if>
    </insert>
    <insert id="saveNewPeriodTaxCheckByCommlyCustomerAndCustomerServiceId">
        insert into c_customer_service_period_month_tax_type_check
        select null,cspm.id,cctt.report_type,cctt.tax_type,'',now(),'',now()
        from (
                 select ccs.*
                 from c_customer_service ccs left join c_customer_tax_type_check cctt on ccs.id = cctt.customer_service_id
                 where cctt.id is null and ccs.tax_type = 2) a
        join c_customer_service_period_month cspm on a.id = cspm.customer_service_id
        join c_customer_tax_type_check cctt on cctt.customer_service_id = -1
        AND ((cspm.period % 100 IN (1,2,4,5,7,8,10,11) AND cctt.report_type in (1, 4)) OR (cspm.period % 100 IN (3, 9) AND cctt.report_type IN (1, 2, 4)) OR (cspm.period % 100 = 6 AND cctt.report_type IN (1, 2, 4, 5)) OR (cspm.period % 100 = 12 AND cctt.report_type IN (1, 2, 3, 4, 5)))
        AND cctt.tax_type not like '社保%'
        where cspm.customer_service_id in
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="periodStart != null">
            and cspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and cspm.period &lt;= ${periodEnd}
        </if>
    </insert>

    <update id="updateCustomerServicePeriodMonthTaxTypeCheck" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck">
        update c_customer_service_period_month_tax_type_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="taxType != null and taxType != ''">tax_type = #{taxType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServicePeriodMonthTaxTypeCheckById" parameterType="Long">
        delete from c_customer_service_period_month_tax_type_check where id = #{id}
    </delete>

    <delete id="deleteCustomerServicePeriodMonthTaxTypeCheckByIds" parameterType="String">
        delete from c_customer_service_period_month_tax_type_check where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>