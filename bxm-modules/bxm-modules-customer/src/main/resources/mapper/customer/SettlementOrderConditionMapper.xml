<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.SettlementOrderConditionMapper">
    
    <resultMap type="com.bxm.customer.domain.SettlementOrderCondition" id="SettlementOrderConditionResult">
        <result property="id"    column="id"    />
        <result property="settlementOrderId"    column="settlement_order_id"    />
        <result property="conditionType"    column="condition_type"    />
        <result property="conditionTypeName"    column="condition_type_name"    />
        <result property="conditionValue"    column="condition_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSettlementOrderConditionVo">
        select id, settlement_order_id, condition_type, condition_type_name, condition_value, create_by, create_time, update_by, update_time from c_settlement_order_condition
    </sql>

    <select id="selectSettlementOrderConditionList" parameterType="com.bxm.customer.domain.SettlementOrderCondition" resultMap="SettlementOrderConditionResult">
        <include refid="selectSettlementOrderConditionVo"/>
        <where>  
            <if test="settlementOrderId != null "> and settlement_order_id = #{settlementOrderId}</if>
            <if test="conditionType != null  and conditionType != ''"> and condition_type = #{conditionType}</if>
            <if test="conditionTypeName != null  and conditionTypeName != ''"> and condition_type_name like concat('%', #{conditionTypeName}, '%')</if>
            <if test="conditionValue != null  and conditionValue != ''"> and condition_value = #{conditionValue}</if>
        </where>
    </select>
    
    <select id="selectSettlementOrderConditionById" parameterType="Long" resultMap="SettlementOrderConditionResult">
        <include refid="selectSettlementOrderConditionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSettlementOrderCondition" parameterType="com.bxm.customer.domain.SettlementOrderCondition" useGeneratedKeys="true" keyProperty="id">
        insert into c_settlement_order_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="settlementOrderId != null">settlement_order_id,</if>
            <if test="conditionType != null and conditionType != ''">condition_type,</if>
            <if test="conditionTypeName != null and conditionTypeName != ''">condition_type_name,</if>
            <if test="conditionValue != null">condition_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="settlementOrderId != null">#{settlementOrderId},</if>
            <if test="conditionType != null and conditionType != ''">#{conditionType},</if>
            <if test="conditionTypeName != null and conditionTypeName != ''">#{conditionTypeName},</if>
            <if test="conditionValue != null">#{conditionValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSettlementOrderCondition" parameterType="com.bxm.customer.domain.SettlementOrderCondition">
        update c_settlement_order_condition
        <trim prefix="SET" suffixOverrides=",">
            <if test="settlementOrderId != null">settlement_order_id = #{settlementOrderId},</if>
            <if test="conditionType != null and conditionType != ''">condition_type = #{conditionType},</if>
            <if test="conditionTypeName != null and conditionTypeName != ''">condition_type_name = #{conditionTypeName},</if>
            <if test="conditionValue != null">condition_value = #{conditionValue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettlementOrderConditionById" parameterType="Long">
        delete from c_settlement_order_condition where id = #{id}
    </delete>

    <delete id="deleteSettlementOrderConditionByIds" parameterType="String">
        delete from c_settlement_order_condition where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>