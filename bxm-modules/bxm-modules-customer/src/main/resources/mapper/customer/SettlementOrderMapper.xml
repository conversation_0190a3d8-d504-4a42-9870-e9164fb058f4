<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.SettlementOrderMapper">
    
    <resultMap type="com.bxm.customer.domain.SettlementOrder" id="SettlementOrderResult">
        <result property="id"    column="id"    />
        <result property="billNo"    column="bill_no"    />
        <result property="settlementBatchNo"    column="settlement_batch_no"    />
        <result property="settlementTitle"    column="settlement_title"    />
        <result property="businessTopDeptId"    column="business_top_dept_id"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="settlementType"    column="settlement_type"    />
        <result property="isSupplement"    column="is_supplement"    />
        <result property="unit"    column="unit"    />
        <result property="dataCount"    column="data_count"    />
        <result property="price"    column="price"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="discountPrice"    column="discount_price"    />
        <result property="settlementPrice"    column="settlement_price"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="isWaitForEdit"    column="is_wait_for_edit"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSettlementOrderVo">
        select id, bill_no, settlement_batch_no, settlement_title, business_top_dept_id, business_dept_id, settlement_type, is_supplement, unit, data_count, price, total_price, discount_price, settlement_price, remark, status, is_wait_for_edit, is_del, create_by, create_time, update_by, update_time from c_settlement_order
    </sql>

    <select id="selectSettlementOrderList" parameterType="com.bxm.customer.domain.SettlementOrder" resultMap="SettlementOrderResult">
        <include refid="selectSettlementOrderVo"/>
        <where>  
            <if test="billNo != null  and billNo != ''"> and bill_no = #{billNo}</if>
            <if test="settlementBatchNo != null  and settlementBatchNo != ''"> and settlement_batch_no = #{settlementBatchNo}</if>
            <if test="settlementTitle != null  and settlementTitle != ''"> and settlement_title = #{settlementTitle}</if>
            <if test="businessTopDeptId != null "> and business_top_dept_id = #{businessTopDeptId}</if>
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="settlementType != null "> and settlement_type = #{settlementType}</if>
            <if test="isSupplement != null "> and is_supplement = #{isSupplement}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="dataCount != null "> and data_count = #{dataCount}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="totalPrice != null "> and total_price = #{totalPrice}</if>
            <if test="discountPrice != null "> and discount_price = #{discountPrice}</if>
            <if test="settlementPrice != null "> and settlement_price = #{settlementPrice}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isWaitForEdit != null "> and is_wait_for_edit = #{isWaitForEdit}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectSettlementOrderById" parameterType="Long" resultMap="SettlementOrderResult">
        <include refid="selectSettlementOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSettlementOrder" parameterType="com.bxm.customer.domain.SettlementOrder" useGeneratedKeys="true" keyProperty="id">
        insert into c_settlement_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billNo != null">bill_no,</if>
            <if test="settlementBatchNo != null">settlement_batch_no,</if>
            <if test="settlementTitle != null">settlement_title,</if>
            <if test="businessTopDeptId != null">business_top_dept_id,</if>
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="settlementType != null">settlement_type,</if>
            <if test="isSupplement != null">is_supplement,</if>
            <if test="unit != null">unit,</if>
            <if test="dataCount != null">data_count,</if>
            <if test="price != null">price,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="discountPrice != null">discount_price,</if>
            <if test="settlementPrice != null">settlement_price,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="isWaitForEdit != null">is_wait_for_edit,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billNo != null">#{billNo},</if>
            <if test="settlementBatchNo != null">#{settlementBatchNo},</if>
            <if test="settlementTitle != null">#{settlementTitle},</if>
            <if test="businessTopDeptId != null">#{businessTopDeptId},</if>
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="settlementType != null">#{settlementType},</if>
            <if test="isSupplement != null">#{isSupplement},</if>
            <if test="unit != null">#{unit},</if>
            <if test="dataCount != null">#{dataCount},</if>
            <if test="price != null">#{price},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="discountPrice != null">#{discountPrice},</if>
            <if test="settlementPrice != null">#{settlementPrice},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="isWaitForEdit != null">#{isWaitForEdit},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSettlementOrder" parameterType="com.bxm.customer.domain.SettlementOrder">
        update c_settlement_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="billNo != null">bill_no = #{billNo},</if>
            <if test="settlementBatchNo != null">settlement_batch_no = #{settlementBatchNo},</if>
            <if test="settlementTitle != null">settlement_title = #{settlementTitle},</if>
            <if test="businessTopDeptId != null">business_top_dept_id = #{businessTopDeptId},</if>
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="settlementType != null">settlement_type = #{settlementType},</if>
            <if test="isSupplement != null">is_supplement = #{isSupplement},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="dataCount != null">data_count = #{dataCount},</if>
            <if test="price != null">price = #{price},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="discountPrice != null">discount_price = #{discountPrice},</if>
            <if test="settlementPrice != null">settlement_price = #{settlementPrice},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isWaitForEdit != null">is_wait_for_edit = #{isWaitForEdit},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettlementOrderById" parameterType="Long">
        delete from c_settlement_order where id = #{id}
    </delete>

    <delete id="deleteSettlementOrderByIds" parameterType="String">
        delete from c_settlement_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>