<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerSysAccountMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerSysAccount" id="CustomerSysAccountResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="sysType"    column="sys_type"    />
        <result property="account"    column="account"    />
        <result property="password"    column="password"    />
        <result property="contact"    column="contact"    />
        <result property="contactMobile"    column="contact_mobile"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerSysAccountVo">
        select id, customer_service_id, sys_type, account, password, contact, contact_mobile, is_del, create_by, create_time, update_by, update_time from c_customer_sys_account
    </sql>

    <select id="selectCustomerSysAccountList" parameterType="com.bxm.customer.domain.CustomerSysAccount" resultMap="CustomerSysAccountResult">
        <include refid="selectCustomerSysAccountVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="sysType != null "> and sys_type = #{sysType}</if>
            <if test="account != null  and account != ''"> and account = #{account}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="contactMobile != null  and contactMobile != ''"> and contact_mobile = #{contactMobile}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCustomerSysAccountById" parameterType="Long" resultMap="CustomerSysAccountResult">
        <include refid="selectCustomerSysAccountVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerSysAccount" parameterType="com.bxm.customer.domain.CustomerSysAccount" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_sys_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="sysType != null">sys_type,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="contact != null">contact,</if>
            <if test="contactMobile != null">contact_mobile,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="sysType != null">#{sysType},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="contact != null">#{contact},</if>
            <if test="contactMobile != null">#{contactMobile},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerSysAccount" parameterType="com.bxm.customer.domain.CustomerSysAccount">
        update c_customer_sys_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="sysType != null">sys_type = #{sysType},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="contactMobile != null">contact_mobile = #{contactMobile},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerSysAccountById" parameterType="Long">
        delete from c_customer_sys_account where id = #{id}
    </delete>

    <delete id="deleteCustomerSysAccountByIds" parameterType="String">
        delete from c_customer_sys_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>