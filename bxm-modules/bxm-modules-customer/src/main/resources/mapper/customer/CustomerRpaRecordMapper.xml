<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerRpaRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerRpaRecord" id="CustomerRpaRecordResult">
        <result property="id"    column="id"    />
        <result property="rpaType"    column="rpa_type"    />
        <result property="operType"    column="oper_type"    />
        <result property="period"    column="period"    />
        <result property="deptId"    column="dept_id"    />
        <result property="dataFileUrl"    column="data_file_url"    />
        <result property="attachFileUrl"    column="attach_file_url"    />
        <result property="status"    column="status"    />
        <result property="hasErrorData"    column="has_error_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerRpaRecordVo">
        select id, rpa_type, oper_type, period, dept_id, data_file_url, attach_file_url, status, has_error_data, create_by, create_time, update_by, update_time from c_customer_rpa_record
    </sql>

    <select id="selectCustomerRpaRecordList" parameterType="com.bxm.customer.domain.CustomerRpaRecord" resultMap="CustomerRpaRecordResult">
        <include refid="selectCustomerRpaRecordVo"/>
        <where>  
            <if test="rpaType != null "> and rpa_type = #{rpaType}</if>
            <if test="operType != null "> and oper_type = #{operType}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="dataFileUrl != null  and dataFileUrl != ''"> and data_file_url = #{dataFileUrl}</if>
            <if test="attachFileUrl != null  and attachFileUrl != ''"> and attach_file_url = #{attachFileUrl}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="hasErrorData != null "> and has_error_data = #{hasErrorData}</if>
        </where>
    </select>
    
    <select id="selectCustomerRpaRecordById" parameterType="Long" resultMap="CustomerRpaRecordResult">
        <include refid="selectCustomerRpaRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerRpaRecord" parameterType="com.bxm.customer.domain.CustomerRpaRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_rpa_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rpaType != null">rpa_type,</if>
            <if test="operType != null">oper_type,</if>
            <if test="period != null">period,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="dataFileUrl != null">data_file_url,</if>
            <if test="attachFileUrl != null">attach_file_url,</if>
            <if test="status != null">status,</if>
            <if test="hasErrorData != null">has_error_data,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rpaType != null">#{rpaType},</if>
            <if test="operType != null">#{operType},</if>
            <if test="period != null">#{period},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="dataFileUrl != null">#{dataFileUrl},</if>
            <if test="attachFileUrl != null">#{attachFileUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="hasErrorData != null">#{hasErrorData},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerRpaRecord" parameterType="com.bxm.customer.domain.CustomerRpaRecord">
        update c_customer_rpa_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="rpaType != null">rpa_type = #{rpaType},</if>
            <if test="operType != null">oper_type = #{operType},</if>
            <if test="period != null">period = #{period},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="dataFileUrl != null">data_file_url = #{dataFileUrl},</if>
            <if test="attachFileUrl != null">attach_file_url = #{attachFileUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hasErrorData != null">has_error_data = #{hasErrorData},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerRpaRecordById" parameterType="Long">
        delete from c_customer_rpa_record where id = #{id}
    </delete>

    <delete id="deleteCustomerRpaRecordByIds" parameterType="String">
        delete from c_customer_rpa_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>