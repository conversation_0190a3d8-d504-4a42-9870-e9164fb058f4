<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BillSettlementOrderRelationMapper">
    
    <resultMap type="com.bxm.customer.domain.BillSettlementOrderRelation" id="BillSettlementOrderRelationResult">
        <result property="id"    column="id"    />
        <result property="billId"    column="bill_id"    />
        <result property="settlementOrderId"    column="settlement_order_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBillSettlementOrderRelationVo">
        select id, bill_id, settlement_order_id, create_by, create_time, update_by, update_time from c_bill_settlement_order_relation
    </sql>

    <select id="selectBillSettlementOrderRelationList" parameterType="com.bxm.customer.domain.BillSettlementOrderRelation" resultMap="BillSettlementOrderRelationResult">
        <include refid="selectBillSettlementOrderRelationVo"/>
        <where>  
            <if test="billId != null "> and bill_id = #{billId}</if>
            <if test="settlementOrderId != null "> and settlement_order_id = #{settlementOrderId}</if>
        </where>
    </select>
    
    <select id="selectBillSettlementOrderRelationById" parameterType="Long" resultMap="BillSettlementOrderRelationResult">
        <include refid="selectBillSettlementOrderRelationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBillSettlementOrderRelation" parameterType="com.bxm.customer.domain.BillSettlementOrderRelation" useGeneratedKeys="true" keyProperty="id">
        insert into c_bill_settlement_order_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="settlementOrderId != null">settlement_order_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId},</if>
            <if test="settlementOrderId != null">#{settlementOrderId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBillSettlementOrderRelation" parameterType="com.bxm.customer.domain.BillSettlementOrderRelation">
        update c_bill_settlement_order_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="settlementOrderId != null">settlement_order_id = #{settlementOrderId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillSettlementOrderRelationById" parameterType="Long">
        delete from c_bill_settlement_order_relation where id = #{id}
    </delete>

    <delete id="deleteBillSettlementOrderRelationByIds" parameterType="String">
        delete from c_bill_settlement_order_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>