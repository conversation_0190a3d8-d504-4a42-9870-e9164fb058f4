<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.WorkOrderMapper">

    <select id="workOrderList" resultType="com.bxm.customer.domain.dto.workOrder.WorkOrderDTO">
        SELECT
            wo.id AS id,
            wo.work_order_type as workOrderType,
            wo.title AS title,
            wo.customer_service_id as customerServiceId,
            cs.business_dept_id as businessDeptId,
            cs.advisor_dept_id as advisorDeptId,
            cs.accounting_top_dept_id as accountingTopDeptId,
            cs.accounting_dept_id as accountingDeptId,
            wo.period_start as periodStart,
            wo.period_end as periodEnd,
            wo.initiate_user_nickname AS initiateUserNickname,
            wo.initiate_user_id AS initiateUserId,
            wo.initiate_dept_id as initiateDeptId,
            wo.create_time AS createTime,
            wo.undertake_dept_id AS undertakeDeptId,
            wo.undertake_user_id AS undertakeUserId,
            wo.undertake_user_nickname AS undertakeUserNickname,
            wo.`status` AS `status`,
            wo.remark as remark,
            wo.ddl AS ddl,
            wo.finish_time as finishTime,
            wo.last_oper_name AS lastOperName,
            wo.last_oper_type AS lastOperType,
            wo.last_oper_time AS lastOperTime,
            wo.current_user_type AS currentUserType,
            wo.current_user_id AS currentUserId,
            wo.current_user_nickname AS currentUserNickname,
            wo.current_dept_id AS currentDeptId
        FROM c_work_order wo LEFT JOIN c_customer_service cs ON wo.customer_service_id = cs.id AND cs.is_del = 0
        <where>
            wo.is_del = 0
            <if test="vo.tabType != null">
                <if test="vo.tabType == 1">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.initiate_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="vo.tabType == 2">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is not null
                </if>
                <if test="vo.tabType == 3">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is null
                </if>
                <if test="vo.tabType == 4">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.current_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.`status` in (1, 4)
                </if>
            </if>
            <if test="vo.currentDealUserType != null">
                <if test="vo.currentDealUserType == 1">
                    <if test="userDept.isAdmin == false">
                        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                            and wo.current_dept_id in
                            <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        <if test="userDept.deptIds == null or userDept.deptIds.size == 0">
                            and 1 = 0
                        </if>
                    </if>
                </if>
                <if test="vo.currentDealUserType == 2">
                    <if test="userDept.isAdmin == false">
                        <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                            and wo.current_dept_id not in
                            <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        <if test="userDept.deptIds == null or userDept.deptIds.size == 0">
                            and 1 = 1
                        </if>
                    </if>
                </if>
            </if>
            <if test="vo.title != null and vo.title != ''">
                and wo.title like concat('%', #{vo.title}, '%')
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and (cs.customer_name like concat('%', #{vo.customerName}, '%') or cs.customer_company_name like concat('%', #{vo.customerName}, '%') or cs.credit_code like concat('%', #{vo.customerName}, '%'))
            </if>
            <if test="vo.status != null">
                and wo.`status` = #{vo.status}
            </if>
            <if test="vo.initiateUserNickname != null and vo.initiateUserNickname != ''">
                and wo.initiate_user_nickname like concat('%', #{vo.initiateUserNickname}, '%')
            </if>
            <if test="vo.undertakeUserNickname != null and vo.undertakeUserNickname != ''">
                and wo.undertake_user_nickname like concat('%', #{vo.undertakeUserNickname}, '%')
            </if>
            <if test="vo.initiateDeptIdList != null and vo.initiateDeptIdList != ''">
                and wo.initiate_dept_id in (${vo.initiateDeptIdList})
            </if>
            <if test="vo.undertakeDeptIdList != null and vo.undertakeDeptIdList != ''">
                and wo.undertake_dept_id in (${vo.undertakeDeptIdList})
            </if>
            <if test="vo.currentUserNickname != null and vo.currentUserNickname != ''">
                and wo.current_user_nickname like concat('%', #{vo.currentUserNickname}, '%')
            </if>
            <if test="vo.lastOperName != null and vo.lastOperName != ''">
                and wo.last_oper_name like concat('%', #{vo.lastOperName}, '%')
            </if>
            <if test="vo.lastOperTimeStart != null and vo.lastOperTimeStart != ''">
                and wo.last_oper_time &gt;= #{vo.lastOperTimeStart}
            </if>
            <if test="vo.lastOperTimeEnd != null and vo.lastOperTimeEnd != ''">
                and wo.last_oper_time &lt;= #{vo.lastOperTimeEnd}
            </if>
            <if test="vo.createTimeStart != null and vo.createTimeStart != ''">
                and wo.create_time &gt;= #{vo.createTimeStart}
            </if>
            <if test="vo.createTimeEnd != null and vo.createTimeEnd != ''">
                and wo.create_time &lt;= #{vo.createTimeEnd}
            </if>
            <if test="vo.workOrderTypes != null and vo.workOrderTypes != ''">
                and wo.work_order_type in (${vo.workOrderTypes})
            </if>
            <if test="vo.ddlStart != null and vo.ddlStart != ''">
                and wo.ddl is not null and wo.ddl &gt;= #{vo.ddlStart}
            </if>
            <if test="vo.ddlEnd != null and vo.ddlEnd != ''">
                and wo.ddl is not null and wo.ddl &lt;= #{vo.ddlEnd}
            </if>
        </where>
        order by wo.id desc
    </select>
    <select id="workOrderCountStatistic" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM c_work_order wo
        <where>
            wo.is_del = 0
            <if test="type != null">
                <if test="type == 1">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.initiate_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.status in (1, 4)
                </if>
                <if test="type == 2">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is not null
                    and wo.status in (1, 4)
                </if>
                <if test="type == 3">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is null
                </if>
                <if test="type == 4">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.current_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.`status` in (1, 4)
                </if>
            </if>
        </where>
    </select>
    <select id="workOrderInitiateDeptList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        wo.initiate_dept_id as deptId,
        count(1) as dataCount
        FROM c_work_order wo
        <where>
            wo.is_del = 0 and wo.initiate_dept_id is not null
            <if test="tabType != null">
                <if test="tabType == 1">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.initiate_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="tabType == 2">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is not null
                </if>
                <if test="tabType == 3">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is null
                </if>
                <if test="tabType == 4">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.current_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.`status` = 1
                </if>
            </if>
        </where>
        group by wo.initiate_dept_id
    </select>
    <select id="workOrderUndertakeDeptList" resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        SELECT
        wo.undertake_dept_id as deptId,
        count(1) as dataCount
        FROM c_work_order wo
        <where>
            wo.is_del = 0 and wo.undertake_dept_id is not null
            <if test="tabType != null">
                <if test="tabType == 1">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.initiate_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="tabType == 2">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is not null
                </if>
                <if test="tabType == 3">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.undertake_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.undertake_user_id is null
                </if>
                <if test="tabType == 4">
                    <if test="userDept.deptIds != null and userDept.deptIds.size > 0">
                        and wo.current_dept_id in
                        <foreach collection="userDept.deptIds" open="(" close=")" item="item" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    and wo.`status` = 1
                </if>
            </if>
        </where>
        group by wo.undertake_dept_id
    </select>
    <select id="workOrderListForXm" resultType="com.bxm.customer.domain.dto.workOrder.WorkOrderDTO">
        SELECT
        wo.id AS id,
        wo.work_order_type as workOrderType,
        wo.title AS title,
        wo.customer_service_id as customerServiceId,
        cs.business_dept_id as businessDeptId,
        cs.advisor_dept_id as advisorDeptId,
        cs.accounting_top_dept_id as accountingTopDeptId,
        cs.accounting_dept_id as accountingDeptId,
        wo.period_start as periodStart,
        wo.period_end as periodEnd,
        wo.initiate_user_nickname AS initiateUserNickname,
        wo.initiate_user_id AS initiateUserId,
        wo.initiate_dept_id as initiateDeptId,
        wo.create_time AS createTime,
        wo.undertake_dept_id AS undertakeDeptId,
        wo.undertake_user_id AS undertakeUserId,
        wo.undertake_user_nickname AS undertakeUserNickname,
        wo.`status` AS `status`,
        wo.remark as remark,
        wo.ddl AS ddl,
        wo.last_oper_name AS lastOperName,
        wo.last_oper_type AS lastOperType,
        wo.last_oper_time AS lastOperTime,
        wo.current_user_type AS currentUserType,
        wo.current_user_id AS currentUserId,
        wo.current_user_nickname AS currentUserNickname,
        wo.current_dept_id AS currentDeptId
        FROM c_work_order wo LEFT JOIN c_customer_service cs ON wo.customer_service_id = cs.id AND cs.is_del = 0
        <where>
            wo.is_del = 0
            <if test="vo.title != null and vo.title != ''">
                and wo.title like concat('%', #{vo.title}, '%')
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and (cs.customer_name like concat('%', #{vo.customerName}, '%') or cs.customer_company_name like concat('%', #{vo.customerName}, '%') or cs.credit_code like concat('%', #{vo.customerName}, '%'))
            </if>
            <if test="vo.status != null">
                and wo.`status` = #{vo.status}
            </if>
            <if test="vo.searchType != null and vo.searchType != ''">
                <if test="vo.searchType == 'WAIT_DEAL'">
                    and wo.current_user_id = #{vo.userId}
                </if>
                <if test="vo.searchType == 'WAIT_UNDERTAKE'">
                    and wo.undertake_user_id is null and wo.undertake_dept_id in
                    <foreach collection="deptIds" separator="," item="item" close=")" open="(">
                        #{item}
                    </foreach>
                </if>
                <if test="vo.searchType == 'INITIATED'">
                    and wo.initiate_user_id = #{vo.userId}
                </if>
            </if>
        </where>
        order by wo.id desc
    </select>
</mapper>