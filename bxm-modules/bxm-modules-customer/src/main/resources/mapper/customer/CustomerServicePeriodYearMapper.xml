<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServicePeriodYearMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServicePeriodYear" id="CustomerServicePeriodYearResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="period"    column="period"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServicePeriodYearVo">
        select id, customer_service_id, period, create_by, create_time, update_by, update_time from c_customer_service_period_year
    </sql>

    <select id="selectCustomerServicePeriodYearList" parameterType="com.bxm.customer.domain.CustomerServicePeriodYear" resultMap="CustomerServicePeriodYearResult">
        <include refid="selectCustomerServicePeriodYearVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="period != null "> and period = #{period}</if>
        </where>
    </select>
    
    <select id="selectCustomerServicePeriodYearById" parameterType="Long" resultMap="CustomerServicePeriodYearResult">
        <include refid="selectCustomerServicePeriodYearVo"/>
        where id = #{id}
    </select>
    <select id="customerServiceYearAccountingCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
            cs.accounting_dept_id as deptId,
            count(1) as dataCount
            from c_customer_service_period_year ccspy join c_customer_service cs on ccspy.customer_service_id = cs.id and cs.is_del = 0
        <where>
            cs.accounting_dept_id is not null
            <if test="userDeptDTO.isAdmin == false and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cs.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cs.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        group by cs.accounting_dept_id
    </select>
    <select id="customerServiceYearAdvisorCountList"
            resultType="com.bxm.customer.domain.dto.CommonDeptCountDTO">
        select
        cs.advisor_dept_id as deptId,
        count(1) as dataCount
        from c_customer_service_period_year ccspy join c_customer_service cs on ccspy.customer_service_id = cs.id and cs.is_del = 0
        <where>
            cs.advisor_dept_id is not null
            <if test="userDeptDTO.isAdmin == false and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cs.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cs.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        group by cs.advisor_dept_id
    </select>

    <insert id="insertCustomerServicePeriodYear" parameterType="com.bxm.customer.domain.CustomerServicePeriodYear" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_period_year
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="period != null">period,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="period != null">#{period},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodYear">
        insert into c_customer_service_period_year
        select null,id,${nowYear},null,null,null,null,null,#{nowPeriod},null,'',now(),'',now() from c_customer_service where is_del = 0 and (end_account_period is null or end_account_period >= ${nowPeriod})
                                                                        and id not in (select distinct customer_service_id from c_customer_service_period_year where period = ${nowYear})
    </insert>

    <update id="updateCustomerServicePeriodYear" parameterType="com.bxm.customer.domain.CustomerServicePeriodYear">
        update c_customer_service_period_year
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateLastInAccountId">
        UPDATE
            c_customer_service_period_year left JOIN (SELECT cca2.id,a.id AS yearId FROM c_customer_service_cashier_accounting cca2 JOIN (
            SELECT cy.id,cy.customer_service_id,MAX(cca.period) AS maxPeriod FROM
            c_customer_service_period_year cy left JOIN
            c_customer_service_cashier_accounting cca ON cy.customer_service_id = cca.customer_service_id AND cy.period = FLOOR(cca.period / 100) AND cca.`type` = 1 AND cca.is_del = 0
            AND (
            cca.major_income_total IS NOT NULL OR
            cca.major_cost_total IS NOT NULL OR
            cca.profit_total IS NOT NULL OR
            cca.prior_year_expense_increase IS NOT NULL OR
            cca.tax_report_count IS NOT NULL OR
            cca.tax_report_salary_total IS NOT NULL
            )
            GROUP BY cy.id) a ON cca2.customer_service_id = a.customer_service_id AND cca2.period = a.maxPeriod AND cca2.`type` = 1) maxPeriod ON c_customer_service_period_year.id = maxPeriod.yearId
            SET c_customer_service_period_year.last_in_account_id = maxPeriod.id WHERE c_customer_service_period_year.id > 0
    </update>

    <delete id="deleteCustomerServicePeriodYearById" parameterType="Long">
        delete from c_customer_service_period_year where id = #{id}
    </delete>

    <delete id="deleteCustomerServicePeriodYearByIds" parameterType="String">
        delete from c_customer_service_period_year where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>