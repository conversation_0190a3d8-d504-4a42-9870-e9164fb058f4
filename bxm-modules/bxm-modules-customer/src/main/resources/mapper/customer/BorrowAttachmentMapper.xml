<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BorrowAttachmentMapper">
    
    <resultMap type="com.bxm.customer.domain.BorrowAttachment" id="BorrowAttachmentResult">
        <result property="id"    column="id"    />
        <result property="borrowOrderId"    column="borrow_order_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBorrowAttachmentVo">
        select id, borrow_order_id, file_name, file_url, is_del, create_by, create_time, update_by, update_time from c_borrow_attachment
    </sql>

    <select id="selectBorrowAttachmentList" parameterType="com.bxm.customer.domain.BorrowAttachment" resultMap="BorrowAttachmentResult">
        <include refid="selectBorrowAttachmentVo"/>
        <where>  
            <if test="borrowOrderId != null "> and borrow_order_id = #{borrowOrderId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectBorrowAttachmentById" parameterType="Long" resultMap="BorrowAttachmentResult">
        <include refid="selectBorrowAttachmentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBorrowAttachment" parameterType="com.bxm.customer.domain.BorrowAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into c_borrow_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="borrowOrderId != null">borrow_order_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="borrowOrderId != null">#{borrowOrderId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBorrowAttachment" parameterType="com.bxm.customer.domain.BorrowAttachment">
        update c_borrow_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="borrowOrderId != null">borrow_order_id = #{borrowOrderId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBorrowAttachmentById" parameterType="Long">
        delete from c_borrow_attachment where id = #{id}
    </delete>

    <delete id="deleteBorrowAttachmentByIds" parameterType="String">
        delete from c_borrow_attachment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>