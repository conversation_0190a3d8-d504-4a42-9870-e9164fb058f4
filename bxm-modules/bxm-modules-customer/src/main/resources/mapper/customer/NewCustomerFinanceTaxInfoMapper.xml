<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.NewCustomerFinanceTaxInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.NewCustomerFinanceTaxInfo" id="NewCustomerFinanceTaxInfoResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="accountingSystem"    column="accounting_system"    />
        <result property="financeRecord"    column="finance_record"    />
        <result property="reportSent"    column="report_sent"    />
        <result property="lastAccountMonth"    column="last_account_month"    />
        <result property="notAccountReason"    column="not_account_reason"    />
        <result property="otherRemarks"    column="other_remarks"    />
        <result property="incomeMain"    column="income_main"    />
        <result property="incomeOther"    column="income_other"    />
        <result property="cost"    column="cost"    />
        <result property="expense"    column="expense"    />
        <result property="profit"    column="profit"    />
        <result property="offsetLoss"    column="offset_loss"    />
        <result property="totalSalary"    column="total_salary"    />
        <result property="welfareFee"    column="welfare_fee"    />
        <result property="entertainmentFee"    column="entertainment_fee"    />
        <result property="otherAdjustment"    column="other_adjustment"    />
        <result property="taxMethod"    column="tax_method"    />
        <result property="ezTaxAccount"    column="ez_tax_account"    />
        <result property="taxDisk"    column="tax_disk"    />
        <result property="taxPassword"    column="tax_password"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNewCustomerFinanceTaxInfoVo">
        select id, customer_id, accounting_system, finance_record, report_sent, last_account_month, not_account_reason, other_remarks, income_main, income_other, cost, expense, profit, offset_loss, total_salary, welfare_fee, entertainment_fee, other_adjustment, tax_method, ez_tax_account, tax_disk, tax_password, create_by, create_time, update_by, update_time from c_new_customer_finance_tax_info
    </sql>

    <select id="selectNewCustomerFinanceTaxInfoList" parameterType="com.bxm.customer.domain.NewCustomerFinanceTaxInfo" resultMap="NewCustomerFinanceTaxInfoResult">
        <include refid="selectNewCustomerFinanceTaxInfoVo"/>
        <where>  
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="accountingSystem != null "> and accounting_system = #{accountingSystem}</if>
            <if test="financeRecord != null "> and finance_record = #{financeRecord}</if>
            <if test="reportSent != null "> and report_sent = #{reportSent}</if>
            <if test="lastAccountMonth != null "> and last_account_month = #{lastAccountMonth}</if>
            <if test="notAccountReason != null  and notAccountReason != ''"> and not_account_reason = #{notAccountReason}</if>
            <if test="otherRemarks != null  and otherRemarks != ''"> and other_remarks = #{otherRemarks}</if>
            <if test="incomeMain != null "> and income_main = #{incomeMain}</if>
            <if test="incomeOther != null "> and income_other = #{incomeOther}</if>
            <if test="cost != null "> and cost = #{cost}</if>
            <if test="expense != null "> and expense = #{expense}</if>
            <if test="profit != null "> and profit = #{profit}</if>
            <if test="offsetLoss != null "> and offset_loss = #{offsetLoss}</if>
            <if test="totalSalary != null "> and total_salary = #{totalSalary}</if>
            <if test="welfareFee != null "> and welfare_fee = #{welfareFee}</if>
            <if test="entertainmentFee != null "> and entertainment_fee = #{entertainmentFee}</if>
            <if test="otherAdjustment != null "> and other_adjustment = #{otherAdjustment}</if>
            <if test="taxMethod != null "> and tax_method = #{taxMethod}</if>
            <if test="ezTaxAccount != null "> and ez_tax_account = #{ezTaxAccount}</if>
            <if test="taxDisk != null "> and tax_disk = #{taxDisk}</if>
            <if test="taxPassword != null  and taxPassword != ''"> and tax_password = #{taxPassword}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectNewCustomerFinanceTaxInfoById" parameterType="Long" resultMap="NewCustomerFinanceTaxInfoResult">
        <include refid="selectNewCustomerFinanceTaxInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNewCustomerFinanceTaxInfo" parameterType="com.bxm.customer.domain.NewCustomerFinanceTaxInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_new_customer_finance_tax_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="accountingSystem != null">accounting_system,</if>
            <if test="financeRecord != null">finance_record,</if>
            <if test="reportSent != null">report_sent,</if>
            <if test="lastAccountMonth != null">last_account_month,</if>
            <if test="notAccountReason != null">not_account_reason,</if>
            <if test="otherRemarks != null">other_remarks,</if>
            <if test="incomeMain != null">income_main,</if>
            <if test="incomeOther != null">income_other,</if>
            <if test="cost != null">cost,</if>
            <if test="expense != null">expense,</if>
            <if test="profit != null">profit,</if>
            <if test="offsetLoss != null">offset_loss,</if>
            <if test="totalSalary != null">total_salary,</if>
            <if test="welfareFee != null">welfare_fee,</if>
            <if test="entertainmentFee != null">entertainment_fee,</if>
            <if test="otherAdjustment != null">other_adjustment,</if>
            <if test="taxMethod != null">tax_method,</if>
            <if test="ezTaxAccount != null">ez_tax_account,</if>
            <if test="taxDisk != null">tax_disk,</if>
            <if test="taxPassword != null">tax_password,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="accountingSystem != null">#{accountingSystem},</if>
            <if test="financeRecord != null">#{financeRecord},</if>
            <if test="reportSent != null">#{reportSent},</if>
            <if test="lastAccountMonth != null">#{lastAccountMonth},</if>
            <if test="notAccountReason != null">#{notAccountReason},</if>
            <if test="otherRemarks != null">#{otherRemarks},</if>
            <if test="incomeMain != null">#{incomeMain},</if>
            <if test="incomeOther != null">#{incomeOther},</if>
            <if test="cost != null">#{cost},</if>
            <if test="expense != null">#{expense},</if>
            <if test="profit != null">#{profit},</if>
            <if test="offsetLoss != null">#{offsetLoss},</if>
            <if test="totalSalary != null">#{totalSalary},</if>
            <if test="welfareFee != null">#{welfareFee},</if>
            <if test="entertainmentFee != null">#{entertainmentFee},</if>
            <if test="otherAdjustment != null">#{otherAdjustment},</if>
            <if test="taxMethod != null">#{taxMethod},</if>
            <if test="ezTaxAccount != null">#{ezTaxAccount},</if>
            <if test="taxDisk != null">#{taxDisk},</if>
            <if test="taxPassword != null">#{taxPassword},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNewCustomerFinanceTaxInfo" parameterType="com.bxm.customer.domain.NewCustomerFinanceTaxInfo">
        update c_new_customer_finance_tax_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="accountingSystem != null">accounting_system = #{accountingSystem},</if>
            <if test="financeRecord != null">finance_record = #{financeRecord},</if>
            <if test="reportSent != null">report_sent = #{reportSent},</if>
            <if test="lastAccountMonth != null">last_account_month = #{lastAccountMonth},</if>
            <if test="notAccountReason != null">not_account_reason = #{notAccountReason},</if>
            <if test="otherRemarks != null">other_remarks = #{otherRemarks},</if>
            <if test="incomeMain != null">income_main = #{incomeMain},</if>
            <if test="incomeOther != null">income_other = #{incomeOther},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="expense != null">expense = #{expense},</if>
            <if test="profit != null">profit = #{profit},</if>
            <if test="offsetLoss != null">offset_loss = #{offsetLoss},</if>
            <if test="totalSalary != null">total_salary = #{totalSalary},</if>
            <if test="welfareFee != null">welfare_fee = #{welfareFee},</if>
            <if test="entertainmentFee != null">entertainment_fee = #{entertainmentFee},</if>
            <if test="otherAdjustment != null">other_adjustment = #{otherAdjustment},</if>
            <if test="taxMethod != null">tax_method = #{taxMethod},</if>
            <if test="ezTaxAccount != null">ez_tax_account = #{ezTaxAccount},</if>
            <if test="taxDisk != null">tax_disk = #{taxDisk},</if>
            <if test="taxPassword != null">tax_password = #{taxPassword},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNewCustomerFinanceTaxInfoById" parameterType="Long">
        delete from c_new_customer_finance_tax_info where id = #{id}
    </delete>

    <delete id="deleteNewCustomerFinanceTaxInfoByIds" parameterType="String">
        delete from c_new_customer_finance_tax_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>