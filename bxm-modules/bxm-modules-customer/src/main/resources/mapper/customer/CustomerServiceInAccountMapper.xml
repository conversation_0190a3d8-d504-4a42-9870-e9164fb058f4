<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceInAccountMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceInAccount" id="CustomerServiceInAccountResult">
        <result property="id"    column="id"    />
        <result property="isDel"    column="is_del"    />
        <result property="customerServicePeriodMonthId"    column="customer_service_period_month_id"    />
        <result property="period"    column="period"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="serviceType"    column="service_type"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="serviceNumber"    column="service_number"    />
        <result property="taxType"    column="tax_type"    />
        <result property="title"    column="title"    />
        <result property="batchNum"    column="batch_num"    />
        <result property="status"    column="status"    />
        <result property="deliverResult"    column="deliver_result"    />
        <result property="majorIncomeTotal"    column="major_income_total"    />
        <result property="majorCostTotal"    column="major_cost_total"    />
        <result property="profitTotal"    column="profit_total"    />
        <result property="taxReportCount"    column="tax_report_count"    />
        <result property="taxReportSalaryTotal"    column="tax_report_salary_total"    />
        <result property="remark"    column="remark"    />
        <result property="bankPaymentInputTime"    column="bank_payment_input_time"    />
        <result property="inTime"    column="in_time"    />
        <result property="inEmployeeDeptId"    column="in_employee_dept_id"    />
        <result property="inEmployeeDeptName"    column="in_employee_dept_name"    />
        <result property="inEmployeeId"    column="in_employee_id"    />
        <result property="inEmployeeName"    column="in_employee_name"    />
        <result property="endTime"    column="end_time"    />
        <result property="endEmployeeDeptId"    column="end_employee_dept_id"    />
        <result property="endEmployeeDeptName"    column="end_employee_dept_name"    />
        <result property="endEmployeeId"    column="end_employee_id"    />
        <result property="endEmployeeName"    column="end_employee_name"    />
        <result property="rpaExeResult"    column="rpa_exe_result"    />
        <result property="tableStatusBalance"    column="table_status_balance"    />
        <result property="rpaSearchTime"    column="rpa_search_time"    />
        <result property="rpaRemark"    column="rpa_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceInAccountVo">
        select id, is_del, customer_service_period_month_id, period, customer_service_id, service_type, customer_name, credit_code, service_number, tax_type, title, batch_num, status, deliver_result, major_income_total, major_cost_total, profit_total, tax_report_count, tax_report_salary_total, remark, bank_payment_input_time, in_time, in_employee_dept_id, in_employee_dept_name, in_employee_id, in_employee_name, end_time, end_employee_dept_id, end_employee_dept_name, end_employee_id, end_employee_name, rpa_exe_result, table_status_balance, rpa_search_time, rpa_remark, create_by, create_time, update_by, update_time from c_customer_service_in_account
    </sql>

    <select id="selectCustomerServiceInAccountList" parameterType="com.bxm.customer.domain.CustomerServiceInAccount" resultMap="CustomerServiceInAccountResult">
        <include refid="selectCustomerServiceInAccountVo"/>
        <where>  
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="customerServicePeriodMonthId != null "> and customer_service_period_month_id = #{customerServicePeriodMonthId}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="serviceType != null "> and service_type = #{serviceType}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="serviceNumber != null  and serviceNumber != ''"> and service_number = #{serviceNumber}</if>
            <if test="taxType != null  and taxType != ''"> and tax_type = #{taxType}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="batchNum != null "> and batch_num = #{batchNum}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="deliverResult != null "> and deliver_result = #{deliverResult}</if>
            <if test="majorIncomeTotal != null "> and major_income_total = #{majorIncomeTotal}</if>
            <if test="majorCostTotal != null "> and major_cost_total = #{majorCostTotal}</if>
            <if test="profitTotal != null "> and profit_total = #{profitTotal}</if>
            <if test="taxReportCount != null "> and tax_report_count = #{taxReportCount}</if>
            <if test="taxReportSalaryTotal != null "> and tax_report_salary_total = #{taxReportSalaryTotal}</if>
            <if test="bankPaymentInputTime != null "> and bank_payment_input_time = #{bankPaymentInputTime}</if>
            <if test="inTime != null "> and in_time = #{inTime}</if>
            <if test="inEmployeeDeptId != null "> and in_employee_dept_id = #{inEmployeeDeptId}</if>
            <if test="inEmployeeDeptName != null  and inEmployeeDeptName != ''"> and in_employee_dept_name like concat('%', #{inEmployeeDeptName}, '%')</if>
            <if test="inEmployeeId != null "> and in_employee_id = #{inEmployeeId}</if>
            <if test="inEmployeeName != null  and inEmployeeName != ''"> and in_employee_name like concat('%', #{inEmployeeName}, '%')</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="endEmployeeDeptId != null "> and end_employee_dept_id = #{endEmployeeDeptId}</if>
            <if test="endEmployeeDeptName != null  and endEmployeeDeptName != ''"> and end_employee_dept_name like concat('%', #{endEmployeeDeptName}, '%')</if>
            <if test="endEmployeeId != null "> and end_employee_id = #{endEmployeeId}</if>
            <if test="endEmployeeName != null  and endEmployeeName != ''"> and end_employee_name like concat('%', #{endEmployeeName}, '%')</if>
            <if test="rpaExeResult != null  and rpaExeResult != ''"> and rpa_exe_result = #{rpaExeResult}</if>
            <if test="tableStatusBalance != null  and tableStatusBalance != ''"> and table_status_balance = #{tableStatusBalance}</if>
            <if test="rpaSearchTime != null  and rpaSearchTime != ''"> and rpa_search_time = #{rpaSearchTime}</if>
            <if test="rpaRemark != null  and rpaRemark != ''"> and rpa_remark = #{rpaRemark}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceInAccountById" parameterType="Long" resultMap="CustomerServiceInAccountResult">
        <include refid="selectCustomerServiceInAccountVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceInAccount" parameterType="com.bxm.customer.domain.CustomerServiceInAccount" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_in_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="serviceNumber != null">service_number,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="title != null">title,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="status != null">status,</if>
            <if test="deliverResult != null">deliver_result,</if>
            <if test="majorIncomeTotal != null">major_income_total,</if>
            <if test="majorCostTotal != null">major_cost_total,</if>
            <if test="profitTotal != null">profit_total,</if>
            <if test="taxReportCount != null">tax_report_count,</if>
            <if test="taxReportSalaryTotal != null">tax_report_salary_total,</if>
            <if test="remark != null">remark,</if>
            <if test="bankPaymentInputTime != null">bank_payment_input_time,</if>
            <if test="inTime != null">in_time,</if>
            <if test="inEmployeeDeptId != null">in_employee_dept_id,</if>
            <if test="inEmployeeDeptName != null">in_employee_dept_name,</if>
            <if test="inEmployeeId != null">in_employee_id,</if>
            <if test="inEmployeeName != null">in_employee_name,</if>
            <if test="endTime != null">end_time,</if>
            <if test="endEmployeeDeptId != null">end_employee_dept_id,</if>
            <if test="endEmployeeDeptName != null">end_employee_dept_name,</if>
            <if test="endEmployeeId != null">end_employee_id,</if>
            <if test="endEmployeeName != null">end_employee_name,</if>
            <if test="rpaExeResult != null">rpa_exe_result,</if>
            <if test="tableStatusBalance != null">table_status_balance,</if>
            <if test="rpaSearchTime != null">rpa_search_time,</if>
            <if test="rpaRemark != null">rpa_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="customerServicePeriodMonthId != null">#{customerServicePeriodMonthId},</if>
            <if test="period != null">#{period},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="serviceNumber != null">#{serviceNumber},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="title != null">#{title},</if>
            <if test="batchNum != null">#{batchNum},</if>
            <if test="status != null">#{status},</if>
            <if test="deliverResult != null">#{deliverResult},</if>
            <if test="majorIncomeTotal != null">#{majorIncomeTotal},</if>
            <if test="majorCostTotal != null">#{majorCostTotal},</if>
            <if test="profitTotal != null">#{profitTotal},</if>
            <if test="taxReportCount != null">#{taxReportCount},</if>
            <if test="taxReportSalaryTotal != null">#{taxReportSalaryTotal},</if>
            <if test="remark != null">#{remark},</if>
            <if test="bankPaymentInputTime != null">#{bankPaymentInputTime},</if>
            <if test="inTime != null">#{inTime},</if>
            <if test="inEmployeeDeptId != null">#{inEmployeeDeptId},</if>
            <if test="inEmployeeDeptName != null">#{inEmployeeDeptName},</if>
            <if test="inEmployeeId != null">#{inEmployeeId},</if>
            <if test="inEmployeeName != null">#{inEmployeeName},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="endEmployeeDeptId != null">#{endEmployeeDeptId},</if>
            <if test="endEmployeeDeptName != null">#{endEmployeeDeptName},</if>
            <if test="endEmployeeId != null">#{endEmployeeId},</if>
            <if test="endEmployeeName != null">#{endEmployeeName},</if>
            <if test="rpaExeResult != null">#{rpaExeResult},</if>
            <if test="tableStatusBalance != null">#{tableStatusBalance},</if>
            <if test="rpaSearchTime != null">#{rpaSearchTime},</if>
            <if test="rpaRemark != null">#{rpaRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodInAccount">
        insert into c_customer_service_in_account (is_del,customer_service_period_month_id,period, customer_service_id,service_type,customer_name,credit_code,service_number,tax_type,title,status)
        select 0,ccspm.id,ccspm.period, ccspm.customer_service_id,ccs.service_type,ccs.customer_name,ccs.credit_code,ccs.service_number,ccs.tax_type,concat('入账交付单【', ccspm.period, '】'), 1
        from c_customer_service_period_month ccspm join c_customer_service ccs on ccspm.customer_service_id = ccs.id
        left join c_customer_service_in_account ccsia on ccspm.id = ccsia.customer_service_period_month_id and ccsia.is_del = 0
        where ccspm.period = ${period} and ccsia.id is null
        and (ccspm.service_status = 1 or (ccspm.service_status = 3 and ccs.service_status = 1))
    </insert>
    <insert id="saveNewPeriodInAccountByCustomerServiceIds">
        insert into c_customer_service_in_account (is_del,customer_service_period_month_id,period, customer_service_id,service_type,customer_name,credit_code,service_number,tax_type,title,status)
        select 0,ccspm.id,ccspm.period, ccspm.customer_service_id,ccs.service_type,ccs.customer_name,ccs.credit_code,ccs.service_number,ccs.tax_type,concat('入账交付单【', ccspm.period, '】'), 1
        from c_customer_service_period_month ccspm join c_customer_service ccs on ccspm.customer_service_id = ccs.id
        where ccspm.customer_service_id in
        <foreach collection="customerServiceIds" item="customerServiceId" open="(" separator="," close=")">
            #{customerServiceId}
        </foreach>
        <if test="periodStart != null">
            and ccspm.period &gt;= ${periodStart}
        </if>
        <if test="periodEnd != null">
            and ccspm.period &lt;= ${periodEnd}
        </if>
    </insert>

    <update id="updateCustomerServiceInAccount" parameterType="com.bxm.customer.domain.CustomerServiceInAccount">
        update c_customer_service_in_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="customerServicePeriodMonthId != null">customer_service_period_month_id = #{customerServicePeriodMonthId},</if>
            <if test="period != null">period = #{period},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="serviceNumber != null">service_number = #{serviceNumber},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="title != null">title = #{title},</if>
            <if test="batchNum != null">batch_num = #{batchNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deliverResult != null">deliver_result = #{deliverResult},</if>
            <if test="majorIncomeTotal != null">major_income_total = #{majorIncomeTotal},</if>
            <if test="majorCostTotal != null">major_cost_total = #{majorCostTotal},</if>
            <if test="profitTotal != null">profit_total = #{profitTotal},</if>
            <if test="taxReportCount != null">tax_report_count = #{taxReportCount},</if>
            <if test="taxReportSalaryTotal != null">tax_report_salary_total = #{taxReportSalaryTotal},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="bankPaymentInputTime != null">bank_payment_input_time = #{bankPaymentInputTime},</if>
            <if test="inTime != null">in_time = #{inTime},</if>
            <if test="inEmployeeDeptId != null">in_employee_dept_id = #{inEmployeeDeptId},</if>
            <if test="inEmployeeDeptName != null">in_employee_dept_name = #{inEmployeeDeptName},</if>
            <if test="inEmployeeId != null">in_employee_id = #{inEmployeeId},</if>
            <if test="inEmployeeName != null">in_employee_name = #{inEmployeeName},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="endEmployeeDeptId != null">end_employee_dept_id = #{endEmployeeDeptId},</if>
            <if test="endEmployeeDeptName != null">end_employee_dept_name = #{endEmployeeDeptName},</if>
            <if test="endEmployeeId != null">end_employee_id = #{endEmployeeId},</if>
            <if test="endEmployeeName != null">end_employee_name = #{endEmployeeName},</if>
            <if test="rpaExeResult != null">rpa_exe_result = #{rpaExeResult},</if>
            <if test="tableStatusBalance != null">table_status_balance = #{tableStatusBalance},</if>
            <if test="rpaSearchTime != null">rpa_search_time = #{rpaSearchTime},</if>
            <if test="rpaRemark != null">rpa_remark = #{rpaRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceInAccountById" parameterType="Long">
        delete from c_customer_service_in_account where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceInAccountByIds" parameterType="String">
        delete from c_customer_service_in_account where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectInAccountList" resultType="com.bxm.customer.domain.dto.inAccount.InAccountDTO">
        select * from c_customer_service_in_account
        <where>
            is_del = 0
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                customer_name like concat('%', #{vo.keyWord}, '%')
                or credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and customer_service_period_month_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and customer_service_period_month_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.status != null">
                and status = #{vo.status}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.inTimeStart != null">
                and in_time &gt;= #{vo.inTimeStart}
            </if>
            <if test="vo.inTimeEnd != null">
                and in_time &lt;= #{vo.inTimeEnd}
            </if>
            <if test="vo.endTimeStart != null">
                and end_time &gt;= #{vo.endTimeStart}
            </if>
            <if test="vo.endTimeEnd != null">
                and end_time &lt;= #{vo.endTimeEnd}
            </if>
            <if test="commonIdsSearchVO.needSearch != null and commonIdsSearchVO.needSearch == true">
                <if test="commonIdsSearchVO.fail == null or commonIdsSearchVO.fail == false">
                    and customer_service_period_month_id in
                    <foreach collection="commonIdsSearchVO.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="commonIdsSearchVO.fail != null and commonIdsSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
        </where>
        order by `period` desc,id desc
    </select>


    <select id="selectInAccountListV2" resultType="com.bxm.customer.domain.dto.inAccount.InAccountDTO">
        select c_customer_service_in_account.*,
        c_customer_service_period_month.customer_name as periodCustomerName,
        c_customer_service_period_month.credit_code as periodCreditCode
        from c_customer_service_in_account
            left join c_customer_service_period_month on c_customer_service_period_month.id = c_customer_service_in_account.customer_service_period_month_id
        <where>
            c_customer_service_in_account.is_del = 0

            <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                    and 1 = -1
                </if>
                <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                    and (c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_customer_service_in_account.customer_service_period_month_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_customer_service_in_account.customer_service_period_month_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_customer_service_in_account.customer_service_id in
                <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_customer_service_in_account.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_customer_service_in_account.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.status != null">
                and c_customer_service_in_account.status = #{vo.status}
            </if>
            <if test="vo.statusList != null and vo.statusList != ''">
                and c_customer_service_in_account.status in (${vo.statusList})
            </if>
            <if test="vo.bankPaymentInputResultList != null and vo.bankPaymentInputResultList != ''">
                <if test='vo.bankPaymentInputResultList.contains("0")'>
                    and (c_customer_service_in_account.bank_payment_input_result in (${vo.bankPaymentInputResultList}) or c_customer_service_in_account.bank_payment_input_result is null)
                </if>
                <if test='!vo.bankPaymentInputResultList.contains("0")'>
                    and c_customer_service_in_account.bank_payment_input_result in (${vo.bankPaymentInputResultList})
                </if>
            </if>
            <if test="vo.inAccountResultList != null and vo.inAccountResultList != ''">
                <if test='vo.inAccountResultList.contains("0")'>
                    and (c_customer_service_in_account.in_account_result in (${vo.inAccountResultList}) or c_customer_service_in_account.in_account_result is null)
                </if>
                <if test='!vo.inAccountResultList.contains("0")'>
                    and c_customer_service_in_account.in_account_result in (${vo.inAccountResultList})
                </if>
            </if>
            <if test="vo.inTimeStart != null">
                and c_customer_service_in_account.in_time &gt;= #{vo.inTimeStart}
            </if>
            <if test="vo.inTimeEnd != null">
                and c_customer_service_in_account.in_time &lt;= #{vo.inTimeEnd}
            </if>
            <if test="vo.endTimeStart != null">
                and c_customer_service_in_account.end_time &gt;= #{vo.endTimeStart}
            </if>
            <if test="vo.endTimeEnd != null">
                and c_customer_service_in_account.end_time &lt;= #{vo.endTimeEnd}
            </if>
            <if test="vo.bankPaymentInputTimeStart != null">
                and c_customer_service_in_account.bank_payment_input_time &gt;= #{vo.bankPaymentInputTimeStart}
            </if>
            <if test="vo.bankPaymentInputTimeEnd != null">
                and c_customer_service_in_account.bank_payment_input_time &lt;= #{vo.bankPaymentInputTimeEnd}
            </if>
            <if test="commonIdsSearchVO.needSearch != null and commonIdsSearchVO.needSearch == true">
                <if test="commonIdsSearchVO.fail == null or commonIdsSearchVO.fail == false">
                    and c_customer_service_in_account.customer_service_period_month_id in
                    <foreach collection="commonIdsSearchVO.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="commonIdsSearchVO.fail != null and commonIdsSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.bankPaymentInputResult != null">
                <if test="vo.bankPaymentInputResult == 0">
                    and c_customer_service_in_account.bank_payment_input_result is null
                </if>
                <if test="vo.bankPaymentInputResult != 0">
                    and c_customer_service_in_account.bank_payment_input_result = #{vo.bankPaymentInputResult}
                </if>
            </if>
            <if test="vo.inAccountResult != null">
                <if test="vo.inAccountResult == 0">
                    and c_customer_service_in_account.in_account_result is null
                </if>
                <if test="vo.inAccountResult != 0">
                    and c_customer_service_in_account.in_account_result = #{vo.inAccountResult}
                </if>
            </if>
        </where>
        order by c_customer_service_in_account.period desc, c_customer_service_in_account.id desc
    </select>


    <select id="selectNotTimeBatch" resultMap="CustomerServiceInAccountResult">
        select * from c_customer_service_in_account
        where is_del = 0 and period &lt; #{period} and (in_time is null or end_time is null)
        and customer_service_id in
        <foreach collection="customerServiceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectNotTimeBatchWithout" resultMap="CustomerServiceInAccountResult">
        select * from c_customer_service_in_account
        where is_del = 0 and period &lt; #{period} and (in_time is null or end_time is null) and customer_service_id = #{customerServiceId}
        and id not in
        <foreach collection="notIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByInAccountList" resultMap="CustomerServiceInAccountResult">
        select customer_service_id, major_income_total, major_cost_total, profit_total, end_time, period from c_customer_service_in_account
        where (customer_service_id,`period`) in
            <foreach collection="inAccountList" open="(" close=")" item="item" separator=",">
                (#{item.customerServiceId}, #{item.period})
            </foreach>
    </select>
    <select id="selectInAccountStatisticByYearAndUserDept" resultMap="CustomerServiceInAccountResult">
        select
            cs.id,
            cs.period,
            cs.in_time,
            cs.end_time
            from c_customer_service_in_account cs join c_customer_service_period_month cspm on cs.customer_service_period_month_id = cspm.id
        <where>
            cs.is_del = 0
            <if test="year != null">
                and cs.period like concat(#{year}, '%')
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    and cspm.id in (select distinct business_id from c_business_tag_relation where tag_id = 1 and business_type = 2)
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType} and cspm.id not in (select distinct business_id from c_business_tag_relation where tag_id = 1 and business_type = 2)
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
    </select>
    <select id="selectInAccountInTimePeriodCountByYearAndUserDept" resultType="com.bxm.customer.domain.dto.PeriodCountDTO">
        select
        cs.period as period,
        count(cs.id) as `count`
        from c_customer_service_in_account cs join c_customer_service_period_month cspm on cs.customer_service_period_month_id = cspm.id
        <where>
            cs.is_del = 0
            <if test="flag == 1">
                and cs.in_time is not null
            </if>
            <if test="flag == 0">
                and cs.in_time is null
            </if>
            <if test="year != null">
                and cs.period like concat(#{year}, '%')
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    and cspm.id in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType} and cspm.id not in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        group by cs.period
    </select>
    <select id="selectInAccountEndTimePeriodCountByYearAndUserDept" resultType="com.bxm.customer.domain.dto.PeriodCountDTO">
        select
        cs.period as period,
        count(cs.id) as `count`
        from c_customer_service_in_account cs join c_customer_service_period_month cspm on cs.customer_service_period_month_id = cspm.id
        <where>
            cs.is_del = 0
            <if test="flag == 1">
                and cs.end_time is not null
            </if>
            <if test="flag == 0">
                and cs.end_time is null
            </if>
            <if test="year != null">
                and cs.period like concat(#{year}, '%')
            </if>
            <if test="statisticTaxType != null">
                <if test="statisticTaxType == 3">
                    and cspm.id in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
                <if test="statisticTaxType != 3">
                    and cspm.tax_type = #{statisticTaxType} and cspm.id not in (select distinct business_id from c_business_tag_relation where tag_id in (1, 13) and business_type = 2)
                </if>
            </if>
            <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                <if test="userDeptDTO.deptType == 1">
                    and (
                    cspm.advisor_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.advisor_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
                <if test="userDeptDTO.deptType == 2">
                    and (
                    cspm.accounting_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cspm.accounting_top_dept_id in
                    <foreach collection="userDeptDTO.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
            <if test="queryDeptIds != null and queryDeptIds.size > 0">
                and (
                cspm.advisor_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.advisor_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                cspm.accounting_top_dept_id in
                <foreach collection="queryDeptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
        </where>
        group by cs.period
    </select>
    <select id="selectCustomerInAccountMaxPeriod"
            resultType="com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO">
        SELECT
            customer_service_id AS customerServiceId,
            MAX(period) AS maxPeriod
        FROM c_customer_service_cashier_accounting
        WHERE is_del = 0 AND `type` = 1 AND in_time IS NOT null
        and customer_service_id in
        <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        GROUP BY customer_service_id
    </select>
    <select id="selectNoInTimePeriodIds" resultType="java.lang.Long">
        SELECT
            distinct c_customer_service_in_account.customer_service_period_month_id
        FROM c_customer_service_in_account
        WHERE c_customer_service_in_account.is_del = 0 AND c_customer_service_in_account.customer_service_id = #{customerServiceId} AND c_customer_service_in_account.in_time IS null
    </select>
</mapper>