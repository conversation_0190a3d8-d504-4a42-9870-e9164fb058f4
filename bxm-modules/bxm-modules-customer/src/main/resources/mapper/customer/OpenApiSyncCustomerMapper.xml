<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiSyncCustomerMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiSyncCustomer" id="OpenApiSyncCustomerResult">
        <result property="id"    column="id"    />
        <result property="sycRecordId"    column="syc_record_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="nationalTaxResult"    column="national_tax_result"    />
        <result property="personTaxResult"    column="person_tax_result"    />
        <result property="taxOperatingResult"    column="tax_operating_result"    />
        <result property="medicalSecurityResult"    column="medical_security_result"    />
        <result property="socialSecurityResult"    column="social_security_result"    />
        <result property="isSuccess"    column="is_success"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiSyncCustomerVo">
        select id, syc_record_id, customer_name, tax_number, national_tax_result, person_tax_result, tax_operating_result, medical_security_result, social_security_result, is_success, create_by, create_time, update_by, update_time from c_open_api_sync_customer
    </sql>

    <select id="selectOpenApiSyncCustomerList" parameterType="com.bxm.customer.domain.OpenApiSyncCustomer" resultMap="OpenApiSyncCustomerResult">
        <include refid="selectOpenApiSyncCustomerVo"/>
        <where>  
            <if test="sycRecordId != null "> and syc_record_id = #{sycRecordId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="nationalTaxResult != null  and nationalTaxResult != ''"> and national_tax_result = #{nationalTaxResult}</if>
            <if test="personTaxResult != null  and personTaxResult != ''"> and person_tax_result = #{personTaxResult}</if>
            <if test="taxOperatingResult != null  and taxOperatingResult != ''"> and tax_operating_result = #{taxOperatingResult}</if>
            <if test="medicalSecurityResult != null  and medicalSecurityResult != ''"> and medical_security_result = #{medicalSecurityResult}</if>
            <if test="socialSecurityResult != null  and socialSecurityResult != ''"> and social_security_result = #{socialSecurityResult}</if>
            <if test="isSuccess != null "> and is_success = #{isSuccess}</if>
        </where>
    </select>
    
    <select id="selectOpenApiSyncCustomerById" parameterType="Long" resultMap="OpenApiSyncCustomerResult">
        <include refid="selectOpenApiSyncCustomerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiSyncCustomer" parameterType="com.bxm.customer.domain.OpenApiSyncCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_sync_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sycRecordId != null">syc_record_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="nationalTaxResult != null">national_tax_result,</if>
            <if test="personTaxResult != null">person_tax_result,</if>
            <if test="taxOperatingResult != null">tax_operating_result,</if>
            <if test="medicalSecurityResult != null">medical_security_result,</if>
            <if test="socialSecurityResult != null">social_security_result,</if>
            <if test="isSuccess != null">is_success,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sycRecordId != null">#{sycRecordId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="nationalTaxResult != null">#{nationalTaxResult},</if>
            <if test="personTaxResult != null">#{personTaxResult},</if>
            <if test="taxOperatingResult != null">#{taxOperatingResult},</if>
            <if test="medicalSecurityResult != null">#{medicalSecurityResult},</if>
            <if test="socialSecurityResult != null">#{socialSecurityResult},</if>
            <if test="isSuccess != null">#{isSuccess},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiSyncCustomer" parameterType="com.bxm.customer.domain.OpenApiSyncCustomer">
        update c_open_api_sync_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="sycRecordId != null">syc_record_id = #{sycRecordId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="nationalTaxResult != null">national_tax_result = #{nationalTaxResult},</if>
            <if test="personTaxResult != null">person_tax_result = #{personTaxResult},</if>
            <if test="taxOperatingResult != null">tax_operating_result = #{taxOperatingResult},</if>
            <if test="medicalSecurityResult != null">medical_security_result = #{medicalSecurityResult},</if>
            <if test="socialSecurityResult != null">social_security_result = #{socialSecurityResult},</if>
            <if test="isSuccess != null">is_success = #{isSuccess},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiSyncCustomerById" parameterType="Long">
        delete from c_open_api_sync_customer where id = #{id}
    </delete>

    <delete id="deleteOpenApiSyncCustomerByIds" parameterType="String">
        delete from c_open_api_sync_customer where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>