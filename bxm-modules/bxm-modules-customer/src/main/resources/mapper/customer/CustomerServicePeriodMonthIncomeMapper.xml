<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServicePeriodMonthIncomeMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServicePeriodMonthIncome" id="CustomerServicePeriodMonthIncomeResult">
        <result property="id"    column="id"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="loginPassword"    column="login_password"    />
        <result property="realNameContact"    column="real_name_contact"    />
        <result property="realNameContactMobile"    column="real_name_contact_mobile"    />
        <result property="result"    column="result"    />
        <result property="allTicketAmount"    column="all_ticket_amount"    />
        <result property="allTicketTaxAmount"    column="all_ticket_tax_amount"    />
        <result property="ticketSearchAmount"    column="ticket_search_amount"    />
        <result property="ticketSearchTaxAmount"    column="ticket_search_tax_amount"    />
        <result property="doTicketGetResult"    column="do_ticket_get_result"    />
        <result property="receiveTicketGetResult"    column="receive_ticket_get_result"    />
        <result property="resultStartTime"    column="result_start_time"    />
        <result property="resultEndTime"    column="result_end_time"    />
        <result property="outputImportResult"    column="output_import_result"    />
        <result property="inputImportResult"    column="input_import_result"    />
        <result property="ticketImportResult"    column="ticket_import_result"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="customerName" column="customer_name" />
        <result property="serviceNumber" column="service_number" />
        <result property="customerTaxNumber" column="customer_tax_number" />
        <result property="period" column="period" />
        <result property="creditCode" column="credit_code" />
        <result property="ticketTime" column="ticket_time" />
        <result property="noTicketIncomeAmount" column="no_ticket_income_amount" />
        <result property="noTicketIncomeTime" column="no_ticket_income_time" />
        <result property="files" column="files" />
        <result property="rpaTime" column="rpa_time" />
        <result property="rpaResult" column="rpa_result" />
        <result property="advisorDeptId" column="advisor_dept_id" />
        <result property="accountingDeptId" column="accounting_dept_id" />
        <result property="businessDeptId" column="business_dept_id" />
        <result property="accountingTopDeptId" column="accounting_top_dept_id" />
    </resultMap>

    <sql id="selectCustomerServicePeriodMonthIncomeVo">
        select id, tax_number, login_password, real_name_contact, real_name_contact_mobile, result, all_ticket_amount, all_ticket_tax_amount, ticket_search_amount, ticket_search_tax_amount, do_ticket_get_result, receive_ticket_get_result, result_start_time, result_end_time, output_import_result, input_import_result, ticket_import_result, create_by, create_time, update_by, update_time from c_customer_service_period_month_income
    </sql>

    <select id="selectCustomerServicePeriodMonthIncomeList" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthIncome" resultMap="CustomerServicePeriodMonthIncomeResult">
        <include refid="selectCustomerServicePeriodMonthIncomeVo"/>
        <where>  
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="loginPassword != null  and loginPassword != ''"> and login_password = #{loginPassword}</if>
            <if test="realNameContact != null  and realNameContact != ''"> and real_name_contact = #{realNameContact}</if>
            <if test="realNameContactMobile != null  and realNameContactMobile != ''"> and real_name_contact_mobile = #{realNameContactMobile}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="allTicketAmount != null "> and all_ticket_amount = #{allTicketAmount}</if>
            <if test="allTicketTaxAmount != null "> and all_ticket_tax_amount = #{allTicketTaxAmount}</if>
            <if test="ticketSearchAmount != null "> and ticket_search_amount = #{ticketSearchAmount}</if>
            <if test="ticketSearchTaxAmount != null "> and ticket_search_tax_amount = #{ticketSearchTaxAmount}</if>
            <if test="doTicketGetResult != null  and doTicketGetResult != ''"> and do_ticket_get_result = #{doTicketGetResult}</if>
            <if test="receiveTicketGetResult != null  and receiveTicketGetResult != ''"> and receive_ticket_get_result = #{receiveTicketGetResult}</if>
            <if test="resultStartTime != null  and resultStartTime != ''"> and result_start_time = #{resultStartTime}</if>
            <if test="resultEndTime != null  and resultEndTime != ''"> and result_end_time = #{resultEndTime}</if>
            <if test="outputImportResult != null  and outputImportResult != ''"> and output_import_result = #{outputImportResult}</if>
            <if test="inputImportResult != null  and inputImportResult != ''"> and input_import_result = #{inputImportResult}</if>
            <if test="ticketImportResult != null  and ticketImportResult != ''"> and ticket_import_result = #{ticketImportResult}</if>
        </where>
    </select>
    
    <select id="selectCustomerServicePeriodMonthIncomeById" parameterType="Long" resultMap="CustomerServicePeriodMonthIncomeResult">
        <include refid="selectCustomerServicePeriodMonthIncomeVo"/>
        where id = #{id}
    </select>
    <select id="incomeList" resultMap="CustomerServicePeriodMonthIncomeResult">
        select
            pmi.*,
            cs.customer_name,
            cs.credit_code,
            cs.service_number,
            cs.tax_number as customer_tax_number,
            cs.advisor_dept_id as advisor_dept_id,
            cs.accounting_dept_id as accounting_dept_id,
            cs.business_dept_id as business_dept_id,
            cs.accounting_top_dept_id as accounting_top_dept_id
            from c_customer_service_period_month_income pmi join c_customer_service cs on pmi.customer_service_id = cs.id
                                                                                              and cs.is_del = 0
        <where>
            <if test="param.keyWord != null and param.keyWord != ''">and (cs.customer_name like concat('%',#{param.keyWord},'%') or cs.customer_company_name like concat('%',#{param.keyWord},'%') or cs.credit_code like concat('%',#{param.keyWord},'%'))</if>
            <if test="param.customerName != null and param.customerName != ''">and cs.customer_name like concat('%',#{param.customerName},'%')</if>
            <if test="param.customerTaxNumber != null and param.customerTaxNumber != null">and cs.tax_number like concat('%',#{param.customerTaxNumber},'%')</if>
            <if test="param.taxNumber != null and param.taxNumber != ''">and pmi.tax_number like concat('%',#{param.taxNumber},'%')</if>
            <if test="param.serviceNumber != null and param.serviceNumber != ''">and cs.service_number like concat('%',#{param.serviceNumber},'%')</if>
            <if test="param.period != null">and pmi.period = #{param.period}</if>
            <if test="param.periodStart != null">and pmi.period &gt;= #{param.periodStart}</if>
            <if test="param.periodEnd != null">and pmi.period &lt;= #{param.periodEnd}</if>
            <if test="param.businessDeptId != null">and cs.business_dept_id = #{param.businessDeptId}</if>
            <if test="param.advisorDeptId != null">and cs.advisor_dept_id = #{param.advisorDeptId}</if>
            <if test="param.accountingDeptId != null">and cs.accounting_dept_id = #{param.accountingDeptId}</if>
            <if test="param.ticketTimeMin != null and param.ticketTimeMin != ''">and pmi.ticket_time &gt;= #{param.ticketTimeMin}</if>
            <if test="param.ticketTimeMax != null and param.ticketTimeMax != ''">and pmi.ticket_time &lt;= #{param.ticketTimeMax}</if>
            <if test="param.allTicketAmountMin != null">and pmi.all_ticket_amount &gt;= #{param.allTicketAmountMin}</if>
            <if test="param.allTicketAmountMax != null">and pmi.all_ticket_amount &lt;= #{param.allTicketAmountMax}</if>
            <if test="param.rpaResult != null">and pmi.rpa_result = #{param.rpaResult}</if>
            <if test="param.noTicketIncomeAmountMin != null">and pmi.no_ticket_income_amount &gt;= #{param.noTicketIncomeAmountMin}</if>
            <if test="param.noTicketIncomeAmountMax != null">and pmi.no_ticket_income_amount &lt;= #{param.noTicketIncomeAmountMax}</if>
            <if test="param.remark != null and param.remark != ''">and pmi.remark like concat('%',#{param.remark},'%')</if>
            <if test="param.taxType != null">and cs.tax_type = #{param.taxType}</if>
            <if test="param.customerEndPeriod != null">and (cs.end_account_period is null or cs.end_account_period >= #{param.customerEndPeriod})</if>
            <if test="param.hasFiles != null">
                <if test="param.hasFiles == 0">and (pmi.files is null or pmi.files = '')</if>
                <if test="param.hasFiles == 1">and pmi.files is not null and pmi.files != ''</if>
            </if>
            <if test="userDept.isAdmin != true and userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (cs.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (cs.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="param.queryDeptIds != null and param.queryDeptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (cs.advisor_dept_id in
                    <foreach collection="param.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.advisor_top_dept_id in
                    <foreach collection="param.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
                <if test="userDept.deptType == 2">
                    and (cs.accounting_dept_id in
                    <foreach collection="param.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                    or
                    cs.accounting_top_dept_id in
                    <foreach collection="param.queryDeptIds" item="deptId" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>)
                </if>
            </if>
            <if test="param.batchNo != null and param.batchNo != ''">
                and pmi.customer_service_id in
                <foreach collection="customerServiceIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        </where>
        order by pmi.period desc, pmi.create_time desc
    </select>
    <select id="getCustomerServiceIncomeByCustomerServiceIdAndPeriod"
            resultType="com.bxm.customer.domain.CustomerServicePeriodMonthIncome">
        select
            id,
            customer_service_id as customerServiceId,
            period,
            input_file,
            output_file,
            no_ticket_income_amount
        from c_customer_service_period_month_income
        where (customer_service_id, period) in
        <foreach collection="voList" item="vo" open="(" separator="," close=")">
            (#{vo.customerServiceId},#{vo.period})
        </foreach>
    </select>

    <insert id="insertCustomerServicePeriodMonthIncome" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthIncome" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_period_month_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServicePeriodId != null">customer_service_period_id,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="loginPassword != null">login_password,</if>
            <if test="realNameContact != null">real_name_contact,</if>
            <if test="realNameContactMobile != null">real_name_contact_mobile,</if>
            <if test="result != null">result,</if>
            <if test="allTicketAmount != null">all_ticket_amount,</if>
            <if test="allTicketTaxAmount != null">all_ticket_tax_amount,</if>
            <if test="ticketSearchAmount != null">ticket_search_amount,</if>
            <if test="ticketSearchTaxAmount != null">ticket_search_tax_amount,</if>
            <if test="doTicketGetResult != null">do_ticket_get_result,</if>
            <if test="receiveTicketGetResult != null">receive_ticket_get_result,</if>
            <if test="resultStartTime != null">result_start_time,</if>
            <if test="resultEndTime != null">result_end_time,</if>
            <if test="outputImportResult != null">output_import_result,</if>
            <if test="inputImportResult != null">input_import_result,</if>
            <if test="ticketImportResult != null">ticket_import_result,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServicePeriodId != null">#{customerServicePeriodId},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="loginPassword != null">#{loginPassword},</if>
            <if test="realNameContact != null">#{realNameContact},</if>
            <if test="realNameContactMobile != null">#{realNameContactMobile},</if>
            <if test="result != null">#{result},</if>
            <if test="allTicketAmount != null">#{allTicketAmount},</if>
            <if test="allTicketTaxAmount != null">#{allTicketTaxAmount},</if>
            <if test="ticketSearchAmount != null">#{ticketSearchAmount},</if>
            <if test="ticketSearchTaxAmount != null">#{ticketSearchTaxAmount},</if>
            <if test="doTicketGetResult != null">#{doTicketGetResult},</if>
            <if test="receiveTicketGetResult != null">#{receiveTicketGetResult},</if>
            <if test="resultStartTime != null">#{resultStartTime},</if>
            <if test="resultEndTime != null">#{resultEndTime},</if>
            <if test="outputImportResult != null">#{outputImportResult},</if>
            <if test="inputImportResult != null">#{inputImportResult},</if>
            <if test="ticketImportResult != null">#{ticketImportResult},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="saveNewPeriodIncome">
        insert into c_customer_service_period_month_income (customer_service_id,period)
        select id, ${nowPeriod} from c_customer_service where service_status != 2 and is_del = 0 and (end_account_period is null or end_account_period >= ${nowPeriod}) and id not in (select distinct customer_service_id from c_customer_service_period_month_income where period = ${nowPeriod})
    </insert>

    <update id="updateCustomerServicePeriodMonthIncome" parameterType="com.bxm.customer.domain.CustomerServicePeriodMonthIncome">
        update c_customer_service_period_month_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServicePeriodId != null">customer_service_period_id = #{customerServicePeriodId},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="loginPassword != null">login_password = #{loginPassword},</if>
            <if test="realNameContact != null">real_name_contact = #{realNameContact},</if>
            <if test="realNameContactMobile != null">real_name_contact_mobile = #{realNameContactMobile},</if>
            <if test="result != null">result = #{result},</if>
            <if test="allTicketAmount != null">all_ticket_amount = #{allTicketAmount},</if>
            <if test="allTicketTaxAmount != null">all_ticket_tax_amount = #{allTicketTaxAmount},</if>
            <if test="ticketSearchAmount != null">ticket_search_amount = #{ticketSearchAmount},</if>
            <if test="ticketSearchTaxAmount != null">ticket_search_tax_amount = #{ticketSearchTaxAmount},</if>
            <if test="doTicketGetResult != null">do_ticket_get_result = #{doTicketGetResult},</if>
            <if test="receiveTicketGetResult != null">receive_ticket_get_result = #{receiveTicketGetResult},</if>
            <if test="resultStartTime != null">result_start_time = #{resultStartTime},</if>
            <if test="resultEndTime != null">result_end_time = #{resultEndTime},</if>
            <if test="outputImportResult != null">output_import_result = #{outputImportResult},</if>
            <if test="inputImportResult != null">input_import_result = #{inputImportResult},</if>
            <if test="ticketImportResult != null">ticket_import_result = #{ticketImportResult},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServicePeriodMonthIncomeById" parameterType="Long">
        delete from c_customer_service_period_month_income where id = #{id}
    </delete>

    <delete id="deleteCustomerServicePeriodMonthIncomeByIds" parameterType="String">
        delete from c_customer_service_period_month_income where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>