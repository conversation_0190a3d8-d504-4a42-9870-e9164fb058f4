<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.MaterialDeliverPeriodInventoryMapper">
    
    <resultMap type="com.bxm.customer.domain.MaterialDeliverPeriodInventory" id="MaterialDeliverPeriodInventoryResult">
        <result property="id"    column="id"    />
        <result property="materialDeliverId"    column="material_deliver_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankAccountNumber"    column="bank_account_number"    />
        <result property="period"    column="period"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="pushResult"    column="push_result"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialDeliverPeriodInventoryVo">
        select id, material_deliver_id, customer_name, bank_name, bank_account_number, period, customer_service_id, push_result, is_del, create_by, create_time, update_by, update_time from c_material_deliver_period_inventory
    </sql>

    <select id="selectMaterialDeliverPeriodInventoryList" parameterType="com.bxm.customer.domain.MaterialDeliverPeriodInventory" resultMap="MaterialDeliverPeriodInventoryResult">
        <include refid="selectMaterialDeliverPeriodInventoryVo"/>
        <where>  
            <if test="materialDeliverId != null "> and material_deliver_id = #{materialDeliverId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankAccountNumber != null  and bankAccountNumber != ''"> and bank_account_number = #{bankAccountNumber}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="pushResult != null  and pushResult != ''"> and push_result = #{pushResult}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectMaterialDeliverPeriodInventoryById" parameterType="Long" resultMap="MaterialDeliverPeriodInventoryResult">
        <include refid="selectMaterialDeliverPeriodInventoryVo"/>
        where id = #{id}
    </select>
    <select id="periodInventoryPageList"
            resultType="com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverPeriodInventoryDTO">
        select
            p.id as id,
            p.material_deliver_number as materialDeliverNumber,
            p.customer_name as customerName,
            m.material_deliver_type as materialDeliverType,
            p.bank_name as bankName,
            p.bank_account_number as bankAccountNumber,
            p.period as period,
            p.push_status as pushResult,
            p.push_result as errorMsg
            from
                c_material_deliver_period_inventory p join c_material_deliver m on p.material_deliver_id = m.id
                left join c_customer_service_period_month on p.customer_service_id = c_customer_service_period_month.customer_service_id and p.period = c_customer_service_period_month.period
        and m.is_del = 0
        <where>
            p.is_del = 0
            <if test="userDept.isAdmin == false and userDept.deptIds != null and userDept.deptIds.size > 0">
                <if test="userDept.deptType == 1">
                    and (c_customer_service_period_month.advisor_top_dept_id in
                    <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or c_customer_service_period_month.advisor_dept_id in
                    <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="userDept.deptType == 2">
                    and (c_customer_service_period_month.accounting_top_dept_id in
                    <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    or c_customer_service_period_month.accounting_dept_id in
                    <foreach collection="userDept.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="customerServiceIds != null and customerServiceIds.size > 0">
                and p.customer_service_id in
                <foreach collection="customerServiceIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="vo.materialDeliverNumber != null and vo.materialDeliverNumber != ''">
                and m.material_deliver_number = #{vo.materialDeliverNumber}
            </if>
            <if test="vo.customerName != null and vo.customerName != ''">
                and p.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="vo.materialDeliverTypeList != null and vo.materialDeliverTypeList != ''">
                and m.material_deliver_type in (${vo.materialDeliverTypeList})
            </if>
            <if test="vo.pushResult != null">
                and p.push_status = #{vo.pushResult}
            </if>
            <if test="vo.periodStart != null">
                and p.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and p.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and p.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="errorMsgList != null and errorMsgList.size > 0">
                AND (
                <foreach collection="errorMsgList" item="errorMsg" separator=" OR ">
                    push_result LIKE CONCAT('%', #{errorMsg}, '%')
                </foreach>
                )
            </if>
        </where>
        order by p.id desc
    </select>

    <insert id="insertMaterialDeliverPeriodInventory" parameterType="com.bxm.customer.domain.MaterialDeliverPeriodInventory" useGeneratedKeys="true" keyProperty="id">
        insert into c_material_deliver_period_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankAccountNumber != null">bank_account_number,</if>
            <if test="period != null">period,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="pushResult != null">push_result,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialDeliverId != null">#{materialDeliverId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankAccountNumber != null">#{bankAccountNumber},</if>
            <if test="period != null">#{period},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="pushResult != null">#{pushResult},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialDeliverPeriodInventory" parameterType="com.bxm.customer.domain.MaterialDeliverPeriodInventory">
        update c_material_deliver_period_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialDeliverId != null">material_deliver_id = #{materialDeliverId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankAccountNumber != null">bank_account_number = #{bankAccountNumber},</if>
            <if test="period != null">period = #{period},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="pushResult != null">push_result = #{pushResult},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialDeliverPeriodInventoryById" parameterType="Long">
        delete from c_material_deliver_period_inventory where id = #{id}
    </delete>

    <delete id="deleteMaterialDeliverPeriodInventoryByIds" parameterType="String">
        delete from c_material_deliver_period_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>