<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.WorkOrderTypeMapper">
    
    <resultMap type="com.bxm.customer.domain.WorkOrderType" id="WorkOrderTypeResult">
        <result property="id"    column="id"    />
        <result property="workOrderType"    column="work_order_type"    />
        <result property="workOrderTypeName"    column="work_order_type_name"    />
        <result property="dispatchType"    column="dispatch_type"    />
        <result property="dispatchDeptId"    column="dispatch_dept_id"    />
        <result property="isNeedCustomer"    column="is_need_customer"    />
        <result property="isNeedPeriod"    column="is_need_period"    />
        <result property="isNeedRemark"    column="is_need_remark"    />
        <result property="defaultRemark"    column="default_remark"    />
        <result property="isShow"    column="is_show"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWorkOrderTypeVo">
        select id, work_order_type, work_order_type_name, dispatch_type, dispatch_dept_id, is_need_customer, is_need_period, is_need_remark, default_remark, is_show, is_del, create_by, create_time, update_by, update_time from c_work_order_type
    </sql>

    <select id="selectWorkOrderTypeList" parameterType="com.bxm.customer.domain.WorkOrderType" resultMap="WorkOrderTypeResult">
        <include refid="selectWorkOrderTypeVo"/>
        <where>  
            <if test="workOrderType != null "> and work_order_type = #{workOrderType}</if>
            <if test="workOrderTypeName != null  and workOrderTypeName != ''"> and work_order_type_name like concat('%', #{workOrderTypeName}, '%')</if>
            <if test="dispatchType != null "> and dispatch_type = #{dispatchType}</if>
            <if test="dispatchDeptId != null "> and dispatch_dept_id = #{dispatchDeptId}</if>
            <if test="isNeedCustomer != null "> and is_need_customer = #{isNeedCustomer}</if>
            <if test="isNeedPeriod != null "> and is_need_period = #{isNeedPeriod}</if>
            <if test="isNeedRemark != null "> and is_need_remark = #{isNeedRemark}</if>
            <if test="defaultRemark != null  and defaultRemark != ''"> and default_remark = #{defaultRemark}</if>
            <if test="isShow != null "> and is_show = #{isShow}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectWorkOrderTypeById" parameterType="Long" resultMap="WorkOrderTypeResult">
        <include refid="selectWorkOrderTypeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWorkOrderType" parameterType="com.bxm.customer.domain.WorkOrderType" useGeneratedKeys="true" keyProperty="id">
        insert into c_work_order_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workOrderType != null">work_order_type,</if>
            <if test="workOrderTypeName != null and workOrderTypeName != ''">work_order_type_name,</if>
            <if test="dispatchType != null">dispatch_type,</if>
            <if test="dispatchDeptId != null">dispatch_dept_id,</if>
            <if test="isNeedCustomer != null">is_need_customer,</if>
            <if test="isNeedPeriod != null">is_need_period,</if>
            <if test="isNeedRemark != null">is_need_remark,</if>
            <if test="defaultRemark != null">default_remark,</if>
            <if test="isShow != null">is_show,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workOrderType != null">#{workOrderType},</if>
            <if test="workOrderTypeName != null and workOrderTypeName != ''">#{workOrderTypeName},</if>
            <if test="dispatchType != null">#{dispatchType},</if>
            <if test="dispatchDeptId != null">#{dispatchDeptId},</if>
            <if test="isNeedCustomer != null">#{isNeedCustomer},</if>
            <if test="isNeedPeriod != null">#{isNeedPeriod},</if>
            <if test="isNeedRemark != null">#{isNeedRemark},</if>
            <if test="defaultRemark != null">#{defaultRemark},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWorkOrderType" parameterType="com.bxm.customer.domain.WorkOrderType">
        update c_work_order_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="workOrderType != null">work_order_type = #{workOrderType},</if>
            <if test="workOrderTypeName != null and workOrderTypeName != ''">work_order_type_name = #{workOrderTypeName},</if>
            <if test="dispatchType != null">dispatch_type = #{dispatchType},</if>
            <if test="dispatchDeptId != null">dispatch_dept_id = #{dispatchDeptId},</if>
            <if test="isNeedCustomer != null">is_need_customer = #{isNeedCustomer},</if>
            <if test="isNeedPeriod != null">is_need_period = #{isNeedPeriod},</if>
            <if test="isNeedRemark != null">is_need_remark = #{isNeedRemark},</if>
            <if test="defaultRemark != null">default_remark = #{defaultRemark},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkOrderTypeById" parameterType="Long">
        delete from c_work_order_type where id = #{id}
    </delete>

    <delete id="deleteWorkOrderTypeByIds" parameterType="String">
        delete from c_work_order_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>