<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BusinessDdlRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.BusinessDdlRecord" id="BusinessDdlRecordResult">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="ddl"    column="ddl"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBusinessDdlRecordVo">
        select id, business_id, business_type, ddl, create_by, create_time, update_by, update_time from c_business_ddl_record
    </sql>

    <select id="selectBusinessDdlRecordList" parameterType="com.bxm.customer.domain.BusinessDdlRecord" resultMap="BusinessDdlRecordResult">
        <include refid="selectBusinessDdlRecordVo"/>
        <where>  
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="ddl != null "> and ddl = #{ddl}</if>
        </where>
    </select>
    
    <select id="selectBusinessDdlRecordById" parameterType="Long" resultMap="BusinessDdlRecordResult">
        <include refid="selectBusinessDdlRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBusinessDdlRecord" parameterType="com.bxm.customer.domain.BusinessDdlRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_ddl_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null">business_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="ddl != null">ddl,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null">#{businessId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="ddl != null">#{ddl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBusinessDdlRecord" parameterType="com.bxm.customer.domain.BusinessDdlRecord">
        update c_business_ddl_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="ddl != null">ddl = #{ddl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessDdlRecordById" parameterType="Long">
        delete from c_business_ddl_record where id = #{id}
    </delete>

    <delete id="deleteBusinessDdlRecordByIds" parameterType="String">
        delete from c_business_ddl_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>