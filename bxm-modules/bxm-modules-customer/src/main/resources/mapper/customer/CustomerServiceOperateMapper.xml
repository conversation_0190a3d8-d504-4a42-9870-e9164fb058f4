<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceOperateMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceOperate" id="CustomerServiceOperateResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="operateType"    column="operate_type"    />
        <result property="operateMonth"    column="operate_month"    />
        <result property="operateSubType"    column="operate_sub_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceOperateVo">
        select id, customer_service_id, operate_type, operate_month, operate_sub_type, create_by, create_time, update_by, update_time from c_customer_service_operate
    </sql>

    <select id="selectCustomerServiceOperateList" parameterType="com.bxm.customer.domain.CustomerServiceOperate" resultMap="CustomerServiceOperateResult">
        <include refid="selectCustomerServiceOperateVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="operateType != null "> and operate_type = #{operateType}</if>
            <if test="operateMonth != null "> and operate_month = #{operateMonth}</if>
            <if test="operateSubType != null "> and operate_sub_type = #{operateSubType}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceOperateById" parameterType="Long" resultMap="CustomerServiceOperateResult">
        <include refid="selectCustomerServiceOperateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceOperate" parameterType="com.bxm.customer.domain.CustomerServiceOperate" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_operate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="operateMonth != null">operate_month,</if>
            <if test="operateSubType != null">operate_sub_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="operateMonth != null">#{operateMonth},</if>
            <if test="operateSubType != null">#{operateSubType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceOperate" parameterType="com.bxm.customer.domain.CustomerServiceOperate">
        update c_customer_service_operate
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="operateType != null">operate_type = #{operateType},</if>
            <if test="operateMonth != null">operate_month = #{operateMonth},</if>
            <if test="operateSubType != null">operate_sub_type = #{operateSubType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceOperateById" parameterType="Long">
        delete from c_customer_service_operate where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceOperateByIds" parameterType="String">
        delete from c_customer_service_operate where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>