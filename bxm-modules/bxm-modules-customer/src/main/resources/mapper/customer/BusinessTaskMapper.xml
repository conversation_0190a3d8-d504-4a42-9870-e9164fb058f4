<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.BusinessTaskMapper">
    
    <resultMap type="com.bxm.customer.domain.BusinessTask" id="BusinessTaskResult">
        <result property="id"    column="id"    />
        <result property="isDel"    column="is_del"    />
        <result property="type"    column="type"    />
        <result property="bizId"    column="biz_id"    />
        <result property="itemType"    column="item_type"    />
        <result property="status"    column="status"    />
        <result property="title"    column="title"    />
        <result property="deadline"    column="deadline"    />
        <result property="remark"    column="remark"    />
        <result property="adminUserId"    column="admin_user_id"    />
        <result property="adminUserName"    column="admin_user_name"    />
        <result property="isAssign"    column="is_assign"    />
        <result property="assignTime"    column="assign_time"    />
        <result property="executeUserId"    column="execute_user_id"    />
        <result property="executeUserName"    column="execute_user_name"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="finishUserId"    column="finish_user_id"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="finishResult"    column="finish_result"    />
        <result property="endTime"    column="end_time"    />
        <result property="lastOperateUserId"    column="last_operate_user_id"    />
        <result property="lastOperateUserName"    column="last_operate_user_name"    />
        <result property="lastOperateType"    column="last_operate_type"    />
        <result property="lastOperateTime"    column="last_operate_time"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerCompanyName"    column="customer_company_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="taxType"    column="tax_type"    />
        <result property="period"    column="period"    />
        <result property="periodAdvisorDeptId"    column="period_advisor_dept_id"    />
        <result property="periodAccountingDeptId"    column="period_accounting_dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBusinessTaskVo">
        select id, is_del, type, biz_id, item_type, status, title, deadline, remark, admin_user_id, admin_user_name, is_assign, assign_time, execute_user_id, execute_user_name, execute_time, finish_user_id, finish_time, finish_result, end_time, last_operate_user_id, last_operate_user_name, last_operate_type, last_operate_time, customer_service_id, customer_name, customer_company_name, credit_code, tax_type, period, period_advisor_dept_id, period_accounting_dept_id, create_by, create_time, update_by, update_time from c_business_task
    </sql>

    <select id="selectBusinessTaskList" parameterType="com.bxm.customer.domain.BusinessTask" resultMap="BusinessTaskResult">
        <include refid="selectBusinessTaskVo"/>
        <where>  
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="bizId != null "> and biz_id = #{bizId}</if>
            <if test="itemType != null "> and item_type = #{itemType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="adminUserId != null "> and admin_user_id = #{adminUserId}</if>
            <if test="adminUserName != null "> and admin_user_name = #{adminUserName}</if>
            <if test="isAssign != null "> and is_assign = #{isAssign}</if>
            <if test="assignTime != null "> and assign_time = #{assignTime}</if>
            <if test="executeUserId != null "> and execute_user_id = #{executeUserId}</if>
            <if test="executeUserName != null "> and execute_user_name = #{executeUserName}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="finishUserId != null "> and finish_user_id = #{finishUserId}</if>
            <if test="finishTime != null "> and finish_time = #{finishTime}</if>
            <if test="finishResult != null "> and finish_result = #{finishResult}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="lastOperateUserId != null "> and last_operate_user_id = #{lastOperateUserId}</if>
            <if test="lastOperateUserName != null "> and last_operate_user_name = #{lastOperateUserName}</if>
            <if test="lastOperateType != null "> and last_operate_type = #{lastOperateType}</if>
            <if test="lastOperateTime != null "> and last_operate_time = #{lastOperateTime}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="customerCompanyName != null  and customerCompanyName != ''"> and customer_company_name like concat('%', #{customerCompanyName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="taxType != null "> and tax_type = #{taxType}</if>
            <if test="period != null "> and period = #{period}</if>
            <if test="periodAdvisorDeptId != null "> and period_advisor_dept_id = #{periodAdvisorDeptId}</if>
            <if test="periodAccountingDeptId != null "> and period_accounting_dept_id = #{periodAccountingDeptId}</if>
        </where>
    </select>
    
    <select id="selectBusinessTaskById" parameterType="Long" resultMap="BusinessTaskResult">
        <include refid="selectBusinessTaskVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBusinessTask" parameterType="com.bxm.customer.domain.BusinessTask" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="type != null">type,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="itemType != null">item_type,</if>
            <if test="status != null">status,</if>
            <if test="title != null">title,</if>
            <if test="deadline != null">deadline,</if>
            <if test="remark != null">remark,</if>
            <if test="adminUserId != null">admin_user_id,</if>
            <if test="adminUserName != null">admin_user_name,</if>
            <if test="isAssign != null">is_assign,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="executeUserId != null">execute_user_id,</if>
            <if test="executeUserName != null">execute_user_name,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="finishUserId != null">finish_user_id,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="finishResult != null">finish_result,</if>
            <if test="endTime != null">end_time,</if>
            <if test="lastOperateUserId != null">last_operate_user_id,</if>
            <if test="lastOperateUserName != null">last_operate_user_name,</if>
            <if test="lastOperateType != null">last_operate_type,</if>
            <if test="lastOperateTime != null">last_operate_time,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">customer_company_name,</if>
            <if test="creditCode != null">credit_code,</if>
            <if test="taxType != null">tax_type,</if>
            <if test="period != null">period,</if>
            <if test="periodAdvisorDeptId != null">period_advisor_dept_id,</if>
            <if test="periodAccountingDeptId != null">period_accounting_dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="type != null">#{type},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="itemType != null">#{itemType},</if>
            <if test="status != null">#{status},</if>
            <if test="title != null">#{title},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="remark != null">#{remark},</if>
            <if test="adminUserId != null">#{adminUserId},</if>
            <if test="adminUserName != null">#{adminUserName},</if>
            <if test="isAssign != null">#{isAssign},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="executeUserId != null">#{executeUserId},</if>
            <if test="executeUserName != null">#{executeUserName},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="finishUserId != null">#{finishUserId},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="finishResult != null">#{finishResult},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="lastOperateUserId != null">#{lastOperateUserId},</if>
            <if test="lastOperateUserName != null">#{lastOperateUserName},</if>
            <if test="lastOperateType != null">#{lastOperateType},</if>
            <if test="lastOperateTime != null">#{lastOperateTime},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">#{customerCompanyName},</if>
            <if test="creditCode != null">#{creditCode},</if>
            <if test="taxType != null">#{taxType},</if>
            <if test="period != null">#{period},</if>
            <if test="periodAdvisorDeptId != null">#{periodAdvisorDeptId},</if>
            <if test="periodAccountingDeptId != null">#{periodAccountingDeptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBusinessTask" parameterType="com.bxm.customer.domain.BusinessTask">
        update c_business_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="type != null">type = #{type},</if>
            <if test="bizId != null">biz_id = #{bizId},</if>
            <if test="itemType != null">item_type = #{itemType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="title != null">title = #{title},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="adminUserId != null">admin_user_id = #{adminUserId},</if>
            <if test="adminUserName != null">admin_user_name = #{adminUserName},</if>
            <if test="isAssign != null">is_assign = #{isAssign},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="executeUserId != null">execute_user_id = #{executeUserId},</if>
            <if test="executeUserName != null">execute_user_name = #{executeUserName},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="finishUserId != null">finish_user_id = #{finishUserId},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="finishResult != null">finish_result = #{finishResult},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="lastOperateUserId != null">last_operate_user_id = #{lastOperateUserId},</if>
            <if test="lastOperateUserName != null">last_operate_user_name = #{lastOperateUserName},</if>
            <if test="lastOperateType != null">last_operate_type = #{lastOperateType},</if>
            <if test="lastOperateTime != null">last_operate_time = #{lastOperateTime},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerCompanyName != null and customerCompanyName != ''">customer_company_name = #{customerCompanyName},</if>
            <if test="creditCode != null">credit_code = #{creditCode},</if>
            <if test="taxType != null">tax_type = #{taxType},</if>
            <if test="period != null">period = #{period},</if>
            <if test="periodAdvisorDeptId != null">period_advisor_dept_id = #{periodAdvisorDeptId},</if>
            <if test="periodAccountingDeptId != null">period_accounting_dept_id = #{periodAccountingDeptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessTaskById" parameterType="Long">
        delete from c_business_task where id = #{id}
    </delete>

    <delete id="deleteBusinessTaskByIds" parameterType="String">
        delete from c_business_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="searchListForPeriod" resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskForPeriodDTO">
        select c_business_task.*,cmo.matters_notes from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        left join c_customer_matters_notes cmo on c_business_task.customer_service_id = cmo.customer_service_id and cmo.item_type = 9
        <where>
            c_business_task.is_del = 0
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and c_business_task.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.bankName != null and vo.bankName != ''">
                and c_business_task.bank_name like concat('%',#{vo.bankName},'%')
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
               <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.hasBankPayment != null">
                and c_business_task.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>
            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_business_task.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')

                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="customerServiceTagSearchVO.needSearch != null and customerServiceTagSearchVO.needSearch == true">
                <if test="customerServiceTagSearchVO.fail == null or customerServiceTagSearchVO.fail == false">
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.customer_service_id not in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.customer_service_id in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="customerServiceTagSearchVO.fail != null and customerServiceTagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                <if test="vo.adminUserId == 0">
                    and c_business_task.admin_user_id is null
                </if>
                <if test="vo.adminUserId != 0">
                    and c_business_task.admin_user_id = #{vo.adminUserId}
                </if>
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.finishResultList != null and vo.finishResultList != ''">
                and c_business_task.finish_result in (${vo.finishResultList})
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
            <if test="vo.finishResultList != null and vo.finishResultList != ''">
                and c_business_task.finish_result in (${vo.finishResultList})
            </if>
            <if test="vo.firstCompleteTimeStart != null and vo.firstCompleteTimeStart != ''">
                and c_business_task.first_complete_time &gt;= #{vo.firstCompleteTimeStart}
            </if>
            <if test="vo.firstCompleteTimeEnd != null and vo.firstCompleteTimeEnd != ''">
                and c_business_task.first_complete_time &lt;= #{vo.firstCompleteTimeEnd}
            </if>
        </where>
        order by c_business_task.customer_name,c_business_task.id desc
    </select>


    <select id="searchListForManage" resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskForManageDTO">
        select c_business_task.*,cmo.matters_notes from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        left join c_customer_matters_notes cmo on c_business_task.customer_service_id = cmo.customer_service_id and cmo.item_type = 9
        <where>
            c_business_task.is_del = 0
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and c_business_task.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.bankName != null and vo.bankName != ''">
                and c_business_task.bank_name like concat('%',#{vo.bankName},'%')
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.hasBankPayment != null">
                and c_business_task.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>

            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_business_task.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')

                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="customerServiceTagSearchVO.needSearch != null and customerServiceTagSearchVO.needSearch == true">
                <if test="customerServiceTagSearchVO.fail == null or customerServiceTagSearchVO.fail == false">
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.customer_service_id not in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.customer_service_id in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="customerServiceTagSearchVO.fail != null and customerServiceTagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                <if test="vo.adminUserId == 0">
                    and c_business_task.admin_user_id is null
                </if>
                <if test="vo.adminUserId != 0">
                    and c_business_task.admin_user_id = #{vo.adminUserId}
                </if>
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.finishResultList != null and vo.finishResultList != ''">
                and c_business_task.finish_result in (${vo.finishResultList})
            </if>
            <if test="vo.firstCompleteTimeStart != null and vo.firstCompleteTimeStart != ''">
                and c_business_task.first_complete_time &gt;= #{vo.firstCompleteTimeStart}
            </if>
            <if test="vo.firstCompleteTimeEnd != null and vo.firstCompleteTimeEnd != ''">
                and c_business_task.first_complete_time &lt;= #{vo.firstCompleteTimeEnd}
            </if>
        </where>
        order by c_business_task.customer_name,c_business_task.id desc
    </select>


    <select id="searchListForMy" resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskForMyDTO">
        select c_business_task.*,cmo.matters_notes from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        left join c_customer_matters_notes cmo on c_business_task.customer_service_id = cmo.customer_service_id and cmo.item_type = 9
        <where>
            c_business_task.is_del = 0
            <if test="vo.bankAccountNumber != null and vo.bankAccountNumber != ''">
                and c_business_task.bank_account_number = #{vo.bankAccountNumber}
            </if>
            <if test="vo.bankName != null and vo.bankName != ''">
                and c_business_task.bank_name like concat('%',#{vo.bankName},'%')
            </if>
            <if test="vo.hasMattersNotes != null">
                <if test="vo.hasMattersNotes == 0">
                    and (cmo.matters_notes is null or cmo.matters_notes = '')
                </if>
                <if test="vo.hasMattersNotes == 1">
                    and cmo.matters_notes is not null and cmo.matters_notes != ''
                </if>
            </if>
            <if test="vo.hasBankPayment != null">
                and c_business_task.has_bank_payment = #{vo.hasBankPayment}
            </if>
            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_customer_service_period_month.customer_name like concat('%', #{vo.keyWord}, '%')

                or c_customer_service_period_month.credit_code like concat('%', #{vo.keyWord}, '%')
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="customerServiceTagSearchVO.needSearch != null and customerServiceTagSearchVO.needSearch == true">
                <if test="customerServiceTagSearchVO.fail == null or customerServiceTagSearchVO.fail == false">
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.customer_service_id not in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="customerServiceTagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.customer_service_id in
                        <foreach collection="customerServiceTagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="customerServiceTagSearchVO.fail != null and customerServiceTagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                <if test="vo.adminUserId == 0">
                    and c_business_task.admin_user_id is null
                </if>
                <if test="vo.adminUserId != 0">
                    and c_business_task.admin_user_id = #{vo.adminUserId}
                </if>
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.finishResultList != null and vo.finishResultList != ''">
                and c_business_task.finish_result in (${vo.finishResultList})
            </if>
            <if test="vo.firstCompleteTimeStart != null and vo.firstCompleteTimeStart != ''">
                and c_business_task.first_complete_time &gt;= #{vo.firstCompleteTimeStart}
            </if>
            <if test="vo.firstCompleteTimeEnd != null and vo.firstCompleteTimeEnd != ''">
                and c_business_task.first_complete_time &lt;= #{vo.firstCompleteTimeEnd}
            </if>
        </where>
        order by c_business_task.customer_name,c_business_task.id desc
    </select>
    <select id="searchListCountForPeriod"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.accounting_dept_id as accountingDeptId,
               count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.accounting_dept_id is not null

            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.accounting_dept_id
    </select>
    <select id="searchListCountForManager"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.accounting_dept_id as accountingDeptId,
        count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.accounting_dept_id is not null

            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.accounting_dept_id
    </select>

    <select id="searchListCountForMy"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.accounting_dept_id as accountingDeptId,
        count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.accounting_dept_id is not null

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>

            <if test="vo.batchNo != null and vo.batchNo != ''">
                and c_business_task.customer_service_id in
                <foreach collection="customerServiceIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.dispatchStatus != null">
                <if test="vo.dispatchStatus == 1">
                    and c_business_task.execute_user_id is null
                </if>
                <if test="vo.dispatchStatus == 2">
                    and c_business_task.execute_user_id is not null
                </if>
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.accounting_dept_id
    </select>
    <select id="searchListBusinessDeptCountForPeriod"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.business_dept_id as accountingDeptId,
        count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.business_dept_id is not null

            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.business_dept_id
    </select>
    <select id="searchListBusinessDeptCountForManager"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.business_dept_id as accountingDeptId,
        count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.business_dept_id is not null

            <if test="userDept != null">
                <if test="userDept.isAdmin == null or userDept.isAdmin == false">
                    <if test="userDept.deptType == null or userDept.deptIds == null or userDept.deptIds.size == 0">
                        and 1 = -1
                    </if>
                    <if test="userDept.deptType == 1 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.advisor_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.advisor_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                    <if test="userDept.deptType == 2 and userDept.deptIds != null and userDept.deptIds.size > 0">
                        and (c_customer_service_period_month.accounting_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or c_customer_service_period_month.accounting_top_dept_id in
                        <foreach collection="userDept.deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>)
                    </if>
                </if>
            </if>

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.accountingDeptId != null">
                and c_customer_service_period_month.accounting_dept_id = #{vo.accountingDeptId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.business_dept_id
    </select>
    <select id="searchListBusinessDeptCountForMy"
            resultType="com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO">
        select c_customer_service_period_month.business_dept_id as accountingDeptId,
        count(1) as dataCount
        from c_business_task
        left join c_customer_service_period_month on c_customer_service_period_month.id = c_business_task.biz_id
        <where>
            c_business_task.is_del = 0 and c_customer_service_period_month.business_dept_id is not null

            <if test="vo.keyWord != null and vo.keyWord != ''">
                and (
                c_business_task.customer_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.customer_company_name like concat('%', #{vo.keyWord}, '%')
                or c_business_task.credit_code = #{vo.keyWord}
                )
            </if>
            <if test="tagSearchVO.needSearch != null and tagSearchVO.needSearch == true">
                <if test="tagSearchVO.fail == null or tagSearchVO.fail == false">
                    <if test="tagSearchVO.tagIncludeFlag == 0">
                        and c_business_task.biz_id not in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="tagSearchVO.tagIncludeFlag == 1">
                        and c_business_task.biz_id in
                        <foreach collection="tagSearchVO.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                </if>
                <if test="tagSearchVO.fail != null and tagSearchVO.fail == true">
                    and 1 = -1
                </if>
            </if>
            <if test="vo.taxType != null">
                and c_customer_service_period_month.tax_type = #{vo.taxType}
            </if>
            <if test="vo.periodStart != null">
                and c_business_task.period &gt;= #{vo.periodStart}
            </if>
            <if test="vo.periodEnd != null">
                and c_business_task.period &lt;= #{vo.periodEnd}
            </if>
            <if test="vo.itemTypeList != null and vo.itemTypeList.size > 0">
                and c_business_task.item_type in
                <foreach collection="vo.itemTypeList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.adminUserId != null">
                and c_business_task.admin_user_id = #{vo.adminUserId}
            </if>
            <if test="vo.executeUserId != null">
                and c_business_task.execute_user_id = #{vo.executeUserId}
            </if>
            <if test="vo.statusList != null and vo.statusList.size > 0">
                and c_business_task.status in
                <foreach collection="vo.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="vo.finishResult != null">
                and c_business_task.finish_result = #{vo.finishResult}
            </if>
            <if test="vo.deadlineStart != null">
                and c_business_task.deadline &gt;= #{vo.deadlineStart}
            </if>
            <if test="vo.deadlineEnd != null">
                and c_business_task.deadline &lt;= #{vo.deadlineEnd}
            </if>
            <if test="vo.executeTimeStart != null">
                and c_business_task.execute_time &gt;= #{vo.executeTimeStart}
            </if>
            <if test="vo.executeTimeEnd != null">
                and c_business_task.execute_time &lt;= #{vo.executeTimeEnd}
            </if>
            <if test="vo.lastOperateUserName != null">
                and c_business_task.last_operate_user_name like concat('%', #{vo.lastOperateUserName}, '%')
            </if>
            <if test="vo.lastOperateTimeStart != null">
                and c_business_task.last_operate_time &gt;= #{vo.lastOperateTimeStart}
            </if>
            <if test="vo.lastOperateTimeEnd != null">
                and c_business_task.last_operate_time &lt;= #{vo.lastOperateTimeEnd}
            </if>
            <if test="vo.mediumPaper != null">
                and c_business_task.medium_paper = #{vo.mediumPaper}
            </if>
            <if test="vo.mediumElectric != null">
                and c_business_task.medium_electric = #{vo.mediumElectric}
            </if>
            <if test="vo.mediumBank != null">
                and c_business_task.medium_bank = #{vo.mediumBank}
            </if>
            <if test="vo.periodBusinessDeptId != null">
                and c_customer_service_period_month.business_dept_id = #{vo.periodBusinessDeptId}
            </if>
        </where>
        group by c_customer_service_period_month.business_dept_id
    </select>
    <select id="getByPeriodAndBankAccountNumber" resultType="com.bxm.customer.domain.BusinessTask">
        select
            *
            from c_business_task
        <where>
            is_del = 0
            and `type` = #{vo.type}
            and item_type = #{vo.itemType}
            and (biz_id, bank_account_number) in
            <foreach collection="vo.periodBankAccountNumberList" open="(" close=")" item="item" separator=",">
                (#{item.customerServicePeriodMonthId}, #{item.bankAccountNumber})
            </foreach>
        </where>
    </select>
    <select id="getBatchNeedCheckBusinessTaskByAccountingCashier"
            resultType="com.bxm.customer.domain.BusinessTask">
        select *
        from c_business_task
        <where>
            is_del = 0 and item_type = 1 and `status` = 2
        and (biz_id, bank_account_number) in
            <foreach collection="cashierList" open="(" close=")" item="item" separator=",">
                (#{item.customerServicePeriodMonthId}, #{item.bankAccountNumber})
            </foreach>
        </where>
    </select>
    <select id="selectLessTaskAdminDeptByDeptIds" resultType="java.lang.Long">
        SELECT a.dept_id FROM (
          SELECT
              sd.dept_id, COUNT(cb.id) AS taskCount
          FROM sys_dept sd LEFT JOIN c_business_task cb ON sd.dept_id = cb.admin_dept_id AND cb.is_del = 0 AND cb.`status` = 1
          WHERE sd.dept_id IN
          <foreach collection="deptIds" open="(" close=")" item="item" separator=",">
              #{item}
          </foreach>
          GROUP BY sd.dept_id) a ORDER BY a.taskCount ASC LIMIT 1
    </select>
</mapper>