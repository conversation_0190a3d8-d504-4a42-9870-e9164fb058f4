<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.QualityCheckingItemMapper">
    
    <resultMap type="com.bxm.customer.domain.QualityCheckingItem" id="QualityCheckingItemResult">
        <result property="id"    column="id"    />
        <result property="qualityCheckingType"    column="quality_checking_type"    />
        <result property="qualityCheckingCycle"    column="quality_checking_cycle"    />
        <result property="itemName"    column="item_name"    />
        <result property="checkingStandard"    column="checking_standard"    />
        <result property="itemRemark"    column="item_remark"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQualityCheckingItemVo">
        select id, quality_checking_type, quality_checking_cycle, item_name, checking_standard, item_remark, risk_level, status, create_by, create_time, update_by, update_time from c_quality_checking_item
    </sql>

    <select id="selectQualityCheckingItemList" parameterType="com.bxm.customer.domain.QualityCheckingItem" resultMap="QualityCheckingItemResult">
        <include refid="selectQualityCheckingItemVo"/>
        <where>  
            <if test="qualityCheckingType != null "> and quality_checking_type = #{qualityCheckingType}</if>
            <if test="qualityCheckingCycle != null "> and quality_checking_cycle = #{qualityCheckingCycle}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="checkingStandard != null  and checkingStandard != ''"> and checking_standard = #{checkingStandard}</if>
            <if test="itemRemark != null  and itemRemark != ''"> and item_remark = #{itemRemark}</if>
            <if test="riskLevel != null "> and risk_level = #{riskLevel}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectQualityCheckingItemById" parameterType="Long" resultMap="QualityCheckingItemResult">
        <include refid="selectQualityCheckingItemVo"/>
        where id = #{id}
    </select>
    <select id="qualityCheckingItemSelect" resultType="com.bxm.customer.domain.QualityCheckingItem">
        select id,item_name
        from c_quality_checking_item
        where `status` = 1
        <if test="qualityCheckingType != null">
            and quality_checking_type = #{qualityCheckingType}
        </if>
        <if test="qualityCheckingCycle != null">
            and quality_checking_cycle = #{qualityCheckingCycle}
        </if>
    </select>

    <insert id="insertQualityCheckingItem" parameterType="com.bxm.customer.domain.QualityCheckingItem" useGeneratedKeys="true" keyProperty="id">
        insert into c_quality_checking_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="qualityCheckingType != null">quality_checking_type,</if>
            <if test="qualityCheckingCycle != null">quality_checking_cycle,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="checkingStandard != null">checking_standard,</if>
            <if test="itemRemark != null">item_remark,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="qualityCheckingType != null">#{qualityCheckingType},</if>
            <if test="qualityCheckingCycle != null">#{qualityCheckingCycle},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="checkingStandard != null">#{checkingStandard},</if>
            <if test="itemRemark != null">#{itemRemark},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQualityCheckingItem" parameterType="com.bxm.customer.domain.QualityCheckingItem">
        update c_quality_checking_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="qualityCheckingType != null">quality_checking_type = #{qualityCheckingType},</if>
            <if test="qualityCheckingCycle != null">quality_checking_cycle = #{qualityCheckingCycle},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="checkingStandard != null">checking_standard = #{checkingStandard},</if>
            <if test="itemRemark != null">item_remark = #{itemRemark},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQualityCheckingItemById" parameterType="Long">
        delete from c_quality_checking_item where id = #{id}
    </delete>

    <delete id="deleteQualityCheckingItemByIds" parameterType="String">
        delete from c_quality_checking_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>