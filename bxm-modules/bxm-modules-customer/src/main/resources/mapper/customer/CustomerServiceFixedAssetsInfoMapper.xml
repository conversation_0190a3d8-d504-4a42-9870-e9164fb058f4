<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CustomerServiceFixedAssetsInfoMapper">
    
    <resultMap type="com.bxm.customer.domain.CustomerServiceFixedAssetsInfo" id="CustomerServiceFixedAssetsInfoResult">
        <result property="id"    column="id"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="assetName"    column="asset_name"    />
        <result property="occurrenceYear"    column="occurrence_year"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerServiceFixedAssetsInfoVo">
        select id, customer_service_id, asset_name, occurrence_year, create_by, create_time, update_by, update_time from c_customer_service_fixed_assets_info
    </sql>

    <select id="selectCustomerServiceFixedAssetsInfoList" parameterType="com.bxm.customer.domain.CustomerServiceFixedAssetsInfo" resultMap="CustomerServiceFixedAssetsInfoResult">
        <include refid="selectCustomerServiceFixedAssetsInfoVo"/>
        <where>  
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="occurrenceYear != null "> and occurrence_year = #{occurrenceYear}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectCustomerServiceFixedAssetsInfoById" parameterType="Long" resultMap="CustomerServiceFixedAssetsInfoResult">
        <include refid="selectCustomerServiceFixedAssetsInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerServiceFixedAssetsInfo" parameterType="com.bxm.customer.domain.CustomerServiceFixedAssetsInfo" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_service_fixed_assets_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="occurrenceYear != null">occurrence_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="occurrenceYear != null">#{occurrenceYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerServiceFixedAssetsInfo" parameterType="com.bxm.customer.domain.CustomerServiceFixedAssetsInfo">
        update c_customer_service_fixed_assets_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="occurrenceYear != null">occurrence_year = #{occurrenceYear},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceFixedAssetsInfoById" parameterType="Long">
        delete from c_customer_service_fixed_assets_info where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceFixedAssetsInfoByIds" parameterType="String">
        delete from c_customer_service_fixed_assets_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>