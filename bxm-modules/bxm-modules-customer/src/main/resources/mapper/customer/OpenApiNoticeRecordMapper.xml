<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.OpenApiNoticeRecordMapper">
    
    <resultMap type="com.bxm.customer.domain.OpenApiNoticeRecord" id="OpenApiNoticeRecordResult">
        <result property="id"    column="id"    />
        <result property="noticeSource"    column="notice_source"    />
        <result property="noticeTarget"    column="notice_target"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="noticeFunction"    column="notice_function"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiNoticeRecordVo">
        select id, notice_source, notice_target, notice_content, notice_function, is_del, create_by, create_time, update_by, update_time from c_open_api_notice_record
    </sql>

    <select id="selectOpenApiNoticeRecordList" parameterType="com.bxm.customer.domain.OpenApiNoticeRecord" resultMap="OpenApiNoticeRecordResult">
        <include refid="selectOpenApiNoticeRecordVo"/>
        <where>  
            <if test="noticeSource != null  and noticeSource != ''"> and notice_source = #{noticeSource}</if>
            <if test="noticeTarget != null  and noticeTarget != ''"> and notice_target = #{noticeTarget}</if>
            <if test="noticeContent != null  and noticeContent != ''"> and notice_content = #{noticeContent}</if>
            <if test="noticeFunction != null  and noticeFunction != ''"> and notice_function = #{noticeFunction}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectOpenApiNoticeRecordById" parameterType="Long" resultMap="OpenApiNoticeRecordResult">
        <include refid="selectOpenApiNoticeRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectRpaListCount" resultType="java.lang.Integer">
        select count(1)
        from c_open_api_notice_record nr
        <where>
            nr.is_rpa = 1
            <if test="vo.tabType == 1">
                and nr.user_id = #{userId}
            </if>
            <if test="vo.tabType == 2">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists (
                    select 1 from c_customer_service cs where cs.id = nr.customer_service_id
                    <if test="userDeptDTO.deptType == 1">
                        and (cs.advisor_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cs.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (cs.accounting_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cs.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>)
                </if>
            </if>
            <if test="vo.tabType == 3">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists (
                    select 1 from c_customer_service_period_month spm where spm.id = nr.customer_service_period_month_id
                    <if test="userDeptDTO.deptType == 1">
                        and (spm.advisor_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (spm.accounting_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>)
                </if>
            </if>
            <if test="(vo.keyWord != null and vo.keyWord != '') or (customerServiceIds != null and customerServiceIds.size > 0)">
                and exists (
                select 1 from c_customer_service cs where cs.id = nr.customer_service_id
                <if test="vo.keyWord != null and vo.keyWord != ''">
                    and (cs.customer_name like concat('%',#{vo.keyWord},'%') or cs.credit_code = #{vo.keyWord})
                </if>
                <if test="customerServiceIds != null and customerServiceIds.size > 0">
                    and cs.id in
                    <foreach item="customerServiceId" collection="customerServiceIds" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
                )
            </if>
            <if test="vo.taskType != null and vo.taskType != ''">
                and nr.task_type = #{vo.taskType}
            </if>
            <if test="vo.userName != null and vo.userName != ''">
                and exists (
                select 1 from sys_user su where su.user_id = nr.user_id
                and su.nick_name like concat('%',#{vo.userName},'%')
                )
            </if>
            <if test="vo.rpaDealResult != null">
                and nr.rpa_deal_result = #{vo.rpaDealResult}
            </if>
            <if test="vo.sysDeliverResult != null">
                and nr.sys_deliver_result = #{vo.sysDeliverResult}
            </if>
            <if test="vo.periodMin != null">
                and nr.period &gt;= #{vo.periodMin}
            </if>
            <if test="vo.periodMax != null">
                and nr.period &lt;= #{vo.periodMax}
            </if>
            <if test="vo.createTimeStart != null and vo.createTimeStart != ''">
                and nr.create_time &gt;= #{vo.createTimeStart}
            </if>
            <if test="vo.createTimeEnd != null and vo.createTimeEnd != ''">
                and nr.create_time &lt;= #{vo.createTimeEnd}
            </if>
            <if test="vo.deliverTimeStart != null and vo.deliverTimeStart != ''">
                and nr.deliver_time &gt;= #{vo.deliverTimeStart}
            </if>
            <if test="vo.deliverTimeEnd != null and vo.deliverTimeEnd != ''">
                and nr.deliver_time &lt;= #{vo.deliverTimeEnd}
            </if>
        </where>
    </select>
    <select id="selectRpaList" resultType="com.bxm.customer.domain.OpenApiNoticeRecord">
        select
            nr.id,
            nr.customer_service_id,
            nr.period,
            nr.task_type,
            nr.user_id,
            nr.rpa_deal_result,
            nr.sys_deliver_result,
            nr.create_time,
            nr.deliver_time
        from c_open_api_notice_record nr
        <where>
            nr.is_rpa = 1
            <if test="vo.tabType == 1">
                and nr.user_id = #{userId}
            </if>
            <if test="vo.tabType == 2">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists (
                    select 1 from c_customer_service cs where cs.id = nr.customer_service_id
                    <if test="userDeptDTO.deptType == 1">
                        and (cs.advisor_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cs.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (cs.accounting_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or cs.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>)
                </if>
            </if>
            <if test="vo.tabType == 3">
                <if test="userDeptDTO.deptIds != null and userDeptDTO.deptIds.size > 0">
                    and exists (
                    select 1 from c_customer_service_period_month spm where spm.id = nr.customer_service_period_month_id
                    <if test="userDeptDTO.deptType == 1">
                        and (spm.advisor_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.advisor_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>
                    <if test="userDeptDTO.deptType == 2">
                        and (spm.accounting_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        or spm.accounting_top_dept_id in
                        <foreach item="deptId" collection="userDeptDTO.deptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        )
                    </if>)
                </if>
            </if>
            <if test="(vo.keyWord != null and vo.keyWord != '') or (customerServiceIds != null and customerServiceIds.size > 0)">
                and exists (
                select 1 from c_customer_service cs where cs.id = nr.customer_service_id
                <if test="vo.keyWord != null and vo.keyWord != ''">
                    and (cs.customer_name like concat('%',#{vo.keyWord},'%') or cs.credit_code = #{vo.keyWord})
                </if>
                <if test="customerServiceIds != null and customerServiceIds.size > 0">
                    and cs.id in
                    <foreach item="customerServiceId" collection="customerServiceIds" open="(" separator="," close=")">
                        #{customerServiceId}
                    </foreach>
                </if>
                )
            </if>
            <if test="vo.taskType != null and vo.taskType != ''">
                and nr.task_type = #{vo.taskType}
            </if>
            <if test="vo.userName != null and vo.userName != ''">
                and exists (
                select 1 from sys_user su where su.user_id = nr.user_id
                and su.nick_name like concat('%',#{vo.userName},'%')
                )
            </if>
            <if test="vo.rpaDealResult != null">
                and nr.rpa_deal_result = #{vo.rpaDealResult}
            </if>
            <if test="vo.sysDeliverResult != null">
                and nr.sys_deliver_result = #{vo.sysDeliverResult}
            </if>
            <if test="vo.periodMin != null">
                and nr.period &gt;= #{vo.periodMin}
            </if>
            <if test="vo.periodMax != null">
                and nr.period &lt;= #{vo.periodMax}
            </if>
            <if test="vo.createTimeStart != null and vo.createTimeStart != ''">
                and nr.create_time &gt;= #{vo.createTimeStart}
            </if>
            <if test="vo.createTimeEnd != null and vo.createTimeEnd != ''">
                and nr.create_time &lt;= #{vo.createTimeEnd}
            </if>
            <if test="vo.deliverTimeStart != null and vo.deliverTimeStart != ''">
                and nr.deliver_time &gt;= #{vo.deliverTimeStart}
            </if>
            <if test="vo.deliverTimeEnd != null and vo.deliverTimeEnd != ''">
                and nr.deliver_time &lt;= #{vo.deliverTimeEnd}
            </if>
        </where>
        order by nr.id desc
        limit #{start},#{limit}
    </select>

    <insert id="insertOpenApiNoticeRecord" parameterType="com.bxm.customer.domain.OpenApiNoticeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_notice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">notice_source,</if>
            <if test="noticeTarget != null and noticeTarget != ''">notice_target,</if>
            <if test="noticeContent != null and noticeContent != ''">notice_content,</if>
            <if test="noticeFunction != null and noticeFunction != ''">notice_function,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">#{noticeSource},</if>
            <if test="noticeTarget != null and noticeTarget != ''">#{noticeTarget},</if>
            <if test="noticeContent != null and noticeContent != ''">#{noticeContent},</if>
            <if test="noticeFunction != null and noticeFunction != ''">#{noticeFunction},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiNoticeRecord" parameterType="com.bxm.customer.domain.OpenApiNoticeRecord">
        update c_open_api_notice_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">notice_source = #{noticeSource},</if>
            <if test="noticeTarget != null and noticeTarget != ''">notice_target = #{noticeTarget},</if>
            <if test="noticeContent != null and noticeContent != ''">notice_content = #{noticeContent},</if>
            <if test="noticeFunction != null and noticeFunction != ''">notice_function = #{noticeFunction},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiNoticeRecordById" parameterType="Long">
        delete from c_open_api_notice_record where id = #{id}
    </delete>

    <delete id="deleteOpenApiNoticeRecordByIds" parameterType="String">
        delete from c_open_api_notice_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>