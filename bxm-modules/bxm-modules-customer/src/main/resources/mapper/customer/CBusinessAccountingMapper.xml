<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.CBusinessAccountingMapper">
    
    <resultMap type="com.bxm.customer.domain.CBusinessAccounting" id="CBusinessAccountingResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="userId"    column="user_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCBusinessAccountingVo">
        select id, business_type, business_id, user_id, employee_id, dept_id, create_by, create_time, update_by, update_time from c_business_accounting
    </sql>

    <select id="selectCBusinessAccountingList" parameterType="com.bxm.customer.domain.CBusinessAccounting" resultMap="CBusinessAccountingResult">
        <include refid="selectCBusinessAccountingVo"/>
        <where>  
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>
    
    <select id="selectCBusinessAccountingById" parameterType="Long" resultMap="CBusinessAccountingResult">
        <include refid="selectCBusinessAccountingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCBusinessAccounting" parameterType="com.bxm.customer.domain.CBusinessAccounting" useGeneratedKeys="true" keyProperty="id">
        insert into c_business_accounting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCBusinessAccounting" parameterType="com.bxm.customer.domain.CBusinessAccounting">
        update c_business_accounting
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCBusinessAccountingById" parameterType="Long">
        delete from c_business_accounting where id = #{id}
    </delete>

    <delete id="deleteCBusinessAccountingByIds" parameterType="String">
        delete from c_business_accounting where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>