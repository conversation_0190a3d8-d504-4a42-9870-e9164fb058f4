package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.dto.RemoteSendQualityCheckingTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteQualityCheckingCreateVO;
import com.bxm.customer.api.domain.vo.RemoteSendQualityCheckingTaskVO;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDetailDTO;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultCheckVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultModifyVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultVO;

import java.util.List;

/**
 * 质检结果Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IQualityCheckingResultService extends IService<QualityCheckingResult>
{
    /**
     * 查询质检结果
     * 
     * @param id 质检结果主键
     * @return 质检结果
     */
    public QualityCheckingResult selectQualityCheckingResultById(Long id);

    /**
     * 查询质检结果列表
     * 
     * @param qualityCheckingResult 质检结果
     * @return 质检结果集合
     */
    public List<QualityCheckingResult> selectQualityCheckingResultList(QualityCheckingResult qualityCheckingResult);

    /**
     * 新增质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    public int insertQualityCheckingResult(QualityCheckingResult qualityCheckingResult);

    /**
     * 修改质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    public int updateQualityCheckingResult(QualityCheckingResult qualityCheckingResult);

    /**
     * 批量删除质检结果
     * 
     * @param ids 需要删除的质检结果主键集合
     * @return 结果
     */
    public int deleteQualityCheckingResultByIds(Long[] ids);

    /**
     * 删除质检结果信息
     * 
     * @param id 质检结果主键
     * @return 结果
     */
    public int deleteQualityCheckingResultById(Long id);

    IPage<QualityCheckingResultDTO> qualityCheckResultPageList(QualityCheckingResultVO vo);

    TCommonOperateDTO<QualityCheckingResult> modifyQualityResult(Long deptId, QualityCheckingResultModifyVO vo);

    TCommonOperateDTO<QualityCheckingResult> checkQualityResult(Long deptId, QualityCheckingResultCheckVO vo);

    QualityCheckingResultDetailDTO qualityCheckingResultDetail(Long deptId, Long qualityCheckingResultId);

    void remoteCreateQualityChecking(RemoteQualityCheckingCreateVO vo);

    RemoteCustomerPeriodDTO remoteCreateQualityCheckingV2(RemoteQualityCheckingCreateVO vo);

    void dealQualityChecking(CommonNoticeVO commonNoticeVO);

    void dealQualityCheckingV2(CommonNoticeVO commonNoticeVO);

    void overTimeClose();

    void remoteSendQualityCheckingTask(RemoteSendQualityCheckingTaskVO vo);
}
