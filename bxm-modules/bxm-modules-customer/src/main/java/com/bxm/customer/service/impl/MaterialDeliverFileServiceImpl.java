package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.MaterialDeliverFileMapper;
import com.bxm.customer.domain.MaterialDeliverFile;
import com.bxm.customer.service.IMaterialDeliverFileService;

/**
 * 材料交接单文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Service
public class MaterialDeliverFileServiceImpl extends ServiceImpl<MaterialDeliverFileMapper, MaterialDeliverFile> implements IMaterialDeliverFileService
{
    @Autowired
    private MaterialDeliverFileMapper materialDeliverFileMapper;

    /**
     * 查询材料交接单文件
     * 
     * @param id 材料交接单文件主键
     * @return 材料交接单文件
     */
    @Override
    public MaterialDeliverFile selectMaterialDeliverFileById(Long id)
    {
        return materialDeliverFileMapper.selectMaterialDeliverFileById(id);
    }

    /**
     * 查询材料交接单文件列表
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 材料交接单文件
     */
    @Override
    public List<MaterialDeliverFile> selectMaterialDeliverFileList(MaterialDeliverFile materialDeliverFile)
    {
        return materialDeliverFileMapper.selectMaterialDeliverFileList(materialDeliverFile);
    }

    /**
     * 新增材料交接单文件
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 结果
     */
    @Override
    public int insertMaterialDeliverFile(MaterialDeliverFile materialDeliverFile)
    {
        materialDeliverFile.setCreateTime(DateUtils.getNowDate());
        return materialDeliverFileMapper.insertMaterialDeliverFile(materialDeliverFile);
    }

    /**
     * 修改材料交接单文件
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 结果
     */
    @Override
    public int updateMaterialDeliverFile(MaterialDeliverFile materialDeliverFile)
    {
        materialDeliverFile.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverFileMapper.updateMaterialDeliverFile(materialDeliverFile);
    }

    /**
     * 批量删除材料交接单文件
     * 
     * @param ids 需要删除的材料交接单文件主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileByIds(Long[] ids)
    {
        return materialDeliverFileMapper.deleteMaterialDeliverFileByIds(ids);
    }

    /**
     * 删除材料交接单文件信息
     * 
     * @param id 材料交接单文件主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileById(Long id)
    {
        return materialDeliverFileMapper.deleteMaterialDeliverFileById(id);
    }
}
