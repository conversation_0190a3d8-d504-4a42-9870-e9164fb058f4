package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument;

/**
 * 补账临时的 材料交接银行票据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceRepairAccountTempBankInstrumentMapper extends BaseMapper<CustomerServiceRepairAccountTempBankInstrument>
{
    /**
     * 查询补账临时的 材料交接银行票据
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 补账临时的 材料交接银行票据
     */
    public CustomerServiceRepairAccountTempBankInstrument selectCustomerServiceRepairAccountTempBankInstrumentById(Long id);

    /**
     * 查询补账临时的 材料交接银行票据列表
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 补账临时的 材料交接银行票据集合
     */
    public List<CustomerServiceRepairAccountTempBankInstrument> selectCustomerServiceRepairAccountTempBankInstrumentList(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 新增补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 修改补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 删除补账临时的 材料交接银行票据
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempBankInstrumentById(Long id);

    /**
     * 批量删除补账临时的 材料交接银行票据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempBankInstrumentByIds(Long[] ids);
}
