package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.ThirdpartFileUploadRecordMapper;
import com.bxm.customer.domain.ThirdpartFileUploadRecord;
import com.bxm.customer.service.IThirdpartFileUploadRecordService;

/**
 * 第三方文件上传记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class ThirdpartFileUploadRecordServiceImpl extends ServiceImpl<ThirdpartFileUploadRecordMapper, ThirdpartFileUploadRecord> implements IThirdpartFileUploadRecordService
{
    @Autowired
    private ThirdpartFileUploadRecordMapper thirdpartFileUploadRecordMapper;

    /**
     * 查询第三方文件上传记录
     * 
     * @param id 第三方文件上传记录主键
     * @return 第三方文件上传记录
     */
    @Override
    public ThirdpartFileUploadRecord selectThirdpartFileUploadRecordById(Long id)
    {
        return thirdpartFileUploadRecordMapper.selectThirdpartFileUploadRecordById(id);
    }

    /**
     * 查询第三方文件上传记录列表
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 第三方文件上传记录
     */
    @Override
    public List<ThirdpartFileUploadRecord> selectThirdpartFileUploadRecordList(ThirdpartFileUploadRecord thirdpartFileUploadRecord)
    {
        return thirdpartFileUploadRecordMapper.selectThirdpartFileUploadRecordList(thirdpartFileUploadRecord);
    }

    /**
     * 新增第三方文件上传记录
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 结果
     */
    @Override
    public int insertThirdpartFileUploadRecord(ThirdpartFileUploadRecord thirdpartFileUploadRecord)
    {
        thirdpartFileUploadRecord.setCreateTime(DateUtils.getNowDate());
        return thirdpartFileUploadRecordMapper.insertThirdpartFileUploadRecord(thirdpartFileUploadRecord);
    }

    /**
     * 修改第三方文件上传记录
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 结果
     */
    @Override
    public int updateThirdpartFileUploadRecord(ThirdpartFileUploadRecord thirdpartFileUploadRecord)
    {
        thirdpartFileUploadRecord.setUpdateTime(DateUtils.getNowDate());
        return thirdpartFileUploadRecordMapper.updateThirdpartFileUploadRecord(thirdpartFileUploadRecord);
    }

    /**
     * 批量删除第三方文件上传记录
     * 
     * @param ids 需要删除的第三方文件上传记录主键
     * @return 结果
     */
    @Override
    public int deleteThirdpartFileUploadRecordByIds(Long[] ids)
    {
        return thirdpartFileUploadRecordMapper.deleteThirdpartFileUploadRecordByIds(ids);
    }

    /**
     * 删除第三方文件上传记录信息
     * 
     * @param id 第三方文件上传记录主键
     * @return 结果
     */
    @Override
    public int deleteThirdpartFileUploadRecordById(Long id)
    {
        return thirdpartFileUploadRecordMapper.deleteThirdpartFileUploadRecordById(id);
    }
}
