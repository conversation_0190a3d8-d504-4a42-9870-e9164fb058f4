package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceDocHandoverBankInstrument;

/**
 * 材料交接银行票据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface CustomerServiceDocHandoverBankInstrumentMapper extends BaseMapper<CustomerServiceDocHandoverBankInstrument>
{
    /**
     * 查询材料交接银行票据
     * 
     * @param id 材料交接银行票据主键
     * @return 材料交接银行票据
     */
    public CustomerServiceDocHandoverBankInstrument selectCustomerServiceDocHandoverBankInstrumentById(Long id);

    /**
     * 查询材料交接银行票据列表
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 材料交接银行票据集合
     */
    public List<CustomerServiceDocHandoverBankInstrument> selectCustomerServiceDocHandoverBankInstrumentList(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument);

    /**
     * 新增材料交接银行票据
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 结果
     */
    public int insertCustomerServiceDocHandoverBankInstrument(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument);

    /**
     * 修改材料交接银行票据
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 结果
     */
    public int updateCustomerServiceDocHandoverBankInstrument(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument);

    /**
     * 删除材料交接银行票据
     * 
     * @param id 材料交接银行票据主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverBankInstrumentById(Long id);

    /**
     * 批量删除材料交接银行票据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverBankInstrumentByIds(Long[] ids);
}
