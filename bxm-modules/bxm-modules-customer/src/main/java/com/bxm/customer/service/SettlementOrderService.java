package com.bxm.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.settlementOrder.*;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskDTO;
import com.bxm.customer.domain.vo.settlementOrder.*;
import com.bxm.customer.domain.vo.settlementOrder.task.BusinessDeptPeriodListForTaskVO;
import com.bxm.customer.domain.vo.settlementOrder.task.DeleteBusinessPeriodListForTaskVO;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SettlementOrderService {

    @Autowired
    private ISettlementOrderService settlementOrderService;

    @Autowired
    private ISettlementOrderFileService settlementOrderFileService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ISettlementOrderConditionService settlementOrderConditionService;

    @Autowired
    private ISettlementOrderDataTempService settlementOrderDataTempService;

    @Autowired
    private ISettlementOrderDataService settlementOrderDataService;

    @Autowired
    private IBillService billService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RedisService redisService;

    public IPage<SettlementOrderDTO> settlementOrderList(Long deptId, SettlementOrderSearchVO vo) {
        IPage<SettlementOrderDTO> result = new Page<>();
        List<Long> businessDeptIds = Lists.newArrayList();
        if (!Objects.isNull(vo.getBusinessDeptId())) {
            SysDept businessDept = remoteDeptService.getDeptInfo(vo.getBusinessDeptId()).getDataThrowException();
            if (Objects.isNull(businessDept)) {
                return result;
            }
            if (businessDept.getLevel() == 1) {
                List<SysDept> depts = remoteDeptService.getByParentId(vo.getBusinessDeptId()).getDataThrowException();
                if (ObjectUtils.isEmpty(depts)) {
                    return result;
                }
                businessDeptIds = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            } else if (businessDept.getLevel() == 2) {
                businessDeptIds = Collections.singletonList(vo.getBusinessDeptId());
            } else {
                return result;
            }
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeStart())) {
            vo.setCreateTimeStart(vo.getCreateTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeEnd())) {
            vo.setCreateTimeEnd(vo.getCreateTimeEnd() + " 23:59:59");
        }
        IPage<SettlementOrder> iPage = settlementOrderService.page(new Page<>(vo.getPageNum(), vo.getPageSize()), new LambdaQueryWrapper<SettlementOrder>()
                .eq(SettlementOrder::getIsDel, false)
                .in(!ObjectUtils.isEmpty(businessDeptIds), SettlementOrder::getBusinessDeptId, businessDeptIds)
                .eq(!Objects.isNull(vo.getSettlementType()), SettlementOrder::getSettlementType, vo.getSettlementType())
                .eq(!Objects.isNull(vo.getStatus()), SettlementOrder::getStatus, vo.getStatus())
                .eq(!Objects.isNull(vo.getIsSupplement()), SettlementOrder::getIsSupplement, vo.getIsSupplement())
                .eq(!Objects.isNull(vo.getIsWaitForEdit()), SettlementOrder::getIsWaitForEdit, vo.getIsWaitForEdit())
                .le(!StringUtils.isEmpty(vo.getCreateTimeEnd()), SettlementOrder::getCreateTime, vo.getCreateTimeEnd())
                .ge(!StringUtils.isEmpty(vo.getCreateTimeStart()), SettlementOrder::getCreateTime, vo.getCreateTimeStart())
                .and(!StringUtils.isEmpty(vo.getBillInfo()), wrapper -> wrapper.like(SettlementOrder::getBillNo, vo.getBillInfo()).or().like(SettlementOrder::getSettlementTitle, vo.getBillInfo()))
                .orderByDesc(SettlementOrder::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            result.setRecords(buildSettlementOrderDTO(iPage.getRecords()));
        }
        return result;
    }

    private List<SettlementOrderDTO> buildSettlementOrderDTO(List<SettlementOrder> records) {
        if (ObjectUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        Set<Long> deptIds = Sets.newHashSet();
        records.forEach(r -> {
            deptIds.add(r.getBusinessTopDeptId());
            deptIds.add(r.getBusinessDeptId());
        });
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(new ArrayList<>(deptIds)).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        List<SettlementOrderDataCountDTO> settlementOrderDataCountList = settlementOrderDataService.selectBatchSettlementOrderDataCount(records.stream().map(SettlementOrder::getId).collect(Collectors.toList()));
        Map<Long, Long> dataCountMap = settlementOrderDataCountList.stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
        return records.stream().map(r -> {
            SettlementOrderDTO dto = new SettlementOrderDTO();
            BeanUtils.copyProperties(r, dto);
            dto.setDataCount(dataCountMap.getOrDefault(r.getId(), 0L));
            dto.setTotalPrice(r.getPrice().multiply(new BigDecimal(dto.getDataCount())));
            dto.setSettlementPrice(dto.getTotalPrice().subtract(Objects.isNull(r.getDiscountPrice()) ? BigDecimal.ZERO : r.getDiscountPrice()));
            dto.setBusinessTopDeptName(deptNameMap.getOrDefault(r.getBusinessTopDeptId(), ""));
            dto.setBusinessDeptName(deptNameMap.getOrDefault(r.getBusinessDeptId(), ""));
            dto.setSettlementTypeName(SettlementType.getByCode(r.getSettlementType()).getName() + (r.getIsSupplement() ? "（补差）" : ""));
            dto.setPriceStr(Objects.isNull(r.getPrice()) ? "" : r.getPrice().stripTrailingZeros().toPlainString());
            dto.setTotalPriceStr(Objects.isNull(dto.getTotalPrice()) ? "" : dto.getTotalPrice().stripTrailingZeros().toPlainString());
            dto.setDiscountPriceStr(Objects.isNull(r.getDiscountPrice()) ? "" : r.getDiscountPrice().stripTrailingZeros().toPlainString());
            dto.setSettlementPriceStr(Objects.isNull(dto.getSettlementPrice()) ? "" : dto.getSettlementPrice().stripTrailingZeros().toPlainString());
            dto.setCreateTimeStr(Objects.isNull(r.getCreateTime()) ? "" : r.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            dto.setStatusName(SettlementOrderStatus.getByCode(r.getStatus()).getName());
            return dto;
        }).collect(Collectors.toList());
    }

    public SettlementOrderDetailDTO settlementOrderDetail(Long settlementOrderId) {
        SettlementOrder settlementOrder = settlementOrderService.getById(settlementOrderId);
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            throw new ServiceException("结算单不存在");
        }
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(Arrays.asList(settlementOrder.getBusinessDeptId(), settlementOrder.getBusinessTopDeptId())).getDataThrowException()
                .stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        List<SettlementOrderFile> files = settlementOrderFileService.selectBySettlementOrderIdAndFileType(settlementOrderId, SettlementOrderFileType.CREATE_FILE.getCode());
        List<SettlementOrderDataCountDTO> settlementOrderDataCountList = settlementOrderDataService.selectBatchSettlementOrderDataCount(Collections.singletonList(settlementOrderId));
        SettlementOrderDetailDTO dto = new SettlementOrderDetailDTO();
        BeanUtils.copyProperties(settlementOrder, dto);
        dto.setDataCount(ObjectUtils.isEmpty(settlementOrderDataCountList) ? 0L : settlementOrderDataCountList.get(0).getDataCount());
        dto.setTotalPrice(settlementOrder.getPrice().multiply(new BigDecimal(dto.getDataCount())));
        dto.setSettlementPrice(dto.getTotalPrice().subtract(Objects.isNull(settlementOrder.getDiscountPrice()) ? BigDecimal.ZERO : settlementOrder.getDiscountPrice()));
        dto.setBusinessTopDeptName(deptNameMap.getOrDefault(settlementOrder.getBusinessTopDeptId(), ""));
        dto.setBusinessDeptName(deptNameMap.getOrDefault(settlementOrder.getBusinessDeptId(), ""));
        dto.setSettlementTypeName(SettlementType.getByCode(settlementOrder.getSettlementType()).getName());
        dto.setFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() :
                files.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl())
                        .fullFileUrl(fileService.getFullFileUrl(f.getFileUrl())).build()).collect(Collectors.toList()));
        List<SettlementOrderCondition> conditions = settlementOrderConditionService.selectBySettlementOrderId(settlementOrderId);
        Optional<SettlementOrderCondition> optional = conditions.stream().filter(c -> Objects.equals(c.getConditionType(), "periodRange") || Objects.equals(c.getConditionType(), "customerServiceRange")).findFirst();
        Optional<SettlementOrderCondition> accountingTopDeptOptional = conditions.stream().filter(c -> Objects.equals(c.getConditionType(), "customerServiceAccountingTopDeptIds") || Objects.equals(c.getConditionType(), "periodAccountingTopDeptIds")).findFirst();
        if (optional.isPresent()) {
            SettlementOrderCondition rangeCondition = optional.get();
            Map<String, String> map = JSONObject.parseObject(rangeCondition.getConditionValue(), Map.class);
            map.put("fullFileUrl", fileService.getFullFileUrl(map.get("fileUrl")));
            rangeCondition.setConditionValue(JSONObject.toJSONString(map));
        }
        if (accountingTopDeptOptional.isPresent()) {
            SettlementOrderCondition accountingTopDeptIdCondition = accountingTopDeptOptional.get();
            Map<String, String> map = JSONObject.parseObject(accountingTopDeptIdCondition.getConditionValue(), Map.class);
            List<Long> deptIds = Arrays.stream(map.get(accountingTopDeptIdCondition.getConditionType()).split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<SysDept> depts = remoteDeptService.getByDeptIds(deptIds).getDataThrowException();
            map.put("deptNames", ObjectUtils.isEmpty(depts) ? "" : depts.stream().map(SysDept::getDeptName).collect(Collectors.joining("，")));
            accountingTopDeptIdCondition.setConditionValue(JSONObject.toJSONString(map));
        }
        dto.setSettlementConditions(conditions);
        dto.setStatusName(SettlementOrderStatus.getByCode(settlementOrder.getStatus()).getName());
        return dto;
    }

    public SettlementOrderDeptPriceDTO businessDeptPriceList(SettlementBusinessPriceGetVO vo) {
        return settlementOrderService.businessDeptPriceList(vo);
    }

    //******** START 仿照 结算单 的 任务用的几个接口
    public BusinessPeriodListForTaskDTO businessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo) {
        return settlementOrderService.businessPeriodListForTask(vo);
    }

    public BusinessPeriodListForTaskDTO freshBusinessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo) {
        return settlementOrderService.freshBusinessPeriodListForTask(vo);
    }

    public void deleteBusinessPeriodListForTask(DeleteBusinessPeriodListForTaskVO vo) {
        settlementOrderService.deleteBusinessPeriodListForTask(vo);
    }
    //******** END 仿照 结算单 的 任务用的几个接口

    public SettlementOrderDeptPriceDTO freshBusinessDeptPriceList(SettlementBusinessPriceGetVO vo) {
        return settlementOrderService.freshBusinessDeptPriceList(vo);
    }

    public void createSettlementOrder(SettlementOrderCreateVO vo) {
        settlementOrderService.createSettlementOrder(vo);
    }

    public void modifySettlementOrder(SettlementOrderCreateVO vo) {
        settlementOrderService.modifySettlementOrder(vo);
    }

    public IPage<SettlementOrderDataDTO> settlementOrderDataListByConditions(SettlementOrderDataSearchVO vo) {
        return settlementOrderDataTempService.settlementOrderDataListByBatchNo(vo);
    }

    public IPage<SettlementOrderDataDTO> settlementOrderDataListBySettlementOrderId(SettlementOrderDataSearchVO vo) {
        return settlementOrderDataService.settlementOrderDataListBySettlementOrderId(vo);
    }

    public String uploadDetailFile(MultipartFile file, Integer settlementType, Integer isSupplement, String batchNo, Long settlementOrderId, Long businessDeptId) {
        return settlementOrderService.uploadDetailFile(file, settlementType, isSupplement, batchNo, settlementOrderId, businessDeptId);
    }

    public SettlementOrderUploadCheckResultDTO getCheckResult(String uploadBatchNo, Integer settlementType) {
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode()) || Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            SettlementOrderUploadCheckResultDTO checkResultDTO = redisService.getCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT + uploadBatchNo);
            if (Objects.isNull(checkResultDTO)) {
                throw new ServiceException("批次号不存在");
            }
            if (!checkResultDTO.getIsComplete()) {
                Object completeCount = redisService.getCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_DATA_COUNT + uploadBatchNo);
                checkResultDTO.setCheckDataCount(Objects.isNull(completeCount) ? 0L : Long.parseLong(completeCount.toString()));
            }
            return checkResultDTO;
        } else {
            SettlementOrderUploadCheckResultDTO checkResultDTO = redisService.getCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT + uploadBatchNo);
            if (Objects.isNull(checkResultDTO)) {
                throw new ServiceException("批次号不存在");
            }
            if (!checkResultDTO.getIsComplete()) {
                Object completeCount = redisService.getCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_DATA_COUNT + uploadBatchNo);
                checkResultDTO.setCheckDataCount(Objects.isNull(completeCount) ? 0L : Long.parseLong(completeCount.toString()));
            }
            return checkResultDTO;
        }
    }

    public AddDataResultDTO confirmAddData(String uploadBatchNo, String batchNo, Long settlementOrderId, Integer settlementType, Long businessDeptId) {
        SettlementOrderUploadCheckResultDTO checkResult = getCheckResult(uploadBatchNo, settlementType);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("解析未完成");
        }
        return settlementOrderService.confirmAddData(uploadBatchNo, batchNo, settlementOrderId, settlementType, businessDeptId);
    }

    public void deleteSettlementOrderDataWhenCreate(CommonIdVO vo) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return;
        }
        settlementOrderDataTempService.removeByIds(vo.getIds());
    }

    @Transactional
    public void deleteSettlementOrderDataWhenModify(CommonIdVO vo) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return;
        }
        List<SettlementOrderData> settlementOrderDataList = settlementOrderDataService.getBaseMapper().selectBatchIds(vo.getIds());
        SettlementOrderData orderData = settlementOrderDataService.getById(vo.getIds().get(0));
        if (!Objects.isNull(orderData)) {
            SettlementOrder settlementOrder = settlementOrderService.getById(orderData.getSettlementOrderId());
            if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode())) {
                if (settlementOrder.getIsSupplement()) {
                    customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                            .eq(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.SUPPLEMENT_SETTLEMENTING.getCode())
                            .in(CustomerServicePeriodMonth::getId, settlementOrderDataList.stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList()))
                            .set(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.SETTLED.getCode()));
                } else {
                    customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                            .eq(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.SETTLEMENTING.getCode())
                            .in(CustomerServicePeriodMonth::getId, settlementOrderDataList.stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList()))
                            .set(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.WAIT_SETTLEMENT.getCode()));
                }
            } else if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
                customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getPrepayStatus, PeriodPrepayStatus.PREPAID.getCode())
                        .in(CustomerServicePeriodMonth::getId, settlementOrderDataList.stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList()))
                        .set(CustomerServicePeriodMonth::getPrepayStatus, PeriodPrepayStatus.UNPREPAID.getCode()));
            } else {
                customerServiceService.update(new LambdaUpdateWrapper<CCustomerService>()
                        .eq(CCustomerService::getSettlementStatus, BusinessSettlementStatus.SETTLEMENTING.getCode())
                        .in(CCustomerService::getId, settlementOrderDataList.stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList()))
                        .set(CCustomerService::getSettlementStatus, BusinessSettlementStatus.WAIT_SETTLEMENT.getCode()));
            }
        }
        settlementOrderDataService.removeByIds(vo.getIds());
    }

    public List<SettlementPushReviewDTO> pushReview(CommonIdVO vo) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return Lists.newArrayList();
        }
        return settlementOrderService.pushReview(vo.getIds());
    }

    public void confirmPush(SettlementOrderPushVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessDeptSettlementOrders())) {
            return;
        }
        billService.createBillBySettlementOrderPush(vo);
    }

    public List<Bill> getSameBusinessDeptBillList(Long settlementOrderId) {
        SettlementOrder settlementOrder = settlementOrderService.getById(settlementOrderId);
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            return Lists.newArrayList();
        }
        return billService.list(new LambdaQueryWrapper<Bill>()
                .eq(Bill::getBusinessDeptId, settlementOrder.getBusinessDeptId())
                .in(Bill::getStatus, BillStatus.canAppendStatus())
                .eq(Bill::getIsDel, false));
    }

    public void settlementAppendToBill(SettlementAppendBillVO vo) {
        billService.settlementAppendToBill(vo);
    }

    public void deleteSettlementOrder(Long id) {
        settlementOrderService.deleteSettlementOrder(id);
    }

    public CommonOperateResultDTO deleteSettlementOrderBatch(List<Long> ids) {
        return settlementOrderService.deleteSettlementOrderBatch(ids);
    }
}
