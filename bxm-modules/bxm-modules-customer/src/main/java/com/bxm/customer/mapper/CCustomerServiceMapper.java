package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodBankDTO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerBusinessTopDeptCreditCodeVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户服务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface CCustomerServiceMapper extends BaseMapper<CCustomerService> {
    /**
     * 查询客户服务
     *
     * @param id 客户服务主键
     * @return 客户服务
     */
    public CCustomerService selectCCustomerServiceById(Long id);

    /**
     * 查询客户服务列表
     *
     * @param cCustomerService 客户服务
     * @return 客户服务集合
     */
    public List<CCustomerService> selectCCustomerServiceList(CCustomerService cCustomerService);

    /**
     * 新增客户服务
     *
     * @param cCustomerService 客户服务
     * @return 结果
     */
    public int insertCCustomerService(CCustomerService cCustomerService);

    /**
     * 修改客户服务
     *
     * @param cCustomerService 客户服务
     * @return 结果
     */
    public int updateCCustomerService(CCustomerService cCustomerService);

    /**
     * 删除客户服务
     *
     * @param id 客户服务主键
     * @return 结果
     */
    public int deleteCCustomerServiceById(Long id);

    /**
     * 批量删除客户服务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCCustomerServiceByIds(Long[] ids);

    List<CustomerServiceDTO> customerServiceList(IPage<CustomerServiceDTO> result, @Param("ids") List<Long> ids, @Param("vo") CustomerServiceSearchVO vo,
                                                 @Param("dateVO") PeriodIncomeDateVO dateVO, @Param("deptType") Integer deptType, @Param("deptIds") List<Long> deptIds,
                                                 @Param("isAdmin") Integer isAdmin, @Param("tagIncludeFlag") Integer tagIncludeFlag, @Param("customerServiceIds") List<Long> customerServiceIds);

    List<CommonDeptCountDTO> customerServiceAccountingDeptCountList(@Param("ids") List<Long> ids, @Param("vo") CustomerServiceSearchVO vo,
                                                                    @Param("dateVO") PeriodIncomeDateVO dateVO, @Param("deptType") Integer deptType, @Param("deptIds") List<Long> deptIds,
                                                                    @Param("isAdmin") Integer isAdmin, @Param("tagIncludeFlag") Integer tagIncludeFlag);

    List<CommonDeptCountDTO> customerServiceAdvisorDeptCountList(@Param("ids") List<Long> ids, @Param("vo") CustomerServiceSearchVO vo,
                                                                    @Param("dateVO") PeriodIncomeDateVO dateVO, @Param("deptType") Integer deptType, @Param("deptIds") List<Long> deptIds,
                                                                    @Param("isAdmin") Integer isAdmin, @Param("tagIncludeFlag") Integer tagIncludeFlag);

    List<CustomerServiceDTO> customerServiceListByCreditCode(@Param("creditCode") String creditCode);

    /**
     * 客户服务预警列表
     *
     * @param iPage
     * @param keyWord
     * @param queryDeptIds 根据下拉框里选择的deptId查询出来的结果集，决定下拉选择的结果
     * @param deptIds 根据请求头里的deptId查询出来的结果集，决定数据权限
     * @param periodIncomeVO
     * @param deptType
     * @param customerServiceIds
     * @param tagName
     * @param tagIncludeType
     * @return
     */
    List<CustomerServiceIncomeExcessDTO> customerServiceWarningListByAccountingDept(IPage<CustomerServiceIncomeExcessDTO> iPage,
                                                                                    @Param("keyWord") String keyWord, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("advisorSearchDeptIds") List<Long> advisorSearchDeptIds, @Param("accountingSearchDeptIds") List<Long> accountingSearchDeptIds,
                                                                                    @Param("deptIds") List<Long> deptIds, @Param("dateVO") PeriodIncomeDateVO periodIncomeVO,
                                                                                    @Param("deptType") Integer deptType, @Param("customerServiceIds") List<Long> customerServiceIds,
                                                                                    @Param("tagName") String tagName, @Param("tagIncludeType") Integer tagIncludeType,
                                                                                    @Param("seasonExcessYellowLevel") BigDecimal seasonExcessYellowLevel, @Param("yearExcessYellowLevel") BigDecimal yearExcessYellowLevel,
                                                                                    @Param("nowPeriod") Integer nowPeriod);

    List<CustomerDeliverMiniDTO> customerDeliverMiniList(IPage<CustomerDeliverMiniDTO> iPage, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("queryDeptType") Integer queryDeptType, @Param("deliverType") Integer deliverType,
                                                         @Param("deliverStatus") Integer deliverStatus, @Param("deliverStatusList") List<Integer> deliverStatusList,
                                                         @Param("customerName") String customerName, @Param("tagName") String tagName,
                                                         @Param("tagIncludeFlag") Integer tagIncludeType, @Param("userDept") UserDeptDTO userDeptDTO,
                                                         @Param("period") Integer period, @Param("month") Integer month, @Param("advisorDeptId") Long advisorDeptId, @Param("accountingDeptId") Long accountingDeptId,
                                                         @Param("periodIds") List<Long> periodIds, @Param("status") Integer status,
                                                         @Param("customerServiceIds") List<Long> customerServiceIds,
                                                         @Param("customerServiceTagName") String customerServiceTagName,
                                                         @Param("customerServiceTagIncludeFlag") Integer customerServiceTagIncludeFlag,
                                                         @Param("customerServiceAdvisorDeptId") Long customerServiceAdvisorDeptId,
                                                         @Param("customerServiceAccountingDeptId") Long customerServiceAccountingDeptId,
                                                         @Param("customerServiceTaxType") Integer customerServiceTaxType,
                                                         @Param("periodTagName") String periodTagName,
                                                         @Param("periodTagIncludeFlag") Integer periodTagIncludeFlag,
                                                         @Param("periodAdvisorDeptId") Long periodAdvisorDeptId,
                                                         @Param("periodAccountingDeptId") Long periodAccountingDeptId,
                                                         @Param("periodTaxType") Integer periodTaxType,
                                                         @Param("taxCheckType") String taxCheckType);

    List<CustomerDeliverMiniDTO> waitCreateDeliverList(@Param("deliverType") Integer deliverType,
                                                         @Param("period") Integer period, @Param("month") Integer month);

    List<CustomerDeliverMiniDTO> customerDeliverMiniList(@Param("queryDeptIds") List<Long> queryDeptIds, @Param("queryDeptType") Integer queryDeptType,
                                                         @Param("deliverType") Integer deliverType,
                                                         @Param("deliverStatus") Integer deliverStatus, @Param("deliverStatusList") List<Integer> deliverStatusList,
                                                         @Param("customerName") String customerName, @Param("tagName") String tagName,
                                                         @Param("tagIncludeFlag") Integer tagIncludeType, @Param("userDept") UserDeptDTO userDeptDTO,
                                                         @Param("period") Integer period, @Param("month") Integer month, @Param("advisorDeptId") Long advisorDeptId, @Param("accountingDeptId") Long accountingDeptId,
                                                         @Param("periodIds") List<Long> periodIds, @Param("status") Integer status,
                                                         @Param("customerServiceIds") List<Long> customerServiceIds,
                                                         @Param("customerServiceTagName") String customerServiceTagName,
                                                         @Param("customerServiceTagIncludeFlag") Integer customerServiceTagIncludeFlag,
                                                         @Param("customerServiceAdvisorDeptId") Long customerServiceAdvisorDeptId,
                                                         @Param("customerServiceAccountingDeptId") Long customerServiceAccountingDeptId,
                                                         @Param("customerServiceTaxType") Integer customerServiceTaxType,
                                                         @Param("periodTagName") String periodTagName,
                                                         @Param("periodTagIncludeFlag") Integer periodTagIncludeFlag,
                                                         @Param("periodAdvisorDeptId") Long periodAdvisorDeptId,
                                                         @Param("periodAccountingDeptId") Long periodAccountingDeptId,
                                                         @Param("periodTaxType") Integer periodTaxType,
                                                         @Param("taxCheckType") String taxCheckType);

    List<CustomerDeliverMiniDTO> medicalSocialWaitDealList(@Param("operType") Integer operType, @Param("deliverStatusList") List<Integer> deliverStatusList, @Param("userDept") UserDeptDTO userDeptDTO,
                                                         @Param("period") Integer period);

    List<CustomerDeliverMiniDTO> updateFileWaitDealList(@Param("operType") Integer operType, @Param("deliverStatusList") List<Integer> deliverStatusList, @Param("userDept") UserDeptDTO userDeptDTO,
                                                           @Param("period") Integer period, @Param("deliverType") Integer deliverType);

    void saveCreateOperateLog(@Param("customerServiceIds") List<Long> customerServiceIds);

    Long countCustomerServiceWarningListByAccountingDept(@Param("keyWord") String keyWord, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("searchDeptIds") List<Long> searchDeptIds,
                                                         @Param("deptIds") List<Long> deptIds, @Param("dateVO") PeriodIncomeDateVO periodIncomeVO,
                                                         @Param("deptType") Integer deptType, @Param("customerServiceIds") List<Long> customerServiceIds,
                                                         @Param("tagName") String tagName, @Param("tagIncludeType") Integer tagIncludeType,
                                                         @Param("seasonExcessYellowLevel") BigDecimal seasonExcessYellowLevel, @Param("yearExcessYellowLevel") BigDecimal yearExcessYellowLevel,
                                                         @Param("nowPeriod") Integer nowPeriod);

    List<CustomerServicePeriodYearDTO> customerServicePeriodYearList(IPage<CustomerServicePeriodYearDTO> result,
                                                                     @Param("vo") CustomerServicePeriodYearSearchVO vo,
                                                                     @Param("userDept") UserDeptDTO userDept,
                                                                     @Param("customerServiceIds") List<Long> customerServiceIds);

    List<CustomerServicePeriodYearDTO> customerServicePeriodYearList(@Param("vo") CustomerServicePeriodYearSearchVO vo,
                                                                     @Param("userDept") UserDeptDTO userDept,
                                                                     @Param("customerServiceIds") List<Long> customerServiceIds);

    void updateCustomerServiceIncome(@Param("thisMonth") Integer thisMonth,
                                     @Param("thisYearStart") Integer thisYearStart, @Param("thisYearEnd") Integer thisYearEnd,
                                     @Param("thisSeasonStart") Integer thisSeasonStart, @Param("thisSeasonEnd") Integer thisSeasonEnd,
                                     @Param("this12MonthStart") Integer this12MonthStart, @Param("this12MonthEnd") Integer this12MonthEnd,
                                     @Param("customerServiceId") Long customerServiceId);

    void updateCustomerServiceThisMonthIncome(@Param("thisMonth") Integer thisMonth,
                                     @Param("customerServiceId") Long customerServiceId);

    void updateCustomerServiceThisSeasonIncome(@Param("thisSeasonStart") Integer thisSeasonStart, @Param("thisSeasonEnd") Integer thisSeasonEnd,
                                     @Param("customerServiceId") Long customerServiceId);

    void updateCustomerServiceThisYearIncome(
                                     @Param("thisYearStart") Integer thisYearStart, @Param("thisYearEnd") Integer thisYearEnd,
                                     @Param("customerServiceId") Long customerServiceId);

    void updateCustomerServiceThis12MonthIncome(@Param("this12MonthStart") Integer this12MonthStart, @Param("this12MonthEnd") Integer this12MonthEnd,
                                     @Param("customerServiceId") Long customerServiceId);

    void updateCustomerServiceTicketTime(@Param("thisYearEnd") Integer thisYearEnd,
                                     @Param("this12MonthStart") Integer this12MonthStart, @Param("customerServiceId") Long customerServiceId);

    void updateSettlementStatusBySettlementOrderDatas(@Param("settlementOrderId") Long settlementOrderId);

    List<CCustomerService> xqySetCustomerStatusList(@Param("endPeriod") Integer endPeriod);

    List<CCustomerService> xqySetCustomerServiceUserList(@Param("period") Integer period);

    // 服务中且未删除的客户，同集团下客户名重复的数据
    Integer repeatCustomerNameCount();

    // 服务中且未删除的客户，同集团下信用代码重复的数据
    Integer repeatCreditCodeCount();

    // 服务中且未删除的客户，同集团下税号重复的数据
    Integer repeatTaxNumberCount();

    // 月账期关联的客户是已删除的或不存在的
    Integer invalidCustomerMonthCount();

    // 年账期关联的客户是已删除的或不存在的
    Integer invalidCustomerYearCount();

    // 账期上的客户信息数据和服务上的不一致
    Integer notSamePeriodCustomerInfoCount();

    // 客户id有重复年账期（同集团下，同客户id，同账期）
    Integer repeatYearPeriodCustomerIdCount();

    // 客户id有重复月账期（同集团下，同客户id，同账期）
    Integer repeatMonthPeriodCustomerIdCount();

    // 信用代码有重复月账期（同集团下，同信用代码，同账期）
    Integer repeatMonthPeriodCreditCodeCount();

    // 客户名有重复月账期（同集团下，同客户名，同账期）
    Integer repeatMonthPeriodCustomerNameCount();

    // 税号有重复月账期（同集团下，同客户名，同账期）
    Integer repeatMonthPeriodTaxNumberCount();

    // 客户有应生成但没有生成的账期
    Integer noGeneratePeriodCount(@Param("nowPeriod") Integer nowPeriod);

    // 服务上是否有标签重复
    Integer repeatTagCustomerCount();

    // 账期上是否有标签重复
    Integer repeatTagMonthPeriodCount();

    // 账期的税务交付单有没有重复
    Integer repeatTaxDeliverCount();

    // 交付单的账期已经删除或客户服务已经删除
    Integer invalidTaxDeliverPeriodCount();

    // 账期的入账交付单有没有重复
    Integer repeatIncomeDeliverCount();

    // 入账交付单的账期已经删除或客户服务已经删除
    Integer invalidIncomeDeliverPeriodCount();

    // 收入按客户名看是否重复
    Integer repeatIncomeCustomerNameCount();

    // 收入按信用代码看是否重复
    Integer repeatIncomeCreditCodeCount();

    // 入账交付单deliver_result结果和in_result结果不对应
    Integer notSameIncomeDeliverCount();

    // 入账时间不为空且入账结果为空 或者 入账结果有值且不是异常且入账时间为空
    Integer inResultNotNullAndInResultIsNullIncomeDeliverCount();

    // 银行流水时间不为空且银行流水结果为空
    Integer bankTimeNotNullAndBankResultIsNullIncomeDeliverCount();

    // 银行流水时间不为空且银行流水结果是异常或银行部分缺
    Integer bankTimeNotNullAndBankResultIsErrorOrPartIncomeDeliverCount();

    // 银行流水结果有值且不是（异常和银行部分缺）且银行流水时间为空
    Integer bankResultNotNullAndBankResultIsNotErrorOrPartAndBankTimeIsNullIncomeDeliverCount();

    // 收入是否缺账期
    Integer incomePeriodIsNullCount();

    // 缺少年度账期
    Integer noPeriodYear(@Param("year") Integer year, @Param("yearFirstPeriod") Integer yearFirstPeriod);

    Long selectNoBankCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO,
                           @Param("queryDeptIds") List<Long> queryDeptIds,
                           @Param("prePeriod") Integer prePeriod);

    List<RemoteCustomerPeriodBankDTO> getCustomerPeriodBankList(@Param("bankAccountNumbers") List<String> bankAccountNumbers);

    List<RemoteCustomerPeriodBankDTO> getCustomerPeriodBankListByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    List<RemoteCustomerBankAccountDTO> getCustomerBankListByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds);

    void updateLastInAccountId(@Param("startPeriod") Integer startPeriod, @Param("endPeriod") Integer endPeriod);

    List<CCustomerService> selectByPeriodDataScope(@Param("userDept") UserDeptDTO userDept, @Param("keyWord") String keyWord);

    Integer selectInAccountResultNotSameCount();

    Integer endSettleAccountStatusWrongCount();

    Integer notInAccountNotEndAccountWrongCount();

    Integer inAccountNotEndAccountWrongCount();

    Integer selectBankPaymentResultNotSameCount();

    Integer selectSettleAccountResultNotSameCount();

    List<CCustomerService> selectEndCustomerServiceBatchByBusinessTopDeptIdAndCreditCode(@Param("voList") List<NewCustomerBusinessTopDeptCreditCodeVO> voList);

    Integer accountAreaIdNotSameCount();

    Integer wrongAccountingTopDeptCustomerCount();

    Integer noAccountingDeptPeriodCount(@Param("period") Integer period);

    Integer noAccountingTopDeptCustomerCount();

    Integer noAccountingDeptNoWaitItemCustomerCount(@Param("period") Integer period);

    List<CustomerServiceIncomeInfoDTO> customerServiceIncomeInfo(IPage<CustomerServiceIncomeInfoDTO> result,
                                                                 @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                 @Param("searchCustomerServiceIds") List<Long> searchCustomerServiceIds,
                                                                 @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                 @Param("vo") CustomerServiceIncomeInfoSearchVO vo,
                                                                 @Param("periodStart") Integer periodStart,
                                                                 @Param("periodEnd") Integer periodEnd);

    List<CustomerServiceYearIncomeInfoDTO> customerServiceYearIncomeInfo(IPage<CustomerServiceYearIncomeInfoDTO> result,
                                                                         @Param("tagCustomerServiceIds") List<Long> tagCustomerServiceIds,
                                                                         @Param("searchCustomerServiceIds") List<Long> searchCustomerServiceIds,
                                                                         @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                                         @Param("vo") CustomerServiceYearIncomeInfoSearchVO vo);

    List<CommonDeptCountDTO> customerServiceIncomeAdvisorDeptCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceIncomeAccountingDeptCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearIncomeAdvisorDeptCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearIncomeAccountingDeptCount(@Param("userDeptDTO") UserDeptDTO userDeptDTO);

    void removeCustomerServicePeriodMonthTaxTypeCheckBlank();

    void removeCustomerServiceBankAccountBlank();

    void removeCustomerServicePeriodMonthBlank();

    void removeCustomerServiceBlank();

    void removeNewCustomerBankAccountBlank();

    void removeNewCustomerTaxTypeCheckBlank();

    void removeNewCustomerInfoBlank();

    void removeSysUserBlank();

    Integer noAdvisorCount(@Param("period") Integer period);

    Integer errorSettleAccountStatusCount();

    List<CCustomerService> selectAccountingStatisticList(@Param("userDept") UserDeptDTO userDept,
                                                         @Param("waitDispatchIds") List<Long> waitDispatchIds);

    Long selectItemCount(@Param("userDept") UserDeptDTO userDept, @Param("itemType") Integer itemType);

    List<CustomerServiceWaitItemDTO> customerServiceWaitDispatchList(IPage<CustomerServiceWaitItemDTO> iPage,
                                                                     @Param("itemType") Integer itemType,
                                                                     @Param("keyWord") String keyWord,
                                                                     @Param("deptIds") List<Long> deptIds,
                                                                     @Param("tagName") String tagName,
                                                                     @Param("tagIncludeFlag") Integer tagIncludeFlag,
                                                                     @Param("customerServiceIds") List<Long> customerServiceIds,
                                                                     @Param("taxType") Integer taxType);
}
