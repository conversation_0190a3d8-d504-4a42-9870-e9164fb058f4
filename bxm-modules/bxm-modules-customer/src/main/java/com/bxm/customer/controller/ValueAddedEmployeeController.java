package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailExportDTO;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailImportDTO;
import com.bxm.customer.domain.dto.valueAdded.SocialInsuranceDTO;
import com.bxm.customer.domain.dto.valueAdded.EmployeeValidationError;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.query.valueAdded.EmployeeQuery;
import com.bxm.customer.domain.vo.valueAdded.BatchAddEmpRequest;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.IValueAddedEmployeeService;
 import com.bxm.customer.service.strategy.ValueAddedEmployeeStrategyFactory;
import com.bxm.customer.service.strategy.ValueAddedEmployeeUpsertStrategy;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.common.core.constant.CacheConstants;
import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.math.BigDecimal;

/**
 * 增值员工信息Controller
 *
 * 提供增值员工信息的upsert操作，支持三种业务类型：
 * 1. 社医保（bizType=1）：支持提醒、更正、减员操作
 * 2. 个税明细（bizType=2）：支持提醒、更正、减员操作
 * 3. 国税账号（bizType=3）：支持会计实名、异地实名操作
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RestController
@RequestMapping("/valueAddedEmployee")
@Api(tags = "增值员工信息管理")
public class ValueAddedEmployeeController extends BaseController {

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    private ValueAddedEmployeeStrategyFactory strategyFactory;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;

    /**
     * 新增或更新增值员工信息
     */

    @Log(title = "增值员工信息", businessType = BusinessType.INSERT)
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值员工信息", notes = "根据交付单ID+身份证号+业务类型判断是新增还是更新")
    public Result<Long> upsert(@Valid @RequestBody ValueAddedEmployeeVO employeeVO) {
        try {

            // 1. 基础验证
            validateEmployeeRequest(employeeVO);

            // 2. 获取对应的业务策略并执行完整的 upsert 流程
            ValueAddedEmployeeUpsertStrategy strategy = strategyFactory.getStrategy(employeeVO.getBizType());
            Long employeeId = strategy.upsert(employeeVO);

            return Result.ok(employeeId, "操作成功");

        } catch (IllegalArgumentException e) {
            log.warn("Validation error in upsert request: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in upsert request for employee: {}", employeeVO.getEmployeeName(), e);
            return Result.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 验证员工信息的基础业务逻辑
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当验证失败时抛出
     */
    private void validateEmployeeRequest(ValueAddedEmployeeVO employeeVO) {
        if (employeeVO == null) {
            throw new IllegalArgumentException("Employee VO cannot be null");
        }
        // 验证业务类型是否有效
        if (!ValueAddedBizType.isValid(employeeVO.getBizType())) {
            throw new IllegalArgumentException("Invalid business type: " + employeeVO.getBizType());
        }
    }

    /**
     * 根据交付单ID和身份证号查询员工信息
     */


    @GetMapping("/getByDeliveryOrderAndIdNumber")
    @ApiOperation(value = "根据交付单编号和身份证号查询员工信息", notes = "用于检查员工是否已存在")
    public Result<ValueAddedEmployee> getByDeliveryOrderAndIdNumber(
            @RequestParam("deliveryOrderNo") @ApiParam("交付单编号") String deliveryOrderNo,
            @RequestParam("idNumber") @ApiParam("身份证号") String idNumber,
            @RequestParam("bizType") @ApiParam("业务类型：1-社医保，2-个税明细，3-国税账号") Integer bizType) {
        try {
            ValueAddedEmployee employee = valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                    deliveryOrderNo, idNumber, bizType);

            if (employee != null) {
                return Result.ok(employee, "查询成功");
            } else {
                return Result.ok(null, "未找到匹配的员工信息");
            }
        } catch (Exception e) {
            log.error("Error querying employee by deliveryOrderNo: {}, idNumber: {}, bizType: {}", deliveryOrderNo, idNumber, bizType, e);
            return Result.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询员工详情
     */
    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据ID查询员工详情", notes = "获取员工的完整信息")
    public Result<ValueAddedEmployee> getById(@PathVariable("id") @ApiParam("员工ID") Long id) {
        try {
            ValueAddedEmployee employee = valueAddedEmployeeService.getById(id);

            if (employee != null) {
                return Result.ok(employee, "查询成功");
            } else {
                return Result.fail("未找到指定的员工信息");
            }
        } catch (Exception e) {
            log.error("Error querying employee by id: {}", id, e);
            return Result.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出增值业务Excel模板
     */
    @PostMapping("/exportValueAddedEmptyExcelTemplate")
    @ApiOperation(value = "导出增值业务Excel模板", notes = "根据业务类型下载对应的空白模板")
    @Log(title = "增值业务模板导出", businessType = BusinessType.EXPORT)
    public void exportEmptyExcelTemplate(
            @RequestParam("bizType") @ApiParam(value = "业务类型：1-社医保，2-个税明细", required = true, allowableValues = "1,2") Integer bizType,
            HttpServletResponse response) {
        log.info("Starting export value added template, bizType: {}", bizType);

        // 验证业务类型参数
        if (bizType == null || (bizType != 1 && bizType != 2)) {
            throw new IllegalArgumentException("业务类型参数无效，必须为1（社医保）或2（个税明细）");
        }

        try {
            if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
                // 社医保模板
                ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
                util.importTemplateExcel(response, "社保明细");
                log.info("Successfully exported social insurance detail template");
            } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
                // 个税明细模板
                ExcelUtil<PersonalTaxDetailImportDTO> util = new ExcelUtil<>(PersonalTaxDetailImportDTO.class);
                util.importTemplateExcel(response, "个税明细");
                log.info("Successfully exported personal tax detail template");
            }
        } catch (Exception e) {
            log.error("Failed to export value added template, bizType: {}", bizType, e);
            throw new RuntimeException("导出模板失败: " + e.getMessage());
        }
    }

    /**
     * 批量新增增值员工信息（支持社医保和个税明细）
     */
    @PostMapping("/batchSaveValueAddedEmp")
    @ApiOperation(value = "批量新增增值员工信息", notes = "上传Excel文件和表单数据，支持社医保和个税明细，立即返回文件ID，后台异步处理")
    @Log(title = "批量新增增值员工", businessType = BusinessType.INSERT)
    public Result<Long> batchSaveValueAddedEmp(
            @ModelAttribute @Valid BatchAddEmpRequest request,
            @RequestParam("excelFile") @ApiParam("Excel文件") MultipartFile excelFile) {
        try {
            log.info("Received batch add value added employees request, fileName: {}, bizType: {}", excelFile.getOriginalFilename(), request.getBizType());

            // 基础验证
            if (excelFile == null || excelFile.isEmpty()) {
                return Result.fail("Excel文件不能为空");
            }

            // 1. 保存文件到c_value_added_file表
            Long fileId = valueAddedFileService.savePersonnelExcelFile(excelFile, request.getDeliveryOrderNo());

            // 2. 异步处理Excel数据
            valueAddedEmployeeService.processBatchEmployeeData(
                    fileId,
                    request.getBizType(),
                    request.getOverrideExisting()
            );

            return Result.ok(fileId, "文件上传成功，正在后台处理");
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in batch add request: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in batch add request", e);
            return Result.fail("批量处理启动失败：" + e.getMessage());
        }
    }

    /**
     * 查询文件处理进度
     */
    @GetMapping("/fileProgress/{fileId}")
    @ApiOperation(value = "查询文件处理进度", notes = "根据文件ID查询处理进度和结果")
    public Result<Map<String, Object>> getFileProgress(@PathVariable("fileId") @ApiParam("文件ID") Long fileId) {
        try {
            log.info("Query file progress, fileId: {}", fileId);

            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }

            String statusJson = valueAddedEmployeeService.getProcessStatusByFileId(fileId);

            //
            @SuppressWarnings("unchecked")
            Map<String, Object> statusMap = JSON.parseObject(statusJson, Map.class);

            return Result.ok(statusMap, "查询成功");
        } catch (Exception e) {
            log.error("Error querying file progress, fileId: {}", fileId, e);
            return Result.fail("查询进度失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询增值员工信息
     */
    @GetMapping("/query")
    @ApiOperation(value = "分页查询增值员工信息", notes = "根据业务类型等条件分页查询员工信息，支持社保明细和个税明细")
    @Log(title = "Query value added employees", businessType = BusinessType.OTHER)
    public Result<IPage<ValueAddedEmployeeVO>> query(EmployeeQuery query) {
        try {
            // 验证必填参数
            if (query.getBizType() == null) {
                throw new IllegalArgumentException("业务类型不能为空");
            }

            // 验证业务类型范围（只支持社保明细和个税明细）
            if (query.getBizType() != ValueAddedBizType.SOCIAL_INSURANCE.getCode()
                && query.getBizType() != ValueAddedBizType.PERSONAL_TAX.getCode()) {
                throw new IllegalArgumentException("业务类型只支持1（社保明细）或2（个税明细）");
            }

            // 创建分页对象
            IPage<ValueAddedEmployeeVO> page = new Page<>(
                query.getPageNum() != null ? query.getPageNum() : 1,
                query.getPageSize() != null ? query.getPageSize() : 10
            );

            // 调用Service层进行分页查询
            IPage<ValueAddedEmployeeVO> result = valueAddedEmployeeService.queryVOPage(page, query);

            log.info("Query value added employees success, total: {}, current page: {}",
                result.getTotal(), result.getCurrent());
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in query request: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Error querying value added employees", e);
            return Result.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出增值员工明细Excel
     *
     * @param bizType 业务类型，必填，1-社保明细，2-个税明细
     * @param deliveryOrderNo 交付单编号，必填
     * @param response HTTP响应对象
     */
    @PostMapping("/exportDetail")
    @ApiOperation(value = "导出增值员工明细Excel", notes = "根据业务类型导出对应的员工明细数据，支持社保明细和个税明细，两个参数都是必填的")
    @Log(title = "Export value added employee detail", businessType = BusinessType.EXPORT)
    public void exportDetail(
            @RequestParam("bizType") @ApiParam(value = "业务类型：1-社保明细，2-个税明细", required = true, allowableValues = "1,2") Integer bizType,
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
            HttpServletResponse response) {
        log.info("Starting export value added employee detail, bizType: {}, deliveryOrderNo: {}", bizType, deliveryOrderNo);

        try {
            // 验证必填参数
            if (StringUtils.isEmpty(deliveryOrderNo)) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }

            // 验证业务类型参数
            if (bizType == null || (bizType != ValueAddedBizType.SOCIAL_INSURANCE.getCode()
                && bizType != ValueAddedBizType.PERSONAL_TAX.getCode())) {
                throw new IllegalArgumentException("业务类型参数无效，必须为1（社保明细）或2（个税明细）");
            }

            if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
                // 导出社保明细
                List<SocialInsuranceDTO> exportData = valueAddedEmployeeService.getSocialInsuranceDetailForExport(deliveryOrderNo);
                ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
                util.exportExcel(response, exportData, "社保明细");
                log.info("Successfully exported {} social insurance detail records", exportData.size());
            } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
                // 导出个税明细
                List<PersonalTaxDetailExportDTO> exportData = valueAddedEmployeeService.getPersonalTaxDetailForExport(deliveryOrderNo);
                ExcelUtil<PersonalTaxDetailExportDTO> util = new ExcelUtil<>(PersonalTaxDetailExportDTO.class);
                util.exportExcel(response, exportData, "个税明细");
                log.info("Successfully exported {} personal tax detail records", exportData.size());
            }
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in export detail request: {}", e.getMessage());
            throw new RuntimeException("参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Failed to export value added employee detail, bizType: {}, deliveryOrderNo: {}", bizType, deliveryOrderNo, e);
            throw new RuntimeException("导出明细失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除增值员工信息
     */
    @Log(title = "增值员工信息", businessType = BusinessType.DELETE)
    @PostMapping("/batchDel")
    @ApiOperation(value = "批量删除增值员工信息", notes = "根据员工ID列表批量删除员工信息")
    public Result<Boolean> batchDel(@RequestBody @ApiParam("员工ID列表") List<Long> ids) {
        try {
            log.info("Received batch delete request for employee ids: {}", ids);
            // 参数验证
            if (ids == null || ids.isEmpty()) {
                return Result.fail("员工ID列表不能为空");
            }
            // 执行批量删除
            boolean result = valueAddedEmployeeService.batchDelete(ids);

            if (result) {
                return Result.ok(true, "批量删除成功");
            } else {
                return Result.fail("批量删除失败");
            }
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in batch delete request: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in batch delete request for ids: {}", ids, e);
            return Result.fail("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 导出失败数据Excel
     */
    @GetMapping("/exportFailExcel/{fileId}")
    @ApiOperation(value = "导出失败数据Excel", notes = "根据文件ID导出校验失败的数据")
    @Log(title = "导出失败数据", businessType = BusinessType.EXPORT)
    public void exportFailExcel(@PathVariable("fileId") @ApiParam("文件ID") Long fileId,
                               HttpServletResponse response) {
        try {
            log.info("Starting export fail excel, fileId: {}", fileId);

            if (fileId == null) {
                throw new IllegalArgumentException("文件ID不能为空");
            }

            // 从Redis获取失败数据
            String failKey = CacheConstants.EMPLOYEE_BATCH_VALIDATION_FAIL + fileId;
            List<EmployeeValidationError> failList = redisService.getLargeCacheList(
                failKey, batchOperationConfig.getRedisBatchSize());

            if (failList == null || failList.isEmpty()) {
                throw new RuntimeException("未找到失败数据或数据已过期");
            }

            // 转换错误类型为中文显示并优化数据
            failList.forEach(error -> {
                if (error.getErrorType() != null) {
                    error.setErrorType(error.getErrorTypeChinese());
                }
                // 确保空值显示为空字符串而不是null
                if (error.getIdNumber() == null) error.setIdNumber("");
                if (error.getPhoneNumber() == null) error.setPhoneNumber("");
                if (error.getEmployeeName() == null) error.setEmployeeName("");
            });

            // 按行号排序，便于用户查看
            failList.sort((e1, e2) -> {
                if (e1.getRowNumber() == null && e2.getRowNumber() == null) return 0;
                if (e1.getRowNumber() == null) return 1;
                if (e2.getRowNumber() == null) return -1;
                return e1.getRowNumber().compareTo(e2.getRowNumber());
            });

            // 导出Excel
            ExcelUtil<EmployeeValidationError> util = new ExcelUtil<>(EmployeeValidationError.class);
            String fileName = String.format("员工数据校验失败记录_%d", fileId);
            util.exportExcel(response, failList, fileName);
            log.info("Successfully exported {} fail records for fileId: {}", failList.size(), fileId);

        } catch (IllegalArgumentException e) {
            log.warn("Validation error in export fail excel: {}", e.getMessage());
            throw new RuntimeException("参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("Failed to export fail excel, fileId: {}", fileId, e);
            throw new RuntimeException("导出失败数据失败: " + e.getMessage());
        }
    }

    /**
     * 保存成功数据
     */
    @PostMapping("/saveSuccEmp/{fileId}")
    @ApiOperation(value = "保存成功数据", notes = "根据文件ID保存校验成功的员工数据到数据库")
    @Log(title = "保存成功数据", businessType = BusinessType.INSERT)
    public Result<String> saveSuccEmp(@PathVariable("fileId") @ApiParam("文件ID") Long fileId) {
        try {
            log.info("Starting save success employees, fileId: {}", fileId);
            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }
            // 从Redis获取成功数据（已经是 ValueAddedEmployee 实体列表）
            String successKey = CacheConstants.EMPLOYEE_BATCH_VALIDATION_SUCCESS + fileId;
            List<ValueAddedEmployee> successList = redisService.getLargeCacheList(
                successKey, batchOperationConfig.getRedisBatchSize());

            if (successList == null || successList.isEmpty()) {
                return Result.fail("未找到成功数据或数据已过期");
            }

            log.info("Found {} success employees to save for fileId: {}", successList.size(), fileId);

            // 验证数据完整性
            long validEmployees = successList.stream()
                    .filter(emp -> emp.getEmployeeName() != null && !emp.getEmployeeName().trim().isEmpty())
                    .filter(emp -> emp.getIdNumber() != null && !emp.getIdNumber().trim().isEmpty())
                    .count();

            if (validEmployees != successList.size()) {
                log.warn("Found {} invalid employees in success list for fileId: {}",
                        successList.size() - validEmployees, fileId);
            }

            // 批量保存到数据库
            boolean saveResult = valueAddedEmployeeService.saveBatch(successList);

            if (saveResult) {
                // 保存成功后清除Redis缓存
                redisService.deleteObject(successKey);
                log.info("Batch save completed successfully, fileId: {}, saved: {}, valid: {}", fileId, successList.size(), validEmployees);
                return Result.ok("保存完成");
            } else {
                log.error("Batch save failed for fileId: {}, attempted to save {} employees",
                         fileId, successList.size());
                return Result.fail("批量保存失败，请检查数据完整性或联系管理员");
            }

        } catch (Exception e) {
            log.error("Failed to save success employees, fileId: {}", fileId, e);
            return Result.fail("保存成功数据失败: " + e.getMessage());
        }
    }



}
