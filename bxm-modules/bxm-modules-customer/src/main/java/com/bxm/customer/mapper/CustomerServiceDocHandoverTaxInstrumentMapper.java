package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument;

/**
 * 材料交接票据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface CustomerServiceDocHandoverTaxInstrumentMapper extends BaseMapper<CustomerServiceDocHandoverTaxInstrument>
{
    /**
     * 查询材料交接票据
     * 
     * @param id 材料交接票据主键
     * @return 材料交接票据
     */
    public CustomerServiceDocHandoverTaxInstrument selectCustomerServiceDocHandoverTaxInstrumentById(Long id);

    /**
     * 查询材料交接票据列表
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 材料交接票据集合
     */
    public List<CustomerServiceDocHandoverTaxInstrument> selectCustomerServiceDocHandoverTaxInstrumentList(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 新增材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    public int insertCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 修改材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    public int updateCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 删除材料交接票据
     * 
     * @param id 材料交接票据主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverTaxInstrumentById(Long id);

    /**
     * 批量删除材料交接票据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverTaxInstrumentByIds(Long[] ids);
}
