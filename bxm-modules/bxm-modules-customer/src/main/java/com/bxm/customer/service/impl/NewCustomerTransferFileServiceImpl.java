package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.NewCustomerTransferFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.NewCustomerTransferFileMapper;
import com.bxm.customer.domain.NewCustomerTransferFile;
import com.bxm.customer.service.INewCustomerTransferFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 新户流转客户文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Service
public class NewCustomerTransferFileServiceImpl extends ServiceImpl<NewCustomerTransferFileMapper, NewCustomerTransferFile> implements INewCustomerTransferFileService
{
    @Autowired
    private NewCustomerTransferFileMapper newCustomerTransferFileMapper;

    @Autowired
    private FileService fileService;

    /**
     * 查询新户流转客户文件
     * 
     * @param id 新户流转客户文件主键
     * @return 新户流转客户文件
     */
    @Override
    public NewCustomerTransferFile selectNewCustomerTransferFileById(Long id)
    {
        return newCustomerTransferFileMapper.selectNewCustomerTransferFileById(id);
    }

    /**
     * 查询新户流转客户文件列表
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 新户流转客户文件
     */
    @Override
    public List<NewCustomerTransferFile> selectNewCustomerTransferFileList(NewCustomerTransferFile newCustomerTransferFile)
    {
        return newCustomerTransferFileMapper.selectNewCustomerTransferFileList(newCustomerTransferFile);
    }

    /**
     * 新增新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    @Override
    public int insertNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile)
    {
        newCustomerTransferFile.setCreateTime(DateUtils.getNowDate());
        return newCustomerTransferFileMapper.insertNewCustomerTransferFile(newCustomerTransferFile);
    }

    /**
     * 修改新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    @Override
    public int updateNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile)
    {
        newCustomerTransferFile.setUpdateTime(DateUtils.getNowDate());
        return newCustomerTransferFileMapper.updateNewCustomerTransferFile(newCustomerTransferFile);
    }

    /**
     * 批量删除新户流转客户文件
     * 
     * @param ids 需要删除的新户流转客户文件主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerTransferFileByIds(Long[] ids)
    {
        return newCustomerTransferFileMapper.deleteNewCustomerTransferFileByIds(ids);
    }

    /**
     * 删除新户流转客户文件信息
     * 
     * @param id 新户流转客户文件主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerTransferFileById(Long id)
    {
        return newCustomerTransferFileMapper.deleteNewCustomerTransferFileById(id);
    }

    @Override
    @Transactional
    public void removeAndSaveNewFiles(Long newCustomerTransferId, NewCustomerTransferFileType newCustomerTransferFileType, List<CommonFileVO> files) {
        if (Objects.isNull(newCustomerTransferId) || Objects.isNull(newCustomerTransferFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<NewCustomerTransferFile>()
                .eq(NewCustomerTransferFile::getCustomerId, newCustomerTransferId)
                .eq(NewCustomerTransferFile::getFileType, newCustomerTransferFileType.getCode())
                .eq(NewCustomerTransferFile::getIsDel, false)
                .set(NewCustomerTransferFile::getIsDel, true));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(file -> new NewCustomerTransferFile().setCustomerId(newCustomerTransferId)
                    .setFileType(newCustomerTransferFileType.getCode())
                    .setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName())
                    .setIsDel(false)).collect(Collectors.toList()));
        }
    }

    @Override
    public List<CommonFileVO> getFilesByCustomerIdAndFileType(Long customerId, Integer fileType) {
        if (Objects.isNull(customerId)) {
            return Collections.emptyList();
        }
        List<NewCustomerTransferFile> list = list(new LambdaQueryWrapper<NewCustomerTransferFile>()
                .eq(NewCustomerTransferFile::getCustomerId, customerId)
                .eq(NewCustomerTransferFile::getIsDel, false)
                .eq(!Objects.isNull(fileType), NewCustomerTransferFile::getFileType, fileType));
        return list.stream().map(file ->
                        CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName())
                                .fullFileUrl(fileService.getFullFileUrl(file.getFileUrl())).build())
                .collect(Collectors.toList());
    }
}
