package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeVO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.domain.dto.CustomerServicePeriodMonthIncomeDetailDTO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthIncomeMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.ICCustomerServiceService;
import com.bxm.customer.service.ICustomerServicePeriodMonthIncomeService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户服务账期收入Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Service
@Slf4j
public class CustomerServicePeriodMonthIncomeServiceImpl extends ServiceImpl<CustomerServicePeriodMonthIncomeMapper, CustomerServicePeriodMonthIncome> implements ICustomerServicePeriodMonthIncomeService
{
    @Autowired
    private CustomerServicePeriodMonthIncomeMapper customerServicePeriodMonthIncomeMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private FileService fileService;

    @Autowired
    private RedisService redisService;

    @Lazy
    @Autowired
    private ICCustomerServiceService customerServiceService;

    private static final Integer BATCH_SIZE = 500;

    /**
     * 查询客户服务账期收入
     *
     * @param id 客户服务账期收入主键
     * @return 客户服务账期收入
     */
    @Override
    public CustomerServicePeriodMonthIncome selectCustomerServicePeriodMonthIncomeById(Long id)
    {
        return customerServicePeriodMonthIncomeMapper.selectCustomerServicePeriodMonthIncomeById(id);
    }

    /**
     * 查询客户服务账期收入列表
     *
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 客户服务账期收入
     */
    @Override
    public List<CustomerServicePeriodMonthIncome> selectCustomerServicePeriodMonthIncomeList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome)
    {
        return customerServicePeriodMonthIncomeMapper.selectCustomerServicePeriodMonthIncomeList(customerServicePeriodMonthIncome);
    }

    /**
     * 新增客户服务账期收入
     *
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    @Override
    public int insertCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome)
    {
        customerServicePeriodMonthIncome.setCreateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthIncomeMapper.insertCustomerServicePeriodMonthIncome(customerServicePeriodMonthIncome);
    }

    /**
     * 修改客户服务账期收入
     *
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    @Override
    public int updateCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome)
    {
        customerServicePeriodMonthIncome.setUpdateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthIncomeMapper.updateCustomerServicePeriodMonthIncome(customerServicePeriodMonthIncome);
    }

    /**
     * 批量删除客户服务账期收入
     *
     * @param ids 需要删除的客户服务账期收入主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthIncomeByIds(Long[] ids)
    {
        return customerServicePeriodMonthIncomeMapper.deleteCustomerServicePeriodMonthIncomeByIds(ids);
    }

    /**
     * 删除客户服务账期收入信息
     *
     * @param id 客户服务账期收入主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthIncomeById(Long id)
    {
        return customerServicePeriodMonthIncomeMapper.deleteCustomerServicePeriodMonthIncomeById(id);
    }

    @Override
    public IPage<CustomerServicePeriodMonthIncome> incomeList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome, Long deptId, Integer pageNum, Integer pageSize) {
        IPage<CustomerServicePeriodMonthIncome> page = new Page<>(pageNum, pageSize);
        UserDeptDTO dept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!dept.getIsAdmin() && ObjectUtils.isEmpty(dept.getDeptIds())) {
            return page;
        }
        List<Long> ids = Lists.newArrayList();
        if (!StringUtils.isEmpty(customerServicePeriodMonthIncome.getBatchNo())) {
            ids = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + customerServicePeriodMonthIncome.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(ids)) {
                return page;
            }
        }
        if (!Objects.isNull(customerServicePeriodMonthIncome.getDeptType())) {
            dept.setDeptType(customerServicePeriodMonthIncome.getDeptType());
        }
        if (!Objects.isNull(customerServicePeriodMonthIncome.getQueryDeptId())) {
            customerServicePeriodMonthIncome.setQueryDeptIds(remoteDeptService.getAllChildrenIdByTopDeptId(customerServicePeriodMonthIncome.getQueryDeptId()).getDataThrowException());
        }
        List<CustomerServicePeriodMonthIncome> data = baseMapper.incomeList(page, customerServicePeriodMonthIncome, dept, ids);
        if (!ObjectUtils.isEmpty(data)) {
            Set<Long> deptIds = new HashSet<>();
            deptIds.addAll(data.stream().map(CustomerServicePeriodMonthIncome::getBusinessDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(CustomerServicePeriodMonthIncome::getAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(CustomerServicePeriodMonthIncome::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(CustomerServicePeriodMonthIncome::getAccountingTopDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            List<Long> deptIdList = ObjectUtils.isEmpty(deptIds) ? Lists.newArrayList() : new ArrayList<>(deptIds);
            Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIdList) ? new HashMap<>() :
                    remoteDeptService.getByDeptIds(deptIdList).getDataThrowException()
                            .stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIdList) ? new HashMap<>() :
                    remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            data.forEach(d -> {
                try {
                    List<CommonFileVO> files = Lists.newArrayList();
                    List<CommonFileVO> fileList = StringUtils.isEmpty(d.getFiles()) ? Lists.newArrayList() :
                            JSONArray.parseArray(d.getFiles(), CommonFileVO.class);
                    files.addAll(fileList);
                    if (!StringUtils.isEmpty(d.getInputFile())) {
                        files.add(JSONObject.parseObject(d.getInputFile(), CommonFileVO.class));
                    }
                    if (!StringUtils.isEmpty(d.getOutputFile())) {
                        files.add(JSONObject.parseObject(d.getOutputFile(), CommonFileVO.class));
                    }
                    d.setFileCount(fileList.size());
                    d.setFileList(files);
                } catch (Exception e) {
                    d.setFileCount(0);
                    d.setFileList(Collections.emptyList());
                }
                d.setAccountingTopDeptName(Objects.isNull(d.getAccountingTopDeptId()) ? "" : deptNameMap.getOrDefault(d.getAccountingTopDeptId(), ""));
                d.setBusinessDeptName(Objects.isNull(d.getBusinessDeptId()) ? "" : deptNameMap.getOrDefault(d.getBusinessDeptId(), ""));
                String advisorDeptName = Objects.isNull(d.getAdvisorDeptId()) ? "" : deptNameMap.getOrDefault(d.getAdvisorDeptId(), "");
                List<SysEmployee> advisorEmployee = Objects.isNull(d.getAdvisorDeptId()) ? Lists.newArrayList() : employeeMap.get(d.getAdvisorDeptId());
                String accountingDeptName = Objects.isNull(d.getAccountingDeptId()) ? "" : deptNameMap.getOrDefault(d.getAccountingDeptId(), "");
                List<SysEmployee> accountingEmployee = Objects.isNull(d.getAccountingDeptId()) ? Lists.newArrayList() : employeeMap.get(d.getAccountingDeptId());
                d.setAdvisorDeptInfo(Objects.isNull(d.getAdvisorDeptId()) ? "" :
                        advisorDeptName + (ObjectUtils.isEmpty(advisorEmployee) ? "" : ("（" + advisorEmployee.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
                d.setAccountingDeptInfo(Objects.isNull(d.getAccountingDeptId()) ? "" :
                        accountingDeptName + (ObjectUtils.isEmpty(accountingEmployee) ? "" : ("（" + accountingEmployee.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
                d.setTicketTimeStr(Objects.isNull(d.getTicketTime()) ? "" : d.getTicketTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setNoTicketIncomeTimeStr(Objects.isNull(d.getNoTicketIncomeTime()) ? "" : d.getNoTicketIncomeTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setAllTicketTaxAmount(Objects.isNull(d.getAllTicketAmount()) ? null : d.getAllTicketTaxAmount().stripTrailingZeros());
                d.setAllTicketAmount(Objects.isNull(d.getAllTicketAmount()) ? null : d.getAllTicketAmount().stripTrailingZeros());
                d.setNoTicketIncomeAmount(Objects.isNull(d.getNoTicketIncomeAmount()) ? null : d.getNoTicketIncomeAmount().stripTrailingZeros());
                d.setAllTicketTaxAmountStr(Objects.isNull(d.getAllTicketAmount()) ? null : d.getAllTicketTaxAmount().stripTrailingZeros().toPlainString());
                d.setAllTicketAmountStr(Objects.isNull(d.getAllTicketAmount()) ? null : d.getAllTicketAmount().stripTrailingZeros().toPlainString());
                d.setNoTicketIncomeAmountStr(Objects.isNull(d.getNoTicketIncomeAmount()) ? null : d.getNoTicketIncomeAmount().stripTrailingZeros().toPlainString());
                d.setSpecialTicketAmountStr(Objects.isNull(d.getSpecialTicketAmount()) ? null : d.getSpecialTicketAmount().stripTrailingZeros().toPlainString());
                d.setNormalTicketAmountStr(Objects.isNull(d.getNormalTicketAmount()) ? null : d.getNormalTicketAmount().stripTrailingZeros().toPlainString());
                d.setTotalInvoiceAmountStr(Objects.isNull(d.getTotalInvoiceAmount()) ? null : d.getTotalInvoiceAmount().stripTrailingZeros().toPlainString());
                d.setTotalInvoiceTaxAmountStr(Objects.isNull(d.getTotalInvoiceTaxAmount()) ? null : d.getTotalInvoiceTaxAmount().stripTrailingZeros().toPlainString());
                d.setRpaTimeStr(Objects.isNull(d.getRpaTime()) ? "" : d.getRpaTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setRpaResultStr(Objects.isNull(d.getRpaResult()) ? "" : (d.getRpaResult() == 1 ? "成功" : "失败"));
            });
        }
        page.setRecords(data);
        return page;
    }

    @Override
    public List<CustomerServicePeriodMonthIncome> selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId));
    }

    @Override
    public List<CustomerServicePeriodMonthIncome> selectBatchByCustomerServiceId(List<Long> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                .in(CustomerServicePeriodMonthIncome::getCustomerServiceId, serviceIds));
    }

    @Override
    @Transactional
    public void addPeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId) {
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operator = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        addPeriodMonthIncome(income, deptId, userId, operator, "新建收入", false);
    }

    private void addPeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId, Long userId, String operator, String operType, Boolean isInner) {
        if (checkPeriodMonthIncomeExists(income.getCustomerServiceId(), income.getPeriod(), null)) {
            throw new ServiceException("该账期交付单已存在");
        }
        income.setCreateTime(LocalDateTime.now());
        income.setUpdateTime(LocalDateTime.now());
        if (!ObjectUtils.isEmpty(income.getFileList())) {
            income.getFileList().forEach(f -> f.setFullFileUrl(""));
        }
        income.setFiles(ObjectUtils.isEmpty(income.getFileList()) ? "" : JSONArray.toJSONString(income.getFileList()));
        save(income);
        if (!isInner) {
            updateCustomerServiceIncome(income);
        }
        Map<String, Object> operContent = getIncomeOperContent(income);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(income.getId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperName(operator)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(userId)
                    .setOperRemark(income.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(income.getFileList()) ? "" : JSONArray.toJSONString(income.getFileList()))
                    .setCreateBy(Objects.equals("医社保平台同步", operType) ? Constants.YSB_CREATE_BY : ""));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Transactional
    @Override
    public void addPeriodMonthIncomeInner(CustomerServicePeriodMonthIncome income, String operator, Long deptId) {
        Long userId = null;
        if (!StringUtils.isEmpty(operator)) {
            SysUser user = remoteUserService.getUserByNickName(operator, deptId, SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(user)) {
                userId = user.getUserId();
            }
        }
        addPeriodMonthIncome(income, null, userId, operator, "医社保平台同步", true);
    }

    private Map<String, Object> getIncomeOperContent(CustomerServicePeriodMonthIncome income) {
        Map<String, Object> operContent = new HashMap<>();
        if (!Objects.isNull(income.getAllTicketAmount())) {
            operContent.put("全量发票开票金额", income.getAllTicketAmount().stripTrailingZeros().toPlainString());
        }
        if (!Objects.isNull(income.getAllTicketTaxAmount())) {
            operContent.put("全量发票开票税额", income.getAllTicketTaxAmount().stripTrailingZeros().toPlainString());
        }
        if (!Objects.isNull(income.getTotalInvoiceAmount())) {
            operContent.put("全量取得发票金额", income.getTotalInvoiceAmount().stripTrailingZeros().toPlainString());
        }
        if (!Objects.isNull(income.getTotalInvoiceTaxAmount())) {
            operContent.put("全量取得开票税额", income.getTotalInvoiceTaxAmount().stripTrailingZeros().toPlainString());
        }
        if (!Objects.isNull(income.getTicketTime())) {
            operContent.put("开票取数截止时间", income.getTicketTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        }
        if (!Objects.isNull(income.getNoTicketIncomeAmount())) {
            operContent.put("无票收入", income.getNoTicketIncomeAmount().stripTrailingZeros().toPlainString());
        }
        if (!Objects.isNull(income.getNoTicketIncomeTime())) {
            operContent.put("无票收入更新时间", income.getNoTicketIncomeTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        }
        if (!Objects.isNull(income.getRpaTime())) {
            operContent.put("RPA最后取数时间", income.getRpaTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        }
        if (!Objects.isNull(income.getRpaResult())) {
            operContent.put("最后取数结果", income.getRpaResult() == 1 ? "成功" : "失败");
        }
        return operContent;
    }

    @Override
    @Transactional
    public void modifyPeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId) {
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operator = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        updatePeriodMonthIncome(income, deptId, userId, operator, "编辑收入", true, false);
    }

    @Transactional
    @Override
    public void modifyPeriodMonthIncomeInner(CustomerServicePeriodMonthIncome income, String operator, Long deptId) {
        Long userId = null;
        if (!StringUtils.isEmpty(operator)) {
            SysUser user = remoteUserService.getUserByNickName(operator, deptId, SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(user)) {
                userId = user.getUserId();
            }
        }
        updatePeriodMonthIncome(income, null, userId, operator, "医社保平台同步", false, true);
    }

    @Transactional
    @Override
    public void modifyPeriodMonthIncomeXqy(CustomerServicePeriodMonthIncome income, String operator, Long deptId) {
        Long userId = null;
        if (!StringUtils.isEmpty(operator)) {
            SysUser user = remoteUserService.getUserByNickName(operator, deptId, SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(user)) {
                userId = user.getUserId();
            }
        }
        updatePeriodMonthIncome(income, null, userId, operator, "鑫启易同步", false, true);
    }

    private void updatePeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId, Long userId, String operator, String operType, Boolean isDealFile, Boolean isInner) {
        CustomerServicePeriodMonthIncome monthIncome = getById(income.getId());
        if (Objects.isNull(monthIncome)) {
            throw new ServiceException("交付不存在");
        }
        if (checkPeriodMonthIncomeExists(income.getCustomerServiceId(), income.getPeriod(), income.getId())) {
            throw new ServiceException("该账期交付单已存在");
        }
        if (!ObjectUtils.isEmpty(income.getFileList())) {
            income.getFileList().forEach(f -> f.setFullFileUrl(""));
        }
        income.setUpdateTime(LocalDateTime.now());
        if (isDealFile) {
            income.setFiles(ObjectUtils.isEmpty(income.getFileList()) ? "" : JSONArray.toJSONString(income.getFileList()));
        }
        income.setAllTicketAmount(Objects.isNull(income.getAllTicketAmount()) ? BigDecimal.ZERO : income.getAllTicketAmount());
        income.setAllTicketTaxAmount(Objects.isNull(income.getAllTicketTaxAmount()) ? BigDecimal.ZERO : income.getAllTicketTaxAmount());
        if (!isInner) {
            income.setTicketSearchAmount(Objects.isNull(income.getTicketSearchAmount()) ? BigDecimal.ZERO : income.getTicketSearchAmount());
            income.setTicketSearchTaxAmount(Objects.isNull(income.getTicketSearchTaxAmount()) ? BigDecimal.ZERO : income.getTicketSearchTaxAmount());
            income.setNoTicketIncomeAmount(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount());
        }
        income.setTotalInvoiceAmount(Objects.isNull(income.getTotalInvoiceAmount()) ? BigDecimal.ZERO : income.getTotalInvoiceAmount());
        income.setTotalInvoiceTaxAmount(Objects.isNull(income.getTotalInvoiceTaxAmount()) ? BigDecimal.ZERO : income.getTotalInvoiceTaxAmount());
        updateById(income);
        // 医社保平台还要清除rpa取数结果/备注/附件
        if (Objects.equals("医社保平台同步", operType)) {
            update(new LambdaUpdateWrapper<CustomerServicePeriodMonthIncome>()
                    .eq(CustomerServicePeriodMonthIncome::getId, income.getId())
                    .set(CustomerServicePeriodMonthIncome::getRpaResult, null)
                    .set(CustomerServicePeriodMonthIncome::getRemark, "")
                    .set(CustomerServicePeriodMonthIncome::getFiles, ""));
        }

        if (!isInner) {
            updateCustomerServiceIncome(income);
        }
        Map<String, Object> operContent = getIncomeOperContent(income);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(income.getId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperName(operator)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(userId)
                    .setOperRemark(income.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(income.getFileList()) ? "" : JSONArray.toJSONString(income.getFileList()))
                    .setCreateBy(Objects.equals("医社保平台同步", operType) ? Constants.YSB_CREATE_BY : Objects.equals("鑫启易同步", operType) ? Constants.XQY_CREATE_BY : ""));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void updatePeriodMonthIncomeAppendFiles(CustomerServicePeriodMonthIncome income, Long deptId, Long userId, String operator, String operType) {
        CustomerServicePeriodMonthIncome monthIncome = getById(income.getId());
        if (Objects.isNull(monthIncome)) {
            throw new ServiceException("交付不存在");
        }
        if (checkPeriodMonthIncomeExists(income.getCustomerServiceId(), income.getPeriod(), income.getId())) {
            throw new ServiceException("该账期交付单已存在");
        }
        if (!ObjectUtils.isEmpty(income.getFileList())) {
            income.getFileList().forEach(f -> f.setFullFileUrl(""));
        }
        income.setUpdateTime(LocalDateTime.now());
        if (!ObjectUtils.isEmpty(income.getFileList())) {
            if (StringUtils.isEmpty(monthIncome.getFiles())) {
                income.setFiles(JSONArray.toJSONString(income.getFileList()));
            } else {
                List<CommonFileVO> originFiles = JSONArray.parseArray(monthIncome.getFiles(), CommonFileVO.class);
                originFiles.addAll(income.getFileList());
                income.setFiles(JSONArray.toJSONString(originFiles));
            }
        }
        updateById(income);

        Map<String, Object> operContent = getIncomeOperContent(income);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(income.getId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperName(operator)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(userId)
                    .setOperRemark(income.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(income.getFileList()) ? "" : JSONArray.toJSONString(income.getFileList())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public Boolean checkPeriodMonthIncomeExists(Long customerServiceId, Integer period, Long id) {
        return count(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId)
                .eq(CustomerServicePeriodMonthIncome::getPeriod, period)
                .ne(!Objects.isNull(id), CustomerServicePeriodMonthIncome::getId, id)) > 0;
    }

    public static void main(String[] args) {
        Integer nowPeriod = DateUtils.getNowPeriod();
        Map<String, LocalDate> seasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
        Map<String, LocalDate> yearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
        Map<String, LocalDate> month12Map = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
        Integer thisSeasonStart = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("startDate"), "yyyyMM"));
        Integer thisSeasonEnd = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("endDate"), "yyyyMM"));
        Integer thisYearStart = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("startDate"), "yyyyMM"));
        Integer thisYearEnd = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("endDate"), "yyyyMM"));
        Integer this12MonthStart = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("startDate"), "yyyyMM"));
        Integer this12MonthEnd = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("endDate"), "yyyyMM"));
        System.out.println(thisSeasonStart);
        System.out.println(thisSeasonEnd);
        System.out.println(thisYearStart);
        System.out.println(thisYearEnd);
        System.out.println(this12MonthStart);
        System.out.println(this12MonthEnd);
    }

    @Override
    @Async
    public void updateCustomerServiceIncome(CustomerServicePeriodMonthIncome income) {
        CCustomerService customerService = customerServiceMapper.selectById(income.getCustomerServiceId());
        if (!Objects.isNull(customerService)) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            Map<String, LocalDate> seasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
            Map<String, LocalDate> yearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
            Map<String, LocalDate> month12Map = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
            Integer thisSeasonStart = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("startDate"), "yyyyMM"));
            Integer thisSeasonEnd = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("endDate"), "yyyyMM"));
            Integer thisYearStart = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("startDate"), "yyyyMM"));
            Integer thisYearEnd = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("endDate"), "yyyyMM"));
            Integer this12MonthStart = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("startDate"), "yyyyMM"));
            Integer this12MonthEnd = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("endDate"), "yyyyMM"));
            List<CustomerServicePeriodMonthIncome> incomeList = list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>().eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, income.getCustomerServiceId())
//                    .le(CustomerServicePeriodMonthIncome::getPeriod, thisYearEnd).ge(CustomerServicePeriodMonthIncome::getPeriod, this12MonthStart)
                    .select(CustomerServicePeriodMonthIncome::getPeriod, CustomerServicePeriodMonthIncome::getAllTicketAmount, CustomerServicePeriodMonthIncome::getNoTicketIncomeAmount, CustomerServicePeriodMonthIncome::getTicketTime, CustomerServicePeriodMonthIncome::getRpaResult, CustomerServicePeriodMonthIncome::getRpaTime));
            Integer period = income.getPeriod();
            CCustomerService update = new CCustomerService().setId(customerService.getId());
            if (Objects.equals(nowPeriod, period)) {
                update.setThisMonthIncome(income.getAllTicketAmount().add(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount()));
                if (!Objects.isNull(income.getRpaResult()) && income.getRpaResult() == 2) {
                    update.setThisMonthIncomeRpaResult(2);
                } else {
                    update.setThisMonthIncomeRpaResult(1);
                }
            }
            if (period >= thisSeasonStart && period <= thisSeasonEnd) {
                BigDecimal thisSeasonIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisSeasonStart && income1.getPeriod() <= thisSeasonEnd && !Objects.equals(income1.getPeriod(), period))
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThisSeasonIncome(thisSeasonIncome.add(income.getAllTicketAmount().add(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount())));
                if (!Objects.isNull(income.getRpaResult()) && income.getRpaResult() == 2) {
                    update.setThisSeasonIncomeRpaResult(2);
                } else {
                    if (incomeList.stream().filter(income1 -> income1.getPeriod() >= thisSeasonStart && income1.getPeriod() <= thisSeasonEnd && !Objects.equals(income1.getPeriod(), period))
                            .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                        update.setThisSeasonIncomeRpaResult(2);
                    } else {
                        update.setThisSeasonIncomeRpaResult(1);
                    }
                }
            }
            if (period >= thisYearStart && period <= thisYearEnd) {
                BigDecimal thisYearIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisYearStart && income1.getPeriod() <= thisYearEnd && !Objects.equals(income1.getPeriod(), period))
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThisYearIncome(thisYearIncome.add(income.getAllTicketAmount().add(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount())));
                if (!Objects.isNull(income.getRpaResult()) && income.getRpaResult() == 2) {
                    update.setThisYearIncomeRpaResult(2);
                } else {
                    if (incomeList.stream().filter(income1 -> income1.getPeriod() >= thisYearStart && income1.getPeriod() <= thisYearEnd && !Objects.equals(income1.getPeriod(), period))
                            .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                        update.setThisYearIncomeRpaResult(2);
                    } else {
                        update.setThisYearIncomeRpaResult(1);
                    }
                }
            }
            if (period >= this12MonthStart && period <= this12MonthEnd) {
                BigDecimal this12MonthIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= this12MonthStart && income1.getPeriod() <= this12MonthEnd && !Objects.equals(income1.getPeriod(), period))
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThis12MonthIncome(this12MonthIncome.add(income.getAllTicketAmount().add(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount())));
                if (!Objects.isNull(income.getRpaResult()) && income.getRpaResult() == 2) {
                    update.setThis12MonthIncomeRpaResult(2);
                } else {
                    if (incomeList.stream().filter(income1 -> income1.getPeriod() >= this12MonthStart && income1.getPeriod() <= this12MonthEnd && !Objects.equals(income1.getPeriod(), period))
                            .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                        update.setThis12MonthIncomeRpaResult(2);
                    } else {
                        update.setThis12MonthIncomeRpaResult(1);
                    }
                }
            }

            update.setTicketTime(incomeList.stream().filter(row -> !Objects.isNull(row.getTicketTime())).max(Comparator.comparing(CustomerServicePeriodMonthIncome::getTicketTime)).orElse(new CustomerServicePeriodMonthIncome()).getTicketTime());
            customerServiceMapper.updateById(update);
        }
    }

    @Async
    @Override
    public void updateCustomerServiceIncome(Long customerServiceId) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
        if (!Objects.isNull(customerService)) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            Map<String, LocalDate> seasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
            Map<String, LocalDate> yearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
            Map<String, LocalDate> month12Map = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
            Integer thisSeasonStart = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("startDate"), "yyyyMM"));
            Integer thisSeasonEnd = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("endDate"), "yyyyMM"));
            Integer thisYearStart = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("startDate"), "yyyyMM"));
            Integer thisYearEnd = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("endDate"), "yyyyMM"));
            Integer this12MonthStart = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("startDate"), "yyyyMM"));
            Integer this12MonthEnd = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("endDate"), "yyyyMM"));
            List<CustomerServicePeriodMonthIncome> incomeList = list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>().eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId)
                    .le(CustomerServicePeriodMonthIncome::getPeriod, thisYearEnd).ge(CustomerServicePeriodMonthIncome::getPeriod, this12MonthStart)
                    .select(CustomerServicePeriodMonthIncome::getPeriod, CustomerServicePeriodMonthIncome::getAllTicketAmount, CustomerServicePeriodMonthIncome::getNoTicketIncomeAmount, CustomerServicePeriodMonthIncome::getTicketTime, CustomerServicePeriodMonthIncome::getRpaResult, CustomerServicePeriodMonthIncome::getRpaTime));
            CCustomerService update = new CCustomerService().setId(customerService.getId());
            CustomerServicePeriodMonthIncome thisMonthIncome = incomeList.stream().filter(income -> Objects.equals(nowPeriod, income.getPeriod())).findFirst().orElse(null);
            update.setThisMonthIncome(Objects.isNull(thisMonthIncome) ? BigDecimal.ZERO : thisMonthIncome.getAllTicketAmount().add(Objects.isNull(thisMonthIncome.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : thisMonthIncome.getNoTicketIncomeAmount()));
            if (!Objects.isNull(thisMonthIncome) && !Objects.isNull(thisMonthIncome.getRpaResult()) && thisMonthIncome.getRpaResult() == 2) {
                update.setThisMonthIncomeRpaResult(2);
            } else {
                update.setThisMonthIncomeRpaResult(1);
            }
            BigDecimal thisSeasonIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisSeasonStart && income1.getPeriod() <= thisSeasonEnd)
                    .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setThisSeasonIncome(thisSeasonIncome);
            if (incomeList.stream().filter(income1 -> income1.getPeriod() >= thisSeasonStart && income1.getPeriod() <= thisSeasonEnd)
                    .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                update.setThisSeasonIncomeRpaResult(2);
            } else {
                update.setThisSeasonIncomeRpaResult(1);
            }

            BigDecimal thisYearIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisYearStart && income1.getPeriod() <= thisYearEnd)
                    .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setThisYearIncome(thisYearIncome);
            if (incomeList.stream().filter(income1 -> income1.getPeriod() >= thisYearStart && income1.getPeriod() <= thisYearStart)
                    .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                update.setThisYearIncomeRpaResult(2);
            } else {
                update.setThisYearIncomeRpaResult(1);
            }

            BigDecimal this12MonthIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= this12MonthStart && income1.getPeriod() <= this12MonthEnd)
                    .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            update.setThis12MonthIncome(this12MonthIncome);
            if (incomeList.stream().filter(income1 -> income1.getPeriod() >= this12MonthStart && income1.getPeriod() <= this12MonthStart)
                    .anyMatch(income1 -> !Objects.isNull(income1.getRpaResult()) && income1.getRpaResult() == 2)) {
                update.setThis12MonthIncomeRpaResult(2);
            } else {
                update.setThis12MonthIncomeRpaResult(1);
            }

            update.setTicketTime(incomeList.stream().filter(row -> !Objects.isNull(row.getTicketTime())).max(Comparator.comparing(CustomerServicePeriodMonthIncome::getTicketTime)).orElse(new CustomerServicePeriodMonthIncome()).getTicketTime());

            customerServiceMapper.updateById(update);
        }
    }

    @Override
    @Async
    public void batchUpdateCustomerServiceIncome(List<Long> customerServiceIds, Integer period) {
        Boolean isNewYear = period % 100 == 1;
        Boolean isNewSeason = period % 100 == 1 || period % 100 == 4 || period % 100 == 7 || period % 100 == 10;
        Integer last12Month = period - 100;
        customerServiceIds.forEach(customerServiceId -> {
            CCustomerService update = new CCustomerService().setId(customerServiceId);
            update.setThisMonthIncome(BigDecimal.ZERO);
            if (isNewYear) {
                update.setThisYearIncome(BigDecimal.ZERO);
            }
            if (isNewSeason) {
                update.setThisSeasonIncome(BigDecimal.ZERO);
            }
            CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
            CustomerServicePeriodMonthIncome income = getOne(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>().eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId)
                    .eq(CustomerServicePeriodMonthIncome::getPeriod, last12Month)
                    .select(CustomerServicePeriodMonthIncome::getPeriod, CustomerServicePeriodMonthIncome::getAllTicketAmount, CustomerServicePeriodMonthIncome::getNoTicketIncomeAmount, CustomerServicePeriodMonthIncome::getTicketTime));
            update.setThis12MonthIncome(customerService.getThis12MonthIncome().subtract(Objects.isNull(income) ? BigDecimal.ZERO : income.getAllTicketAmount().add(Objects.isNull(income.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income.getNoTicketIncomeAmount())));
            customerServiceMapper.updateById(update);
        });
    }

    @Override
    public List<CommonFileVO> getIncomeFiles(Long id) {
        CustomerServicePeriodMonthIncome monthIncome = getById(id);
        if (Objects.isNull(monthIncome)) {
            return Collections.emptyList();
        }
        List<CommonFileVO> fileList = StringUtils.isEmpty(monthIncome.getFiles()) ? Collections.emptyList() : JSONArray.parseArray(monthIncome.getFiles(), CommonFileVO.class);
        if (!ObjectUtils.isEmpty(fileList)) {
            fileList.forEach(f -> f.setFullFileUrl(fileService.getFullFileUrl(f.getFileUrl())));
        }
        return fileList;
    }

    @Override
    public CustomerServicePeriodMonthIncomeDetailDTO detail(Long id) {
        CustomerServicePeriodMonthIncome income = getById(id);
        if (Objects.isNull(income)) {
            throw new ServiceException("收入不存在");
        }
        CustomerServicePeriodMonthIncomeDetailDTO result = new CustomerServicePeriodMonthIncomeDetailDTO();
        BeanUtils.copyProperties(income, result);
        result.setTitle("收入【" + income.getPeriod() + "】");
        List<CommonFileVO> fileList = StringUtils.isEmpty(income.getFiles()) ? Lists.newArrayList() :
                JSONArray.parseArray(income.getFiles(), CommonFileVO.class);
        fileList.forEach(f -> f.setFullFileUrl(fileService.getFullFileUrl(f.getFileUrl())));
        result.setFileList(fileList);
        return result;
    }

    @Override
    public List<CustomerServicePeriodMonthIncome> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(List<RemoteCustomerServiceIncomeSearchVO> voList) {
        if (ObjectUtils.isEmpty(voList)) {
            return Lists.newArrayList();
        }
        return baseMapper.getCustomerServiceIncomeByCustomerServiceIdAndPeriod(voList);
    }

    @Override
    @Transactional
    public void remoteUpdateOrCreateIncome(RemoteCustomerServiceIncomeVO vo) {
        if (Objects.isNull(vo.getId())) {
            // 新增
            CustomerServicePeriodMonthIncome income = new CustomerServicePeriodMonthIncome()
                    .setCustomerServiceId(vo.getCustomerServiceId())
                    .setPeriod(vo.getPeriod())
                    .setAllTicketAmount(vo.getAllTicketAmount())
                    .setAllTicketTaxAmount(vo.getAllTicketTaxAmount())
                    .setTotalInvoiceAmount(vo.getTotalInvoiceAmount())
                    .setTotalInvoiceTaxAmount(vo.getTotalInvoiceTaxAmount())
                    .setNoTicketIncomeAmount(vo.getNoTicketIncomeAmount())
                    .setTicketTime(vo.getTicketTime())
                    .setNoTicketIncomeTime(vo.getNoTicketIncomeTime())
                    .setRpaTime(vo.getRpaTime())
                    .setRemark(vo.getRemark())
                    .setFileList(vo.getFiles());
            addPeriodMonthIncome(income, vo.getDeptId(), vo.getUserId(), vo.getOperName(), "新建收入", true);
        } else {
            // 编辑
            CustomerServicePeriodMonthIncome income = new CustomerServicePeriodMonthIncome()
                    .setId(vo.getId())
                    .setAllTicketAmount(vo.getAllTicketAmount())
                    .setAllTicketTaxAmount(vo.getAllTicketTaxAmount())
                    .setTotalInvoiceAmount(vo.getTotalInvoiceAmount())
                    .setTotalInvoiceTaxAmount(vo.getTotalInvoiceTaxAmount())
                    .setNoTicketIncomeAmount(vo.getNoTicketIncomeAmount())
                    .setTicketTime(vo.getTicketTime())
                    .setNoTicketIncomeTime(vo.getNoTicketIncomeTime())
                    .setRpaTime(vo.getRpaTime())
                    .setRemark(vo.getRemark())
                    .setFileList(vo.getFiles());
            updatePeriodMonthIncomeAppendFiles(income, vo.getDeptId(), vo.getUserId(), vo.getOperName(), "编辑收入");
        }
    }

    @Override
    public void remoteUpdateCustomerIncome(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        List<CCustomerService> customerServices = customerServiceMapper.selectBatchIds(customerServiceIds);
        if (!ObjectUtils.isEmpty(customerServices)) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            Map<String, LocalDate> seasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
            Map<String, LocalDate> yearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
            Map<String, LocalDate> month12Map = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
            Integer thisSeasonStart = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("startDate"), "yyyyMM"));
            Integer thisSeasonEnd = Integer.parseInt(DateUtils.localDateToStr(seasonMap.get("endDate"), "yyyyMM"));
            Integer thisYearStart = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("startDate"), "yyyyMM"));
            Integer thisYearEnd = Integer.parseInt(DateUtils.localDateToStr(yearMap.get("endDate"), "yyyyMM"));
            Integer this12MonthStart = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("startDate"), "yyyyMM"));
            Integer this12MonthEnd = Integer.parseInt(DateUtils.localDateToStr(month12Map.get("endDate"), "yyyyMM"));

            List<Long> existsCustomerServiceIds = customerServices.stream().map(CCustomerService::getId).collect(Collectors.toList());
            List<CustomerServicePeriodMonthIncome> incomes = list(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>().in(CustomerServicePeriodMonthIncome::getCustomerServiceId, existsCustomerServiceIds)
                    .le(CustomerServicePeriodMonthIncome::getPeriod, thisYearEnd).ge(CustomerServicePeriodMonthIncome::getPeriod, this12MonthStart)
                    .select(CustomerServicePeriodMonthIncome::getCustomerServiceId, CustomerServicePeriodMonthIncome::getPeriod, CustomerServicePeriodMonthIncome::getAllTicketAmount, CustomerServicePeriodMonthIncome::getNoTicketIncomeAmount, CustomerServicePeriodMonthIncome::getTicketTime, CustomerServicePeriodMonthIncome::getRpaResult, CustomerServicePeriodMonthIncome::getRpaTime));
            Map<Long, List<CustomerServicePeriodMonthIncome>> incomeMap = incomes.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonthIncome::getCustomerServiceId));

            List<CCustomerService> updateCustomerServiceList = Lists.newArrayList();
            for (Long existsCustomerServiceId : existsCustomerServiceIds) {
                CCustomerService update = new CCustomerService().setId(existsCustomerServiceId);

                List<CustomerServicePeriodMonthIncome> incomeList = incomeMap.getOrDefault(existsCustomerServiceId, Lists.newArrayList());
                CustomerServicePeriodMonthIncome thisMonthIncome = incomeList.stream().filter(income -> Objects.equals(nowPeriod, income.getPeriod())).findFirst().orElse(null);
                update.setThisMonthIncome(Objects.isNull(thisMonthIncome) ? BigDecimal.ZERO : thisMonthIncome.getAllTicketAmount().add(Objects.isNull(thisMonthIncome.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : thisMonthIncome.getNoTicketIncomeAmount()));

                BigDecimal thisSeasonIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisSeasonStart && income1.getPeriod() <= thisSeasonEnd)
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThisSeasonIncome(thisSeasonIncome);

                BigDecimal thisYearIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= thisYearStart && income1.getPeriod() <= thisYearEnd)
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThisYearIncome(thisYearIncome);

                BigDecimal this12MonthIncome = incomeList.stream().filter(income1 -> income1.getPeriod() >= this12MonthStart && income1.getPeriod() <= this12MonthEnd)
                        .map(income1 -> income1.getAllTicketAmount().add(Objects.isNull(income1.getNoTicketIncomeAmount()) ? BigDecimal.ZERO : income1.getNoTicketIncomeAmount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                update.setThis12MonthIncome(this12MonthIncome);

                updateCustomerServiceList.add(update);
            }

            if (!ObjectUtils.isEmpty(updateCustomerServiceList)) {
                customerServiceService.updateBatchById(updateCustomerServiceList);
            }
        }
    }

    @Override
    public void createIncomeByStartAndEnd(Long customerServiceId, Integer startPeriod, Integer endPeriod) {
        LocalDate startDate = LocalDate.parse(startPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate endDate = LocalDate.parse(endPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        while (!startDate.isAfter(endDate)) {
            Integer period = Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            if (count(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>()
                    .eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId)
                    .eq(CustomerServicePeriodMonthIncome::getPeriod, period)) == 0) {
                save(new CustomerServicePeriodMonthIncome().setCustomerServiceId(customerServiceId)
                        .setPeriod(period)
                        .setAllTicketAmount(BigDecimal.ZERO).setAllTicketTaxAmount(BigDecimal.ZERO).setNoTicketIncomeAmount(BigDecimal.ZERO));
            }
            startDate = startDate.plusMonths(1);
        }
    }

    @Override
    public void addPeriodMonthIncomeInputOutput(CustomerServicePeriodMonthIncome income, Long userId, String createBy, String sourceName) {
        income.setCreateTime(LocalDateTime.now());
        income.setUpdateTime(LocalDateTime.now());
        save(income);
        List<CommonFileVO> files = Lists.newArrayList();
        if (!StringUtils.isEmpty(income.getInputFile())) {
            files.add(JSONObject.parseObject(income.getInputFile(), CommonFileVO.class));
        }
        if (!StringUtils.isEmpty(income.getOutputFile())) {
            files.add(JSONObject.parseObject(income.getOutputFile(), CommonFileVO.class));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(income.getId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                    .setDeptId(null)
                    .setOperType("更新收入明细")
                    .setOperName(createBy)
                    .setOperUserId(userId)
                    .setOperRemark(income.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateBy(sourceName));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public void modifyPeriodMonthIncomeInputOutput(CustomerServicePeriodMonthIncome update, Long userId, String createBy, String sourceName) {
        update.setUpdateTime(LocalDateTime.now());
        updateById(update);
        List<CommonFileVO> files = Lists.newArrayList();
        if (!StringUtils.isEmpty(update.getInputFile())) {
            files.add(JSONObject.parseObject(update.getInputFile(), CommonFileVO.class));
        }
        if (!StringUtils.isEmpty(update.getOutputFile())) {
            files.add(JSONObject.parseObject(update.getOutputFile(), CommonFileVO.class));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(update.getId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                    .setDeptId(null)
                    .setOperType("更新收入明细")
                    .setOperName(createBy)
                    .setOperUserId(userId)
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateBy(sourceName));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public void updateIncomeNoTicketIncome(Long customerServiceId, Integer period, BigDecimal nationalTaxNoTicketIncome, Long deptId, Long userId, String operName, LocalDateTime operTime) {
        CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome = getOne(new LambdaQueryWrapper<CustomerServicePeriodMonthIncome>().eq(CustomerServicePeriodMonthIncome::getCustomerServiceId, customerServiceId)
                .eq(CustomerServicePeriodMonthIncome::getPeriod, period), false);
        if (Objects.isNull(customerServicePeriodMonthIncome)) {
            return;
        }
        if (!Objects.equals(nationalTaxNoTicketIncome, customerServicePeriodMonthIncome.getNoTicketIncomeAmount())) {
            updateById(new CustomerServicePeriodMonthIncome().setId(customerServicePeriodMonthIncome.getId())
                    .setNoTicketIncomeTime(operTime)
                    .setNoTicketIncomeAmount(nationalTaxNoTicketIncome));
            customerServicePeriodMonthIncome.setNoTicketIncomeTime(operTime);
            customerServicePeriodMonthIncome.setNoTicketIncomeAmount(nationalTaxNoTicketIncome);
            updateCustomerServiceIncome(customerServicePeriodMonthIncome);
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("无票收入", nationalTaxNoTicketIncome.stripTrailingZeros().toPlainString());
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServicePeriodMonthIncome.getId())
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD_INCOME.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑")
                        .setOperName(operName)
                        .setOperUserId(userId)
                        .setOperContent(JSONObject.toJSONString(operContent))
                        .setOperRemark("因国税交付单修改")
                        .setCreateTime(operTime));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public List<CommonFileVO> buildFiles(CustomerServicePeriodMonthIncome vo, List<CustomerServicePeriodMonthIncome> list) {
        if (StringUtils.isEmpty(vo.getExportTypes())) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(vo.getExportTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir;
            if (downloadFileType == 1) {
                baseDir = "收入附件";
            } else {
                baseDir = "未知";
            }
            for (CustomerServicePeriodMonthIncome income : list) {
                String dirPath = baseDir + "/" + income.getCustomerName() + "-" + income.getCreditCode() + "-" + income.getPeriod();
                if (downloadFileType == 1) {
                    List<CommonFileVO> fileList = income.getFileList();
                    if (!ObjectUtils.isEmpty(fileList)) {
                        for (CommonFileVO file : fileList) {
                            file.setBaseDir(dirPath);
                        }
                        files.addAll(fileList);
                    }
                } else {
                    // 待定
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            StringUtils.dealFileNames(files);
        }
        return files;
    }
}
