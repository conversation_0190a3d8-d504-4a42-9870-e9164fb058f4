package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.customer.domain.vo.xqy.XqyReportVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerReport" + "${spring.profiles.active}", topic = "openapiReport_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "xqy")
public class XqyReportListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到鑫启易申报消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            XqyReportVO xqyReportVO = JSONObject.parseObject(new String(message.getBody()), XqyReportVO.class);
            if (xqyReportVO.getOperateType() == 2) {
                // 申报
                xqyService.report(xqyReportVO);
            } else if (xqyReportVO.getOperateType() == 1) {
                // 税种更新
//                xqyService.updateTaxCheck(xqyReportVO);
                xqyService.updateTaxCheckV2(xqyReportVO);
            } else if (xqyReportVO.getOperateType() == 3) {
                // 预认证认证
                xqyService.preAuthAuth(xqyReportVO);
            } else if (xqyReportVO.getOperateType() == 4) {
                // 认证提醒
                xqyService.preAuthRemind(xqyReportVO);
            } else if (xqyReportVO.getOperateType() == 5) {
                // 收入更新
//                xqyService.incomeUpdate(xqyReportVO);
                xqyService.incomeUpdateV2(xqyReportVO);
            } else if (xqyReportVO.getOperateType() == 6) {
                // 工商年报
                xqyService.xqyAnnualReport(xqyReportVO);
            }
        } catch (Exception e) {
            log.error("处理鑫启易申报消息异常:{}", e.getMessage());
        }
    }
}
