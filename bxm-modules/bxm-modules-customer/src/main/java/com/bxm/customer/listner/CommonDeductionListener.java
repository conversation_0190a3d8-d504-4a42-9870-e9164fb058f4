package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.customer.domain.vo.CommonDeductionVO;
import com.bxm.customer.domain.vo.xqy.XqySupplementVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerCommonDeduction" + "${spring.profiles.active}", topic = "openapiCommonDeduction_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "common")
public class CommonDeductionListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到公共扣款消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            CommonDeductionVO commonDeductionVO = JSONObject.parseObject(new String(message.getBody()), CommonDeductionVO.class);
            xqyService.commonDeduction(commonDeductionVO);
        } catch (Exception e) {
            log.error("处理公共扣款消息异常:{}", e.getMessage());
        }
    }
}
