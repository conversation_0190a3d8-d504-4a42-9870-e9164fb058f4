package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.OpenApiSyncStatementDetailMapper;
import com.bxm.customer.domain.OpenApiSyncStatementDetail;
import com.bxm.customer.service.IOpenApiSyncStatementDetailService;

/**
 * 医社保个人明细查询记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
@Service
public class OpenApiSyncStatementDetailServiceImpl extends ServiceImpl<OpenApiSyncStatementDetailMapper, OpenApiSyncStatementDetail> implements IOpenApiSyncStatementDetailService
{
    @Autowired
    private OpenApiSyncStatementDetailMapper openApiSyncStatementDetailMapper;

    /**
     * 查询医社保个人明细查询记录
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 医社保个人明细查询记录
     */
    @Override
    public OpenApiSyncStatementDetail selectOpenApiSyncStatementDetailById(Long id)
    {
        return openApiSyncStatementDetailMapper.selectOpenApiSyncStatementDetailById(id);
    }

    /**
     * 查询医社保个人明细查询记录列表
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 医社保个人明细查询记录
     */
    @Override
    public List<OpenApiSyncStatementDetail> selectOpenApiSyncStatementDetailList(OpenApiSyncStatementDetail openApiSyncStatementDetail)
    {
        return openApiSyncStatementDetailMapper.selectOpenApiSyncStatementDetailList(openApiSyncStatementDetail);
    }

    /**
     * 新增医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    @Override
    public int insertOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail)
    {
        openApiSyncStatementDetail.setCreateTime(DateUtils.getNowDate());
        return openApiSyncStatementDetailMapper.insertOpenApiSyncStatementDetail(openApiSyncStatementDetail);
    }

    /**
     * 修改医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    @Override
    public int updateOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail)
    {
        openApiSyncStatementDetail.setUpdateTime(DateUtils.getNowDate());
        return openApiSyncStatementDetailMapper.updateOpenApiSyncStatementDetail(openApiSyncStatementDetail);
    }

    /**
     * 批量删除医社保个人明细查询记录
     * 
     * @param ids 需要删除的医社保个人明细查询记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncStatementDetailByIds(Long[] ids)
    {
        return openApiSyncStatementDetailMapper.deleteOpenApiSyncStatementDetailByIds(ids);
    }

    /**
     * 删除医社保个人明细查询记录信息
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncStatementDetailById(Long id)
    {
        return openApiSyncStatementDetailMapper.deleteOpenApiSyncStatementDetailById(id);
    }

    @Override
    public List<OpenApiSyncStatementDetail> selectBySyncRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiSyncStatementDetail>()
                .eq(OpenApiSyncStatementDetail::getSycRecordId, recordId));
    }
}
