package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.DeliverFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.vo.RemoteSupplementReportFilesVO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerDeliverFile;
import com.bxm.customer.domain.vo.CustomerDeliverBatchFileVO;

import java.util.List;

/**
 * 交付附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface ICustomerDeliverFileService extends IService<CustomerDeliverFile>
{
    /**
     * 查询交付附件
     * 
     * @param id 交付附件主键
     * @return 交付附件
     */
    public CustomerDeliverFile selectCustomerDeliverFileById(Long id);

    /**
     * 查询交付附件列表
     * 
     * @param customerDeliverFile 交付附件
     * @return 交付附件集合
     */
    public List<CustomerDeliverFile> selectCustomerDeliverFileList(CustomerDeliverFile customerDeliverFile);

    /**
     * 新增交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    public int insertCustomerDeliverFile(CustomerDeliverFile customerDeliverFile);

    /**
     * 修改交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    public int updateCustomerDeliverFile(CustomerDeliverFile customerDeliverFile);

    /**
     * 批量删除交付附件
     * 
     * @param ids 需要删除的交付附件主键集合
     * @return 结果
     */
    public int deleteCustomerDeliverFileByIds(Long[] ids);

    /**
     * 删除交付附件信息
     * 
     * @param id 交付附件主键
     * @return 结果
     */
    public int deleteCustomerDeliverFileById(Long id);

    List<CustomerDeliverFile> selectByBatchDeliverId(List<Long> deliverIds);

    List<CustomerDeliverFile> selectByDeliverId(Long deliverId);

    void removeAndSaveNewFiles(Long deliverId, List<CommonFileVO> files, Integer fileType);

    void saveNewFiles(Long deliverId, List<CommonFileVO> files, Integer fileType);

    void removeAndSaveNewFiles(List<RemoteSupplementReportFilesVO> voList, Integer fileType);

    void saveNewFile(RemoteSupplementReportFilesVO vo);

    List<CustomerDeliverFile> selectByDeliverIdAndFileTypes(Long deliverId, List<Integer> fileTypes);

    List<CustomerDeliverFile> selectByDeliverIdsAndFileType(List<Long> deliverIds, Integer fileType);

    Boolean checkFileExist(Long deliverId, Integer fileType);

    void batchRemoveAndSaveNewFiles(List<CustomerDeliverBatchFileVO> voList, DeliverFileType fileType);

    void removeAndSaveStatementDetailFile(Long deliverId, CommonFileVO statementDetailFile, Integer fileType);

    void removeAndSaveNewReportFiles(Long deliverId, List<CommonFileVO> huisuanqingjiaoFiles, List<CommonFileVO> yijiaokuanFiles, List<CommonFileVO> daijiaokuanFiles, List<CommonFileVO> otherDeliverFiles);

    void saveNewReportFile(Long deliverId, List<CommonFileVO> huisuanqingjiaoFiles, List<CommonFileVO> yijiaokuanFiles, List<CommonFileVO> daijiaokuanFiles, List<CommonFileVO> otherDeliverFiles);

    void removeFileExceptOfficalFileName(Long deliverId, Integer fileType, String officalFileName);

    void removeFileIncludeOfficalFileName(Long deliverId, Integer fileType, String officalFileName);

    void removeAndSaveReportTable(Long deliverId, List<CommonFileVO> files);
}
