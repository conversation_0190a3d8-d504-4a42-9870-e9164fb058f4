package com.bxm.customer.domain.vo.businessTask.operate;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:32
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinishBatchVO {
    @ApiModelProperty("ids")
    @NotEmpty
    private List<Long> ids;

    @ApiModelProperty(value = "完成结果")
    @NotNull
    private Integer finishResult;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    @NotNull(message = "材料完整度不能为空")
    private Integer materialIntegrity;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
