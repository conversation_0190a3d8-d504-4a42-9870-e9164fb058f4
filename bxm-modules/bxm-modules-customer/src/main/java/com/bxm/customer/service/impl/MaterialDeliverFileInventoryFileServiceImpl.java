package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.MaterialDeliverFileInventoryFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.MaterialDeliverFileInventoryFileMapper;
import com.bxm.customer.domain.MaterialDeliverFileInventoryFile;
import com.bxm.customer.service.IMaterialDeliverFileInventoryFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 文件清单附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-12
 */
@Service
public class MaterialDeliverFileInventoryFileServiceImpl extends ServiceImpl<MaterialDeliverFileInventoryFileMapper, MaterialDeliverFileInventoryFile> implements IMaterialDeliverFileInventoryFileService
{
    @Autowired
    private MaterialDeliverFileInventoryFileMapper materialDeliverFileInventoryFileMapper;

    /**
     * 查询文件清单附件
     * 
     * @param id 文件清单附件主键
     * @return 文件清单附件
     */
    @Override
    public MaterialDeliverFileInventoryFile selectMaterialDeliverFileInventoryFileById(Long id)
    {
        return materialDeliverFileInventoryFileMapper.selectMaterialDeliverFileInventoryFileById(id);
    }

    /**
     * 查询文件清单附件列表
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 文件清单附件
     */
    @Override
    public List<MaterialDeliverFileInventoryFile> selectMaterialDeliverFileInventoryFileList(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile)
    {
        return materialDeliverFileInventoryFileMapper.selectMaterialDeliverFileInventoryFileList(materialDeliverFileInventoryFile);
    }

    /**
     * 新增文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    @Override
    public int insertMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile)
    {
        materialDeliverFileInventoryFile.setCreateTime(DateUtils.getNowDate());
        return materialDeliverFileInventoryFileMapper.insertMaterialDeliverFileInventoryFile(materialDeliverFileInventoryFile);
    }

    /**
     * 修改文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    @Override
    public int updateMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile)
    {
        materialDeliverFileInventoryFile.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverFileInventoryFileMapper.updateMaterialDeliverFileInventoryFile(materialDeliverFileInventoryFile);
    }

    /**
     * 批量删除文件清单附件
     * 
     * @param ids 需要删除的文件清单附件主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileInventoryFileByIds(Long[] ids)
    {
        return materialDeliverFileInventoryFileMapper.deleteMaterialDeliverFileInventoryFileByIds(ids);
    }

    /**
     * 删除文件清单附件信息
     * 
     * @param id 文件清单附件主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileInventoryFileById(Long id)
    {
        return materialDeliverFileInventoryFileMapper.deleteMaterialDeliverFileInventoryFileById(id);
    }

    @Override
    @Transactional
    public void saveFiles(Long materialDeliverFileInventoryId, List<CommonFileVO> files, MaterialDeliverFileInventoryFileType fileType) {
        if (Objects.isNull(materialDeliverFileInventoryId) || ObjectUtils.isEmpty(files) || Objects.isNull(fileType)) {
            return;
        }
        saveBatch(files.stream().map(file -> new MaterialDeliverFileInventoryFile()
                .setFileInventoryId(materialDeliverFileInventoryId)
                .setFileUrl(file.getFileUrl())
                .setFileSize(file.getFileSize())
                .setFileName(file.getFileName())
                .setFileType(fileType.getCode())
                .setIsDel(false)).collect(Collectors.toList()));
    }

    @Override
    public List<MaterialDeliverFileInventoryFile> selectByBatchFileInventoryIdAndFileType(List<Long> materialDeliverFileInventoryIds, Integer fileType) {
        if (ObjectUtils.isEmpty(materialDeliverFileInventoryIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<MaterialDeliverFileInventoryFile>()
                .eq(MaterialDeliverFileInventoryFile::getIsDel, false)
                .in(MaterialDeliverFileInventoryFile::getFileInventoryId, materialDeliverFileInventoryIds)
                .eq(!Objects.isNull(fileType), MaterialDeliverFileInventoryFile::getFileType, fileType));
    }

    @Override
    public List<CommonFileVO> getMaterialFileInventoryFile(Long materialFileInventoryId, Integer fileType) {
        if (Objects.isNull(materialFileInventoryId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<MaterialDeliverFileInventoryFile>()
                .eq(MaterialDeliverFileInventoryFile::getFileInventoryId, materialFileInventoryId)
                .eq(!Objects.isNull(fileType), MaterialDeliverFileInventoryFile::getFileType, fileType)
                .eq(MaterialDeliverFileInventoryFile::getIsDel, false))
                .stream().map(f -> CommonFileVO.builder().fileUrl(f.getFileUrl()).fileName(f.getFileName()).fileSize(f.getFileSize()).build())
                .collect(Collectors.toList());
    }

    @Override
    public void deleteByMaterialDeliverFileInventoryIdAndFileType(Long materialFileInventoryId, Integer fileType) {
        if (Objects.isNull(materialFileInventoryId)) {
            return;
        }
        update(new LambdaUpdateWrapper<MaterialDeliverFileInventoryFile>()
                .eq(MaterialDeliverFileInventoryFile::getFileInventoryId, materialFileInventoryId)
                .eq(MaterialDeliverFileInventoryFile::getIsDel, false)
                .eq(!Objects.isNull(fileType), MaterialDeliverFileInventoryFile::getFileType, fileType)
                .set(MaterialDeliverFileInventoryFile::getIsDel, true));
    }

    @Override
    public void deleteByMaterialDeliverFileInventoryIdsAndFileType(List<Long> materialDeliverFileInventoryIds, Integer fileType) {
        if (ObjectUtils.isEmpty(materialDeliverFileInventoryIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<MaterialDeliverFileInventoryFile>()
                .in(MaterialDeliverFileInventoryFile::getFileInventoryId, materialDeliverFileInventoryIds)
                .eq(MaterialDeliverFileInventoryFile::getIsDel, false)
                .eq(!Objects.isNull(fileType), MaterialDeliverFileInventoryFile::getFileType, fileType)
                .set(MaterialDeliverFileInventoryFile::getIsDel, true));
    }
}
