package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerInsuranceFundStatus;

/**
 * 新户流转五险一金月状态Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerInsuranceFundStatusMapper extends BaseMapper<NewCustomerInsuranceFundStatus>
{
    /**
     * 查询新户流转五险一金月状态
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 新户流转五险一金月状态
     */
    public NewCustomerInsuranceFundStatus selectNewCustomerInsuranceFundStatusById(Long id);

    /**
     * 查询新户流转五险一金月状态列表
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 新户流转五险一金月状态集合
     */
    public List<NewCustomerInsuranceFundStatus> selectNewCustomerInsuranceFundStatusList(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 新增新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    public int insertNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 修改新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    public int updateNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 删除新户流转五险一金月状态
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundStatusById(Long id);

    /**
     * 批量删除新户流转五险一金月状态
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundStatusByIds(Long[] ids);
}
