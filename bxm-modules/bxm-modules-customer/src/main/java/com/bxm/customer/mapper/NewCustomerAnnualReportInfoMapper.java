package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerAnnualReportInfo;

/**
 * 新户流转工商年报信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerAnnualReportInfoMapper extends BaseMapper<NewCustomerAnnualReportInfo>
{
    /**
     * 查询新户流转工商年报信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 新户流转工商年报信息
     */
    public NewCustomerAnnualReportInfo selectNewCustomerAnnualReportInfoById(Long id);

    /**
     * 查询新户流转工商年报信息列表
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 新户流转工商年报信息集合
     */
    public List<NewCustomerAnnualReportInfo> selectNewCustomerAnnualReportInfoList(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 新增新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    public int insertNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 修改新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    public int updateNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 删除新户流转工商年报信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 结果
     */
    public int deleteNewCustomerAnnualReportInfoById(Long id);

    /**
     * 批量删除新户流转工商年报信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerAnnualReportInfoByIds(Long[] ids);
}
