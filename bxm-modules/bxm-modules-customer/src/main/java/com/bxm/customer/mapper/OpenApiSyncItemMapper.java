package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemSearchDTO;
import com.bxm.customer.domain.vo.workBench.SyncItemSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiSyncItem;
import org.apache.ibatis.annotations.Param;

/**
 * 第三方申报同步客户详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Mapper
public interface OpenApiSyncItemMapper extends BaseMapper<OpenApiSyncItem>
{
    /**
     * 查询第三方申报同步客户详情
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 第三方申报同步客户详情
     */
    public OpenApiSyncItem selectOpenApiSyncItemById(Long id);

    /**
     * 查询第三方申报同步客户详情列表
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 第三方申报同步客户详情集合
     */
    public List<OpenApiSyncItem> selectOpenApiSyncItemList(OpenApiSyncItem openApiSyncItem);

    /**
     * 新增第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    public int insertOpenApiSyncItem(OpenApiSyncItem openApiSyncItem);

    /**
     * 修改第三方申报同步客户详情
     * 
     * @param openApiSyncItem 第三方申报同步客户详情
     * @return 结果
     */
    public int updateOpenApiSyncItem(OpenApiSyncItem openApiSyncItem);

    /**
     * 删除第三方申报同步客户详情
     * 
     * @param id 第三方申报同步客户详情主键
     * @return 结果
     */
    public int deleteOpenApiSyncItemById(Long id);

    /**
     * 批量删除第三方申报同步客户详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncItemByIds(Long[] ids);

    Long waitReportDeliverCount(@Param("reportPeriod") String reportPeriod,
                                @Param("period") Integer period,
                                @Param("userDept") UserDeptDTO userDept,
                                @Param("queryDeptIds") List<Long> queryDeptIds);

    Long waitDeductionDeliverCount(@Param("reportPeriod") String reportPeriod,
                                   @Param("period") Integer period,
                                   @Param("userDept") UserDeptDTO userDept,
                                   @Param("queryDeptIds") List<Long> queryDeptIds);

    List<SyncItemSearchDTO> selectSyncItemPageList(IPage<SyncItemSearchDTO> result,
                                                   @Param("vo") SyncItemSearchVO vo,
                                                   @Param("userDept") UserDeptDTO userDeptDTO,
                                                   @Param("customerServiceIds") List<Long> customerServiceIds,
                                                   @Param("periodIds") List<Long> periodIds,
                                                   @Param("queryDeptIds") List<Long> queryDeptIds);

    List<CommonDeptCountDTO> syncItemAdvisorDeptList(@Param("userDept") UserDeptDTO userDeptDTO,
                                              @Param("type") Integer type,
                                              @Param("queryDeptIds") List<Long> queryDeptIds,
                                                     @Param("reportPeriod") String reportPeriod,
                                                     @Param("period") Integer period,
                                                     @Param("periodStr") String periodStr);

    List<CommonDeptCountDTO> syncItemAccountingDeptList(@Param("userDept") UserDeptDTO userDeptDTO,
                                                     @Param("type") Integer type,
                                                     @Param("queryDeptIds") List<Long> queryDeptIds,
                                                        @Param("reportPeriod") String reportPeriod,
                                                        @Param("period") Integer period,
                                                        @Param("periodStr") String periodStr);
}
