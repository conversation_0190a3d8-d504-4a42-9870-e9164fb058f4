package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierMaterialMedia;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.businessTask.BusinessTaskStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverPeriodInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPeriodInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPushPreviewDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPushPreviewListDTO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierCreateVO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverPeriodInventorySearchVO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 材料交接单账期清单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Service
@Slf4j
public class MaterialDeliverPeriodInventoryServiceImpl extends ServiceImpl<MaterialDeliverPeriodInventoryMapper, MaterialDeliverPeriodInventory> implements IMaterialDeliverPeriodInventoryService
{
    @Autowired
    private MaterialDeliverPeriodInventoryMapper materialDeliverPeriodInventoryMapper;

    @Autowired
    private MaterialDeliverFileInventoryMapper materialDeliverFileInventoryMapper;

    @Autowired
    private MaterialDeliverMapper materialDeliverMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    @Lazy
    private IMaterialDeliverFileInventoryService materialDeliverFileInventoryService;

    @Autowired
    private IMaterialDeliverPeriodInventoryFileService materialDeliverPeriodInventoryFileService;

    @Autowired
    @Lazy
    private IMaterialDeliverService materialDeliverService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MaterialDeliverFileInventoryFileMapper materialDeliverFileInventoryFileMapper;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private IBusinessTaskService businessTaskService;

    @Autowired
    private IWorkOrderService workOrderService;

    /**
     * 查询材料交接单账期清单
     * 
     * @param id 材料交接单账期清单主键
     * @return 材料交接单账期清单
     */
    @Override
    public MaterialDeliverPeriodInventory selectMaterialDeliverPeriodInventoryById(Long id)
    {
        return materialDeliverPeriodInventoryMapper.selectMaterialDeliverPeriodInventoryById(id);
    }

    /**
     * 查询材料交接单账期清单列表
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 材料交接单账期清单
     */
    @Override
    public List<MaterialDeliverPeriodInventory> selectMaterialDeliverPeriodInventoryList(MaterialDeliverPeriodInventory materialDeliverPeriodInventory)
    {
        return materialDeliverPeriodInventoryMapper.selectMaterialDeliverPeriodInventoryList(materialDeliverPeriodInventory);
    }

    /**
     * 新增材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    @Override
    public int insertMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory)
    {
        materialDeliverPeriodInventory.setCreateTime(DateUtils.getNowDate());
        return materialDeliverPeriodInventoryMapper.insertMaterialDeliverPeriodInventory(materialDeliverPeriodInventory);
    }

    /**
     * 修改材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    @Override
    public int updateMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory)
    {
        materialDeliverPeriodInventory.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverPeriodInventoryMapper.updateMaterialDeliverPeriodInventory(materialDeliverPeriodInventory);
    }

    /**
     * 批量删除材料交接单账期清单
     * 
     * @param ids 需要删除的材料交接单账期清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverPeriodInventoryByIds(Long[] ids)
    {
        return materialDeliverPeriodInventoryMapper.deleteMaterialDeliverPeriodInventoryByIds(ids);
    }

    /**
     * 删除材料交接单账期清单信息
     * 
     * @param id 材料交接单账期清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverPeriodInventoryById(Long id)
    {
        return materialDeliverPeriodInventoryMapper.deleteMaterialDeliverPeriodInventoryById(id);
    }

    @Override
    public IPage<MaterialPeriodInventoryDTO> materialPeriodInventoryPageList(Long materialDeliverId, Integer pageNum, Integer pageSize) {
        IPage<MaterialPeriodInventoryDTO> result = new Page<>(pageNum, pageSize);
        IPage<MaterialDeliverPeriodInventory> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<MaterialDeliverPeriodInventory>()
                .eq(MaterialDeliverPeriodInventory::getMaterialDeliverId, materialDeliverId)
                .eq(MaterialDeliverPeriodInventory::getIsDel, false)
                .orderByAsc(MaterialDeliverPeriodInventory::getCustomerName)
                .orderByAsc(MaterialDeliverPeriodInventory::getPeriod));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            Map<Long, List<MaterialDeliverPeriodInventoryFile>> periodFileMap = materialDeliverPeriodInventoryFileService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventoryFile>()
                            .eq(MaterialDeliverPeriodInventoryFile::getIsDel, false)
                            .in(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId, iPage.getRecords().stream().map(MaterialDeliverPeriodInventory::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId));
            result.setRecords(iPage.getRecords().stream().map(row ->
                    MaterialPeriodInventoryDTO.builder()
                            .id(row.getId())
                            .customerName(row.getCustomerName())
                            .bankName(row.getBankName())
                            .bankAccountNumber(row.getBankAccountNumber())
                            .bankInfo((StringUtils.isEmpty(row.getBankName()) ? "" : row.getBankName()) + (StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankAccountNumber() + "）")))
                            .period(Objects.isNull(row.getPeriod()) ? "" : DateUtils.periodToYeaMonth(row.getPeriod()))
                            .fileCount((long) periodFileMap.getOrDefault(row.getId(), new ArrayList<>()).size())
                            .pushResultStr(row.getPushResult())
                            .build()).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<CommonFileVO> getMaterialPeriodInventoryFile(Long materialPeriodInventoryId) {
        MaterialDeliverPeriodInventory materialDeliverPeriodInventory = getById(materialPeriodInventoryId);
        if (Objects.isNull(materialDeliverPeriodInventory) || materialDeliverPeriodInventory.getIsDel()) {
            return Collections.emptyList();
        }
        List<MaterialDeliverPeriodInventoryFile> fileInventories = materialDeliverPeriodInventoryFileService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventoryFile>()
                .eq(MaterialDeliverPeriodInventoryFile::getIsDel, false)
                .eq(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId, materialPeriodInventoryId));
        return ObjectUtils.isEmpty(fileInventories) ? Lists.newArrayList() :
                fileInventories.stream().map(row -> CommonFileVO.builder()
                        .fileName(row.getFileName())
                        .fileUrl(row.getFileUrl())
                        .fileSize(row.getFileSize())
                        .build()).collect(Collectors.toList());
    }

    @Override
    public void deleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds) {
        if (ObjectUtils.isEmpty(materialDeliverIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<MaterialDeliverPeriodInventory>().in(MaterialDeliverPeriodInventory::getMaterialDeliverId, materialDeliverIds));
    }

    @Override
    @Transactional
    public void logicDeleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds) {
        if (ObjectUtils.isEmpty(materialDeliverIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<MaterialDeliverPeriodInventory>().in(MaterialDeliverPeriodInventory::getMaterialDeliverId, materialDeliverIds)
                .set(MaterialDeliverPeriodInventory::getIsDel, true));
    }

    @Override
    public MaterialPushPreviewDTO materialPushPreview(List<MaterialDeliver> waitPushList) {
        if (ObjectUtils.isEmpty(waitPushList)) {
            return new MaterialPushPreviewDTO();
        }
        List<Long> waitPushIds = waitPushList.stream().map(MaterialDeliver::getId).collect(Collectors.toList());
        List<MaterialDeliverFileInventory> fileInventories = materialDeliverFileInventoryMapper.selectList(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
//                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.NORMAL.getCode())
                .in(MaterialDeliverFileInventory::getMaterialDeliverId, waitPushIds)
                .orderByDesc(MaterialDeliverFileInventory::getMaterialDeliverId));
        if (ObjectUtils.isEmpty(fileInventories)) {
            return new MaterialPushPreviewDTO();
        }
        MaterialPushPreviewDTO dto = new MaterialPushPreviewDTO();
        dto.setBatchNo(UUID.randomUUID().toString().replaceAll("-", ""));

        Map<Long, MaterialDeliver> materialDeliverMap = waitPushList
                .stream()
                .collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));

        // 查询所有客户
        List<Long> customerServiceIds = fileInventories.stream().map(MaterialDeliverFileInventory::getCustomerServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CCustomerService> customerServices = ObjectUtils.isEmpty(customerServiceIds) ? Lists.newArrayList() :
                customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, false).in(CCustomerService::getId, customerServiceIds));
        Map<Long, CCustomerService> customerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));

        // 查询所有账期
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = ObjectUtils.isEmpty(customerServices) ? Lists.newArrayList() :
                customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServices.stream().map(CCustomerService::getId).collect(Collectors.toList())));
        Map<Long, Map<Integer, CustomerServicePeriodMonth>> periodMap = customerServicePeriodMonths.stream()
                .collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId,
                        Collectors.toMap(CustomerServicePeriodMonth::getPeriod, Function.identity(), (v1, v2) -> v1)));

        List<MaterialPushPreviewListDTO> previewList = Lists.newArrayList();
        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(fileInventory.getMaterialDeliverId());
            CCustomerService customerService = Objects.isNull(fileInventory.getCustomerServiceId()) ? null : customerServiceMap.get(fileInventory.getCustomerServiceId());
            Long customerServiceId = Objects.isNull(customerService) ? null : customerService.getId();
            if (!StringUtils.isEmpty(fileInventory.getPeriod())) {
                Long customerServicePeriodMonthId = null;
                Map<Integer, CustomerServicePeriodMonth> monthMap = periodMap.get(customerServiceId);
                if (!ObjectUtils.isEmpty(monthMap)) {
                    CustomerServicePeriodMonth customerServicePeriodMonth = monthMap.get(Integer.parseInt(fileInventory.getPeriod()));
                    if (!Objects.isNull(customerServicePeriodMonth)) {
                        customerServicePeriodMonthId = customerServicePeriodMonth.getId();
                    }
                }
                MaterialPushPreviewListDTO previewListDTO = new MaterialPushPreviewListDTO();
                previewListDTO.setCustomerServiceId(customerServiceId);
                previewListDTO.setCustomerServicePeriodMonthId(customerServicePeriodMonthId);
                previewListDTO.setMaterialDeliverNumber(materialDeliver.getMaterialDeliverNumber());
                previewListDTO.setMaterialDeliverType(materialDeliver.getMaterialDeliverType());
                previewListDTO.setCustomerName(fileInventory.getCustomerName());
                previewListDTO.setBankName(previewListDTO.getBankName());
                previewListDTO.setBankAccountNumber(fileInventory.getBankAccountNumber());
                previewListDTO.setBankInfo((StringUtils.isEmpty(fileInventory.getBankName()) ? "" : fileInventory.getBankName()) + (StringUtils.isEmpty(fileInventory.getBankAccountNumber()) ? "" : ("（" + fileInventory.getBankAccountNumber() + "）")));
                previewListDTO.setPeriod(fileInventory.getPeriod());
                previewListDTO.setMaterialDeliverId(fileInventory.getMaterialDeliverId());
                previewListDTO.setMaterialDeliverFileInventoryId(fileInventory.getId());
                previewListDTO.setFiles(Collections.singletonList(CommonFileVO.builder().fileName(fileInventory.getFileName())
                        .fileUrl(fileInventory.getFileUrl()).fileSize(fileInventory.getFileSize()).fileRemark(fileInventory.getRemark()).fileNumber(fileInventory.getFileNumber()).build()));
                String errorMsg = "";
                if (Objects.isNull(customerServiceId)) {
                    errorMsg = "客户不存在";
                } else {
                    if (Objects.isNull(customerServicePeriodMonthId)) {
                        errorMsg = "账期不存在";
                    }
                }
                previewListDTO.setErrorMsg(errorMsg);
                previewList.add(previewListDTO);
            } else {
                LocalDate startDate = fileInventory.getStartDate().withDayOfMonth(1);
                LocalDate endDate = (fileInventory.getEndDate() != null ? fileInventory.getEndDate() : startDate).withDayOfMonth(1);
                while (!startDate.isAfter(endDate)) {
                    Long customerServicePeriodMonthId = null;
                    Map<Integer, CustomerServicePeriodMonth> monthMap = periodMap.get(customerServiceId);
                    if (!ObjectUtils.isEmpty(monthMap)) {
                        CustomerServicePeriodMonth customerServicePeriodMonth = monthMap.get(Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMM"))));
                        if (!Objects.isNull(customerServicePeriodMonth)) {
                            customerServicePeriodMonthId = customerServicePeriodMonth.getId();
                        }
                    }
                    MaterialPushPreviewListDTO previewListDTO = new MaterialPushPreviewListDTO();
                    previewListDTO.setCustomerServiceId(customerServiceId);
                    previewListDTO.setCustomerServicePeriodMonthId(customerServicePeriodMonthId);
                    previewListDTO.setMaterialDeliverNumber(materialDeliver.getMaterialDeliverNumber());
                    previewListDTO.setMaterialDeliverType(materialDeliver.getMaterialDeliverType());
                    previewListDTO.setCustomerName(fileInventory.getCustomerName());
                    previewListDTO.setBankName(fileInventory.getBankName());
                    previewListDTO.setBankAccountNumber(fileInventory.getBankAccountNumber());
                    previewListDTO.setBankInfo((StringUtils.isEmpty(fileInventory.getBankName()) ? "" : fileInventory.getBankName()) + (StringUtils.isEmpty(fileInventory.getBankAccountNumber()) ? "" : ("（" + fileInventory.getBankAccountNumber() + "）")));
                    previewListDTO.setPeriod(startDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
                    previewListDTO.setMaterialDeliverId(fileInventory.getMaterialDeliverId());
                    previewListDTO.setMaterialDeliverFileInventoryId(fileInventory.getId());
                    previewListDTO.setFiles(Collections.singletonList(CommonFileVO.builder().fileName(fileInventory.getFileName())
                            .fileUrl(fileInventory.getFileUrl()).fileSize(fileInventory.getFileSize()).fileRemark(fileInventory.getRemark()).fileNumber(fileInventory.getFileNumber()).build()));
                    String errorMsg = "";
                    if (Objects.isNull(customerServiceId)) {
                        errorMsg = "客户不存在";
                    } else {
                        if (Objects.isNull(customerServicePeriodMonthId)) {
                            errorMsg = "账期不存在";
                        }
                    }
                    previewListDTO.addErrorMsg(errorMsg);
                    previewList.add(previewListDTO);

                    startDate = startDate.plusMonths(1);
                }
            }
        }
        // **整合相同的记录逻辑**
        Map<String, MaterialPushPreviewListDTO> mergedPreviewMap = new LinkedHashMap<>();
        for (MaterialPushPreviewListDTO preview : previewList) {
            String key = Objects.equals(preview.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())
                    ? preview.getMaterialDeliverId() + "_" + preview.getPeriod() + "_" + preview.getBankAccountNumber()
                    : preview.getMaterialDeliverId() + "_" + preview.getCustomerServicePeriodMonthId();
            if (mergedPreviewMap.containsKey(key)) {
                MaterialPushPreviewListDTO existing = mergedPreviewMap.get(key);
                List<CommonFileVO> files = Lists.newArrayList();
                Set<Long> fileInventoryIds = Sets.newHashSet();
                if (!ObjectUtils.isEmpty(existing.getFiles())) {
                    files.addAll(existing.getFiles());
                }
                if (!ObjectUtils.isEmpty(preview.getFiles())) {
                    files.addAll(preview.getFiles());
                }
                if (!ObjectUtils.isEmpty(existing.getFileInventoryIds())) {
                    fileInventoryIds.addAll(existing.getFileInventoryIds());
                }
                fileInventoryIds.add(preview.getMaterialDeliverFileInventoryId());
                existing.setFiles(files);
                existing.setFileCount((long) existing.getFiles().size());
                existing.setFileInventoryIds(fileInventoryIds);
            } else {
                preview.setFileCount(ObjectUtils.isEmpty(preview.getFiles()) ? 0L : preview.getFiles().size());
                preview.setFileInventoryIds(Collections.singleton(preview.getMaterialDeliverFileInventoryId()));
                mergedPreviewMap.put(key, preview);
            }
        }
        previewList = new ArrayList<>(mergedPreviewMap.values());

        List<MaterialPushPreviewListDTO> failList = previewList.stream().filter(row -> !StringUtils.isEmpty(row.getErrorMsg())).collect(Collectors.toList());
        List<MaterialPushPreviewListDTO> successList = Lists.newArrayList();
        List<MaterialPushPreviewListDTO> noErrorList = previewList.stream().filter(row -> StringUtils.isEmpty(row.getErrorMsg())).collect(Collectors.toList());
        // 根据previewListDTO 最后再得到success和fail
        List<MaterialPushPreviewListDTO> bankAccountingCashier = noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())).collect(Collectors.toList());
        List<MaterialPushPreviewListDTO> inAccountCashier = noErrorList.stream().filter(row -> !Objects.equals(row.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())).collect(Collectors.toList());
        List<CustomerServiceCashierAccounting> existsInAccountCashier = ObjectUtils.isEmpty(inAccountCashier) ? Lists.newArrayList() :
                customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getIsDel, false).in(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, inAccountCashier.stream().map(MaterialPushPreviewListDTO::getCustomerServicePeriodMonthId).collect(Collectors.toList()))
                        .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode()));
        List<Long> existsInAccountPeriodIds = existsInAccountCashier.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
        List<CustomerServiceCashierAccounting> existsBankAccountingCashier = ObjectUtils.isEmpty(bankAccountingCashier) ? Lists.newArrayList() :
                customerServiceCashierAccountingMapper.selectBatchByCustomerServicePeriodIdAndBankAccountNumber(bankAccountingCashier);
        List<String> existsBankAccountingCashierPeriodIds = existsBankAccountingCashier.stream().map(row -> row.getCustomerServicePeriodMonthId() + "_" + row.getBankAccountNumber()).distinct().collect(Collectors.toList());
        for (MaterialPushPreviewListDTO previewListDTO : noErrorList) {
            if (Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                if (existsBankAccountingCashierPeriodIds.contains(previewListDTO.getCustomerServicePeriodMonthId() + "_" + previewListDTO.getBankAccountNumber())) {
                    previewListDTO.addErrorMsg("交付单已存在");
                }
                if (isDuplicatePeriodMonthIdAndBankAccountNumber(previewListDTO.getCustomerServicePeriodMonthId(), previewListDTO.getBankAccountNumber(), noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId())).collect(Collectors.toList()))) {
                    previewListDTO.addErrorMsg("交接单冲突");
                }
                if (StringUtils.isEmpty(previewListDTO.getErrorMsg())) {
                    successList.add(previewListDTO);
                } else {
                    failList.add(previewListDTO);
                }
            } else {
                if (existsInAccountPeriodIds.contains(previewListDTO.getCustomerServicePeriodMonthId())) {
                    previewListDTO.addErrorMsg("交付单已存在");
                }
                if (isDuplicatePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId(), noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId())).collect(Collectors.toList()))) {
                    previewListDTO.addErrorMsg("交接单冲突");
                }
                if (StringUtils.isEmpty(previewListDTO.getErrorMsg())) {
                    successList.add(previewListDTO);
                } else {
                    failList.add(previewListDTO);
                }
            }
        }
        if (!ObjectUtils.isEmpty(successList)) {
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY + dto.getBatchNo(), successList, 1000, 60 * 60 * 24, TimeUnit.SECONDS);
        }
        if (!ObjectUtils.isEmpty(failList)) {
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + dto.getBatchNo(), failList, 1000, 60 * 60 * 24, TimeUnit.SECONDS);
        }
        dto.setSuccessList(successList);
        dto.setFailList(failList);
        return dto;
    }

    @Override
    public List<MaterialPushPreviewListDTO> getPushReviewErrorList(String batchNo) {
        List<MaterialPushPreviewListDTO> result = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + batchNo, 1000);
        return ObjectUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    @Transactional
    public void confirmPush(String batchNo, Long deptId) {
        List<MaterialPushPreviewListDTO> successResult = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY + batchNo, 1000);
        List<MaterialPushPreviewListDTO> failResult = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + batchNo, 1000);
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, userId);
        for (MaterialPushPreviewListDTO previewListDTO : successResult) {
            try {
                AccountingCashierCreateVO vo = new AccountingCashierCreateVO();
                vo.setCustomerServiceId(previewListDTO.getCustomerServiceId());
                vo.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
                vo.setPeriod(Integer.parseInt(previewListDTO.getPeriod()));
                vo.setType(Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode()) ? AccountingCashierType.FLOW.getCode() : AccountingCashierType.INCOME.getCode());
                vo.setBankName(previewListDTO.getBankName());
                vo.setBankAccountNumber(previewListDTO.getBankAccountNumber());
                vo.setHasBankPayment(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? 1 : null);
                vo.setHasTicket(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? null : (Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode()) ? 1 : 0));
                vo.setMaterialMedia(AccountingCashierMaterialMedia.ELECTRONIC.getCode());
                vo.setMaterialFiles(previewListDTO.getFiles());
                vo.setUserId(userId);
                vo.setDeptId(deptId);
                customerServiceCashierAccountingService.createAccountingCashierByMaterial(vo, operateUserInfoDTO);
            } catch (Exception e) {
                log.error("材料推送创建交付单失败:{}", e.getMessage());
            }
        }
        List<Long> successFileInventoryIds = ObjectUtils.isEmpty(successResult) ? Lists.newArrayList() :
                successResult.stream()
                .filter(dto -> !ObjectUtils.isEmpty(dto.getFileInventoryIds())) // 过滤掉没有 fileInventoryIds 的元素
                .flatMap(dto -> dto.getFileInventoryIds().stream()) // 展平所有的 fileInventoryIds
                .collect(Collectors.toList());
        List<Long> failFileInventoryIds = ObjectUtils.isEmpty(failResult) ? Lists.newArrayList() :
                failResult.stream()
                .filter(dto -> !ObjectUtils.isEmpty(dto.getFileInventoryIds())) // 过滤掉没有 fileInventoryIds 的元素
                .flatMap(dto -> dto.getFileInventoryIds().stream()) // 展平所有的 fileInventoryIds
                .collect(Collectors.toList());
        Set<Long> allMaterialDeliverIds = Sets.newHashSet();
        List<Long> successMaterialDeliverIds = ObjectUtils.isEmpty(successResult) ? Lists.newArrayList() :
                successResult.stream().map(MaterialPushPreviewListDTO::getMaterialDeliverId).distinct().collect(Collectors.toList());
        List<Long> failMaterialDeliverIds = ObjectUtils.isEmpty(failResult) ? Lists.newArrayList() :
                failResult.stream().map(MaterialPushPreviewListDTO::getMaterialDeliverId).distinct().collect(Collectors.toList());
        allMaterialDeliverIds.addAll(successMaterialDeliverIds);
        allMaterialDeliverIds.addAll(failMaterialDeliverIds);
        // 修改推送状态
        if (!ObjectUtils.isEmpty(successFileInventoryIds)) {
            materialDeliverFileInventoryService.update(new LambdaUpdateWrapper<MaterialDeliverFileInventory>()
                    .in(MaterialDeliverFileInventory::getId, successFileInventoryIds)
                    .set(MaterialDeliverFileInventory::getPushResult, MaterialDeliverPushResult.SUCCESS.getCode()));
        }
        if (!ObjectUtils.isEmpty(failFileInventoryIds)) {
            materialDeliverFileInventoryService.update(new LambdaUpdateWrapper<MaterialDeliverFileInventory>()
                    .in(MaterialDeliverFileInventory::getId, failFileInventoryIds)
                    .set(MaterialDeliverFileInventory::getPushResult, MaterialDeliverPushResult.FAIL.getCode()));
        }
        if (!ObjectUtils.isEmpty(allMaterialDeliverIds)) {
            materialDeliverService.update(new LambdaUpdateWrapper<MaterialDeliver>()
                    .in(MaterialDeliver::getId, allMaterialDeliverIds)
                    .set(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.PUSHED.getCode()));
        }
        // 插入账期清单表
        for (MaterialPushPreviewListDTO previewListDTO : successResult) {
            MaterialDeliverPeriodInventory materialDeliverPeriodInventory = new MaterialDeliverPeriodInventory();
            materialDeliverPeriodInventory.setMaterialDeliverId(previewListDTO.getMaterialDeliverId());
            materialDeliverPeriodInventory.setMaterialDeliverNumber(previewListDTO.getMaterialDeliverNumber());
            materialDeliverPeriodInventory.setCustomerName(previewListDTO.getCustomerName());
            materialDeliverPeriodInventory.setBankName(previewListDTO.getBankName());
            materialDeliverPeriodInventory.setBankAccountNumber(previewListDTO.getBankAccountNumber());
            materialDeliverPeriodInventory.setPeriod(StringUtils.isEmpty(previewListDTO.getPeriod()) ? null : Integer.parseInt(previewListDTO.getPeriod()));
            materialDeliverPeriodInventory.setCustomerServiceId(previewListDTO.getCustomerServiceId());
            materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
            materialDeliverPeriodInventory.setPushResult("推送成功");
            save(materialDeliverPeriodInventory);
            if (!ObjectUtils.isEmpty(previewListDTO.getFiles())) {
                materialDeliverPeriodInventoryFileService.saveByFiles(materialDeliverPeriodInventory.getId(), previewListDTO.getFiles());
            }
        }
        for (MaterialPushPreviewListDTO previewListDTO : failResult) {
            MaterialDeliverPeriodInventory materialDeliverPeriodInventory = new MaterialDeliverPeriodInventory();
            materialDeliverPeriodInventory.setMaterialDeliverId(previewListDTO.getMaterialDeliverId());
            materialDeliverPeriodInventory.setMaterialDeliverNumber(previewListDTO.getMaterialDeliverNumber());
            materialDeliverPeriodInventory.setCustomerName(previewListDTO.getCustomerName());
            materialDeliverPeriodInventory.setBankName(previewListDTO.getBankName());
            materialDeliverPeriodInventory.setBankAccountNumber(previewListDTO.getBankAccountNumber());
            materialDeliverPeriodInventory.setPeriod(StringUtils.isEmpty(previewListDTO.getPeriod()) ? null : Integer.parseInt(previewListDTO.getPeriod()));
            materialDeliverPeriodInventory.setCustomerServiceId(previewListDTO.getCustomerServiceId());
            materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
            materialDeliverPeriodInventory.setPushResult(previewListDTO.getErrorMsg());
            save(materialDeliverPeriodInventory);
            if (!ObjectUtils.isEmpty(previewListDTO.getFiles())) {
                materialDeliverPeriodInventoryFileService.saveByFiles(materialDeliverPeriodInventory.getId(), previewListDTO.getFiles());
            }
        }
        LocalDateTime operTime = LocalDateTime.now();
        if (!ObjectUtils.isEmpty(allMaterialDeliverIds)) {
            materialDeliverService.updateBatchById(allMaterialDeliverIds.stream().map(materialDeliverId ->
                new MaterialDeliver().setId(materialDeliverId)
                        .setLastOperTime(operTime)
                        .setLastOperName(operateUserInfoDTO.getOperName())
                        .setLastOperType("完成推送")
            ).collect(Collectors.toList()));
            allMaterialDeliverIds.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row)
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType("完成推送")
                            .setCreateTime(operTime)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public List<AccountingCashierCreateVO> confirmPushV2(String batchNo, Long deptId) {
        List<MaterialPushPreviewListDTO> successResult = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY + batchNo, 1000);
        List<MaterialPushPreviewListDTO> failResult = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + batchNo, 1000);
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, userId);
        List<AccountingCashierCreateVO> createAccountingCashierList = Lists.newArrayList();
        LocalDateTime operTime = LocalDateTime.now();
        for (MaterialPushPreviewListDTO previewListDTO : successResult) {
            try {
                AccountingCashierCreateVO vo = new AccountingCashierCreateVO();
                vo.setId(previewListDTO.getAccountingCashierId());
                vo.setCustomerServiceId(previewListDTO.getCustomerServiceId());
                vo.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
                vo.setPeriod(Integer.parseInt(previewListDTO.getPeriod()));
                vo.setType(Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode()) ? AccountingCashierType.FLOW.getCode() : AccountingCashierType.INCOME.getCode());
                vo.setBankName(previewListDTO.getBankName());
                vo.setBankAccountNumber(previewListDTO.getBankAccountNumber());
                vo.setHasBankPayment(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? 1 : null);
                vo.setHasTicket(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? null : (Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode()) ? 1 : 0));
                vo.setMaterialMedia(AccountingCashierMaterialMedia.ELECTRONIC.getCode());
                vo.setMaterialFiles(previewListDTO.getFiles().stream().map(row -> {
                    String fileName;
                    if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                        fileName = previewListDTO.getCustomerServiceName() + "-" + previewListDTO.getBankAccountNumber() + "-" + previewListDTO.getStartPeriod() + "-" + previewListDTO.getEndPeriod() + "-" + (Objects.equals(row.getDeliverFileType(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode()) ? "回单" : "对账单") + "-" + row.getFileName();
                    } else {
                        fileName = previewListDTO.getCustomerServiceName() + "-" + previewListDTO.getPeriod() + "-" + row.getFileName();
                    }
                    return CommonFileVO.builder()
                            .fileUrl(row.getFileUrl())
                            .fileName(fileName)
                            .fileSize(row.getFileSize())
                            .deliverFileType(row.getDeliverFileType())
                            .build();
                }).collect(Collectors.toList()));
                vo.setUserId(userId);
                vo.setDeptId(deptId);
                CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingService.createAccountingCashierByMaterial(vo, operateUserInfoDTO);
                if (!Objects.isNull(customerServiceCashierAccounting) && Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType())) {
                    BusinessTask businessTask = businessTaskService.createBankTaskV4(customerServiceCashierAccounting, vo.getMaterialFiles(), deptId, userId, operateUserInfoDTO.getOperName(), operTime, null, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode());
                    vo.setBusinessTaskId(Objects.isNull(businessTask) ? null : businessTask.getId());
                    vo.setId(customerServiceCashierAccounting.getId());
                    vo.setOperName(operateUserInfoDTO.getOperName());
                    createAccountingCashierList.add(vo);
                }
            } catch (Exception e) {
                log.error("材料推送创建交付单失败:{}", e.getMessage());
            }
        }
        List<Long> successFileInventoryIds = ObjectUtils.isEmpty(successResult) ? Lists.newArrayList() :
                successResult.stream()
                        .filter(dto -> !ObjectUtils.isEmpty(dto.getFileInventoryIds())) // 过滤掉没有 fileInventoryIds 的元素
                        .flatMap(dto -> dto.getFileInventoryIds().stream()) // 展平所有的 fileInventoryIds
                        .collect(Collectors.toList());
        List<Long> failFileInventoryIds = ObjectUtils.isEmpty(failResult) ? Lists.newArrayList() :
                failResult.stream()
                        .filter(dto -> !ObjectUtils.isEmpty(dto.getFileInventoryIds())) // 过滤掉没有 fileInventoryIds 的元素
                        .flatMap(dto -> dto.getFileInventoryIds().stream()) // 展平所有的 fileInventoryIds
                        .collect(Collectors.toList());
        Set<Long> allMaterialDeliverIds = Sets.newHashSet();
        List<Long> successMaterialDeliverIds = ObjectUtils.isEmpty(successResult) ? Lists.newArrayList() :
                successResult.stream().map(MaterialPushPreviewListDTO::getMaterialDeliverId).distinct().collect(Collectors.toList());
        List<Long> failMaterialDeliverIds = ObjectUtils.isEmpty(failResult) ? Lists.newArrayList() :
                failResult.stream().map(MaterialPushPreviewListDTO::getMaterialDeliverId).distinct().collect(Collectors.toList());
        allMaterialDeliverIds.addAll(successMaterialDeliverIds);
        allMaterialDeliverIds.addAll(failMaterialDeliverIds);
        // 修改推送状态
        if (!ObjectUtils.isEmpty(successFileInventoryIds)) {
            materialDeliverFileInventoryService.update(new LambdaUpdateWrapper<MaterialDeliverFileInventory>()
                    .in(MaterialDeliverFileInventory::getId, successFileInventoryIds)
                    .set(MaterialDeliverFileInventory::getPushResult, MaterialDeliverPushResult.SUCCESS.getCode()));
        }
        if (!ObjectUtils.isEmpty(failFileInventoryIds)) {
            materialDeliverFileInventoryService.update(new LambdaUpdateWrapper<MaterialDeliverFileInventory>()
                    .in(MaterialDeliverFileInventory::getId, failFileInventoryIds)
                    .set(MaterialDeliverFileInventory::getPushResult, MaterialDeliverPushResult.FAIL.getCode()));
        }
        if (!ObjectUtils.isEmpty(allMaterialDeliverIds)) {
            materialDeliverService.update(new LambdaUpdateWrapper<MaterialDeliver>()
                    .in(MaterialDeliver::getId, allMaterialDeliverIds)
                    .set(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.PUSHED.getCode()));
        }
        // 插入账期清单表
        for (MaterialPushPreviewListDTO previewListDTO : successResult) {
            MaterialDeliverPeriodInventory materialDeliverPeriodInventory = new MaterialDeliverPeriodInventory();
            materialDeliverPeriodInventory.setMaterialDeliverId(previewListDTO.getMaterialDeliverId());
            materialDeliverPeriodInventory.setMaterialDeliverNumber(previewListDTO.getMaterialDeliverNumber());
            materialDeliverPeriodInventory.setCustomerName(previewListDTO.getCustomerName());
            materialDeliverPeriodInventory.setBankName(previewListDTO.getBankName());
            materialDeliverPeriodInventory.setBankAccountNumber(previewListDTO.getBankAccountNumber());
            materialDeliverPeriodInventory.setPeriod(StringUtils.isEmpty(previewListDTO.getPeriod()) ? null : Integer.parseInt(previewListDTO.getPeriod()));
            materialDeliverPeriodInventory.setCustomerServiceId(previewListDTO.getCustomerServiceId());
            materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
            materialDeliverPeriodInventory.setPushResult(previewListDTO.getErrorMsg());
            save(materialDeliverPeriodInventory);
            if (!ObjectUtils.isEmpty(previewListDTO.getFiles())) {
                materialDeliverPeriodInventoryFileService.saveByFiles(materialDeliverPeriodInventory.getId(), previewListDTO.getFiles());
            }
        }
        for (MaterialPushPreviewListDTO previewListDTO : failResult) {
            MaterialDeliverPeriodInventory materialDeliverPeriodInventory = new MaterialDeliverPeriodInventory();
            materialDeliverPeriodInventory.setMaterialDeliverId(previewListDTO.getMaterialDeliverId());
            materialDeliverPeriodInventory.setMaterialDeliverNumber(previewListDTO.getMaterialDeliverNumber());
            materialDeliverPeriodInventory.setCustomerName(previewListDTO.getCustomerName());
            materialDeliverPeriodInventory.setBankName(previewListDTO.getBankName());
            materialDeliverPeriodInventory.setBankAccountNumber(previewListDTO.getBankAccountNumber());
            materialDeliverPeriodInventory.setPeriod(StringUtils.isEmpty(previewListDTO.getPeriod()) ? null : Integer.parseInt(previewListDTO.getPeriod()));
            materialDeliverPeriodInventory.setCustomerServiceId(previewListDTO.getCustomerServiceId());
            materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId());
            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
            materialDeliverPeriodInventory.setPushResult(previewListDTO.getErrorMsg());
            save(materialDeliverPeriodInventory);
            if (!ObjectUtils.isEmpty(previewListDTO.getFiles())) {
                materialDeliverPeriodInventoryFileService.saveByFiles(materialDeliverPeriodInventory.getId(), previewListDTO.getFiles());
            }
            if (!StringUtils.isEmpty(previewListDTO.getCustomerName()) && previewListDTO.getErrorMsg().contains("银行不存在")) {
                // 创建工单
                workOrderService.createMaterialPushWorkOrder(previewListDTO.getCustomerName(), previewListDTO.getBankName(), previewListDTO.getBankAccountNumber(), operateUserInfoDTO, previewListDTO.getFiles(), previewListDTO.getMaterialDeliverNumber());
            }
        }
        if (!ObjectUtils.isEmpty(allMaterialDeliverIds)) {
            materialDeliverService.updateBatchById(allMaterialDeliverIds.stream().map(materialDeliverId ->
                    new MaterialDeliver().setId(materialDeliverId)
                            .setLastOperTime(operTime)
                            .setLastOperName(operateUserInfoDTO.getOperName())
                            .setLastOperType("完成推送")
            ).collect(Collectors.toList()));
            allMaterialDeliverIds.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row)
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType("完成推送")
                            .setCreateTime(operTime)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return createAccountingCashierList;
    }

    @Override
    public IPage<MaterialDeliverPeriodInventoryDTO> periodInventoryPageList(MaterialDeliverPeriodInventorySearchVO vo, Long deptId) {
        IPage<MaterialDeliverPeriodInventoryDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            customerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(customerServiceIds)) {
                return result;
            }
        }
        List<MaterialDeliverPeriodInventoryDTO> data = baseMapper.periodInventoryPageList(result, vo, userDept, customerServiceIds, StringUtils.isEmpty(vo.getPushResultStr()) ? Lists.newArrayList() : Arrays.asList(vo.getPushResultStr().split(",")));
        if (!ObjectUtils.isEmpty(data)) {
            Map<Long, List<MaterialDeliverPeriodInventoryFile>> periodInventoryMap = materialDeliverPeriodInventoryFileService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventoryFile>()
                            .eq(MaterialDeliverPeriodInventoryFile::getIsDel, false)
                            .in(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId, data.stream().map(MaterialDeliverPeriodInventoryDTO::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId));
            data.forEach(d -> {
                List<MaterialDeliverPeriodInventoryFile> files = periodInventoryMap.get(d.getId());
                d.setMaterialDeliverTypeName(MaterialDeliverType.getByCode(d.getMaterialDeliverType()).getName());
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
                d.setPeriodStr(Objects.isNull(d.getPeriod()) ? "" : DateUtils.periodToYeaMonth(d.getPeriod()));
                d.setFileCount(ObjectUtils.isEmpty(files) ? 0L : files.size());
                d.setPushResultStr(MaterialDeliverPushResult.getByCode(d.getPushResult()).getName());
            });
        }
        result.setRecords(data);
        return result;
    }

    @Override
    @Transactional
    public void createAccountingCashier(List<MaterialDeliverPeriodInventory> failList, Long deptId) {
        List<Long> customerServiceIds = failList.stream().map(MaterialDeliverPeriodInventory::getCustomerServiceId).distinct().collect(Collectors.toList());
        List<CustomerServicePeriodMonth> periodMonthList = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds));
        Map<Long, Map<Integer, CustomerServicePeriodMonth>> periodMonthMap = periodMonthList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId, Collectors.toMap(CustomerServicePeriodMonth::getPeriod, Function.identity())));
        List<MaterialDeliver> materialDelivers = materialDeliverMapper.selectList(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false).in(MaterialDeliver::getId, failList.stream().map(MaterialDeliverPeriodInventory::getMaterialDeliverId).distinct().collect(Collectors.toList())));
        Map<Long, MaterialDeliver> materialDeliverMap = materialDelivers.stream().collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));
        List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .in(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceIds).in(CustomerServiceCashierAccounting::getType, Lists.newArrayList(AccountingCashierType.FLOW.getCode(), AccountingCashierType.INCOME.getCode()))
                .eq(CustomerServiceCashierAccounting::getIsDel, false));
        Map<Long, Map<Long, List<CustomerServiceCashierAccounting>>> customerServiceCashierAccountingMap = customerServiceCashierAccountings.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getCustomerServiceId, Collectors.groupingBy(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId)));
        for (MaterialDeliverPeriodInventory materialDeliverPeriodInventory : failList) {
            Map<Integer, CustomerServicePeriodMonth> monthMap = periodMonthMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
            if (ObjectUtils.isEmpty(monthMap)) {
                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                materialDeliverPeriodInventory.setPushResult("客户不存在");
            } else {
                CustomerServicePeriodMonth periodMonth = monthMap.get(materialDeliverPeriodInventory.getPeriod());
                if (Objects.isNull(periodMonth)) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult("账期不存在");
                } else {
                    materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(periodMonth.getId());
                    Map<Long, List<CustomerServiceCashierAccounting>> periodCashierMap = customerServiceCashierAccountingMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
                    List<CustomerServiceCashierAccounting> cashierList = ObjectUtils.isEmpty(periodCashierMap) ? Lists.newArrayList() :
                            periodCashierMap.get(periodMonth.getId());
                    Map<Integer, List<CustomerServiceCashierAccounting>> cashierTypeMap = ObjectUtils.isEmpty(cashierList) ? Maps.newHashMap() :
                            cashierList.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getType));
                    MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverPeriodInventory.getMaterialDeliverId());
                    if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                        List<CustomerServiceCashierAccounting> bankCashierList = cashierTypeMap.get(AccountingCashierType.FLOW.getCode());
                        if (!ObjectUtils.isEmpty(bankCashierList) && bankCashierList.stream().anyMatch(row -> Objects.equals(row.getBankAccountNumber(), materialDeliverPeriodInventory.getBankAccountNumber()))) {
                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                            materialDeliverPeriodInventory.setPushResult("交付单已存在");
                        } else {
                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                            materialDeliverPeriodInventory.setPushResult("推送成功");
                        }
                    } else {
                        List<CustomerServiceCashierAccounting> incomeCashierList = cashierTypeMap.get(AccountingCashierType.INCOME.getCode());
                        if (!ObjectUtils.isEmpty(incomeCashierList)) {
                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                            materialDeliverPeriodInventory.setPushResult("交付单已存在");
                        } else {
                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                            materialDeliverPeriodInventory.setPushResult("推送成功");
                        }
                    }
                }
            }
        }
        for (MaterialDeliverPeriodInventory materialDeliverPeriodInventory : failList) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverPeriodInventory.getMaterialDeliverId());
            if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                if (!StringUtils.isEmpty(materialDeliverPeriodInventory.getBankAccountNumber()) && !Objects.isNull(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId()) &&
                        isDuplicatePeriodMonthIdAndBankAccountNumberByPeriodInventory(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId(), materialDeliverPeriodInventory.getBankAccountNumber(), failList.stream().filter(row -> !Objects.equals(row.getId(), materialDeliverPeriodInventory.getId())).collect(Collectors.toList()))) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult(StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult()) ? "交付单冲突" : (materialDeliverPeriodInventory.getPushResult() + "; " + "交付单冲突"));
                }
            } else {
                if (!Objects.isNull(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId()) &&
                        isDuplicatePeriodMonthIdByPeriodInventory(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId(), failList.stream().filter(row -> !Objects.equals(row.getId(), materialDeliverPeriodInventory.getId())).collect(Collectors.toList()))) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult(StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult()) ? "交付单冲突" : (materialDeliverPeriodInventory.getPushResult() + "; " + "交付单冲突"));
                }
            }
            if (StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult())) {
                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                materialDeliverPeriodInventory.setPushResult("推送成功");
            }
        }
        updateBatchById(failList);

        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        List<MaterialDeliverPeriodInventory> successList = failList.stream().filter(row -> Objects.equals(row.getPushStatus(), MaterialDeliverPushResult.SUCCESS.getCode())).collect(Collectors.toList());
        List<MaterialDeliverPeriodInventory> fails = failList.stream().filter(row -> Objects.equals(row.getPushStatus(), MaterialDeliverPushResult.FAIL.getCode())).collect(Collectors.toList());

        Map<Long, List<MaterialDeliverPeriodInventoryFile>> fileMap = ObjectUtils.isEmpty(successList) ? Maps.newHashMap() :
                materialDeliverPeriodInventoryFileService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventoryFile>()
                        .eq(MaterialDeliverPeriodInventoryFile::getIsDel, false).in(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId, successList.stream().map(MaterialDeliverPeriodInventory::getId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId));
        LocalDateTime operTime = LocalDateTime.now();
        for (MaterialDeliverPeriodInventory periodInventory : successList) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(periodInventory.getMaterialDeliverId());
            try {
                AccountingCashierCreateVO vo = new AccountingCashierCreateVO();
                vo.setCustomerServiceId(periodInventory.getCustomerServiceId());
                vo.setCustomerServicePeriodMonthId(periodInventory.getCustomerServicePeriodMonthId());
                vo.setPeriod(periodInventory.getPeriod());
                vo.setType(Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode()) ? AccountingCashierType.FLOW.getCode() : AccountingCashierType.INCOME.getCode());
                vo.setBankName(periodInventory.getBankName());
                vo.setBankAccountNumber(periodInventory.getBankAccountNumber());
                vo.setHasBankPayment(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? 1 : null);
                vo.setHasTicket(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? null : (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode()) ? 1 : 0));
                vo.setMaterialMedia(AccountingCashierMaterialMedia.ELECTRONIC.getCode());
                vo.setMaterialFiles(fileMap.getOrDefault(periodInventory.getId(), Lists.newArrayList()).stream().map(row -> CommonFileVO.builder().fileSize(row.getFileSize())
                        .fileName(row.getFileName()).fileUrl(row.getFileUrl()).fileNumber(row.getFileNumber()).fileRemark(row.getRemark()).build()).collect(Collectors.toList()));
                vo.setUserId(userId);
                vo.setDeptId(deptId);
                customerServiceCashierAccountingService.createAccountingCashierByMaterial(vo, operateUserInfoDTO);
            } catch (Exception e) {
                log.error("材料推送创建交付单失败:{}", e.getMessage());
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(periodInventory.getId())
                        .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER_PERIOD_INVENTORY.getCode())
                        .setDeptId(operateUserInfoDTO.getDeptId())
                        .setOperType("重新生成交付单")
                        .setOperType("成功")
                        .setCreateTime(operTime)
                        .setOperName(operateUserInfoDTO.getOperName())
                        .setOperUserId(operateUserInfoDTO.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        if (!ObjectUtils.isEmpty(fails)) {
            fails.forEach(periodInventory -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(periodInventory.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER_PERIOD_INVENTORY.getCode())
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType("重新生成交付单")
                            .setOperContent(periodInventory.getPushResult() + "，无法生成交付单")
                            .setCreateTime(operTime)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public List<AccountingCashierCreateVO> createAccountingCashierV2(List<MaterialDeliverPeriodInventory> failList, Long deptId) {
        List<Long> customerServiceIds = failList.stream().map(MaterialDeliverPeriodInventory::getCustomerServiceId).distinct().collect(Collectors.toList());
        Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().in(CCustomerService::getId, customerServiceIds).eq(CCustomerService::getIsDel, false))
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        List<CustomerServicePeriodMonth> periodMonthList = customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds));
        Map<Long, Map<Integer, CustomerServicePeriodMonth>> periodMonthMap = periodMonthList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId, Collectors.toMap(CustomerServicePeriodMonth::getPeriod, Function.identity())));
        List<MaterialDeliver> materialDelivers = materialDeliverMapper.selectList(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false).in(MaterialDeliver::getId, failList.stream().map(MaterialDeliverPeriodInventory::getMaterialDeliverId).distinct().collect(Collectors.toList())));
        Map<Long, MaterialDeliver> materialDeliverMap = materialDelivers.stream().collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));
        List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .in(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceIds).in(CustomerServiceCashierAccounting::getType, Lists.newArrayList(AccountingCashierType.FLOW.getCode(), AccountingCashierType.INCOME.getCode()))
                .eq(CustomerServiceCashierAccounting::getIsDel, false));
        Map<Long, Map<Long, List<CustomerServiceCashierAccounting>>> customerServiceCashierAccountingMap = customerServiceCashierAccountings.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getCustomerServiceId, Collectors.groupingBy(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId)));
        Map<Long, Map<String, CustomerServiceBankAccount>> bankAccountMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .in(CustomerServiceBankAccount::getCustomerServiceId, customerServiceIds)).stream().collect(Collectors.groupingBy(CustomerServiceBankAccount::getCustomerServiceId, Collectors.toMap(CustomerServiceBankAccount::getBankAccountNumber, Function.identity(), (v1, v2) -> v1)));
        Integer nowPeriod = DateUtils.getNowPeriod();
        for (MaterialDeliverPeriodInventory materialDeliverPeriodInventory : failList) {
            if (!Objects.isNull(materialDeliverPeriodInventory.getPeriod()) && Objects.equals(materialDeliverPeriodInventory.getPeriod(), nowPeriod)) {
                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                materialDeliverPeriodInventory.setPushResult("异常（当月不交接）");
            } else {
                if (!customerServiceMap.containsKey(materialDeliverPeriodInventory.getCustomerServiceId())) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult("异常（客户不存在）");
                } else {
                    Map<Integer, CustomerServicePeriodMonth> monthMap = periodMonthMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
                    if (ObjectUtils.isEmpty(monthMap)) {
                        materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                        materialDeliverPeriodInventory.setPushResult("异常（账期不存在）");
                    } else {
                        CustomerServicePeriodMonth periodMonth = monthMap.get(materialDeliverPeriodInventory.getPeriod());
                        if (Objects.isNull(periodMonth)) {
                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                            materialDeliverPeriodInventory.setPushResult("异常（账期不存在）");
                        } else {
                            MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverPeriodInventory.getMaterialDeliverId());
                            if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                                Map<String, CustomerServiceBankAccount> bankMap = bankAccountMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
                                if (ObjectUtils.isEmpty(bankMap)) {
                                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                                    materialDeliverPeriodInventory.setPushResult("异常（账期不存在）");
                                } else {
                                    CustomerServiceBankAccount customerServiceBankAccount = bankMap.get(materialDeliverPeriodInventory.getBankAccountNumber());
                                    if (Objects.isNull(customerServiceBankAccount)) {
                                        materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                                        materialDeliverPeriodInventory.setPushResult("异常（账期不存在）");
                                    } else {
                                        if ((!Objects.isNull(customerServiceBankAccount.getAccountOpenDate()) && periodMonth.getPeriod() < Integer.parseInt(customerServiceBankAccount.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))
                                                || (!Objects.isNull(customerServiceBankAccount.getAccountCloseDate()) && periodMonth.getPeriod() > Integer.parseInt(customerServiceBankAccount.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))) {
                                            materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                                            materialDeliverPeriodInventory.setPushResult("异常（账期不存在）");
                                        } else {
                                            materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(periodMonth.getId());
                                            Map<Long, List<CustomerServiceCashierAccounting>> periodCashierMap = customerServiceCashierAccountingMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
                                            List<CustomerServiceCashierAccounting> cashierList = ObjectUtils.isEmpty(periodCashierMap) ? Lists.newArrayList() :
                                                    periodCashierMap.get(periodMonth.getId());
                                            Map<Integer, List<CustomerServiceCashierAccounting>> cashierTypeMap = ObjectUtils.isEmpty(cashierList) ? Maps.newHashMap() :
                                                    cashierList.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getType));
                                            List<CustomerServiceCashierAccounting> bankCashierList = cashierTypeMap.get(AccountingCashierType.FLOW.getCode());
                                            if (!ObjectUtils.isEmpty(bankCashierList) && bankCashierList.stream().anyMatch(row -> Objects.equals(row.getBankAccountNumber(), materialDeliverPeriodInventory.getBankAccountNumber()))) {
                                                CustomerServiceCashierAccounting customerServiceCashierAccounting = bankCashierList.stream().filter(row -> Objects.equals(row.getBankAccountNumber(), materialDeliverPeriodInventory.getBankAccountNumber())).findFirst().orElse(null);
                                                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                                                materialDeliverPeriodInventory.setAccountingCashierId(customerServiceCashierAccounting.getId());
                                                materialDeliverPeriodInventory.setPushResult("正常（材料补充）");
                                            } else {
                                                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                                                materialDeliverPeriodInventory.setPushResult("正常（新建交付单）");
                                            }
                                        }
                                    }
                                }
                            } else {
                                materialDeliverPeriodInventory.setCustomerServicePeriodMonthId(periodMonth.getId());
                                Map<Long, List<CustomerServiceCashierAccounting>> periodCashierMap = customerServiceCashierAccountingMap.get(materialDeliverPeriodInventory.getCustomerServiceId());
                                List<CustomerServiceCashierAccounting> cashierList = ObjectUtils.isEmpty(periodCashierMap) ? Lists.newArrayList() :
                                        periodCashierMap.get(periodMonth.getId());
                                Map<Integer, List<CustomerServiceCashierAccounting>> cashierTypeMap = ObjectUtils.isEmpty(cashierList) ? Maps.newHashMap() :
                                        cashierList.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getType));
                                List<CustomerServiceCashierAccounting> incomeCashierList = cashierTypeMap.get(AccountingCashierType.INCOME.getCode());
                                if (!ObjectUtils.isEmpty(incomeCashierList)) {
                                    CustomerServiceCashierAccounting customerServiceCashierAccounting = incomeCashierList.get(0);
                                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                                    materialDeliverPeriodInventory.setAccountingCashierId(customerServiceCashierAccounting.getId());
                                    materialDeliverPeriodInventory.setPushResult("正常（材料补充）");
                                } else {
                                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                                    materialDeliverPeriodInventory.setPushResult("正常（新建交付单）");
                                }
                            }
                        }
                    }
                }
            }
        }
        for (MaterialDeliverPeriodInventory materialDeliverPeriodInventory : failList) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverPeriodInventory.getMaterialDeliverId());
            if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                if (!StringUtils.isEmpty(materialDeliverPeriodInventory.getBankAccountNumber()) && !Objects.isNull(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId()) &&
                        isDuplicatePeriodMonthIdAndBankAccountNumberByPeriodInventory(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId(), materialDeliverPeriodInventory.getBankAccountNumber(), failList.stream().filter(row -> !Objects.equals(row.getId(), materialDeliverPeriodInventory.getId())).collect(Collectors.toList()))) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult("异常（" + (StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult()) ? "交接单冲突" : (materialDeliverPeriodInventory.getPushResult() + "; " + "交接单冲突") + "）"));
                }
            } else {
                if (!Objects.isNull(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId()) &&
                        isDuplicatePeriodMonthIdByPeriodInventory(materialDeliverPeriodInventory.getCustomerServicePeriodMonthId(), failList.stream().filter(row -> !Objects.equals(row.getId(), materialDeliverPeriodInventory.getId())).collect(Collectors.toList()))) {
                    materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.FAIL.getCode());
                    materialDeliverPeriodInventory.setPushResult("异常（" + (StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult()) ? "交接单冲突" : (materialDeliverPeriodInventory.getPushResult() + "; " + "交接单冲突") + "）"));
                }
            }
            if (StringUtils.isEmpty(materialDeliverPeriodInventory.getPushResult())) {
                materialDeliverPeriodInventory.setPushStatus(MaterialDeliverPushResult.SUCCESS.getCode());
                materialDeliverPeriodInventory.setPushResult("成功（新建交付单）");
            }
        }
        updateBatchById(failList);

        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, userId);

        List<MaterialDeliverPeriodInventory> successList = failList.stream().filter(row -> Objects.equals(row.getPushStatus(), MaterialDeliverPushResult.SUCCESS.getCode())).collect(Collectors.toList());
        List<MaterialDeliverPeriodInventory> fails = failList.stream().filter(row -> Objects.equals(row.getPushStatus(), MaterialDeliverPushResult.FAIL.getCode())).collect(Collectors.toList());

        Map<Long, List<MaterialDeliverPeriodInventoryFile>> fileMap = ObjectUtils.isEmpty(successList) ? Maps.newHashMap() :
                materialDeliverPeriodInventoryFileService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventoryFile>()
                                .eq(MaterialDeliverPeriodInventoryFile::getIsDel, false).in(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId, successList.stream().map(MaterialDeliverPeriodInventory::getId).collect(Collectors.toList())))
                        .stream().collect(Collectors.groupingBy(MaterialDeliverPeriodInventoryFile::getPeriodInventoryId));
        LocalDateTime operTime = LocalDateTime.now();
        List<AccountingCashierCreateVO> createVOList = Lists.newArrayList();
        for (MaterialDeliverPeriodInventory periodInventory : successList) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(periodInventory.getMaterialDeliverId());
            try {
                AccountingCashierCreateVO vo = new AccountingCashierCreateVO();
                vo.setId(periodInventory.getAccountingCashierId());
                vo.setCustomerServiceId(periodInventory.getCustomerServiceId());
                vo.setCustomerServicePeriodMonthId(periodInventory.getCustomerServicePeriodMonthId());
                vo.setPeriod(periodInventory.getPeriod());
                vo.setType(Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode()) ? AccountingCashierType.FLOW.getCode() : AccountingCashierType.INCOME.getCode());
                vo.setBankName(periodInventory.getBankName());
                vo.setBankAccountNumber(periodInventory.getBankAccountNumber());
                vo.setHasBankPayment(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? 1 : null);
                vo.setHasTicket(Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType()) ? null : (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode()) ? 1 : 0));
                vo.setMaterialMedia(AccountingCashierMaterialMedia.ELECTRONIC.getCode());
                vo.setMaterialFiles(fileMap.getOrDefault(periodInventory.getId(), Lists.newArrayList()).stream().map(row -> CommonFileVO.builder().fileSize(row.getFileSize())
                        .fileName(row.getFileName()).fileUrl(row.getFileUrl()).fileNumber(row.getFileNumber()).fileRemark(row.getRemark()).deliverFileType(row.getFileType()).build()).collect(Collectors.toList()));
                vo.setUserId(userId);
                vo.setDeptId(deptId);
                CustomerServiceCashierAccounting customerServiceCashierAccounting = customerServiceCashierAccountingService.createAccountingCashierByMaterial(vo, operateUserInfoDTO);
                if (!Objects.isNull(customerServiceCashierAccounting) && Objects.equals(AccountingCashierType.FLOW.getCode(), vo.getType())) {
                    BusinessTask businessTask = businessTaskService.createBankTaskV4(customerServiceCashierAccounting, vo.getMaterialFiles(), deptId, userId, operateUserInfoDTO.getOperName(), operTime, null, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode());
                    vo.setBusinessTaskId(Objects.isNull(businessTask) ? null : businessTask.getId());
                    vo.setOperName(operateUserInfoDTO.getOperName());
                    vo.setId(customerServiceCashierAccounting.getId());
                    createVOList.add(vo);
                }
            } catch (Exception e) {
                log.error("材料推送创建交付单失败:{}", e.getMessage());
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(periodInventory.getId())
                        .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER_PERIOD_INVENTORY.getCode())
                        .setDeptId(operateUserInfoDTO.getDeptId())
                        .setOperType("重新生成交付单")
                        .setOperType("成功")
                        .setCreateTime(operTime)
                        .setOperName(operateUserInfoDTO.getOperName())
                        .setOperUserId(operateUserInfoDTO.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        if (!ObjectUtils.isEmpty(fails)) {
            fails.forEach(periodInventory -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(periodInventory.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER_PERIOD_INVENTORY.getCode())
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType("重新生成交付单")
                            .setOperContent(periodInventory.getPushResult() + "，无法生成交付单")
                            .setCreateTime(operTime)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return createVOList;
    }

    @Override
    public MaterialPushPreviewDTO materialPushPreviewV2(List<MaterialDeliver> waitPushList) {
        if (ObjectUtils.isEmpty(waitPushList)) {
            return new MaterialPushPreviewDTO();
        }
        List<Long> waitPushIds = waitPushList.stream().map(MaterialDeliver::getId).collect(Collectors.toList());
        List<MaterialDeliverFileInventory> fileInventories = materialDeliverFileInventoryMapper.selectList(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .in(MaterialDeliverFileInventory::getMaterialDeliverId, waitPushIds)
                .orderByDesc(MaterialDeliverFileInventory::getMaterialDeliverId));
        if (ObjectUtils.isEmpty(fileInventories)) {
            return new MaterialPushPreviewDTO();
        }
        Integer nowPeriod = DateUtils.getNowPeriod();
        MaterialPushPreviewDTO dto = new MaterialPushPreviewDTO();
        dto.setBatchNo(UUID.randomUUID().toString().replaceAll("-", ""));

        Map<Long, MaterialDeliver> materialDeliverMap = waitPushList
                .stream()
                .collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));

        // 查询所有客户
        List<Long> customerServiceIds = fileInventories.stream().map(MaterialDeliverFileInventory::getCustomerServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CCustomerService> customerServices = ObjectUtils.isEmpty(customerServiceIds) ? Lists.newArrayList() :
                customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                        .eq(CCustomerService::getIsDel, false).in(CCustomerService::getId, customerServiceIds));
        Map<Long, CCustomerService> customerServiceMap = customerServices.stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, Map<String, CustomerServiceBankAccount>> bankAccountMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .in(CustomerServiceBankAccount::getCustomerServiceId, customerServiceIds)).stream().collect(Collectors.groupingBy(CustomerServiceBankAccount::getCustomerServiceId, Collectors.toMap(CustomerServiceBankAccount::getBankAccountNumber, Function.identity(), (v1, v2) -> v1)));

        // 查询所有账期
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = ObjectUtils.isEmpty(customerServices) ? Lists.newArrayList() :
                customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServices.stream().map(CCustomerService::getId).collect(Collectors.toList())));
        Map<Long, Map<Integer, CustomerServicePeriodMonth>> periodMap = customerServicePeriodMonths.stream()
                .collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId,
                        Collectors.toMap(CustomerServicePeriodMonth::getPeriod, Function.identity(), (v1, v2) -> v1)));
        // 查询所有附件
        Map<Long, List<MaterialDeliverFileInventoryFile>> fileMap = materialDeliverFileInventoryFileMapper.selectList(new LambdaQueryWrapper<MaterialDeliverFileInventoryFile>()
                .in(MaterialDeliverFileInventoryFile::getFileInventoryId, fileInventories.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()))
                .eq(MaterialDeliverFileInventoryFile::getIsDel, false))
                .stream().collect(Collectors.groupingBy(MaterialDeliverFileInventoryFile::getFileInventoryId));
        List<MaterialPushPreviewListDTO> previewList = Lists.newArrayList();
        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            MaterialDeliver materialDeliver = materialDeliverMap.get(fileInventory.getMaterialDeliverId());
            CCustomerService customerService = Objects.isNull(fileInventory.getCustomerServiceId()) ? null : customerServiceMap.get(fileInventory.getCustomerServiceId());
            Long customerServiceId = Objects.isNull(customerService) ? null : customerService.getId();
            if (!StringUtils.isEmpty(fileInventory.getPeriod())) {
                Long customerServicePeriodMonthId = null;
                Map<Integer, CustomerServicePeriodMonth> monthMap = periodMap.get(customerServiceId);
                if (!ObjectUtils.isEmpty(monthMap)) {
                    CustomerServicePeriodMonth customerServicePeriodMonth = monthMap.get(Integer.parseInt(fileInventory.getPeriod()));
                    if (!Objects.isNull(customerServicePeriodMonth)) {
                        customerServicePeriodMonthId = customerServicePeriodMonth.getId();
                    }
                }
                MaterialPushPreviewListDTO previewListDTO = new MaterialPushPreviewListDTO();
                previewListDTO.setCustomerServiceId(customerServiceId);
                previewListDTO.setCustomerServicePeriodMonthId(customerServicePeriodMonthId);
                previewListDTO.setMaterialDeliverNumber(materialDeliver.getMaterialDeliverNumber());
                previewListDTO.setMaterialDeliverType(materialDeliver.getMaterialDeliverType());
                previewListDTO.setCustomerName(fileInventory.getCustomerName());
                previewListDTO.setBankName(previewListDTO.getBankName());
                previewListDTO.setCustomerServiceName(Objects.isNull(customerService) ? "" : customerService.getCustomerName());
                previewListDTO.setBankAccountNumber(fileInventory.getBankAccountNumber());
                previewListDTO.setBankInfo((StringUtils.isEmpty(fileInventory.getBankName()) ? "" : fileInventory.getBankName()) + (StringUtils.isEmpty(fileInventory.getBankAccountNumber()) ? "" : ("（" + fileInventory.getBankAccountNumber() + "）")));
                previewListDTO.setPeriod(fileInventory.getPeriod());
                previewListDTO.setMaterialDeliverId(fileInventory.getMaterialDeliverId());
                previewListDTO.setMaterialDeliverFileInventoryId(fileInventory.getId());
                List<MaterialDeliverFileInventoryFile> files = fileMap.get(fileInventory.getId());
                previewListDTO.setFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName())
                        .fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).deliverFileType(f.getFileType()).build()).collect(Collectors.toList()));
                String errorMsg = "";
                if (Objects.equals(nowPeriod.toString(), fileInventory.getPeriod())) {
                    errorMsg = "当月不交接";
                } else {
                    if (Objects.isNull(customerServiceId)) {
                        errorMsg = "客户不存在";
                    } else {
                        if (Objects.isNull(customerServicePeriodMonthId)) {
                            errorMsg = "账期不存在";
                        }
                    }
                }
                previewListDTO.setErrorMsg(errorMsg);
                previewListDTO.setIsSuccess(StringUtils.isEmpty(errorMsg));
                previewList.add(previewListDTO);
            } else {
                LocalDate startDate = fileInventory.getStartDate().withDayOfMonth(1);
                LocalDate endDate = (fileInventory.getEndDate() != null ? fileInventory.getEndDate() : startDate).withDayOfMonth(1);
                Integer startPeriod = Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
                Integer endPeriod = Integer.parseInt(endDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
                while (!startDate.isAfter(endDate)) {
                    Long customerServicePeriodMonthId = null;
                    Map<Integer, CustomerServicePeriodMonth> monthMap = periodMap.get(customerServiceId);
                    if (!ObjectUtils.isEmpty(monthMap)) {
                        CustomerServicePeriodMonth customerServicePeriodMonth = monthMap.get(Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMM"))));
                        if (!Objects.isNull(customerServicePeriodMonth)) {
                            customerServicePeriodMonthId = customerServicePeriodMonth.getId();
                        }
                    }
                    MaterialPushPreviewListDTO previewListDTO = new MaterialPushPreviewListDTO();
                    previewListDTO.setCustomerServiceId(customerServiceId);
                    previewListDTO.setCustomerServicePeriodMonthId(customerServicePeriodMonthId);
                    previewListDTO.setMaterialDeliverNumber(materialDeliver.getMaterialDeliverNumber());
                    previewListDTO.setMaterialDeliverType(materialDeliver.getMaterialDeliverType());
                    previewListDTO.setCustomerName(fileInventory.getCustomerName());
                    previewListDTO.setBankName(fileInventory.getBankName());
                    previewListDTO.setBankAccountNumber(fileInventory.getBankAccountNumber());
                    previewListDTO.setCustomerServiceName(Objects.isNull(customerService) ? "" : customerService.getCustomerName());
                    previewListDTO.setBankInfo((StringUtils.isEmpty(fileInventory.getBankName()) ? "" : fileInventory.getBankName()) + (StringUtils.isEmpty(fileInventory.getBankAccountNumber()) ? "" : ("（" + fileInventory.getBankAccountNumber() + "）")));
                    previewListDTO.setPeriod(startDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
                    previewListDTO.setMaterialDeliverId(fileInventory.getMaterialDeliverId());
                    previewListDTO.setMaterialDeliverFileInventoryId(fileInventory.getId());
                    previewListDTO.setStartPeriod(startPeriod);
                    previewListDTO.setEndPeriod(endPeriod);
                    List<MaterialDeliverFileInventoryFile> files = fileMap.get(fileInventory.getId());
                    previewListDTO.setFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName())
                            .fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).deliverFileType(f.getFileType()).build()).collect(Collectors.toList()));
                    String errorMsg = "";
                    if (Objects.equals(nowPeriod.toString(), previewListDTO.getPeriod())) {
                        errorMsg = "当月不交接";
                    } else {
                        if (Objects.isNull(fileInventory.getCustomerServiceId())) {
                            errorMsg = "银行不存在";
                        } else {
                            if (Objects.isNull(customerServiceId)) {
                                errorMsg = "客户不存在";
                            } else {
                                if (Objects.isNull(customerServicePeriodMonthId)) {
                                    errorMsg = "账期不存在";
                                } else {
                                    Map<String, CustomerServiceBankAccount> bankMap = bankAccountMap.get(customerServiceId);
                                    if (ObjectUtils.isEmpty(bankMap)) {
                                        errorMsg = "银行不存在";
                                    } else {
                                        CustomerServiceBankAccount customerServiceBankAccount = bankMap.get(fileInventory.getBankAccountNumber());
                                        if (Objects.isNull(customerServiceBankAccount)) {
                                            errorMsg = "银行不存在";
                                        } else {
                                            if ((!Objects.isNull(customerServiceBankAccount.getAccountOpenDate()) && Integer.parseInt(previewListDTO.getPeriod()) < Integer.parseInt(customerServiceBankAccount.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))
                                                    || (!Objects.isNull(customerServiceBankAccount.getAccountCloseDate()) && Integer.parseInt(previewListDTO.getPeriod()) > Integer.parseInt(customerServiceBankAccount.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))) {
                                                errorMsg = "账期不存在";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    previewListDTO.addErrorMsg(errorMsg);
                    previewListDTO.setIsSuccess(StringUtils.isEmpty(errorMsg));
                    previewList.add(previewListDTO);

                    startDate = startDate.plusMonths(1);
                }
            }
        }
        // **整合相同的记录逻辑**
        Map<String, MaterialPushPreviewListDTO> mergedPreviewMap = new LinkedHashMap<>();
        for (MaterialPushPreviewListDTO preview : previewList) {
            String key;
            if (Objects.equals(preview.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                if (!Objects.isNull(preview.getCustomerServiceId())) {
                    key = preview.getMaterialDeliverId() + "_" + preview.getCustomerServiceId() + "_" + preview.getPeriod() + "_" + preview.getBankAccountNumber();
                } else {
                    key = preview.getMaterialDeliverId() + "_" + preview.getCustomerName() + "_" + preview.getPeriod() + "_" + preview.getBankAccountNumber();
                }
            } else {
                if (!Objects.isNull(preview.getCustomerServiceId())) {
                    key = preview.getMaterialDeliverId() + "_" + preview.getCustomerServiceId() + "_" + preview.getPeriod();
                } else {
                    key = preview.getMaterialDeliverId() + "_" + preview.getCustomerName() + "_" + preview.getPeriod();
                }
            }
            if (mergedPreviewMap.containsKey(key)) {
                MaterialPushPreviewListDTO existing = mergedPreviewMap.get(key);
                List<CommonFileVO> files = Lists.newArrayList();
                Set<Long> fileInventoryIds = Sets.newHashSet();
                if (!ObjectUtils.isEmpty(existing.getFiles())) {
                    files.addAll(existing.getFiles());
                }
                if (!ObjectUtils.isEmpty(preview.getFiles())) {
                    files.addAll(preview.getFiles());
                }
                if (!ObjectUtils.isEmpty(existing.getFileInventoryIds())) {
                    fileInventoryIds.addAll(existing.getFileInventoryIds());
                }
                fileInventoryIds.add(preview.getMaterialDeliverFileInventoryId());
                existing.setFiles(files);
                existing.setFileCount((long) existing.getFiles().size());
                existing.setFileInventoryIds(fileInventoryIds);
            } else {
                preview.setFileCount(ObjectUtils.isEmpty(preview.getFiles()) ? 0L : preview.getFiles().size());
                preview.setFileInventoryIds(Sets.newHashSet(preview.getMaterialDeliverFileInventoryId()));
                mergedPreviewMap.put(key, preview);
            }
        }
        previewList = new ArrayList<>(mergedPreviewMap.values());

        List<MaterialPushPreviewListDTO> failList = Lists.newArrayList();
        List<MaterialPushPreviewListDTO> successList = Lists.newArrayList();
        List<MaterialPushPreviewListDTO> noErrorList = previewList.stream().filter(row -> StringUtils.isEmpty(row.getErrorMsg())).collect(Collectors.toList());
        // 根据previewListDTO 最后再得到success和fail
        List<MaterialPushPreviewListDTO> bankAccountingCashier = noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())).collect(Collectors.toList());
        List<MaterialPushPreviewListDTO> inAccountCashier = noErrorList.stream().filter(row -> !Objects.equals(row.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())).collect(Collectors.toList());
        List<CustomerServiceCashierAccounting> existsInAccountCashier = ObjectUtils.isEmpty(inAccountCashier) ? Lists.newArrayList() :
                customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getIsDel, false).in(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, inAccountCashier.stream().map(MaterialPushPreviewListDTO::getCustomerServicePeriodMonthId).collect(Collectors.toList()))
                        .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode()));
        Map<Long/*账期id*/, Long/*交付单id*/> existsInAccountPeriodIds = existsInAccountCashier.stream().collect(Collectors.toMap(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getId, (v1, v2) -> v1));
        List<CustomerServiceCashierAccounting> existsBankAccountingCashier = ObjectUtils.isEmpty(bankAccountingCashier) ? Lists.newArrayList() :
                customerServiceCashierAccountingMapper.selectBatchByCustomerServicePeriodIdAndBankAccountNumber(bankAccountingCashier);
        Map<String/*账期id+银行账号*/, Long/*交付单id*/> existsBankAccountingCashierPeriodIds = existsBankAccountingCashier.stream().collect(Collectors.toMap(row -> row.getCustomerServicePeriodMonthId() + "_" + row.getBankAccountNumber(), CustomerServiceCashierAccounting::getId, (v1, v2) -> v1));
        for (MaterialPushPreviewListDTO previewListDTO : previewList) {
            if (Objects.equals(previewListDTO.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                if (!Objects.isNull(previewListDTO.getCustomerServicePeriodMonthId())) {
                    if (isDuplicatePeriodMonthIdAndBankAccountNumber(previewListDTO.getCustomerServicePeriodMonthId(), previewListDTO.getBankAccountNumber(), noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId()) && !Objects.isNull(row.getCustomerServicePeriodMonthId())).collect(Collectors.toList()))) {
                        previewListDTO.addErrorMsg("交接单冲突");
                        previewListDTO.setIsSuccess(false);
                    } else {
                        if (previewListDTO.getIsSuccess()) {
                            String key = previewListDTO.getCustomerServicePeriodMonthId() + "_" + previewListDTO.getBankAccountNumber();
                            if (existsBankAccountingCashierPeriodIds.containsKey(key)) {
                                previewListDTO.addErrorMsg("正常（材料补充）");
                                previewListDTO.setAccountingCashierId(existsBankAccountingCashierPeriodIds.get(key));
                                previewListDTO.setIsSuccess(true);
                            } else {
                                previewListDTO.addErrorMsg("正常（新建交付单）");
                                previewListDTO.setIsSuccess(true);
                            }
                        }
                    }
                } else {
                    if (isDuplicateCustomerNameAndBankAccountNumberAndPeriod(previewListDTO.getCustomerName(), previewListDTO.getBankAccountNumber(), previewListDTO.getPeriod(), previewList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId()) && Objects.isNull(row.getCustomerServicePeriodMonthId())).collect(Collectors.toList()))) {
                        previewListDTO.addErrorMsg("交接单冲突");
                        previewListDTO.setIsSuccess(false);
                    }
                }
                if (previewListDTO.getIsSuccess()) {
                    successList.add(previewListDTO);
                } else {
                    previewListDTO.setErrorMsg("异常（" + previewListDTO.getErrorMsg() + "）");
                    failList.add(previewListDTO);
                }
            } else {
                if (!Objects.isNull(previewListDTO.getCustomerServicePeriodMonthId())) {
                    if (isDuplicatePeriodMonthId(previewListDTO.getCustomerServicePeriodMonthId(), noErrorList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId())).collect(Collectors.toList()))) {
                        previewListDTO.addErrorMsg("交接单冲突");
                        previewListDTO.setIsSuccess(false);
                    } else {
                        if (previewListDTO.getIsSuccess()) {
                            if (existsInAccountPeriodIds.containsKey(previewListDTO.getCustomerServicePeriodMonthId())) {
                                previewListDTO.addErrorMsg("正常（材料补充）");
                                previewListDTO.setAccountingCashierId(existsInAccountPeriodIds.get(previewListDTO.getCustomerServicePeriodMonthId()));
                                previewListDTO.setIsSuccess(true);
                            } else {
                                previewListDTO.addErrorMsg("正常（新建交付单）");
                                previewListDTO.setIsSuccess(true);
                            }
                        }
                    }
                } else {
                    if (isDuplicateCustomerNameAndPeriod(previewListDTO.getCustomerName(), previewListDTO.getPeriod(), previewList.stream().filter(row -> Objects.equals(row.getMaterialDeliverType(), previewListDTO.getMaterialDeliverType()) && !Objects.equals(row.getMaterialDeliverId(), previewListDTO.getMaterialDeliverId()) && Objects.isNull(row.getCustomerServicePeriodMonthId())).collect(Collectors.toList()))) {
                        previewListDTO.addErrorMsg("交接单冲突");
                        previewListDTO.setIsSuccess(false);
                    }
                }
                if (previewListDTO.getIsSuccess()) {
                    successList.add(previewListDTO);
                } else {
                    previewListDTO.setErrorMsg("异常（" + previewListDTO.getErrorMsg() + "）");
                    failList.add(previewListDTO);
                }
            }
        }
        if (!ObjectUtils.isEmpty(successList)) {
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY + dto.getBatchNo(), successList, 1000, 60 * 60 * 24, TimeUnit.SECONDS);
        }
        if (!ObjectUtils.isEmpty(failList)) {
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + dto.getBatchNo(), failList, 1000, 60 * 60 * 24, TimeUnit.SECONDS);
        }
        dto.setSuccessList(successList);
        dto.setFailList(failList);
        return dto;
    }

    private boolean isDuplicateCustomerNameAndPeriod(String customerName, String period, List<MaterialPushPreviewListDTO> dataList) {
        return dataList.stream().anyMatch(data -> Objects.equals(data.getCustomerName() + "_" + data.getPeriod(), customerName + "_" + period));
    }

    private boolean isDuplicatePeriodMonthId(Long customerServicePeriodMonthId, List<MaterialPushPreviewListDTO> dataList) {
        return dataList.stream().anyMatch(data -> Objects.equals(data.getCustomerServicePeriodMonthId(), customerServicePeriodMonthId));
    }

    private boolean isDuplicatePeriodMonthIdAndBankAccountNumber(Long customerServicePeriodMonthId, String bankAccountNumber, List<MaterialPushPreviewListDTO> dataList) {
        return dataList.stream().anyMatch(data -> Objects.equals(data.getCustomerServicePeriodMonthId() + "_" + data.getBankAccountNumber(), customerServicePeriodMonthId + "_" + bankAccountNumber));
    }

    private boolean isDuplicateCustomerNameAndBankAccountNumberAndPeriod(String customerName, String bankAccountNumber, String period, List<MaterialPushPreviewListDTO> dataList) {
        return dataList.stream().anyMatch(data -> Objects.equals(data.getCustomerName() + "_" + data.getBankAccountNumber() + "_" + data.getPeriod(), customerName + "_" + bankAccountNumber + "_" + period));
    }

    private boolean isDuplicatePeriodMonthIdByPeriodInventory(Long customerServicePeriodMonthId, List<MaterialDeliverPeriodInventory> dataList) {
        return dataList.stream().anyMatch(data -> !Objects.isNull(data.getCustomerServicePeriodMonthId()) && Objects.equals(data.getCustomerServicePeriodMonthId(), customerServicePeriodMonthId));
    }

    private boolean isDuplicatePeriodMonthIdAndBankAccountNumberByPeriodInventory(Long customerServicePeriodMonthId, String bankAccountNumber, List<MaterialDeliverPeriodInventory> dataList) {
        return dataList.stream().anyMatch(data -> !StringUtils.isEmpty(data.getBankAccountNumber()) && !Objects.isNull(data.getCustomerServicePeriodMonthId()) && Objects.equals(data.getCustomerServicePeriodMonthId() + "_" + data.getBankAccountNumber(), customerServicePeriodMonthId + "_" + bankAccountNumber));
    }
}
