package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceInsuranceFundStatus;

/**
 * 客户服务五险一金月状态Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICustomerServiceInsuranceFundStatusService extends IService<CustomerServiceInsuranceFundStatus>
{
    /**
     * 查询客户服务五险一金月状态
     * 
     * @param id 客户服务五险一金月状态主键
     * @return 客户服务五险一金月状态
     */
    public CustomerServiceInsuranceFundStatus selectCustomerServiceInsuranceFundStatusById(Long id);

    /**
     * 查询客户服务五险一金月状态列表
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 客户服务五险一金月状态集合
     */
    public List<CustomerServiceInsuranceFundStatus> selectCustomerServiceInsuranceFundStatusList(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus);

    /**
     * 新增客户服务五险一金月状态
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 结果
     */
    public int insertCustomerServiceInsuranceFundStatus(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus);

    /**
     * 修改客户服务五险一金月状态
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 结果
     */
    public int updateCustomerServiceInsuranceFundStatus(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus);

    /**
     * 批量删除客户服务五险一金月状态
     * 
     * @param ids 需要删除的客户服务五险一金月状态主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInsuranceFundStatusByIds(Long[] ids);

    /**
     * 删除客户服务五险一金月状态信息
     * 
     * @param id 客户服务五险一金月状态主键
     * @return 结果
     */
    public int deleteCustomerServiceInsuranceFundStatusById(Long id);
}
