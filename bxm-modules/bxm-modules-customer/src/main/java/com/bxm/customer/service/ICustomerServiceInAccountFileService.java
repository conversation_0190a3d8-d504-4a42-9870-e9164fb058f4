package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.inAccount.InAccountFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceInAccountFile;
import com.bxm.customer.domain.vo.inAccount.file.GetByInAccountAndTypeVO;

import java.util.List;
import java.util.Map;

/**
 * 入账交付 附件Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceInAccountFileService extends IService<CustomerServiceInAccountFile> {
    /**
     * 查询入账交付 附件
     *
     * @param id 入账交付 附件主键
     * @return 入账交付 附件
     */
    public CustomerServiceInAccountFile selectCustomerServiceInAccountFileById(Long id);

    /**
     * 查询入账交付 附件列表
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 入账交付 附件集合
     */
    public List<CustomerServiceInAccountFile> selectCustomerServiceInAccountFileList(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 新增入账交付 附件
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    public int insertCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 修改入账交付 附件
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    public int updateCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 批量删除入账交付 附件
     *
     * @param ids 需要删除的入账交付 附件主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInAccountFileByIds(Long[] ids);

    /**
     * 删除入账交付 附件信息
     *
     * @param id 入账交付 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceInAccountFileById(Long id);

    //****** start self method ******
    //根据 fileType 获取附件
    List<CustomerServiceInAccountFile> selectByFileType(List<Long> inAccountIds, List<InAccountFileType> inAccountFileTypes);

    //根据 fileType 获取附件，并根据类型转成MAP
    Map<Long, Map<Integer, List<CustomerServiceInAccountFile>>> selectMapByFileType(List<Long> inAccountIds, List<InAccountFileType> inAccountFileTypes);

    //根据 交付ID 获取附件
    List<CustomerServiceInAccountFile> selectByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes);

    //根据 交付ID 获取附件，并根据类型转成MAP
    Map<Integer, List<CustomerServiceInAccountFile>> selectMapByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes);

    //根据 交付ID 获取附件，MAP，key是主类型+子类型
    Map<String, List<CustomerServiceInAccountFile>> selectMapSubKeyByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes);

    //根据 交付ID 删除附件
    void deleteByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes);

    //保存附件
    void saveFile(Long inAccountId, List<CommonFileVO> files, InAccountFileType inAccountFileTypes, String subFileType);

    //原始附件处理成前端可用数据
    List<CommonFileVO> covToCommonFileVO(List<CustomerServiceInAccountFile> files);

    //根据入账交付ID和附件类型获取附件
    Map<Integer, List<CommonFileVO>> getByInAccountAndTypes(GetByInAccountAndTypeVO vo);
}
