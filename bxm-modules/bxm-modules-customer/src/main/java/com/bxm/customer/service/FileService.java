package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.ThirdpartFileUploadRecord;
import com.bxm.customer.domain.vo.CommonNoticeFileVO;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.file.api.domain.RemoteThirdpartFileVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class FileService {

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private IThirdpartFileUploadRecordService thirdpartFileUploadRecordService;

    public String getFullFileUrl(String fileUrl) {
        return remoteFileService.getFullFileUrl(fileUrl).getDataThrowException();
    }

    /**
     * 获取文件地址
     * @param fileUrl 文件短链接
     * @param validTime 有效期 秒
     * @return
     */
    public String getFullFileUrlValidTime(String fileUrl, Long validTime) {
        return remoteFileService.getFullFileUrlValidTime(fileUrl, validTime).getDataThrowException();
    }

    public Map<String, RemoteAliFileDTO> getFileInfoBatch(List<String> fileUrls) {
        if (ObjectUtils.isEmpty(fileUrls)) {
            return Maps.newHashMap();
        }
        List<RemoteAliFileDTO> fileInfos = remoteFileService.batchGetFileInfo(fileUrls).getDataThrowException();
        return ObjectUtils.isEmpty(fileInfos) ? Maps.newHashMap() :
                fileInfos.stream().collect(Collectors.toMap(RemoteAliFileDTO::getUrl, r -> r, (k1, k2) -> k1));
    }

    public List<CommonFileVO> uploadByThirdFileUrls(List<CommonNoticeFileVO> images, Long deliverId) {
        List<CommonFileVO> result = Lists.newArrayList();
        List<CommonNoticeFileVO> waitUploadImages = Lists.newArrayList();
        for (CommonNoticeFileVO image : images) {
            ThirdpartFileUploadRecord one = thirdpartFileUploadRecordService.getOne(new LambdaQueryWrapper<ThirdpartFileUploadRecord>()
                    .eq(ThirdpartFileUploadRecord::getFileName, image.getFileName())
                    .eq(ThirdpartFileUploadRecord::getDeliverId, deliverId).last("limit 1"));
            if (!Objects.isNull(one)) {
                result.add(CommonFileVO.builder()
                        .fileName(one.getFileName())
                        .fileUrl(one.getFileUrl())
                        .officalFilename(one.getOfficalFileName())
                        .build());
            } else {
                waitUploadImages.add(image);
            }
        }
        if (!ObjectUtils.isEmpty(waitUploadImages)) {
            List<CommonFileVO> uploadImages = remoteFileService.uploadByThirdFileUrls(waitUploadImages.stream().map(row -> RemoteThirdpartFileVO.builder()
                    .fileId(row.getFileId())
                    .fileName(row.getFileName())
                    .officalFilename(row.getOfficalFilename())
                    .build()).collect(Collectors.toList()), SecurityConstants.INNER).getDataThrowException(false);
            if (!ObjectUtils.isEmpty(uploadImages)) {
                result.addAll(uploadImages);
                thirdpartFileUploadRecordService.saveBatch(uploadImages.stream().map(row -> new ThirdpartFileUploadRecord().setThirdpartFileUrl(row.getOriginFileUrl())
                        .setFileUrl(row.getFileUrl()).setFileName(row.getFileName()).setOfficalFileName(row.getOfficalFilename()).setDeliverId(deliverId)).collect(Collectors.toList()));
            }
        }
        return result;
    }

    public List<CommonFileVO> uploadReportTableFile(CommonNoticeFileVO thirdpartFile) {
        return remoteFileService.uploadByReportTable(RemoteThirdpartFileVO.builder()
                .fileId(thirdpartFile.getFileId())
                .fileName(thirdpartFile.getFileName())
                .officalFilename(thirdpartFile.getOfficalFilename())
                .deliverId(thirdpartFile.getDeliverId())
                .build(), SecurityConstants.INNER).getDataThrowException(false);
    }
}
