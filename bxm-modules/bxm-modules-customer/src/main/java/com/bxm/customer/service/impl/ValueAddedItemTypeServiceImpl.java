package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.mapper.ValueAddedItemTypeMapper;
import com.bxm.customer.service.IValueAddedItemTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 增值事项类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedItemTypeServiceImpl extends ServiceImpl<ValueAddedItemTypeMapper, ValueAddedItemType>
        implements IValueAddedItemTypeService {

    @Override
    public List<ValueAddedItemTypeVO> listItemTypeVO() {
        try {
            log.info("Query value added item type list");

            // 查询未删除的增值事项类型
            LambdaQueryWrapper<ValueAddedItemType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedItemType::getIsDel, false)
                       .orderByAsc(ValueAddedItemType::getId);

            List<ValueAddedItemType> itemTypes = this.list(queryWrapper);

            // 转换为VO对象
            List<ValueAddedItemTypeVO> voList = itemTypes.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            log.info("Query value added item type list success, count: {}", voList.size());
            return voList;

        } catch (Exception e) {
            log.error("Query value added item type list failed", e);
            throw new RuntimeException("查询增值事项类型列表失败", e);
        }
    }

    /**
     * 将实体对象转换为VO对象
     *
     * @param itemType 增值事项类型实体
     * @return 增值事项类型VO
     */
    private ValueAddedItemTypeVO convertToVO(ValueAddedItemType itemType) {
        ValueAddedItemTypeVO vo = new ValueAddedItemTypeVO();
        BeanUtils.copyProperties(itemType, vo);
        return vo;
    }

}
