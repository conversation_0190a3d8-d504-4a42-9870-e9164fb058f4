package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiSyncStatementDetail;

/**
 * 医社保个人明细查询记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
@Mapper
public interface OpenApiSyncStatementDetailMapper extends BaseMapper<OpenApiSyncStatementDetail>
{
    /**
     * 查询医社保个人明细查询记录
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 医社保个人明细查询记录
     */
    public OpenApiSyncStatementDetail selectOpenApiSyncStatementDetailById(Long id);

    /**
     * 查询医社保个人明细查询记录列表
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 医社保个人明细查询记录集合
     */
    public List<OpenApiSyncStatementDetail> selectOpenApiSyncStatementDetailList(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 新增医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    public int insertOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 修改医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    public int updateOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 删除医社保个人明细查询记录
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 结果
     */
    public int deleteOpenApiSyncStatementDetailById(Long id);

    /**
     * 批量删除医社保个人明细查询记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncStatementDetailByIds(Long[] ids);
}
