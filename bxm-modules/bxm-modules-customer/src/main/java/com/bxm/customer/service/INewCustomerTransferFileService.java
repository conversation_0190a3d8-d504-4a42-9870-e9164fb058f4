package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.NewCustomerTransferFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.NewCustomerTransferFile;

/**
 * 新户流转客户文件Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
public interface INewCustomerTransferFileService extends IService<NewCustomerTransferFile>
{
    /**
     * 查询新户流转客户文件
     * 
     * @param id 新户流转客户文件主键
     * @return 新户流转客户文件
     */
    public NewCustomerTransferFile selectNewCustomerTransferFileById(Long id);

    /**
     * 查询新户流转客户文件列表
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 新户流转客户文件集合
     */
    public List<NewCustomerTransferFile> selectNewCustomerTransferFileList(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 新增新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    public int insertNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 修改新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    public int updateNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 批量删除新户流转客户文件
     * 
     * @param ids 需要删除的新户流转客户文件主键集合
     * @return 结果
     */
    public int deleteNewCustomerTransferFileByIds(Long[] ids);

    /**
     * 删除新户流转客户文件信息
     * 
     * @param id 新户流转客户文件主键
     * @return 结果
     */
    public int deleteNewCustomerTransferFileById(Long id);

    void removeAndSaveNewFiles(Long newCustomerTransferId, NewCustomerTransferFileType newCustomerTransferFileType, List<CommonFileVO> files);

    List<CommonFileVO> getFilesByCustomerIdAndFileType(Long customerId, Integer fileType);
}
