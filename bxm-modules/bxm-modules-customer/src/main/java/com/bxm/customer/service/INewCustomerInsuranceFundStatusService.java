package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerInsuranceFundStatus;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceInfoDTO;

/**
 * 新户流转五险一金月状态Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerInsuranceFundStatusService extends IService<NewCustomerInsuranceFundStatus>
{
    /**
     * 查询新户流转五险一金月状态
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 新户流转五险一金月状态
     */
    public NewCustomerInsuranceFundStatus selectNewCustomerInsuranceFundStatusById(Long id);

    /**
     * 查询新户流转五险一金月状态列表
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 新户流转五险一金月状态集合
     */
    public List<NewCustomerInsuranceFundStatus> selectNewCustomerInsuranceFundStatusList(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 新增新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    public int insertNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 修改新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    public int updateNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus);

    /**
     * 批量删除新户流转五险一金月状态
     * 
     * @param ids 需要删除的新户流转五险一金月状态主键集合
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundStatusByIds(Long[] ids);

    /**
     * 删除新户流转五险一金月状态信息
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundStatusById(Long id);

    List<NewCustomerInsuranceFundStatus> selectByCustomerId(Long customerId);

    void deleteByCustomerIdAndType(Long customerId, Integer type);

    void removeAndSaveNew(Long newCustomerTransferId, NewCustomerTransferInsuranceInfoDTO insuranceInfoDTO);

    Map<Long, List<NewCustomerInsuranceFundStatus>> selectMapByCustomerIds(List<Long> newCustomerTransferIds);
}
