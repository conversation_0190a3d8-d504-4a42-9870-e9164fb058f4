package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.CustomerServiceChangeType;
import com.bxm.customer.domain.CCustomerServiceChangeRecord;

import java.util.List;

/**
 * 客户服务变更记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface ICCustomerServiceChangeRecordService extends IService<CCustomerServiceChangeRecord>
{
    /**
     * 查询客户服务变更记录
     * 
     * @param id 客户服务变更记录主键
     * @return 客户服务变更记录
     */
    public CCustomerServiceChangeRecord selectCCustomerServiceChangeRecordById(Long id);

    /**
     * 查询客户服务变更记录列表
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 客户服务变更记录集合
     */
    public List<CCustomerServiceChangeRecord> selectCCustomerServiceChangeRecordList(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 新增客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    public int insertCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 修改客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    public int updateCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 批量删除客户服务变更记录
     * 
     * @param ids 需要删除的客户服务变更记录主键集合
     * @return 结果
     */
    public int deleteCCustomerServiceChangeRecordByIds(Long[] ids);

    /**
     * 删除客户服务变更记录信息
     * 
     * @param id 客户服务变更记录主键
     * @return 结果
     */
    public int deleteCCustomerServiceChangeRecordById(Long id);

    List<CCustomerServiceChangeRecord> selectNotDoneBatchByCustomerIdAndChangeType(List<Long> customerServiceIds, CustomerServiceChangeType changeType);
}
