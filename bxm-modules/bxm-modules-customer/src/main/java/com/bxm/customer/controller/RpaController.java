package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.RpaTaskType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.customize.annotation.TimeParamHandler;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.dto.rpa.RpaDTO;
import com.bxm.customer.domain.dto.rpa.TaskTypeDTO;
import com.bxm.customer.domain.vo.rpa.RpaSearchVO;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.IDownloadRecordService;
import com.bxm.customer.service.IOpenApiNoticeRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/rpa")
@Api(tags = "rpa模块")
public class RpaController {

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @GetMapping("/taskTypeList")
    @ApiOperation("任务类型下拉数据")
    public Result<List<TaskTypeDTO>> taskTypeList() {
        return Result.ok(Arrays.asList(RpaTaskType.values()).stream().map(row -> TaskTypeDTO.builder().code(row.getCode()).name(row.getTaskTypeName()).build()).collect(Collectors.toList()));
    }

    @GetMapping("/rpaList")
    @ApiOperation("rpa列表")
    public Result<IPage<RpaDTO>> rpaList(RpaSearchVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(openApiNoticeRecordService.rpaList(vo));
    }

    @PostMapping("/rpaListExportAndUpload")
    @ApiOperation("导出rpa列表（异步导出）")
    @TimeParamHandler
    public Result rpaListExportAndUpload(RpaSearchVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "rpa记录" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.RPA_RECORD);
        CompletableFuture.runAsync(() -> {
            try {
                List<RpaDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RpaDTO> l = openApiNoticeRecordService.rpaList(vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<RpaDTO> util = new ExcelUtil<>(RpaDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }
}
