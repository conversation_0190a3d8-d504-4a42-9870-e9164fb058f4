package com.bxm.customer.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.DeliverStatus;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.CustomerDeliverRpaDetailDTO;
import com.bxm.customer.domain.dto.rpa.*;
import com.bxm.customer.mapper.CustomerRpaRecordMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerRpaRecordDetailMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * rap明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
@Slf4j
public class CustomerRpaRecordDetailServiceImpl extends ServiceImpl<CustomerRpaRecordDetailMapper, CustomerRpaRecordDetail> implements ICustomerRpaRecordDetailService
{
    @Autowired
    private CustomerRpaRecordDetailMapper customerRpaRecordDetailMapper;

    @Autowired
    private ICustomerRpaDetailFileService customerRpaDetailFileService;

    @Autowired
    private CustomerRpaRecordMapper customerRpaRecordMapper;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    /**
     * 查询rap明细
     * 
     * @param id rap明细主键
     * @return rap明细
     */
    @Override
    public CustomerRpaRecordDetail selectCustomerRpaRecordDetailById(Long id)
    {
        return customerRpaRecordDetailMapper.selectCustomerRpaRecordDetailById(id);
    }

    /**
     * 查询rap明细列表
     * 
     * @param customerRpaRecordDetail rap明细
     * @return rap明细
     */
    @Override
    public List<CustomerRpaRecordDetail> selectCustomerRpaRecordDetailList(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        return customerRpaRecordDetailMapper.selectCustomerRpaRecordDetailList(customerRpaRecordDetail);
    }

    /**
     * 新增rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    @Override
    public int insertCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        customerRpaRecordDetail.setCreateTime(DateUtils.getNowDate());
        return customerRpaRecordDetailMapper.insertCustomerRpaRecordDetail(customerRpaRecordDetail);
    }

    /**
     * 修改rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    @Override
    public int updateCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        customerRpaRecordDetail.setUpdateTime(DateUtils.getNowDate());
        return customerRpaRecordDetailMapper.updateCustomerRpaRecordDetail(customerRpaRecordDetail);
    }

    /**
     * 批量删除rap明细
     * 
     * @param ids 需要删除的rap明细主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordDetailByIds(Long[] ids)
    {
        return customerRpaRecordDetailMapper.deleteCustomerRpaRecordDetailByIds(ids);
    }

    /**
     * 删除rap明细信息
     * 
     * @param id rap明细主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordDetailById(Long id)
    {
        return customerRpaRecordDetailMapper.deleteCustomerRpaRecordDetailById(id);
    }

    @Override
    public List<CustomerRpaRecordDetail> selectByRpaRecordId(Long rpaRecordId) {
        if (Objects.isNull(rpaRecordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId));
    }

    @Override
    public IPage<CustomerDeliverRpaDetailDTO> rpaDetailList(Long rpaRecordId, String customerName, Integer status, Integer pageNum, Integer pageSize) {
        IPage<CustomerDeliverRpaDetailDTO> result = new Page<>();
        IPage<CustomerRpaRecordDetail> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(!Objects.isNull(status), CustomerRpaRecordDetail::getStatus, status)
                .like(!StringUtils.isEmpty(customerName), CustomerRpaRecordDetail::getCustomerName, customerName)
                .eq(!Objects.isNull(rpaRecordId), CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId)
                .orderByAsc(CustomerRpaRecordDetail::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<CustomerRpaRecord> records = customerRpaRecordMapper.selectBatchIds(iPage.getRecords().stream().map(CustomerRpaRecordDetail::getRpaRecordId).distinct().collect(Collectors.toList()));
            Map<Long, CustomerRpaRecord> recordMap = ObjectUtils.isEmpty(records) ? Maps.newHashMap() :
                    records.stream().collect(Collectors.toMap(CustomerRpaRecord::getId, Function.identity()));
            List<CustomerRpaDetailFile> files = customerRpaDetailFileService.selectByBatchRpaDetailId(iPage.getRecords().stream().map(CustomerRpaRecordDetail::getId).collect(Collectors.toList()));
            Map<Long, List<CustomerRpaDetailFile>> fileMap = ObjectUtils.isEmpty(files) ? Maps.newHashMap() :
                    files.stream().collect(Collectors.groupingBy(CustomerRpaDetailFile::getRpaDetailId));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                CustomerRpaRecord record = recordMap.get(row.getRpaRecordId());
                List<CustomerRpaDetailFile> fileList = fileMap.getOrDefault(row.getId(), Lists.newArrayList());
                CustomerDeliverRpaDetailDTO dto = new CustomerDeliverRpaDetailDTO();
                BeanUtils.copyProperties(row, dto);
                dto.setFileCount(fileList.size());
                dto.setPeriod(Objects.isNull(record) ? null : record.getPeriod());
                dto.setRpaType(Objects.isNull(record) ? null : record.getRpaType());
                dto.setOperType(Objects.isNull(record) ? null : record.getOperType());
                return dto;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void saveRpaToCustomerDeliverByDetailIds(List<Long> rpaDetailIds, Long deptId) {
        if (ObjectUtils.isEmpty(rpaDetailIds)) {
            return;
        }
        saveRpaToCustomerDeliverByDetails(getBaseMapper().selectBatchIds(rpaDetailIds), deptId);
    }

    @Override
    @Transactional
    public void saveRpaToCustomerDeliverByDetails(List<CustomerRpaRecordDetail> details, Long deptId) {
        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        details = details.stream().filter(d -> d.getStatus() == 0 || d.getStatus() == 3).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        CustomerRpaRecord record = customerRpaRecordMapper.selectById(details.get(0).getRpaRecordId());
        details.forEach(d -> {
            if (!Objects.isNull(record)) {
                d.setOperType(record.getOperType());
                d.setRpaType(record.getRpaType());
                d.setPeriod(record.getPeriod());
            }
        });
        List<String> noCustomerIdCreditCodes = details.stream().filter(d -> Objects.isNull(d.getCustomerServiceId())).map(CustomerRpaRecordDetail::getCreditCode).collect(Collectors.toList());
        List<CCustomerService> customerServices = ObjectUtils.isEmpty(noCustomerIdCreditCodes) ? Lists.newArrayList() :
                customerServiceService.getCreditCodeByListCreditCode(noCustomerIdCreditCodes);
        Map<String, CCustomerService> customerMap = ObjectUtils.isEmpty(customerServices) ? Maps.newHashMap() :
                customerServices.stream().collect(Collectors.toMap(CCustomerService::getCreditCode, Function.identity(), (v1, v2) -> v1));
        List<CustomerRpaRecordDetail> updates = Lists.newArrayList();
        List<CustomerRpaRecordDetail> successDetails = Lists.newArrayList();
        details.forEach(d -> {
            CustomerRpaRecordDetail update = new CustomerRpaRecordDetail().setId(d.getId());
            StringBuilder failReason = new StringBuilder();
            if (Objects.isNull(d.getRpaRecordId())) {
                failReason.append("上传记录不存在；");
            }
            Long customerServiceId = d.getCustomerServiceId();
            if (Objects.isNull(d.getCustomerServiceId())) {
                CCustomerService customerService = customerMap.get(d.getCreditCode());
                if (Objects.isNull(customerService)) {
                    failReason.append("客户不存在；");
                } else {
                    customerServiceId = customerService.getId();
                }
            }
            if (!Objects.isNull(customerServiceId)) {
                d.setCustomerServiceId(customerServiceId);
                if (d.getRpaType() == 1) {
                    // 医保
                    CustomerDeliver medicareDeliver = customerDeliverService.selectByCustomerServiceIdAndPeriodAndDeliverType(customerServiceId, d.getPeriod(), DeliverType.MEDICAL_INSURANCE.getCode());
                    // 社保
                    CustomerDeliver socialDeliver = customerDeliverService.selectByCustomerServiceIdAndPeriodAndDeliverType(customerServiceId, d.getPeriod(), DeliverType.SOCIAL_INSURANCE.getCode());
                    if (Objects.isNull(medicareDeliver)) {
                        failReason.append("医保交付不存在；");
                    } else {
                        d.setMedicalDeliverId(medicareDeliver.getId());
                        if (d.getOperType() == 1 && !Objects.equals(medicareDeliver.getStatus(), DeliverStatus.STATUS_0.getCode())) {
                            failReason.append("医保交付不为待申报状态；");
                        }
                        if (d.getOperType() == 2 && !Objects.equals(medicareDeliver.getStatus(), DeliverStatus.STATUS_3.getCode())) {
                            failReason.append("医保交付不为待扣款状态；");
                        }
                    }
                    if (Objects.isNull(socialDeliver)) {
                        failReason.append("社保交付不存在；");
                    } else {
                        d.setCommonDeliverId(socialDeliver.getId());
                        if (d.getOperType() == 1 && !Objects.equals(socialDeliver.getStatus(), DeliverStatus.STATUS_0.getCode())) {
                            failReason.append("社保交付不为待交付状态；");
                        }
                        if (d.getOperType() == 2 && !Objects.equals(socialDeliver.getStatus(), DeliverStatus.STATUS_3.getCode())) {
                            failReason.append("社保交付不为待扣款状态；");
                        }
                    }
                } else if (d.getRpaType() == 5) {
                    if (customerServicePeriodMonthIncomeService.checkPeriodMonthIncomeExists(customerServiceId, d.getPeriod(), null)) {
                        failReason.append("当月收入已存在；");
                    }
                } else if (d.getRpaType() == 4) {
                    // 预认证
                    CustomerDeliver customerDeliver = customerDeliverService.selectByCustomerServiceIdAndPeriodAndDeliverType(customerServiceId, d.getPeriod(), DeliverType.PRE_AUTH.getCode());
                    if (Objects.isNull(customerDeliver)) {
                        failReason.append("预认证交付不存在；");
                    } else {
                        d.setCommonDeliverId(customerDeliver.getId());
                        if (d.getOperType() == 3 && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_103.getCode())) {
                            failReason.append("预认证交付不为待确认状态；");
                        }
                    }
                } else {
                    CustomerDeliver customerDeliver = customerDeliverService.selectByCustomerServiceIdAndPeriodAndDeliverType(customerServiceId, d.getPeriod(), d.getRpaType() == 6 ? 6 : (d.getRpaType() + 1));
                    if (Objects.isNull(customerDeliver)) {
                        failReason.append("交付单不存在；");
                    } else {
                        d.setCommonDeliverId(customerDeliver.getId());
                        if (d.getOperType() == 1 && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_0.getCode())) {
                            failReason.append("交付单不为待申报状态；");
                        }
                        if (d.getOperType() == 2 && !Objects.equals(customerDeliver.getStatus(), DeliverStatus.STATUS_3.getCode())) {
                            failReason.append("交付单不为待扣款状态；");
                        }
                    }
                }
            } else {
                failReason.append("客户服务不存在；");
            }
            if (!StringUtils.isEmpty(failReason.toString())) {
                update.setFailReason(failReason.toString());
                update.setStatus(3);
                update.setCustomerServiceId(customerServiceId);
                updates.add(update);
            } else {
                successDetails.add(d);
            }
        });
        if (!ObjectUtils.isEmpty(updates)) {
            updateBatchById(updates);
        }
        if (!ObjectUtils.isEmpty(successDetails)) {
            // 异步上传数据
            updateBatchById(successDetails.stream().map(row -> new CustomerRpaRecordDetail().setId(row.getId()).setStatus(1)).collect(Collectors.toList()));
            CompletableFuture.runAsync(() -> {
                // 待处理列表
                dealRpaDetail(deptId, record, successDetails);
                // 更新rpa记录的是否有异常数据值
                List<Long> rpaRecordIds = successDetails.stream().map(CustomerRpaRecordDetail::getRpaRecordId).distinct().collect(Collectors.toList());
                for (Long rpaRecordId : rpaRecordIds) {
                    if (count(new LambdaQueryWrapper<CustomerRpaRecordDetail>().eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId)
                            .ne(CustomerRpaRecordDetail::getStatus, 1)) == 0) {
                        customerRpaRecordMapper.updateById(new CustomerRpaRecord().setId(rpaRecordId).setHasErrorData(Boolean.FALSE));
                    }
                }
            });
        }
    }

    private void dealRpaDetail(Long deptId, CustomerRpaRecord record, List<CustomerRpaRecordDetail> details) {
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        if (record.getOperType() == 1) {
            // 申报
            switch (record.getRpaType()) {
                case 1:
                    // 医社保
                    details.forEach(d -> {
                        if (!Objects.isNull(d.getMedicalDeliverId())) {
                            RpaMedicalSocialInsuranceReportSheet1Data medicalInsuranceReportData = JSONObject.parseObject(d.getContent(), RpaMedicalSocialInsuranceReportSheet1Data.class);
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(d.getMedicalDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(medicalInsuranceReportData.getMedicalReportAmount()) ? null : new BigDecimal(medicalInsuranceReportData.getMedicalReportAmount()));
                            vo.setOverdueAmount(BigDecimal.ZERO);
                            vo.setSupplementAmount(BigDecimal.ZERO);
                            vo.setReportAmount(StringUtils.isEmpty(medicalInsuranceReportData.getMedicalReportAmount()) ? null : new BigDecimal(medicalInsuranceReportData.getMedicalReportAmount()));
                            vo.setReportRemark(medicalInsuranceReportData.getMedicalReportResult());
                            vo.setReportFiles(ObjectUtils.isEmpty(medicalInsuranceReportData.getFiles()) ? Lists.newArrayList() :
                                    medicalInsuranceReportData.getFiles().stream().filter(f -> f.getFileName().contains("医保")).map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(!StringUtils.isEmpty(medicalInsuranceReportData.getMedicalReportResult()) && medicalInsuranceReportData.getMedicalReportResult().contains("成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(SecurityUtils.getUserId());
                            vo.setDeptId(deptId);
                            vo.setIsRpa(true);
                            try {
                                customerDeliverService.remoteReport(vo);
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(2).setFailReason("").setExceptionMsg(""));
                            } catch (ServiceException e) {
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(3).setFailReason(e.getMessage()));
                                log.error("医保申报失败,params:{}", JSONObject.toJSON(vo));
                            }
                        }
                        if (!Objects.isNull(d.getCommonDeliverId())) {
                            RpaMedicalSocialInsuranceReportSheet1Data medicalInsuranceReportData = JSONObject.parseObject(d.getContent(), RpaMedicalSocialInsuranceReportSheet1Data.class);
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(d.getCommonDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(medicalInsuranceReportData.getSocialReportAmount()) ? null : new BigDecimal(medicalInsuranceReportData.getSocialReportAmount()));
                            vo.setOverdueAmount(BigDecimal.ZERO);
                            vo.setSupplementAmount(BigDecimal.ZERO);
                            vo.setReportAmount(StringUtils.isEmpty(medicalInsuranceReportData.getSocialReportAmount()) ? null : new BigDecimal(medicalInsuranceReportData.getSocialReportAmount()));
                            vo.setReportRemark(medicalInsuranceReportData.getSocialReportResult());
                            vo.setReportFiles(ObjectUtils.isEmpty(medicalInsuranceReportData.getFiles()) ? Lists.newArrayList() :
                                    medicalInsuranceReportData.getFiles().stream().filter(f -> f.getFileName().contains("社保")).map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(!StringUtils.isEmpty(medicalInsuranceReportData.getSocialReportResult()) && medicalInsuranceReportData.getSocialReportResult().contains("成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(SecurityUtils.getUserId());
                            vo.setDeptId(deptId);
                            vo.setIsRpa(true);
                            try {
                                customerDeliverService.remoteReport(vo);
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(2).setFailReason("").setExceptionMsg(""));
                            } catch (ServiceException e) {
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(3).setFailReason(e.getMessage()));
                                log.error("社保申报失败,params:{}", JSONObject.toJSON(vo));
                            }
                        }
                    });
                    break;
                case 2:
                    // 个税（工资薪金）
                    details.forEach(d -> {
                        if (!Objects.isNull(d.getCommonDeliverId())) {
                            RpaPersonTaxReportData personTaxReportData = JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class);
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(d.getCommonDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(personTaxReportData.getReportAmount()) ? null : new BigDecimal(personTaxReportData.getReportAmount()));
                            vo.setOverdueAmount(BigDecimal.ZERO);
                            vo.setSupplementAmount(BigDecimal.ZERO);
                            vo.setReportAmount(StringUtils.isEmpty(personTaxReportData.getReportAmount()) ? null : new BigDecimal(personTaxReportData.getReportAmount()));
                            vo.setReportRemark(personTaxReportData.getReportSearchResult());
                            vo.setReportFiles(ObjectUtils.isEmpty(personTaxReportData.getFiles()) ? Lists.newArrayList() :
                                    personTaxReportData.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(!StringUtils.isEmpty(personTaxReportData.getReportSearchResult()) && personTaxReportData.getReportSearchResult().contains("成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(SecurityUtils.getUserId());
                            vo.setDeptId(deptId);
                            vo.setIsRpa(true);
                            try {
                                customerDeliverService.remoteReport(vo);
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(2).setFailReason("").setExceptionMsg(""));
                            } catch (ServiceException e) {
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(3).setFailReason(e.getMessage()));
                                log.error("社保申报失败,params:{}", JSONObject.toJSON(vo));
                            }
                        }
                    });
                    break;
                case 3:
                    // 国税
                    details.forEach(d -> {
                        if (!Objects.isNull(d.getCommonDeliverId())) {
                            RpaCountryTaxReportSheet1Data countryTaxReportSheet1Data = JSONObject.parseObject(d.getContent(), RpaCountryTaxReportSheet1Data.class);
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(d.getCommonDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(countryTaxReportSheet1Data.getReportAmount()) ? null : new BigDecimal(countryTaxReportSheet1Data.getReportAmount()));
                            vo.setOverdueAmount(BigDecimal.ZERO);
                            vo.setSupplementAmount(BigDecimal.ZERO);
                            vo.setTotalTaxAmount(StringUtils.isEmpty(countryTaxReportSheet1Data.getReportAmount()) ? null : new BigDecimal(countryTaxReportSheet1Data.getReportAmount()));
                            vo.setReportRemark(countryTaxReportSheet1Data.getReportResult());
                            vo.setReportFiles(ObjectUtils.isEmpty(countryTaxReportSheet1Data.getFiles()) ? Lists.newArrayList() :
                                    countryTaxReportSheet1Data.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(Objects.equals(countryTaxReportSheet1Data.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(SecurityUtils.getUserId());
                            vo.setDeptId(deptId);
                            vo.setIsRpa(true);
                            try {
                                customerDeliverService.remoteReport(vo);
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(2).setFailReason("").setExceptionMsg(""));
                            } catch (ServiceException e) {
                                updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                        .setCustomerServiceId(d.getCustomerServiceId())
                                        .setStatus(3).setFailReason(e.getMessage()));
                                log.error("社保申报失败,params:{}", JSONObject.toJSON(vo));
                            }
                        }
                    });
                    break;
                default:
                    break;
            }
        } else if (record.getOperType() == 2) {
            // 扣款
            if (record.getRpaType() == 3) {
                details.forEach(d -> {
                    if (!Objects.isNull(d.getCommonDeliverId())) {
                        RpaCountryTaxDeductionSheet1Data countryTaxDeductionSheet1Data = JSONObject.parseObject(d.getContent(), RpaCountryTaxDeductionSheet1Data.class);
                        RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                        vo.setId(d.getCommonDeliverId());
                        vo.setDeductionStatus(!StringUtils.isEmpty(countryTaxDeductionSheet1Data.getDeductionResult()) && countryTaxDeductionSheet1Data.getDeductionResult().contains("成功") ? 1 : 2);
                        vo.setDeductionRemark(countryTaxDeductionSheet1Data.getDeductionResult());
                        vo.setDeductionFiles(ObjectUtils.isEmpty(countryTaxDeductionSheet1Data.getFiles()) ? Lists.newArrayList() :
                                countryTaxDeductionSheet1Data.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                        vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                        vo.setUserId(SecurityUtils.getUserId());
                        vo.setDeptId(deptId);
                        try {
                            customerDeliverService.remoteDeduction(vo);
                            updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                    .setCustomerServiceId(d.getCustomerServiceId())
                                    .setStatus(2).setFailReason("").setExceptionMsg(""));
                        } catch (ServiceException e) {
                            updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                    .setCustomerServiceId(d.getCustomerServiceId())
                                    .setStatus(3).setFailReason(e.getMessage()));
                            log.error("国税扣款失败,params:{}", JSONObject.toJSON(vo));
                        }
                    }
                });
            }
        } else if (record.getOperType() == 3) {
            if (record.getRpaType() == 4) {
                // 预认证
            } else if (record.getRpaType() == 5) {
                // 收入
                details.forEach(d -> {
                    if (!Objects.isNull(d.getCustomerServiceId())) {
                        RpaCustomerPeriodIncomeSheet1Data customerPeriodIncomeSheet1Data = JSONObject.parseObject(d.getContent(), RpaCustomerPeriodIncomeSheet1Data.class);
                        customerServicePeriodMonthIncomeService.save(new CustomerServicePeriodMonthIncome().setCustomerServiceId(d.getCustomerServiceId())
                                .setCustomerName(d.getCustomerName())
                                .setCustomerTaxNumber(d.getCreditCode())
                                .setPeriod(d.getPeriod())
                                .setAllTicketAmount(StringUtils.isEmpty(customerPeriodIncomeSheet1Data.getAllTicketAmount()) ? null: new BigDecimal(customerPeriodIncomeSheet1Data.getAllTicketAmount()))
                                .setAllTicketTaxAmount(StringUtils.isEmpty(customerPeriodIncomeSheet1Data.getAllTicketTaxAmount()) ? null : new BigDecimal(customerPeriodIncomeSheet1Data.getAllTicketTaxAmount()))
                                .setTicketSearchAmount(StringUtils.isEmpty(customerPeriodIncomeSheet1Data.getTicketSearchAmount()) ? null : new BigDecimal(customerPeriodIncomeSheet1Data.getTicketSearchAmount()))
                                .setTicketSearchTaxAmount(StringUtils.isEmpty(customerPeriodIncomeSheet1Data.getTicketSearchTaxAmount()) ? null : new BigDecimal(customerPeriodIncomeSheet1Data.getTicketSearchTaxAmount()))
                                .setTicketTime(Objects.isNull(customerPeriodIncomeSheet1Data.getStartTime()) ? null : DateUtils.dateToLocaldateTime(customerPeriodIncomeSheet1Data.getStartTime())));
                        updateById(new CustomerRpaRecordDetail().setId(d.getId())
                                .setCustomerServiceId(d.getCustomerServiceId())
                                .setStatus(2).setFailReason("").setExceptionMsg(""));
                    }
                });
            }
        } else if (record.getOperType() == 4) {
            List<RemoteBatchDeliverCheckResultVO> dataList = Lists.newArrayList();
            switch(record.getRpaType()) {
                case 2:
                    // 个税（工资薪金）
                    details.forEach(d -> {
                        if (!Objects.isNull(d.getCommonDeliverId())) {
                            RpaPersonTaxReportData personTaxCheckResult = JSONObject.parseObject(d.getContent(), RpaPersonTaxReportData.class);
                            dataList.add(RemoteBatchDeliverCheckResultVO.builder()
                                    .id(d.getCommonDeliverId())
                                    .checkFiles(ObjectUtils.isEmpty(personTaxCheckResult.getFiles()) ? Lists.newArrayList() :
                                            personTaxCheckResult.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList())).build());
                        }
                    });
                    break;
                case 3:
                    // 国税
                    details.forEach(d -> {
                        if (!Objects.isNull(d.getCommonDeliverId())) {
                            RpaCountryTaxCheckResultSheetData countryaxCheckResult = JSONObject.parseObject(d.getContent(), RpaCountryTaxCheckResultSheetData.class);
                            dataList.add(RemoteBatchDeliverCheckResultVO.builder()
                                    .id(d.getCommonDeliverId())
                                    .checkFiles(ObjectUtils.isEmpty(countryaxCheckResult.getFiles()) ? Lists.newArrayList() :
                                            countryaxCheckResult.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList())).build());
                        }
                    });
                    break;
                default:
                    break;
            }
            if (!ObjectUtils.isEmpty(dataList)) {
                customerDeliverService.batchDeliverCheckResult(RemoteCustomerDeliverCheckResultVO.builder()
                                .dataList(dataList)
                                .deptId(deptId)
                                .userId(userId)
                        .build());
            }
        }
    }

    @Override
    public List<CustomerRpaRecordDetail> selectFailByRpaRecordId(Long rpaRecordId) {
        if (Objects.isNull(rpaRecordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId)
                .eq(CustomerRpaRecordDetail::getStatus, 3));
    }
}
