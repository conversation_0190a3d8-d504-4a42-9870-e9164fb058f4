package com.bxm.customer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "special.user")
@Getter
@Setter
public class SpecialUserIdIdProperties
{
    private Long xtzlzg;

    private Long xtzl;
}
