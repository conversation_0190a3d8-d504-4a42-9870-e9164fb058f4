package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.TaxType;
import com.bxm.customer.domain.NewCustomerTaxTypeCheck;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerServiceTaxTypeCheckVO;
import com.bxm.customer.mapper.NewCustomerTaxTypeCheckMapper;
import com.bxm.customer.service.INewCustomerTaxTypeCheckService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.reactive.TransactionalOperator;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新户流转税种核定Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerTaxTypeCheckServiceImpl extends ServiceImpl<NewCustomerTaxTypeCheckMapper, NewCustomerTaxTypeCheck> implements INewCustomerTaxTypeCheckService
{
    @Autowired
    private NewCustomerTaxTypeCheckMapper newCustomerTaxTypeCheckMapper;

    /**
     * 查询新户流转税种核定
     * 
     * @param id 新户流转税种核定主键
     * @return 新户流转税种核定
     */
    @Override
    public NewCustomerTaxTypeCheck selectNewCustomerTaxTypeCheckById(Long id)
    {
        return newCustomerTaxTypeCheckMapper.selectNewCustomerTaxTypeCheckById(id);
    }

    /**
     * 查询新户流转税种核定列表
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 新户流转税种核定
     */
    @Override
    public List<NewCustomerTaxTypeCheck> selectNewCustomerTaxTypeCheckList(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck)
    {
        return newCustomerTaxTypeCheckMapper.selectNewCustomerTaxTypeCheckList(newCustomerTaxTypeCheck);
    }

    /**
     * 新增新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    @Override
    public int insertNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck)
    {
        return newCustomerTaxTypeCheckMapper.insertNewCustomerTaxTypeCheck(newCustomerTaxTypeCheck);
    }

    /**
     * 修改新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    @Override
    public int updateNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck)
    {
        return newCustomerTaxTypeCheckMapper.updateNewCustomerTaxTypeCheck(newCustomerTaxTypeCheck);
    }

    /**
     * 批量删除新户流转税种核定
     * 
     * @param ids 需要删除的新户流转税种核定主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerTaxTypeCheckByIds(Long[] ids)
    {
        return newCustomerTaxTypeCheckMapper.deleteNewCustomerTaxTypeCheckByIds(ids);
    }

    /**
     * 删除新户流转税种核定信息
     * 
     * @param id 新户流转税种核定主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerTaxTypeCheckById(Long id)
    {
        return newCustomerTaxTypeCheckMapper.deleteNewCustomerTaxTypeCheckById(id);
    }

    @Override
    public List<NewCustomerServiceTaxTypeCheckVO> selectCustomerTaxTypeCheckByNewCustomerTransferId(Long newCustomerTransferId, Integer taxType) {
        if (Objects.isNull(newCustomerTransferId)) {
            return Collections.emptyList();
        }
        List<NewCustomerTaxTypeCheck> customerTaxTypeCheckList = selectByNewCustomerTransferId(newCustomerTransferId);
        if (ObjectUtils.isEmpty(customerTaxTypeCheckList)) {
            if (Objects.equals(taxType, TaxType.COMMONLY.getCode())) {
                customerTaxTypeCheckList = selectByNewCustomerTransferId(-1L);
            } else {
                customerTaxTypeCheckList = selectByNewCustomerTransferId(0L);
            }
        }
        if (ObjectUtils.isEmpty(customerTaxTypeCheckList)) {
            return Collections.emptyList();
        }
        return customerTaxTypeCheckList.stream().map(row -> NewCustomerServiceTaxTypeCheckVO.builder()
                .id(row.getId())
                .taxTypeName(row.getTaxType())
                .reportType(row.getReportType())
                .newCustomerTransferId(row.getCustomerId())
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<NewCustomerTaxTypeCheck> selectByNewCustomerTransferId(Long newCustomerTransferId) {
        if (Objects.isNull(newCustomerTransferId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<NewCustomerTaxTypeCheck>().eq(NewCustomerTaxTypeCheck::getCustomerId, newCustomerTransferId)
                .orderByAsc(NewCustomerTaxTypeCheck::getReportType).orderByDesc(NewCustomerTaxTypeCheck::getId));
    }

    @Override
    @Transactional
    public void removeAndCreateTaxTypeCheck(Long newCustomerTransferId, List<NewCustomerServiceTaxTypeCheckVO> taxTypeCheckList) {
        baseMapper.delete(new LambdaQueryWrapper<NewCustomerTaxTypeCheck>()
                .eq(NewCustomerTaxTypeCheck::getCustomerId, newCustomerTransferId));
        if (!ObjectUtils.isEmpty(taxTypeCheckList)) {
            saveBatch(taxTypeCheckList.stream().map(taxTypeCheck -> {
                NewCustomerTaxTypeCheck newCustomerTaxTypeCheck = new NewCustomerTaxTypeCheck();
                BeanUtils.copyProperties(taxTypeCheck, newCustomerTaxTypeCheck);
                newCustomerTaxTypeCheck.setCustomerId(newCustomerTransferId);
                newCustomerTaxTypeCheck.setId(null);
                newCustomerTaxTypeCheck.setCreateTime(null);
                newCustomerTaxTypeCheck.setUpdateTime(null);
                newCustomerTaxTypeCheck.setTaxType(taxTypeCheck.getTaxTypeName());
                return newCustomerTaxTypeCheck;
            }).collect(Collectors.toList()));
        }
    }
}
