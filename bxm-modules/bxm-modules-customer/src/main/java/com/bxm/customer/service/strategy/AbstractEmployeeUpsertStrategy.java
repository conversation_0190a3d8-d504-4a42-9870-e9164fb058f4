package com.bxm.customer.service.strategy;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.IValueAddedEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * 增值员工upsert策略抽象基类
 *
 * 提供通用的 upsert 流程实现，子类只需实现业务特定的验证和处理逻辑。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
public abstract class AbstractEmployeeUpsertStrategy implements ValueAddedEmployeeUpsertStrategy {

    @Autowired
    protected IValueAddedEmployeeService valueAddedEmployeeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upsert(ValueAddedEmployeeVO employeeVO) {
        try {
            log.info("Starting upsert operation for employee: {}, bizType: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType());

            // 1. 业务特定字段验证
            validateBusinessFields(employeeVO);

            // 2. VO转换为DO
            ValueAddedEmployee employee = convertVoToEntity(employeeVO);

            // 3. 数据预处理
            preprocessEmployee(employee);

            // 4. 查找现有记录
            ValueAddedEmployee existingEmployee = findExistingEmployee(employee);

            boolean isUpdate = existingEmployee != null;
            ValueAddedEmployee targetEmployee;

            if (isUpdate) {
                // 更新操作：合并数据
                targetEmployee = mergeEmployee(existingEmployee, employee);

                // 执行更新
                if (!valueAddedEmployeeService.updateById(targetEmployee)) {
                    throw new RuntimeException("Failed to update employee: " + targetEmployee.getId());
                }
                log.info("Employee updated successfully: ID={}, Name={}",
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            } else {
                // 插入操作
                targetEmployee = employee;
                // 执行插入
                if (!valueAddedEmployeeService.save(targetEmployee)) {
                    throw new RuntimeException("Failed to insert employee: " + targetEmployee.getEmployeeName());
                }
                log.info("Employee inserted successfully: ID={}, Name={}",
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            }

            return targetEmployee.getId();

        } catch (IllegalArgumentException e) {
            log.error("Validation failed during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("Runtime error during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage(), e);
            throw new RuntimeException("Upsert operation failed for employee " + employeeVO.getEmployeeName() + ": " + e.getMessage(), e);
        }
    }

    /**
     * VO转换为Entity
     *
     * @param employeeVO 员工信息VO
     * @return 员工信息Entity
     */
    protected ValueAddedEmployee convertVoToEntity(ValueAddedEmployeeVO employeeVO) {
        ValueAddedEmployee employee = new ValueAddedEmployee();
        BeanUtils.copyProperties(employeeVO, employee);

        // 设置业务类型
        employee.setBizType(getSupportedBizType());

        return employee;
    }


    /**
     * 验证员工信息的业务特定字段
     * 子类必须实现此方法
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当验证失败时抛出
     */
    protected abstract void validateBusinessFields(ValueAddedEmployeeVO employeeVO);

    /**
     * 预处理员工信息
     * 子类可以重写此方法实现特定的预处理逻辑
     *
     * @param employee 员工信息Entity
     */
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 默认空实现，子类可以重写
    }

    /**
     * 查找现有员工记录
     * 子类必须实现此方法
     *
     * @param employee 员工信息Entity
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected abstract ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee);

    /**
     * 合并员工信息
     * 子类可以重写此方法实现特定的合并逻辑
     *
     * @param existing 现有员工记录
     * @param newEmployee 新的员工信息
     * @return 合并后的员工信息
     */
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 使用BeanUtils复制基础字段，但排除ID和创建时间等不应被覆盖的字段
        String[] ignoreProperties = {"id", "createTime", "createBy"};
        BeanUtils.copyProperties(newEmployee, existing, ignoreProperties);
        return existing;
    }

    /**
     * 根据身份证号查找现有员工记录
     * 通用的查找方法，子类可以直接使用
     *
     * @param employee 员工信息
     * @param bizType 业务类型
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected ValueAddedEmployee findExistingEmployeeByIdNumber(ValueAddedEmployee employee, Integer bizType) {
        return valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                employee.getDeliveryOrderNo(),
                employee.getIdNumber(),
                bizType
        );
    }

}
