package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.WorkOrderAccountingCashierRelation;

/**
 * 工单账务交付单关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
@Mapper
public interface WorkOrderAccountingCashierRelationMapper extends BaseMapper<WorkOrderAccountingCashierRelation>
{
    /**
     * 查询工单账务交付单关联
     * 
     * @param id 工单账务交付单关联主键
     * @return 工单账务交付单关联
     */
    public WorkOrderAccountingCashierRelation selectWorkOrderAccountingCashierRelationById(Long id);

    /**
     * 查询工单账务交付单关联列表
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 工单账务交付单关联集合
     */
    public List<WorkOrderAccountingCashierRelation> selectWorkOrderAccountingCashierRelationList(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 新增工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    public int insertWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 修改工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    public int updateWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 删除工单账务交付单关联
     * 
     * @param id 工单账务交付单关联主键
     * @return 结果
     */
    public int deleteWorkOrderAccountingCashierRelationById(Long id);

    /**
     * 批量删除工单账务交付单关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderAccountingCashierRelationByIds(Long[] ids);
}
