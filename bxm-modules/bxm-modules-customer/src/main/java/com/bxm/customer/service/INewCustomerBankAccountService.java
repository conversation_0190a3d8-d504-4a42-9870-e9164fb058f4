package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerBankAccount;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferBankAccountDTO;

/**
 * 新户流转银行账号Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerBankAccountService extends IService<NewCustomerBankAccount>
{
    /**
     * 查询新户流转银行账号
     * 
     * @param id 新户流转银行账号主键
     * @return 新户流转银行账号
     */
    public NewCustomerBankAccount selectNewCustomerBankAccountById(Long id);

    /**
     * 查询新户流转银行账号列表
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 新户流转银行账号集合
     */
    public List<NewCustomerBankAccount> selectNewCustomerBankAccountList(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 新增新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    public int insertNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 修改新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    public int updateNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 批量删除新户流转银行账号
     * 
     * @param ids 需要删除的新户流转银行账号主键集合
     * @return 结果
     */
    public int deleteNewCustomerBankAccountByIds(Long[] ids);

    /**
     * 删除新户流转银行账号信息
     * 
     * @param id 新户流转银行账号主键
     * @return 结果
     */
    public int deleteNewCustomerBankAccountById(Long id);

    List<NewCustomerBankAccount> selectByNewCustomerId(Long newCustomerId);

    Integer countByNewCustomerId(Long newCustomerId);

    Map<Long, List<NewCustomerBankAccount>> selectMapByNewCustomerIds(List<Long> newCustomerIds);

    void deleteCustomerServiceBankAccount(Long id);

    void addNewCustomerTransferBankAccount(NewCustomerTransferBankAccountDTO vo, NewCustomerInfo newCustomerInfo);

    void modifyNewCustomerTransferAccount(NewCustomerTransferBankAccountDTO vo, NewCustomerInfo newCustomerInfo);

    Boolean checkBankAccountExists(Long businessTopDeptId, String account, Long id, String creditCode, Long newCustomerTransferId);

    void deleteByNewCustomerId(Long newCustomerTransferId);

    void removeAndCreateBankAccount(Long newCustomerTransferId, List<NewCustomerTransferBankAccountDTO> bankList);
}
