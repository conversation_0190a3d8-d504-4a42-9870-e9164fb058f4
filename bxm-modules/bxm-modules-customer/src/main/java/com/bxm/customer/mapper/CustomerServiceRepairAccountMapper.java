package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.repairAccount.RepairAccountDTO;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.repairAccount.RepairAccountVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccount;
import org.apache.ibatis.annotations.Param;

/**
 * 补账Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceRepairAccountMapper extends BaseMapper<CustomerServiceRepairAccount> {
    /**
     * 查询补账
     *
     * @param id 补账主键
     * @return 补账
     */
    public CustomerServiceRepairAccount selectCustomerServiceRepairAccountById(Long id);

    /**
     * 查询补账列表
     *
     * @param customerServiceRepairAccount 补账
     * @return 补账集合
     */
    public List<CustomerServiceRepairAccount> selectCustomerServiceRepairAccountList(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 新增补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    public int insertCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 修改补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    public int updateCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 删除补账
     *
     * @param id 补账主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountById(Long id);

    /**
     * 批量删除补账
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountByIds(Long[] ids);

    List<Long> searchAccounting(@Param("accountingEmployee") String accountingEmployee);

    List<Long> searchDeliverStatus(@Param("deliverStatus") Integer deliverStatus);

    // 查询补账待分派数量
    Long countWaitDispatchByDeptAndStatus(@Param("userDept") UserDeptDTO userDept,
                                          @Param("status") Integer status);

    List<RepairAccountDTO> selectRepairAccountList(
            IPage<RepairAccountDTO> result,
            @Param("vo") RepairAccountVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("commonIdsSearchVO") CommonIdsSearchVO commonIdsSearchVO,
            @Param("userDept") UserDeptDTO userDept
    );
}
