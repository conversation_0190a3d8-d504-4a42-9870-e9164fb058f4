package com.bxm.customer.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.utils.uuid.Seq;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.customize.annotation.TimeParamHandler;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteMaterialDeliver;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.materialDeliver.*;
import com.bxm.customer.domain.vo.materialDeliver.*;
import com.bxm.customer.service.*;


import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipFile;

/**
 * 材料交接单Controller
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Slf4j
@RestController
@RequestMapping("/material/deliver")
@Api(tags = "材料交接单")
public class MaterialDeliverController extends BaseController
{

    //private static final Logger log = LoggerFactory.getLogger(MaterialDeliverController.class);
    @Autowired
    private IMaterialDeliverService materialDeliverService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;
    @Autowired
    private CuOssService cuOssService;

    @Autowired
    private CuMaterialDeliverService cuMaterialDeliverService;

    @Autowired
    private CuCustomerServicePeriodMonthService cuCustomerServicePeriodMonthService;


    @Autowired
    private CuMaterialDeliverFileInventoryService cuMaterialDeliverFileInventoryService;

    @Autowired
    private CuCustomerServiceService cuCustomerServiceService;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;


    /**
     * 上传交接单文件
     * @param file
     * @param fileType 1：材料单Excel文件 2：材料单压缩包 只支持zip，不支持rar
     * @return
     */
    @PostMapping("/materialUploadFile")
//    @ApiOperation("上传交接单文件")
    @TimeParamHandler
    @ApiIgnore
     public Result<String> materialUploadFile(@RequestParam("file") MultipartFile file, @RequestParam(value = "fileType", defaultValue = "1") int fileType){



        try {

            // 设置文件保存路径
            String uploadDir = System.getProperty("user.dir") + "/uploadtmp"; // 获取当前应用程序的根目录路径，并附加 /uploadtmp

            // 确保上传目录存在
            File directory = new File(uploadDir);
            if (!directory.exists()) {
                directory.mkdirs(); // 如果目录不存在则创建
            }

            // 检查文件是否为空
            if (file == null || file.isEmpty()) {
                return Result.fail(100008, "上传的文件为空");
            }


            // 获取文件扩展名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return Result.fail(100009, "文件名不能为空");
            }

            // 获取文件扩展名（例如 .jpg, .png）
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));

            // 生成唯一的文件名
            String uniqueFileName = Seq.getId("UPLOAD") + fileExtension;


            // 保存文件
            File uploadedFile = new File(uploadDir, uniqueFileName);
            file.transferTo(uploadedFile);
            String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");
            if (1 == fileType) {
                cuOssService.uploadExcelFile(uploadedFile,"MaterialDeliver/"+yearMonth+"/"+ uploadedFile.getName());
            }else {
                cuOssService.uploadAndUnzip(uploadedFile,"MaterialDeliver/"+yearMonth);
            }

            ZipSecureFile.setMinInflateRatio(-1.0d);

           /* if (fileType != 1 && fileType != 2) {
                return Result.fail(100006, "文件类型错误");
            }*/

            return Result.ok(uniqueFileName);


        }catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return Result.fail(100007, "文件上传失败"+e.getMessage());
        } catch (Exception e) {
            log.error("未知错误: {}", e.getMessage(), e);
            return Result.fail(100007, "文件上传失败"+e.getMessage());
        }



    }


    /**
     * 材料单的数据校验，并返回校验结果
     * @param deptId 提交组织ID
     * @param excelFileName 上传的Excel文件名，文件名带后缀，上传接口返回的名称
     * @param zipFileName 上传的Zip文件名，文件名带后缀，上传接口返回的名称
     * @param materialDeliverType 交接单类型，1-银行流水，2-普通入账，3-凭票入账
     * @return
     */
    @GetMapping("/materialDeliverDataCheck")
//    @ApiOperation("材料单的数据校验")
    @TimeParamHandler
    @ApiIgnore
    public Result<MaterialCheckResult> materialDeliverDataCheck(@RequestParam("dept_id") Long deptId,
                                                                @RequestParam("excelfilename") String excelFileName,
                                                                @RequestParam("zipfilename") String zipFileName,
                                                                @RequestParam("materialDeliverType") Integer materialDeliverType
                                                          ) {

        MaterialCheckResult materialCheckResult=new MaterialCheckResult();

        String UPLOAD_DIR = System.getProperty("user.dir") + "/uploadtmp/";

        String excelFilePath=UPLOAD_DIR+excelFileName;

        File file = new File(excelFilePath);
        if (!file.exists()) {
            return Result.fail("Excel 文件错误：文件不存在") ;
        }

        try (InputStream inputStream = new FileInputStream(excelFilePath);
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return Result.fail("Excel文件没有有效的数据");
            }

            // 添加表头
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                Cell statusHeaderCell = headerRow.createCell(headerRow.getLastCellNum());
                statusHeaderCell.setCellValue("状态"); // 设置表头
            }

            // 获取文件名列的值
            Set<String> uniqueNames = new HashSet<>();
            List<String> duplicateNames = new ArrayList<>();

            for (int i = 1; i <= sheet.getPhysicalNumberOfRows()-1; i++) { // 从第2行开始读取数据
                Row row = sheet.getRow(i);
                if (row == null || row.getCell(0) == null) {
                    continue; // 跳过空行
                }
                String fileName = row.getCell(0).getStringCellValue().trim();

                if (!uniqueNames.add(fileName)) {
                    duplicateNames.add(fileName); // 发现重复文件名
                }
            }




            String zipFilePath=UPLOAD_DIR+zipFileName;

            File zipfile = new File(zipFilePath);
            if (!zipfile.exists()) {
                return Result.fail("Zip 文件错误：文件不存在") ;
            }


            Set<String> fileNames = new HashSet<>();
            ZipFile zipFile = new ZipFile(file);
            zipFile.stream().forEach(entry -> {
                if (!entry.isDirectory()) {
                    fileNames.add(entry.getName());
                }
            });

            if (fileNames.isEmpty()) {
                return Result.fail("Zip 文件错误：读取失败");
            }

            // 检查 Excel 中的文件名是否在 Zip 中
            List<String> missingFiles = new ArrayList<>();
            for (String fileName : uniqueNames) {
                if (!fileNames.contains(fileName)) {
                    missingFiles.add(fileName);
                }
            }




            // 第二遍，标注重复状态
            for (int i = 1; i <= sheet.getPhysicalNumberOfRows()-1; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell fileNameCell = row.getCell(0); // 第一列为文件名
                    if (fileNameCell != null) {
                        String fileName = fileNameCell.getStringCellValue().trim();
                        // 在最后一列标注“重复”或“唯一”
                        Cell statusCell = row.createCell(headerRow.getLastCellNum() - 1); // 在新增列标注
                        if (duplicateNames.contains(fileName)) {
                            if (missingFiles.contains(fileName)){
                                statusCell.setCellValue("重复，文件不存在");
                                //missingFilesCount++;
                            }else {
                                statusCell.setCellValue("重复，文件存在");
                            }

                        } else {
                            if (missingFiles.contains(fileName)){
                                statusCell.setCellValue("唯一，文件不存在");
                                //missingFilesCount++;
                            }else {
                                statusCell.setCellValue("唯一，文件存在");
                            }

                        }
                    }
                }
            }

            // 保存修改后的 Excel 文件
            FileOutputStream fileOutputStream = new FileOutputStream(excelFilePath);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            workbook.close();
            inputStream.close();




            materialCheckResult.setTotalCount(sheet.getPhysicalNumberOfRows()-1);

            materialCheckResult.setDuplicateNamesCount(duplicateNames.size()*2);
            materialCheckResult.setMissingFilesCount(missingFiles.size());

            materialCheckResult.setSuccessCount(sheet.getPhysicalNumberOfRows()-1-duplicateNames.size()*2-missingFiles.size());
            materialCheckResult.setErrorCount(missingFiles.size()+duplicateNames.size()*2);

            if (materialCheckResult.getErrorCount()>materialCheckResult.getTotalCount()){
                materialCheckResult.setErrorCount(materialCheckResult.getTotalCount());
            }

            if (materialCheckResult.getSuccessCount()<0){
                materialCheckResult.setSuccessCount(0);
            }

            materialCheckResult.setExcelDownloadUrl("/uploadtmp/"+excelFileName);


        } catch (Exception e) {
            return Result.fail("解析文件时出错：" + e.getMessage());
        }

        return Result.ok(materialCheckResult);
    }



    /**
     * 材料单的提交并往出解析
     * @param deptId 提交组织ID
     * @param excelFileName 上传的Excel文件名，文件名带后缀，上传接口返回的名称
     * @param zipFileName 上传的Zip文件名，文件名带后缀，上传接口返回的名称
     * @param materialDeliverType 交接单类型，1-银行流水，2-普通入账，3-凭票入账
     * @param deptId 小组id
     * @return
     */
    @GetMapping("/materialDeliverAdd")
//    @ApiOperation("材料单的提交并往出解析")
    @TimeParamHandler
    @ApiIgnore
    public Result<String> materialDeliverAdd(@RequestParam("dept_id") Long deptId,
                                                          @RequestParam("excelfilename") String excelFileName,
                                                          @RequestParam("zipfilename") String zipFileName,
                                                          @RequestParam("materialDeliverType") Integer materialDeliverType,
                                                          @RequestHeader("deptId") Long deptId2){


        String UPLOAD_DIR = System.getProperty("user.dir") + "/uploadtmp/";

        String excelFilePath=UPLOAD_DIR+excelFileName;

        MaterialDeliver materialDeliver=new MaterialDeliver();
        materialDeliver.setMaterialDeliverNumber(Seq.getId("UPLOAD"));
        materialDeliver.setMaterialDeliverType(materialDeliverType);
        materialDeliver.setAnalysisStatus(2);
        materialDeliver.setAnalysisResult(1);
        materialDeliver.setPushStatus(1);
        materialDeliver.setCommitDeptId(deptId);
        materialDeliver.setCommitUserId(deptId2);
        materialDeliver.setIsDel(false);

        //List<MaterialDeliverFile> materialDeliverFileList= Lists.newArrayList();
        List<MaterialDeliverFileInventory> materialDeliverFileInventoryArrayList= Lists.newArrayList();

        File file = new File(excelFilePath);
        if (!file.exists()) {
            return Result.fail("Excel 文件错误：文件不存在") ;
        }


        try (InputStream inputStream = new FileInputStream(excelFilePath);
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return Result.fail("Excel文件没有有效的数据");
            }



            if (materialDeliverType==1){
                // 银行流水
                for (int i = 1; i <= sheet.getPhysicalNumberOfRows()-1; i++) { // 从第2行开始读取数据
                    boolean isValid = true; // 默认认为数据是完整的


                    MaterialDeliverFileInventory materialDeliverFileInventory=new MaterialDeliverFileInventory();
                    materialDeliverFileInventory.setMaterialDeliverId(materialDeliver.getId());
                    materialDeliverFileInventory.setIsDel(false);
                    materialDeliverFileInventory.setMaterialFileName("");
                    materialDeliverFileInventory.setFileName("");
                    materialDeliverFileInventory.setFileUrl("");
                    materialDeliverFileInventory.setFileSize(0L);
                    materialDeliverFileInventory.setCustomerName("");
                    materialDeliverFileInventory.setCreditCode("");
                    materialDeliverFileInventory.setBankName("");




                    Row row = sheet.getRow(i);
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(0));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(1));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(2));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(3));

                    String tmpstr="";



                    if(row.getCell(3)!=null && row.getCell(3).getCellType()!= CellType.STRING){

                        // 如果是数值类型，强制转换为字符串并去掉科学计数法
                        String numericValue = String.valueOf(row.getCell(3).getNumericCellValue());
                        // 可能会存在科学计数法格式的情况，处理这种情况
                        if (numericValue.contains("E")) {
                            // 防止科学计数法，转换为标准字符串
                            DecimalFormat df = new DecimalFormat("0");
                            numericValue= df.format(row.getCell(3).getNumericCellValue());
                        }

                       tmpstr = numericValue;
                    }else {
                        tmpstr = row.getCell(3).getStringCellValue();
                    }

                    //isValid=CuMaterialDeliverService.isValidDate(tmpstr);

                    if (!isValid) {
                        //materialDeliverFile.setFileName(row.getCell(0).getStringCellValue().trim());
                        //materialDeliverFileInventory.setBankAccountNumber(row.getCell(2).getStringCellValue().trim());
                        materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());

                        materialDeliverFileInventory.setErrorMsg("数据不完整");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);

                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);

                        continue;

                    }


                    materialDeliverFileInventory.setBankAccountNumber(row.getCell(2).getStringCellValue().trim());
                    materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setPeriod(tmpstr);

                    String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");
                    materialDeliverFileInventory.setFileUrl("/bxm410/MaterialDeliver/"+yearMonth+"/"+materialDeliverFileInventory.getMaterialFileName());
                    materialDeliverFileInventory.setCustomerName(row.getCell(1).getStringCellValue().trim());


                    CustomerServiceBankAccount customerServiceBankAccount= cuMaterialDeliverService.findByBankAccountNumber(row.getCell(2).getStringCellValue().trim());

                    if (customerServiceBankAccount==null){
                        isValid=false;
                        materialDeliverFileInventory.setErrorMsg("银行账号不存在");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }else {
                        materialDeliverFileInventory.setBankName(customerServiceBankAccount.getBankName());
                        materialDeliverFileInventory.setCustomerServiceId(customerServiceBankAccount.getCustomerServiceId());

                        isValid= cuCustomerServicePeriodMonthService.checkRecordExists(customerServiceBankAccount.getCustomerServiceId(), Integer.valueOf(tmpstr.trim()));

                        if (!isValid){

                            materialDeliverFileInventory.setErrorMsg("账期不存在");
                            materialDeliverFileInventory.setAnalysisResult(2);
                            materialDeliver.setAnalysisStatus(2);
                            materialDeliver.setAnalysisResult(2);

                            materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                            continue;
                        }else {
                            materialDeliverFileInventory.setAnalysisResult(1);

                            isValid=cuMaterialDeliverFileInventoryService.checkRecordExists(materialDeliverFileInventory.getMaterialFileName(),materialDeliverFileInventory.getPeriod(),customerServiceBankAccount.getCustomerServiceId());

                            if (!isValid){

                                materialDeliverFileInventory.setErrorMsg("疑似重复");
                                materialDeliverFileInventory.setAnalysisResult(2);
                                materialDeliver.setAnalysisStatus(2);
                                materialDeliver.setAnalysisResult(2);
                                materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                                continue;
                            }else {
                                materialDeliverFileInventory.setAnalysisResult(1);
                                materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                                continue;
                            }

                        }

                    }





                }

            }else if (materialDeliverType==2){
                // 普通入账

                for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 从第2行开始读取数据
                    boolean isValid = true; // 默认认为数据是完整的


                    MaterialDeliverFileInventory materialDeliverFileInventory=new MaterialDeliverFileInventory();
                    materialDeliverFileInventory.setMaterialDeliverId(materialDeliver.getId());
                    materialDeliverFileInventory.setIsDel(false);
                    materialDeliverFileInventory.setMaterialFileName("");
                    materialDeliverFileInventory.setFileName("");
                    materialDeliverFileInventory.setFileUrl("");
                    materialDeliverFileInventory.setFileSize(0L);
                    materialDeliverFileInventory.setCustomerName("");
                    materialDeliverFileInventory.setCreditCode("");
                    materialDeliverFileInventory.setBankName("");
                    materialDeliverFileInventory.setBankAccountNumber("");




                    Row row = sheet.getRow(i);
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(0));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(1));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(2));

                    if (!isValid) {

                        materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());

                        materialDeliverFileInventory.setErrorMsg("数据不完整");
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventory.setAnalysisResult(2);

                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);

                        continue;

                    }

                    CCustomerService cCustomerService= cuCustomerServiceService.getCustomerByCreditCode(row.getCell(2).getStringCellValue().trim());

                    if (cCustomerService==null){
                        isValid=false;
                        materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());
                        materialDeliverFileInventory.setErrorMsg("客户不存在");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }


                    String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");

                    materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setPeriod(yearMonth);


                    materialDeliverFileInventory.setFileUrl("/bxm410/MaterialDeliver/"+yearMonth+"/"+materialDeliverFileInventory.getMaterialFileName());
                    materialDeliverFileInventory.setCustomerName(row.getCell(1).getStringCellValue().trim());

                    materialDeliverFileInventory.setAnalysisResult(1);



                    isValid=cuMaterialDeliverFileInventoryService.checkRecordExists(materialDeliverFileInventory.getMaterialFileName(),materialDeliverFileInventory.getPeriod(),cCustomerService.getId());

                    if (!isValid){

                        materialDeliverFileInventory.setErrorMsg("疑似重复");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }else {
                        materialDeliverFileInventory.setAnalysisResult(1);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }


                }


            }else if (materialDeliverType==3){
                // 凭票入账


                for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 从第2行开始读取数据
                    boolean isValid = true; // 默认认为数据是完整的


                    MaterialDeliverFileInventory materialDeliverFileInventory=new MaterialDeliverFileInventory();
                    materialDeliverFileInventory.setMaterialDeliverId(materialDeliver.getId());
                    materialDeliverFileInventory.setIsDel(false);
                    materialDeliverFileInventory.setMaterialFileName("");
                    materialDeliverFileInventory.setFileName("");
                    materialDeliverFileInventory.setFileUrl("");
                    materialDeliverFileInventory.setFileSize(0L);
                    materialDeliverFileInventory.setCustomerName("");
                    materialDeliverFileInventory.setCreditCode("");
                    materialDeliverFileInventory.setBankName("");
                    materialDeliverFileInventory.setBankAccountNumber("");




                    Row row = sheet.getRow(i);
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(0));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(1));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(2));
                    isValid=CuMaterialDeliverService.isValidCell(row.getCell(3));

                    String tmpstr="";

                    if(row.getCell(3)!=null && row.getCell(3).getCellType()!= CellType.STRING){
                        // 如果是数值类型，强制转换为字符串并去掉科学计数法
                        String numericValue = String.valueOf(row.getCell(3).getNumericCellValue());
                        // 可能会存在科学计数法格式的情况，处理这种情况
                        if (numericValue.contains("E")) {
                            // 防止科学计数法，转换为标准字符串
                            DecimalFormat df = new DecimalFormat("0");
                            numericValue= df.format(row.getCell(3).getNumericCellValue());
                        }

                        tmpstr = numericValue;
                    }else {
                        tmpstr = row.getCell(3).getStringCellValue();
                    }

                    //isValid=CuMaterialDeliverService.isValidDate(tmpstr);

                    if (!isValid) {

                        materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());

                        materialDeliverFileInventory.setErrorMsg("数据不完整");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);

                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);

                        continue;

                    }

                    CCustomerService cCustomerService= cuCustomerServiceService.getCustomerByCreditCode(row.getCell(2).getStringCellValue().trim());

                    if (cCustomerService==null){
                        isValid=false;
                        materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());
                        materialDeliverFileInventory.setErrorMsg("客户不存在");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }


                    String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");

                    materialDeliverFileInventory.setMaterialFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setFileName(row.getCell(0).getStringCellValue().trim());
                    materialDeliverFileInventory.setPeriod(tmpstr);


                    materialDeliverFileInventory.setFileUrl("/bxm410/MaterialDeliver/"+yearMonth+"/"+materialDeliverFileInventory.getMaterialFileName());
                    materialDeliverFileInventory.setCustomerName(row.getCell(1).getStringCellValue().trim());

                    materialDeliverFileInventory.setAnalysisResult(1);


                    isValid= cuCustomerServicePeriodMonthService.checkRecordExists(cCustomerService.getId(), Integer.valueOf(tmpstr.trim()));

                    if (!isValid){
                        materialDeliverFileInventory.setErrorMsg("账期不存在");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }



                    isValid=cuMaterialDeliverFileInventoryService.checkRecordExists(materialDeliverFileInventory.getMaterialFileName(),materialDeliverFileInventory.getPeriod(),cCustomerService.getId());

                    if (!isValid){

                        materialDeliverFileInventory.setErrorMsg("疑似重复");
                        materialDeliverFileInventory.setAnalysisResult(2);
                        materialDeliver.setAnalysisStatus(2);
                        materialDeliver.setAnalysisResult(2);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }else {
                        materialDeliverFileInventory.setAnalysisResult(1);
                        materialDeliverFileInventoryArrayList.add(materialDeliverFileInventory);
                        continue;
                    }


                }


            }



            // 保存修改后的 Excel 文件
            FileOutputStream fileOutputStream = new FileOutputStream(excelFilePath);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            workbook.close();
            inputStream.close();


            cuMaterialDeliverService.insertMaterialDeliverAndFiles(materialDeliver,materialDeliverFileInventoryArrayList);

            return Result.ok("材料提交成功");



        } catch (Exception e) {
            return Result.fail("解析文件时出错：" + e.getMessage());
        }





    }


    @GetMapping("/materialDeliverList")
    @ApiOperation("获取材料交接单分页数据")
    @TimeParamHandler
    public Result<IPage<MaterialDeliverDTO>> materialDeliverList(MaterialDeliverSearchVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(materialDeliverService.materialDeliverList(vo, deptId));
    }

    @PostMapping("/materialDeliverExportAndUpload")
    @ApiOperation("导出材料交接单数据（异步导出）")
    @TimeParamHandler
    public Result materialDeliverExportAndUpload(MaterialDeliverSearchVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "材料交接单" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.MATERIAL_DELIVER);
        CompletableFuture.runAsync(() -> {
            try {
                List<MaterialDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<MaterialDeliverDTO> l = materialDeliverService.materialDeliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<MaterialDeliverDTO> util = new ExcelUtil<>(MaterialDeliverDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @GetMapping("/materialDetail")
    @ApiOperation("交付单详情上方交付单信息（操作记录businessType=11）")
    public Result<MaterialDeliverDetailDTO> materialDetail(@RequestParam("id") @ApiParam("交付单id") Long id) {
        return Result.ok(materialDeliverService.materialDeliverDetail(id));
    }

    @GetMapping("/materialFileInventoryPageList")
    @ApiOperation("数据明细列表（详情下方文件清单列表）")
    public Result<IPage<MaterialFileInventoryDTO>> materialFileInventoryPageList(@RequestParam("id") @ApiParam("交付单id") Long id,
                                                                             @RequestParam("pageNum") Integer pageNum,
                                                                             @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(materialDeliverService.materialFileInventoryPageList(id, pageNum, pageSize));
    }

    @GetMapping("/materialFileInventoryPageListV2")
    @ApiOperation("数据明细列表（详情下方文件清单列表）")
    public Result<IPage<MaterialFileInventoryV2DTO>> materialFileInventoryPageListV2(@RequestParam("id") @ApiParam("交付单id") Long id,
                                                                             @RequestParam("pageNum") Integer pageNum,
                                                                             @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(materialDeliverService.materialFileInventoryPageListV2(id, pageNum, pageSize));
    }

//    @GetMapping("/getMaterialFileInventoryFile")
//    @ApiOperation("获取文件清单对应的文件列表（这里不会返回文件长链接，长链接还需要用短链接调用接口获取）")
    public Result<List<CommonFileVO>> getMaterialFileInventoryFile(@RequestParam("materialFileInventoryId") @ApiParam("文件清单id") Long materialFileInventoryId,
                                                                   @RequestParam(value = "fileType", required = false) @ApiParam("文件类型，1-对账单文件/入账材料，2-回单文件") Integer fileType) {
        return Result.ok(materialDeliverService.getMaterialFileInventoryFile(materialFileInventoryId, fileType));
    }

    @GetMapping("/materialFileInventoryList")
    @ApiOperation("获取交付单所有文件清单（这里不会返回文件长链接，长链接还需要用短链接调用接口获取）")
    public Result<List<CommonFileVO>> materialFileInventoryList(@RequestParam("id") @ApiParam("交付单id") Long id) {
        return Result.ok(materialDeliverService.materialFileInventoryList(id));
    }

    @GetMapping("/materialPeriodInventoryPageList")
    @ApiOperation("交付单详情下方账期清单列表（分页）")
    public Result<IPage<MaterialPeriodInventoryDTO>> materialPeriodInventoryPageList(@RequestParam("id") @ApiParam("交付单id") Long id,
                                                                             @RequestParam("pageNum") Integer pageNum,
                                                                             @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(materialDeliverService.materialPeriodInventoryPageList(id, pageNum, pageSize));
    }

    @GetMapping("/getMaterialPeriodInventoryFile")
    @ApiOperation("获取账期清单对应的文件列表（这里不会返回文件长链接，长链接还需要用短链接调用接口获取）")
    public Result<List<CommonFileVO>> getMaterialPeriodInventoryFile(@RequestParam("materialPeriodInventoryId") @ApiParam("账期清单id") Long materialPeriodInventoryId) {
        return Result.ok(materialDeliverService.getMaterialPeriodInventoryFile(materialPeriodInventoryId));
    }

    @PostMapping("/deleteMaterialDeliver")
    @ApiOperation("删除材料交付单，批量操作，传ids")
    public Result<TCommonOperateDTO<MaterialDeliver>> deleteMaterialDeliver(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(materialDeliverService.deleteMaterialDeliver(vo.getIds(), deptId));
    }

//    @PostMapping("/stopMaterialDeliverAnalysis")
//    @ApiOperation("中止解析，批量操作，传ids")
//    public Result<TCommonOperateDTO<MaterialDeliver>> stopMaterialDeliverAnalysis(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
//        return Result.ok(materialDeliverService.stopMaterialDeliverAnalysis(vo.getIds(), deptId));
//    }

    @PostMapping("/downloadOperateErrorRecord/{batchNo}")
    @ApiOperation("统一的下载操作异常的记录表格（同步导出）")
    public void downloadOperateErrorRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<MaterialDeliverErrorDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_OPERATE_ERROR_RECORD + batchNo, 500);
        errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
        ExcelUtil<MaterialDeliverErrorDTO> util = new ExcelUtil<>(MaterialDeliverErrorDTO.class);
        util.exportExcel(response, errorDTOList, "交付单操作异常记录");
    }

    @PostMapping("/materialPushPreview")
    @ApiOperation("推送预览，传参直接传选中的交接单的id的数组，不需要key（下载异常明细接口/bxmFile/customer/deliver/pushReviewExportAndUpload）")
    public Result<MaterialPushPreviewDTO> materialPushPreview(@RequestBody List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Result.fail("请选择需要推送的交接单");
        }
        return Result.ok(materialDeliverService.materialPushPreview(ids));
    }

    @PostMapping("/materialPushPreviewV2")
    @ApiOperation("推送预览，传参直接传选中的交接单的id的数组，不需要key（下载异常明细接口/bxmFile/customer/deliver/pushReviewExportAndUpload）")
    public Result<MaterialPushPreviewDTO> materialPushPreviewV2(@RequestBody List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Result.fail("请选择需要推送的交接单");
        }
        return Result.ok(materialDeliverService.materialPushPreviewV2(ids));
    }

    @PostMapping("/downloadPushError/{batchNo}")
    @ApiOperation("推送异常数据下载（改为所有数据了）")
    public void downloadPushError(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<MaterialPushPreviewListDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_ERROR_LIST_KEY + batchNo, 500);
        List<MaterialPushPreviewListDTO> successDTOList = redisService.getLargeCacheList(CacheConstants.MATERIAL_DELIVER_PUSH_PREVIEW_LIST_KEY + batchNo, 500);
        List<MaterialPushPreviewListDTO> allData = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(errorDTOList)) {
            allData.addAll(errorDTOList);
        }
        if (!ObjectUtils.isEmpty(successDTOList)) {
            allData.addAll(successDTOList);
        }
        ExcelUtil<MaterialPushPreviewListDTO> util = new ExcelUtil<>(MaterialPushPreviewListDTO.class);
        util.exportExcel(response, allData, "推送明细");
    }

    @PostMapping("/confirmPush")
    @ApiOperation("确认推送")
    public Result confirmPush(@RequestBody ConfirmPushVO vo, @RequestHeader("deptId") Long deptId) {
        String batchNo = vo.getBatchNo();
        if (StringUtils.isEmpty(batchNo)) {
            return Result.fail("参数错误");
        }
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:material:deliver:confirmPush:" + batchNo, userId.toString(), 5L)) {
            try {
                materialDeliverService.confirmPush(batchNo, deptId);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:material:deliver:confirmPush:" + batchNo, userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    @PostMapping("/confirmPushV2")
    @ApiOperation("确认推送")
    public Result confirmPushV2(@RequestBody ConfirmPushVO vo, @RequestHeader("deptId") Long deptId) {
        String batchNo = vo.getBatchNo();
        if (StringUtils.isEmpty(batchNo)) {
            return Result.fail("参数错误");
        }
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:material:deliver:confirmPush:" + batchNo, userId.toString(), 5L)) {
            try {
                materialDeliverService.confirmPushV2(batchNo, deptId);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:material:deliver:confirmPush:" + batchNo, userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    @GetMapping("/periodInventoryPageList")
    @ApiOperation("账期清单列表")
    public Result<IPage<MaterialDeliverPeriodInventoryDTO>> periodInventoryPageList(MaterialDeliverPeriodInventorySearchVO vo,
                                                                                    @RequestHeader("deptId") Long deptId) {
        return Result.ok(materialDeliverService.periodInventoryPageList(vo, deptId));
    }

    @PostMapping("/getPushReviewErrorList")
    @ApiIgnore
    @InnerAuth
    public Result<List<MaterialPushPreviewListDTO>> getPushReviewErrorList(@RequestBody String batchNo) {
        if (StringUtils.isEmpty(batchNo)) {
            return Result.ok(Lists.newArrayList());
        }
        return Result.ok(materialDeliverService.getPushReviewErrorList(batchNo));
    }

    @PostMapping("/materialDeliverInventoryDetailPageList")
    @ApiOperation("明细列表(分页)")
    public Result<IPage<MaterialDeliverInventoryDetailDTO>> materialDeliverInventoryDetailPageList(@RequestBody MaterialDeliverInventoryDetailSearchVO vo,
                                                                                                   @RequestHeader("deptId") Long deptId) {
        return Result.ok(materialDeliverService.materialDeliverInventoryDetailPageList(vo, deptId));
    }

    @PostMapping("/materialDeliverInventoryDetailPageListV2")
    @ApiOperation("明细列表(分页)")
    public Result<IPage<MaterialFileInventoryV2DTO>> materialDeliverInventoryDetailPageListV2(@RequestBody MaterialDeliverInventoryDetailSearchVO vo,
                                                                                                   @RequestHeader("deptId") Long deptId) {
        return Result.ok(materialDeliverService.materialDeliverInventoryDetailPageListV2(vo, deptId));
    }

    @PostMapping("/materialDeliverInventoryDetailFileList")
    @ApiOperation("获取所有明细文件,点击打开附件组件用,不分页,同时每条数据都有明细id,定位用")
    public Result<List<CommonFileVO>> materialDeliverInventoryDetailFileList(@RequestBody MaterialDeliverInventoryDetailSearchVO vo) {
        return Result.ok(materialDeliverService.materialDeliverInventoryDetailFileList(vo));
    }

    @PostMapping("/modifyMaterialDeliverFileInventory")
    @ApiOperation("修改明细,单个修改传id,批量修改传ids")
    public Result<TCommonOperateDTO<MaterialDeliverFileInventory>> modifyMaterialDeliverFileInventory(@RequestBody MaterialDeliverInventoryDetailModifyVO vo,
                                                                                                      @RequestHeader("deptId") Long deptId) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(materialDeliverService.modifyMaterialDeliverFileInventory(vo, deptId));
    }

    @PostMapping("/modifyMaterialDeliverFileInventoryV2")
    @ApiOperation("修改明细,单个修改传id,批量修改传ids")
    public Result<TCommonOperateDTO<MaterialDeliverFileInventory>> modifyMaterialDeliverFileInventoryV2(@RequestBody MaterialDeliverInventoryDetailModifyV2VO vo,
                                                                                                      @RequestHeader("deptId") Long deptId) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(materialDeliverService.modifyMaterialDeliverFileInventoryV2(vo, deptId));
    }

    @PostMapping("/deleteMaterialDeliverFileInventory")
    @ApiOperation("删除明细,单个删除传id,批量删除传ids(删除基本不会出现异常,暂不考虑异常情况)")
    public Result<TCommonOperateDTO<MaterialDeliverFileInventory>> deleteMaterialDeliverFileInventory(@RequestBody MaterialDeliverInventoryDetailModifyVO vo,
                                                                                                      @RequestHeader("deptId") Long deptId) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(materialDeliverService.deleteMaterialDeliverFileInventory(vo, deptId));
    }

    @PostMapping("/deleteMaterialDeliverFileInventoryV2")
    @ApiOperation("删除明细,单个删除传id,批量删除传ids(删除基本不会出现异常,暂不考虑异常情况)")
    public Result<TCommonOperateDTO<MaterialDeliverFileInventory>> deleteMaterialDeliverFileInventoryV2(@RequestBody MaterialDeliverInventoryDetailModifyVO vo,
                                                                                                      @RequestHeader("deptId") Long deptId) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(materialDeliverService.deleteMaterialDeliverFileInventoryV2(vo, deptId));
    }

    @PostMapping("/remoteCreateMaterialDeliver")
    @ApiIgnore
    @InnerAuth
    public Result remoteCreateMaterialDeliver(@RequestBody RemoteMaterialDeliver vo) {
        materialDeliverService.remoteCreateMaterialDeliver(vo);
        return Result.ok();
    }

    @GetMapping("/repeatFile")
    @ApiOperation("疑似重复列表")
    public Result<List<MaterialRepeatDTO>> repeatFile(@RequestParam("customerServiceId") Long customerServiceId,
                                                      @RequestParam("fileName") String fileName) {
        return Result.ok(customerServiceCashierAccountingService.repeatFile(customerServiceId, fileName));
    }

    @PostMapping("/retryAnalysis")
    @ApiOperation("重新解析，直接传选中的交接单的id列表，不需要key，会返回一个批次号，通过批次号轮询获取进度")
    public Result<String> retryAnalysis(@RequestBody List<Long> ids) {
        return Result.ok(materialDeliverService.retryAnalysis(ids));
    }

    @PostMapping("/retryAnalysisV2")
    @ApiOperation("重新解析，直接传选中的交接单的id列表，不需要key，会返回一个批次号，通过批次号轮询获取进度")
    public Result<String> retryAnalysisV2(@RequestBody List<Long> ids) {
        return Result.ok(materialDeliverService.retryAnalysisV2(ids));
    }

    @GetMapping("/getRetryAnalysisProgress/{batchNo}")
    @ApiOperation("轮询获取重新解析进度")
    public Result<MaterialRetryAnalysisDTO> getRetryAnalysisProgress(@PathVariable String batchNo) {
        return Result.ok(materialDeliverService.getRetryAnalysisProgress(batchNo));
    }

    @PostMapping("/createAccountingCashier")
    @ApiOperation("重新生成交付单，直接传账期清单的id列表，不需要key")
    public Result createAccountingCashier(@RequestBody List<Long> ids,
                                          @RequestHeader("deptId") Long deptId) {
        materialDeliverService.createAccountingCashier(ids, deptId);
        return Result.ok();
    }

    @PostMapping("/createAccountingCashierV2")
    @ApiOperation("重新生成交付单，直接传账期清单的id列表，不需要key")
    public Result createAccountingCashierV2(@RequestBody List<Long> ids,
                                          @RequestHeader("deptId") Long deptId) {
        materialDeliverService.createAccountingCashierV2(ids, deptId);
        return Result.ok();
    }
}
