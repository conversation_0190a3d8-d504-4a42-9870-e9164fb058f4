package com.bxm.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.BusinessTaskFile;
import com.bxm.customer.service.IBusinessTaskFileService;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.page.TableDataInfo;

/**
 * 业务任务的附件Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/file")
@Api(tags = "业务任务的附件")
public class BusinessTaskFileController extends BaseController
{
    @Autowired
    private IBusinessTaskFileService businessTaskFileService;

    /**
     * 查询业务任务的附件列表
     */
    @RequiresPermissions("customer:file:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询业务任务的附件列表", notes = "查询业务任务的附件列表")
    public TableDataInfo list(BusinessTaskFile businessTaskFile)
    {
        startPage();
        List<BusinessTaskFile> list = businessTaskFileService.selectBusinessTaskFileList(businessTaskFile);
        return getDataTable(list);
    }

    /**
     * 导出业务任务的附件列表
     */
    @RequiresPermissions("customer:file:export")
    @Log(title = "业务任务的附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出业务任务的附件列表", notes = "导出业务任务的附件列表")
    public void export(HttpServletResponse response, BusinessTaskFile businessTaskFile)
    {
        List<BusinessTaskFile> list = businessTaskFileService.selectBusinessTaskFileList(businessTaskFile);
        ExcelUtil<BusinessTaskFile> util = new ExcelUtil<BusinessTaskFile>(BusinessTaskFile.class);
        util.exportExcel(response, list, "业务任务的附件数据");
    }

    /**
     * 获取业务任务的附件详细信息
     */
    @RequiresPermissions("customer:file:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取业务任务的附件详细信息", notes = "获取业务任务的附件详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(businessTaskFileService.selectBusinessTaskFileById(id));
    }

    /**
     * 新增业务任务的附件
     */
    @RequiresPermissions("customer:file:add")
    @Log(title = "业务任务的附件", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增业务任务的附件", notes = "新增业务任务的附件")
    public AjaxResult add(@RequestBody BusinessTaskFile businessTaskFile)
    {
        return toAjax(businessTaskFileService.insertBusinessTaskFile(businessTaskFile));
    }

    /**
     * 修改业务任务的附件
     */
    @RequiresPermissions("customer:file:edit")
    @Log(title = "业务任务的附件", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改业务任务的附件", notes = "修改业务任务的附件")
    public AjaxResult edit(@RequestBody BusinessTaskFile businessTaskFile)
    {
        return toAjax(businessTaskFileService.updateBusinessTaskFile(businessTaskFile));
    }

    /**
     * 删除业务任务的附件
     */
    @RequiresPermissions("customer:file:remove")
    @Log(title = "业务任务的附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除业务任务的附件", notes = "删除业务任务的附件")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessTaskFileService.deleteBusinessTaskFileByIds(ids));
    }
}
