package com.bxm.customer.domain.vo.workBench;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthMiniListSearchVO extends BaseVO {

    @ApiModelProperty("miniList类型，1-上月有效户，2-上月重启，3-上月移出，4-上月新户")
    private Integer type;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("标签名")
    private String tagName;

    @ApiModelProperty("标签是否包含，0-否，1-是")
    private Integer tagIncludeFlag;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("统计类型，1-顾问客户统计，2-会计客户统计")
    private Integer statisticType;

    @ApiModelProperty("选择的部门")
    private Long deptId;
}
