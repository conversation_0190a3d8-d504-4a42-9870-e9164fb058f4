package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerDeliverTemplate;

/**
 * 交付模板Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface ICustomerDeliverTemplateService extends IService<CustomerDeliverTemplate>
{
    /**
     * 查询交付模板
     * 
     * @param id 交付模板主键
     * @return 交付模板
     */
    public CustomerDeliverTemplate selectCustomerDeliverTemplateById(Long id);

    /**
     * 查询交付模板列表
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 交付模板集合
     */
    public List<CustomerDeliverTemplate> selectCustomerDeliverTemplateList(CustomerDeliverTemplate customerDeliverTemplate);

    /**
     * 新增交付模板
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 结果
     */
    public int insertCustomerDeliverTemplate(CustomerDeliverTemplate customerDeliverTemplate);

    /**
     * 修改交付模板
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 结果
     */
    public int updateCustomerDeliverTemplate(CustomerDeliverTemplate customerDeliverTemplate);

    /**
     * 批量删除交付模板
     * 
     * @param ids 需要删除的交付模板主键集合
     * @return 结果
     */
    public int deleteCustomerDeliverTemplateByIds(Long[] ids);

    /**
     * 删除交付模板信息
     * 
     * @param id 交付模板主键
     * @return 结果
     */
    public int deleteCustomerDeliverTemplateById(Long id);

    CustomerDeliverTemplate selectByDeliverTypeAndOperType(Integer deliverType, Integer operType);

    String getDeliverTemplate(Integer deliverType, Integer operType, Integer period);
}
