package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ThirdpartFileUploadRecord;

/**
 * 第三方文件上传记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface IThirdpartFileUploadRecordService extends IService<ThirdpartFileUploadRecord>
{
    /**
     * 查询第三方文件上传记录
     * 
     * @param id 第三方文件上传记录主键
     * @return 第三方文件上传记录
     */
    public ThirdpartFileUploadRecord selectThirdpartFileUploadRecordById(Long id);

    /**
     * 查询第三方文件上传记录列表
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 第三方文件上传记录集合
     */
    public List<ThirdpartFileUploadRecord> selectThirdpartFileUploadRecordList(ThirdpartFileUploadRecord thirdpartFileUploadRecord);

    /**
     * 新增第三方文件上传记录
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 结果
     */
    public int insertThirdpartFileUploadRecord(ThirdpartFileUploadRecord thirdpartFileUploadRecord);

    /**
     * 修改第三方文件上传记录
     * 
     * @param thirdpartFileUploadRecord 第三方文件上传记录
     * @return 结果
     */
    public int updateThirdpartFileUploadRecord(ThirdpartFileUploadRecord thirdpartFileUploadRecord);

    /**
     * 批量删除第三方文件上传记录
     * 
     * @param ids 需要删除的第三方文件上传记录主键集合
     * @return 结果
     */
    public int deleteThirdpartFileUploadRecordByIds(Long[] ids);

    /**
     * 删除第三方文件上传记录信息
     * 
     * @param id 第三方文件上传记录主键
     * @return 结果
     */
    public int deleteThirdpartFileUploadRecordById(Long id);
}
