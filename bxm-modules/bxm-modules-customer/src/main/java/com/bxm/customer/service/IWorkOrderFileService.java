package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.workOrder.WorkOrderFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.WorkOrderFile;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IWorkOrderFileService extends IService<WorkOrderFile>
{

    List<CommonFileVO> getByWorkOrderIdAndFileType(Long workOrderId, Integer workOrderFileType);

    void saveNewFile(Long workOrderId, List<CommonFileVO> files, WorkOrderFileType workOrderFileType, String operName);
}
