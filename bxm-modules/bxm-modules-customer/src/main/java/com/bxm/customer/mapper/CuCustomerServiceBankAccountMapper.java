package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface  CuCustomerServiceBankAccountMapper extends BaseMapper<CustomerServiceBankAccount> {
    /**
     * 根据客户服务ID查找银行账号
     *
     * @param customerServiceId 客户服务ID
     * @return 银行账号列表
     */
    @Select("SELECT * FROM c_customer_service_bank_account WHERE customer_service_id = #{customerServiceId}")
    List<CustomerServiceBankAccount> findByCustomerServiceId(@Param("customerServiceId") Long customerServiceId);

    /**
     * 根据银行账号查找银行账号信息
     *
     * @param bankAccountNumber 银行账号
     * @return 银行账号信息
     */
    @Select("SELECT * FROM c_customer_service_bank_account WHERE bank_account_number = #{bankAccountNumber}")
    CustomerServiceBankAccount findByBankAccountNumber(@Param("bankAccountNumber") String bankAccountNumber);

    /**
     * 根据客户服务ID和开户时间范围查找银行账号
     *
     * @param customerServiceId 客户服务ID
     * @param startDate 开户时间起始日期
     * @param endDate 开户时间结束日期
     * @return 银行账号列表
     */
    @Select("SELECT * FROM c_customer_service_bank_account WHERE customer_service_id = #{customerServiceId} AND account_open_date BETWEEN #{startDate} AND #{endDate}")
    List<CustomerServiceBankAccount> findByCustomerServiceIdAndAccountOpenDateRange(
            @Param("customerServiceId") Long customerServiceId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);
}
