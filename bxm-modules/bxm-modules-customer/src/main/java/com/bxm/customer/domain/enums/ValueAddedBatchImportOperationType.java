package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出操作类型枚举
 *
 * 定义批量导出模板支持的操作类型
 * 每种操作类型对应不同的Excel模板结构
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Getter
@AllArgsConstructor
public enum ValueAddedExportOperationType {

    /**
     * 交付操作
     * 包含：交付单编号、企业名、信用代码、交付结果、总扣缴金额、备注、附件文件名、附件文件夹名、库存表文件名
     */
    DELIVERY("DELIVERY", "交付"),

    /**
     * 补充交付附件操作
     * 包含：交付单编号、企业名、信用代码、备注、附件文件名、附件文件夹名、库存表文件名
     */
    SUPPLEMENT_DELIVERY("SUPPLEMENT_DELIVERY", "补充交付附件"),

    /**
     * 扣款操作
     * 包含：交付单编号、企业名、信用代码、交付结果、备注、附件文件名、附件文件夹名、库存表文件名
     */
    DEDUCTION("DEDUCTION", "扣款");

    /**
     * 操作代码
     */
    private final String code;

    /**
     * 操作描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 操作代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ValueAddedExportOperationType getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ValueAddedExportOperationType type : values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证操作代码是否有效
     *
     * @param code 操作代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有可用的操作代码
     *
     * @return 操作代码数组
     */
    public static String[] getAllCodes() {
        ValueAddedExportOperationType[] types = values();
        String[] codes = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }
}
