//package com.bxm.customer.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.bxm.common.core.enums.BusinessLogBusinessType;
//import com.bxm.common.core.enums.YesNo;
//import com.bxm.common.core.enums.docHandover.DocHandoverFileType;
//import com.bxm.common.core.enums.docHandover.DocHandoverTaxInstrumentType;
//import com.bxm.common.core.enums.repairAccount.RepairAccountStatus;
//import com.bxm.common.core.exception.ServiceException;
//import com.bxm.common.core.utils.DateUtils;
//import com.bxm.common.core.utils.StringUtils;
//import com.bxm.common.log.service.AsyncLogService;
//import com.bxm.common.security.utils.SecurityUtils;
//import com.bxm.customer.domain.*;
//import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentBankDTO;
//import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentDTO;
//import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentTaxItemDTO;
//import com.bxm.customer.domain.dto.repairAccount.*;
//import com.bxm.customer.domain.vo.CommonIdsSearchVO;
//import com.bxm.customer.domain.vo.repairAccount.AddRepairAccountBaseVO;
//import com.bxm.customer.domain.vo.repairAccount.RepairAccountVO;
//import com.bxm.customer.domain.vo.repairAccount.UpdateRepairAccountBaseVO;
//import com.bxm.customer.mapper.*;
//import com.bxm.customer.service.ICCustomerServiceService;
//import com.bxm.customer.service.ICustomerServiceDocHandoverService;
//import com.bxm.customer.service.ICustomerServiceRepairAccountFileService;
//import com.bxm.customer.service.ICustomerServiceRepairAccountService;
//import com.bxm.system.api.RemoteDeptService;
//import com.bxm.system.api.domain.BusinessLogDTO;
//import com.bxm.system.api.domain.SysEmployee;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.ObjectUtils;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 补账Service业务层处理
// *
// * <AUTHOR>
// * @date 2024-07-11
// */
//@Slf4j
//@Service
//public class CustomerServiceRepairAccountServiceImplBackUp extends ServiceImpl<CustomerServiceRepairAccountMapper, CustomerServiceRepairAccount> implements ICustomerServiceRepairAccountService {
//    @Autowired
//    private CustomerServiceRepairAccountMapper customerServiceRepairAccountMapper;
//
//    @Autowired
//    private ICustomerServiceDocHandoverService iCustomerServiceDocHandoverService;
//
//    @Autowired
//    private ICCustomerServiceService icCustomerServiceService;
//
//    @Autowired
//    private AsyncLogService asyncLogService;
//
//    @Autowired
//    private RemoteDeptService remoteDeptService;
//
//    @Autowired
//    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;
//
//    @Autowired
//    private ICustomerServiceRepairAccountFileService iCustomerServiceRepairAccountFileService;
//
//    @Autowired
//    private CustomerServiceRepairAccountTempDocHandoverMapper customerServiceRepairAccountTempDocHandoverMapper;
//
//    @Autowired
//    private CustomerServiceRepairAccountTempBankInstrumentMapper customerServiceRepairAccountTempBankInstrumentMapper;
//
//    @Autowired
//    private CustomerServiceRepairAccountTempTaxInstrumentMapper customerServiceRepairAccountTempTaxInstrumentMapper;
//
//    @Autowired
//    private ICustomerServiceRepairAccountFileService iCustomerServiceRepairAccountFileService;
//
//    /**
//     * 查询补账
//     *
//     * @param id 补账主键
//     * @return 补账
//     */
//    @Override
//    public CustomerServiceRepairAccount selectCustomerServiceRepairAccountById(Long id) {
//        return customerServiceRepairAccountMapper.selectCustomerServiceRepairAccountById(id);
//    }
//
//    /**
//     * 查询补账列表
//     *
//     * @param customerServiceRepairAccount 补账
//     * @return 补账
//     */
//    @Override
//    public List<CustomerServiceRepairAccount> selectCustomerServiceRepairAccountList(CustomerServiceRepairAccount customerServiceRepairAccount) {
//        return customerServiceRepairAccountMapper.selectCustomerServiceRepairAccountList(customerServiceRepairAccount);
//    }
//
//    /**
//     * 新增补账
//     *
//     * @param customerServiceRepairAccount 补账
//     * @return 结果
//     */
//    @Override
//    public int insertCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount) {
//        customerServiceRepairAccount.setCreateTime(DateUtils.getNowDate());
//        return customerServiceRepairAccountMapper.insertCustomerServiceRepairAccount(customerServiceRepairAccount);
//    }
//
//    /**
//     * 修改补账
//     *
//     * @param customerServiceRepairAccount 补账
//     * @return 结果
//     */
//    @Override
//    public int updateCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount) {
//        customerServiceRepairAccount.setUpdateTime(DateUtils.getNowDate());
//        return customerServiceRepairAccountMapper.updateCustomerServiceRepairAccount(customerServiceRepairAccount);
//    }
//
//    /**
//     * 批量删除补账
//     *
//     * @param ids 需要删除的补账主键
//     * @return 结果
//     */
//    @Override
//    public int deleteCustomerServiceRepairAccountByIds(Long[] ids) {
//        return customerServiceRepairAccountMapper.deleteCustomerServiceRepairAccountByIds(ids);
//    }
//
//    /**
//     * 删除补账信息
//     *
//     * @param id 补账主键
//     * @return 结果
//     */
//    @Override
//    public int deleteCustomerServiceRepairAccountById(Long id) {
//        return customerServiceRepairAccountMapper.deleteCustomerServiceRepairAccountById(id);
//    }
//
//    @Override
//    public IPage<RepairAccountDTO> repairAccountList(RepairAccountVO vo) {
////        IPage<RepairAccountDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
////
////        //处理，标签搜索
////        TagSearchVO tagSearchVO = iCustomerServiceDocHandoverService.tagSearch(vo.getTagIncludeFlag(), vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE);
////        if (tagSearchVO.getNeedSearch() && tagSearchVO.getFail()) {
////            return result;
////        }
////
////        //处理，会计搜索
////        //补账ID
////        CommonIdsSearchVO accountingSearchVO = accountingSearchOfRepair(vo.getAccountingEmployee());
////        if (accountingSearchVO.getNeedSearch() && accountingSearchVO.getFail()) {
////            return result;
////        }
////
////        //处理，交付状态
////        //补账ID
////        CommonIdsSearchVO deliverStatusSearchVO = deliverStatusSearch(vo.getDeliverStatus());
////        if (deliverStatusSearchVO.getNeedSearch() && deliverStatusSearchVO.getFail()) {
////            return result;
////        }
////
////        //合并
////        CommonIdsSearchVO commonIdsSearchVO = CustomerServiceInAccountServiceImpl.mergeCommonIdsSearchData(Lists.newArrayList(accountingSearchVO, deliverStatusSearchVO));
////        if (commonIdsSearchVO.getNeedSearch() && commonIdsSearchVO.getFail()) {
////            return result;
////        }
////
////        //原始数据
////        List<RepairAccountDTO> source = customerServiceRepairAccountMapper.selectRepairAccountList(result, vo, tagSearchVO, commonIdsSearchVO);
////
////        //处理数据
////        if (!ObjectUtils.isEmpty(source)) {
////            List<Long> customerServicePeriodMonthIds = source.stream().map(InAccountDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
////
////            //批量获取会计
////            Map<Long, List<AccountingInfoSourceDTO>> accountingInfoSourceMap = iCustomerServiceDocHandoverService.getAccountingInfoSource(
////                    customerServicePeriodMonthIds
////            );
////
////            /*
////             * 取账期对应的材料交接单做逻辑判断：
////             * 无材料交接单：无材料
////             * 有材料交接单且有至少一条是有缺失：有缺失
////             * 有材料交接单且无有缺失且有至少一条是缺但齐：缺但齐
////             * 不符合以上条件：完整
////             * 状态不是“无材料”时，提供详情链接，点击打开月度材料详情抽屉
////             */
////
////            Map<Long, List<CustomerServiceDocHandover>> docHandoverMap = Maps.newHashMap();
////            if (vo.getWholeLevel() == null) {
////                //拿到这些入账交付单的对应账期的对应材料交接单
////                docHandoverMap = iCustomerServiceDocHandoverService.getMapByPeriodMonthIds(customerServicePeriodMonthIds);
////            }
////
////            for (InAccountDTO row : source) {
////                Integer wholeLevel;
////
////                if (vo.getWholeLevel() == null) {
////                    wholeLevel = handleWholeLevel(docHandoverMap.get(row.getCustomerServicePeriodMonthId()));
////                } else {
////                    wholeLevel = vo.getWholeLevel();
////                }
////
////                row.setAccountingEmployeeNameFull(
////                        CustomerServiceDocHandoverServiceImpl.handleAccountingEmployeeNameFull(accountingInfoSourceMap.get(row.getCustomerServicePeriodMonthId()))
////                );
////                row.setStatusStr(InAccountStatus.getByCode(row.getStatus()).getName());
////                row.setWholeLevel(wholeLevel);
////                row.setWholeLevelStr(WholeLevel.getByCode(wholeLevel).getName());
////            }
////        }
////
////        //返回数据
////        result.setRecords(source);
////
////        return result;
//        return null;
//    }
//
//    @Override
//    public FirstPeriodDTO getFirstPeriod(Long customerServiceId) {
//        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
//        if (cCustomerService == null) {
//            throw new ServiceException("客户服务不存在");
//        }
//
//        Integer firstAccountPeriod = cCustomerService.getFirstAccountPeriod();
//        if (firstAccountPeriod == null) {
//            throw new ServiceException("客户服务的首个账期不存在不存在");
//        }
//
//        //获取客户服务补账的结束账期
//        Integer periodEnd = getPeriodEndOfCustomerService(customerServiceId, firstAccountPeriod);
//
//        return FirstPeriodDTO.builder()
//                .customerName(cCustomerService.getCustomerName())
//                .customerServiceId(customerServiceId)
//                .firstAccountPeriod(firstAccountPeriod)
//                .firstAccountPeriodStr(new StringBuilder(String.valueOf(firstAccountPeriod)).insert(4, "-").toString())
//                .periodEnd(periodEnd)
//                .build();
//    }
//
//    @Override
//    public RepairAccountPeriodEndDTO getRepairAccountPeriodEnd(Long customerServiceId) {
//        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
//        if (cCustomerService == null) {
//            throw new ServiceException("客户服务不存在");
//        }
//
//        Integer firstAccountPeriod = cCustomerService.getFirstAccountPeriod();
//        if (firstAccountPeriod == null) {
//            throw new ServiceException("客户服务的首个账期不存在不存在");
//        }
//
//        //获取客户服务补账的结束账期
//        Integer periodEnd = getPeriodEndOfCustomerService(customerServiceId, firstAccountPeriod);
//
//        return RepairAccountPeriodEndDTO.builder()
//                .customerName(cCustomerService.getCustomerName())
//                .customerServiceId(customerServiceId)
//                .periodEnd(periodEnd)
//                .build();
//    }
//
//    @Transactional
//    @Override
//    public Long addRepairAccountBase(Long deptId, AddRepairAccountBaseVO vo) {
//        Long customerServiceId = vo.getCustomerServiceId();
//
//        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
//        if (cCustomerService == null || cCustomerService.getIsDel()) {
//            throw new ServiceException("客户服务不存在");
//        }
//
//        Integer periodStart = vo.getPeriodStart();
//        Integer periodEnd = getPeriodEndOfCustomerService(customerServiceId, cCustomerService.getFirstAccountPeriod());
//        if (periodStart > periodEnd) {
//            throw new ServiceException("服务账期不可重复提交");
//        }
//
//        //同时没有其他待分派的补账服务；报错提示：该客户还有补账服务还未处理，请勿重复提交。
//        int needGiveCount = count(new LambdaQueryWrapper<CustomerServiceRepairAccount>()
//                .eq(CustomerServiceRepairAccount::getIsDel, Boolean.FALSE)
//                .eq(CustomerServiceRepairAccount::getCustomerServiceId, customerServiceId)
//                .eq(CustomerServiceRepairAccount::getStatus, RepairAccountStatus.NEED_GIVE.getCode())
//        );
//        if (needGiveCount > 0) {
//            throw new ServiceException("该客户还有补账服务还未处理，请勿重复提交。");
//        }
//
//        Long userId = SecurityUtils.getUserId();
//        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//        String setOperName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
//        //Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
//        //SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
//
//        CustomerServiceRepairAccount newEntry = new CustomerServiceRepairAccount();
//        newEntry.setIsDel(Boolean.FALSE);
//
//        newEntry.setCustomerServiceId(customerServiceId);
//        newEntry.setCustomerName(cCustomerService.getCustomerName());
//        newEntry.setCreditCode(cCustomerService.getCreditCode());
//        newEntry.setServiceNumber(cCustomerService.getServiceNumber());
//        newEntry.setTaxNumber(cCustomerService.getTaxNumber());
//        newEntry.setTaxType(cCustomerService.getTaxType());
//
//        newEntry.setAccountingDeptId(cCustomerService.getAccountingDeptId());
//        newEntry.setAccountingDeptName(
//                remoteDeptService.getDeptInfo(cCustomerService.getAccountingDeptId())
//                        .getDataThrowException()
//                        .getDeptName()
//        );
//        newEntry.setAccountingTopDeptId(cCustomerService.getAccountingTopDeptId());
//
//        newEntry.setAdvisorDeptId(cCustomerService.getAdvisorDeptId());
//        newEntry.setAdvisorDeptName(
//                remoteDeptService.getDeptInfo(cCustomerService.getAdvisorDeptId())
//                        .getDataThrowException()
//                        .getDeptName()
//        );
//        newEntry.setAdvisorTopDeptId(cCustomerService.getAdvisorTopDeptId());
//
//        newEntry.setBusinessDeptId(cCustomerService.getBusinessDeptId());
//        newEntry.setBusinessDeptName(
//                remoteDeptService.getDeptInfo(cCustomerService.getBusinessDeptId())
//                        .getDataThrowException()
//                        .getDeptName()
//        );
//        newEntry.setBusinessTopDeptId(cCustomerService.getBusinessTopDeptId());
//
//        newEntry.setStatus(RepairAccountStatus.NEED_PERFECT.getCode());
//
//        newEntry.setStartPeriod(periodStart);
//        newEntry.setEndPeriod(periodEnd);
//
//        newEntry.setRemark(vo.getRemark());
//
//        newEntry.setVerificationEmployeeType(2);
//
//        //存数据
//        save(newEntry);
//
//        //存附件
//        iCustomerServiceRepairAccountFileService.saveFile(newEntry.getId(), vo.getFiles(), DocHandoverFileType.BASE, String.valueOf(newEntry.getId()));
//
//        Map<String, Object> map = Maps.newLinkedHashMap();
//        map.put("开始账期", periodStart);
//        map.put("结束账期", periodEnd);
//        map.put("备注", vo.getRemark());
//        String operContent = JSONObject.toJSONString(map);
//        try {
//            //Long userId = SecurityUtils.getUserId();
//            //List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//
//            asyncLogService.saveBusinessLog(
//                    new BusinessLogDTO()
//                            .setBusinessId(newEntry.getId())
//                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
//                            .setDeptId(deptId)
//                            .setOperType("新建补账服务")
//                            .setOperName(setOperName)
//                            .setOperContent(operContent)
//                            .setOperRemark("新建补账服务")
//                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
//                            .setOperUserId(userId)
//            );
//        } catch (Exception e) {
//            log.error("新增业务日志失败:{}", e.getMessage());
//        }
//
//        return newEntry.getId();
//    }
//
//    @Override
//    public RepairAccountBaseDTO getRepairAccountBase(Long id) {
//        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
//        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
//            throw new ServiceException("补账不存在");
//        }
//
//        return getRepairAccountBaseResult(customerServiceRepairAccount);
//    }
//
//    @Override
//    public void updateRepairAccountBase(Long deptId, UpdateRepairAccountBaseVO vo) {
//        CustomerServiceRepairAccount customerServiceRepairAccount = getById(vo.getId());
//        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
//            throw new ServiceException("补账不存在");
//        }
//        if (!RepairAccountStatus.canUpdate(customerServiceRepairAccount.getStatus())) {
//            throw new ServiceException("当前状态的补账不可编辑");
//        }
//        Long customerServiceId = customerServiceRepairAccount.getCustomerServiceId();
//        CCustomerService cCustomerService = icCustomerServiceService.getById(customerServiceId);
//        if (cCustomerService == null || cCustomerService.getIsDel()) {
//            throw new ServiceException("客户服务不存在");
//        }
//
//        Integer periodStart = vo.getPeriodStart();
//        Integer periodEnd = getPeriodEndOfCustomerService(customerServiceId, cCustomerService.getFirstAccountPeriod());
//        if (periodStart > periodEnd) {
//            throw new ServiceException("服务账期不可重复提交");
//        }
//
//        //同时没有其他待分派的补账服务；报错提示：该客户还有补账服务还未处理，请勿重复提交。
//        int needGiveCount = count(new LambdaQueryWrapper<CustomerServiceRepairAccount>()
//                .eq(CustomerServiceRepairAccount::getIsDel, Boolean.FALSE)
//                .eq(CustomerServiceRepairAccount::getCustomerServiceId, customerServiceId)
//                .eq(CustomerServiceRepairAccount::getStatus, RepairAccountStatus.NEED_GIVE.getCode())
//        );
//        if (needGiveCount > 0) {
//            throw new ServiceException("该客户还有补账服务还未处理，请勿重复提交。");
//        }
//
//        CustomerServiceRepairAccount updateEntry = new CustomerServiceRepairAccount();
//        updateEntry.setId(vo.getId());
//        updateEntry.setStartPeriod(periodStart);
//        updateEntry.setEndPeriod(periodEnd);
//        updateEntry.setRemark(vo.getRemark());
//        updateEntry.setUpdateTime(LocalDateTime.now());
//
//        updateById(updateEntry);
//
//        //先删除原来的文件
//        iCustomerServiceDocHandoverFileService.deleteByDocHandoverId(vo.getId(), Lists.newArrayList(DocHandoverFileType.BASE));
//        //再存附件
//        iCustomerServiceDocHandoverFileService.saveFile(vo.getId(), vo.getFiles(), DocHandoverFileType.BASE, String.valueOf(vo.getId()));
//
//        Map<String, Object> map = Maps.newLinkedHashMap();
//        map.put("开始账期", periodStart);
//        map.put("结束账期", periodEnd);
//        map.put("备注", vo.getRemark());
//        String operContent = JSONObject.toJSONString(map);
//        try {
//            Long userId = SecurityUtils.getUserId();
//            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
//
//            asyncLogService.saveBusinessLog(
//                    new BusinessLogDTO()
//                            .setBusinessId(updateEntry.getId())
//                            .setBusinessType(BusinessLogBusinessType.DOC_HANDOVER.getCode())
//                            .setDeptId(deptId)
//                            .setOperType("编辑补账服务")
//                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
//                            .setOperContent(operContent)
//                            .setOperRemark("编辑补账服务-基础信息")
//                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
//                            .setOperUserId(userId)
//            );
//        } catch (Exception e) {
//            log.error("新增业务日志失败:{}", e.getMessage());
//        }
//    }
//
//    @Override
//    public RepairAccountInstrumentDTO getRepairAccountInstrument(Long id) {
//        CustomerServiceRepairAccount customerServiceRepairAccount = getById(id);
//        if (customerServiceRepairAccount == null || customerServiceRepairAccount.getIsDel()) {
//            throw new ServiceException("补账不存在");
//        }
//
//        //获取除了base之外的所有㡿
//        Map<String, List<CustomerServiceRepairAccountFile>> filesMap = ;//iCustomerServiceDocHandoverFileService.selectMapSubKeyByDocHandover(id, DocHandoverFileType.exceptBase());
//
//        //获取临时的材料交接数据
//        List<CustomerServiceRepairAccountTempDocHandover> tempDocHandovers = customerServiceRepairAccountTempDocHandoverMapper.selectList(
//                new LambdaQueryWrapper<CustomerServiceRepairAccountTempDocHandover>()
//                        .in(CustomerServiceRepairAccountTempDocHandover::getCustomerServiceRepairAccountId, id)
//        );
//        Map<Integer, CustomerServiceRepairAccountTempDocHandover> tempDocHandoversMap = tempDocHandovers
//                .stream().collect(Collectors.toMap(CustomerServiceRepairAccountTempDocHandover::getPeriod, row -> row));
//
//        //获取临时的银行票据数据
//        List<CustomerServiceRepairAccountTempBankInstrument> tempBankInstruments = customerServiceRepairAccountTempBankInstrumentMapper.selectList(
//                new LambdaQueryWrapper<CustomerServiceRepairAccountTempBankInstrument>()
//                        .in(CustomerServiceRepairAccountTempBankInstrument::getCustomerServiceRepairAccountId, id)
//        );
//        Map<Integer, List<CustomerServiceRepairAccountTempBankInstrument>> tempBankInstrumentsMap = tempBankInstruments
//                .stream().collect(Collectors.groupingBy(CustomerServiceRepairAccountTempBankInstrument::getPeriod));
//
//        //获取临时的税号发票以及其他票据数据
//        List<CustomerServiceRepairAccountTempTaxInstrument> tempTaxInstruments = customerServiceRepairAccountTempTaxInstrumentMapper.selectList(
//                new LambdaQueryWrapper<CustomerServiceRepairAccountTempTaxInstrument>()
//                        .in(CustomerServiceRepairAccountTempTaxInstrument::getCustomerServiceRepairAccountId, id)
//        );
//        Map<Integer, List<CustomerServiceRepairAccountTempTaxInstrument>> tempTaxInstrumentsMap = tempTaxInstruments
//                .stream().collect(Collectors.groupingBy(CustomerServiceRepairAccountTempTaxInstrument::getPeriod));
//
//        List<DocHandoverInstrumentDTO> items = Lists.newArrayList();
//
//        //账期范围内的所有账期的数据
//        for (int indexPeriod = customerServiceRepairAccount.getStartPeriod(); indexPeriod <= customerServiceRepairAccount.getEndPeriod(); ) {
//            //indexPeriod 是 账期值，也是下标
//
//            DocHandoverInstrumentDTO docHandoverInstrumentDTO = covDocHandoverInstrumentDTO(
//                    indexPeriod,
//                    customerServiceRepairAccount.getCustomerServiceId(),
//                    filesMap,
//                    tempDocHandoversMap.get(indexPeriod),
//                    tempBankInstrumentsMap.get(indexPeriod),
//                    tempTaxInstrumentsMap.get(indexPeriod)
//            );
//
//            if (docHandoverInstrumentDTO != null) {
//                items.add(docHandoverInstrumentDTO);
//            }
//
//
////            items.add(
////                    covDocHandoverInstrumentDTO(
////                            indexPeriod,
////                            filesMap,
////                            tempDocHandoversMap.get(indexPeriod),
////                            tempBankInstrumentsMap.get(indexPeriod),
////                            tempTaxInstrumentsMap.get(indexPeriod)
////                    )
////            );
//
//            indexPeriod = nextPeriod(indexPeriod, 1L);
//
//        }
//
//        return RepairAccountInstrumentDTO.builder()
//                .id(id)
//                .items(items)
//                .build();
//    }
//
//    //处理，会计搜索
//    //补账的ID
//    @Override
//    public CommonIdsSearchVO accountingSearchOfRepair(String accountingEmployee) {
//        boolean needSearch = true;//是否需要搜索
//        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
//        List<Long> ids = null;
//
//        if (StringUtils.isEmpty(accountingEmployee)) {
//            needSearch = false;
//        } else {
//            ids = customerServiceRepairAccountMapper.searchAccounting(accountingEmployee);
//
//            if (ObjectUtils.isEmpty(ids)) {
//                fail = true;
//            }
//        }
//
//        return CommonIdsSearchVO.builder()
//                .needSearch(needSearch)
//                .fail(fail)
//                .ids(ids)
//                .build();
//    }
//
//    /*
//     * 当补账还是待分派时，交付状态=待交付
//     * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
//     * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
//     * 其他状态为交付中
//     */
//    //付状态：1-待交付、2-交付中、3-已完成
//    //补账的ID
//    private CommonIdsSearchVO deliverStatusSearch(Integer deliverStatus) {
//        boolean needSearch = true;//是否需要搜索
//        boolean fail = false;//需要搜索的情况下，已经知道搜索不到，就直接失败返回，true=返回
//        List<Long> ids = null;
//
//        if (deliverStatus == null) {
//            needSearch = false;
//        } else {
//            ids = customerServiceRepairAccountMapper.searchDeliverStatus(deliverStatus);
//
//            if (ObjectUtils.isEmpty(ids)) {
//                fail = true;
//            }
//        }
//
//        return CommonIdsSearchVO.builder()
//                .needSearch(needSearch)
//                .fail(fail)
//                .ids(ids)
//                .build();
//    }
//
//    //获取客户服务补账的结束账期
//    private Integer getPeriodEndOfCustomerService(Long customerServiceId, Integer firstAccountPeriod) {
//        Integer nowMinPeriod = customerServicePeriodMonthMapper.selectMinPeriod(customerServiceId);
//        Integer nowMStartPeriod = (nowMinPeriod == null || nowMinPeriod >= firstAccountPeriod) ? firstAccountPeriod : nowMinPeriod;
//        LocalDate nowMStartPeriodLocalDate = DateUtils.strToLocalDate(String.valueOf(nowMStartPeriod), DateUtils.YYYYMMDD);
//
//        return Integer.parseInt(DateUtils.localDateToStr(nowMStartPeriodLocalDate.plusMonths(-1L), DateUtils.YYYYMM));
//    }
//
//    private RepairAccountBaseDTO getRepairAccountBaseResult(CustomerServiceRepairAccount customerServiceRepairAccount) {
//        if (customerServiceRepairAccount == null) {
//            return null;
//        }
//
//        List<CustomerServiceDocHandoverFile> files = iCustomerServiceRepairAccountFileService.selectByDocHandover(customerServiceDocHandover.getId(), Lists.newArrayList(DocHandoverFileType.BASE));
//
//        return RepairAccountBaseDTO.builder()
//                .customerName(customerServiceRepairAccount.getCustomerName())
//                .customerServiceId(customerServiceRepairAccount.getCustomerServiceId())
//                .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(files))
//                .id(customerServiceRepairAccount.getId())
//                .periodEnd(customerServiceRepairAccount.getEndPeriod())
//                .periodStart(customerServiceRepairAccount.getStartPeriod())
//                .remark(customerServiceRepairAccount.getRemark())
//                .build();
//    }
//
//    private DocHandoverInstrumentDTO covDocHandoverInstrumentDTO(
//            Integer indexPeriod,
//            Long customerServiceId,
//            Map<String, List<CustomerServiceRepairAccountFile>> filesMap,
//            CustomerServiceRepairAccountTempDocHandover hisTempDocHandover,
//            List<CustomerServiceRepairAccountTempBankInstrument> hisTempBankInstruments,
//            List<CustomerServiceRepairAccountTempTaxInstrument> hisTaxInstruments
//    ) {
//        if (indexPeriod == null) {
//            return null;
//        }
//
//        DocHandoverInstrumentDTO result = DocHandoverInstrumentDTO.builder().build();
//
//
//        //该账期的 临时材料交接
////        CustomerServiceRepairAccountTempDocHandover hisTempDocHandover = tempDocHandoversMap.get(indexPeriod);
////
////        List<CustomerServiceRepairAccountTempBankInstrument> hisTempBankInstruments = tempBankInstrumentsMap.get(indexPeriod);
////        List<CustomerServiceRepairAccountTempTaxInstrument> hisTaxInstruments = tempTaxInstrumentsMap.getOrDefault(indexPeriod, Lists.newArrayList());
//
//        Map<Integer, List<CustomerServiceRepairAccountTempTaxInstrument>> customerServiceDocHandoverTaxInstrumentsMap = hisTaxInstruments.stream()
//                .collect(Collectors.groupingBy(CustomerServiceRepairAccountTempTaxInstrument::getBizType));
//
//        if (hisTempDocHandover == null) {
//            //之前还没保存、提交过
//
//            result.setId(null);
//            result.setBankInstruments(
//                    iCustomerServiceDocHandoverService.handleInitBankInstrument(customerServiceId, indexPeriod)
//            );
//            result.setHasTaxTicket(YesNo.YES.getCode());
//            result.setHasOtherTicket(YesNo.YES.getCode());
//        } else {
//            //给之前用户提交的数据
//
//            result.setId(hisTempDocHandover.getId());
//            result.setBankInstruments(
//                    handleHisBankInstrument(indexPeriod, hisTempBankInstruments, filesMap)
//            );
//            result.setHasTaxTicket(hisTempDocHandover.getHasTaxTicket());
//            result.setHasOtherTicket(hisTempDocHandover.getHasOtherTicket());
//        }
//
//        //处理 税号发票
//        List<DocHandoverInstrumentTaxItemDTO> taxInstruments = Lists.newArrayList();
//        Map<String, CustomerServiceRepairAccountTempTaxInstrument> alreadyDocHandoverTaxInstrumentMap = customerServiceDocHandoverTaxInstrumentsMap.getOrDefault(2, Lists.newArrayList())
//                .stream().collect(Collectors.toMap(CustomerServiceRepairAccountTempTaxInstrument::getName, row -> row));
//        for (DocHandoverTaxInstrumentType row : DocHandoverTaxInstrumentType.values()) {
//            CustomerServiceRepairAccountTempTaxInstrument customerServiceDocHandoverTaxInstrument = alreadyDocHandoverTaxInstrumentMap.get(row.name());
//            DocHandoverInstrumentTaxItemDTO docHandoverInstrumentTaxItemDTO;
//            if (customerServiceDocHandoverTaxInstrument == null) {
//                docHandoverInstrumentTaxItemDTO = DocHandoverInstrumentTaxItemDTO.builder()
//                        .name(row.name())
//                        .paperCount(null)
//                        .remark(null)
//                        .files(null)
//                        .build();
//            } else {
//                docHandoverInstrumentTaxItemDTO = DocHandoverInstrumentTaxItemDTO.builder()
//                        .name(customerServiceDocHandoverTaxInstrument.getName())
//                        .paperCount(customerServiceDocHandoverTaxInstrument.getPaperCount())
//                        .remark(customerServiceDocHandoverTaxInstrument.getRemark())
//                        .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(
//                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.TAX_TICKET.getCode(), String.valueOf(customerServiceDocHandoverTaxInstrument.getRowNum())))
//                                )
//                        )
//                        .build();
//            }
//            taxInstruments.add(docHandoverInstrumentTaxItemDTO);
//        }
//
//        //处理 其他票据
//        List<DocHandoverInstrumentTaxItemDTO> otherInstruments = customerServiceDocHandoverTaxInstrumentsMap.getOrDefault(1, Lists.newArrayList())
//                .stream()
//                .map(row -> DocHandoverInstrumentTaxItemDTO.builder()
//                        .name(row.getName())
//                        .paperCount(row.getPaperCount())
//                        .remark(row.getRemark())
//                        .files(iCustomerServiceRepairAccountFileService.covToCommonFileVO(
//                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.TAX_TICKET.getCode(), String.valueOf(row.getRowNum())))
//                                )
//                        )
//                        .build())
//                .collect(Collectors.toList());
//
//        result.setTaxInstruments(taxInstruments);
//        result.setOtherInstruments(otherInstruments);
//
//        return result;
//    }
//
//    private List<DocHandoverInstrumentBankDTO> handleHisBankInstrument(Integer indexPeriod, List<CustomerServiceRepairAccountTempBankInstrument> source, Map<String, List<CustomerServiceRepairAccountFile>> filesMap) {
//        List<DocHandoverInstrumentBankDTO> bankInstruments = Lists.newArrayList();
//
//        if (!ObjectUtils.isEmpty(source)) {
//            for (CustomerServiceRepairAccountTempBankInstrument row : source) {
//                String subFileType = keySubFileType(indexPeriod, row.getBankName());
//
//                bankInstruments.add(
//                        DocHandoverInstrumentBankDTO.builder()
//                                .backTicketContents(CustomerServiceDocHandoverServiceImpl.handleTicketContent(row.getBackTicketContent()))
//                                .backTicketFiles(
//                                        iCustomerServiceRepairAccountFileService.covToCommonFileVO(
//                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_BACK_TICKET.getCode(), subFileType))
//                                        )
//                                )
//                                .bankName(row.getBankName())
//                                .checkTicketContents(CustomerServiceDocHandoverServiceImpl.handleTicketContent(row.getCheckTicketContent()))
//                                .checkTicketFiles(
//                                        iCustomerServiceRepairAccountFileService.covToCommonFileVO(
//                                                filesMap.get(CustomerServiceDocHandoverFileServiceImpl.key(DocHandoverFileType.BANK_CHECK_TICKET.getCode(), subFileType))
//                                        )
//                                )
//                                .has(row.getHas())
//                                .hasBackTicket(row.getHasBackTicket())
//                                .hasCheckTicket(row.getHasCheckTicket())
//                                .hasPayment(row.getHasPayment())
//                                .build()
//                );
//            }
//        }
//
//        return bankInstruments;
//    }
//
//    public static Integer nextPeriod(Integer period, Long monthsToAdd) {
//        LocalDate periodLocalDate = DateUtils.strToLocalDate(String.valueOf(period), DateUtils.YYYYMMDD);
//
//        return Integer.parseInt(DateUtils.localDateToStr(periodLocalDate.plusMonths(monthsToAdd), DateUtils.YYYYMM));
//    }
//
//    public static String keySubFileType(Integer period, String flag) {
//        return period + "_" + flag;
//    }
//
//    public static void main(String[] args) {
//        Integer nowMStartPeriod = 202407;
//
//        LocalDate nowMStartPeriodLocalDate = DateUtils.strToLocalDate(String.valueOf(nowMStartPeriod), DateUtils.YYYYMMDD);
//
//        System.out.println(Integer.parseInt(DateUtils.localDateToStr(nowMStartPeriodLocalDate.plusMonths(-1L), DateUtils.YYYYMM)));
//    }
//}
