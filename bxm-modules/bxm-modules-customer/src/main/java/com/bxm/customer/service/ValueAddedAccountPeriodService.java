package com.bxm.customer.service;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;

/**
 * 账期服务接口
 *
 * 用于处理增值交付单状态变更时的账期相关操作，包括校验、生成和删除账期记录
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ValueAddedAccountPeriodService {

    /**
     * 校验补账和改账类型的账期规则
     *
     * 根据增值事项类型执行不同的校验逻辑：
     * 1. 补账：如果在指定账期范围内存在任何period记录，则抛出异常
     * 2. 改账：如果在指定账期范围内不存在任何period记录，则抛出异常
     *
     * @param order 增值交付单对象，包含businessTopDeptId、creditCode、accountingPeriodStart、accountingPeriodEnd、valueAddedItemTypeId等字段
     * @throws IllegalArgumentException 当校验失败时抛出，包含具体的错误信息
     * @throws RuntimeException 当系统异常时抛出
     */
    void validateAccountPeriodForSubmit(ValueAddedDeliveryOrder order);

    /**
     * 生成增值账期记录
     *
     * 根据交付单信息在c_value_added_period_month表中生成账期范围内的所有月份记录
     *
     * @param order 增值交付单对象，包含creditCode、businessTopDeptId、accountingPeriodStart、accountingPeriodEnd等字段
     * @throws RuntimeException 当生成记录失败时抛出
     */
    void generateValueAddedPeriodRecords(ValueAddedDeliveryOrder order);

    /**
     * 删除增值账期记录
     *
     * 根据交付单信息删除c_value_added_period_month表中对应的账期记录
     *
     * @param order 增值交付单对象，包含creditCode、businessTopDeptId、accountingPeriodStart、accountingPeriodEnd等字段
     * @throws RuntimeException 当删除记录失败时抛出
     */
    void deleteValueAddedPeriodRecords(ValueAddedDeliveryOrder order);
}
