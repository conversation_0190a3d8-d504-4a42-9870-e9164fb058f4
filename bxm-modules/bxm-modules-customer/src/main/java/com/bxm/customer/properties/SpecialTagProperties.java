package com.bxm.customer.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "special.tag")
public class SpecialTagProperties
{
    private Long lingshenbao;

    private <PERSON> sanling;

    private Long yibao;

    private Long shebao;

    private Long wupiaoshouru;

    private Long gongjijin;

    public Long getVip() {
        return vip;
    }

    public void setVip(Long vip) {
        this.vip = vip;
    }

    private Long vip;

    private Long yq;

    private Long gongzibiao;

    private Long pprz;

    private Long wxfprw;

    // 次月15号出账
    private Long cy15cz;

    // 次月末出账
    private Long cymcz;

    // 个性化报税
    private Long gxhbs;

    public Long getGxhbs() {
        return gxhbs;
    }

    public void setGxhbs(Long gxhbs) {
        this.gxhbs = gxhbs;
    }

    public Long getCy15cz() {
        return cy15cz;
    }

    public void setCy15cz(Long cy15cz) {
        this.cy15cz = cy15cz;
    }

    public Long getCymcz() {
        return cymcz;
    }

    public void setCymcz(Long cymcz) {
        this.cymcz = cymcz;
    }

    public Long getWxfprw() {
        return wxfprw;
    }

    public void setWxfprw(Long wxfprw) {
        this.wxfprw = wxfprw;
    }

    public Long getPprz() {
        return pprz;
    }

    public void setPprz(Long pprz) {
        this.pprz = pprz;
    }

    public Long getGongzibiao() {
        return gongzibiao;
    }

    public Long getYq() {
        return yq;
    }

    public void setYq(Long yq) {
        this.yq = yq;
    }

    public void setGongzibiao(Long gongzibiao) {
        this.gongzibiao = gongzibiao;
    }

    public Long getGongjijin() {
        return gongjijin;
    }

    public void setGongjijin(Long gongjijin) {
        this.gongjijin = gongjijin;
    }

    public Long getWupiaoshouru() {
        return wupiaoshouru;
    }

    public void setWupiaoshouru(Long wupiaoshouru) {
        this.wupiaoshouru = wupiaoshouru;
    }

    public Long getLingshenbao() {
        return lingshenbao;
    }

    public void setLingshenbao(Long lingshenbao) {
        this.lingshenbao = lingshenbao;
    }

    public Long getSanling() {
        return sanling;
    }

    public void setSanling(Long sanling) {
        this.sanling = sanling;
    }

    public Long getYibao() {
        return yibao;
    }

    public void setYibao(Long yibao) {
        this.yibao = yibao;
    }

    public Long getShebao() {
        return shebao;
    }

    public void setShebao(Long shebao) {
        this.shebao = shebao;
    }

}
