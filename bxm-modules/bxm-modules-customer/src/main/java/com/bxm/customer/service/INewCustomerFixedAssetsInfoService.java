package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerFixedAssetsInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferFixedAssetsInfoDTO;

/**
 * 新户流转固定资产信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerFixedAssetsInfoService extends IService<NewCustomerFixedAssetsInfo>
{
    /**
     * 查询新户流转固定资产信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 新户流转固定资产信息
     */
    public NewCustomerFixedAssetsInfo selectNewCustomerFixedAssetsInfoById(Long id);

    /**
     * 查询新户流转固定资产信息列表
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 新户流转固定资产信息集合
     */
    public List<NewCustomerFixedAssetsInfo> selectNewCustomerFixedAssetsInfoList(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 新增新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    public int insertNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 修改新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    public int updateNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 批量删除新户流转固定资产信息
     * 
     * @param ids 需要删除的新户流转固定资产信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerFixedAssetsInfoByIds(Long[] ids);

    /**
     * 删除新户流转固定资产信息信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 结果
     */
    public int deleteNewCustomerFixedAssetsInfoById(Long id);

    List<NewCustomerFixedAssetsInfo> selectByCustomerId(Long customerId);

    Integer countByCustomerId(Long customerId);

    void deleteByCustomerId(Long customerId);

    void removeAndSaveNew(Long customerId, List<NewCustomerTransferFixedAssetsInfoDTO> fixedAssetsList);

    Map<Long, List<NewCustomerFixedAssetsInfo>> selectMapByCustomerIds(List<Long> newCustomerTransferIds);
}
