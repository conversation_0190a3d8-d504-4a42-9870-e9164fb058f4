package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CCustomerServicePeriodEmployee;

import java.util.List;

/**
 * 账期服务人员Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
public interface ICCustomerServicePeriodEmployeeService extends IService<CCustomerServicePeriodEmployee>
{
    /**
     * 查询账期服务人员
     * 
     * @param id 账期服务人员主键
     * @return 账期服务人员
     */
    public CCustomerServicePeriodEmployee selectCCustomerServicePeriodEmployeeById(Long id);

    /**
     * 查询账期服务人员列表
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 账期服务人员集合
     */
    public List<CCustomerServicePeriodEmployee> selectCCustomerServicePeriodEmployeeList(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 新增账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    public int insertCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 修改账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    public int updateCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 批量删除账期服务人员
     * 
     * @param ids 需要删除的账期服务人员主键集合
     * @return 结果
     */
    public int deleteCCustomerServicePeriodEmployeeByIds(Long[] ids);

    /**
     * 删除账期服务人员信息
     * 
     * @param id 账期服务人员主键
     * @return 结果
     */
    public int deleteCCustomerServicePeriodEmployeeById(Long id);

    void deleteByPeriodId(Long periodId);

    void saveNewPeriodEmployee(Integer nowPeriod);

    void saveNewPeriodEmployeeByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd);

    List<CCustomerServicePeriodEmployee> selectByPeriodIdsAndEmployeeType(List<Long> customerServicePeriodMonthIdList, Integer employeeType);
}
