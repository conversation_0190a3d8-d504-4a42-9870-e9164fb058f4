package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.ServiceWaitItemType;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.CCustomerServiceWaitItem;
import com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO;

import java.util.List;

/**
 * 客户服务待确认事项Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-12
 */
public interface ICCustomerServiceWaitItemService extends IService<CCustomerServiceWaitItem>
{
    /**
     * 查询客户服务待确认事项
     * 
     * @param id 客户服务待确认事项主键
     * @return 客户服务待确认事项
     */
    public CCustomerServiceWaitItem selectCCustomerServiceWaitItemById(Long id);

    /**
     * 查询客户服务待确认事项列表
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 客户服务待确认事项集合
     */
    public List<CCustomerServiceWaitItem> selectCCustomerServiceWaitItemList(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 新增客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    public int insertCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 修改客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    public int updateCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 批量删除客户服务待确认事项
     * 
     * @param ids 需要删除的客户服务待确认事项主键集合
     * @return 结果
     */
    public int deleteCCustomerServiceWaitItemByIds(Long[] ids);

    /**
     * 删除客户服务待确认事项信息
     * 
     * @param id 客户服务待确认事项主键
     * @return 结果
     */
    public int deleteCCustomerServiceWaitItemById(Long id);

    void updateInValidByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType);

    void updateDoneStatusByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType);

    void updateInValidByCustomerServiceIdsAndItemTypes(List<Long> customerServiceIds, List<Integer> itemTypes);

    void updateDoneStatusByCustomerServiceIdsAndItemTypes(List<Long> customerServiceIds, List<Integer> itemTypes);

    IPage<CustomerServiceWaitItemDTO> customerServiceWaitItemList(Integer itemType, String keyWord, Integer jumpType, Long deptId, List<Long> queryDeptIds, String tagName, Integer tagIncludeFlag, String advisorDeptEmployeeName, String accountingDeptEmployeeName, Integer taxType, Integer pageNum, Integer pageSize);

    void notDispatch(CommonIdVO vo, Long deptId, Long userId, String operName);

    Integer selectCountByServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType);

    Integer selectCountByDeptIdsAndItemType(List<Long> deptIds, List<Long> queryDeptIds, Integer itemType);

    List<CCustomerServiceWaitItem> selectByDeptIdsAndItemType(List<Long> deptIds, Integer itemType);

    List<CCustomerServiceWaitItem> selectByDeptIdsAndItemType(List<Long> deptIds, List<Long> queryDeptIds, List<Integer> itemTypes);

    Integer selectNotDoneCountByCustomerServiceIdAndItemType(Long customerServiceId, ServiceWaitItemType serviceWaitItemType);

    List<CCustomerServiceWaitItem> selectNotDoneCountByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, ServiceWaitItemType serviceWaitItemType);
}
