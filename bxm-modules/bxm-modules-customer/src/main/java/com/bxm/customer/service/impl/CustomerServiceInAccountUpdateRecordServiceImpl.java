package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.CustomerServiceInAccountUpdateRecord;
import com.bxm.customer.mapper.CustomerServiceInAccountUpdateRecordMapper;
import com.bxm.customer.service.ICustomerServiceInAccountUpdateRecordService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class CustomerServiceInAccountUpdateRecordServiceImpl extends ServiceImpl<CustomerServiceInAccountUpdateRecordMapper, CustomerServiceInAccountUpdateRecord> implements ICustomerServiceInAccountUpdateRecordService {
    @Override
    @Async
    public void createInAccountUpdateRecord(Long customerServiceId) {
        Integer operateDate = Integer.parseInt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (count(new LambdaQueryWrapper<CustomerServiceInAccountUpdateRecord>()
                .eq(CustomerServiceInAccountUpdateRecord::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceInAccountUpdateRecord::getOperateDate, operateDate)) == 0) {
            save(new CustomerServiceInAccountUpdateRecord().setCustomerServiceId(customerServiceId)
                    .setOperateDate(operateDate));
        }
    }
}
