package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CCompany;

/**
 * 企业Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface ICCompanyService extends IService<CCompany>
{
    /**
     * 查询企业
     * 
     * @param id 企业主键
     * @return 企业
     */
    public CCompany selectCCompanyById(Long id);

    /**
     * 查询企业列表
     * 
     * @param cCompany 企业
     * @return 企业集合
     */
    public List<CCompany> selectCCompanyList(CCompany cCompany);

    /**
     * 新增企业
     * 
     * @param cCompany 企业
     * @return 结果
     */
    public int insertCCompany(CCompany cCompany);

    /**
     * 修改企业
     * 
     * @param cCompany 企业
     * @return 结果
     */
    public int updateCCompany(CCompany cCompany);

    /**
     * 批量删除企业
     * 
     * @param ids 需要删除的企业主键集合
     * @return 结果
     */
    public int deleteCCompanyByIds(Long[] ids);

    /**
     * 删除企业信息
     * 
     * @param id 企业主键
     * @return 结果
     */
    public int deleteCCompanyById(Long id);

    List<CCompany> selectByKeyWord(String keyWord);
}
