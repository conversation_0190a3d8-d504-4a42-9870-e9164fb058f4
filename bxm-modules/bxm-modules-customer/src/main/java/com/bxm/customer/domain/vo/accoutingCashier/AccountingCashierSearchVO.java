package com.bxm.customer.domain.vo.accoutingCashier;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierSearchVO extends BaseVO {

    @ApiModelProperty("账务类型，1-入账，2-流水，3-改账")
    private Integer type;

    @ApiModelProperty("客户查询关键字")
    private String keyWord;

    @ApiModelProperty("客户批量查询批次号")
    private String batchNo;

    @ApiModelProperty("服务标签是否包含，0-不包含，1-包含")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty("服务标签关键字")
    private String customerServiceTagName;

    @ApiModelProperty("服务纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("账期标签是否包含，0-不包含，1-包含")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期标签关键字")
    private String periodTagName;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty("业务公司id")
    private Long customerServiceBusinessDeptId;

    @ApiModelProperty("服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty("服务会计区域id")
    private Long customerServiceAccountingTopDeptId;

    @ApiModelProperty("服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty("账期会计区域id")
    private Long periodAccountingTopDeptId;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("账期筛选起点，yyyyMM")
    private Integer periodMin;

    @ApiModelProperty("账期筛选终点，yyyyMM")
    private Integer periodMax;

    @ApiModelProperty("是否有事项备注，0-否，1-是")
    private Integer hasMattersNotes;

    @ApiModelProperty("是否凭票入账，0-否，1-是")
    private Integer hasTicket;

    @ApiModelProperty("银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("是否有银行流水，0-无，1-有")
    private Integer hasBankPayment;

    @ApiModelProperty("是否有交付要求，0-无，1-有")
    private Integer hasDeliverRequire;

    @ApiModelProperty("材料介质，1-电子，2-纸质，3-无，4-其他，5-银企，多个用逗号隔开")
    private String materialMediaList;

    @ApiModelProperty("任务状态，0-空，1-待完成，2-待审核，3-已完结，4-已关闭，5-异常，多个用逗号隔开")
    private String taskStatusList;

    @ApiModelProperty("交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付，多个用逗号隔开")
    private String deliverStatusList;

    @ApiModelProperty("交付结果，1-正常，2-无账务，3-无需交付，4-异常，多个用逗号隔开")
    private String deliverResultList;

    @ApiModelProperty("完成时间筛选起点，yyyy-MM-dd")
    private String completeTimeStart;

    @ApiModelProperty("完成时间筛选终点，yyyy-MM-dd")
    private String completeTimeEnd;

    @ApiModelProperty("银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成，多个用,隔开")
    private String bankPaymentResultList;

    @ApiModelProperty("结账状态，1未入账、2已入账未结账、3已入账已结账，多个用,隔开")
    private String settleAccountStatusList;

    @ApiModelProperty("结账时间筛选起点，yyyy-MM-dd")
    private String endTimeStart;

    @ApiModelProperty("结账时间筛选终点，yyyy-MM-dd")
    private String endTimeEnd;

    @ApiModelProperty("报表状态是否平衡，0-否，1-是")
    private Integer tableStatusBalance;

    @ApiModelProperty("材料完整度，0-空，1-齐，2-缺，3-缺但齐，多个用逗号隔开")
    private String materialIntegrityList;

    @ApiModelProperty("材料补充状态，1-无需处理，2-待核对，3-已核对")
    private Integer materialSupplementStatus;

    @ApiModelProperty("ddl筛选起点，yyyy-MM-dd")
    private String ddlStart;

    @ApiModelProperty("ddl筛选终点，yyyy-MM-dd")
    private String ddlEnd;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
