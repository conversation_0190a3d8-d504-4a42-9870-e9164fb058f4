package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.OpenApiInvoiceData;
import com.bxm.customer.mapper.OpenApiInvoiceDataMapper;
import com.bxm.customer.service.IOpenApiInvoiceDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class OpenApiInvoiceDataServiceImpl extends ServiceImpl<OpenApiInvoiceDataMapper, OpenApiInvoiceData> implements IOpenApiInvoiceDataService {

    @Autowired
    private OpenApiInvoiceDataMapper openApiInvoiceDataMapper;

    @Override
    public OpenApiInvoiceData selectByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<OpenApiInvoiceData>()
                .eq(OpenApiInvoiceData::getSycRecordId, recordId), false);
    }

    @Override
    public List<OpenApiInvoiceData> selectListByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiInvoiceData>()
                .eq(OpenApiInvoiceData::getSycRecordId, recordId));
    }
}
