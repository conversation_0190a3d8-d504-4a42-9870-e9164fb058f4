package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.ServiceStatus;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.workOrder.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.WorkOrderFile;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderCountDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDetailDTO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.domain.vo.workOrder.*;
import com.bxm.customer.mapper.WorkOrderMapper;
import com.bxm.customer.properties.SpecialDeptIdProperties;
import com.bxm.customer.properties.SpecialEmployeeIdProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.YsbNoticeVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkOrderServiceImpl extends ServiceImpl<WorkOrderMapper, WorkOrder> implements IWorkOrderService {

    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Autowired
    private IWorkOrderFileService workOrderFileService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private SpecialDeptIdProperties specialDeptIdProperties;

    @Autowired
    private SpecialEmployeeIdProperties specialEmployeeIdProperties;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private IWorkOrderAccountingCashierRelationService workOrderAccountingCashierRelationService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private IWorkOrderTypeService workOrderTypeService;

    @Autowired
    private IBusinessDdlRecordService businessDdlRecordService;

    @Override
    public IPage<WorkOrderDTO> workOrderList(WorkOrderSearchVO vo, Long deptId) {
        IPage<WorkOrderDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        Long userId = vo.getUserId();
        UserDeptDTO userDept = remoteDeptService.workOrderUserDeptList(userId, deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        if (!StringUtils.isEmpty(vo.getLastOperTimeStart())) {
            vo.setLastOperTimeStart(vo.getLastOperTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getLastOperTimeEnd())) {
            vo.setLastOperTimeEnd(vo.getLastOperTimeEnd() + " 23:59:59");
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeStart())) {
            vo.setCreateTimeStart(vo.getCreateTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getCreateTimeEnd())) {
            vo.setCreateTimeEnd(vo.getCreateTimeEnd() + " 23:59:59");
        }
        List<WorkOrderDTO> data = workOrderMapper.workOrderList(result, vo, userDept);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> deptIds = Lists.newArrayList();
            if (data.stream().anyMatch(d -> !Objects.isNull(d.getUndertakeDeptId()))) {
                deptIds.addAll(data.stream().map(WorkOrderDTO::getUndertakeDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            if (data.stream().anyMatch(d -> !Objects.isNull(d.getCurrentDeptId()))) {
                deptIds.addAll(data.stream().map(WorkOrderDTO::getCurrentDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            deptIds.addAll(data.stream().map(WorkOrderDTO::getBusinessDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAccountingTopDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds = deptIds.stream().distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            List<Long> customerServiceIds = data.stream().map(WorkOrderDTO::getCustomerServiceId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, String> customerServiceNameMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceService.list(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, CCustomerService::getCustomerName));
            Map<Long, List<WorkOrderFile>> workOrderFileMap = workOrderFileService.list(new LambdaQueryWrapper<WorkOrderFile>()
                            .in(WorkOrderFile::getFileType, WorkOrderFileType.CREATE_FILE.getCode(), WorkOrderFileType.FOLLOW_FILE.getCode())
                            .in(WorkOrderFile::getWorkOrderId, data.stream().map(WorkOrderDTO::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(WorkOrderFile::getWorkOrderId));
            Map<Integer, String> workOrderTypeMap = workOrderTypeService.list(new LambdaQueryWrapper<com.bxm.customer.domain.WorkOrderType>()
                            .in(com.bxm.customer.domain.WorkOrderType::getWorkOrderType, data.stream().map(WorkOrderDTO::getWorkOrderType).distinct().collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(com.bxm.customer.domain.WorkOrderType::getWorkOrderType, com.bxm.customer.domain.WorkOrderType::getWorkOrderTypeName));
            data.forEach(d -> {
                List<WorkOrderFile> files = workOrderFileMap.get(d.getId());
                d.setWorkOrderTypeStr(workOrderTypeMap.getOrDefault(d.getWorkOrderType(), ""));
                d.setInitiateTime(d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setUndertakeDeptName(Objects.isNull(d.getUndertakeDeptId()) ? "" : deptNameMap.getOrDefault(d.getUndertakeDeptId(), ""));
                d.setStatusStr(WorkOrderStatus.getByCode(d.getStatus()).getDesc());
                d.setDdlStr(Objects.isNull(d.getDdl()) ? null : d.getDdl().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
                d.setFinishTimeStr(Objects.isNull(d.getFinishTime()) ? null : d.getFinishTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setLastOperTimeStr(Objects.isNull(d.getLastOperTime()) ? null : d.getLastOperTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setCurrentDeptName(Objects.isNull(d.getCurrentDeptId()) ? "" : deptNameMap.getOrDefault(d.getCurrentDeptId(), ""));
                d.setCurrentUserInfo(Objects.isNull(d.getCurrentUserType()) ? "" : WorkOrderObject.getByCode(d.getCurrentUserType()).getName() + "（" + (StringUtils.isEmpty(d.getCurrentUserNickname()) ? "-" : d.getCurrentUserNickname()) + "）");
                d.setCanFollowUp(getCanFollowUp(userDept, d.getCurrentUserId(), d.getCurrentDeptId(), userId));
                d.setCanTransmitInitiate(getCanTransmit(userDept, d.getInitiateUserId(), d.getInitiateDeptId(), userId));
                d.setCanTransmitUndertake(getCanTransmit(userDept, d.getUndertakeUserId(), d.getUndertakeDeptId(), userId));
                d.setCanConfirm(d.getCanTransmitInitiate());
                d.setCustomerName(Objects.isNull(d.getCustomerServiceId()) ? "" : customerServiceNameMap.getOrDefault(d.getCustomerServiceId(), ""));
                d.setFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() :
                        files.stream().map(file -> {
                            CommonFileVO commonFileVO = CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).deliverFileType(file.getFileType())
                                    .build();
                            if (Objects.equals(file.getFileType(), WorkOrderFileType.CREATE_FILE.getCode())) {
                                commonFileVO.setBaseDir(d.getTitle() + "/提交附件");
                            } else if (Objects.equals(file.getFileType(), WorkOrderFileType.FOLLOW_FILE.getCode())) {
                                commonFileVO.setBaseDir(d.getTitle() + "/跟进附件-" + file.getCreateBy() + "-" + file.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYYMMDDHHMMSS)));
                            }
                            return commonFileVO;
                        }).collect(Collectors.toList()));
                d.setFileCount((int) d.getFiles().stream().filter(f -> Objects.equals(f.getDeliverFileType(), WorkOrderFileType.CREATE_FILE.getCode())).count());
                d.setPeriodCount(calcPeriodCount(d.getPeriodStart(), d.getPeriodEnd()));
                d.setBusinessDeptName(Objects.isNull(d.getBusinessDeptId()) ? "" : deptNameMap.get(d.getBusinessDeptId()));
                d.setAdvisorDeptName(Objects.isNull(d.getAdvisorDeptId()) ? "" : deptNameMap.get(d.getAdvisorDeptId()));
                d.setAdvisorEmployeeName(Objects.isNull(d.getAdvisorDeptId()) ? "" : employeeMap.getOrDefault(d.getAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
                d.setAccountingTopDeptName(Objects.isNull(d.getAccountingTopDeptId()) ? "" : deptNameMap.get(d.getAccountingTopDeptId()));
                d.setAccountingDeptName(Objects.isNull(d.getAccountingDeptId()) ? "" : deptNameMap.get(d.getAccountingDeptId()));
                d.setAccountingEmployeeName(Objects.isNull(d.getAccountingDeptId()) ? "" : employeeMap.getOrDefault(d.getAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
            });
        }
        result.setRecords(data);
        return result;
    }

    @Override
    public List<WorkOrderDTO> remoteWorkOrderListForXm(WorkOrderSearchVO vo) {
        List<WorkOrderDTO> data = Lists.newArrayList();
        Long userId = vo.getUserId();
        if (Objects.isNull(userId)) {
            return data;
        }
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchByUserIds(Lists.newArrayList(userId)).getDataThrowException(false);
        List<Long> empDeptIds = ObjectUtils.isEmpty(employeeList) ? Lists.newArrayList() : employeeList.stream().map(SysEmployee::getDeptId).distinct().collect(Collectors.toList());
        if ("WAIT_UNDERTAKE".equalsIgnoreCase(vo.getSearchType())) {
            if (ObjectUtils.isEmpty(empDeptIds)) {
                return data;
            }
        }
        if (!"INITIATED".equalsIgnoreCase(vo.getSearchType())) {
            vo.setStatus(WorkOrderStatus.WAIT_END.getCode());
        }
        data = workOrderMapper.workOrderListForXm(vo, empDeptIds);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> deptIds = Lists.newArrayList();
            if (data.stream().anyMatch(d -> !Objects.isNull(d.getUndertakeDeptId()))) {
                deptIds.addAll(data.stream().map(WorkOrderDTO::getUndertakeDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            if (data.stream().anyMatch(d -> !Objects.isNull(d.getCurrentDeptId()))) {
                deptIds.addAll(data.stream().map(WorkOrderDTO::getCurrentDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            deptIds.addAll(data.stream().map(WorkOrderDTO::getBusinessDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAccountingTopDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(WorkOrderDTO::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds = deptIds.stream().distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            List<Long> customerServiceIds = data.stream().map(WorkOrderDTO::getCustomerServiceId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, String> customerServiceNameMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceService.list(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, CCustomerService::getCustomerName));
            Map<Integer, String> workOrderTypeMap = workOrderTypeService.list(new LambdaQueryWrapper<com.bxm.customer.domain.WorkOrderType>()
                            .in(com.bxm.customer.domain.WorkOrderType::getWorkOrderType, data.stream().map(WorkOrderDTO::getWorkOrderType).distinct().collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(com.bxm.customer.domain.WorkOrderType::getWorkOrderType, com.bxm.customer.domain.WorkOrderType::getWorkOrderTypeName));
            data.forEach(d -> {
                d.setWorkOrderTypeStr(workOrderTypeMap.getOrDefault(d.getWorkOrderType(), ""));
                d.setInitiateTime(d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setUndertakeDeptName(Objects.isNull(d.getUndertakeDeptId()) ? "" : deptNameMap.getOrDefault(d.getUndertakeDeptId(), ""));
                d.setStatusStr(WorkOrderStatus.getByCode(d.getStatus()).getDesc());
                d.setDdlStr(Objects.isNull(d.getDdl()) ? null : d.getDdl().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
                d.setFinishTimeStr(Objects.isNull(d.getFinishTime()) ? null : d.getFinishTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setLastOperTimeStr(Objects.isNull(d.getLastOperTime()) ? null : d.getLastOperTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setCurrentDeptName(Objects.isNull(d.getCurrentDeptId()) ? "" : deptNameMap.getOrDefault(d.getCurrentDeptId(), ""));
                d.setCurrentUserInfo(Objects.isNull(d.getCurrentUserType()) ? "" : WorkOrderObject.getByCode(d.getCurrentUserType()).getName() + "（" + (StringUtils.isEmpty(d.getCurrentUserNickname()) ? "-" : d.getCurrentUserNickname()) + "）");
                d.setCanFollowUp(getCanOperate(empDeptIds, d.getCurrentDeptId()));
                d.setCanTransmitInitiate(getCanOperate(empDeptIds, d.getInitiateDeptId()));
                d.setCanTransmitUndertake(getCanOperate(empDeptIds, d.getUndertakeDeptId()));
                d.setCanConfirm(d.getCanTransmitInitiate());
                d.setCustomerName(Objects.isNull(d.getCustomerServiceId()) ? "" : customerServiceNameMap.getOrDefault(d.getCustomerServiceId(), ""));
                d.setPeriodCount(calcPeriodCount(d.getPeriodStart(), d.getPeriodEnd()));
                d.setBusinessDeptName(Objects.isNull(d.getBusinessDeptId()) ? "" : deptNameMap.get(d.getBusinessDeptId()));
                d.setAdvisorDeptName(Objects.isNull(d.getAdvisorDeptId()) ? "" : deptNameMap.get(d.getAdvisorDeptId()));
                d.setAdvisorEmployeeName(Objects.isNull(d.getAdvisorDeptId()) ? "" : employeeMap.getOrDefault(d.getAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
                d.setAccountingTopDeptName(Objects.isNull(d.getAccountingTopDeptId()) ? "" : deptNameMap.get(d.getAccountingTopDeptId()));
                d.setAccountingDeptName(Objects.isNull(d.getAccountingDeptId()) ? "" : deptNameMap.get(d.getAccountingDeptId()));
                d.setAccountingEmployeeName(Objects.isNull(d.getAccountingDeptId()) ? "" : employeeMap.getOrDefault(d.getAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
            });
        }
        return data;
    }

    private Integer calcPeriodCount(Integer periodStart, Integer periodEnd) {
        // 1. 如果任一为空，返回0
        if (periodStart == null || periodEnd == null) {
            return 0;
        }

        // 2. 如果periodStart > periodEnd，返回0
        if (periodStart > periodEnd) {
            return 0;
        }

        // 3. 如果periodStart = periodEnd，返回1
        if (periodStart.equals(periodEnd)) {
            return 1;
        }

        // 4. 计算月份差
        int startYear = periodStart / 100;  // 获取开始年份
        int startMonth = periodStart % 100; // 获取开始月份
        int endYear = periodEnd / 100;      // 获取结束年份
        int endMonth = periodEnd % 100;     // 获取结束月份

        // 计算总月份数
        return (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
    }

    private Boolean getCanTransmit(UserDeptDTO userDept, Long dealUserId, Long deptId, Long userId) {
        if (userDept.getIsAdmin()) {
            return true;
        }
        if (ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return false;
        }
        return userDept.getDeptIds().contains(deptId);
//        if (Objects.isNull(dealUserId)) {
//            if (ObjectUtils.isEmpty(userDept.getDeptIds())) {
//                return false;
//            }
//            return userDept.getDeptIds().contains(deptId);
//        } else {
//            return Objects.equals(dealUserId, userId);
//        }
    }

    private Boolean getCanOperate(List<Long> employeeDeptIds, Long deptId) {
        if (ObjectUtils.isEmpty(employeeDeptIds)) {
            return false;
        }
        return employeeDeptIds.contains(deptId);
    }

    private Boolean getCanFollowUp(UserDeptDTO userDept, Long currentUserId, Long currentDeptId, Long userId) {
//        if (Objects.isNull(currentUserId)) {
//            if (userDept.getIsAdmin()) {
//                return true;
//            }
//            if (ObjectUtils.isEmpty(userDept.getDeptIds())) {
//                return false;
//            }
//            return userDept.getDeptIds().contains(currentDeptId);
//        } else {
//            return Objects.equals(currentUserId, userId);
//        }
        if (userDept.getIsAdmin()) {
            return true;
        }
        if (ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return false;
        }
        return userDept.getDeptIds().contains(currentDeptId);
    }

    @Override
    public WorkOrderDetailDTO workOrderDetail(Long id, Long deptId) {
        WorkOrder workOrder = getById(id);
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        com.bxm.customer.domain.WorkOrderType workOrderType = workOrderTypeService.getById(workOrder.getWorkOrderType());
        if (Objects.isNull(workOrderType)) {
            throw new ServiceException("工单类型不存在");
        }
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        SysDept initiateDept = Objects.isNull(workOrder.getInitiateDeptId()) ? null : deptMap.get(workOrder.getInitiateDeptId());
        SysDept undertakeDept = Objects.isNull(workOrder.getUndertakeDeptId()) ? null : deptMap.get(workOrder.getUndertakeDeptId());
        String initiateDeptPathStr = getDeptTreeName(initiateDept, deptMap);
        String undertakeDeptPathStr = getDeptTreeName(undertakeDept, deptMap);
        CCustomerService customerService = customerServiceService.getById(workOrder.getCustomerServiceId());
        List<Long> initiateDeptIdPath = getDeptIdPath(initiateDept);
        List<Long> undertakeDeptIdPath = getDeptIdPath(undertakeDept);
        WorkOrderDetailDTO detailDTO = WorkOrderDetailDTO.builder()
                .id(id)
                .title(workOrder.getTitle())
                .status(workOrder.getStatus())
                .statusStr(WorkOrderStatus.getByCode(workOrder.getStatus()).getDesc())
                .initiateInfo(initiateDeptPathStr + "（" + (StringUtils.isEmpty(workOrder.getInitiateUserNickname()) ? "-" : workOrder.getInitiateUserNickname()) + "）")
                .undertakeInfo(undertakeDeptPathStr + "（" + (StringUtils.isEmpty(workOrder.getUndertakeUserNickname()) ? "-" : workOrder.getUndertakeUserNickname()) + "）")
                .customerServiceId(workOrder.getCustomerServiceId())
                .customerName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : customerService.getCustomerName())
                .remark(workOrder.getRemark())
                .files(workOrderFileService.getByWorkOrderIdAndFileType(id, WorkOrderFileType.CREATE_FILE.getCode()))
                .ddl(Objects.isNull(workOrder.getDdl()) ? null : workOrder.getDdl().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)))
                .periodStart(Objects.isNull(workOrder.getPeriodStart()) ? null : DateUtils.periodToYeaMonth(workOrder.getPeriodStart()))
                .periodEnd(Objects.isNull(workOrder.getPeriodEnd()) ? null : DateUtils.periodToYeaMonth(workOrder.getPeriodEnd()))
                .currentDeptPath(Objects.isNull(workOrder.getCurrentUserType()) ? null : Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? initiateDeptIdPath : undertakeDeptIdPath)
                .currentEmployeeId(workOrder.getCurrentEmployeeId())
                .currentDeptType(Objects.isNull(workOrder.getCurrentUserType()) ? null : Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? (Objects.isNull(initiateDept) ? null : initiateDept.getDeptType()) : (Objects.isNull(undertakeDept) ? null : undertakeDept.getDeptType()))
                .initiateDeptPath(initiateDeptIdPath)
                .initiateEmployeeId(workOrder.getInitiateEmployeeId())
                .initiateDeptType(Objects.isNull(initiateDept) ? null : initiateDept.getDeptType())
                .undertakeDeptPath(undertakeDeptIdPath)
                .undertakeEmployeeId(workOrder.getUndertakeEmployeeId())
                .undertakeDeptType(Objects.isNull(undertakeDept) ? null : undertakeDept.getDeptType())
                .build();
        if (workOrderType.getIsShowAccountingCashierDeliver()) {
            detailDTO.setAccountingCashierList(workOrderAccountingCashierRelationService.selectAccountingCashierList(workOrder));
        }
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        detailDTO.setIsInitiate(!Objects.isNull(userDept) && (userDept.getIsAdmin() || userDept.getDeptIds().contains(workOrder.getInitiateDeptId())));
        detailDTO.setIsShowAccountingCashierDeliver(workOrderType.getIsShowAccountingCashierDeliver());
        return detailDTO;
    }

    private String getDeptTreeName(SysDept sysDept, Map<Long, SysDept> deptMap) {
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        String[] split = sysDept.getAncestors().split(",");
        for (String s : split) {
            if (Objects.equals("0", s)) {
                continue;
            }
            SysDept dept = deptMap.get(Long.valueOf(s));
            if (Objects.isNull(dept) || !Objects.equals("0", dept.getDelFlag())) {
                continue;
            }
            sb.append(dept.getDeptName()).append("-");
        }
        sb.append(sysDept.getDeptName());
        return sb.toString();
    }

    private List<Long> getDeptIdPath(SysDept sysDept) {
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            return Lists.newArrayList();
        }
        List<Long> ids = Arrays.stream(sysDept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList());
        ids.remove(0L);
        ids.add(sysDept.getDeptId());
        return ids;
    }

    @Override
    @Transactional
    public void createWorkOrder(WorkOrderCreateVO vo, Long deptId) {
        com.bxm.customer.domain.WorkOrderType workOrderType = workOrderTypeService.selectByWorkOrderType(vo.getWorkOrderType());
        if (Objects.isNull(workOrderType)) {
            throw new ServiceException("工单类型不存在");
        }
        if (workOrderType.getIsNeedCustomer() && Objects.isNull(vo.getCustomerServiceId())) {
            throw new ServiceException("请选择客户");
        }
        if (workOrderType.getIsNeedPeriod() && (Objects.isNull(vo.getPeriodStart()) || Objects.isNull(vo.getPeriodEnd()))) {
            throw new ServiceException("账期范围不能为空");
        }
        if (workOrderType.getIsNeedRemark() && StringUtils.isEmpty(vo.getRemark())) {
            throw new ServiceException("请填写工单备注");
        }
        checkDdl(vo.getDdl());
        CCustomerService customerService = null;
        if (!Objects.isNull(vo.getCustomerServiceId())) {
            customerService = customerServiceService.getById(vo.getCustomerServiceId());
            if (Objects.isNull(customerService) || customerService.getIsDel()) {
                throw new ServiceException("客户不存在");
            }
            if (Objects.isNull(customerService.getAdvisorDeptId())) {
                throw new ServiceException("该客户还未分配顾问");
            }
            if (Objects.isNull(customerService.getAccountingDeptId())) {
                throw new ServiceException("该客户还未分配会计");
            }
        }
        if (Objects.equals(vo.getWorkOrderType(), WorkOrderType.COLLECT_DEBT.getCode())) {
            customerServicePeriodMonthService.checkCanCollectDebtV2(vo.getCustomerServiceId(), vo.getPeriodStart(), vo.getPeriodEnd());
        }
        Long userId = SecurityUtils.getUserId();
        String nickName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        SysEmployee employee;
        if (ObjectUtils.isEmpty(employees)) {
            employee = null;
        } else {
            if (employees.stream().anyMatch(e -> Objects.equals(e.getDeptId(), vo.getDeptId()))) {
                employee = employees.stream().filter(e -> Objects.equals(e.getDeptId(), vo.getDeptId())).findFirst().get();
            } else {
                employee = employees.get(0);
            }
        }
        String operName = Objects.isNull(employee) ? nickName : employee.getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "新建";
        createWorkOrder(vo, userId, deptId, operName, Objects.isNull(employee) ? null : employee.getEmployeeId(), operTime, operType, customerService, workOrderType);
    }

    private void checkDdl(String ddl) {
        if (!StringUtils.isEmpty(ddl)) {
            LocalDate ddlDate = LocalDate.parse(ddl, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD));
            LocalDate now = LocalDate.now();
            if (!ddlDate.isAfter(now)) {
                throw new ServiceException("请修改DDL时间，最早只能选到明天");
            }
            if (!remoteDeptService.checkDateIsWorkDay(ddlDate.getYear(), ddlDate.getMonthValue(), ddlDate.getDayOfMonth(), SecurityConstants.INNER).getDataThrowException()) {
                throw new ServiceException("DDL必须为工作日");
            }
        }
    }

    private void createWorkOrder(WorkOrderCreateVO vo, Long userId, Long deptId, String operName, Long employeeId, LocalDateTime operTime, String operType, CCustomerService customerService, com.bxm.customer.domain.WorkOrderType workOrderType) {
        WorkOrder workOrder = new WorkOrder().setCustomerServiceId(vo.getCustomerServiceId())
                .setTitle(vo.getTitle())
                .setWorkOrderType(vo.getWorkOrderType())
                .setRemark(vo.getRemark())
                .setDdl(StringUtils.isEmpty(vo.getDdl()) ? null : LocalDate.parse(vo.getDdl(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)))
                .setPeriodStart(vo.getPeriodStart())
                .setPeriodEnd(vo.getPeriodEnd())
                .setInitiateUserId(userId)
                .setInitiateUserNickname(operName)
                .setInitiateEmployeeId(employeeId)
                .setInitiateEmployeeName(operName)
                .setInitiateDeptId(vo.getDeptId())
                .setStatus(WorkOrderStatus.WAIT_END.getCode())
                .setLastOperName(operName)
                .setLastOperTime(operTime)
                .setLastOperType(operType);
        setWorkOrderUndertakeAndCurrentV2(workOrder, customerService, deptId, workOrderType);
        save(workOrder);
        if (Objects.equals(workOrder.getWorkOrderType(), WorkOrderType.CHANGE_ACCOUNT.getCode())) {
            // 如果是改账，需要把账期范围内的所有账务交付单的当前状态记录一下
            workOrderAccountingCashierRelationService.saveWorkOrderAccountingCashierRelation(workOrder.getId(), workOrder.getCustomerServiceId(), workOrder.getPeriodStart(), workOrder.getPeriodEnd());
        }
        if (!ObjectUtils.isEmpty(vo.getFiles())) {
            workOrderFileService.saveBatch(vo.getFiles().stream().map(file -> new WorkOrderFile().setWorkOrderId(workOrder.getId()).setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName()).setFileType(WorkOrderFileType.CREATE_FILE.getCode())).collect(Collectors.toList()));
        }
        if (!Objects.isNull(workOrder.getUndertakeDeptId())) {
            SysDept undertakeDept = remoteDeptService.getDeptInfo(workOrder.getUndertakeDeptId()).getDataThrowException();
            if (!Objects.isNull(workOrder.getUndertakeUserId())) {
                // 发送系统通知
                String content = Objects.isNull(customerService) ? String.format("您有新的工单：%s", vo.getTitle()) :
                        String.format("您有新的工单：【%s】%s", customerService.getCustomerName(), vo.getTitle());
                asyncService.asyncSendMessage(workOrder.getUndertakeUserId(), workOrder.getUndertakeEmployeeId(),
                        workOrder.getUndertakeEmployeeName(), workOrder.getUndertakeDeptId(),
                        Objects.isNull(undertakeDept) ? "" : undertakeDept.getDeptName(),
                        content, operName);
                asyncService.sendYsbNotice(workOrder.getUndertakeUserId(), workOrder.getUndertakeEmployeeName(), userId, operName, content, "新工单消息", workOrder.getId());
            } else {
                // 给组下所有员工发
                List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(workOrder.getUndertakeDeptId()).getDataThrowException(false);
                if (!ObjectUtils.isEmpty(employeeList)) {
                    String content = Objects.isNull(customerService) ? String.format("您有新的工单：%s", vo.getTitle()) :
                            String.format("您有新的工单：【%s】%s", customerService.getCustomerName(), vo.getTitle());
                    for (SysEmployee e : employeeList) {
                        asyncService.asyncSendMessage(e.getUserId(), e.getEmployeeId(),
                                e.getEmployeeName(), workOrder.getUndertakeDeptId(),
                                Objects.isNull(undertakeDept) ? "" : undertakeDept.getDeptName(),
                                content, operName);
                        asyncService.sendYsbNotice(e.getUserId(), e.getEmployeeName(), userId, operName, content, "新工单消息", workOrder.getId());
                    }
                }
            }
        }
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        SysDept undertakeDept = Objects.isNull(workOrder.getUndertakeDeptId()) ? null : deptMap.get(workOrder.getUndertakeDeptId());
        String undertakeDeptPathStr = getDeptTreeName(undertakeDept, deptMap);
        Map<String, String> operContent = Maps.newLinkedHashMap();
        operContent.put("标题", vo.getTitle());
        operContent.put("承接方", undertakeDeptPathStr + "（" + (StringUtils.isEmpty(workOrder.getUndertakeUserNickname()) ? "-" : workOrder.getUndertakeUserNickname()) + "）");
        if (!StringUtils.isEmpty(vo.getDdl())) {
            operContent.put("DDL", vo.getDdl());
        }
        if (Objects.equals(workOrder.getWorkOrderType(), WorkOrderType.BIND_BOOK.getCode()) && !Objects.isNull(customerService)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(Collections.singletonList(customerService.getId()), TagBusinessType.CUSTOMER_SERVICE);
            List<TagDTO> tagList = tagMap.getOrDefault(customerService.getId(), Lists.newArrayList());
            operContent.put("账套系统", tagList.stream().map(TagDTO::getId).anyMatch(t -> Objects.equals(t, specialTagProperties.getYq())) ? "YQ" : "YJZ");
            operContent.put("是否凭票入账", tagList.stream().map(TagDTO::getId).anyMatch(t -> Objects.equals(t, specialTagProperties.getPprz())) ? "是" : "否");
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(workOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(userId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(workOrder.getId(), BusinessLogBusinessType.WORK_ORDER, userId, vo.getDdl());
        }
    }

    private void setWorkOrderUndertakeAndCurrentV2(WorkOrder workOrder, CCustomerService customerService, Long deptId, com.bxm.customer.domain.WorkOrderType workOrderType) {
        if (Objects.equals(workOrderType.getDispatchType(), WorkOrderDispatchType.ASSIGN_TO_SPECIFIC_ORGANIZATION.getCode())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            workOrder.setUndertakeDeptId(workOrderType.getDispatchDeptId());
            workOrder.setCurrentDeptId(workOrderType.getDispatchDeptId());
            if (!Objects.isNull(workOrderType.getDispatchDeptId())) {
                List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(workOrderType.getDispatchDeptId()).getDataThrowException();
                if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                    SysEmployee employee = employees.get(0);
                    SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                    workOrder.setUndertakeUserId(employee.getUserId());
                    workOrder.setUndertakeUserNickname(user.getNickName());
                    workOrder.setUndertakeEmployeeId(employee.getEmployeeId());
                    workOrder.setUndertakeEmployeeName(employee.getEmployeeName());
                    workOrder.setCurrentUserId(employee.getUserId());
                    workOrder.setCurrentUserNickname(user.getNickName());
                    workOrder.setCurrentEmployeeId(employee.getEmployeeId());
                    workOrder.setCurrentEmployeeName(employee.getEmployeeName());
                }
            }
        } else if (Objects.equals(workOrderType.getDispatchType(), WorkOrderDispatchType.ASSIGN_TO_SERVICE_ACCOUNTANT.getCode())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getAccountingDeptId())) {
                workOrder.setUndertakeDeptId(customerService.getAccountingDeptId());
                workOrder.setCurrentDeptId(customerService.getAccountingDeptId());
                List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAccountingDeptId()).getDataThrowException();
                if (!ObjectUtils.isEmpty(accountingEmployees) && accountingEmployees.size() == 1) {
                    SysEmployee accountingEmployee = accountingEmployees.get(0);
                    SysUser user = remoteUserService.getByUserId(accountingEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                    workOrder.setUndertakeUserId(accountingEmployee.getUserId());
                    workOrder.setUndertakeUserNickname(user.getNickName());
                    workOrder.setUndertakeEmployeeId(accountingEmployee.getEmployeeId());
                    workOrder.setUndertakeEmployeeName(accountingEmployee.getEmployeeName());
                    workOrder.setCurrentUserId(accountingEmployee.getUserId());
                    workOrder.setCurrentUserNickname(user.getNickName());
                    workOrder.setCurrentEmployeeId(accountingEmployee.getEmployeeId());
                    workOrder.setCurrentEmployeeName(accountingEmployee.getEmployeeName());
                }
            }
        } else if (Objects.equals(workOrderType.getDispatchType(), WorkOrderDispatchType.ASSIGN_TO_SERVICE_ADVISOR.getCode())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getAdvisorDeptId())) {
                workOrder.setUndertakeDeptId(customerService.getAdvisorDeptId());
                workOrder.setCurrentDeptId(customerService.getAdvisorDeptId());
                List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException();
                if (!ObjectUtils.isEmpty(advisorEmployees) && advisorEmployees.size() == 1) {
                    SysEmployee advisorEmployee = advisorEmployees.get(0);
                    SysUser user = remoteUserService.getByUserId(advisorEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                    workOrder.setUndertakeUserId(advisorEmployee.getUserId());
                    workOrder.setUndertakeUserNickname(user.getNickName());
                    workOrder.setUndertakeEmployeeId(advisorEmployee.getEmployeeId());
                    workOrder.setUndertakeEmployeeName(advisorEmployee.getEmployeeName());
                    workOrder.setCurrentUserId(advisorEmployee.getUserId());
                    workOrder.setCurrentUserNickname(user.getNickName());
                    workOrder.setCurrentEmployeeId(advisorEmployee.getEmployeeId());
                    workOrder.setCurrentEmployeeName(advisorEmployee.getEmployeeName());
                }
            }
        } else if (Objects.equals(workOrderType.getDispatchType(), WorkOrderDispatchType.ASSIGN_TO_INITIATOR.getCode())) {
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (Objects.equals(sysDept.getDeptType(), 1)) {
                // 业务公司
                if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getAccountingDeptId())) {
                    workOrder.setUndertakeDeptId(customerService.getAccountingDeptId());
                    workOrder.setCurrentDeptId(customerService.getAccountingDeptId());
                    List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAccountingDeptId()).getDataThrowException();
                    if (!ObjectUtils.isEmpty(accountingEmployees) && accountingEmployees.size() == 1) {
                        SysEmployee accountingEmployee = accountingEmployees.get(0);
                        SysUser user = remoteUserService.getByUserId(accountingEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                        workOrder.setUndertakeUserId(accountingEmployee.getUserId());
                        workOrder.setUndertakeUserNickname(user.getNickName());
                        workOrder.setUndertakeEmployeeId(accountingEmployee.getEmployeeId());
                        workOrder.setUndertakeEmployeeName(accountingEmployee.getEmployeeName());
                        workOrder.setCurrentUserId(accountingEmployee.getUserId());
                        workOrder.setCurrentUserNickname(user.getNickName());
                        workOrder.setCurrentEmployeeId(accountingEmployee.getEmployeeId());
                        workOrder.setCurrentEmployeeName(accountingEmployee.getEmployeeName());
                    }
                }
            } else {
                // 工厂
                if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getAdvisorDeptId())) {
                    workOrder.setUndertakeDeptId(customerService.getAdvisorDeptId());
                    workOrder.setCurrentDeptId(customerService.getAdvisorDeptId());
                    List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException();
                    if (!ObjectUtils.isEmpty(advisorEmployees) && advisorEmployees.size() == 1) {
                        SysEmployee advisorEmployee = advisorEmployees.get(0);
                        SysUser user = remoteUserService.getByUserId(advisorEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                        workOrder.setUndertakeUserId(advisorEmployee.getUserId());
                        workOrder.setUndertakeUserNickname(user.getNickName());
                        workOrder.setUndertakeEmployeeId(advisorEmployee.getEmployeeId());
                        workOrder.setUndertakeEmployeeName(advisorEmployee.getEmployeeName());
                        workOrder.setCurrentUserId(advisorEmployee.getUserId());
                        workOrder.setCurrentUserNickname(user.getNickName());
                        workOrder.setCurrentEmployeeId(advisorEmployee.getEmployeeId());
                        workOrder.setCurrentEmployeeName(advisorEmployee.getEmployeeName());
                    }
                }
            }
        }
    }

    private void setWorkOrderUndertakeAndCurrent(WorkOrder workOrder, CCustomerService customerService, Long deptId) {
        if (WorkOrderType.zdzTypes().contains(workOrder.getWorkOrderType())) {
            Long zdzEmployeeId = specialEmployeeIdProperties.getZdz();
            SysEmployee employee = Objects.isNull(zdzEmployeeId) ? null : remoteEmployeeService.getEmployeeInfo(zdzEmployeeId).getDataThrowException();
            SysUser user = Objects.isNull(employee) ? null : remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
            if (Objects.isNull(employee)) {
                List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(specialDeptIdProperties.getZdz()).getDataThrowException();
                if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                    employee = employees.get(0);
                    user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                }
            }
            workOrder.setUndertakeDeptId(specialDeptIdProperties.getZdz())
                    .setUndertakeUserId(Objects.isNull(employee) ? null : employee.getUserId())
                    .setUndertakeUserNickname(Objects.isNull(user) ? null : user.getNickName())
                    .setUndertakeEmployeeId(Objects.isNull(employee) ? null : employee.getEmployeeId())
                    .setUndertakeEmployeeName(Objects.isNull(employee) ? null : employee.getEmployeeName())
                    .setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode())
                    .setCurrentDeptId(specialDeptIdProperties.getZdz())
                    .setCurrentUserId(Objects.isNull(employee) ? null : employee.getUserId())
                    .setCurrentUserNickname(Objects.isNull(user) ? null : user.getNickName())
                    .setCurrentEmployeeId(Objects.isNull(employee) ? null : employee.getEmployeeId())
                    .setCurrentEmployeeName(Objects.isNull(employee) ? null : employee.getEmployeeName());
        } else if (WorkOrderType.clzTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            workOrder.setUndertakeDeptId(specialDeptIdProperties.getClz());
            workOrder.setCurrentDeptId(specialDeptIdProperties.getClz());
            List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(specialDeptIdProperties.getClz()).getDataThrowException();
            if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                SysEmployee employee = employees.get(0);
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                workOrder.setUndertakeUserId(employee.getUserId());
                workOrder.setUndertakeUserNickname(user.getNickName());
                workOrder.setUndertakeEmployeeId(employee.getEmployeeId());
                workOrder.setUndertakeEmployeeName(employee.getEmployeeName());
                workOrder.setCurrentUserId(employee.getUserId());
                workOrder.setCurrentUserNickname(user.getNickName());
                workOrder.setCurrentEmployeeId(employee.getEmployeeId());
                workOrder.setCurrentEmployeeName(employee.getEmployeeName());
            }
        } else if (WorkOrderType.cpyyTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            workOrder.setUndertakeDeptId(specialDeptIdProperties.getCpyy());
            workOrder.setCurrentDeptId(specialDeptIdProperties.getCpyy());
            List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(specialDeptIdProperties.getCpyy()).getDataThrowException();
            if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                SysEmployee employee = employees.get(0);
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                workOrder.setUndertakeUserId(employee.getUserId());
                workOrder.setUndertakeUserNickname(user.getNickName());
                workOrder.setUndertakeEmployeeId(employee.getEmployeeId());
                workOrder.setUndertakeEmployeeName(employee.getEmployeeName());
                workOrder.setCurrentUserId(employee.getUserId());
                workOrder.setCurrentUserNickname(user.getNickName());
                workOrder.setCurrentEmployeeId(employee.getEmployeeId());
                workOrder.setCurrentEmployeeName(employee.getEmployeeName());
            }
        } else if (WorkOrderType.hdzxTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            workOrder.setUndertakeDeptId(specialDeptIdProperties.getHdzx());
            workOrder.setCurrentDeptId(specialDeptIdProperties.getHdzx());
            List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(specialDeptIdProperties.getHdzx()).getDataThrowException();
            if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                SysEmployee employee = employees.get(0);
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                workOrder.setUndertakeUserId(employee.getUserId());
                workOrder.setUndertakeUserNickname(user.getNickName());
                workOrder.setUndertakeEmployeeId(employee.getEmployeeId());
                workOrder.setUndertakeEmployeeName(employee.getEmployeeName());
                workOrder.setCurrentUserId(employee.getUserId());
                workOrder.setCurrentUserNickname(user.getNickName());
                workOrder.setCurrentEmployeeId(employee.getEmployeeId());
                workOrder.setCurrentEmployeeName(employee.getEmployeeName());
            }
        } else if (WorkOrderType.kfzTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            workOrder.setUndertakeDeptId(specialDeptIdProperties.getKfz());
            workOrder.setCurrentDeptId(specialDeptIdProperties.getKfz());
            List<SysEmployee> employees = remoteEmployeeService.getEmployeeListByDeptId(specialDeptIdProperties.getKfz()).getDataThrowException();
            if (!ObjectUtils.isEmpty(employees) && employees.size() == 1) {
                SysEmployee employee = employees.get(0);
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                workOrder.setUndertakeUserId(employee.getUserId());
                workOrder.setUndertakeUserNickname(user.getNickName());
                workOrder.setUndertakeEmployeeId(employee.getEmployeeId());
                workOrder.setUndertakeEmployeeName(employee.getEmployeeName());
                workOrder.setCurrentUserId(employee.getUserId());
                workOrder.setCurrentUserNickname(user.getNickName());
                workOrder.setCurrentEmployeeId(employee.getEmployeeId());
                workOrder.setCurrentEmployeeName(employee.getEmployeeName());
            }
        } else if (WorkOrderType.customerAccountingTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (!Objects.isNull(customerService.getAccountingDeptId())) {
                workOrder.setUndertakeDeptId(customerService.getAccountingDeptId());
                workOrder.setCurrentDeptId(customerService.getAccountingDeptId());
                List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAccountingDeptId()).getDataThrowException();
                if (!ObjectUtils.isEmpty(accountingEmployees) && accountingEmployees.size() == 1) {
                    SysEmployee accountingEmployee = accountingEmployees.get(0);
                    SysUser user = remoteUserService.getByUserId(accountingEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                    workOrder.setUndertakeUserId(accountingEmployee.getUserId());
                    workOrder.setUndertakeUserNickname(user.getNickName());
                    workOrder.setUndertakeEmployeeId(accountingEmployee.getEmployeeId());
                    workOrder.setUndertakeEmployeeName(accountingEmployee.getEmployeeName());
                    workOrder.setCurrentUserId(accountingEmployee.getUserId());
                    workOrder.setCurrentUserNickname(user.getNickName());
                    workOrder.setCurrentEmployeeId(accountingEmployee.getEmployeeId());
                    workOrder.setCurrentEmployeeName(accountingEmployee.getEmployeeName());
                }
            }
        } else if (WorkOrderType.customerAdvisorTypes().contains(workOrder.getWorkOrderType())) {
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (!Objects.isNull(customerService.getAdvisorDeptId())) {
                workOrder.setUndertakeDeptId(customerService.getAdvisorDeptId());
                workOrder.setCurrentDeptId(customerService.getAdvisorDeptId());
                List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException();
                if (!ObjectUtils.isEmpty(advisorEmployees) && advisorEmployees.size() == 1) {
                    SysEmployee advisorEmployee = advisorEmployees.get(0);
                    SysUser user = remoteUserService.getByUserId(advisorEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                    workOrder.setUndertakeUserId(advisorEmployee.getUserId());
                    workOrder.setUndertakeUserNickname(user.getNickName());
                    workOrder.setUndertakeEmployeeId(advisorEmployee.getEmployeeId());
                    workOrder.setUndertakeEmployeeName(advisorEmployee.getEmployeeName());
                    workOrder.setCurrentUserId(advisorEmployee.getUserId());
                    workOrder.setCurrentUserNickname(user.getNickName());
                    workOrder.setCurrentEmployeeId(advisorEmployee.getEmployeeId());
                    workOrder.setCurrentEmployeeName(advisorEmployee.getEmployeeName());
                }
            }
        } else if (WorkOrderType.customerAccountingAdvisorTypes().contains(workOrder.getWorkOrderType())) {
            SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
            workOrder.setCurrentUserType(WorkOrderObject.UNDERTAKE_PARTY.getCode());
            if (Objects.equals(sysDept.getDeptType(), 1)) {
                // 业务公司
                if (!Objects.isNull(customerService.getAccountingDeptId())) {
                    workOrder.setUndertakeDeptId(customerService.getAccountingDeptId());
                    workOrder.setCurrentDeptId(customerService.getAccountingDeptId());
                    List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAccountingDeptId()).getDataThrowException();
                    if (!ObjectUtils.isEmpty(accountingEmployees) && accountingEmployees.size() == 1) {
                        SysEmployee accountingEmployee = accountingEmployees.get(0);
                        SysUser user = remoteUserService.getByUserId(accountingEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                        workOrder.setUndertakeUserId(accountingEmployee.getUserId());
                        workOrder.setUndertakeUserNickname(user.getNickName());
                        workOrder.setUndertakeEmployeeId(accountingEmployee.getEmployeeId());
                        workOrder.setUndertakeEmployeeName(accountingEmployee.getEmployeeName());
                        workOrder.setCurrentUserId(accountingEmployee.getUserId());
                        workOrder.setCurrentUserNickname(user.getNickName());
                        workOrder.setCurrentEmployeeId(accountingEmployee.getEmployeeId());
                        workOrder.setCurrentEmployeeName(accountingEmployee.getEmployeeName());
                    }
                }
            } else {
                // 工厂
                if (!Objects.isNull(customerService.getAdvisorDeptId())) {
                    workOrder.setUndertakeDeptId(customerService.getAdvisorDeptId());
                    workOrder.setCurrentDeptId(customerService.getAdvisorDeptId());
                    List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(customerService.getAdvisorDeptId()).getDataThrowException();
                    if (!ObjectUtils.isEmpty(advisorEmployees) && advisorEmployees.size() == 1) {
                        SysEmployee advisorEmployee = advisorEmployees.get(0);
                        SysUser user = remoteUserService.getByUserId(advisorEmployee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                        workOrder.setUndertakeUserId(advisorEmployee.getUserId());
                        workOrder.setUndertakeUserNickname(user.getNickName());
                        workOrder.setUndertakeEmployeeId(advisorEmployee.getEmployeeId());
                        workOrder.setUndertakeEmployeeName(advisorEmployee.getEmployeeName());
                        workOrder.setCurrentUserId(advisorEmployee.getUserId());
                        workOrder.setCurrentUserNickname(user.getNickName());
                        workOrder.setCurrentEmployeeId(advisorEmployee.getEmployeeId());
                        workOrder.setCurrentEmployeeName(advisorEmployee.getEmployeeName());
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void followUp(WorkOrderFollowUpVO vo, Long deptId) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanFollowUpStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待完结状态下可跟进");
        }
        if (Objects.isNull(workOrder.getCurrentDeptId())) {
            throw new ServiceException("当前处理方为空");
        }
        Long currentUserId = SecurityUtils.getUserId();
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(currentUserId, deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && !userDeptDTO.getDeptIds().contains(workOrder.getCurrentDeptId())) {
            throw new ServiceException("只有当前处理方组织可操作");
        }
        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) && vo.getIsFinish() == 0) {
            checkDdl(vo.getDdl());
        }
//        if (!Objects.isNull(workOrder.getCurrentUserId())) {
//            if (!Objects.equals(currentUserId, workOrder.getCurrentUserId())) {
//                throw new ServiceException("只有当前处理人可操作");
//            }
//        } else {
//            UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(currentUserId, deptId).getDataThrowException();
//            if (!userDeptDTO.getIsAdmin() && !userDeptDTO.getDeptIds().contains(workOrder.getUndertakeDeptId())) {
//                throw new ServiceException("只有当前处理方组织可操作");
//            }
//        }
        if (vo.getIsFinish() == 0 && Objects.isNull(vo.getFollowUpType())) {
            throw new ServiceException("跟进类型不能为空");
        }
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, currentUserId).getDataThrowException();
        SysEmployee currentEmployee = ObjectUtils.isEmpty(employees) ? null : employees.get(0);
        String operName = Objects.isNull(currentEmployee) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : currentEmployee.getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType;
        if (vo.getIsFinish() == 1) {
            operType = "完结工单";
        } else {
            if (vo.getFollowUpType() == 1) {
                operType = "跟进工单";
            } else {
                operType = "转交工单";
            }
        }
        String currentUserNickName = "";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType)
                .set(WorkOrder::getLastFollowUpName, operName)
                .set(WorkOrder::getLastFollowUpTime, operTime);
        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) && vo.getIsFinish() == 0) {
            updateWrapper.set(WorkOrder::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl());
        }
        if (vo.getIsFinish() == 1) {
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED.getCode())
                        .set(WorkOrder::getFinishTime, operTime)
                        .set(WorkOrder::getCurrentUserType, null)
                        .set(WorkOrder::getCurrentDeptId, null)
                        .set(WorkOrder::getCurrentUserId, null)
                        .set(WorkOrder::getCurrentUserNickname, null)
                        .set(WorkOrder::getCurrentEmployeeId, null)
                        .set(WorkOrder::getCurrentEmployeeName, null);
            } else {
                updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED_CONFIRM.getCode())
                        .set(WorkOrder::getFinishTime, operTime)
                        .set(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                        .set(WorkOrder::getCurrentDeptId, workOrder.getInitiateDeptId())
                        .set(WorkOrder::getCurrentUserId, workOrder.getInitiateUserId())
                        .set(WorkOrder::getCurrentUserNickname, workOrder.getInitiateUserNickname())
                        .set(WorkOrder::getCurrentEmployeeId, workOrder.getInitiateEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, workOrder.getInitiateEmployeeName());
            }
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode()) && Objects.isNull(workOrder.getUndertakeUserId())) {
                updateWrapper.set(WorkOrder::getUndertakeUserId, currentUserId)
                        .set(WorkOrder::getUndertakeUserNickname, SecurityUtils.getLoginUser().getSysUser().getNickName())
                        .set(WorkOrder::getUndertakeEmployeeId, Objects.isNull(currentEmployee) ? null : currentEmployee.getEmployeeId())
                        .set(WorkOrder::getUndertakeEmployeeName, Objects.isNull(currentEmployee) ? "" : currentEmployee.getEmployeeName());
            }
        } else {
            if (vo.getFollowUpType() == 1) {
                // 变更处理方
                if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
                    // 如果当前是承接方，则当前修改为发起方
                    updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                            .set(WorkOrder::getCurrentDeptId, workOrder.getInitiateDeptId())
                            .set(WorkOrder::getCurrentUserId, workOrder.getInitiateUserId())
                            .set(WorkOrder::getCurrentUserNickname, workOrder.getInitiateUserNickname())
                            .set(WorkOrder::getCurrentEmployeeId, workOrder.getInitiateEmployeeId())
                            .set(WorkOrder::getCurrentEmployeeName, workOrder.getInitiateEmployeeName());
                    if (Objects.isNull(workOrder.getUndertakeUserId())) {
                        updateWrapper.set(WorkOrder::getUndertakeUserId, currentUserId)
                                .set(WorkOrder::getUndertakeUserNickname, SecurityUtils.getLoginUser().getSysUser().getNickName())
                                .set(WorkOrder::getUndertakeEmployeeId, Objects.isNull(currentEmployee) ? null : currentEmployee.getEmployeeId())
                                .set(WorkOrder::getUndertakeEmployeeName, Objects.isNull(currentEmployee) ? "" : currentEmployee.getEmployeeName());
                    }
                } else {
                    // 如果当前是发起方，则当前修改为承接方
                    updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.UNDERTAKE_PARTY.getCode())
                            .set(WorkOrder::getCurrentDeptId, workOrder.getUndertakeDeptId())
                            .set(WorkOrder::getCurrentUserId, workOrder.getUndertakeUserId())
                            .set(WorkOrder::getCurrentUserNickname, workOrder.getUndertakeUserNickname())
                            .set(WorkOrder::getCurrentEmployeeId, workOrder.getUndertakeEmployeeId())
                            .set(WorkOrder::getCurrentEmployeeName, workOrder.getUndertakeEmployeeName());
                }
            } else {
                // 转交他人
                SysEmployee employee = remoteEmployeeService.getEmployeeInfo(vo.getEmployeeId()).getDataThrowException();
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
                if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                    updateWrapper.set(WorkOrder::getInitiateDeptId, vo.getDeptId())
                            .set(WorkOrder::getInitiateUserId, employee.getUserId())
                            .set(WorkOrder::getInitiateUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                            .set(WorkOrder::getInitiateEmployeeId, vo.getEmployeeId())
                            .set(WorkOrder::getInitiateEmployeeName, employee.getEmployeeName());
                } else {
                    updateWrapper.set(WorkOrder::getUndertakeDeptId, vo.getDeptId())
                            .set(WorkOrder::getUndertakeUserId, employee.getUserId())
                            .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                            .set(WorkOrder::getUndertakeEmployeeId, vo.getEmployeeId())
                            .set(WorkOrder::getUndertakeEmployeeName, employee.getEmployeeName());
                }
                currentUserNickName = user.getNickName();
            }
        }
        update(updateWrapper);

        workOrderFileService.saveNewFile(vo.getId(), vo.getFiles(), WorkOrderFileType.FOLLOW_FILE, operName);

        if (vo.getIsFinish() == 1 && Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getInitiateUserId())) {
                LocalDate autoConfirmTime = LocalDate.now().plusDays(7);
                // 发送系统通知
                SysDept initiateDept = remoteDeptService.getDeptInfo(newWorkOrder.getInitiateDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                String content = Objects.isNull(customerService) ? String.format("您的工单%s已完结，请尽快确认，如不处理，该工单最晚将在%s自动确认。", newWorkOrder.getTitle(), autoConfirmTime) :
                        String.format("您的工单【%s】%s 已完结，请尽快确认，如不处理，该工单最晚将在%s自动确认。", customerService.getCustomerName(), newWorkOrder.getTitle(), autoConfirmTime);
                asyncService.asyncSendMessage(newWorkOrder.getInitiateUserId(), newWorkOrder.getInitiateEmployeeId(),
                        newWorkOrder.getInitiateEmployeeName(), newWorkOrder.getInitiateDeptId(),
                        Objects.isNull(initiateDept) ? "" : initiateDept.getDeptName(),
                        content, operName);
                asyncService.sendYsbNotice(newWorkOrder.getInitiateUserId(), newWorkOrder.getInitiateEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
            }
        } else {
            // 查一下最新的
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getCurrentDeptId())) {
                SysDept currentDept = remoteDeptService.getDeptInfo(newWorkOrder.getCurrentDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                if (Objects.isNull(newWorkOrder.getCurrentUserId())) {
                    // 给组下所有员工发
                    List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(workOrder.getCurrentDeptId()).getDataThrowException(false);
                    if (!ObjectUtils.isEmpty(employeeList)) {
                        String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                                String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                        for (SysEmployee e : employeeList) {
                            asyncService.asyncSendMessage(e.getUserId(), e.getEmployeeId(),
                                    e.getEmployeeName(), newWorkOrder.getCurrentDeptId(),
                                    Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                                    content, operName);
                            asyncService.sendYsbNotice(e.getUserId(), e.getEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
                        }
                    }
                } else {
                    String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                            String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                    asyncService.asyncSendMessage(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeId(),
                            newWorkOrder.getCurrentEmployeeName(), newWorkOrder.getCurrentDeptId(),
                            Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                            content, operName);
                    asyncService.sendYsbNotice(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
                }
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("是否完结", vo.getIsFinish() == 1 ? "是" : "否");
        if (vo.getIsFinish() == 0) {
            if (vo.getFollowUpType() == 1) {
                operContent.put("指派给", Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? "承接方" : "发起方");
            } else {
                Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
                SysDept currentDept = Objects.isNull(vo.getDeptId()) ? null : deptMap.get(vo.getDeptId());
                String currentDeptPathStr = getDeptTreeName(currentDept, deptMap);
                operContent.put("指派给", currentDeptPathStr + "（" + (StringUtils.isEmpty(currentUserNickName) ? "-" : currentUserNickName) + "）");
            }
        }
        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) && vo.getIsFinish() == 0) {
            if (!StringUtils.isEmpty(vo.getDdl())) {
                operContent.put("DDL", vo.getDdl());
            }
        } else {
            if (!Objects.isNull(workOrder.getDdl())) {
                operContent.put("DDL", workOrder.getDdl().toString());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(workOrder.getId(), BusinessLogBusinessType.WORK_ORDER, currentUserId, vo.getDdl());
        }
    }

    @Override
    @Transactional
    public Boolean remoteFollowUp(WorkOrderFollowUpVO vo) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanFollowUpStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待完结状态下可跟进");
        }
        if (Objects.isNull(workOrder.getCurrentDeptId())) {
            throw new ServiceException("当前处理方为空");
        }
        Long currentUserId = vo.getUserId();
        List<SysEmployee> currentEmployees = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(currentUserId)).getDataThrowException(false);
        boolean isAdmin = Objects.equals(1L, currentUserId);
        if (!isAdmin && (ObjectUtils.isEmpty(currentEmployees) || currentEmployees.stream().map(SysEmployee::getDeptId).noneMatch(deptId -> Objects.equals(deptId, workOrder.getCurrentDeptId())))) {
            throw new ServiceException("只有当前处理方组织可操作");
        }
        if (vo.getIsFinish() == 0 && Objects.isNull(vo.getFollowUpType())) {
            throw new ServiceException("跟进类型不能为空");
        }
        SysEmployee currentEmployee = ObjectUtils.isEmpty(currentEmployees) ? null : currentEmployees.stream().filter(row -> Objects.equals(row.getDeptId(), workOrder.getCurrentDeptId())).findFirst().orElse(null);
        SysUser currentUser = remoteUserService.getByUserId(currentUserId, SecurityConstants.INNER).getDataThrowException(false);
        String operName = Objects.isNull(currentUser) ? "" : currentUser.getNickName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType;
        if (vo.getIsFinish() == 1) {
            operType = "完结工单";
        } else {
            if (vo.getFollowUpType() == 1) {
                operType = "跟进工单";
            } else {
                operType = "转交工单";
            }
        }
        String currentUserNickName = "";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType)
                .set(WorkOrder::getLastFollowUpName, operName)
                .set(WorkOrder::getLastFollowUpTime, operTime);
        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) && vo.getIsFinish() == 0) {
            updateWrapper.set(WorkOrder::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl());
        }
        if (vo.getIsFinish() == 1) {
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED.getCode())
                        .set(WorkOrder::getFinishTime, operTime)
                        .set(WorkOrder::getCurrentUserType, null)
                        .set(WorkOrder::getCurrentDeptId, null)
                        .set(WorkOrder::getCurrentUserId, null)
                        .set(WorkOrder::getCurrentUserNickname, null)
                        .set(WorkOrder::getCurrentEmployeeId, null)
                        .set(WorkOrder::getCurrentEmployeeName, null);
            } else {
                updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED_CONFIRM.getCode())
                        .set(WorkOrder::getFinishTime, operTime)
                        .set(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                        .set(WorkOrder::getCurrentDeptId, workOrder.getInitiateDeptId())
                        .set(WorkOrder::getCurrentUserId, workOrder.getInitiateUserId())
                        .set(WorkOrder::getCurrentUserNickname, workOrder.getInitiateUserNickname())
                        .set(WorkOrder::getCurrentEmployeeId, workOrder.getInitiateEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, workOrder.getInitiateEmployeeName());
            }
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode()) && Objects.isNull(workOrder.getUndertakeUserId())) {
                updateWrapper.set(WorkOrder::getUndertakeUserId, currentUserId)
                        .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(currentUser) ? "" : currentUser.getNickName())
                        .set(WorkOrder::getUndertakeEmployeeId, Objects.isNull(currentEmployee) ? null : currentEmployee.getEmployeeId())
                        .set(WorkOrder::getUndertakeEmployeeName, Objects.isNull(currentEmployee) ? "" : currentEmployee.getEmployeeName());
            }
        } else {
            if (vo.getFollowUpType() == 1) {
                // 变更处理方
                if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
                    // 如果当前是承接方，则当前修改为发起方
                    updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                            .set(WorkOrder::getCurrentDeptId, workOrder.getInitiateDeptId())
                            .set(WorkOrder::getCurrentUserId, workOrder.getInitiateUserId())
                            .set(WorkOrder::getCurrentUserNickname, workOrder.getInitiateUserNickname())
                            .set(WorkOrder::getCurrentEmployeeId, workOrder.getInitiateEmployeeId())
                            .set(WorkOrder::getCurrentEmployeeName, workOrder.getInitiateEmployeeName());
                    if (Objects.isNull(workOrder.getUndertakeUserId())) {
                        updateWrapper.set(WorkOrder::getUndertakeUserId, currentUserId)
                                .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(currentUser) ? "" : currentUser.getNickName())
                                .set(WorkOrder::getUndertakeEmployeeId, Objects.isNull(currentEmployee) ? null : currentEmployee.getEmployeeId())
                                .set(WorkOrder::getUndertakeEmployeeName, Objects.isNull(currentEmployee) ? "" : currentEmployee.getEmployeeName());
                    }
                } else {
                    // 如果当前是发起方，则当前修改为承接方
                    updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.UNDERTAKE_PARTY.getCode())
                            .set(WorkOrder::getCurrentDeptId, workOrder.getUndertakeDeptId())
                            .set(WorkOrder::getCurrentUserId, workOrder.getUndertakeUserId())
                            .set(WorkOrder::getCurrentUserNickname, workOrder.getUndertakeUserNickname())
                            .set(WorkOrder::getCurrentEmployeeId, workOrder.getUndertakeEmployeeId())
                            .set(WorkOrder::getCurrentEmployeeName, workOrder.getUndertakeEmployeeName());
                }
            } else {
                // 转交他人
                SysEmployee employee = remoteEmployeeService.getEmployeeInfo(vo.getEmployeeId()).getDataThrowException();
                SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
                if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                    updateWrapper.set(WorkOrder::getInitiateDeptId, vo.getDeptId())
                            .set(WorkOrder::getInitiateUserId, employee.getUserId())
                            .set(WorkOrder::getInitiateUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                            .set(WorkOrder::getInitiateEmployeeId, vo.getEmployeeId())
                            .set(WorkOrder::getInitiateEmployeeName, employee.getEmployeeName());
                } else {
                    updateWrapper.set(WorkOrder::getUndertakeDeptId, vo.getDeptId())
                            .set(WorkOrder::getUndertakeUserId, employee.getUserId())
                            .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                            .set(WorkOrder::getUndertakeEmployeeId, vo.getEmployeeId())
                            .set(WorkOrder::getUndertakeEmployeeName, employee.getEmployeeName());
                }
                currentUserNickName = user.getNickName();
            }
        }
        update(updateWrapper);

        workOrderFileService.saveNewFile(vo.getId(), vo.getFiles(), WorkOrderFileType.FOLLOW_FILE, operName);

        if (vo.getIsFinish() == 1 && Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getInitiateUserId())) {
                LocalDate autoConfirmTime = LocalDate.now().plusDays(7);
                // 发送系统通知
                SysDept initiateDept = remoteDeptService.getDeptInfo(newWorkOrder.getInitiateDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                String content = Objects.isNull(customerService) ? String.format("您的工单%s已完结，请尽快确认，如不处理，该工单最晚将在%s自动确认。", newWorkOrder.getTitle(), autoConfirmTime) :
                        String.format("您的工单【%s】%s 已完结，请尽快确认，如不处理，该工单最晚将在%s自动确认。", customerService.getCustomerName(), newWorkOrder.getTitle(), autoConfirmTime);
                asyncService.asyncSendMessage(newWorkOrder.getInitiateUserId(), newWorkOrder.getInitiateEmployeeId(),
                        newWorkOrder.getInitiateEmployeeName(), newWorkOrder.getInitiateDeptId(),
                        Objects.isNull(initiateDept) ? "" : initiateDept.getDeptName(),
                        content, operName);
                asyncService.sendYsbNotice(newWorkOrder.getInitiateUserId(), newWorkOrder.getInitiateEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
            }
        } else {
            // 查一下最新的
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getCurrentDeptId())) {
                SysDept currentDept = remoteDeptService.getDeptInfo(newWorkOrder.getCurrentDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                if (Objects.isNull(newWorkOrder.getCurrentUserId())) {
                    // 给组下所有员工发
                    List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(workOrder.getCurrentDeptId()).getDataThrowException(false);
                    if (!ObjectUtils.isEmpty(employeeList)) {
                        String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                                String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                        for (SysEmployee e : employeeList) {
                            asyncService.asyncSendMessage(e.getUserId(), e.getEmployeeId(),
                                    e.getEmployeeName(), newWorkOrder.getCurrentDeptId(),
                                    Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                                    content, operName);
                            asyncService.sendYsbNotice(e.getUserId(), e.getEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
                        }
                    }
                } else {
                    String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                            String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                    asyncService.asyncSendMessage(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeId(),
                            newWorkOrder.getCurrentEmployeeName(), newWorkOrder.getCurrentDeptId(),
                            Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                            content, operName);
                    asyncService.sendYsbNotice(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单跟进消息", workOrder.getId());
                }
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("是否完结", vo.getIsFinish() == 1 ? "是" : "否");
        if (vo.getIsFinish() == 0) {
            if (vo.getFollowUpType() == 1) {
                operContent.put("指派给", Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? "承接方" : "发起方");
            } else {
                Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
                SysDept currentDept = Objects.isNull(vo.getDeptId()) ? null : deptMap.get(vo.getDeptId());
                String currentDeptPathStr = getDeptTreeName(currentDept, deptMap);
                operContent.put("指派给", currentDeptPathStr + "（" + (StringUtils.isEmpty(currentUserNickName) ? "-" : currentUserNickName) + "）");
            }
        }
        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) && vo.getIsFinish() == 0) {
            if (!StringUtils.isEmpty(vo.getDdl())) {
                operContent.put("DDL", vo.getDdl());
            }
        } else {
            if (!Objects.isNull(workOrder.getDdl())) {
                operContent.put("DDL", workOrder.getDdl().toString());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(null)
                    .setOperType(operType)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(workOrder.getId(), BusinessLogBusinessType.WORK_ORDER, currentUserId, vo.getDdl());
        }
        return true;
    }

    @Override
    @Transactional
    public void commentWorkOrder(WorkOrderCommentVO vo, Long deptId) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        Long currentUserId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, currentUserId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "评论工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);
        update(updateWrapper);


        if (!Objects.isNull(workOrder.getCurrentUserId())) {
            SysDept dept = remoteDeptService.getDeptInfo(workOrder.getCurrentDeptId()).getDataThrowException();
            CCustomerService customerService = Objects.isNull(workOrder.getCustomerServiceId()) ? null : customerServiceService.getById(workOrder.getCustomerServiceId());
            String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的评论", workOrder.getTitle()) :
                    String.format("您的工单【%s】%s 有新的评论", customerService.getCustomerName(), workOrder.getTitle());
            asyncService.asyncSendMessage(workOrder.getCurrentUserId(), workOrder.getCurrentEmployeeId(),
                    workOrder.getCurrentEmployeeName(), workOrder.getCurrentUserId(),
                    Objects.isNull(dept) ? "" : dept.getDeptName(),
                    content, operName);
            asyncService.sendYsbNotice(workOrder.getCurrentUserId(), workOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单评论消息", workOrder.getId());
        }

        Map<String, String> operContent = new LinkedHashMap<>();
        if (!Objects.isNull(workOrder.getDdl())) {
            operContent.put("DDL", workOrder.getDdl().toString());
        }

        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Boolean remoteCommentWorkOrder(WorkOrderCommentVO vo) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        Long currentUserId = vo.getUserId();
        SysUser user = remoteUserService.getByUserId(currentUserId, SecurityConstants.INNER).getDataThrowException(false);
        String operName = Objects.isNull(user) ? "" : user.getNickName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "评论工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);
        update(updateWrapper);


        if (!Objects.isNull(workOrder.getCurrentUserId())) {
            SysDept dept = remoteDeptService.getDeptInfo(workOrder.getCurrentDeptId()).getDataThrowException();
            CCustomerService customerService = Objects.isNull(workOrder.getCustomerServiceId()) ? null : customerServiceService.getById(workOrder.getCustomerServiceId());
            String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的评论", workOrder.getTitle()) :
                    String.format("您的工单【%s】%s 有新的评论", customerService.getCustomerName(), workOrder.getTitle());
            asyncService.asyncSendMessage(workOrder.getCurrentUserId(), workOrder.getCurrentEmployeeId(),
                    workOrder.getCurrentEmployeeName(), workOrder.getCurrentUserId(),
                    Objects.isNull(dept) ? "" : dept.getDeptName(),
                    content, operName);
            asyncService.sendYsbNotice(workOrder.getCurrentUserId(), workOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单评论消息", workOrder.getId());
        }

        Map<String, String> operContent = new LinkedHashMap<>();
        if (!Objects.isNull(workOrder.getDdl())) {
            operContent.put("DDL", workOrder.getDdl().toString());
        }

        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(null)
                    .setOperType(operType)
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean remoteConfirmWorkOrder(WorkOrderConfirmVO vo) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanConfirmStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待确认状态下可确认");
        }
        Long currentUserId = vo.getUserId();
        List<SysEmployee> currentEmployees = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(currentUserId)).getDataThrowException(false);
        boolean isAdmin = Objects.equals(1L, currentUserId);
        if (!isAdmin && (ObjectUtils.isEmpty(currentEmployees) || currentEmployees.stream().map(SysEmployee::getDeptId).noneMatch(deptId -> Objects.equals(deptId, workOrder.getInitiateDeptId())))) {
            throw new ServiceException("只有发起方组织可操作");
        }
        if (vo.getIsFinish() == 0) {
            checkDdl(vo.getDdl());
        }
        SysUser currentUser = remoteUserService.getByUserId(currentUserId, SecurityConstants.INNER).getDataThrowException(false);
        String operName = Objects.isNull(currentUser) ? "" : currentUser.getNickName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "确认工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);
        if (vo.getIsFinish() == 1) {
            updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED.getCode())
                    .set(WorkOrder::getCurrentUserType, null)
                    .set(WorkOrder::getCurrentDeptId, null)
                    .set(WorkOrder::getCurrentUserId, null)
                    .set(WorkOrder::getCurrentUserNickname, null)
                    .set(WorkOrder::getCurrentEmployeeId, null)
                    .set(WorkOrder::getCurrentEmployeeName, null);
        } else {
            updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                    .set(WorkOrder::getFinishTime, null)
                    .set(WorkOrder::getCurrentUserType, WorkOrderObject.UNDERTAKE_PARTY.getCode())
                    .set(WorkOrder::getCurrentDeptId, workOrder.getUndertakeDeptId())
                    .set(WorkOrder::getCurrentUserId, workOrder.getUndertakeUserId())
                    .set(WorkOrder::getCurrentUserNickname, workOrder.getUndertakeUserNickname())
                    .set(WorkOrder::getCurrentEmployeeId, workOrder.getUndertakeEmployeeId())
                    .set(WorkOrder::getCurrentEmployeeName, workOrder.getUndertakeEmployeeName())
                    .set(WorkOrder::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl());
        }
        update(updateWrapper);

        workOrderFileService.saveNewFile(vo.getId(), vo.getFiles(), WorkOrderFileType.CONFIRM_FILE, operName);

        if (vo.getIsFinish() == 0) {
            // 查一下最新的
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getCurrentDeptId())) {
                SysDept currentDept = remoteDeptService.getDeptInfo(newWorkOrder.getCurrentDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                if (Objects.isNull(newWorkOrder.getCurrentUserId())) {
                    // 给组下所有员工发
                    List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(workOrder.getCurrentDeptId()).getDataThrowException(false);
                    if (!ObjectUtils.isEmpty(employeeList)) {
                        String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                                String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                        for (SysEmployee e : employeeList) {
                            asyncService.asyncSendMessage(e.getUserId(), e.getEmployeeId(),
                                    e.getEmployeeName(), newWorkOrder.getCurrentDeptId(),
                                    Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                                    content, operName);
                            asyncService.sendYsbNotice(e.getUserId(), e.getEmployeeName(), currentUserId, operName, content, "工单确认消息", workOrder.getId());
                        }
                    }
                } else {
                    String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                            String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                    asyncService.asyncSendMessage(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeId(),
                            newWorkOrder.getCurrentEmployeeName(), newWorkOrder.getCurrentDeptId(),
                            Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                            content, operName);
                    asyncService.sendYsbNotice(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单确认消息", workOrder.getId());
                }
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("是否完结", vo.getIsFinish() == 1 ? "是" : "否");
        if (vo.getIsFinish() == 0) {
            operContent.put("DDL", Objects.isNull(vo.getDdl()) ? "-" : vo.getDdl());
        } else {
            if (!Objects.isNull(workOrder.getDdl())) {
                operContent.put("DDL", workOrder.getDdl().toString());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(null)
                    .setOperType(operType)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(workOrder.getId(), BusinessLogBusinessType.WORK_ORDER, currentUserId, vo.getDdl());
        }
        return true;
    }

    @Override
    @Transactional
    public void transmit(WorkOrderTransmitVO vo, Long deptId) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanTransmitStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待完结、已完结待确认状态下可转交");
        }
        Long currentUserId = SecurityUtils.getUserId();
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(currentUserId, deptId).getDataThrowException();
        if (vo.getTransmitType() == 1) {
            if (!userDeptDTO.getIsAdmin() && !userDeptDTO.getDeptIds().contains(workOrder.getInitiateDeptId())) {
                throw new ServiceException("只有发起方组织可操作");
            }
        } else {
            if (!userDeptDTO.getIsAdmin() && !userDeptDTO.getDeptIds().contains(workOrder.getUndertakeDeptId())) {
                throw new ServiceException("只有承接方组织可操作");
            }
        }
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, currentUserId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "转交工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);

        SysEmployee employee = remoteEmployeeService.getEmployeeInfo(vo.getEmployeeId()).getDataThrowException();
        SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
        if (vo.getTransmitType() == 1) {
            // 转交发起方
            updateWrapper.set(WorkOrder::getInitiateDeptId, vo.getDeptId())
                    .set(WorkOrder::getInitiateUserId, employee.getUserId())
                    .set(WorkOrder::getInitiateUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                    .set(WorkOrder::getInitiateEmployeeId, vo.getEmployeeId())
                    .set(WorkOrder::getInitiateEmployeeName, employee.getEmployeeName());
            // 如果当前处理方是发起方，一起改了
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
            }
        } else {
            // 转交承接方
            updateWrapper.set(WorkOrder::getUndertakeDeptId, vo.getDeptId())
                    .set(WorkOrder::getUndertakeUserId, employee.getUserId())
                    .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                    .set(WorkOrder::getUndertakeEmployeeId, vo.getEmployeeId())
                    .set(WorkOrder::getUndertakeEmployeeName, employee.getEmployeeName());
            // 如果当前处理方是承接方，一起改了
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
            }
        }

        update(updateWrapper);

        SysDept dept = remoteDeptService.getDeptInfo(vo.getDeptId()).getDataThrowException();
        if (!Objects.isNull(employee.getUserId())) {
            CCustomerService customerService = Objects.isNull(workOrder.getCustomerServiceId()) ? null : customerServiceService.getById(workOrder.getCustomerServiceId());
            String content = Objects.isNull(customerService) ? String.format("工单%s 转交给你，请尽快跟进。", workOrder.getTitle()) :
                    String.format("工单【%s】%s 转交给你，请尽快跟进。", customerService.getCustomerName(), workOrder.getTitle());
            asyncService.asyncSendMessage(employee.getUserId(), vo.getEmployeeId(),
                    employee.getEmployeeName(), vo.getDeptId(),
                    Objects.isNull(dept) ? "" : dept.getDeptName(),
                    content, operName);
            asyncService.sendYsbNotice(employee.getUserId(), employee.getEmployeeName(), currentUserId, operName, content, "工单转交消息", workOrder.getId());
        }

        Map<String, String> operContent = new HashMap<>();
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        String currentDeptPathStr = getDeptTreeName(dept, deptMap);
        operContent.put("转交人", currentDeptPathStr + "（" + (Objects.isNull(user) || StringUtils.isEmpty(user.getNickName()) ? "-" : user.getNickName()) + "）");
        if (!Objects.isNull(workOrder.getDdl())) {
            operContent.put("DDL", workOrder.getDdl().toString());
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Boolean remoteTransmit(WorkOrderTransmitVO vo) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanTransmitStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待完结、已完结待确认状态下可转交");
        }
        Long currentUserId = vo.getUserId();
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(vo.getUserId())).getDataThrowException(false);
        boolean isAdmin = Objects.equals(currentUserId, 1L);
        if (vo.getTransmitType() == 1) {
            if (!isAdmin && (ObjectUtils.isEmpty(employeeList) || employeeList.stream().map(SysEmployee::getDeptId).noneMatch(deptId -> Objects.equals(deptId, workOrder.getInitiateDeptId())))) {
                throw new ServiceException("只有发起方组织可操作");
            }
        } else {
            if (!isAdmin && (ObjectUtils.isEmpty(employeeList) || employeeList.stream().map(SysEmployee::getDeptId).noneMatch(deptId -> Objects.equals(deptId, workOrder.getUndertakeDeptId())))) {
                throw new ServiceException("只有承接方组织可操作");
            }
        }
        SysUser currentUser = remoteUserService.getByUserId(currentUserId, SecurityConstants.INNER).getDataThrowException(false);
        String operName = Objects.isNull(currentUser) ? "" : currentUser.getNickName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "转交工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);

        SysEmployee employee = remoteEmployeeService.getEmployeeInfo(vo.getEmployeeId()).getDataThrowException();
        SysUser user = remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
        if (vo.getTransmitType() == 1) {
            // 转交发起方
            updateWrapper.set(WorkOrder::getInitiateDeptId, vo.getDeptId())
                    .set(WorkOrder::getInitiateUserId, employee.getUserId())
                    .set(WorkOrder::getInitiateUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                    .set(WorkOrder::getInitiateEmployeeId, vo.getEmployeeId())
                    .set(WorkOrder::getInitiateEmployeeName, employee.getEmployeeName());
            // 如果当前处理方是发起方，一起改了
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
            }
        } else {
            // 转交承接方
            updateWrapper.set(WorkOrder::getUndertakeDeptId, vo.getDeptId())
                    .set(WorkOrder::getUndertakeUserId, employee.getUserId())
                    .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                    .set(WorkOrder::getUndertakeEmployeeId, vo.getEmployeeId())
                    .set(WorkOrder::getUndertakeEmployeeName, employee.getEmployeeName());
            // 如果当前处理方是承接方，一起改了
            if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
                updateWrapper.set(WorkOrder::getCurrentDeptId, vo.getDeptId())
                        .set(WorkOrder::getCurrentUserId, employee.getUserId())
                        .set(WorkOrder::getCurrentUserNickname, Objects.isNull(user) ? "" : user.getNickName())
                        .set(WorkOrder::getCurrentEmployeeId, vo.getEmployeeId())
                        .set(WorkOrder::getCurrentEmployeeName, employee.getEmployeeName());
            }
        }

        update(updateWrapper);

        SysDept dept = remoteDeptService.getDeptInfo(vo.getDeptId()).getDataThrowException();
        if (!Objects.isNull(employee.getUserId())) {
            CCustomerService customerService = Objects.isNull(workOrder.getCustomerServiceId()) ? null : customerServiceService.getById(workOrder.getCustomerServiceId());
            String content = Objects.isNull(customerService) ? String.format("工单%s 转交给你，请尽快跟进。", workOrder.getTitle()) :
                    String.format("工单【%s】%s 转交给你，请尽快跟进。", customerService.getCustomerName(), workOrder.getTitle());
            asyncService.asyncSendMessage(employee.getUserId(), vo.getEmployeeId(),
                    employee.getEmployeeName(), vo.getDeptId(),
                    Objects.isNull(dept) ? "" : dept.getDeptName(),
                    content, operName);
            asyncService.sendYsbNotice(employee.getUserId(), employee.getEmployeeName(), currentUserId, operName, content, "工单转交消息", workOrder.getId());
        }

        Map<String, String> operContent = new HashMap<>();
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        String currentDeptPathStr = getDeptTreeName(dept, deptMap);
        operContent.put("转交人", currentDeptPathStr + "（" + (Objects.isNull(user) || StringUtils.isEmpty(user.getNickName()) ? "-" : user.getNickName()) + "）");
        if (!Objects.isNull(workOrder.getDdl())) {
            operContent.put("DDL", workOrder.getDdl().toString());
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(null)
                    .setOperType(operType)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return true;
    }

    @Override
    public WorkOrderCountDTO workOrderStatistic(Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.workOrderUserDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        return WorkOrderCountDTO.builder()
                .waitDealCount(baseMapper.workOrderCountStatistic(userDeptDTO, 4))
                .myInitiateCount(baseMapper.workOrderCountStatistic(userDeptDTO, 1))
                .myUndertakeCount(baseMapper.workOrderCountStatistic(userDeptDTO, 2))
                .waitUndertakeCount(baseMapper.workOrderCountStatistic(userDeptDTO, 3))
                .build();
    }

    @Override
    @Transactional
    public TCommonOperateDTO<WorkOrder> transmitBatch(WorkOrderTransmitBatchVO vo, Long deptId) {
        TCommonOperateDTO<WorkOrder> result = new TCommonOperateDTO<>();
        List<WorkOrder> totalWorkOrders = list(new LambdaQueryWrapper<WorkOrder>()
                .eq(WorkOrder::getIsDel, false)
                .in(WorkOrder::getId, vo.getIds()));
        result.setTotal(totalWorkOrders);
        List<WorkOrder> successList = Lists.newArrayList();
        List<WorkOrder> failList = Lists.newArrayList();
        result.setSuccess(successList);
        result.setFail(failList);
        if (ObjectUtils.isEmpty(totalWorkOrders)) {
            return result;
        }
        for (WorkOrder workOrder : totalWorkOrders) {
            WorkOrderTransmitVO transmitVO = new WorkOrderTransmitVO();
            BeanUtils.copyProperties(vo, transmitVO);
            transmitVO.setId(workOrder.getId());
            try {
                transmit(transmitVO, deptId);
                successList.add(workOrder);
            } catch (Exception e) {
                log.error("转交失败:{}", e.getMessage());
                failList.add(workOrder);
            }
        }
        result.setFail(failList);
        result.setSuccess(successList);
        return result;
    }

    @Override
    @Transactional
    public void restartWorkOrder(WorkOrderRestartVO vo, Long deptId) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!Objects.equals(workOrder.getStatus(), WorkOrderStatus.ENDED.getCode()) && !Objects.equals(workOrder.getStatus(), WorkOrderStatus.OVERTIME_CLOSE.getCode()) && !Objects.equals(workOrder.getStatus(), WorkOrderStatus.OVERTIME_CONFIRM.getCode())) {
            throw new ServiceException("已完结、超时确认、超时关闭状态下才可重启");
        }
        SysDept dept;
        String dealUserName = "";
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "重启工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<WorkOrder>()
                .eq(WorkOrder::getId, vo.getId())
                .set(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                .set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType)
                .set(WorkOrder::getDdl, null)
                .set(WorkOrder::getFinishTime, null);
        if (Objects.equals(vo.getDealUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
            updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                    .set(WorkOrder::getCurrentDeptId, workOrder.getInitiateDeptId())
                    .set(WorkOrder::getCurrentUserId, workOrder.getInitiateUserId())
                    .set(WorkOrder::getCurrentUserNickname, workOrder.getInitiateUserNickname())
                    .set(WorkOrder::getCurrentEmployeeId, workOrder.getInitiateEmployeeId())
                    .set(WorkOrder::getCurrentEmployeeName, workOrder.getInitiateEmployeeName());
            dept = remoteDeptService.getDeptInfo(workOrder.getInitiateDeptId()).getDataThrowException();
            dealUserName = workOrder.getInitiateUserNickname();
        } else {
            updateWrapper.set(WorkOrder::getCurrentUserType, WorkOrderObject.UNDERTAKE_PARTY.getCode())
                    .set(WorkOrder::getCurrentDeptId, workOrder.getUndertakeDeptId())
                    .set(WorkOrder::getCurrentUserId, workOrder.getUndertakeUserId())
                    .set(WorkOrder::getCurrentUserNickname, workOrder.getUndertakeUserNickname())
                    .set(WorkOrder::getCurrentEmployeeId, workOrder.getUndertakeEmployeeId())
                    .set(WorkOrder::getCurrentEmployeeName, workOrder.getUndertakeEmployeeName());
            dept = remoteDeptService.getDeptInfo(workOrder.getUndertakeDeptId()).getDataThrowException();
            dealUserName = workOrder.getUndertakeUserNickname();
        }
        update(updateWrapper);

        Map<String, String> operContent = new HashMap<>();
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        String currentDeptPathStr = getDeptTreeName(dept, deptMap);
        operContent.put("处理人", (Objects.equals(vo.getDealUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? "发起人 " : "承接人 ") + currentDeptPathStr + "（" + dealUserName + "）");
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(userId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<CommonDeptCountDTO> workOrderInitiateDeptList(UserDeptDTO userDeptDTO, Integer tabType) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.workOrderInitiateDeptList(userDeptDTO, tabType);
    }

    @Override
    public List<CommonDeptCountDTO> workOrderUndertakeDeptList(UserDeptDTO userDeptDTO, Integer tabType) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.workOrderUndertakeDeptList(userDeptDTO, tabType);
    }

    @Override
    @Transactional
    public void workOrderCloseTask() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime overTime = LocalDateTime.of(now.minusDays(11).toLocalDate(), LocalTime.of(23, 59, 59));
        LocalDateTime operTime = overTime.plusDays(10);
        List<WorkOrder> overTimeWorkOrderList = list(new LambdaQueryWrapper<WorkOrder>()
                .eq(WorkOrder::getIsDel, false)
                .eq(WorkOrder::getCurrentUserType, WorkOrderObject.INITIATE_PARTY.getCode())
                .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                .le(WorkOrder::getLastFollowUpTime, overTime));
        if (!ObjectUtils.isEmpty(overTimeWorkOrderList)) {
            update(new LambdaUpdateWrapper<WorkOrder>()
                    .set(WorkOrder::getStatus, WorkOrderStatus.OVERTIME_CLOSE.getCode())
                    .set(WorkOrder::getCurrentUserType, null)
                    .set(WorkOrder::getCurrentDeptId, null)
                    .set(WorkOrder::getCurrentUserId, null)
                    .set(WorkOrder::getCurrentUserNickname, null)
                    .set(WorkOrder::getCurrentEmployeeId, null)
                    .set(WorkOrder::getCurrentEmployeeName, null)
                    .set(WorkOrder::getLastOperTime, operTime)
                    .set(WorkOrder::getLastOperType, "超时关闭")
                    .set(WorkOrder::getLastOperName, "系统")
                    .in(WorkOrder::getId, overTimeWorkOrderList.stream().map(WorkOrder::getId).collect(Collectors.toList())));
            overTimeWorkOrderList.forEach(workOrder -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(workOrder.getId())
                            .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                            .setDeptId(0L)
                            .setOperType("超时关闭")
                            .setOperName("系统")
                            .setOperUserId(1L)
                            .setCreateTime(operTime));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public void workOrderConfirmTask() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime overTime = LocalDateTime.of(now.minusDays(8).toLocalDate(), LocalTime.of(23, 59, 59));
        LocalDateTime operTime = overTime.plusDays(7);
        List<WorkOrder> overTimeWorkOrderList = list(new LambdaQueryWrapper<WorkOrder>()
                .eq(WorkOrder::getIsDel, false)
                .eq(WorkOrder::getStatus, WorkOrderStatus.ENDED_CONFIRM.getCode())
                .le(WorkOrder::getFinishTime, overTime));
        if (!ObjectUtils.isEmpty(overTimeWorkOrderList)) {
            update(new LambdaUpdateWrapper<WorkOrder>()
                    .set(WorkOrder::getStatus, WorkOrderStatus.OVERTIME_CONFIRM.getCode())
                    .set(WorkOrder::getCurrentUserType, null)
                    .set(WorkOrder::getCurrentDeptId, null)
                    .set(WorkOrder::getCurrentUserId, null)
                    .set(WorkOrder::getCurrentUserNickname, null)
                    .set(WorkOrder::getCurrentEmployeeId, null)
                    .set(WorkOrder::getCurrentEmployeeName, null)
                    .set(WorkOrder::getLastOperTime, operTime)
                    .set(WorkOrder::getLastOperType, "超时确认")
                    .set(WorkOrder::getLastOperName, "系统")
                    .in(WorkOrder::getId, overTimeWorkOrderList.stream().map(WorkOrder::getId).collect(Collectors.toList())));
            overTimeWorkOrderList.forEach(workOrder -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(workOrder.getId())
                            .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                            .setDeptId(0L)
                            .setOperType("超时确认")
                            .setOperName("系统")
                            .setOperUserId(1L)
                            .setCreateTime(operTime));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public void createMaterialPushWorkOrder(String customerName, String bankName, String bankAccountNumber, OperateUserInfoDTO operateUserInfoDTO, List<CommonFileVO> files, String materialDeliverNumber) {
        List<CCustomerService> customerServices = customerServiceService.list(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getCustomerName, customerName).eq(CCustomerService::getIsDel, false));
        SysEmployee employee = ObjectUtils.isEmpty(operateUserInfoDTO.getEmployees()) ? null : operateUserInfoDTO.getEmployees().get(0);
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "材料交接单异常创建";
        CCustomerService customerService = null;
        WorkOrderCreateVO vo;
        com.bxm.customer.domain.WorkOrderType workOrderType;
        if (ObjectUtils.isEmpty(customerServices)) {
            // 工单类型：回单问题
            //
            //客户：空
            //
            //账期：空
            //
            //标题：有银行材料交接但无银行账号
            //
            //内容：客户名、银行名、账号缺少
            //
            //发起组织：推送交接单的组织
            //
            //发起用户：推送交接单的用户
            //
            //承接组织：342，回单问题的默认组织
            //
            //承接人：小组下是多个人，默认待承接
            vo = WorkOrderCreateVO.builder()
                    .title("有银行材料交接但无银行账号")
                    .workOrderType(WorkOrderType.REPLY_BILL.getCode())
                    .remark(String.format("%s %s缺少，交接单编号：%s\r\n银行不存在且找不到客户，请核实客户名是否正确", customerName, bankAccountNumber, materialDeliverNumber))
                    .deptId(Objects.isNull(employee) ? operateUserInfoDTO.getDeptId() : employee.getDeptId())
                    .files(files)
                    .build();
            workOrderType = workOrderTypeService.selectByWorkOrderType(WorkOrderType.REPLY_BILL.getCode());
        } else {
            List<CCustomerService> servicingCustomerList = customerServices.stream().filter(row -> Objects.equals(row.getServiceStatus(), ServiceStatus.SERVICE.getCode())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(servicingCustomerList)) {
                customerService = customerServices.get(0);
            } else {
                customerService = servicingCustomerList.get(0);
            }
            // 工单类型：信息维护
            //
            //客户：用客户名匹配，匹配不到留空
            //
            //账期：空
            //
            //标题：有银行材料交接但无银行账号
            //
            //内容：客户名、银行名、账号缺少
            //
            //发起组织：推送交接单的组织
            //
            //发起用户：推送交接单的用户
            //
            //承接组织：客户的顾问小组
            //
            //承接人：客户的顾问，如果顾问小组下是多个人，默认待承接
            vo = WorkOrderCreateVO.builder()
                    .customerServiceId(customerService.getId())
                    .title("有银行材料交接但无银行账号")
                    .workOrderType(WorkOrderType.INFORMATION_MAINTENANCE.getCode())
                    .remark(String.format("%s %s缺少，交接单编号：%s\r\n请在慧进账系统添加该客户银行账号，然后通知回单中心重新发起交接，或在“材料-清单”模块找到失败的交接单重新生成", customerName, bankAccountNumber, materialDeliverNumber))
                    .deptId(Objects.isNull(employee) ? operateUserInfoDTO.getDeptId() : employee.getDeptId())
                    .files(files)
                    .build();
            workOrderType = workOrderTypeService.selectByWorkOrderType(WorkOrderType.INFORMATION_MAINTENANCE.getCode());
        }
        createWorkOrder(vo, operateUserInfoDTO.getUserId(), operateUserInfoDTO.getDeptId(), operateUserInfoDTO.getOperName(), Objects.isNull(employee) ? null : employee.getEmployeeId(), operTime, operType, customerService, workOrderType);
    }

    @Override
    @Transactional
    @Async
    public void updateWorkOrderDeptId(Long customerServiceId, Long oldDeptId, Long newDeptId, String operName, Long deptId, Long userId, String dispatchType) {
        if (!Objects.isNull(oldDeptId) && !Objects.equals(oldDeptId, newDeptId)) {
            // 原来的组不为空并且新组和原组不相同，才进行下面的逻辑
            List<WorkOrder> initateWorkOrderList = list(new LambdaQueryWrapper<WorkOrder>()
                    .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                    .eq(WorkOrder::getIsDel, false)
                    .eq(WorkOrder::getCustomerServiceId, customerServiceId)
                    .eq(WorkOrder::getInitiateDeptId, oldDeptId));
            List<WorkOrder> undertakeWorkOrderList = list(new LambdaQueryWrapper<WorkOrder>()
                    .eq(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                    .eq(WorkOrder::getIsDel, false)
                    .eq(WorkOrder::getCustomerServiceId, customerServiceId)
                    .eq(WorkOrder::getUndertakeDeptId, oldDeptId));
            LocalDateTime operTime = LocalDateTime.now();
            if (!ObjectUtils.isEmpty(initateWorkOrderList) || !ObjectUtils.isEmpty(undertakeWorkOrderList)) {
                Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
                SysDept newDept = remoteDeptService.getDeptInfo(newDeptId).getDataThrowException();
                List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(newDeptId).getDataThrowException();
                SysEmployee employee = ObjectUtils.isEmpty(employeeList) ? null : employeeList.get(0);
                SysUser sysUser = Objects.isNull(employee) ? null : remoteUserService.getByUserId(employee.getUserId(), SecurityConstants.INNER).getDataThrowException();
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("转交至", String.format("%s（%s）", getDeptTreeName(newDept, deptMap), Objects.isNull(employee) ? "" : employee.getEmployeeName()));
                String operRemark = "ACCOUNTING".equalsIgnoreCase(dispatchType) ? "分派会计" : "分派顾问";
                if (!ObjectUtils.isEmpty(initateWorkOrderList)) {
                    initateWorkOrderList.forEach(workOrder -> {
                        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<WorkOrder>()
                                .eq(WorkOrder::getId, workOrder.getId())
                                .set(WorkOrder::getInitiateDeptId, newDeptId)
                                .set(WorkOrder::getInitiateUserId, Objects.isNull(employee) ? null : employee.getUserId())
                                .set(WorkOrder::getInitiateUserNickname, Objects.isNull(sysUser) ? null : sysUser.getNickName())
                                .set(WorkOrder::getInitiateEmployeeId, Objects.isNull(employee) ? null : employee.getEmployeeId())
                                .set(WorkOrder::getInitiateEmployeeName, Objects.isNull(employee) ? null : employee.getEmployeeName());
                        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode())) {
                            updateWrapper.set(WorkOrder::getCurrentUserId, Objects.isNull(employee) ? null : employee.getUserId())
                                    .set(WorkOrder::getCurrentUserNickname, Objects.isNull(sysUser) ? null : sysUser.getNickName())
                                    .set(WorkOrder::getCurrentEmployeeId, Objects.isNull(employee) ? null : employee.getEmployeeId())
                                    .set(WorkOrder::getCurrentEmployeeName, Objects.isNull(employee) ? null : employee.getEmployeeName())
                                    .set(WorkOrder::getCurrentDeptId, newDeptId);
                        }
                        update(updateWrapper);
                        try {
                            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(workOrder.getId())
                                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("转交工单")
                                    .setOperName(operName)
                                    .setOperUserId(userId)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setOperRemark(operRemark)
                                    .setCreateTime(operTime));
                        } catch (Exception e) {
                            log.error("新增业务日志失败:{}", e.getMessage());
                            throw new ServiceException("操作记录写入失败，请稍后重试");
                        }
                    });
                }
                if (!ObjectUtils.isEmpty(undertakeWorkOrderList)) {
                    undertakeWorkOrderList.forEach(workOrder -> {
                        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<WorkOrder>()
                                .eq(WorkOrder::getId, workOrder.getId())
                                .set(WorkOrder::getUndertakeDeptId, newDeptId)
                                .set(WorkOrder::getUndertakeUserId, Objects.isNull(employee) ? null : employee.getUserId())
                                .set(WorkOrder::getUndertakeUserNickname, Objects.isNull(sysUser) ? null : sysUser.getNickName())
                                .set(WorkOrder::getUndertakeEmployeeId, Objects.isNull(employee) ? null : employee.getEmployeeId())
                                .set(WorkOrder::getUndertakeEmployeeName, Objects.isNull(employee) ? null : employee.getEmployeeName());
                        if (Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.UNDERTAKE_PARTY.getCode())) {
                            updateWrapper.set(WorkOrder::getCurrentUserId, Objects.isNull(employee) ? null : employee.getUserId())
                                    .set(WorkOrder::getCurrentUserNickname, Objects.isNull(sysUser) ? null : sysUser.getNickName())
                                    .set(WorkOrder::getCurrentEmployeeId, Objects.isNull(employee) ? null : employee.getEmployeeId())
                                    .set(WorkOrder::getCurrentEmployeeName, Objects.isNull(employee) ? null : employee.getEmployeeName())
                                    .set(WorkOrder::getCurrentDeptId, newDeptId);
                        }
                        update(updateWrapper);
                        try {
                            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(workOrder.getId())
                                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("转交工单")
                                    .setOperName(operName)
                                    .setOperUserId(userId)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setOperRemark(operRemark)
                                    .setCreateTime(operTime));
                        } catch (Exception e) {
                            log.error("新增业务日志失败:{}", e.getMessage());
                            throw new ServiceException("操作记录写入失败，请稍后重试");
                        }
                    });
                }
            }
        }
    }

    @Override
    @Transactional
    public void confirmWorkOrder(WorkOrderConfirmVO vo, Long deptId) {
        WorkOrder workOrder = getById(vo.getId());
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        if (!WorkOrderStatus.getCanConfirmStatus().contains(workOrder.getStatus())) {
            throw new ServiceException("只有待确认状态下可确认");
        }
        Long currentUserId = SecurityUtils.getUserId();
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(currentUserId, deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && !userDeptDTO.getDeptIds().contains(workOrder.getInitiateDeptId())) {
            throw new ServiceException("只有发起方组织可操作");
        }
        if (vo.getIsFinish() == 0) {
            checkDdl(vo.getDdl());
        }
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, currentUserId).getDataThrowException();
        SysEmployee currentEmployee = ObjectUtils.isEmpty(employees) ? null : employees.get(0);
        String operName = Objects.isNull(currentEmployee) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : currentEmployee.getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "确认工单";
        LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrder::getId, vo.getId());
        updateWrapper.set(WorkOrder::getLastOperTime, operTime)
                .set(WorkOrder::getLastOperName, operName)
                .set(WorkOrder::getLastOperType, operType);
        if (vo.getIsFinish() == 1) {
            updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.ENDED.getCode())
                    .set(WorkOrder::getCurrentUserType, null)
                    .set(WorkOrder::getCurrentDeptId, null)
                    .set(WorkOrder::getCurrentUserId, null)
                    .set(WorkOrder::getCurrentUserNickname, null)
                    .set(WorkOrder::getCurrentEmployeeId, null)
                    .set(WorkOrder::getCurrentEmployeeName, null);
        } else {
            updateWrapper.set(WorkOrder::getStatus, WorkOrderStatus.WAIT_END.getCode())
                    .set(WorkOrder::getFinishTime, null)
                    .set(WorkOrder::getCurrentUserType, WorkOrderObject.UNDERTAKE_PARTY.getCode())
                    .set(WorkOrder::getCurrentDeptId, workOrder.getUndertakeDeptId())
                    .set(WorkOrder::getCurrentUserId, workOrder.getUndertakeUserId())
                    .set(WorkOrder::getCurrentUserNickname, workOrder.getUndertakeUserNickname())
                    .set(WorkOrder::getCurrentEmployeeId, workOrder.getUndertakeEmployeeId())
                    .set(WorkOrder::getCurrentEmployeeName, workOrder.getUndertakeEmployeeName())
                    .set(WorkOrder::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl());
        }
        update(updateWrapper);

        workOrderFileService.saveNewFile(vo.getId(), vo.getFiles(), WorkOrderFileType.CONFIRM_FILE, operName);

        if (vo.getIsFinish() == 0) {
            // 查一下最新的
            WorkOrder newWorkOrder = getById(vo.getId());
            if (!Objects.isNull(newWorkOrder.getCurrentDeptId())) {
                SysDept currentDept = remoteDeptService.getDeptInfo(newWorkOrder.getCurrentDeptId()).getDataThrowException();
                CCustomerService customerService = Objects.isNull(newWorkOrder.getCustomerServiceId()) ? null : customerServiceService.getById(newWorkOrder.getCustomerServiceId());
                if (Objects.isNull(newWorkOrder.getCurrentUserId())) {
                    // 给组下所有员工发
                    List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(workOrder.getCurrentDeptId()).getDataThrowException(false);
                    if (!ObjectUtils.isEmpty(employeeList)) {
                        String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                                String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                        for (SysEmployee e : employeeList) {
                            asyncService.asyncSendMessage(e.getUserId(), e.getEmployeeId(),
                                    e.getEmployeeName(), newWorkOrder.getCurrentDeptId(),
                                    Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                                    content, operName);
                            asyncService.sendYsbNotice(e.getUserId(), e.getEmployeeName(), currentUserId, operName, content, "工单确认消息", workOrder.getId());
                        }
                    }
                } else {
                    String content = Objects.isNull(customerService) ? String.format("您的工单%s 有新的跟进信息，当前需要您处理，请尽快跟进", newWorkOrder.getTitle()) :
                            String.format("您的工单【%s】%s 有新的跟进信息，当前需要您处理，请尽快跟进", customerService.getCustomerName(), newWorkOrder.getTitle());
                    asyncService.asyncSendMessage(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeId(),
                            newWorkOrder.getCurrentEmployeeName(), newWorkOrder.getCurrentDeptId(),
                            Objects.isNull(currentDept) ? "" : currentDept.getDeptName(),
                            content, operName);
                    asyncService.sendYsbNotice(newWorkOrder.getCurrentUserId(), newWorkOrder.getCurrentEmployeeName(), currentUserId, operName, content, "工单确认消息", workOrder.getId());
                }
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("是否完结", vo.getIsFinish() == 1 ? "是" : "否");
        if (vo.getIsFinish() == 0) {
            operContent.put("DDL", Objects.isNull(vo.getDdl()) ? "-" : vo.getDdl());
        } else {
            if (!Objects.isNull(workOrder.getDdl())) {
                operContent.put("DDL", workOrder.getDdl().toString());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType(operType)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperName(operName)
                    .setOperUserId(currentUserId)
                    .setCreateTime(operTime)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(workOrder.getId(), BusinessLogBusinessType.WORK_ORDER, currentUserId, vo.getDdl());
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<WorkOrder> modifyDdl(WorkOrderModifyDdlVO vo, Long deptId) {
        checkDdl(vo.getDdl());
        Long currentUserId = SecurityUtils.getUserId();
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(currentUserId, deptId).getDataThrowException();
        TCommonOperateDTO<WorkOrder> result = new TCommonOperateDTO<>();
        List<WorkOrder> totalWorkOrders = list(new LambdaQueryWrapper<WorkOrder>()
                .eq(WorkOrder::getIsDel, false)
                .in(WorkOrder::getId, vo.getIds()));
        result.setTotal(totalWorkOrders);
        List<WorkOrder> successList = Lists.newArrayList();
        List<WorkOrder> failList = Lists.newArrayList();
        result.setSuccess(successList);
        result.setFail(failList);
        if (ObjectUtils.isEmpty(totalWorkOrders)) {
            return result;
        }
        successList = totalWorkOrders.stream().filter(row -> WorkOrderStatus.canModifyDdl(row.getStatus()) && getCanTransmit(userDeptDTO, null, row.getInitiateDeptId(), null)).collect(Collectors.toList());
        failList = totalWorkOrders.stream().filter(row -> !WorkOrderStatus.canModifyDdl(row.getStatus()) || !getCanTransmit(userDeptDTO, null, row.getInitiateDeptId(), null)).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new WorkOrder().setId(row.getId()).setDdl(LocalDate.parse(vo.getDdl(), DateTimeFormatter.ofPattern("yyyy-MM-dd")))).collect(Collectors.toList()));
            String operType = "修改DDL";
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("DDL", vo.getDdl());
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, currentUserId).getDataThrowException();
            SysEmployee currentEmployee = ObjectUtils.isEmpty(employees) ? null : employees.get(0);
            String operName = Objects.isNull(currentEmployee) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : currentEmployee.getEmployeeName();
            LocalDateTime operTime = LocalDateTime.now();
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.WORK_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType(operType)
                            .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                            .setOperName(operName)
                            .setOperUserId(currentUserId)
                            .setCreateTime(operTime));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                if (!StringUtils.isEmpty(vo.getDdl())) {
                    businessDdlRecordService.saveDdlRecord(row.getId(), BusinessLogBusinessType.WORK_ORDER, currentUserId, vo.getDdl());
                }
            });
        }
        result.setFail(failList);
        result.setSuccess(successList);
        return result;
    }

    @Override
    public WorkOrderDetailDTO remoteWorkOrderDetail(Long id, Long userId) {
        WorkOrder workOrder = getById(id);
        if (Objects.isNull(workOrder) || workOrder.getIsDel()) {
            throw new ServiceException("工单不存在");
        }
        com.bxm.customer.domain.WorkOrderType workOrderType = workOrderTypeService.getById(workOrder.getWorkOrderType());
        if (Objects.isNull(workOrderType)) {
            throw new ServiceException("工单类型不存在");
        }
        Map<Long, SysDept> deptMap = remoteDeptService.getAllDept().getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        SysDept initiateDept = Objects.isNull(workOrder.getInitiateDeptId()) ? null : deptMap.get(workOrder.getInitiateDeptId());
        SysDept undertakeDept = Objects.isNull(workOrder.getUndertakeDeptId()) ? null : deptMap.get(workOrder.getUndertakeDeptId());
        String initiateDeptPathStr = getDeptTreeName(initiateDept, deptMap);
        String undertakeDeptPathStr = getDeptTreeName(undertakeDept, deptMap);
        CCustomerService customerService = customerServiceService.getById(workOrder.getCustomerServiceId());
        List<Long> initiateDeptIdPath = getDeptIdPath(initiateDept);
        List<Long> undertakeDeptIdPath = getDeptIdPath(undertakeDept);
        WorkOrderDetailDTO detailDTO = WorkOrderDetailDTO.builder()
                .id(id)
                .title(workOrder.getTitle())
                .status(workOrder.getStatus())
                .statusStr(WorkOrderStatus.getByCode(workOrder.getStatus()).getDesc())
                .initiateInfo(initiateDeptPathStr + "（" + (StringUtils.isEmpty(workOrder.getInitiateUserNickname()) ? "-" : workOrder.getInitiateUserNickname()) + "）")
                .undertakeInfo(undertakeDeptPathStr + "（" + (StringUtils.isEmpty(workOrder.getUndertakeUserNickname()) ? "-" : workOrder.getUndertakeUserNickname()) + "）")
                .customerServiceId(workOrder.getCustomerServiceId())
                .customerName(Objects.isNull(customerService) || customerService.getIsDel() ? "" : customerService.getCustomerName())
                .remark(workOrder.getRemark())
                .files(workOrderFileService.getByWorkOrderIdAndFileType(id, WorkOrderFileType.CREATE_FILE.getCode()))
                .ddl(Objects.isNull(workOrder.getDdl()) ? null : workOrder.getDdl().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)))
                .periodStart(Objects.isNull(workOrder.getPeriodStart()) ? null : DateUtils.periodToYeaMonth(workOrder.getPeriodStart()))
                .periodEnd(Objects.isNull(workOrder.getPeriodEnd()) ? null : DateUtils.periodToYeaMonth(workOrder.getPeriodEnd()))
                .currentDeptPath(Objects.isNull(workOrder.getCurrentUserType()) ? null : Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? initiateDeptIdPath : undertakeDeptIdPath)
                .currentEmployeeId(workOrder.getCurrentEmployeeId())
                .currentDeptType(Objects.isNull(workOrder.getCurrentUserType()) ? null : Objects.equals(workOrder.getCurrentUserType(), WorkOrderObject.INITIATE_PARTY.getCode()) ? (Objects.isNull(initiateDept) ? null : initiateDept.getDeptType()) : (Objects.isNull(undertakeDept) ? null : undertakeDept.getDeptType()))
                .initiateDeptPath(initiateDeptIdPath)
                .initiateEmployeeId(workOrder.getInitiateEmployeeId())
                .initiateDeptType(Objects.isNull(initiateDept) ? null : initiateDept.getDeptType())
                .undertakeDeptPath(undertakeDeptIdPath)
                .undertakeEmployeeId(workOrder.getUndertakeEmployeeId())
                .undertakeDeptType(Objects.isNull(undertakeDept) ? null : undertakeDept.getDeptType())
                .build();
        if (workOrderType.getIsShowAccountingCashierDeliver()) {
            detailDTO.setAccountingCashierList(workOrderAccountingCashierRelationService.selectAccountingCashierList(workOrder));
        }
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchByUserIds(Collections.singletonList(userId)).getDataThrowException(false);
        if (ObjectUtils.isEmpty(employeeList)) {
            detailDTO.setIsInitiate(false);
        } else {
            detailDTO.setIsInitiate(Objects.equals(1L, userId) || employeeList.stream().map(SysEmployee::getDeptId).anyMatch(deptId -> Objects.equals(deptId, workOrder.getInitiateDeptId())));
        }
        detailDTO.setIsShowAccountingCashierDeliver(workOrderType.getIsShowAccountingCashierDeliver());
        List<Long> empDeptIds = ObjectUtils.isEmpty(employeeList) ? Lists.newArrayList() : employeeList.stream().map(SysEmployee::getDeptId).distinct().collect(Collectors.toList());
        detailDTO.setCanFollowUp(WorkOrderStatus.getCanFollowUpStatus().contains(workOrder.getStatus()) && getCanOperate(empDeptIds, workOrder.getCurrentDeptId()));
        detailDTO.setCanTransmitInitiate(WorkOrderStatus.getCanTransmitStatus().contains(workOrder.getStatus()) && getCanOperate(empDeptIds, workOrder.getInitiateDeptId()));
        detailDTO.setCanTransmitUndertake(WorkOrderStatus.getCanTransmitStatus().contains(workOrder.getStatus()) && getCanOperate(empDeptIds, workOrder.getUndertakeDeptId()));
        detailDTO.setCanConfirm(WorkOrderStatus.getCanConfirmStatus().contains(workOrder.getStatus()) && getCanOperate(empDeptIds, workOrder.getInitiateDeptId()));
        detailDTO.setCanComment(WorkOrderStatus.getCanCommentStatus().contains(workOrder.getStatus()));
        return detailDTO;
    }
}
