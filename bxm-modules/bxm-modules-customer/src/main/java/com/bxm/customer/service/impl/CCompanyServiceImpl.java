package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.CCompany;
import com.bxm.customer.mapper.CCompanyMapper;
import com.bxm.customer.service.ICCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 企业Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class CCompanyServiceImpl extends ServiceImpl<CCompanyMapper, CCompany> implements ICCompanyService
{
    @Autowired
    private CCompanyMapper cCompanyMapper;

    /**
     * 查询企业
     * 
     * @param id 企业主键
     * @return 企业
     */
    @Override
    public CCompany selectCCompanyById(Long id)
    {
        return cCompanyMapper.selectCCompanyById(id);
    }

    /**
     * 查询企业列表
     * 
     * @param cCompany 企业
     * @return 企业
     */
    @Override
    public List<CCompany> selectCCompanyList(CCompany cCompany)
    {
        return cCompanyMapper.selectCCompanyList(cCompany);
    }

    /**
     * 新增企业
     * 
     * @param cCompany 企业
     * @return 结果
     */
    @Override
    public int insertCCompany(CCompany cCompany)
    {
        cCompany.setCreateTime(DateUtils.getNowDate());
        return cCompanyMapper.insertCCompany(cCompany);
    }

    /**
     * 修改企业
     * 
     * @param cCompany 企业
     * @return 结果
     */
    @Override
    public int updateCCompany(CCompany cCompany)
    {
        cCompany.setUpdateTime(DateUtils.getNowDate());
        return cCompanyMapper.updateCCompany(cCompany);
    }

    /**
     * 批量删除企业
     * 
     * @param ids 需要删除的企业主键
     * @return 结果
     */
    @Override
    public int deleteCCompanyByIds(Long[] ids)
    {
        return cCompanyMapper.deleteCCompanyByIds(ids);
    }

    /**
     * 删除企业信息
     * 
     * @param id 企业主键
     * @return 结果
     */
    @Override
    public int deleteCCompanyById(Long id)
    {
        return cCompanyMapper.deleteCCompanyById(id);
    }

    @Override
    public List<CCompany> selectByKeyWord(String keyWord) {
        if (StringUtils.isEmpty(keyWord)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CCompany>()
                .eq(CCompany::getKeyWord, keyWord));
    }
}
