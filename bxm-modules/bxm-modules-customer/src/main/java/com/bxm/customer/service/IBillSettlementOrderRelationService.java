package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.BillSettlementOrderRelation;

/**
 * 账单结算单关系Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface IBillSettlementOrderRelationService extends IService<BillSettlementOrderRelation>
{
    /**
     * 查询账单结算单关系
     * 
     * @param id 账单结算单关系主键
     * @return 账单结算单关系
     */
    public BillSettlementOrderRelation selectBillSettlementOrderRelationById(Long id);

    /**
     * 查询账单结算单关系列表
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 账单结算单关系集合
     */
    public List<BillSettlementOrderRelation> selectBillSettlementOrderRelationList(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 新增账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    public int insertBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 修改账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    public int updateBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 批量删除账单结算单关系
     * 
     * @param ids 需要删除的账单结算单关系主键集合
     * @return 结果
     */
    public int deleteBillSettlementOrderRelationByIds(Long[] ids);

    /**
     * 删除账单结算单关系信息
     * 
     * @param id 账单结算单关系主键
     * @return 结果
     */
    public int deleteBillSettlementOrderRelationById(Long id);

    List<BillSettlementOrderRelation> selectBatchByBillIds(List<Long> billIds);

    void deleteByBillId(Long billId);

    List<BillSettlementOrderRelation> selectByBillId(Long billId);

    BillSettlementOrderRelation selectByBillIdAndSettlementOrderId(Long billId, Long settlementOrderId);

    void deleteBySettlementOrderId(Long settlementOrderId);
}
