package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.BankPaymentResult;
import com.bxm.common.core.enums.inAccount.InAccountDeliverResult;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.bill.BillDTO;
import com.bxm.customer.domain.dto.bill.BillDetailDTO;
import com.bxm.customer.domain.dto.bill.BillReviewDTO;
import com.bxm.customer.domain.dto.bill.BillSettlementOrderDTO;
import com.bxm.customer.domain.dto.settlementOrder.*;
import com.bxm.customer.domain.vo.bill.RejectBillVO;
import com.bxm.customer.domain.vo.bill.RemovePushVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementAppendBillVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderPushVO;
import com.bxm.customer.mapper.BillMapper;
import com.bxm.customer.mapper.SettlementOrderDataMapper;
import com.bxm.customer.mapper.SettlementOrderMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptAccountService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Service
@Slf4j
public class BillServiceImpl extends ServiceImpl<BillMapper, Bill> implements IBillService
{
    @Autowired
    private BillMapper billMapper;

    @Autowired
    private SettlementOrderMapper settlementOrderMapper;

    @Autowired
    private IBillFileService billFileService;

    @Autowired
    private IBillSettlementOrderRelationService billSettlementOrderRelationService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    @Lazy
    private ISettlementOrderService settlementOrderService;

    @Autowired
    private ISettlementOrderDataService settlementOrderDataService;

    @Autowired
    private SettlementOrderDataMapper settlementOrderDataMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteDeptAccountService remoteDeptAccountService;

    /**
     * 查询账单
     *
     * @param id 账单主键
     * @return 账单
     */
    @Override
    public Bill selectBillById(Long id)
    {
        return billMapper.selectBillById(id);
    }

    /**
     * 查询账单列表
     *
     * @param bill 账单
     * @return 账单
     */
    @Override
    public List<Bill> selectBillList(Bill bill)
    {
        return billMapper.selectBillList(bill);
    }

    /**
     * 新增账单
     *
     * @param bill 账单
     * @return 结果
     */
    @Override
    public int insertBill(Bill bill)
    {
        bill.setCreateTime(DateUtils.getNowDate());
        return billMapper.insertBill(bill);
    }

    /**
     * 修改账单
     *
     * @param bill 账单
     * @return 结果
     */
    @Override
    public int updateBill(Bill bill)
    {
        bill.setUpdateTime(DateUtils.getNowDate());
        return billMapper.updateBill(bill);
    }

    /**
     * 批量删除账单
     *
     * @param ids 需要删除的账单主键
     * @return 结果
     */
    @Override
    public int deleteBillByIds(Long[] ids)
    {
        return billMapper.deleteBillByIds(ids);
    }

    /**
     * 删除账单信息
     *
     * @param id 账单主键
     * @return 结果
     */
    @Override
    public int deleteBillById(Long id)
    {
        return billMapper.deleteBillById(id);
    }

    @Override
    @Transactional
    public void createBillBySettlementOrderPush(SettlementOrderPushVO vo) {
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Map<Long, SysDept> deptMap = remoteDeptService.getByDeptIds(vo.getBusinessDeptSettlementOrders().stream().map(SettlementPushReviewDTO::getBusinessDeptId).collect(Collectors.toList()))
                .getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        checkBusinessAccountBalance(deptMap, vo.getBusinessDeptSettlementOrders());
        List<RemoteDeptAccountBalanceDetail> details = Lists.newArrayList();
        vo.getBusinessDeptSettlementOrders().forEach(row -> {
            Bill bill = createBillBySettleOrders(vo.getBillTitle(), vo.getRemark(), vo.getFiles(), row, userId, deptId, employeeName);
            if (!Objects.isNull(row.getDeductionPrice()) && row.getDeductionPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 生成抵扣记录
                details.add(RemoteDeptAccountBalanceDetail.builder()
                        .businessDeptId(row.getBusinessDeptId())
                        .sourceType(DeptAccountBalanceDetailSource.BILL.getCode())
                        .businessNo(bill.getBillNo())
                        .businessType(DeptBalanceDetailBusinessType.BILL.getCode())
                        .businessId(bill.getId())
                        .incomeType(IncomeType.EXPENDITURE.getCode())
                        .changeAmount(row.getDeductionPrice())
                        .status(DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode())
                        .remark(bill.getBillTitle())
                        .createBy(employeeName)
                        .build());
            }
        });
        if (!ObjectUtils.isEmpty(details)) {
            remoteDeptAccountService.remoteBatchCreateBalanceDetail(details);
        }
    }

    private void checkBusinessAccountBalance(Map<Long, SysDept> deptMap, List<SettlementPushReviewDTO> businessDeptSettlementOrders) {
        businessDeptSettlementOrders.forEach(row -> {
            if (!Objects.isNull(row.getDeductionPrice()) && row.getDeductionPrice().compareTo(BigDecimal.ZERO) > 0) {
                SysDept businessDept = deptMap.get(row.getBusinessDeptId());
                if (Objects.isNull(businessDept) || businessDept.getAccountBalance().compareTo(row.getDeductionPrice()) < 0) {
                    throw new ServiceException((Objects.isNull(businessDept) ? "" : businessDept.getDeptName()) + "当前预存账户金额不足");
                }
            }
        });
    }

    @Override
    public IPage<BillDTO> billList(Long deptId, String billTitle, Long businessDeptId, Integer status, String createTimeStart, String createTimeEnd, Integer pageNum, Integer pageSize) {
        IPage<BillDTO> result = new Page<>(pageNum, pageSize);
        List<Long> businessDeptIds = com.google.common.collect.Lists.newArrayList();
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin()) {
            SysDept currentDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
            if (!currentDept.getIsHeadquarters()) {
                if (ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                    return result;
                } else {
                    businessDeptIds = userDeptDTO.getDeptIds();
                }
            } else {
                if (currentDept.getDeptType() == 1) {
                    if (ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        return result;
                    } else {
                        businessDeptIds = userDeptDTO.getDeptIds();
                    }
                }
            }
        }
        if (!StringUtils.isEmpty(createTimeStart)) {
            createTimeStart = createTimeStart + " 00:00:00";
        }
        if (!StringUtils.isEmpty(createTimeEnd)) {
            createTimeEnd = createTimeEnd + " 23:59:59";
        }
        IPage<Bill> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<Bill>()
                .eq(Bill::getIsDel, false).in(!ObjectUtils.isEmpty(businessDeptIds), Bill::getBusinessDeptId, businessDeptIds)
                .eq(!Objects.isNull(businessDeptId), Bill::getBusinessDeptId, businessDeptId)
                .eq(!Objects.isNull(status), Bill::getStatus, status)
                .le(!StringUtils.isEmpty(createTimeEnd), Bill::getCreateTime, createTimeEnd)
                .ge(!StringUtils.isEmpty(createTimeStart), Bill::getCreateTime, createTimeStart)
                .and(!StringUtils.isEmpty(billTitle), w -> w.like(Bill::getBillTitle, billTitle).or().like(Bill::getBillNo, billTitle))
                .orderByDesc(Bill::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<Long> deptIds = Lists.newArrayList();
            iPage.getRecords().forEach(bill -> {
                if (!Objects.isNull(bill.getBusinessTopDeptId())) {
                    deptIds.add(bill.getBusinessTopDeptId());
                }
                if (!Objects.isNull(bill.getBusinessDeptId())) {
                    deptIds.add(bill.getBusinessDeptId());
                }
            });
            Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            Map<Long, List<SettlementPushReviewOrderDTO>> billSettlementOrderMap = selectBillSettlementOrderList(iPage.getRecords().stream().map(Bill::getId).collect(Collectors.toList()));
            result.setRecords(iPage.getRecords().stream().map(bill -> {
                List<SettlementPushReviewOrderDTO> settlementOrderList = billSettlementOrderMap.getOrDefault(bill.getId(), Lists.newArrayList());
                BigDecimal totalPrice = ObjectUtils.isEmpty(settlementOrderList) ? BigDecimal.ZERO : settlementOrderList.stream().map(SettlementPushReviewOrderDTO::getTotalPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal discountPrice = ObjectUtils.isEmpty(settlementOrderList) ? BigDecimal.ZERO : settlementOrderList.stream().map(SettlementPushReviewOrderDTO::getDiscountPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal oughtPrice = totalPrice.subtract(Objects.isNull(bill.getDeductionPrice()) ? BigDecimal.ZERO : bill.getDeductionPrice()).subtract(discountPrice);
                return BillDTO.builder()
                        .id(bill.getId())
                        .billTitle(bill.getBillTitle())
                        .billNo(bill.getBillNo())
                        .businessTopDeptId(bill.getBusinessTopDeptId())
                        .businessTopDeptName(deptNameMap.getOrDefault(bill.getBusinessTopDeptId(), ""))
                        .businessDeptId(bill.getBusinessDeptId())
                        .businessDeptName(deptNameMap.getOrDefault(bill.getBusinessDeptId(), ""))
                        .settlementOrderCount(settlementOrderList.size())
                        .totalPrice(totalPrice)
                        .totalPriceStr(totalPrice.stripTrailingZeros().toPlainString())
                        .deductionPrice(bill.getDeductionPrice())
                        .deductionPriceStr(bill.getDeductionPrice().stripTrailingZeros().toPlainString())
                        .discountPrice(discountPrice)
                        .oughtPrice(oughtPrice)
                        .oughtPriceStr(oughtPrice.stripTrailingZeros().toPlainString())
                        .createTime(bill.getCreateTime())
                        .createTimeStr(bill.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)))
                        .status(bill.getStatus())
                        .statusName(BillStatus.getByCode(bill.getStatus()).getName())
                        .settlementOrders(settlementOrderList)
                        .build();
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public BillDetailDTO billDetail(Long id) {
        Bill bill = checkBillExists(id);
        SysDept businessTopDept = remoteDeptService.getDeptInfo(bill.getBusinessTopDeptId()).getDataThrowException();
        SysDept businessDept = remoteDeptService.getDeptInfo(bill.getBusinessDeptId()).getDataThrowException();
        Map<Long, List<SettlementPushReviewOrderDTO>> billSettlementOrderMap = selectBillSettlementOrderList(Collections.singletonList(id));
        List<SettlementPushReviewOrderDTO> settlementOrderList = billSettlementOrderMap.getOrDefault(id, Lists.newArrayList());
        BigDecimal totalPrice = ObjectUtils.isEmpty(settlementOrderList) ? BigDecimal.ZERO : settlementOrderList.stream().map(SettlementPushReviewOrderDTO::getTotalPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal discountPrice = ObjectUtils.isEmpty(settlementOrderList) ? BigDecimal.ZERO : settlementOrderList.stream().map(SettlementPushReviewOrderDTO::getDiscountPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal oughtPrice = totalPrice.subtract(Objects.isNull(bill.getDeductionPrice()) ? BigDecimal.ZERO : bill.getDeductionPrice()).subtract(discountPrice);
        return BillDetailDTO.builder()
                .id(bill.getId())
                .billNo(bill.getBillNo())
                .billTitle(bill.getBillTitle())
                .remark(bill.getRemark())
                .files(billFileService.selectBillFileByBillId(bill.getId(), BillFileType.CREATE.getCode()))
                .businessTopDeptId(bill.getBusinessTopDeptId())
                .businessTopDeptName(Objects.isNull(businessTopDept) ? "" : businessTopDept.getDeptName())
                .businessDeptId(bill.getBusinessDeptId())
                .businessDeptName(Objects.isNull(businessDept) ? "" : businessDept.getDeptName())
                .status(bill.getStatus())
                .statusName(BillStatus.getByCode(bill.getStatus()).getName())
                .totalPrice(totalPrice)
                .deductionPrice(bill.getDeductionPrice())
                .discountPrice(discountPrice)
                .oughtPrice(oughtPrice)
                .settlementOrderList(settlementOrderList)
                .build();
    }

    @Override
    @Transactional
    public void delete(CommonIdVO vo) {
        Bill bill = checkBillExists(vo.getId());
        if (!BillStatus.canDeleteStatus().contains(bill.getStatus())) {
            throw new ServiceException("账单状态不符合");
        }
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        updateById(new Bill().setId(bill.getId()).setIsDel(true));
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(vo.getId());
        deleteBillSettlementOrders(relations);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bill.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("删除账单")
                    .setOperName(employeeName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        Map<String, String> map = new HashMap<>();
        map.put("账单名", bill.getBillTitle());
        map.put("账单编号", bill.getBillNo());
        String settlementOperContent = JSONObject.toJSONString(map);
        relations.forEach(relation -> {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(relation.getSettlementOrderId())
                        .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                        .setDeptId(deptId)
                        .setOperType("移除，因账单删除")
                        .setOperName(employeeName)
                        .setOperUserId(userId)
                        .setOperContent(settlementOperContent));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    public List<SettlementPushReviewDTO> rePushReview(CommonIdVO vo) {
        if (Objects.isNull(vo.getId())) {
            return Collections.emptyList();
        }
        Bill bill = checkBillExists(vo.getId());
        if (!BillStatus.canRePushStatus().contains(bill.getStatus())) {
            throw new ServiceException("账单状态不符合");
        }
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(vo.getId());
        if (ObjectUtils.isEmpty(relations)) {
            throw new ServiceException("暂无结算单，不允许重新推送");
        }
        return settlementOrderService.pushReview(relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public void confirmRePush(SettlementOrderPushVO vo) {
        if (Objects.isNull(vo.getBillId())) {
            return;
        }
        Bill bill = checkBillExists(vo.getBillId());
        if (!BillStatus.canRePushStatus().contains(bill.getStatus())) {
            throw new ServiceException("账单状态不符合");
        }
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(vo.getBillId());
        if (ObjectUtils.isEmpty(relations)) {
            throw new ServiceException("暂无结算单，不允许重新推送");
        }
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        List<RemoteDeptAccountBalanceDetail> details = Lists.newArrayList();
        vo.getBusinessDeptSettlementOrders().forEach(row -> {
            updateBillInfo(vo.getBillId(), vo.getBillTitle(), vo.getRemark(), vo.getFiles(), row, userId, deptId, employeeName);
            if (!Objects.isNull(row.getDeductionPrice()) && row.getDeductionPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 生成抵扣记录
                details.add(RemoteDeptAccountBalanceDetail.builder()
                        .businessDeptId(row.getBusinessDeptId())
                        .sourceType(DeptAccountBalanceDetailSource.BILL.getCode())
                        .businessNo(bill.getBillNo())
                        .businessType(DeptBalanceDetailBusinessType.BILL.getCode())
                        .businessId(bill.getId())
                        .incomeType(IncomeType.EXPENDITURE.getCode())
                        .changeAmount(row.getDeductionPrice())
                        .status(DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode())
                        .remark(bill.getBillTitle())
                        .createBy(employeeName)
                        .build());
            }
        });
        if (!ObjectUtils.isEmpty(details)) {
            remoteDeptAccountService.remoteBatchCreateBalanceDetail(details);
        }
    }

    private void updateBillInfo(Long billId, String billTitle, String remark, List<CommonFileVO> files, SettlementPushReviewDTO dto, Long userId, Long deptId, String employeeName) {
        Bill bill = new Bill().setId(billId).setBillTitle(billTitle)
                .setStatus(BillStatus.PUSH_WAIT_CONFIRM.getCode())
                .setTotalPrice(dto.getTotalPrice())
                .setDeductionPrice(dto.getDeductionPrice()).setOughtPrice(dto.getTotalPrice().subtract(Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice()).subtract(Objects.isNull(dto.getDeductionPrice()) ? BigDecimal.ZERO : dto.getDeductionPrice()))
                .setRemark(remark);
        updateById(bill);
        billFileService.removeAndSaveNewFile(billId, files, BillFileType.CREATE.getCode());
        Map<Long, SettlementOrder> settlementOrderMap = ObjectUtils.isEmpty(dto.getSettlementOrderList()) ? Maps.newHashMap() :
                settlementOrderMapper.selectList(new LambdaQueryWrapper<SettlementOrder>().in(SettlementOrder::getId, dto.getSettlementOrderList().stream().map(SettlementPushReviewOrderDTO::getSettlementOrderId).collect(Collectors.toList()))
                                .eq(SettlementOrder::getIsDel, false)).stream()
                        .collect(Collectors.toMap(SettlementOrder::getId, Function.identity()));
        if (!ObjectUtils.isEmpty(dto.getSettlementOrderList())) {
            settlementOrderMapper.update(new SettlementOrder().setBillNo(bill.getBillNo()).setBillTitle(billTitle).setStatus(SettlementOrderStatus.SETTLEMENTING.getCode()).setIsWaitForEdit(false),
                    new LambdaQueryWrapper<SettlementOrder>().in(SettlementOrder::getId, dto.getSettlementOrderList().stream().map(SettlementPushReviewOrderDTO::getSettlementOrderId).collect(Collectors.toList()))
                            .eq(SettlementOrder::getIsDel, false));
            billSettlementOrderRelationService.deleteByBillId(billId);
            billSettlementOrderRelationService.saveBatch(dto.getSettlementOrderList().stream().map(row -> new BillSettlementOrderRelation().setBillId(bill.getId()).setSettlementOrderId(row.getSettlementOrderId())).collect(Collectors.toList()));
        }
        String operImages = ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bill.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("重新推送")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperRemark(remark)
                    .setOperImages(operImages));
            if (!ObjectUtils.isEmpty(dto.getSettlementOrderList())) {
                dto.getSettlementOrderList().forEach(settlementOrder -> {
                    Map<String, String> operContentMap = new HashMap<>();
                    operContentMap.put("前置状态", SettlementOrderStatus.getByCode(settlementOrderMap.getOrDefault(settlementOrder.getSettlementOrderId(), new SettlementOrder()).getStatus()).getName());
                    operContentMap.put("后置状态", SettlementOrderStatus.SETTLEMENTING.getName());
                    operContentMap.put("账单标题", billTitle);
                    operContentMap.put("账单编号", bill.getBillNo());
                    String operContent = JSONObject.toJSONString(operContentMap);
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getSettlementOrderId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("账单重新推送")
                            .setOperName(employeeName)
                            .setOperUserId(userId)
                            .setOperRemark(remark)
                            .setOperImages(operImages)
                            .setOperContent(operContent));
                });
            }
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void removePush(RemovePushVO vo) {
        BillSettlementOrderRelation billSettlementOrderRelation = billSettlementOrderRelationService.selectByBillIdAndSettlementOrderId(vo.getBillId(), vo.getSettlementOrderId());
        if (Objects.isNull(billSettlementOrderRelation)) {
            return;
        }
        billSettlementOrderRelationService.removeById(billSettlementOrderRelation.getId());
        SettlementOrder settlementOrder = settlementOrderMapper.selectById(vo.getSettlementOrderId());
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            return;
        }
        Bill bill = getById(vo.getBillId());
        if (Objects.isNull(bill) || bill.getIsDel()) {
            return;
        }
        settlementOrderService.update(new LambdaUpdateWrapper<SettlementOrder>()
                .eq(SettlementOrder::getId, settlementOrder.getId())
                .set(SettlementOrder::getStatus, SettlementOrderStatus.WAIT_PUSH.getCode())
                .set(SettlementOrder::getIsWaitForEdit, false)
                .set(SettlementOrder::getBillNo, null)
                .set(SettlementOrder::getBillTitle, null));
        CommonFileVO settlementDataFile = createSettlementDataFile(settlementOrder);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Map<String, String> billOperContent = new HashMap<>();
        billOperContent.put("结算单标题", settlementOrder.getSettlementTitle());
        billOperContent.put("结算单编号", settlementOrder.getSettlementOrderNo());
        Map<String, String> settlementOrderOperContent = new HashMap<>();
        billOperContent.put("账单名", bill.getBillTitle());
        billOperContent.put("账单编号", bill.getBillNo());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getBillId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("移除结算单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(JSONObject.toJSONString(billOperContent))
                    .setOperImages(Objects.isNull(settlementDataFile) ? "" : JSONArray.toJSONString(Collections.singletonList(settlementDataFile))));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getSettlementOrderId())
                    .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("从账单移出")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(JSONObject.toJSONString(settlementOrderOperContent)));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private CommonFileVO createSettlementDataFile(SettlementOrder settlementOrder) {
        SettlementOrderDataSearchVO vo = new SettlementOrderDataSearchVO();
        vo.setPageNum(1);
        vo.setPageSize(-1);
        vo.setBusinessDeptId(settlementOrder.getBusinessDeptId());
        vo.setSettlementOrderId(settlementOrder.getId());
        SysDept businessDept = remoteDeptService.getDeptInfo(vo.getBusinessDeptId()).getDataThrowException();
        if (Objects.isNull(businessDept)) {
            return null;
        }
        String fileUrl;
        String fileName = businessDept.getDeptName() + " " + settlementOrder.getSettlementTitle() + "明细";
        List<SettlementOrderDataDTO> data = settlementOrderDataService.settlementOrderDataListBySettlementOrderId(vo).getRecords();
        if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode())) {
            List<SettlementOrderPeriodDataDTO> exports = data.stream().map(d -> {
                SettlementOrderPeriodDataDTO dto = new SettlementOrderPeriodDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setPeriodInAccountBankPaymentInputTime(Objects.isNull(d.getPeriodInAccountBankPaymentInputTime()) ? "" : d.getPeriodInAccountBankPaymentInputTime().toString());
                dto.setPeriodInAccountInTime(Objects.isNull(d.getPeriodInAccountInTime()) ? "" : d.getPeriodInAccountInTime().toString());
                dto.setPeriodInAccountEndTime(Objects.isNull(d.getPeriodInAccountEndTime()) ? "" : d.getPeriodInAccountEndTime().toString());
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderPeriodDataDTO> util = new ExcelUtil<>(SettlementOrderPeriodDataDTO.class);
            fileUrl = util.exportExcelAndUpload(exports, fileName);
        } else {
            List<SettlementOrderCustomerDataDTO> exports = data.stream().map(d -> {
                SettlementOrderCustomerDataDTO dto = new SettlementOrderCustomerDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setCustomerServiceTaxType(d.getCustomerServiceTaxTypeStr());
                dto.setCreateTime(Objects.isNull(d.getCreateTime()) ? "" : d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderCustomerDataDTO> util = new ExcelUtil<>(SettlementOrderCustomerDataDTO.class);
            fileUrl = util.exportExcelAndUpload(exports, fileName);
        }
        return CommonFileVO.builder().fileUrl(fileUrl).fileName(fileName + ".xlsx").build();
    }

    @Override
    @Transactional
    public void revokeBill(CommonIdVO vo) {
        Bill bill = checkBillExists(vo.getId());
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(bill.getId());
        updateById(new Bill().setId(vo.getId()).setStatus(BillStatus.REVOKE.getCode()).setDeductionPrice(BigDecimal.ZERO));
        remoteDeptAccountService.remoteBatchCancelBalanceDetail(CancelBalanceDetailVO.builder()
                .sourceType(DeptAccountBalanceDetailSource.BILL.getCode())
                .businessType(DeptBalanceDetailBusinessType.BILL.getCode())
                .businessIds(Collections.singletonList(vo.getId())).build());
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("撤回账单")
                    .setOperName(employeeName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!ObjectUtils.isEmpty(relations)) {
            settlementOrderMapper.update(new SettlementOrder().setStatus(SettlementOrderStatus.REVOKE.getCode()), new LambdaQueryWrapper<SettlementOrder>()
                    .in(SettlementOrder::getId, relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList())));
            relations.forEach(relation -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(relation.getSettlementOrderId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("撤回结算单")
                            .setOperName(employeeName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    public List<SettlementPushReviewOrderDTO> settlementListByBillId(Long billId) {
        if (Objects.isNull(billId)) {
            return Collections.emptyList();
        }
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(billId);
        if (ObjectUtils.isEmpty(relations)) {
            return Collections.emptyList();
        }
        List<SettlementOrder> settlementOrders = settlementOrderMapper.selectBatchIds(relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()))
                .stream().filter(row -> !row.getIsDel()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(settlementOrders)) {
            return Lists.newArrayList();
        }
        Map<Long, Long> dataCountMap = ObjectUtils.isEmpty(settlementOrders) ? Maps.newHashMap() :
                settlementOrderDataMapper.selectBatchSettlementOrderDataCount(settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
        return settlementOrders.stream().map(settlementOrder -> {
            Long dataCount = dataCountMap.getOrDefault(settlementOrder.getId(), 0L);
            BigDecimal totalPrice = settlementOrder.getPrice().multiply(new BigDecimal(dataCount));
            return SettlementPushReviewOrderDTO.builder()
                    .settlementOrderId(settlementOrder.getId())
                    .settlementOrderTitle(settlementOrder.getSettlementTitle())
                    .settlementType(settlementOrder.getSettlementType())
                    .settlementTypeName(SettlementType.getByCode(settlementOrder.getSettlementType()).getName() + (settlementOrder.getIsSupplement() ? "（补差）" : ""))
                    .unit(settlementOrder.getUnit())
                    .dataCount(dataCount)
                    .price(settlementOrder.getPrice())
                    .totalPrice(totalPrice)
                    .discountPrice(settlementOrder.getDiscountPrice())
                    .settlementPrice(totalPrice.subtract(Objects.isNull(settlementOrder.getDiscountPrice()) ? BigDecimal.ZERO : settlementOrder.getDiscountPrice()))
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void rejectBill(RejectBillVO vo) {
        Bill bill = checkBillExists(vo.getId());
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(bill.getId());
        updateById(new Bill().setId(vo.getId()).setStatus(BillStatus.REJECT.getCode()).setDeductionPrice(BigDecimal.ZERO));
        remoteDeptAccountService.remoteBatchCancelBalanceDetail(CancelBalanceDetailVO.builder()
                .sourceType(DeptAccountBalanceDetailSource.BILL.getCode())
                .businessType(DeptBalanceDetailBusinessType.BILL.getCode())
                .businessIds(Collections.singletonList(vo.getId())).build());
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        String operImages = ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("驳回账单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperRemark(vo.getRemark())
                    .setOperImages(operImages));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!ObjectUtils.isEmpty(relations)) {
            settlementOrderMapper.update(new SettlementOrder().setStatus(SettlementOrderStatus.REJECT.getCode()), new LambdaQueryWrapper<SettlementOrder>()
                    .in(SettlementOrder::getId, relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList())));
            if (!ObjectUtils.isEmpty(vo.getSettlementOrderIds())) {
                settlementOrderMapper.update(new SettlementOrder().setIsWaitForEdit(true), new LambdaQueryWrapper<SettlementOrder>()
                        .in(SettlementOrder::getId, vo.getSettlementOrderIds()));
            }
            relations.forEach(relation -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(relation.getSettlementOrderId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("驳回结算单")
                            .setOperName(employeeName)
                            .setOperUserId(userId)
                            .setOperRemark(vo.getRemark())
                            .setOperImages(operImages));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public void confirmBill(CommonIdVO vo) {
        Bill bill = checkBillExists(vo.getId());
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(bill.getId());
        updateById(new Bill().setId(vo.getId()).setStatus(BillStatus.CONFIRM.getCode()));
        remoteDeptAccountService.remoteBatchConfirmBalanceDetail(ConfirmBalanceDetailVO.builder()
                .sourceType(DeptAccountBalanceDetailSource.BILL.getCode())
                .businessType(DeptBalanceDetailBusinessType.BILL.getCode())
                .businessIds(Collections.singletonList(vo.getId())).build());
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        String operImages = ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("确认账单")
                    .setOperName(employeeName)
                    .setOperImages(operImages)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!ObjectUtils.isEmpty(relations)) {
            List<SettlementOrder> settlementOrders = settlementOrderMapper.selectBatchIds(relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()));
            if (ObjectUtils.isEmpty(settlementOrders)) {
                return;
            }
            settlementOrderMapper.update(new SettlementOrder().setStatus(SettlementOrderStatus.CONFIRM.getCode()), new LambdaQueryWrapper<SettlementOrder>()
                    .in(SettlementOrder::getId, settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList())));
            List<SettlementOrder> newUserPrepaymentOrders = settlementOrders.stream().filter(s -> Objects.equals(SettlementType.NEW_USER_PREPAYMENT.getCode(), s.getSettlementType())).collect(Collectors.toList());
            List<SettlementOrder> periodPrepaymentOrders = settlementOrders.stream().filter(s -> Objects.equals(SettlementType.IN_ACCOUNT_PREPAYMENT.getCode(), s.getSettlementType())).collect(Collectors.toList());
            List<SettlementOrder> inAccountOrders = settlementOrders.stream().filter(s -> Objects.equals(SettlementType.IN_ACCOUNT.getCode(), s.getSettlementType()) && !s.getIsSupplement()).collect(Collectors.toList());
            List<SettlementOrder> supplementAccountOrders = settlementOrders.stream().filter(s -> Objects.equals(SettlementType.IN_ACCOUNT.getCode(), s.getSettlementType()) && s.getIsSupplement()).collect(Collectors.toList());
            List<BusinessLogDTO> logDTOList = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(newUserPrepaymentOrders)) {
                List<Long> newUserPrepaymentOrderIds = newUserPrepaymentOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList());
                Map<Long, Long> dataCountMap = settlementOrderDataMapper.selectBatchSettlementOrderDataCount(newUserPrepaymentOrderIds)
                        .stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
                // 生成新户预收流水
                remoteDeptAccountService.remoteBatchCreateBalanceDetail(newUserPrepaymentOrders.stream().map(row -> RemoteDeptAccountBalanceDetail.builder()
                        .businessDeptId(row.getBusinessDeptId()).sourceType(DeptAccountBalanceDetailSource.SETTLEMENT_ORDER.getCode())
                        .businessNo(row.getSettlementOrderNo()).businessType(DeptBalanceDetailBusinessType.SETTLEMENT_ORDER.getCode()).businessId(row.getId())
                        .incomeType(IncomeType.INCOME.getCode()).changeAmount(row.getPrice().multiply(new BigDecimal(dataCountMap.getOrDefault(row.getId(), 0L))))
                        .status(DeptAccountBalanceDetailStatus.CONFIRMED.getCode()).createBy(employeeName).build()).collect(Collectors.toList()));
                settlementOrderDataMapper.updateNewUserPrepaymentDataSettlementStatus(newUserPrepaymentOrderIds, BusinessSettlementStatus.SETTLED.getCode());
                List<Long> customerServiceIds = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                                .in(SettlementOrderData::getSettlementOrderId, newUserPrepaymentOrderIds)
                                .eq(SettlementOrderData::getBusinessType, 1)
                                .select(SettlementOrderData::getBusinessId))
                        .stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(customerServiceIds)) {
                    logDTOList.addAll(customerServiceIds.stream().map(customerServiceId -> new BusinessLogDTO().setBusinessId(customerServiceId)
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                            .setDeptId(deptId)
                            .setOperType("完成结算")
                            .setOperName(employeeName)
                            .setOperUserId(userId)).collect(Collectors.toList()));
                }
            }
            if (!ObjectUtils.isEmpty(periodPrepaymentOrders)) {
                List<Long> periodPrepaymentOrderIds = periodPrepaymentOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList());
                Map<Long, Long> dataCountMap = settlementOrderDataMapper.selectBatchSettlementOrderDataCount(periodPrepaymentOrderIds)
                        .stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
                // 生成新户预收流水
                remoteDeptAccountService.remoteBatchCreateBalanceDetail(periodPrepaymentOrders.stream().map(row -> RemoteDeptAccountBalanceDetail.builder()
                        .businessDeptId(row.getBusinessDeptId()).sourceType(DeptAccountBalanceDetailSource.SETTLEMENT_ORDER.getCode())
                        .businessNo(row.getSettlementOrderNo()).businessType(DeptBalanceDetailBusinessType.SETTLEMENT_ORDER.getCode()).businessId(row.getId())
                        .incomeType(IncomeType.INCOME.getCode()).changeAmount(row.getPrice().multiply(new BigDecimal(dataCountMap.getOrDefault(row.getId(), 0L))))
                        .status(DeptAccountBalanceDetailStatus.CONFIRMED.getCode()).createBy(employeeName).build()).collect(Collectors.toList()));
                List<Long> customerServicePeriodMonthId = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                                .in(SettlementOrderData::getSettlementOrderId, periodPrepaymentOrderIds)
                                .eq(SettlementOrderData::getBusinessType, 2)
                                .select(SettlementOrderData::getBusinessId))
                        .stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(customerServicePeriodMonthId)) {
                    logDTOList.addAll(customerServicePeriodMonthId.stream().map(customerServicePeriodId -> new BusinessLogDTO().setBusinessId(customerServicePeriodId)
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD.getCode())
                            .setDeptId(deptId)
                            .setOperType("完成结算")
                            .setOperName(employeeName)
                            .setOperUserId(userId)).collect(Collectors.toList()));
                }
                settlementOrderDataMapper.updateInAccountDataPrepayStatus(periodPrepaymentOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), PeriodPrepayStatus.PREPAID_SUCCESS.getCode());
            }
            if (!ObjectUtils.isEmpty(inAccountOrders)) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(inAccountOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), BusinessSettlementStatus.SETTLED.getCode());
                List<Long> customerServicePeriodMonthIds = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                                .in(SettlementOrderData::getSettlementOrderId, inAccountOrders)
                                .eq(SettlementOrderData::getBusinessType, 2)
                                .select(SettlementOrderData::getBusinessId))
                        .stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
                    logDTOList.addAll(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new BusinessLogDTO().setBusinessId(customerServicePeriodMonthId)
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD.getCode())
                            .setDeptId(deptId)
                            .setOperType("完成结算")
                            .setOperName(employeeName)
                            .setOperUserId(userId)).collect(Collectors.toList()));
                }
            }
            if (!ObjectUtils.isEmpty(supplementAccountOrders)) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(supplementAccountOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), BusinessSettlementStatus.SETTLED.getCode());
                List<Long> customerServicePeriodMonthIds = settlementOrderDataMapper.selectList(new LambdaQueryWrapper<SettlementOrderData>()
                                .in(SettlementOrderData::getSettlementOrderId, inAccountOrders)
                                .eq(SettlementOrderData::getBusinessType, 2)
                                .select(SettlementOrderData::getBusinessId))
                        .stream().map(SettlementOrderData::getBusinessId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
                    logDTOList.addAll(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new BusinessLogDTO().setBusinessId(customerServicePeriodMonthId)
                            .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD.getCode())
                            .setDeptId(deptId)
                            .setOperType("完成补差结算")
                            .setOperName(employeeName)
                            .setOperUserId(userId)).collect(Collectors.toList()));
                }
            }
            settlementOrders.forEach(settlementOrder -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("确认结算单")
                            .setOperName(employeeName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
            if (!ObjectUtils.isEmpty(logDTOList)) {
                try {
                    asyncLogService.saveBatchBusinessLog(logDTOList);
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
    }

    @Override
    public BillReviewDTO billReview(Long billId) {
        List<BillSettlementOrderRelation> relations = billSettlementOrderRelationService.selectByBillId(billId);
        List<BillSettlementOrderDTO> settlementOrderDataList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(relations)) {
            List<SettlementOrder> settlementOrders = settlementOrderMapper.selectList(new LambdaQueryWrapper<SettlementOrder>()
                    .eq(SettlementOrder::getIsDel, false).in(SettlementOrder::getId, relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList())));
            if (!ObjectUtils.isEmpty(settlementOrders)) {
                List<SettlementOrderDataDTO> settlementOrderDatas = settlementOrderDataMapper.settlementOrderDataListBySettlementOrderIds(settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()));
                if (!ObjectUtils.isEmpty(settlementOrderDatas)) {
                    Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(settlementOrderDatas.stream().map(SettlementOrderDataDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
                    List<Long> periodIds = settlementOrderDatas.stream().filter(d -> d.getBusinessType() == 2).map(SettlementOrderDataDTO::getBusinessId).collect(Collectors.toList());
                    Map<Long, List<TagDTO>> periodTagMap = ObjectUtils.isEmpty(periodIds) ? Maps.newHashMap() :
                            businessTagRelationService.getTagsByBusinessTypeForList(periodIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
                    settlementOrderDatas.forEach(d -> {
                        d.setCustomerServiceTags(tagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
                        d.setCustomerServiceTaxTypeStr(TaxType.getByCode(d.getCustomerServiceTaxType()).getDesc());
                        d.setCustomerServiceAdvisorInfo((StringUtils.isEmpty(d.getCustomerServiceAdvisorDeptName()) ? "" : d.getCustomerServiceAdvisorDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAdvisorEmployeeName()) ? "" : "（" + d.getCustomerServiceAdvisorEmployeeName() + "）"));
                        d.setCustomerServiceAccountingInfo((StringUtils.isEmpty(d.getCustomerServiceAccountingDeptName()) ? "" : d.getCustomerServiceAccountingDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAccountingEmployeeName()) ? "" : "（" + d.getCustomerServiceAccountingEmployeeName() + "）"));
                        d.setCustomerServiceFirstAccountPeriod(StringUtils.isEmpty(d.getCustomerServiceFirstAccountPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getCustomerServiceFirstAccountPeriod())));
                        d.setPeriod(StringUtils.isEmpty(d.getPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getPeriod())));
                        d.setPeriodServiceTypeStr(CustomerServicePeriodMonthServiceType.getByCode(d.getPeriodServiceType()).getName());
                        d.setPeriodTaxTypeStr(TaxType.getByCode(d.getPeriodTaxType()).getDesc());
                        d.setPeriodTags(d.getBusinessType() == 2 ? periodTagMap.getOrDefault(d.getBusinessId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")) : "");
                        d.setPeriodAdvisorInfo(StringUtils.isEmpty(d.getPeriodAdvisorDeptName()) ? "" : d.getPeriodAdvisorDeptName() + (StringUtils.isEmpty(d.getPeriodAdvisorEmployeeName()) ? "" : "（" + d.getPeriodAdvisorEmployeeName() + "）"));
                        d.setPeriodAccountingInfo(StringUtils.isEmpty(d.getPeriodAccountingDeptName()) ? "" : d.getPeriodAccountingDeptName() + (StringUtils.isEmpty(d.getPeriodAccountingEmployeeName()) ? "" : "（" + d.getPeriodAccountingEmployeeName() + "）"));
                        d.setPeriodAccountStatusStr(AccountingStatus.getByCode(d.getPeriodAccountStatus()).getName());
                        d.setPeriodInAccountDeliverResultStr(AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountDeliverResult()).getName());
                        d.setPeriodBankPaymentInputResultStr(Objects.isNull(d.getPeriodBankPaymentInputResult()) ? "" : BankPaymentResult.getByCode(d.getPeriodBankPaymentInputResult()).getDesc());
                        d.setPeriodInAccountResultStr(Objects.isNull(d.getPeriodInAccountResult()) ? "" : AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountResult()).getName());
                        d.setCreateInfo((StringUtils.isEmpty(d.getCreateDeptName()) ? "" : d.getCreateDeptName()) + (StringUtils.isEmpty(d.getCreateBy()) ? "" : "（" + d.getCreateBy() + "）"));
                        d.setPeriodSettlementStatusStr(Objects.isNull(d.getPeriodSettlementStatus()) ? "" : BusinessSettlementStatus.getByCode(d.getPeriodSettlementStatus()).getShowName());
                        d.setPeriodPrepayStatusStr(Objects.isNull(d.getPeriodPrepayStatus()) ? "" : PeriodPrepayStatus.getByCode(d.getPeriodPrepayStatus()).getDesc());
                    });
                }
                Map<Long, List<SettlementOrderDataDTO>> dataMap = settlementOrderDatas.stream().collect(Collectors.groupingBy(SettlementOrderDataDTO::getSettlementOrderId));
                settlementOrderDataList = settlementOrders.stream().map(settlementOrder -> BillSettlementOrderDTO.builder()
                        .settlementOrderTitle(settlementOrder.getSettlementTitle())
                        .settlementType(settlementOrder.getSettlementType())
                        .settlementOrderDataList(dataMap.getOrDefault(settlementOrder.getId(), Lists.newArrayList()))
                        .build()).collect(Collectors.toList());
            }
        }
        return BillReviewDTO.builder()
                .billDetail(billDetail(billId))
                .billSettlementOrderList(settlementOrderDataList)
                .build();
    }

    private List<SettlementOrderDataDTO> buildSettlementOrderDataList(List<SettlementOrderDataDTO> data) {
        if (ObjectUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(SettlementOrderDataDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
        List<Long> periodIds = data.stream().filter(d -> d.getBusinessType() == 2).map(SettlementOrderDataDTO::getBusinessId).collect(Collectors.toList());
        Map<Long, List<TagDTO>> periodTagMap = ObjectUtils.isEmpty(periodIds) ? Maps.newHashMap() :
                businessTagRelationService.getTagsByBusinessTypeForList(periodIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
        data.forEach(d -> {
            d.setCustomerServiceTags(tagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
            d.setCustomerServiceTaxTypeStr(TaxType.getByCode(d.getCustomerServiceTaxType()).getDesc());
            d.setCustomerServiceAdvisorInfo((StringUtils.isEmpty(d.getCustomerServiceAdvisorDeptName()) ? "" : d.getCustomerServiceAdvisorDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAdvisorEmployeeName()) ? "" : "（" + d.getCustomerServiceAdvisorEmployeeName() + "）"));
            d.setCustomerServiceAccountingInfo((StringUtils.isEmpty(d.getCustomerServiceAccountingDeptName()) ? "" : d.getCustomerServiceAccountingDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAccountingEmployeeName()) ? "" : "（" + d.getCustomerServiceAccountingEmployeeName() + "）"));
            d.setCustomerServiceFirstAccountPeriod(StringUtils.isEmpty(d.getCustomerServiceFirstAccountPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getCustomerServiceFirstAccountPeriod())));
            d.setPeriod(StringUtils.isEmpty(d.getPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getPeriod())));
            d.setPeriodServiceTypeStr(CustomerServicePeriodMonthServiceType.getByCode(d.getPeriodServiceType()).getName());
            d.setPeriodTaxTypeStr(TaxType.getByCode(d.getPeriodTaxType()).getDesc());
            d.setPeriodTags(d.getBusinessType() == 2 ? periodTagMap.getOrDefault(d.getBusinessId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")) : "");
            d.setPeriodAdvisorInfo(StringUtils.isEmpty(d.getPeriodAdvisorDeptName()) ? "" : d.getPeriodAdvisorDeptName() + (StringUtils.isEmpty(d.getPeriodAdvisorEmployeeName()) ? "" : "（" + d.getPeriodAdvisorEmployeeName() + "）"));
            d.setPeriodAccountingInfo(StringUtils.isEmpty(d.getPeriodAccountingDeptName()) ? "" : d.getPeriodAccountingDeptName() + (StringUtils.isEmpty(d.getPeriodAccountingEmployeeName()) ? "" : "（" + d.getPeriodAccountingEmployeeName() + "）"));
            d.setPeriodInAccountDeliverResultStr(AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountDeliverResult()).getName());
            d.setCreateInfo((StringUtils.isEmpty(d.getCreateDeptName()) ? "" : d.getCreateDeptName()) + (StringUtils.isEmpty(d.getCreateBy()) ? "" : "（" + d.getCreateBy() + "）"));
        });
        return data;
    }

    @Override
    @Transactional
    public void settlementAppendToBill(SettlementAppendBillVO vo) {
        Bill bill = getById(vo.getBillId());
        if (Objects.isNull(bill) || bill.getIsDel()) {
            throw new ServiceException("账单不存在");
        }
        if (!BillStatus.canAppendStatus().contains(bill.getStatus())) {
            throw new ServiceException("账单状态不符，无法追加");
        }
        SettlementOrder settlementOrder = settlementOrderMapper.selectById(vo.getSettlementOrderId());
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            throw new ServiceException("结算单不存在");
        }
        if (!Objects.equals(SettlementOrderStatus.WAIT_PUSH.getCode(), settlementOrder.getStatus())) {
            throw new ServiceException("结算单状态不符，无法追加");
        }
        settlementOrderMapper.updateById(new SettlementOrder().setStatus(SettlementOrderStatus.SETTLEMENTING.getCode()).setId(vo.getSettlementOrderId()).setIsWaitForEdit(false)
                .setBillTitle(bill.getBillTitle()).setBillNo(bill.getBillNo()));
        billSettlementOrderRelationService.save(new BillSettlementOrderRelation().setBillId(vo.getBillId()).setSettlementOrderId(vo.getSettlementOrderId()));
        CommonFileVO settlementDataFile = createSettlementDataFile(settlementOrder);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Map<String, String> billOperContent = new HashMap<>();
        billOperContent.put("结算单标题", settlementOrder.getSettlementTitle());
        billOperContent.put("结算单编号", settlementOrder.getSettlementOrderNo());
        Map<String, String> settlementOrderOperContent = new HashMap<>();
        billOperContent.put("账单名", bill.getBillTitle());
        billOperContent.put("账单编号", bill.getBillNo());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getBillId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("追加结算单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(JSONObject.toJSONString(billOperContent))
                    .setOperImages(Objects.isNull(settlementDataFile) ? "" : JSONArray.toJSONString(Collections.singletonList(settlementDataFile))));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getSettlementOrderId())
                    .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("追加进账单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(JSONObject.toJSONString(settlementOrderOperContent)));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private Bill createBillBySettleOrders(String billTitle, String remark, List<CommonFileVO> files, SettlementPushReviewDTO dto, Long userId, Long deptId, String employeeName) {
        String billNo = Constants.BILL_NO_PREFIX + dto.getBusinessTopDeptId() + dto.getBusinessDeptId() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        Bill bill = new Bill().setBillNo(billNo).setBillTitle(billTitle)
                .setBillNo(billNo).setBusinessTopDeptId(dto.getBusinessTopDeptId())
                .setBusinessDeptId(dto.getBusinessDeptId()).setStatus(BillStatus.PUSH_WAIT_CONFIRM.getCode())
                .setTotalPrice(dto.getTotalPrice()).setDiscountPrice(dto.getDiscountPrice())
                .setDeductionPrice(dto.getDeductionPrice()).setOughtPrice(dto.getTotalPrice().subtract(Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice()).subtract(Objects.isNull(dto.getDeductionPrice()) ? BigDecimal.ZERO : dto.getDeductionPrice()))
                .setRemark(remark);
        save(bill);
        if (!ObjectUtils.isEmpty(files)) {
            billFileService.saveBatch(files.stream().map(f -> new BillFile().setBillId(bill.getId()).setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileType(BillFileType.CREATE.getCode())).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(dto.getSettlementOrderList())) {
            settlementOrderMapper.update(new SettlementOrder().setBillNo(billNo).setBillTitle(billTitle).setStatus(SettlementOrderStatus.SETTLEMENTING.getCode()).setIsWaitForEdit(false),
                    new LambdaQueryWrapper<SettlementOrder>().in(SettlementOrder::getId, dto.getSettlementOrderList().stream().map(SettlementPushReviewOrderDTO::getSettlementOrderId).collect(Collectors.toList()))
                            .eq(SettlementOrder::getIsDel, false));
            billSettlementOrderRelationService.saveBatch(dto.getSettlementOrderList().stream().map(row -> new BillSettlementOrderRelation().setBillId(bill.getId()).setSettlementOrderId(row.getSettlementOrderId())).collect(Collectors.toList()));
        }
        String operImages = ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bill.getId())
                    .setBusinessType(BusinessLogBusinessType.BILL.getCode())
                    .setDeptId(deptId)
                    .setOperType("新建账单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperRemark(remark)
                    .setOperImages(operImages));
            if (!ObjectUtils.isEmpty(dto.getSettlementOrderList())) {
                Map<String, String> operContentMap = new HashMap<>();
                operContentMap.put("账单标题", billTitle);
                operContentMap.put("账单编号", billNo);
                String operContent = JSONObject.toJSONString(operContentMap);
                dto.getSettlementOrderList().forEach(settlementOrder -> {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getSettlementOrderId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("推送账单")
                            .setOperName(employeeName)
                            .setOperUserId(userId)
                            .setOperRemark(remark)
                            .setOperImages(operImages)
                            .setOperContent(operContent));
                });
            }
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return bill;
    }

    private Map<Long, List<SettlementPushReviewOrderDTO>> selectBillSettlementOrderList(List<Long> billIds) {
        if (ObjectUtils.isEmpty(billIds)) {
            return Maps.newHashMap();
        }
        List<BillSettlementOrderRelation> billSettlementOrderRelations = billSettlementOrderRelationService.selectBatchByBillIds(billIds);
        Map<Long, List<BillSettlementOrderRelation>> relationMap = billSettlementOrderRelations.stream().collect(Collectors.groupingBy(BillSettlementOrderRelation::getBillId));
        List<SettlementOrder> settlementOrders = ObjectUtils.isEmpty(billSettlementOrderRelations) ? Lists.newArrayList() :
                settlementOrderMapper.selectBatchIds(billSettlementOrderRelations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()));
        Map<Long, Long> dataCountMap = ObjectUtils.isEmpty(settlementOrders) ? Maps.newHashMap() :
                settlementOrderDataMapper.selectBatchSettlementOrderDataCount(settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
        Map<Long, SettlementOrder> settlementOrderMap = settlementOrders.stream().collect(Collectors.toMap(SettlementOrder::getId, Function.identity()));
        Map<Long, List<SettlementPushReviewOrderDTO>> result = Maps.newHashMap();
        billIds.forEach(billId -> {
            List<BillSettlementOrderRelation> relations = relationMap.getOrDefault(billId, Lists.newArrayList());
            List<SettlementPushReviewOrderDTO> settlementOrderList = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(relations)) {
                for (BillSettlementOrderRelation relation : relations) {
                    SettlementOrder settlementOrder = settlementOrderMap.get(relation.getSettlementOrderId());
                    if (!Objects.isNull(settlementOrder)) {
                        Long dataCount = dataCountMap.getOrDefault(settlementOrder.getId(), 0L);
                        BigDecimal totalPrice = settlementOrder.getPrice().multiply(new BigDecimal(dataCount));
                        settlementOrderList.add(SettlementPushReviewOrderDTO.builder()
                                .settlementOrderId(relation.getSettlementOrderId())
                                .settlementOrderTitle(settlementOrder.getSettlementTitle())
                                .settlementType(settlementOrder.getSettlementType())
                                .settlementTypeName(SettlementType.getByCode(settlementOrder.getSettlementType()).getName())
                                .unit(settlementOrder.getUnit())
                                .dataCount(dataCount)
                                .price(settlementOrder.getPrice())
                                .totalPrice(totalPrice)
                                .discountPrice(settlementOrder.getDiscountPrice())
                                .isWaitForEdit(settlementOrder.getIsWaitForEdit())
                                .settlementPrice(totalPrice.subtract(Objects.isNull(settlementOrder.getDiscountPrice()) ? BigDecimal.ZERO : settlementOrder.getDiscountPrice()))
                                .build());
                    }
                }
            }
            result.put(billId, settlementOrderList);
        });
        return result;
    }

    private void deleteBillSettlementOrders(List<BillSettlementOrderRelation> relations) {
        if (!ObjectUtils.isEmpty(relations)) {
            billSettlementOrderRelationService.removeByIds(relations.stream().map(BillSettlementOrderRelation::getId).collect(Collectors.toList()));
//            settlementOrderService.deleteSettlementOrderByBill(relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()));
            settlementOrderService.reBackSettlementOrderByBill(relations.stream().map(BillSettlementOrderRelation::getSettlementOrderId).collect(Collectors.toList()));
        }
    }

    private Bill checkBillExists(Long billId) {
        Bill bill = getById(billId);
        if (Objects.isNull(bill) || bill.getIsDel()) {
            throw new ServiceException("账单不存在");
        }
        return bill;
    }
}
