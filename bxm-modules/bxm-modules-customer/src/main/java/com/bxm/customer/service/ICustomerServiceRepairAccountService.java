package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.CustomerServiceRepairAccount;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.repairAccount.*;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.repairAccount.*;

import java.util.List;

/**
 * 补账Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceRepairAccountService extends IService<CustomerServiceRepairAccount> {
    /**
     * 查询补账
     *
     * @param id 补账主键
     * @return 补账
     */
    public CustomerServiceRepairAccount selectCustomerServiceRepairAccountById(Long id);

    /**
     * 查询补账列表
     *
     * @param customerServiceRepairAccount 补账
     * @return 补账集合
     */
    public List<CustomerServiceRepairAccount> selectCustomerServiceRepairAccountList(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 新增补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    public int insertCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 修改补账
     *
     * @param customerServiceRepairAccount 补账
     * @return 结果
     */
    public int updateCustomerServiceRepairAccount(CustomerServiceRepairAccount customerServiceRepairAccount);

    /**
     * 批量删除补账
     *
     * @param ids 需要删除的补账主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountByIds(Long[] ids);

    /**
     * 删除补账信息
     *
     * @param id 补账主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountById(Long id);

    //****** start self method ******  批量操作:
    //分页获取补账列表
    IPage<RepairAccountDTO> repairAccountList(Long deptId, RepairAccountVO vo);

    //获取首个账期信息以
    FirstPeriodDTO getFirstPeriod(Long customerServiceId);

    //获取补账的服务结束时间
    RepairAccountPeriodEndDTO getRepairAccountPeriodEnd(Long customerServiceId);

    //新增，基础信息
    Long addRepairAccountBase(Long deptId, AddRepairAccountBaseVO vo);

    Long addRepairAccountBaseByRestart(Long deptId, AddRepairAccountBaseVO vo);

    //获取基础信息
    RepairAccountBaseDTO getRepairAccountBase(Long id);

    //编辑基础信息
    void updateRepairAccountBase(Long deptId, UpdateRepairAccountBaseVO vo);

    //根据补账id获取票据信息
    RepairAccountInstrumentDTO getRepairAccountInstrument(Long id);

    //一并校验可提交规则，false 校验不通过
    CheckCanSubmitDTO checkCanSubmit(RepairAccountInstrumentDTO vo);

    //当新增材料交接、编辑材料交接时，保存
    //isSubmit：这个接口逻辑，和 submitDocHandoverInstrument 逻辑很像，所以底层代码共用了，用 isSubmit 字段来区分具体的动作，然后在几个细节上做不同处理
    void saveDocHandoverInstrument(Long deptId, RepairAccountInstrumentDTO vo, Boolean isSubmit);

    //当新增材料交接、编辑材料交接时，提交
    void submitDocHandoverInstrument(Long deptId, RepairAccountInstrumentDTO vo);

    //获取详情标题头列表
    List<RepairAccountTitleDTO> getDetailTitles(Long id);

    //获取补账详情
    RepairAccountFullDTO getRepairAccountFull(Long id);

    //获取补账详情
    RepairAccountFullDTO getRepairAccountFullV2(Long id);

    //****** 列表上的操作按钮
    //提交-批量
    TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateSubmit(Long deptId, List<Long> ids);

    //删除-批量
    TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateDelete(Long deptId, List<Long> ids);

    //单体操作的时候，默认回显当前客户服务的会计小组
    List<Long> getAccountingDeptIdFullPathForSingle(Long id);

    //选择会计组别后，显示当前会计上限提示。
    AccountingSelectTipsDTO getAccountingSelectTips(Long accountingDeptId);

    //分派-批量
    TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateAssign(Long deptId, BatchOperateAssignRepairAccountVO vo);

    //退回-批量
    TCommonOperateDTO<CustomerServiceRepairAccount> batchOperateBack(Long deptId, BatchOperateBatchRepairAccountVO vo);

    //****** 其他科共用的逻辑API
    //处理，会计搜索
    //客户服务上的会计
    CommonIdsSearchVO accountingSearchOfRepair(String accountingEmployee);

    void getRepairAccountOrder(Long deptId, CommonIdVO vo);
}
