package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.NewCustomerOtherInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferSettlementPaymentInfoDTO;
import com.bxm.customer.mapper.NewCustomerOtherInfoMapper;
import com.bxm.customer.service.INewCustomerOtherInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 新户流转其他信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerOtherInfoServiceImpl extends ServiceImpl<NewCustomerOtherInfoMapper, NewCustomerOtherInfo> implements INewCustomerOtherInfoService
{
    @Autowired
    private NewCustomerOtherInfoMapper newCustomerOtherInfoMapper;

    /**
     * 查询新户流转其他信息
     * 
     * @param id 新户流转其他信息主键
     * @return 新户流转其他信息
     */
    @Override
    public NewCustomerOtherInfo selectNewCustomerOtherInfoById(Long id)
    {
        return newCustomerOtherInfoMapper.selectNewCustomerOtherInfoById(id);
    }

    /**
     * 查询新户流转其他信息列表
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 新户流转其他信息
     */
    @Override
    public List<NewCustomerOtherInfo> selectNewCustomerOtherInfoList(NewCustomerOtherInfo newCustomerOtherInfo)
    {
        return newCustomerOtherInfoMapper.selectNewCustomerOtherInfoList(newCustomerOtherInfo);
    }

    /**
     * 新增新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo)
    {
        return newCustomerOtherInfoMapper.insertNewCustomerOtherInfo(newCustomerOtherInfo);
    }

    /**
     * 修改新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo)
    {
        return newCustomerOtherInfoMapper.updateNewCustomerOtherInfo(newCustomerOtherInfo);
    }

    /**
     * 批量删除新户流转其他信息
     * 
     * @param ids 需要删除的新户流转其他信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerOtherInfoByIds(Long[] ids)
    {
        return newCustomerOtherInfoMapper.deleteNewCustomerOtherInfoByIds(ids);
    }

    /**
     * 删除新户流转其他信息信息
     * 
     * @param id 新户流转其他信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerOtherInfoById(Long id)
    {
        return newCustomerOtherInfoMapper.deleteNewCustomerOtherInfoById(id);
    }

    @Override
    public NewCustomerOtherInfo selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<NewCustomerOtherInfo>()
                .eq(NewCustomerOtherInfo::getCustomerId, customerId), false);
    }

    @Override
    @Transactional
    public void updateByOtherInfo(Long customerId, NewCustomerTransferSettlementPaymentInfoDTO settlementPaymentInfo) {
        NewCustomerOtherInfo oldOtherInfo = selectByCustomerId(customerId);
        if (Objects.isNull(oldOtherInfo)) {
            if (!Objects.isNull(settlementPaymentInfo)) {
                NewCustomerOtherInfo otherInfo = new NewCustomerOtherInfo();
                BeanUtils.copyProperties(settlementPaymentInfo, otherInfo);
                otherInfo.setCustomerId(customerId);
                save(otherInfo);
            }
        } else {
            update(new LambdaUpdateWrapper<NewCustomerOtherInfo>()
                    .eq(NewCustomerOtherInfo::getId, oldOtherInfo.getId())
                    .set(NewCustomerOtherInfo::getTaxSubmissionStatus, Objects.isNull(settlementPaymentInfo) ? null : settlementPaymentInfo.getTaxSubmissionStatus())
                    .set(NewCustomerOtherInfo::getNextYearSupplement, Objects.isNull(settlementPaymentInfo) ? null : settlementPaymentInfo.getNextYearSupplementAmount())
                    .set(NewCustomerOtherInfo::getPreTaxProfit, Objects.isNull(settlementPaymentInfo) ? null : settlementPaymentInfo.getPreTaxProfit())
                    .set(NewCustomerOtherInfo::getNotes, Objects.isNull(settlementPaymentInfo) ? null : settlementPaymentInfo.getNotes()));
        }
    }

    @Override
    public Map<Long, NewCustomerOtherInfo> selectMapByCustomerIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerOtherInfo>().in(NewCustomerOtherInfo::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.toMap(NewCustomerOtherInfo::getCustomerId, Function.identity(), (o1, o2) -> o1));
    }
}
