package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.enums.quality.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.dto.RemoteQualityCheckingRecordDTO;
import com.bxm.customer.api.domain.dto.RemoteQualityCheckingResultDTO;
import com.bxm.customer.api.domain.dto.RemoteSendQualityCheckingTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteQualityCheckingCreateVO;
import com.bxm.customer.api.domain.vo.RemoteSendQualityCheckingTaskVO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.QualityCheckingItem;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDetailDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultOperateDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityTestingRpaResultDTO;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultCheckVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultModifyVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultVO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.QualityCheckingItemMapper;
import com.bxm.customer.mapper.QualityCheckingResultMapper;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
public class QualityCheckingResultServiceImpl extends ServiceImpl<QualityCheckingResultMapper, QualityCheckingResult> implements IQualityCheckingResultService
{
    @Autowired
    private QualityCheckingResultMapper qualityCheckingResultMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private QualityCheckingItemMapper qualityCheckingItemMapper;

    @Autowired
    private IQualityCheckingFileService qualityCheckingFileService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private IQualityCheckingRecordService qualityCheckingRecordService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    /**
     * 查询质检结果
     * 
     * @param id 质检结果主键
     * @return 质检结果
     */
    @Override
    public QualityCheckingResult selectQualityCheckingResultById(Long id)
    {
        return qualityCheckingResultMapper.selectQualityCheckingResultById(id);
    }

    /**
     * 查询质检结果列表
     * 
     * @param qualityCheckingResult 质检结果
     * @return 质检结果
     */
    @Override
    public List<QualityCheckingResult> selectQualityCheckingResultList(QualityCheckingResult qualityCheckingResult)
    {
        return qualityCheckingResultMapper.selectQualityCheckingResultList(qualityCheckingResult);
    }

    /**
     * 新增质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    @Override
    public int insertQualityCheckingResult(QualityCheckingResult qualityCheckingResult)
    {
        qualityCheckingResult.setCreateTime(DateUtils.getNowDate());
        return qualityCheckingResultMapper.insertQualityCheckingResult(qualityCheckingResult);
    }

    /**
     * 修改质检结果
     * 
     * @param qualityCheckingResult 质检结果
     * @return 结果
     */
    @Override
    public int updateQualityCheckingResult(QualityCheckingResult qualityCheckingResult)
    {
        qualityCheckingResult.setUpdateTime(DateUtils.getNowDate());
        return qualityCheckingResultMapper.updateQualityCheckingResult(qualityCheckingResult);
    }

    /**
     * 批量删除质检结果
     * 
     * @param ids 需要删除的质检结果主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingResultByIds(Long[] ids)
    {
        return qualityCheckingResultMapper.deleteQualityCheckingResultByIds(ids);
    }

    /**
     * 删除质检结果信息
     * 
     * @param id 质检结果主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingResultById(Long id)
    {
        return qualityCheckingResultMapper.deleteQualityCheckingResultById(id);
    }

    @Override
    public IPage<QualityCheckingResultDTO> qualityCheckResultPageList(QualityCheckingResultVO vo) {
        IPage<QualityCheckingResultDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDept = remoteDeptService.userDeptList(vo.getUserId(), vo.getDeptId()).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        if (!StringUtils.isEmpty(vo.getLastCheckingTimeStart())) {
            vo.setLastCheckingTimeStart(vo.getLastCheckingTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getLastCheckingTimeEnd())) {
            vo.setLastCheckingTimeEnd(vo.getLastCheckingTimeEnd() + " 23:59:59");
        }
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            if (!vo.getPeriodTagName().contains("&")) {
                vo.setPeriodTagType(1);
                vo.setPeriodTagNameList(Collections.singletonList(vo.getPeriodTagName()));
                vo.setPeriodTagSize(1);
            } else {
                vo.setPeriodTagType(2);
                vo.setPeriodTagNameList(Arrays.asList(vo.getPeriodTagName().split("&")));
                vo.setPeriodTagSize(vo.getPeriodTagNameList().size());
            }
        }
        List<QualityCheckingResultDTO> data = qualityCheckingResultMapper.qualityCheckResultPageList(result, vo, userDept, batchSearchCustomerServiceIds);
        if (!ObjectUtils.isEmpty(data)) {
            fillQualityCheckingResultData(data);
        }
        result.setRecords(data);
        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<QualityCheckingResult> modifyQualityResult(Long deptId, QualityCheckingResultModifyVO vo) {
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        String operType = "编辑质检结果";
        LocalDateTime operTime = LocalDateTime.now();
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("质检结果", QualityCheckingResultResult.getByCode(vo.getCheckingResult()).getName());
        String operContentStr = JSONObject.toJSONString(operContent);
        if (!Objects.isNull(vo.getQualityCheckingResultId())) {
            modifySingle(vo.getQualityCheckingResultId(), operateUserInfo, operType, operTime, operContentStr, vo);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getQualityCheckingResultIds())) {
                throw new ServiceException("请选择要编辑的数据");
            }
            return modifyBatch(vo.getQualityCheckingResultIds(), operateUserInfo, operType, operTime, operContentStr, vo);
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<QualityCheckingResult> checkQualityResult(Long deptId, QualityCheckingResultCheckVO vo) {
        OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        String operType = "检查";
        LocalDateTime operTime = LocalDateTime.now();
        String operContentStr = "";
        if (!Objects.isNull(vo.getQualityCheckingResultId())) {
            checkSingle(vo.getQualityCheckingResultId(), operateUserInfo, operType, operTime, operContentStr, vo);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getQualityCheckingResultIds())) {
                throw new ServiceException("请选择要检查的数据");
            }
            return checkBatch(vo.getQualityCheckingResultIds(), operateUserInfo, operType, operTime, operContentStr, vo);
        }
    }

    @Override
    public QualityCheckingResultDetailDTO qualityCheckingResultDetail(Long deptId, Long qualityCheckingResultId) {
        QualityCheckingResult checkingResult = getById(qualityCheckingResultId);
        if (Objects.isNull(checkingResult) || checkingResult.getIsDel()) {
            throw new ServiceException("质检结果不存在");
        }
        CCustomerService customerService = customerServiceMapper.selectById(checkingResult.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户不存在");
        }
        QualityCheckingItem qualityCheckingItem = qualityCheckingItemMapper.selectById(checkingResult.getQualityCheckingItemId());
        Map<String, List<QualityCheckingResultDTO>> otherQualityCheckingResultMap = new LinkedHashMap<>();
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        // 查询同账期其他的质检事项的质检结果
        List<Long> otherResultIds = list(new LambdaQueryWrapper<QualityCheckingResult>()
                .eq(QualityCheckingResult::getCustomerServicePeriodMonthId, checkingResult.getCustomerServicePeriodMonthId())
                .ne(QualityCheckingResult::getQualityCheckingItemId, checkingResult.getQualityCheckingItemId())
                .ne(QualityCheckingResult::getId, qualityCheckingResultId)
                .eq(QualityCheckingResult::getIsDel, false)).stream().map(QualityCheckingResult::getId).collect(Collectors.toList());
        Map<Integer, List<QualityCheckingResultDTO>> otherMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(otherResultIds)) {
            List<QualityCheckingResultDTO> qualityCheckingResultDTOS = baseMapper.qualityCheckResultListByIds(otherResultIds);
            fillQualityCheckingResultData(qualityCheckingResultDTOS);
            otherMap = qualityCheckingResultDTOS.stream().collect(Collectors.groupingBy(QualityCheckingResultDTO::getQualityCheckingType));
        }
        for (QualityCheckingType qualityCheckingType : QualityCheckingType.allValidTypes(sysDept.getDeptType())) {
            List<QualityCheckingResultDTO> qualityCheckingResults = otherMap.get(qualityCheckingType.getCode());
            if (!ObjectUtils.isEmpty(qualityCheckingResults)) {
                qualityCheckingResults.sort(Comparator.comparing(QualityCheckingResultDTO::getQualityCheckingResultId));
            }
            otherQualityCheckingResultMap.put(qualityCheckingType.getName(), qualityCheckingResults);
        }
        return QualityCheckingResultDetailDTO.builder()
                .qualityCheckingResultId(qualityCheckingResultId)
                .customerServicePeriodMonthId(checkingResult.getCustomerServicePeriodMonthId())
                .customerServiceId(checkingResult.getCustomerServiceId())
                .creditCode(customerService.getCreditCode())
                .customerName(customerService.getCustomerName())
                .qualityCheckingItemId(checkingResult.getQualityCheckingItemId())
                .qualityCheckingItemName(Objects.isNull(qualityCheckingItem) ? "" : qualityCheckingItem.getItemName())
                .qualityCheckingType(checkingResult.getQualityCheckingType())
                .qualityCheckingTypeName(QualityCheckingType.getByCode(checkingResult.getQualityCheckingType()).getName())
                .qualityCheckingCycle(checkingResult.getQualityCheckingCycle())
                .qualityCheckingCycleName(QualityCheckingCycle.getByCode(checkingResult.getQualityCheckingCycle()).getName())
                .periodStr(DateUtils.periodToYeaMonth(checkingResult.getPeriod()))
                .checkingResult(checkingResult.getCheckingResult())
                .checkingResultName(Objects.isNull(checkingResult.getCheckingResult()) ? "" : QualityCheckingResultResult.getByCode(checkingResult.getCheckingResult()).getName())
                .status(checkingResult.getStatus())
                .statusName(Objects.isNull(checkingResult.getStatus()) ? "" : QualityCheckingResultStatus.getByCode(checkingResult.getStatus()).getName())
                .checkedTimes(qualityCheckingRecordService.getCheckedTimesByQualityCheckingResultId(checkingResult.getId()))
                .firstCheckTime(checkingResult.getFirstCheckingTime())
                .lastCheckTime(checkingResult.getLastCheckingTime())
                .remark(checkingResult.getRemark())
                .files(qualityCheckingFileService.getByBusinessIdAndBusinessType(Constants.QUALITY_CHECKING_RESULT_TYPE, qualityCheckingResultId, QualityCheckingResultFileType.MAIN_FILE.getCode()))
                .otherQualityCheckingResultMap(otherQualityCheckingResultMap)
                .build();
    }

    @Override
    @Transactional
    public void remoteCreateQualityChecking(RemoteQualityCheckingCreateVO vo) {
        QualityCheckingResult checkingResult = getOne(new LambdaQueryWrapper<QualityCheckingResult>().eq(QualityCheckingResult::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                .eq(QualityCheckingResult::getQualityCheckingItemId, vo.getQualityCheckingItemId())
                .eq(QualityCheckingResult::getIsDel, false).last("limit 1"));
        LocalDateTime operTime = LocalDateTime.now();
        if (Objects.isNull(checkingResult)) {
            checkingResult = new QualityCheckingResult()
                    .setCustomerServiceId(vo.getCustomerServiceId())
                    .setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId())
                    .setPeriod(vo.getPeriod())
                    .setQualityCheckingItemId(vo.getQualityCheckingItemId())
                    .setQualityCheckingType(vo.getQualityCheckingType())
                    .setQualityCheckingCycle(vo.getQualityCheckingCycle())
                    .setStatus(QualityCheckingResultStatus.UNEXECUTED.getCode())
                    .setIsDel(false);
            checkingResult.setCreateBy(vo.getOperName());
            save(checkingResult);
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingResult.getId())
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType("检查")
                        .setOperName(vo.getOperName())
                        .setCreateTime(operTime)
                        .setOperUserId(vo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        QualityCheckingRecord newQualityCheckingRecord = qualityCheckingRecordService.createNewQualityCheckingRecord(checkingResult, vo.getUserId(), vo.getDeptId(), vo.getOperName(), operTime, vo.getBatchNo());
        asyncService.sendCheckingTask(newQualityCheckingRecord, vo.getOperName(), vo.getUserId(), vo.getDeptId());
    }

    @Override
    @Transactional
    public RemoteCustomerPeriodDTO remoteCreateQualityCheckingV2(RemoteQualityCheckingCreateVO vo) {
        Map<Long, QualityCheckingResult> qualityCheckingResultMap = list(new LambdaQueryWrapper<QualityCheckingResult>().eq(QualityCheckingResult::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                .eq(QualityCheckingResult::getIsDel, false)).stream().collect(Collectors.toMap(QualityCheckingResult::getQualityCheckingItemId, Function.identity(), (k1, k2) -> k1));
        Map<Long, QualityCheckingRecord> checkingRecordMap = qualityCheckingRecordService.list(new LambdaQueryWrapper<QualityCheckingRecord>().eq(QualityCheckingRecord::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                .eq(QualityCheckingRecord::getIsDel, false).eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode())).stream().collect(Collectors.toMap(QualityCheckingRecord::getQualityCheckingItemId, Function.identity(), (k1, k2) -> k1));
        LocalDateTime operTime = LocalDateTime.now();
        List<QualityCheckingItem> allItems = qualityCheckingItemMapper.selectList(new LambdaQueryWrapper<QualityCheckingItem>()
                .eq(QualityCheckingItem::getStatus, 1));
        boolean needRpa = false;
        RemoteCustomerPeriodDTO periodDTO = null;
        for (QualityCheckingItem item : allItems) {
            if (!checkingRecordMap.containsKey(item.getId())) {
                // 没有进行中的质检任务才发起质检请求
                QualityCheckingResult checkingResult = qualityCheckingResultMap.get(item.getId());
                if (Objects.isNull(checkingResult)) {
                    checkingResult = new QualityCheckingResult()
                            .setCustomerServiceId(vo.getCustomerServiceId())
                            .setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId())
                            .setPeriod(vo.getPeriod())
                            .setQualityCheckingItemId(item.getId())
                            .setQualityCheckingType(item.getQualityCheckingType())
                            .setQualityCheckingCycle(item.getQualityCheckingCycle())
                            .setStatus(QualityCheckingResultStatus.UNEXECUTED.getCode())
                            .setIsDel(false);
                    checkingResult.setCreateBy(vo.getOperName());
                    save(checkingResult);
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingResult.getId())
                                .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType("检查")
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                }
                if (item.getIsRpa()) {
                    QualityCheckingRecord newQualityCheckingRecord = qualityCheckingRecordService.createNewQualityCheckingRecord(checkingResult, vo.getUserId(), vo.getDeptId(), vo.getOperName(), operTime, vo.getBatchNo());
                    if (!Objects.isNull(newQualityCheckingRecord)) {
                        needRpa = true;
                    }
                }
            }
        }
        if (needRpa) {
            periodDTO = RemoteCustomerPeriodDTO.builder()
                    .id(vo.getCustomerServicePeriodMonthId())
                    .customerServiceId(vo.getCustomerServiceId())
                    .period(vo.getPeriod())
                    .build();
        }
        return periodDTO;
    }

    @Override
    @Transactional
    public void dealQualityChecking(CommonNoticeVO commonNoticeVO) {
        Map<String, Object> noticeParameter = commonNoticeVO.getNoticeParameter();
        Boolean status = (Boolean) noticeParameter.get("status");
        Boolean success = (Boolean) noticeParameter.get("success");
        String message = (String) noticeParameter.get("message");
        if (Objects.isNull(status) || Objects.isNull(success)) {
            log.info("回调参数有误，uuid:{}", commonNoticeVO.getUuid());
            return;
        }
        Long qualityCheckingRecordId = commonNoticeVO.getQualityCheckingRecordId();
        QualityCheckingRecord checkingRecord = qualityCheckingRecordService.getById(qualityCheckingRecordId);

        Long qualityCheckingResultId = commonNoticeVO.getQualityCheckingResultId();
        QualityCheckingResult checkingResult = getById(qualityCheckingResultId);

        LocalDateTime operTime = LocalDateTime.now();
        Long userId = commonNoticeVO.getUserId();
        Long deptId = commonNoticeVO.getDeptId();
        String operName = commonNoticeVO.getOperator();
        if (!success) {
            // 质检记录和质检结果结果都不写，只写质检记录的状态为"失败关闭”。质检结果次数不变，首次质检时间和末次时间都不变。
            //记操作记录：
            //business_type：质检结果
            //oper_type：质检失败
            //business_type：质检记录
            //oper_type：质检失败
            if (!Objects.isNull(checkingRecord) && !checkingRecord.getIsDel() && Objects.equals(checkingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
                qualityCheckingRecordService.updateById(new QualityCheckingRecord().setId(qualityCheckingRecordId)
                        .setStatus(QualityCheckingRecordStatus.FAILED.getCode())
                        .setCloseTime(operTime));
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingRecord.getId())
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                            .setDeptId(deptId)
                            .setOperType("质检失败")
                            .setOperName(operName)
                            .setCreateTime(operTime)
                            .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                            .setDeptId(deptId)
                            .setOperType("质检失败")
                            .setOperName(operName)
                            .setCreateTime(operTime)
                            .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        } else {
            if (!Objects.isNull(checkingRecord) && !checkingRecord.getIsDel() && Objects.equals(checkingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
                qualityCheckingRecordService.updateById(new QualityCheckingRecord().setId(qualityCheckingRecordId)
                        .setStatus(QualityCheckingRecordStatus.EXECUTING.getCode())
                        .setCheckingResult(status ? QualityCheckingRecordResult.NORMAL.getCode() : QualityCheckingRecordResult.EXCEPTION.getCode())
                        .setFinishTime(operTime));
            }
            if (!Objects.isNull(checkingResult) && !checkingResult.getIsDel()) {
                updateById(new QualityCheckingResult().setId(qualityCheckingResultId)
                        .setStatus(QualityCheckingResultStatus.EXECUTED.getCode())
                        .setCheckingResult(status ? QualityCheckingResultResult.NORMAL.getCode() : QualityCheckingResultResult.EXCEPTION.getCode())
                        .setFirstCheckingTime(Objects.isNull(checkingResult.getFirstCheckingTime()) ? operTime : null)
                        .setLastCheckingTime(operTime));
            }
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("质检结果", status ? QualityCheckingResultResult.NORMAL.getName() : QualityCheckingResultResult.EXCEPTION.getName());
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingRecord.getId())
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                        .setDeptId(deptId)
                        .setOperType("质检完成")
                        .setOperName(operName)
                        .setCreateTime(operTime)
                        .setOperContent(JSONObject.toJSONString(operContent))
                        .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                        .setDeptId(deptId)
                        .setOperType("质检完成")
                        .setOperName(operName)
                        .setCreateTime(operTime)
                        .setOperContent(JSONObject.toJSONString(operContent))
                        .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    public static void main(String[] args) {
        String json = "{\"uuid\": \"2afcffdc9a8f4ff4813d106b49364108\", \"deptId\": 2, \"period\": 202505, \"source\": 2, \"userId\": 1, \"groupId\": \"1498120906013171713\", \"operator\": \"系统\", \"platType\": \"易捷账\", \"groupName\": \"融易算君道龙岩\", \"notifyUrl\": \"https://bxmtest.sikilab.com/bxmOpenApi/openapi/commonNotice\", \"taxNumber\": \"91350802MA32R94P61\", \"creditCode\": \"91350802MA32R94P61\", \"noticeCode\": \"qualityTesting\", \"sourceName\": \"医社保\", \"customerName\": \"龙岩市闽秀商贸有限公司\", \"noticeParameter\": {\"testingData\": [{\"id\": \"1\", \"name\": \"111\", \"status\": true, \"message\": \"质检成功\", \"success\": true}]}, \"customerServiceId\": 892, \"customerServicePeriodMonthId\": 16268}";
        Map<String, Object> noticeParameter = JSONObject.parseObject(json).getJSONObject("noticeParameter");
        System.out.println(noticeParameter);
        Object dataObj = noticeParameter.get("testingData");
        System.out.println(dataObj);
        List<QualityTestingRpaResultDTO> rpaResultList = JSONObject.parseArray(dataObj.toString(), QualityTestingRpaResultDTO.class);
        System.out.println(rpaResultList);
    }

    @Override
    @Transactional
    public void dealQualityCheckingV2(CommonNoticeVO commonNoticeVO) {
        Map<String, Object> noticeParameter = commonNoticeVO.getNoticeParameter();
        Object successObj = noticeParameter.get("success");
        if (Objects.isNull(successObj)) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "回调参数有误,success为空");
            return;
        }
        List<QualityCheckingResult> qualityCheckingResultList = qualityCheckingResultMapper.selectList(new LambdaQueryWrapper<QualityCheckingResult>()
                .eq(QualityCheckingResult::getCustomerServicePeriodMonthId, commonNoticeVO.getCustomerServicePeriodMonthId())
                .eq(QualityCheckingResult::getIsDel, false));
        if (ObjectUtils.isEmpty(qualityCheckingResultList)) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "回调参数有误,无质检结果");
            return;
        }
        Map<Long, QualityCheckingResult> resultMap = qualityCheckingResultList.stream().collect(Collectors.toMap(QualityCheckingResult::getId, Function.identity()));
        List<QualityCheckingRecord> checkingRecordList = qualityCheckingRecordService.list(new LambdaQueryWrapper<QualityCheckingRecord>()
                .in(QualityCheckingRecord::getQualityCheckingResultId, qualityCheckingResultList.stream().map(QualityCheckingResult::getId).collect(Collectors.toList()))
                .eq(QualityCheckingRecord::getIsDel, false)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode()));
        Map<Long, QualityCheckingRecord> recordMap = checkingRecordList.stream().collect(Collectors.toMap(QualityCheckingRecord::getQualityCheckingItemId, Function.identity()));
        if (ObjectUtils.isEmpty(checkingRecordList)) {
            openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "回调参数有误,无进行中的质检记录");
            return;
        }
        Boolean taskSuccess = (Boolean) successObj;
        if (!taskSuccess) {
            openApiNoticeRecordService.updateSysDealSuccessByUuid(commonNoticeVO.getUuid());
            Object messageObj = noticeParameter.get("message");
            String message = Objects.isNull(messageObj) ? "" : messageObj.toString();
            for (Map.Entry<Long, QualityCheckingRecord> entry : recordMap.entrySet()) {
                QualityCheckingRecord checkingRecord = entry.getValue();

                Long qualityCheckingResultId = checkingRecord.getQualityCheckingResultId();

                LocalDateTime operTime = LocalDateTime.now();
                Long userId = commonNoticeVO.getUserId();
                Long deptId = commonNoticeVO.getDeptId();
                String operName = commonNoticeVO.getOperator();
                failDeal(checkingRecord, operTime, deptId, userId, operName, message, qualityCheckingResultId);
            }
        } else {
            Object dataObj = noticeParameter.get("testingData");
            if (Objects.isNull(dataObj)) {
                log.info("回调参数有误，uuid:{}", commonNoticeVO.getUuid());
                openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "回调参数有误,testingData为空");
                return;
            }
            List<QualityTestingRpaResultDTO> rpaResultList = JSONObject.parseArray(dataObj.toString(), QualityTestingRpaResultDTO.class);
            if (ObjectUtils.isEmpty(rpaResultList)) {
                log.info("rpa返回无质检结果，uuid:{}", commonNoticeVO.getUuid());
                openApiNoticeRecordService.updateSysDealFailByUuid(commonNoticeVO.getUuid(), "回调参数有误,rpa返回无质检结果");
                return;
            }
            Map<String, QualityTestingRpaResultDTO> rpaResultMap = rpaResultList.stream().collect(Collectors.toMap(QualityTestingRpaResultDTO::getId, Function.identity()));
            openApiNoticeRecordService.updateSysDealSuccessByUuid(commonNoticeVO.getUuid());
            for (Map.Entry<Long, QualityCheckingRecord> entry : recordMap.entrySet()) {
                QualityCheckingRecord checkingRecord = entry.getValue();

                Long qualityCheckingResultId = checkingRecord.getQualityCheckingResultId();
                QualityCheckingResult checkingResult = resultMap.get(qualityCheckingResultId);

                QualityTestingRpaResultDTO rpaResult = rpaResultMap.get(entry.getKey().toString());
                if (!Objects.isNull(rpaResult)) {
                    boolean success = rpaResult.getSuccess();
                    boolean status = rpaResult.getStatus();
                    String message = rpaResult.getMessage();

                    LocalDateTime operTime = LocalDateTime.now();
                    Long userId = commonNoticeVO.getUserId();
                    Long deptId = commonNoticeVO.getDeptId();
                    String operName = commonNoticeVO.getOperator();
                    if (!success) {
                        failDeal(checkingRecord, operTime, deptId, userId, operName, message, qualityCheckingResultId);
                    } else {
                        successDeal(checkingRecord, checkingResult, status, operTime, deptId, userId, operName, message, qualityCheckingResultId);
                    }
                }
            }
        }
    }

    private void failDeal(QualityCheckingRecord checkingRecord, LocalDateTime operTime, Long deptId, Long userId, String operName, String message, Long qualityCheckingResultId) {
        // 质检记录和质检结果结果都不写，只写质检记录的状态为"失败关闭”。质检结果次数不变，首次质检时间和末次时间都不变。
        //记操作记录：
        //business_type：质检结果
        //oper_type：质检失败
        //business_type：质检记录
        //oper_type：质检失败
        if (Objects.equals(checkingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
            qualityCheckingRecordService.updateById(new QualityCheckingRecord().setId(checkingRecord.getId())
                    .setStatus(QualityCheckingRecordStatus.FAILED.getCode())
                    .setCloseTime(operTime));
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingRecord.getId())
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                        .setDeptId(deptId)
                        .setOperType("质检失败")
                        .setOperName(operName)
                        .setCreateTime(operTime)
                        .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                        .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                        .setDeptId(deptId)
                        .setOperType("质检失败")
                        .setOperName(operName)
                        .setCreateTime(operTime)
                        .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private void successDeal(QualityCheckingRecord checkingRecord, QualityCheckingResult checkingResult, Boolean status, LocalDateTime operTime, Long deptId, Long userId, String operName, String message, Long qualityCheckingResultId) {
        if (Objects.equals(checkingRecord.getStatus(), QualityCheckingRecordStatus.UNEXECUTED.getCode())) {
            qualityCheckingRecordService.updateById(new QualityCheckingRecord().setId(checkingRecord.getId())
                    .setStatus(QualityCheckingRecordStatus.EXECUTING.getCode())
                    .setCheckingResult(status ? QualityCheckingRecordResult.NORMAL.getCode() : QualityCheckingRecordResult.EXCEPTION.getCode())
                    .setFinishTime(operTime));
        }
        if (!Objects.isNull(checkingResult) && !checkingResult.getIsDel()) {
            updateById(new QualityCheckingResult().setId(qualityCheckingResultId)
                    .setStatus(QualityCheckingResultStatus.EXECUTED.getCode())
                    .setCheckingResult(status ? QualityCheckingResultResult.NORMAL.getCode() : QualityCheckingResultResult.EXCEPTION.getCode())
                    .setFirstCheckingTime(Objects.isNull(checkingResult.getFirstCheckingTime()) ? operTime : null)
                    .setLastCheckingTime(operTime));
        }
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("质检结果", status ? QualityCheckingResultResult.NORMAL.getName() : QualityCheckingResultResult.EXCEPTION.getName());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingRecord.getId())
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                    .setDeptId(deptId)
                    .setOperType("质检完成")
                    .setOperName(operName)
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                    .setDeptId(deptId)
                    .setOperType("质检完成")
                    .setOperName(operName)
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperRemark(StringUtils.isEmpty(message) ? "" : message)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void overTimeClose() {
        // 对发起时间超过6小时状态仍然为进行中的质检记录进行关闭
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime overTime = now.minusHours(6);
        List<QualityCheckingRecord> list = qualityCheckingRecordService.list(new LambdaQueryWrapper<QualityCheckingRecord>()
                .eq(QualityCheckingRecord::getIsDel, false)
                .eq(QualityCheckingRecord::getStatus, QualityCheckingRecordStatus.UNEXECUTED.getCode())
                .le(QualityCheckingRecord::getCreateTime, overTime));
        if (!ObjectUtils.isEmpty(list)) {
            qualityCheckingRecordService.updateBatchById(list.stream().map(row -> new QualityCheckingRecord().setId(row.getId())
                    .setStatus(QualityCheckingRecordStatus.EXECUTED.getCode())
                    .setCloseTime(now)).collect(Collectors.toList()));
            list.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RECORD.getCode())
                            .setDeptId(null)
                            .setOperType("超时关闭")
                            .setOperName("系统")
                            .setCreateTime(now)
                            .setOperUserId(1L));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
            List<Long> qualityCheckingResultIds = list.stream().map(QualityCheckingRecord::getQualityCheckingResultId).distinct().collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(qualityCheckingResultIds)) {
                qualityCheckingResultIds.forEach(qualityCheckingResultId -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                                .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                                .setDeptId(null)
                                .setOperType("超时关闭")
                                .setOperName("系统")
                                .setCreateTime(now)
                                .setOperUserId(1L));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
        }
    }

    @Override
    public void remoteSendQualityCheckingTask(RemoteSendQualityCheckingTaskVO vo) {
        asyncService.sendBatchCheckingTask(vo, vo.getDeptId(), vo.getUserId(), vo.getOperName());
    }

    private TCommonOperateDTO<QualityCheckingResult> checkBatch(List<Long> qualityCheckingResultIds, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultCheckVO vo) {
        TCommonOperateDTO<QualityCheckingResult> result = new TCommonOperateDTO<>();
        List<QualityCheckingResult> total = list(new LambdaQueryWrapper<QualityCheckingResult>()
                .eq(QualityCheckingResult::getIsDel, false)
                .in(QualityCheckingResult::getId, qualityCheckingResultIds));
        result.setTotal(total);
        if (ObjectUtils.isEmpty(total)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<QualityCheckingResult> success = Lists.newArrayList();
        List<QualityCheckingResult> fail = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<Long, String> errorMagMap = new HashMap<>();
        List<Long> resultIds = total.stream().map(QualityCheckingResult::getId).collect(Collectors.toList());
        Map<Long, Boolean> hasCheckingMap = qualityCheckingRecordService.existsCheckingRecordByQualityCheckingResultIds(resultIds);
        for (QualityCheckingResult qualityCheckingResult : total) {
            if (hasCheckingMap.get(qualityCheckingResult.getId())) {
                failIds.add(qualityCheckingResult.getId());
                fail.add(qualityCheckingResult);
                errorMagMap.put(qualityCheckingResult.getId(), "存在进行中的质检");
            } else {
                success.add(qualityCheckingResult);
            }
        }
        result.setSuccess(success);
        result.setFail(fail);
        if (!ObjectUtils.isEmpty(failIds)) {
            String batchNo = StringUtils.getUuid();
            result.setErrorDataBatchNo(batchNo);
            buildErrorDataList(failIds, errorMagMap, batchNo);
        }
        if (!ObjectUtils.isEmpty(success)) {
            Map<Long, QualityCheckingRecord> resultRecordMap = new HashMap<>();
            for (QualityCheckingResult checkingResult : success) {
                QualityCheckingRecord qualityCheckingRecord = qualityCheckingRecordService.createNewQualityCheckingRecord(checkingResult, operateUserInfo, operTime);
                resultRecordMap.put(checkingResult.getId(), qualityCheckingRecord);
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(checkingResult.getId())
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                            .setDeptId(operateUserInfo.getDeptId())
                            .setOperType(operType)
                            .setOperName(operateUserInfo.getOperName())
                            .setCreateTime(operTime)
                            .setOperContent(operContentStr)
                            .setOperUserId(operateUserInfo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
            asyncService.sendBatchCheckingTask(success, resultRecordMap, operateUserInfo);
        }
        return result;
    }

    private void buildErrorDataList(List<Long> failIds, Map<Long, String> errorMagMap, String batchNo) {
        List<QualityCheckingResultDTO> failRecordList = baseMapper.qualityCheckResultListByIds(failIds);
        if (!ObjectUtils.isEmpty(failRecordList)) {
            fillQualityCheckingResultData(failRecordList);
            List<QualityCheckingResultOperateDTO> errorDTOList = failRecordList.stream().map(e -> {
                QualityCheckingResultOperateDTO dto = new QualityCheckingResultOperateDTO();
                BeanUtils.copyProperties(e, dto);
                dto.setErrorMsg(errorMagMap.getOrDefault(e.getQualityCheckingResultId(), ""));
                return dto;
            }).collect(Collectors.toList());
            redisService.setLargeCacheList(CacheConstants.QUALITY_CHECKING_RESULT_OPERATE_ERROR_RECORD + batchNo, errorDTOList, 500, 60 * 60, TimeUnit.SECONDS);
        }
    }

    private void checkSingle(Long qualityCheckingResultId, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultCheckVO vo) {
        QualityCheckingResult checkingResult = getById(qualityCheckingResultId);
        if (Objects.isNull(checkingResult) || checkingResult.getIsDel()) {
            throw new ServiceException("质检结果不存在");
        }
        if (qualityCheckingRecordService.existsCheckingRecordByQualityCheckingResultId(qualityCheckingResultId)) {
            throw new ServiceException("存在进行中的质检，请勿重复发起");
        }
        QualityCheckingRecord qualityCheckingRecord = qualityCheckingRecordService.createNewQualityCheckingRecord(checkingResult, operateUserInfo, operTime);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType(operType)
                    .setOperName(operateUserInfo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(operContentStr)
                    .setOperUserId(operateUserInfo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        asyncService.sendCheckingTask(checkingResult, qualityCheckingRecord, operateUserInfo);
    }

    private TCommonOperateDTO<QualityCheckingResult> modifyBatch(List<Long> qualityCheckingResultIds, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultModifyVO vo) {
        TCommonOperateDTO<QualityCheckingResult> result = new TCommonOperateDTO<>();
        List<QualityCheckingResult> total = list(new LambdaQueryWrapper<QualityCheckingResult>().in(QualityCheckingResult::getId, qualityCheckingResultIds)
                .eq(QualityCheckingResult::getIsDel, false));
        List<QualityCheckingResult> success = Lists.newArrayList();
        List<QualityCheckingResult> fail = Lists.newArrayList();
        result.setTotal(total);
        if (ObjectUtils.isEmpty(total)) {
            result.setSuccess(success);
            result.setFail(fail);
            return result;
        }
        for (QualityCheckingResult qualityCheckingResult : total) {
            // 允许编辑的 添加到success里
            success.add(qualityCheckingResult);
        }
        result.setSuccess(success);
        result.setFail(fail);
        if (!ObjectUtils.isEmpty(success)) {
            List<Long> qualityCheckingItemIds = success.stream().map(QualityCheckingResult::getQualityCheckingItemId).distinct().collect(Collectors.toList());
            Map<Long, QualityCheckingItem> itemMap = qualityCheckingItemMapper.selectBatchIds(qualityCheckingItemIds).stream().collect(Collectors.toMap(QualityCheckingItem::getId, Function.identity()));
            updateBatchById(success.stream().map(row -> {
                QualityCheckingItem qualityCheckingItem = itemMap.get(row.getQualityCheckingItemId());
                return new QualityCheckingResult().setId(row.getId())
                        .setCheckingResult(vo.getCheckingResult())
                        .setLastCheckingTime(operTime)
                        .setStatus(!Objects.isNull(qualityCheckingItem) && !qualityCheckingItem.getIsRpa() ? QualityCheckingResultStatus.EXECUTED.getCode() : null)
                        .setRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark());
            }).collect(Collectors.toList()));
            qualityCheckingFileService.removeAndSaveNewFiles(Constants.QUALITY_CHECKING_RESULT_TYPE, qualityCheckingResultIds, vo.getFiles(), QualityCheckingResultFileType.MAIN_FILE.getCode());
            success.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                            .setDeptId(operateUserInfo.getDeptId())
                            .setOperType(operType)
                            .setOperName(operateUserInfo.getOperName())
                            .setOperRemark(vo.getRemark())
                            .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                            .setCreateTime(operTime)
                            .setOperContent(operContentStr)
                            .setOperUserId(operateUserInfo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return result;
    }

    private void modifySingle(Long qualityCheckingResultId, OperateUserInfoDTO operateUserInfo, String operType, LocalDateTime operTime, String operContentStr, QualityCheckingResultModifyVO vo) {
        QualityCheckingResult checkingResult = getById(qualityCheckingResultId);
        if (Objects.isNull(checkingResult) || checkingResult.getIsDel()) {
            throw new ServiceException("质检结果不存在");
        }
        QualityCheckingItem qualityCheckingItem = qualityCheckingItemMapper.selectById(checkingResult.getQualityCheckingItemId());
        if (Objects.isNull(qualityCheckingItem)) {
            throw new ServiceException("质检事项不存在");
        }
        QualityCheckingResult update = new QualityCheckingResult()
                .setId(qualityCheckingResultId)
                .setCheckingResult(vo.getCheckingResult())
                .setLastCheckingTime(operTime)
                .setRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark());
        if (!qualityCheckingItem.getIsRpa()) {
            update.setStatus(QualityCheckingResultStatus.EXECUTED.getCode());
        }
        updateById(update);
        qualityCheckingFileService.removeAndSaveNewFiles(Constants.QUALITY_CHECKING_RESULT_TYPE, qualityCheckingResultId, vo.getFiles(), QualityCheckingResultFileType.MAIN_FILE.getCode());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(qualityCheckingResultId)
                    .setBusinessType(BusinessLogBusinessType.QUALITY_CHECKING_RESULT.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType(operType)
                    .setOperName(operateUserInfo.getOperName())
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONObject.toJSONString(vo.getFiles()))
                    .setCreateTime(operTime)
                    .setOperContent(operContentStr)
                    .setOperUserId(operateUserInfo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void fillQualityCheckingResultData(List<QualityCheckingResultDTO> data) {
        if (ObjectUtils.isEmpty(data)) {
            return;
        }
        Set<Long> deptIdSet = new HashSet<>();
        List<Long> lastDeptIds = Lists.newArrayList();
        Set<Long> advisorDeptIds = data.stream().map(QualityCheckingResultDTO::getPeriodAdvisorDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> accountingDeptIds = data.stream().map(QualityCheckingResultDTO::getPeriodAccountingDeptId).filter(Objects::nonNull).collect(Collectors.toSet());
        deptIdSet.addAll(data.stream().map(QualityCheckingResultDTO::getPeriodBusinessTopDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(data.stream().map(QualityCheckingResultDTO::getPeriodBusinessDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(advisorDeptIds);
        deptIdSet.addAll(data.stream().map(QualityCheckingResultDTO::getPeriodAccountingTopDeptId).filter(Objects::nonNull).collect(Collectors.toSet()));
        deptIdSet.addAll(accountingDeptIds);
        lastDeptIds.addAll(advisorDeptIds);
        lastDeptIds.addAll(accountingDeptIds);
        Map<Long, String> deptMap = ObjectUtils.isEmpty(deptIdSet) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(new ArrayList<>(deptIdSet)).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(lastDeptIds) ? Maps.newHashMap() :
                remoteEmployeeService.getBatchEmployeeByDeptIds(lastDeptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(QualityCheckingResultDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
        Map<Long, String> itemMap = qualityCheckingItemMapper.selectBatchIds(data.stream().map(QualityCheckingResultDTO::getQualityCheckingItemId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(QualityCheckingItem::getId, QualityCheckingItem::getItemName));
        Map<Long, List<CommonFileVO>> fileMap = qualityCheckingFileService.getBatchByBusinessIdAndBusinessType(Constants.QUALITY_CHECKING_RESULT_TYPE, data.stream().map(QualityCheckingResultDTO::getQualityCheckingResultId).distinct().collect(Collectors.toList()));
        data.forEach(item -> {
            List<TagDTO> tags = tagMap.get(item.getCustomerServicePeriodMonthId());
            item.setPeriodStr(DateUtils.periodToYeaMonth(item.getPeriod()));
            item.setPeriodBusinessTopDeptName(deptMap.getOrDefault(item.getPeriodBusinessTopDeptId(), ""));
            item.setPeriodBusinessDeptName(deptMap.getOrDefault(item.getPeriodBusinessDeptId(), ""));
            item.setPeriodAdvisorDeptName(deptMap.getOrDefault(item.getPeriodAdvisorDeptId(), ""));
            item.setPeriodAdvisorEmployeeName(employeeMap.getOrDefault(item.getPeriodAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            item.setPeriodAccountingTopDeptName(deptMap.getOrDefault(item.getPeriodAccountingTopDeptId(), ""));
            item.setPeriodAccountingDeptName(deptMap.getOrDefault(item.getPeriodAccountingDeptId(), ""));
            item.setPeriodAccountingEmployeeName(employeeMap.getOrDefault(item.getPeriodAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            item.setPeriodTags(ObjectUtils.isEmpty(tags) ? "" :
                    tags.stream().map(TagDTO::getFullTagName).collect(Collectors.joining("，")));
            item.setPeriodTaxTypeName(Objects.isNull(item.getPeriodTaxType()) ? "" : TaxType.getByCode(item.getPeriodTaxType()).getDesc());
            item.setQualityCheckingTypeName(QualityCheckingType.getByCode(item.getQualityCheckingType()).getName());
            item.setQualityCheckingItemName(itemMap.getOrDefault(item.getQualityCheckingItemId(), ""));
            item.setQualityCheckingCycleName(QualityCheckingCycle.getByCode(item.getQualityCheckingCycle()).getName());
            item.setStatusName(QualityCheckingResultStatus.getByCode(item.getStatus()).getName());
            item.setCheckingResultName(Objects.isNull(item.getCheckingResult()) ? "" : QualityCheckingResultResult.getByCode(item.getCheckingResult()).getName());
            item.setFiles(fileMap.getOrDefault(item.getQualityCheckingResultId(), Lists.newArrayList()));
        });
    }
}
