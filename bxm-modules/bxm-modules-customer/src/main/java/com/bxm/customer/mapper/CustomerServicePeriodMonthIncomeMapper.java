package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceIncomeDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import org.apache.ibatis.annotations.Param;

/**
 * 客户服务账期收入Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-13
 */
@Mapper
public interface CustomerServicePeriodMonthIncomeMapper extends BaseMapper<CustomerServicePeriodMonthIncome>
{
    /**
     * 查询客户服务账期收入
     * 
     * @param id 客户服务账期收入主键
     * @return 客户服务账期收入
     */
    public CustomerServicePeriodMonthIncome selectCustomerServicePeriodMonthIncomeById(Long id);

    /**
     * 查询客户服务账期收入列表
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 客户服务账期收入集合
     */
    public List<CustomerServicePeriodMonthIncome> selectCustomerServicePeriodMonthIncomeList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 新增客户服务账期收入
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    public int insertCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 修改客户服务账期收入
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    public int updateCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 删除客户服务账期收入
     * 
     * @param id 客户服务账期收入主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthIncomeById(Long id);

    /**
     * 批量删除客户服务账期收入
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthIncomeByIds(Long[] ids);

    List<CustomerServicePeriodMonthIncome> incomeList(IPage<CustomerServicePeriodMonthIncome> page,
                                                      @Param("param") CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome,
                                                      @Param("userDept") UserDeptDTO userDept,
                                                      @Param("customerServiceIds") List<Long> customerServiceIds);

    void saveNewPeriodIncome(@Param("nowPeriod") Integer nowPeriod);

    List<CustomerServicePeriodMonthIncome> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(@Param("voList") List<RemoteCustomerServiceIncomeSearchVO> voList);
}
