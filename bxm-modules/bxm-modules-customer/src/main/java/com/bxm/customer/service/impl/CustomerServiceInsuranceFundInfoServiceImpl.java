package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceInsuranceFundInfoMapper;
import com.bxm.customer.domain.CustomerServiceInsuranceFundInfo;
import com.bxm.customer.service.ICustomerServiceInsuranceFundInfoService;

/**
 * 客户服务五险一金信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceInsuranceFundInfoServiceImpl extends ServiceImpl<CustomerServiceInsuranceFundInfoMapper, CustomerServiceInsuranceFundInfo> implements ICustomerServiceInsuranceFundInfoService
{
    @Autowired
    private CustomerServiceInsuranceFundInfoMapper customerServiceInsuranceFundInfoMapper;

    /**
     * 查询客户服务五险一金信息
     * 
     * @param id 客户服务五险一金信息主键
     * @return 客户服务五险一金信息
     */
    @Override
    public CustomerServiceInsuranceFundInfo selectCustomerServiceInsuranceFundInfoById(Long id)
    {
        return customerServiceInsuranceFundInfoMapper.selectCustomerServiceInsuranceFundInfoById(id);
    }

    /**
     * 查询客户服务五险一金信息列表
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 客户服务五险一金信息
     */
    @Override
    public List<CustomerServiceInsuranceFundInfo> selectCustomerServiceInsuranceFundInfoList(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo)
    {
        return customerServiceInsuranceFundInfoMapper.selectCustomerServiceInsuranceFundInfoList(customerServiceInsuranceFundInfo);
    }

    /**
     * 新增客户服务五险一金信息
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceInsuranceFundInfo(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo)
    {
        return customerServiceInsuranceFundInfoMapper.insertCustomerServiceInsuranceFundInfo(customerServiceInsuranceFundInfo);
    }

    /**
     * 修改客户服务五险一金信息
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceInsuranceFundInfo(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo)
    {
        return customerServiceInsuranceFundInfoMapper.updateCustomerServiceInsuranceFundInfo(customerServiceInsuranceFundInfo);
    }

    /**
     * 批量删除客户服务五险一金信息
     * 
     * @param ids 需要删除的客户服务五险一金信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInsuranceFundInfoByIds(Long[] ids)
    {
        return customerServiceInsuranceFundInfoMapper.deleteCustomerServiceInsuranceFundInfoByIds(ids);
    }

    /**
     * 删除客户服务五险一金信息信息
     * 
     * @param id 客户服务五险一金信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInsuranceFundInfoById(Long id)
    {
        return customerServiceInsuranceFundInfoMapper.deleteCustomerServiceInsuranceFundInfoById(id);
    }
}
