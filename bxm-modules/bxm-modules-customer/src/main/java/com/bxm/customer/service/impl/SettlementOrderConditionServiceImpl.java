package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.SettlementOrderConditionMapper;
import com.bxm.customer.domain.SettlementOrderCondition;
import com.bxm.customer.service.ISettlementOrderConditionService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 结算单条件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
public class SettlementOrderConditionServiceImpl extends ServiceImpl<SettlementOrderConditionMapper, SettlementOrderCondition> implements ISettlementOrderConditionService
{
    @Autowired
    private SettlementOrderConditionMapper settlementOrderConditionMapper;

    /**
     * 查询结算单条件
     * 
     * @param id 结算单条件主键
     * @return 结算单条件
     */
    @Override
    public SettlementOrderCondition selectSettlementOrderConditionById(Long id)
    {
        return settlementOrderConditionMapper.selectSettlementOrderConditionById(id);
    }

    /**
     * 查询结算单条件列表
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结算单条件
     */
    @Override
    public List<SettlementOrderCondition> selectSettlementOrderConditionList(SettlementOrderCondition settlementOrderCondition)
    {
        return settlementOrderConditionMapper.selectSettlementOrderConditionList(settlementOrderCondition);
    }

    /**
     * 新增结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    @Override
    public int insertSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition)
    {
        settlementOrderCondition.setCreateTime(DateUtils.getNowDate());
        return settlementOrderConditionMapper.insertSettlementOrderCondition(settlementOrderCondition);
    }

    /**
     * 修改结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    @Override
    public int updateSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition)
    {
        settlementOrderCondition.setUpdateTime(DateUtils.getNowDate());
        return settlementOrderConditionMapper.updateSettlementOrderCondition(settlementOrderCondition);
    }

    /**
     * 批量删除结算单条件
     * 
     * @param ids 需要删除的结算单条件主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderConditionByIds(Long[] ids)
    {
        return settlementOrderConditionMapper.deleteSettlementOrderConditionByIds(ids);
    }

    /**
     * 删除结算单条件信息
     * 
     * @param id 结算单条件主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderConditionById(Long id)
    {
        return settlementOrderConditionMapper.deleteSettlementOrderConditionById(id);
    }

    @Override
    public List<SettlementOrderCondition> selectBySettlementOrderId(Long settlementOrderId) {
        if (Objects.isNull(settlementOrderId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SettlementOrderCondition>().eq(SettlementOrderCondition::getSettlementOrderId, settlementOrderId));
    }

    @Override
    @Transactional
    public void removeAndSaveNewBySettlementOrderId(Long settlementOrderId, List<SettlementOrderCondition> settlementConditions) {
        if (Objects.isNull(settlementOrderId)) {
            return;
        }
        remove(new LambdaQueryWrapper<SettlementOrderCondition>().eq(SettlementOrderCondition::getSettlementOrderId, settlementOrderId));
        if (!ObjectUtils.isEmpty(settlementConditions)) {
            settlementConditions.forEach(c -> c.setSettlementOrderId(settlementOrderId));
            saveBatch(settlementConditions);
        }
    }
}
