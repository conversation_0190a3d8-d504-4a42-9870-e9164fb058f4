package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.CommonExcelUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.domain.DownloadRecord;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierBankExportDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierInAccountExportDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierModifyAccountExportDTO;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForManageDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForMyDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForPeriodDTO;
import com.bxm.customer.domain.dto.docHandover.DocHandoverDTO;
import com.bxm.customer.domain.dto.download.DownloadRecordDTO;
import com.bxm.customer.domain.dto.inAccount.InAccountDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.repairAccount.RepairAccountDTO;
import com.bxm.customer.domain.dto.rpa.RpaDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemDeductionDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemReportDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemSearchDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.dto.workBench.*;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierSearchVO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderSearchVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.customer.domain.vo.docHandover.CustomerServiceDocHandoverVO;
import com.bxm.customer.domain.vo.inAccount.InAccountVO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverSearchVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingRecordVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultVO;
import com.bxm.customer.domain.vo.repairAccount.RepairAccountVO;
import com.bxm.customer.domain.vo.rpa.RpaSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthNoAccountingNoAdvisorVO;
import com.bxm.customer.domain.vo.workBench.SyncItemSearchVO;
import com.bxm.customer.domain.vo.workOrder.QualityExceptionMiniListSearchVO;
import com.bxm.customer.mapper.DownloadRecordMapper;
import com.bxm.customer.service.*;
import com.bxm.customer.utils.ExcelUtils;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteLogService;
import com.bxm.system.api.domain.RemoteCustomerServiceLogSearchVO;
import com.bxm.system.api.domain.SysDept;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
@Slf4j
public class DownloadRecordServiceImpl extends ServiceImpl<DownloadRecordMapper, DownloadRecord> implements IDownloadRecordService
{
    @Autowired
    private DownloadRecordMapper downloadRecordMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FileService fileService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    @Lazy
    private ICCustomerServiceService customerServiceService;

    @Autowired
    @Lazy
    private ICustomerServiceRepairAccountService customerServiceRepairAccountService;

    @Autowired
    @Lazy
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    @Lazy
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    @Lazy
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    @Lazy
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    @Lazy
    private IBorrowOrderService borrowOrderService;

    @Autowired
    private IMaterialDeliverService materialDeliverService;

    @Autowired
    @Lazy
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    @Lazy
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    @Lazy
    private IBusinessTaskService businessTaskService;

    @Autowired
    private RemoteLogService remoteLogService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Lazy
    @Autowired
    private WorkBenchService workBenchService;

    @Lazy
    @Autowired
    private QualityCheckingService qualityCheckingService;

    @Override
    @PreDestroy
    public void updateFailWhenDestroy() {
        log.info("项目停止了======================");
    }

    @Override
    public Long createRecord(String title, Long dataCount, Long attachmentCount, Object obj, DownloadType downloadType) {
        if (checkHasDoingRecord(SecurityUtils.getUserId())) {
            throw new ServiceException("同时进行中的任务只能有一个");
        }
        DownloadRecord record = new DownloadRecord().setTitle(title)
                .setStatus(0)
                .setParam(JSONObject.toJSONString(obj))
                .setDataCount(dataCount)
                .setUserId(SecurityUtils.getUserId())
                .setDownloadType(downloadType.getCode())
                .setAttachmentCount(attachmentCount);
        save(record);
        return record.getId();
    }

    @Override
    public IPage<DownloadRecordDTO> downloadRecordList(Integer pageNum, Integer pageSize) {
        IPage<DownloadRecordDTO> result = new Page<>();
        IPage<DownloadRecord> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<DownloadRecord>()
                .eq(DownloadRecord::getUserId, SecurityUtils.getUserId())
                .eq(DownloadRecord::getIsDel, false)
                .ge(DownloadRecord::getCreateTime, LocalDateTime.now().minusHours(24))
                .orderByDesc(DownloadRecord::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            result.setRecords(iPage.getRecords().stream().map(row -> {
                DownloadRecordDTO dto = new DownloadRecordDTO();
                BeanUtils.copyProperties(row, dto);
                if (row.getStatus() == 1) {
//                    dto.setDownloadUrl(fileService.getFullFileUrl(row.getDownloadUrl()));
                    dto.setFileType(row.getDownloadUrl().contains(".zip") ? 2 : 1);
                }
                return dto;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void deleteDownloadRecord(CommonIdVO vo) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:deleteDownloadRecord:" + vo.getId(), userId.toString(), 5)) {
            try {
                DownloadRecord downloadRecord = getById(vo.getId());
                if (Objects.isNull(downloadRecord) || downloadRecord.getIsDel()) {
                    throw new ServiceException("导出记录不存在");
                }
                if (!Objects.equals(userId, downloadRecord.getUserId())) {
                    throw new ServiceException("无权限操作");
                }
                if (downloadRecord.getStatus() == 0) {
                    throw new ServiceException("正在导出，请完成后再删除");
                }
                updateById(new DownloadRecord().setId(vo.getId()).setIsDel(true));
            } finally {
                redisService.unlock("lock:deleteDownloadRecord:" + vo.getId(), userId.toString());
            }
        } else {
            throw new ServiceException("请勿重复操作");
        }
    }

    @Override
    @Transactional
    public void retryDownloadRecord(CommonIdVO vo) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:retryDownloadRecord:" + vo.getId(), userId.toString(), 5)) {
            try {
                DownloadRecord downloadRecord = getById(vo.getId());
                if (Objects.isNull(downloadRecord) || downloadRecord.getIsDel()) {
                    throw new ServiceException("导出记录不存在");
                }
                if (!Objects.equals(userId, downloadRecord.getUserId())) {
                    throw new ServiceException("无权限操作");
                }
                if (downloadRecord.getStatus() == 0) {
                    throw new ServiceException("导出未完成");
                }
                if (downloadRecord.getStatus() == 1) {
                    throw new ServiceException("导出已成功，请勿重试");
                }
                if (checkHasDoingRecord(userId)) {
                    throw new ServiceException("同时进行中的任务只能有一个");
                }
                updateById(new DownloadRecord().setId(vo.getId()).setStatus(0));
                doRetry(downloadRecord);
            } finally {
                redisService.unlock("lock:retryDownloadRecord:" + vo.getId(), userId.toString());
            }
        } else {
            throw new ServiceException("请勿重复操作");
        }
    }

    @Override
    public Boolean checkHasDoingRecord(Long userId) {
        return count(new LambdaQueryWrapper<DownloadRecord>()
                .eq(DownloadRecord::getUserId, userId).eq(DownloadRecord::getIsDel, false)
                .eq(DownloadRecord::getStatus, 0).eq(DownloadRecord::getIsFileDel, false)) > 0;
    }

    private void doRetry(DownloadRecord downloadRecord) {
        // 1-服务-客户列表，2-服务-操作记录，3-服务-账期列表，4-服务-年度汇总，5-服务-补账服务，6-交付-医社保，7-交付-个税（工资薪金），8-个税（经营所得），9-交付-国税，10-交付-医社保 含附件，11-交付-个税（工资薪金）含附件，12-个税（经营所得）含附件，13-交付-国税 含附件，14-交付-入账，15-收入，16-材料-交接，17-材料借阅
        if (downloadRecord.getDownloadType() == 1) {
            // 客户列表
            CompletableFuture.runAsync(() -> {
//                try {
//                    CustomerServiceSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServiceSearchVO.class);
//                    List<CustomerServiceDTO> list = Lists.newArrayList();
//                    Integer pageNum = 1;
//                    Integer pageSize = 5000;
//                    vo.setPageSize(pageSize);
//                    while (true) {
//                        vo.setPageNum(pageNum);
//                        List<CustomerServiceDTO> l = customerServiceService.customerServiceList(vo.getDeptId(), vo).getRecords();
//                        if (!ObjectUtils.isEmpty(l)) {
//                            list.addAll(l);
//                            pageNum++;
//                        } else {
//                            break;
//                        }
//                    }
//                    updateDataCount(downloadRecord.getId(), (long) list.size());
//                    ExcelUtil<CustomerServiceDTO> util = new ExcelUtil<>(CustomerServiceDTO.class);
//                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
//                } catch (Exception e) {
//                    updateDownloadError(downloadRecord.getId(), e.getMessage());
//                }
                try {
                    CustomerServiceSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServiceSearchVO.class);
                    SysDept sysDept = remoteDeptService.getDeptInfo(vo.getDeptId()).getDataThrowException();
                    Integer deptType = Objects.isNull(sysDept) ? 1 : 2;
                    List<CustomerServiceDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerServiceDTO> l = customerServiceService.customerServiceList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());

                    File zipFile = File.createTempFile("temp", ".zip");
                    try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                        if (vo.getExportTypes().contains("1")) {
                            Map<String, Class<?>> sheetClassMap = new HashMap<>();
                            Map<String, List<?>> dataMap = new HashMap<>();
                            sheetClassMap.put("客户信息", CustomerServiceDTO.class);
                            dataMap.put("客户信息", list);
                            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                            ZipEntry excelEntry = new ZipEntry("客户信息.xlsx");
                            zos.putNextEntry(excelEntry);
                            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                                workbook.write(baos);
                                zos.write(baos.toByteArray());
                            }
                        }
                        if (vo.getExportTypes().contains("2")) {
                            Map<String, Class<?>> sheetClassMap = new HashMap<>();
                            Map<String, List<?>> dataMap = new HashMap<>();
                            if (Objects.equals(deptType, 1)) {
                                sheetClassMap.put("银行账号", CustomerServiceBankBusinessDTO.class);
                                dataMap.put("银行账号", list.stream().map(CustomerServiceDTO::getBankBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                            } else {
                                sheetClassMap.put("银行账号", CustomerServiceBankDTO.class);
                                dataMap.put("银行账号", list.stream().map(CustomerServiceDTO::getBankList).flatMap(List::stream).collect(Collectors.toList()));
                            }
                            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                            ZipEntry putNextEntry = new ZipEntry("银行账号.xlsx");
                            zos.putNextEntry(putNextEntry);
                            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                                workbook.write(baos);
                                zos.write(baos.toByteArray());
                            }
                        }
                        if (vo.getExportTypes().contains("3")) {
                            Map<String, Class<?>> sheetClassMap = new HashMap<>();
                            Map<String, List<?>> dataMap = new HashMap<>();
                            if (Objects.equals(deptType, 1)) {
                                sheetClassMap.put("税种", CustomerTaxTypeCheckBusinessDTO.class);
                                dataMap.put("税种", list.stream().map(CustomerServiceDTO::getTaxTypeCheckBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                            } else {
                                sheetClassMap.put("税种", CustomerTaxTypeCheckDTO.class);
                                dataMap.put("税种", list.stream().map(CustomerServiceDTO::getTaxTypeCheckList).flatMap(List::stream).collect(Collectors.toList()));
                            }
                            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                            ZipEntry putNextEntry = new ZipEntry("税种.xlsx");
                            zos.putNextEntry(putNextEntry);
                            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                                workbook.write(baos);
                                zos.write(baos.toByteArray());
                            }
                        }
                        if (vo.getExportTypes().contains("4")) {
                            Map<String, Class<?>> sheetClassMap = new HashMap<>();
                            Map<String, List<?>> dataMap = new HashMap<>();
                            if (Objects.equals(deptType, 1)) {
                                sheetClassMap.put("系统账号", CustomerSysAccountBusinessDTO.class);
                                dataMap.put("系统账号", list.stream().map(CustomerServiceDTO::getSysAccountBusinessList).flatMap(List::stream).collect(Collectors.toList()));
                            } else {
                                sheetClassMap.put("系统账号", CustomerSysAccountDTO.class);
                                dataMap.put("系统账号", list.stream().map(CustomerServiceDTO::getSysAccountList).flatMap(List::stream).collect(Collectors.toList()));
                            }
                            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
                            ZipEntry putNextEntry = new ZipEntry("系统账号.xlsx");
                            zos.putNextEntry(putNextEntry);
                            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                                workbook.write(baos);
                                zos.write(baos.toByteArray());
                            }
                        }
                    }
                    asyncService.uploadExport(zipFile, (long) list.size(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 2) {
            // 操作记录
            RemoteCustomerServiceLogSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerServiceLogSearchVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteLogService.exportCustomerBusinessLogAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 3) {
            // 账期列表
            CompletableFuture.runAsync(() -> {
               try {
                   CustomerServicePeriodMonthSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServicePeriodMonthSearchVO.class);
                   List<CustomerServicePeriodMonthDTO> list = Lists.newArrayList();
                   Integer pageNum = 1;
                   Integer pageSize = 5000;
                   vo.setPageSize(pageSize);
                   while (true) {
                       vo.setPageNum(pageNum);
                       List<CustomerServicePeriodMonthDTO> l = customerServiceService.customerServicePeriodMonthList(vo.getDeptId(), vo).getRecords();
                       if (!ObjectUtils.isEmpty(l)) {
                           list.addAll(l);
                           pageNum++;
                       } else {
                           break;
                       }
                   }
                   updateDataCount(downloadRecord.getId(), (long) list.size());
                   ExcelUtil<CustomerServicePeriodMonthDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthDTO.class);
                   asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
               } catch (Exception e) {
                   updateDownloadError(downloadRecord.getId(), e.getMessage());
               }
            });
        } else if (downloadRecord.getDownloadType() == 4) {
            // 年度汇总
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerServicePeriodYearSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServicePeriodYearSearchVO.class);
                    List<CustomerServicePeriodYearDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerServicePeriodYearDTO> l = customerServiceService.customerServicePeriodYearList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<CustomerServicePeriodYearDTO> util = new ExcelUtil<>(CustomerServicePeriodYearDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 5) {
            // 补账服务
            CompletableFuture.runAsync(() -> {
                try {
                    RepairAccountVO vo = JSONObject.parseObject(downloadRecord.getParam(), RepairAccountVO.class);
                    List<RepairAccountDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<RepairAccountDTO> l = customerServiceRepairAccountService.repairAccountList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<CustomerServicePeriodYearDTO> util = new ExcelUtil<>(CustomerServicePeriodYearDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 6) {
            //交付-医社保
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverSocialSecurityExportDTO dto = new CustomerDeliverSocialSecurityExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverSocialSecurityExportDTO> util = new ExcelUtil<>(CustomerDeliverSocialSecurityExportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

       } else if (downloadRecord.getDownloadType() == 7) {
            //交付-个税（工资薪金）
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverPersonalTaxExportDTO dto = new CustomerDeliverPersonalTaxExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverPersonalTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverPersonalTaxExportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 8) {
            //个税（经营所得）
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverOperatingIncomePersonTaxExportDTO dto = new CustomerDeliverOperatingIncomePersonTaxExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverOperatingIncomePersonTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverOperatingIncomePersonTaxExportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 9) {
            //交付-国税
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverCountryTaxExportDTO dto = new CustomerDeliverCountryTaxExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                dto.setTotalTaxAmount(Objects.isNull(row.getTotalTaxAmount()) ? "-" : row.getTotalTaxAmount());
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverCountryTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverCountryTaxExportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 10) {
            //交付-医社保 含附件
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.socialSecurityExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 11) {
            //交付-个税（工资薪金）含附件
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.personalTaxExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 12) {
            //个税（经营所得）含附件
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.operatingIncomeTaxExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 13) {
            //交付-国税 含附件
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.countryTaxExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 14) {
            //交付-入账
            CompletableFuture.runAsync(() -> {
                try {
                    InAccountVO vo = JSONObject.parseObject(downloadRecord.getParam(), InAccountVO.class);
                    List<InAccountDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<InAccountDTO> l = customerServiceInAccountService.inAccountList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<InAccountDTO> util = new ExcelUtil<>(InAccountDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 15) {
            //收入
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerServicePeriodMonthIncome vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServicePeriodMonthIncome.class);
                    List<CustomerServicePeriodMonthIncome> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    while (true) {
                        List<CustomerServicePeriodMonthIncome> l = customerServicePeriodMonthIncomeService.incomeList(vo, vo.getDeptId(), pageNum, pageSize).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    if (StringUtils.isEmpty(vo.getExportTypes())) {
                        ExcelUtil<CustomerServicePeriodMonthIncome> util = new ExcelUtil<>(CustomerServicePeriodMonthIncome.class);
                        asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                    } else {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        sheetClassMap.put(downloadRecord.getTitle(), CustomerServicePeriodMonthIncome.class);
                        dataMap.put(downloadRecord.getTitle(), list);
                        asyncService.uploadExport(customerServicePeriodMonthIncomeService.buildFiles(vo, list), dataMap, sheetClassMap, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 16) {
            //材料-交接
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerServiceDocHandoverVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServiceDocHandoverVO.class);
                    List<DocHandoverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<DocHandoverDTO> l = customerServiceDocHandoverService.docHandoverList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<DocHandoverDTO> util = new ExcelUtil<>(DocHandoverDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 17) {
            //材料借阅
            CompletableFuture.runAsync(() -> {
                try {
                    BorrowOrderSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), BorrowOrderSearchVO.class);
                    List<BorrowOrderDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<BorrowOrderDTO> l = borrowOrderService.borrowOrderList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<BorrowOrderDTO> util = new ExcelUtil<>(BorrowOrderDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });

        } else if (downloadRecord.getDownloadType() == 18) {
            // 入账-含附件
            RemoteCustomerInAccountDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerInAccountDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.exportInAccountListAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 19) {
            //交付-预认证
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                PreAuthInfoDTO preAuthInfoDTO = row.getPreAuthInfoDTO();
                                CustomerDeliverPreAuthExportDTO dto = new CustomerDeliverPreAuthExportDTO();
                                BeanUtils.copyProperties(preAuthInfoDTO, dto);
                                BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(PreAuthInfoDTO.class).toArray(new String[0]));
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverCountryTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverCountryTaxExportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 20) {
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.preAuthExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 21) {
            //交付-汇算
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverSettleAccountsDTO dto = new CustomerDeliverSettleAccountsDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverSettleAccountsDTO> util = new ExcelUtil<>(CustomerDeliverSettleAccountsDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 22) {
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.settleAccountsExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 23) {
            //交付-年报
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverAnnualReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverAnnualReportDTO dto = new CustomerDeliverAnnualReportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverAnnualReportDTO> util = new ExcelUtil<>(CustomerDeliverAnnualReportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 24) {
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.annualReportExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 25) {
            CompletableFuture.runAsync(() -> {
                try {
                    AccountingCashierSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), AccountingCashierSearchVO.class);
                    List<AccountingCashierDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<AccountingCashierDTO> l = customerServiceCashierAccountingService.accountingCashierList(vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                        List<AccountingCashierInAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                                list.stream().map(row -> {
                                    AccountingCashierInAccountExportDTO dto = new AccountingCashierInAccountExportDTO();
                                    BeanUtils.copyProperties(row, dto);
                                    return dto;
                                }).collect(Collectors.toList());
                        updateDataCount(downloadRecord.getId(), (long) exports.size());
                        ExcelUtil<AccountingCashierInAccountExportDTO> util = new ExcelUtil<>(AccountingCashierInAccountExportDTO.class);
                        asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                    } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                        List<AccountingCashierBankExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                                list.stream().map(row -> {
                                    AccountingCashierBankExportDTO dto = new AccountingCashierBankExportDTO();
                                    BeanUtils.copyProperties(row, dto);
                                    return dto;
                                }).collect(Collectors.toList());
                        updateDataCount(downloadRecord.getId(), (long) exports.size());
                        ExcelUtil<AccountingCashierBankExportDTO> util = new ExcelUtil<>(AccountingCashierBankExportDTO.class);
                        asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                    } else if (Objects.equals(vo.getType(), AccountingCashierType.CHANGE.getCode())) {
                        List<AccountingCashierModifyAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                                list.stream().map(row -> {
                                    AccountingCashierModifyAccountExportDTO dto = new AccountingCashierModifyAccountExportDTO();
                                    BeanUtils.copyProperties(row, dto);
                                    return dto;
                                }).collect(Collectors.toList());
                        updateDataCount(downloadRecord.getId(), (long) exports.size());
                        ExcelUtil<AccountingCashierModifyAccountExportDTO> util = new ExcelUtil<>(AccountingCashierModifyAccountExportDTO.class);
                        asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 26) {
            RemoteAccountingCashierSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteAccountingCashierSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.accountingCashierExportAndUploadRetry(vo, SecurityConstants.INNER).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 27) {
            CompletableFuture.runAsync(() -> {
                try {
                    MaterialDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), MaterialDeliverSearchVO.class);
                    List<MaterialDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<MaterialDeliverDTO> l = materialDeliverService.materialDeliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<MaterialDeliverDTO> util = new ExcelUtil<>(MaterialDeliverDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 28) {
            RemoteMaterialFilesDownloadRetryDTO dto = new RemoteMaterialFilesDownloadRetryDTO();
            List<Long> ids = JSONObject.parseArray(downloadRecord.getParam(), Long.class);
            dto.setIds(ids);
            dto.setDownloadRecordTitle(downloadRecord.getTitle());
            dto.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.materialFilesExportAndUploadRetry(dto, SecurityConstants.INNER).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 29) {
            RemotePushReviewErrorDataRetryDTO dto = new RemotePushReviewErrorDataRetryDTO();
            String batchNo = JSONObject.parseObject(downloadRecord.getParam(), String.class);
            dto.setBatchNo(batchNo);
            dto.setDownloadRecordTitle(downloadRecord.getTitle());
            dto.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.pushReviewExportAndUploadRetry(dto, SecurityConstants.INNER).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 30) {
            //交付-残保金
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverSettleAccountsDTO dto = new CustomerDeliverSettleAccountsDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverSettleAccountsDTO> util = new ExcelUtil<>(CustomerDeliverSettleAccountsDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 31) {
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.residualBenefitsExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 32) {
            CustomerServiceIncomeInfoSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServiceIncomeInfoSearchVO.class);
            SysDept sysDept = remoteDeptService.getDeptInfo(vo.getDeptId()).getDataThrowException();
            Integer deptType = !Objects.isNull(sysDept) && Objects.equals("0", sysDept.getDelFlag()) ? sysDept.getDeptType() : 1;
            CompletableFuture.runAsync(() -> {
                try {
                    List<CustomerServiceIncomeInfoDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    while (true) {
                        vo.setPageNum(pageNum);
                        vo.setPageSize(pageSize);
                        List<CustomerServiceIncomeInfoDTO> l = customerServiceService.customerServiceIncomeInfo(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<CustomerServiceIncomeInfoDTO> util = new ExcelUtil<>(CustomerServiceIncomeInfoDTO.class);
                    if (Objects.equals(1, deptType)) {
                        util.hideColumn("accountingTopDeptName", "accountingInfo");
                    }
                    LocalDate now = LocalDate.now();
                    Field thisMonthIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthIncomeStr");
                    Excel thisMonthIncomeStrAnnotation = thisMonthIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthIncomeStrAnnotation, "name", now.getMonthValue() + "月");

                    Field thisMonthPreOneIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreOneIncomeStr");
                    Excel thisMonthPreOneIncomeStrAnnotation = thisMonthPreOneIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreOneIncomeStrAnnotation, "name", now.minusMonths(1).getMonthValue() + "月");

                    Field thisMonthPreTwoIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreTwoIncomeStr");
                    Excel thisMonthPreTwoIncomeStrAnnotation = thisMonthPreTwoIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreTwoIncomeStrAnnotation, "name", now.minusMonths(2).getMonthValue() + "月");

                    Field thisMonthPreThreeIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreThreeIncomeStr");
                    Excel thisMonthPreThreeIncomeStrAnnotation = thisMonthPreThreeIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreThreeIncomeStrAnnotation, "name", now.minusMonths(3).getMonthValue() + "月");

                    Field thisMonthPreFourIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreFourIncomeStr");
                    Excel thisMonthPreFourIncomeStrAnnotation = thisMonthPreFourIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreFourIncomeStrAnnotation, "name", now.minusMonths(4).getMonthValue() + "月");

                    Field thisMonthPreFiveIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreFiveIncomeStr");
                    Excel thisMonthPreFiveIncomeStrAnnotation = thisMonthPreFiveIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreFiveIncomeStrAnnotation, "name", now.minusMonths(5).getMonthValue() + "月");

                    Field thisMonthPreSixIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreSixIncomeStr");
                    Excel thisMonthPreSixIncomeStrAnnotation = thisMonthPreSixIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreSixIncomeStrAnnotation, "name", now.minusMonths(6).getMonthValue() + "月");

                    Field thisMonthPreSevenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreSevenIncomeStr");
                    Excel thisMonthPreSevenIncomeStrAnnotation = thisMonthPreSevenIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreSevenIncomeStrAnnotation, "name", now.minusMonths(7).getMonthValue() + "月");

                    Field thisMonthPreEightIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreEightIncomeStr");
                    Excel thisMonthPreEightIncomeStrAnnotation = thisMonthPreEightIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreEightIncomeStrAnnotation, "name", now.minusMonths(8).getMonthValue() + "月");

                    Field thisMonthPreNineIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreNineIncomeStr");
                    Excel thisMonthPreNineIncomeStrAnnotation = thisMonthPreNineIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreNineIncomeStrAnnotation, "name", now.minusMonths(9).getMonthValue() + "月");

                    Field thisMonthPreTenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreTenIncomeStr");
                    Excel thisMonthPreTenIncomeStrAnnotation = thisMonthPreTenIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreTenIncomeStrAnnotation, "name", now.minusMonths(10).getMonthValue() + "月");

                    Field thisMonthPreElevenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreElevenIncomeStr");
                    Excel thisMonthPreElevenIncomeStrAnnotation = thisMonthPreElevenIncomeStr.getAnnotation(Excel.class);
                    CommonExcelUtils.setAnnotationValue(thisMonthPreElevenIncomeStrAnnotation, "name", now.minusMonths(11).getMonthValue() + "月");

                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 33) {
            CustomerServiceYearIncomeInfoSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServiceYearIncomeInfoSearchVO.class);
            SysDept sysDept = remoteDeptService.getDeptInfo(vo.getDeptId()).getDataThrowException();
            Integer deptType = !Objects.isNull(sysDept) && Objects.equals("0", sysDept.getDelFlag()) ? sysDept.getDeptType() : 1;
            CompletableFuture.runAsync(() -> {
                try {
                    List<CustomerServiceYearIncomeInfoDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    while (true) {
                        vo.setPageNum(pageNum);
                        vo.setPageSize(pageSize);
                        List<CustomerServiceYearIncomeInfoDTO> l = customerServiceService.customerServiceYearIncomeInfo(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<CustomerServiceYearIncomeInfoDTO> util = new ExcelUtil<>(CustomerServiceYearIncomeInfoDTO.class);
                    if (Objects.equals(1, deptType)) {
                        util.hideColumn("accountingTopDeptName", "accountingInfo");
                    }
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 34) {
            //第三方通知
            CompletableFuture.runAsync(() -> {
                try {
                    OpenApiRecordSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), OpenApiRecordSearchVO.class);
                    List<OpenApiRecordDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<OpenApiRecordDTO> l = openApiNoticeRecordService.openapiRecordList(vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<OpenApiRecordDTO> util = new ExcelUtil<>(OpenApiRecordDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 35) {
            // rpa
            CompletableFuture.runAsync(() -> {
                try {
                    RpaSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), RpaSearchVO.class);
                    List<RpaDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<RpaDTO> l = openApiNoticeRecordService.rpaList(vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<RpaDTO> util = new ExcelUtil<>(RpaDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 36) {
            CompletableFuture.runAsync(() -> {
                try {
                    BusinessTaskVO vo = JSONObject.parseObject(downloadRecord.getParam(), BusinessTaskVO.class);
                    List<BusinessTaskForPeriodDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<BusinessTaskForPeriodDTO> l = businessTaskService.businessTaskListForPeriod(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<BusinessTaskForPeriodDTO> util = new ExcelUtil<>(BusinessTaskForPeriodDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 37) {
            CompletableFuture.runAsync(() -> {
                try {
                    BusinessTaskVO vo = JSONObject.parseObject(downloadRecord.getParam(), BusinessTaskVO.class);
                    List<BusinessTaskForManageDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<BusinessTaskForManageDTO> l = businessTaskService.businessTaskListForManage(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<BusinessTaskForManageDTO> util = new ExcelUtil<>(BusinessTaskForManageDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 38) {
            CompletableFuture.runAsync(() -> {
                try {
                    BusinessTaskVO vo = JSONObject.parseObject(downloadRecord.getParam(), BusinessTaskVO.class);
                    List<BusinessTaskForMyDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<BusinessTaskForMyDTO> l = businessTaskService.businessTaskListForMy(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<BusinessTaskForMyDTO> util = new ExcelUtil<>(BusinessTaskForMyDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 39) {
            RemoteWorkOrderDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteWorkOrderDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.workOrderExportAndUploadRetry(vo, SecurityConstants.INNER).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 40) {
            //交付-次报
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerDeliverSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerDeliverSearchVO.class);
                    List<CustomerDeliverDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 5000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, vo.getDeptId()).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    List<CustomerDeliverTimesReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                CustomerDeliverTimesReportDTO dto = new CustomerDeliverTimesReportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    updateDataCount(downloadRecord.getId(), (long) exports.size());
                    ExcelUtil<CustomerDeliverTimesReportDTO> util = new ExcelUtil<>(CustomerDeliverTimesReportDTO.class);
                    asyncService.uploadExport(util, exports, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 41) {
            RemoteCustomerDeliverSearchDownloadVO vo = JSONObject.parseObject(downloadRecord.getParam(), RemoteCustomerDeliverSearchDownloadVO.class);
            vo.setDownloadRecordTitle(downloadRecord.getTitle());
            vo.setDownloadRecordId(downloadRecord.getId());
            remoteFileService.timesReportExportAndUploadRetry(vo).getDataThrowException();
        } else if (downloadRecord.getDownloadType() == 42) {
            CompletableFuture.runAsync(() -> {
                try {
                    SyncItemSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), SyncItemSearchVO.class);
                    List<SyncItemSearchDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 1000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<SyncItemSearchDTO> l = workBenchService.syncItemPageList(vo.getHeadDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    if (vo.getType() == 1) {
                        List<SyncItemReportDTO> exportList = list.stream().map(row -> {
                            SyncItemReportDTO dto = new SyncItemReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                        ExcelUtil<SyncItemReportDTO> util = new ExcelUtil<>(SyncItemReportDTO.class);
                        asyncService.uploadExport(util, exportList, downloadRecord.getTitle(), downloadRecord.getId());
                    } else if (vo.getType() == 2) {
                        List<SyncItemDeductionDTO> exportList = list.stream().map(row -> {
                            SyncItemDeductionDTO dto = new SyncItemDeductionDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                        ExcelUtil<SyncItemDeductionDTO> util = new ExcelUtil<>(SyncItemDeductionDTO.class);
                        asyncService.uploadExport(util, exportList, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 46) {
            CompletableFuture.runAsync(() -> {
                try {
                    CustomerServicePeriodMonthNoAccountingNoAdvisorVO vo = JSONObject.parseObject(downloadRecord.getParam(), CustomerServicePeriodMonthNoAccountingNoAdvisorVO.class);
                    List<CustomerServicePeriodMonthSimpleDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 1000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<CustomerServicePeriodMonthSimpleDTO> l = workBenchService.periodNoAccountingNoAdvisorPageList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    if (vo.getType() == 1) {
                        List<CustomerServicePeriodMonthNoAccountingDTO> exportList = list.stream().map(row -> {
                            CustomerServicePeriodMonthNoAccountingDTO dto = new CustomerServicePeriodMonthNoAccountingDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                        ExcelUtil<CustomerServicePeriodMonthNoAccountingDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthNoAccountingDTO.class);
                        asyncService.uploadExport(util, exportList, downloadRecord.getTitle(), downloadRecord.getId());
                    } else if (vo.getType() == 2) {
                        List<CustomerServicePeriodMonthNoAdvisorDTO> exportList = list.stream().map(row -> {
                            CustomerServicePeriodMonthNoAdvisorDTO dto = new CustomerServicePeriodMonthNoAdvisorDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                        ExcelUtil<CustomerServicePeriodMonthNoAdvisorDTO> util = new ExcelUtil<>(CustomerServicePeriodMonthNoAdvisorDTO.class);
                        asyncService.uploadExport(util, exportList, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 43) {
            CompletableFuture.runAsync(() -> {
                try {
                    QualityExceptionMiniListSearchVO vo = JSONObject.parseObject(downloadRecord.getParam(), QualityExceptionMiniListSearchVO.class);
                    List<QualityExceptionMiniListDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 1000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<QualityExceptionMiniListDTO> l = workBenchService.qualityExceptionMiniList(vo.getDeptId(), vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    ExcelUtil<QualityExceptionMiniListDTO> util = new ExcelUtil<>(QualityExceptionMiniListDTO.class);
                    asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 44) {
            CompletableFuture.runAsync(() -> {
                try {
                    QualityCheckingResultVO vo = JSONObject.parseObject(downloadRecord.getParam(), QualityCheckingResultVO.class);
                    List<QualityCheckingResultDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 1000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<QualityCheckingResultDTO> l = qualityCheckingService.qualityCheckingResultPageList(vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    if (StringUtils.isEmpty(vo.getExportTypes())) {
                        ExcelUtil<QualityCheckingResultDTO> util = new ExcelUtil<>(QualityCheckingResultDTO.class);
                        asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                    } else {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        sheetClassMap.put(downloadRecord.getTitle(), QualityCheckingResultDTO.class);
                        dataMap.put(downloadRecord.getTitle(), list);
                        asyncService.uploadExport(qualityCheckingService.buildQualityCheckingResultFiles(vo.getExportTypes(), list), dataMap, sheetClassMap, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        } else if (downloadRecord.getDownloadType() == 45) {
            CompletableFuture.runAsync(() -> {
                try {
                    QualityCheckingRecordVO vo = JSONObject.parseObject(downloadRecord.getParam(), QualityCheckingRecordVO.class);
                    List<QualityCheckingRecordDTO> list = Lists.newArrayList();
                    Integer pageNum = 1;
                    Integer pageSize = 1000;
                    vo.setPageSize(pageSize);
                    while (true) {
                        vo.setPageNum(pageNum);
                        List<QualityCheckingRecordDTO> l = qualityCheckingService.qualityCheckingRecordPageList(vo).getRecords();
                        if (!ObjectUtils.isEmpty(l)) {
                            list.addAll(l);
                            pageNum++;
                        } else {
                            break;
                        }
                    }
                    updateDataCount(downloadRecord.getId(), (long) list.size());
                    if (StringUtils.isEmpty(vo.getExportTypes())) {
                        ExcelUtil<QualityCheckingRecordDTO> util = new ExcelUtil<>(QualityCheckingRecordDTO.class);
                        asyncService.uploadExport(util, list, downloadRecord.getTitle(), downloadRecord.getId());
                    } else {
                        Map<String, Class<?>> sheetClassMap = new HashMap<>();
                        Map<String, List<?>> dataMap = new HashMap<>();
                        sheetClassMap.put(downloadRecord.getTitle(), QualityCheckingRecordDTO.class);
                        dataMap.put(downloadRecord.getTitle(), list);
                        asyncService.uploadExport(qualityCheckingService.buildQualityCheckingRecordFiles(vo.getExportTypes(), list), dataMap, sheetClassMap, downloadRecord.getTitle(), downloadRecord.getId());
                    }
                } catch (Exception e) {
                    updateDownloadError(downloadRecord.getId(), e.getMessage());
                }
            });
        }
        else {
            throw new ServiceException("未知导出类型");
        }
    }

    private List<String> getPreAuthAllFieldNames(Class<PreAuthInfoDTO> preAuthInfoDTOClass) {
        List<String> fieldNames = new ArrayList<>();
        Field[] fields = preAuthInfoDTOClass.getDeclaredFields();
        for (Field field : fields) {
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    @Override
    @Transactional
    public void stopDownloadRecord(CommonIdVO vo) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:stopDownloadRecord:" + vo.getId(), userId.toString(), 5)) {
            try {
                DownloadRecord downloadRecord = getById(vo.getId());
                if (Objects.isNull(downloadRecord) || downloadRecord.getIsDel()) {
                    throw new ServiceException("导出记录不存在");
                }
                if (!Objects.equals(userId, downloadRecord.getUserId())) {
                    throw new ServiceException("无权限操作");
                }
                if (downloadRecord.getStatus() != 0) {
                    throw new ServiceException("导出已完成");
                }
                updateById(new DownloadRecord().setId(vo.getId()).setStatus(2));
            } finally {
                redisService.unlock("lock:stopDownloadRecord:" + vo.getId(), userId.toString());
            }
        } else {
            throw new ServiceException("请勿重复操作");
        }
    }

    @Override
    @Transactional
    public void deleteDownloadFile(String jobParam) {
        String endTime;
        if (!StringUtils.isEmpty(jobParam)) {
            endTime = jobParam + " 23:59:59";
        } else {
            // 默认删除前2天的未删除的文件
            LocalDate now = LocalDate.now();
            endTime = now.minusDays(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59";
        }
        List<DownloadRecord> needDeleteFileRecords = list(new LambdaQueryWrapper<DownloadRecord>()
                .eq(DownloadRecord::getIsFileDel, false)
                .le(DownloadRecord::getCreateTime, endTime));
        if (ObjectUtils.isEmpty(needDeleteFileRecords)) {
            return;
        }
        updateBatchById(needDeleteFileRecords.stream().map(row -> new DownloadRecord().setId(row.getId()).setIsFileDel(true)).collect(Collectors.toList()));
        needDeleteFileRecords.forEach(record -> remoteFileService.deleteOssFile(record.getDownloadUrl(), SecurityConstants.INNER));
    }

    @Override
    public String getDownloadUlr(Long id) {
        if (Objects.isNull(id)) {
            return "";
        }
        DownloadRecord downloadRecord = getById(id);
        if (Objects.isNull(downloadRecord) || downloadRecord.getIsDel() ||
                downloadRecord.getStatus() != 1 || StringUtils.isEmpty(downloadRecord.getDownloadUrl())) {
            return "";
        }
        return fileService.getFullFileUrl(downloadRecord.getDownloadUrl());
    }

    @Override
    public void updateDataCount(Long recordId, Long dataCount) {
        updateById(new DownloadRecord().setId(recordId).setDataCount(dataCount));
    }

    @Override
    public void updateDownloadError(Long recordId, String errorMsg) {
        updateById(new DownloadRecord().setId(recordId).setStatus(2).setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg));
    }
}
