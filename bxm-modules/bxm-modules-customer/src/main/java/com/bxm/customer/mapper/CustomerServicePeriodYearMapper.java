package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServicePeriodYear;
import org.apache.ibatis.annotations.Param;

/**
 * 客户服务年度账期Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Mapper
public interface CustomerServicePeriodYearMapper extends BaseMapper<CustomerServicePeriodYear>
{
    /**
     * 查询客户服务年度账期
     * 
     * @param id 客户服务年度账期主键
     * @return 客户服务年度账期
     */
    public CustomerServicePeriodYear selectCustomerServicePeriodYearById(Long id);

    /**
     * 查询客户服务年度账期列表
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 客户服务年度账期集合
     */
    public List<CustomerServicePeriodYear> selectCustomerServicePeriodYearList(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 新增客户服务年度账期
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    public int insertCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 修改客户服务年度账期
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    public int updateCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 删除客户服务年度账期
     * 
     * @param id 客户服务年度账期主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodYearById(Long id);

    /**
     * 批量删除客户服务年度账期
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodYearByIds(Long[] ids);

    void saveNewPeriodYear(@Param("nowPeriod") Integer nowPeriod, @Param("nowYear") Integer nowYear);

    void updateLastInAccountId();

    List<CommonDeptCountDTO> customerServiceYearAccountingCountList(@Param("userDeptDTO") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearAdvisorCountList(@Param("userDeptDTO") UserDeptDTO userDeptDTO);
}
