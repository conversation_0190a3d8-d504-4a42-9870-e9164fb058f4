package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.CustomerServiceInAccount;
import com.bxm.customer.domain.dto.PeriodCountDTO;
import com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.dto.inAccount.InAccountDTO;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.inAccount.InAccountVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 入账、入账交付Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceInAccountMapper extends BaseMapper<CustomerServiceInAccount> {
    /**
     * 查询入账、入账交付
     *
     * @param id 入账、入账交付主键
     * @return 入账、入账交付
     */
    public CustomerServiceInAccount selectCustomerServiceInAccountById(Long id);

    /**
     * 查询入账、入账交付列表
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 入账、入账交付集合
     */
    public List<CustomerServiceInAccount> selectCustomerServiceInAccountList(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 新增入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    public int insertCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 修改入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    public int updateCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 删除入账、入账交付
     *
     * @param id 入账、入账交付主键
     * @return 结果
     */
    public int deleteCustomerServiceInAccountById(Long id);

    /**
     * 批量删除入账、入账交付
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInAccountByIds(Long[] ids);

    List<InAccountDTO> selectInAccountList(
            IPage<InAccountDTO> result,
            @Param("vo") InAccountVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("commonIdsSearchVO") CommonIdsSearchVO commonIdsSearchVO
    );

    List<InAccountDTO> selectInAccountListV2(
            IPage<InAccountDTO> result,
            @Param("vo") InAccountVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("commonIdsSearchVO") CommonIdsSearchVO commonIdsSearchVO,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds
    );

    @Select("select * from c_customer_service_in_account where is_del = 0 and period < #{period} and (in_time is null or end_time is null) and customer_service_id = #{customerServiceId}")
    @ResultType(CustomerServiceInAccount.class)
    List<CustomerServiceInAccount> selectNotTime(
            @Param("customerServiceId") Long customerServiceId,
            @Param("period") Integer period
    );

    List<CustomerServiceInAccount> selectNotTimeBatch(
            @Param("customerServiceIds") List<Long> customerServiceIds,
            @Param("period") Integer period
    );

    List<CustomerServiceInAccount> selectNotTimeBatchWithout(
            @Param("customerServiceId") Long customerServiceId,
            @Param("period") Integer period,
            @Param("notIds") List<Long> notIds
    );

    List<CustomerServiceInAccount> selectByInAccountList(@Param("inAccountList") List<CustomerServiceInAccount> inAccountList);

    List<CustomerServiceInAccount> selectInAccountStatisticByYearAndUserDept(@Param("year") Integer year, @Param("statisticTaxType") Integer statisticTaxType,
                                                                             @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds);

    List<PeriodCountDTO> selectInAccountInTimePeriodCountByYearAndUserDept(@Param("year") Integer year, @Param("statisticTaxType") Integer statisticTaxType,
                                                                           @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("flag") Integer flag);

    List<PeriodCountDTO> selectInAccountEndTimePeriodCountByYearAndUserDept(@Param("year") Integer year, @Param("statisticTaxType") Integer statisticTaxType,
                                                                            @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds, @Param("flag") Integer flag);

    void saveNewPeriodInAccount(@Param("period") Integer period);

    void saveNewPeriodInAccountByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                    @Param("periodStart") Integer periodStart,
                                                    @Param("periodEnd") Integer periodEnd);

    @Select("select * from c_customer_service_in_account where customer_service_period_month_id = #{customerServicePeriodMonthId}")
    @ResultType(CustomerServiceInAccount.class)
    CustomerServiceInAccount selectByPeriod(@Param("customerServicePeriodMonthId") Long customerServicePeriodMonthId);

    List<CustomerInAccountMaxPeriodDTO> selectCustomerInAccountMaxPeriod(@Param("customerServiceIds") List<Long> customerServiceIds);

    List<Long> selectNoInTimePeriodIds(@Param("customerServiceId") Long customerServiceId);
}
