package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.dto.download.DownloadRecordDTO;
import com.bxm.customer.service.IDownloadRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/download")
@Api(tags = "导出下载相关")
public class DownloadController {

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @GetMapping("/downloadRecordList")
    @ApiOperation("下载记录")
    public Result<IPage<DownloadRecordDTO>> downloadRecordList(@RequestParam("pageNum") Integer pageNum,
                                                               @RequestParam("pageSize") Integer pageSize) {
        return Result.ok(downloadRecordService.downloadRecordList(pageNum, pageSize));
    }

    @GetMapping("/getDownloadUrl")
    @ApiOperation("获取下载地址")
    public Result<String> getDownloadUrl(@RequestParam("id") @ApiParam("下载记录id") Long id) {
        return Result.ok(downloadRecordService.getDownloadUlr(id));
    }

    @PostMapping("/deleteDownloadRecord")
    @ApiOperation("删除下载记录,单个删除，传id")
    public Result deleteDownloadRecord(@RequestBody CommonIdVO vo) {
        downloadRecordService.deleteDownloadRecord(vo);
        return Result.ok();
    }

    @PostMapping("/retryDownloadRecord")
    @ApiOperation("重试下载记录,单个重试，传id")
    public Result retryDownloadRecord(@RequestBody CommonIdVO vo) {
        downloadRecordService.retryDownloadRecord(vo);
        return Result.ok();
    }

    @PostMapping("/stopDownloadRecord")
    @ApiOperation("终止下载记录,单个终止，传id")
    public Result stopDownloadRecord(@RequestBody CommonIdVO vo) {
        downloadRecordService.stopDownloadRecord(vo);
        return Result.ok();
    }
}
