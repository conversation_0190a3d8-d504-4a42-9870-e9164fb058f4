package com.bxm.customer.service.impl;

import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.TaxDisk;
import com.bxm.common.core.enums.TaxMethod;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.dto.CustomerServiceFinanceTaxInfoDTO;
import com.bxm.customer.domain.dto.CustomerServiceTaxTypeCheckDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceFinanceTaxInfoMapper;
import com.bxm.customer.domain.CustomerServiceFinanceTaxInfo;
import com.bxm.customer.service.ICustomerServiceFinanceTaxInfoService;

/**
 * 客户服务财税信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceFinanceTaxInfoServiceImpl extends ServiceImpl<CustomerServiceFinanceTaxInfoMapper, CustomerServiceFinanceTaxInfo> implements ICustomerServiceFinanceTaxInfoService
{
    @Autowired
    private CustomerServiceFinanceTaxInfoMapper customerServiceFinanceTaxInfoMapper;

    /**
     * 查询客户服务财税信息
     * 
     * @param id 客户服务财税信息主键
     * @return 客户服务财税信息
     */
    @Override
    public CustomerServiceFinanceTaxInfo selectCustomerServiceFinanceTaxInfoById(Long id)
    {
        return customerServiceFinanceTaxInfoMapper.selectCustomerServiceFinanceTaxInfoById(id);
    }

    /**
     * 查询客户服务财税信息列表
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 客户服务财税信息
     */
    @Override
    public List<CustomerServiceFinanceTaxInfo> selectCustomerServiceFinanceTaxInfoList(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo)
    {
        return customerServiceFinanceTaxInfoMapper.selectCustomerServiceFinanceTaxInfoList(customerServiceFinanceTaxInfo);
    }

    /**
     * 新增客户服务财税信息
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceFinanceTaxInfo(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo)
    {
        return customerServiceFinanceTaxInfoMapper.insertCustomerServiceFinanceTaxInfo(customerServiceFinanceTaxInfo);
    }

    /**
     * 修改客户服务财税信息
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceFinanceTaxInfo(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo)
    {
        return customerServiceFinanceTaxInfoMapper.updateCustomerServiceFinanceTaxInfo(customerServiceFinanceTaxInfo);
    }

    /**
     * 批量删除客户服务财税信息
     * 
     * @param ids 需要删除的客户服务财税信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceFinanceTaxInfoByIds(Long[] ids)
    {
        return customerServiceFinanceTaxInfoMapper.deleteCustomerServiceFinanceTaxInfoByIds(ids);
    }

    /**
     * 删除客户服务财税信息信息
     * 
     * @param id 客户服务财税信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceFinanceTaxInfoById(Long id)
    {
        return customerServiceFinanceTaxInfoMapper.deleteCustomerServiceFinanceTaxInfoById(id);
    }

    @Override
    public CustomerServiceFinanceTaxInfo selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<CustomerServiceFinanceTaxInfo>().eq(CustomerServiceFinanceTaxInfo::getCustomerServiceId, customerServiceId), false);
    }

    @Override
    public String editCustomerServiceFinanceTaxInfo(CustomerServiceTaxTypeCheckDTO dto) {
        CustomerServiceFinanceTaxInfo oldTaxInfo = selectByCustomerServiceId(dto.getCustomerServiceId());
        if (Objects.isNull(oldTaxInfo)) {
            CustomerServiceFinanceTaxInfo taxInfo = new CustomerServiceFinanceTaxInfo();
            BeanUtils.copyProperties(dto.getTaxInfo(), taxInfo);
            save(taxInfo);
        } else {
            CustomerServiceFinanceTaxInfo update = new CustomerServiceFinanceTaxInfo();
            BeanUtils.copyProperties(dto.getTaxInfo(), update);
            update.setId(oldTaxInfo.getId());
            updateById(update);
        }
        return getOperContent(oldTaxInfo, dto.getTaxInfo());
    }

    private String getOperContent(CustomerServiceFinanceTaxInfo oldTaxInfo, CustomerServiceFinanceTaxInfoDTO dto) {
        StringBuilder operContent = new StringBuilder();
        if (Objects.isNull(oldTaxInfo) || !Objects.equals(dto.getTaxDisk(), oldTaxInfo.getTaxDisk())) {
            operContent.append(String.format("%s为%s，", "税盘", TaxDisk.getByCode(dto.getTaxDisk()).getName()));
        }
        if (Objects.isNull(oldTaxInfo) || !Objects.equals(dto.getEzTaxAccount(), oldTaxInfo.getEzTaxAccount())) {
            operContent.append(String.format("%s为%s，", "易捷账个税账号", Objects.isNull(dto.getEzTaxAccount()) ? "-" : (dto.getEzTaxAccount() ? "已维护" : "未维护")));
        }
        if (Objects.isNull(oldTaxInfo) || !Objects.equals(dto.getTaxMethod(), oldTaxInfo.getTaxMethod())) {
            operContent.append(String.format("%s为%s，", "个税申报方式", TaxMethod.getByCode(dto.getTaxMethod()).getName()));
        }
        if (Objects.isNull(oldTaxInfo) || !Objects.equals(dto.getTaxPassword(), oldTaxInfo.getTaxPassword())) {
            operContent.append(String.format("%s为%s，", "税盘密码", StringUtils.isEmpty(dto.getTaxPassword()) ? "-" : dto.getTaxPassword()));
        }
        return operContent.toString();
    }
}
