package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CTag;
import com.bxm.customer.mapper.CTagMapper;
import com.bxm.customer.service.ICTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class CTagServiceImpl extends ServiceImpl<CTagMapper, CTag> implements ICTagService
{
    @Autowired
    private CTagMapper cTagMapper;

    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    @Override
    public CTag selectCTagById(Long id)
    {
        return cTagMapper.selectCTagById(id);
    }

    /**
     * 查询标签列表
     * 
     * @param cTag 标签
     * @return 标签
     */
    @Override
    public List<CTag> selectCTagList(CTag cTag)
    {
        return cTagMapper.selectCTagList(cTag);
    }

    /**
     * 新增标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    @Override
    public int insertCTag(CTag cTag)
    {
        cTag.setCreateTime(DateUtils.getNowDate());
        return cTagMapper.insertCTag(cTag);
    }

    /**
     * 修改标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    @Override
    public int updateCTag(CTag cTag)
    {
        cTag.setUpdateTime(DateUtils.getNowDate());
        return cTagMapper.updateCTag(cTag);
    }

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的标签主键
     * @return 结果
     */
    @Override
    public int deleteCTagByIds(Long[] ids)
    {
        return cTagMapper.deleteCTagByIds(ids);
    }

    /**
     * 删除标签信息
     * 
     * @param id 标签主键
     * @return 结果
     */
    @Override
    public int deleteCTagById(Long id)
    {
        return cTagMapper.deleteCTagById(id);
    }

    @Override
    public CTag selectByTagTypeAndTagName(Integer tagType, String tagName) {
        return getOne(new LambdaQueryWrapper<CTag>().eq(CTag::getTagName, tagName)
                .eq(CTag::getTagType, tagType).last("limit 1"));
    }
}
