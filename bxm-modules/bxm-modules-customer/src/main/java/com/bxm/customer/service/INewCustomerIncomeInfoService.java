package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerIncomeInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferMonthIncomeDTO;

import java.util.List;
import java.util.Map;

/**
 * 新户流转收入信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerIncomeInfoService extends IService<NewCustomerIncomeInfo>
{
    /**
     * 查询新户流转收入信息
     * 
     * @param id 新户流转收入信息主键
     * @return 新户流转收入信息
     */
    public NewCustomerIncomeInfo selectNewCustomerIncomeInfoById(Long id);

    /**
     * 查询新户流转收入信息列表
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 新户流转收入信息集合
     */
    public List<NewCustomerIncomeInfo> selectNewCustomerIncomeInfoList(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 新增新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    public int insertNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 修改新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    public int updateNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 批量删除新户流转收入信息
     * 
     * @param ids 需要删除的新户流转收入信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerIncomeInfoByIds(Long[] ids);

    /**
     * 删除新户流转收入信息信息
     * 
     * @param id 新户流转收入信息主键
     * @return 结果
     */
    public int deleteNewCustomerIncomeInfoById(Long id);

    List<NewCustomerIncomeInfo> selectByCustomerId(Long customerId);

    Integer countByCustomerId(Long newCustomerId);

    void removeAndSaveNew(Long newCustomerId, List<NewCustomerTransferMonthIncomeDTO> incomeList);

    Map<Long, List<NewCustomerIncomeInfo>> selectMapByCustomerIds(List<Long> newCustomerTransferIds);

    void createDefaultIncomeList(NewCustomerInfo newCustomerInfo);
}
