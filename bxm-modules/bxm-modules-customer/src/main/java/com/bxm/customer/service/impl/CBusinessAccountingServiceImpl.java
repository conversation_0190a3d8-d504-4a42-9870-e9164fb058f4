package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.AccountingBusinessType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CBusinessAccounting;
import com.bxm.customer.mapper.CBusinessAccountingMapper;
import com.bxm.customer.service.ICBusinessAccountingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 会计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Service
public class CBusinessAccountingServiceImpl extends ServiceImpl<CBusinessAccountingMapper, CBusinessAccounting> implements ICBusinessAccountingService
{
    @Autowired
    private CBusinessAccountingMapper cBusinessAccountingMapper;

    /**
     * 查询会计
     * 
     * @param id 会计主键
     * @return 会计
     */
    @Override
    public CBusinessAccounting selectCBusinessAccountingById(Long id)
    {
        return cBusinessAccountingMapper.selectCBusinessAccountingById(id);
    }

    /**
     * 查询会计列表
     * 
     * @param cBusinessAccounting 会计
     * @return 会计
     */
    @Override
    public List<CBusinessAccounting> selectCBusinessAccountingList(CBusinessAccounting cBusinessAccounting)
    {
        return cBusinessAccountingMapper.selectCBusinessAccountingList(cBusinessAccounting);
    }

    /**
     * 新增会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    @Override
    public int insertCBusinessAccounting(CBusinessAccounting cBusinessAccounting)
    {
        cBusinessAccounting.setCreateTime(DateUtils.getNowDate());
        return cBusinessAccountingMapper.insertCBusinessAccounting(cBusinessAccounting);
    }

    /**
     * 修改会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    @Override
    public int updateCBusinessAccounting(CBusinessAccounting cBusinessAccounting)
    {
        cBusinessAccounting.setUpdateTime(DateUtils.getNowDate());
        return cBusinessAccountingMapper.updateCBusinessAccounting(cBusinessAccounting);
    }

    /**
     * 批量删除会计
     * 
     * @param ids 需要删除的会计主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessAccountingByIds(Long[] ids)
    {
        return cBusinessAccountingMapper.deleteCBusinessAccountingByIds(ids);
    }

    /**
     * 删除会计信息
     * 
     * @param id 会计主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessAccountingById(Long id)
    {
        return cBusinessAccountingMapper.deleteCBusinessAccountingById(id);
    }

    @Override
    public Long getAccountingByBusinessIdAndBusinessType(Long businessId, AccountingBusinessType accountingBusinessType) {
        if (Objects.isNull(businessId)) {
            return null;
        }
        CBusinessAccounting accounting = getOne(new LambdaQueryWrapper<CBusinessAccounting>()
                .eq(CBusinessAccounting::getBusinessId, businessId).eq(CBusinessAccounting::getBusinessType, accountingBusinessType.getCode())
                .last("limit 1"));
        return Objects.isNull(accounting) ? null : accounting.getEmployeeId();
    }
}
