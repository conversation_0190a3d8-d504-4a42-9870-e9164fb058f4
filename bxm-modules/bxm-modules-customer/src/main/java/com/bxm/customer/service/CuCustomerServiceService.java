package com.bxm.customer.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CuCustomerServiceService extends ServiceImpl<CCustomerServiceMapper, CCustomerService> {

    // 通过信用代码检查是否存在该客户
    public boolean checkCustomerExistsByCreditCode(String creditCode) {
        QueryWrapper<CCustomerService> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("credit_code", creditCode)
                .eq("is_del", 0);  // 确保查询的记录未被删除

        // 使用 count 查询符合条件的记录数，如果大于 0，则表示记录存在
        return count(queryWrapper) > 0;
    }


    // 通过信用代码获取客户记录
    public CCustomerService getCustomerByCreditCode(String creditCode) {
        QueryWrapper<CCustomerService> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("credit_code", creditCode)
                .eq("is_del", 0);  // 确保查询的记录未被删除

        // 使用 getOne 查询符合条件的唯一记录，如果找到返回，否则返回 null
        return getOne(queryWrapper);
    }

    // 通过信用代码获取客户记录列表
    public List<CCustomerService> getCustomersByCreditCode(String creditCode) {
        QueryWrapper<CCustomerService> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("credit_code", creditCode)
                .eq("is_del", 0);  // 确保查询的记录未被删除

        // 使用 list 查询所有符合条件的记录，返回一个列表
        return list(queryWrapper);
    }

}
