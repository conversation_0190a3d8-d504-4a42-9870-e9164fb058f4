package com.bxm.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceOperate;
import com.bxm.customer.service.ICustomerServiceOperateService;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 客户服务操作记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@ApiIgnore
@RestController
@RequestMapping("/operate")
@Api(tags = "客户服务操作记录")
public class CustomerServiceOperateController extends BaseController
{
    @Autowired
    private ICustomerServiceOperateService customerServiceOperateService;

    /**
     * 查询客户服务操作记录列表
     */
    @RequiresPermissions("customer:operate:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户服务操作记录列表", notes = "查询客户服务操作记录列表")
    public TableDataInfo list(CustomerServiceOperate customerServiceOperate)
    {
        startPage();
        List<CustomerServiceOperate> list = customerServiceOperateService.selectCustomerServiceOperateList(customerServiceOperate);
        return getDataTable(list);
    }

    /**
     * 导出客户服务操作记录列表
     */
    @RequiresPermissions("customer:operate:export")
    @Log(title = "客户服务操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出客户服务操作记录列表", notes = "导出客户服务操作记录列表")
    public void export(HttpServletResponse response, CustomerServiceOperate customerServiceOperate)
    {
        List<CustomerServiceOperate> list = customerServiceOperateService.selectCustomerServiceOperateList(customerServiceOperate);
        ExcelUtil<CustomerServiceOperate> util = new ExcelUtil<CustomerServiceOperate>(CustomerServiceOperate.class);
        util.exportExcel(response, list, "客户服务操作记录数据");
    }

    /**
     * 获取客户服务操作记录详细信息
     */
    @RequiresPermissions("customer:operate:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取客户服务操作记录详细信息", notes = "获取客户服务操作记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customerServiceOperateService.selectCustomerServiceOperateById(id));
    }

    /**
     * 新增客户服务操作记录
     */
    @RequiresPermissions("customer:operate:add")
    @Log(title = "客户服务操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增客户服务操作记录", notes = "新增客户服务操作记录")
    public AjaxResult add(@RequestBody CustomerServiceOperate customerServiceOperate)
    {
        return toAjax(customerServiceOperateService.insertCustomerServiceOperate(customerServiceOperate));
    }

    /**
     * 修改客户服务操作记录
     */
    @RequiresPermissions("customer:operate:edit")
    @Log(title = "客户服务操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改客户服务操作记录", notes = "修改客户服务操作记录")
    public AjaxResult edit(@RequestBody CustomerServiceOperate customerServiceOperate)
    {
        return toAjax(customerServiceOperateService.updateCustomerServiceOperate(customerServiceOperate));
    }

    /**
     * 删除客户服务操作记录
     */
    @RequiresPermissions("customer:operate:remove")
    @Log(title = "客户服务操作记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除客户服务操作记录", notes = "删除客户服务操作记录")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(customerServiceOperateService.deleteCustomerServiceOperateByIds(ids));
    }
}
