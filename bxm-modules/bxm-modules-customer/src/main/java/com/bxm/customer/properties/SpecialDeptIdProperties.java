package com.bxm.customer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "special.dept")
@Getter
@Setter
public class SpecialDeptIdProperties
{
    private Long zdz;

    private Long clz;

    private Long cpyy;

    private Long hdzx;

    private Long fjq;

    private Long fjeq;

    private Long kfz;

    private <PERSON> jd;

    private Long fd;

    private Long yt;

    private Long ytjt;

    private Long xm;
}
