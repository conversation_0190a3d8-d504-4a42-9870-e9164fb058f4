package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.newCustomerTransfer.*;
import com.bxm.customer.mapper.NewCustomerAnnualReportInfoMapper;
import com.bxm.customer.service.INewCustomerAnnualReportInfoService;
import com.bxm.customer.service.INewCustomerFixedAssetsInfoService;
import com.bxm.customer.service.INewCustomerIncomeInfoService;
import com.bxm.customer.service.INewCustomerOtherInfoService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 新户流转工商年报信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerAnnualReportInfoServiceImpl extends ServiceImpl<NewCustomerAnnualReportInfoMapper, NewCustomerAnnualReportInfo> implements INewCustomerAnnualReportInfoService
{
    @Autowired
    private NewCustomerAnnualReportInfoMapper newCustomerAnnualReportInfoMapper;

    @Autowired
    private INewCustomerIncomeInfoService newCustomerIncomeInfoService;

    @Autowired
    private INewCustomerOtherInfoService newCustomerOtherInfoService;

    @Autowired
    private INewCustomerFixedAssetsInfoService newCustomerFixedAssetsInfoService;

    /**
     * 查询新户流转工商年报信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 新户流转工商年报信息
     */
    @Override
    public NewCustomerAnnualReportInfo selectNewCustomerAnnualReportInfoById(Long id)
    {
        return newCustomerAnnualReportInfoMapper.selectNewCustomerAnnualReportInfoById(id);
    }

    /**
     * 查询新户流转工商年报信息列表
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 新户流转工商年报信息
     */
    @Override
    public List<NewCustomerAnnualReportInfo> selectNewCustomerAnnualReportInfoList(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo)
    {
        return newCustomerAnnualReportInfoMapper.selectNewCustomerAnnualReportInfoList(newCustomerAnnualReportInfo);
    }

    /**
     * 新增新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo)
    {
        return newCustomerAnnualReportInfoMapper.insertNewCustomerAnnualReportInfo(newCustomerAnnualReportInfo);
    }

    /**
     * 修改新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo)
    {
        return newCustomerAnnualReportInfoMapper.updateNewCustomerAnnualReportInfo(newCustomerAnnualReportInfo);
    }

    /**
     * 批量删除新户流转工商年报信息
     * 
     * @param ids 需要删除的新户流转工商年报信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerAnnualReportInfoByIds(Long[] ids)
    {
        return newCustomerAnnualReportInfoMapper.deleteNewCustomerAnnualReportInfoByIds(ids);
    }

    /**
     * 删除新户流转工商年报信息信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerAnnualReportInfoById(Long id)
    {
        return newCustomerAnnualReportInfoMapper.deleteNewCustomerAnnualReportInfoById(id);
    }

    @Override
    public NewCustomerTransferOtherInfoDTO buildOtherInfo(NewCustomerInfo newCustomerInfo) {
        LocalDate startDateTemp = LocalDate.now().minusMonths(12);
        LocalDate registerDateTemp = DateUtils.strToLocalDate(newCustomerInfo.getRegistrationDate(), "yyyy-MM-dd");
        LocalDate startDate = LocalDate.of(startDateTemp.getYear(), startDateTemp.getMonth(), 1);
        LocalDate registerDate = LocalDate.of(registerDateTemp.getYear(), registerDateTemp.getMonth(), 1);
        List<Integer> yearMonthList = getThis12YearMonthList(!registerDate.isBefore(startDate) ? registerDate : startDate);
        Map<Integer, NewCustomerIncomeInfo> incomeMap = newCustomerIncomeInfoService.selectByCustomerId(newCustomerInfo.getId())
                .stream().collect(Collectors.toMap(NewCustomerIncomeInfo::getMonth, Function.identity()));
        NewCustomerOtherInfo newCustomerOtherInfo = newCustomerOtherInfoService.selectByCustomerId(newCustomerInfo.getId());
        List<NewCustomerFixedAssetsInfo> assetsInfos = newCustomerFixedAssetsInfoService.selectByCustomerId(newCustomerInfo.getId());
        NewCustomerAnnualReportInfo newCustomerAnnualReportInfo = selectByCustomerId(newCustomerInfo.getId());
        return NewCustomerTransferOtherInfoDTO.builder()
                .newCustomerTransferId(newCustomerInfo.getId())
                .incomeList(yearMonthList.stream().map(yearMonth -> {
                    NewCustomerIncomeInfo newCustomerIncomeInfo = incomeMap.get(yearMonth);
                    return NewCustomerTransferMonthIncomeDTO.builder()
                            .yearMonth(yearMonth)
                            .month(yearMonth % 100 + "月")
                            .amount(Objects.isNull(newCustomerIncomeInfo) ? null : newCustomerIncomeInfo.getAmount())
                            .build();
                }).collect(Collectors.toList()))
                .settlementPaymentInfo(NewCustomerTransferSettlementPaymentInfoDTO.builder()
                        .taxSubmissionStatus(Objects.isNull(newCustomerOtherInfo) ? null : newCustomerOtherInfo.getTaxSubmissionStatus())
                        .nextYearSupplementAmount(Objects.isNull(newCustomerOtherInfo) ? null : newCustomerOtherInfo.getNextYearSupplement())
                        .preTaxProfit(Objects.isNull(newCustomerOtherInfo) ? null : newCustomerOtherInfo.getPreTaxProfit())
                        .notes(Objects.isNull(newCustomerOtherInfo) ? null : newCustomerOtherInfo.getNotes())
                        .fixedAssetsList(assetsInfos.stream().map(row -> NewCustomerTransferFixedAssetsInfoDTO.builder()
                                .assetName(row.getAssetName())
                                .occurrenceYear(row.getOccurrenceYear())
                                .build()).collect(Collectors.toList()))
                        .build())
                .annualReportInfo(NewCustomerTransferAnnualReportInfoDTO.builder()
                        .status(Objects.isNull(newCustomerAnnualReportInfo) ? null : newCustomerAnnualReportInfo.getStatus())
                        .eBusinessLicense(Objects.isNull(newCustomerAnnualReportInfo) || Objects.isNull(newCustomerAnnualReportInfo.getEBusinessLicense()) ? null : (newCustomerAnnualReportInfo.getEBusinessLicense() ? 1 : 0))
                        .contactName(Objects.isNull(newCustomerAnnualReportInfo) ? null : newCustomerAnnualReportInfo.getContactName())
                        .contactIdNumber(Objects.isNull(newCustomerAnnualReportInfo) ? null : newCustomerAnnualReportInfo.getContactIdNumber())
                        .contactMobile(Objects.isNull(newCustomerAnnualReportInfo) ? null : newCustomerAnnualReportInfo.getContactMobile())
                        .notes(Objects.isNull(newCustomerAnnualReportInfo) ? null : newCustomerAnnualReportInfo.getNotes())
                        .build())
                .build();
    }

    @Override
    public NewCustomerAnnualReportInfo selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<NewCustomerAnnualReportInfo>()
                .eq(NewCustomerAnnualReportInfo::getCustomerId, customerId), false);
    }

    @Override
    public Map<Long, NewCustomerAnnualReportInfo> selectMapByCustomerIds(List<Long> customerIds) {
        if (ObjectUtils.isEmpty(customerIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerAnnualReportInfo>().in(NewCustomerAnnualReportInfo::getCustomerId, customerIds))
                .stream().collect(Collectors.toMap(NewCustomerAnnualReportInfo::getCustomerId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    @Transactional
    public void updateByOtherInfo(NewCustomerTransferOtherInfoDTO otherInfo) {
        newCustomerIncomeInfoService.removeAndSaveNew(otherInfo.getNewCustomerTransferId(), otherInfo.getIncomeList());
        newCustomerOtherInfoService.updateByOtherInfo(otherInfo.getNewCustomerTransferId(), otherInfo.getSettlementPaymentInfo());
        newCustomerFixedAssetsInfoService.removeAndSaveNew(otherInfo.getNewCustomerTransferId(), Objects.isNull(otherInfo.getSettlementPaymentInfo()) ? Lists.newArrayList() : otherInfo.getSettlementPaymentInfo().getFixedAssetsList());
        NewCustomerAnnualReportInfo oldAnnualReportInfo = selectByCustomerId(otherInfo.getNewCustomerTransferId());
        if (Objects.isNull(oldAnnualReportInfo)) {
            if (!Objects.isNull(otherInfo.getAnnualReportInfo())) {
                NewCustomerAnnualReportInfo annualReportInfo = new NewCustomerAnnualReportInfo();
                BeanUtils.copyProperties(otherInfo.getAnnualReportInfo(), annualReportInfo);
                annualReportInfo.setEBusinessLicense(Objects.isNull(otherInfo.getAnnualReportInfo().getEBusinessLicense()) ? null : otherInfo.getAnnualReportInfo().getEBusinessLicense() == 1);
                annualReportInfo.setCustomerId(otherInfo.getNewCustomerTransferId());
                save(annualReportInfo);
            }
        } else {
            update(new LambdaUpdateWrapper<NewCustomerAnnualReportInfo>()
                    .eq(NewCustomerAnnualReportInfo::getId, oldAnnualReportInfo.getId())
                    .set(NewCustomerAnnualReportInfo::getStatus, Objects.isNull(otherInfo.getAnnualReportInfo()) ? null : otherInfo.getAnnualReportInfo().getStatus())
                    .set(NewCustomerAnnualReportInfo::getEBusinessLicense, Objects.isNull(otherInfo.getAnnualReportInfo()) || Objects.isNull(otherInfo.getAnnualReportInfo().getEBusinessLicense()) ? null : otherInfo.getAnnualReportInfo().getEBusinessLicense() == 1)
                    .set(NewCustomerAnnualReportInfo::getContactName, Objects.isNull(otherInfo.getAnnualReportInfo()) ? null : otherInfo.getAnnualReportInfo().getContactName())
                    .set(NewCustomerAnnualReportInfo::getContactMobile, Objects.isNull(otherInfo.getAnnualReportInfo()) ? null : otherInfo.getAnnualReportInfo().getContactMobile())
                    .set(NewCustomerAnnualReportInfo::getContactIdNumber, Objects.isNull(otherInfo.getAnnualReportInfo()) ? null : otherInfo.getAnnualReportInfo().getContactIdNumber())
                    .set(NewCustomerAnnualReportInfo::getNotes, Objects.isNull(otherInfo.getAnnualReportInfo()) ? null : otherInfo.getAnnualReportInfo().getNotes()));
        }
    }

    private List<Integer> getThis12YearMonthList(LocalDate startDate) {
        List<Integer> yearMonthList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate endDate = LocalDate.of(now.getYear(), now.getMonthValue(), 1);
        while (!startDate.isAfter(endDate)) {
            yearMonthList.add(startDate.getYear() * 100 + startDate.getMonthValue());
            startDate = startDate.plusMonths(1);
        }
        return yearMonthList;
    }
}
