package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck;
import com.bxm.customer.domain.CustomerTaxTypeCheck;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import com.bxm.customer.mapper.CustomerServicePeriodMonthTaxTypeCheckMapper;
import com.bxm.customer.service.ICustomerServicePeriodMonthTaxTypeCheckService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务月账期税种核定Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
@Slf4j
public class CustomerServicePeriodMonthTaxTypeCheckServiceImpl extends ServiceImpl<CustomerServicePeriodMonthTaxTypeCheckMapper, CustomerServicePeriodMonthTaxTypeCheck> implements ICustomerServicePeriodMonthTaxTypeCheckService
{
    private static final Map<String, Integer> REPORT_TYPE_MAP;

    static {
        Map<String, Integer> map = new HashMap<>();
        map.put("月", 1);
        map.put("季", 2);
        map.put("年", 3);
        map.put("次", 4);
        map.put("半年", 5);
        REPORT_TYPE_MAP = Collections.unmodifiableMap(map);
    }

    @Autowired
    private CustomerServicePeriodMonthTaxTypeCheckMapper customerServicePeriodMonthTaxTypeCheckMapper;

    /**
     * 查询服务月账期税种核定
     * 
     * @param id 服务月账期税种核定主键
     * @return 服务月账期税种核定
     */
    @Override
    public CustomerServicePeriodMonthTaxTypeCheck selectCustomerServicePeriodMonthTaxTypeCheckById(Long id)
    {
        return customerServicePeriodMonthTaxTypeCheckMapper.selectCustomerServicePeriodMonthTaxTypeCheckById(id);
    }

    /**
     * 查询服务月账期税种核定列表
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 服务月账期税种核定
     */
    @Override
    public List<CustomerServicePeriodMonthTaxTypeCheck> selectCustomerServicePeriodMonthTaxTypeCheckList(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck)
    {
        return customerServicePeriodMonthTaxTypeCheckMapper.selectCustomerServicePeriodMonthTaxTypeCheckList(customerServicePeriodMonthTaxTypeCheck);
    }

    /**
     * 新增服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    @Override
    public int insertCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck)
    {
        customerServicePeriodMonthTaxTypeCheck.setCreateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthTaxTypeCheckMapper.insertCustomerServicePeriodMonthTaxTypeCheck(customerServicePeriodMonthTaxTypeCheck);
    }

    /**
     * 修改服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    @Override
    public int updateCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck)
    {
        customerServicePeriodMonthTaxTypeCheck.setUpdateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthTaxTypeCheckMapper.updateCustomerServicePeriodMonthTaxTypeCheck(customerServicePeriodMonthTaxTypeCheck);
    }

    /**
     * 批量删除服务月账期税种核定
     * 
     * @param ids 需要删除的服务月账期税种核定主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthTaxTypeCheckByIds(Long[] ids)
    {
        return customerServicePeriodMonthTaxTypeCheckMapper.deleteCustomerServicePeriodMonthTaxTypeCheckByIds(ids);
    }

    /**
     * 删除服务月账期税种核定信息
     * 
     * @param id 服务月账期税种核定主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthTaxTypeCheckById(Long id)
    {
        return customerServicePeriodMonthTaxTypeCheckMapper.deleteCustomerServicePeriodMonthTaxTypeCheckById(id);
    }

    @Override
    @Transactional
    public void saveByCheckVOList(Long customerServicePeriodMonthId, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList) {
        if (Objects.isNull(customerServicePeriodMonthId) || ObjectUtils.isEmpty(taxTypeCheckList)) {
            return;
        }
        saveBatch(taxTypeCheckList.stream().map(row -> new CustomerServicePeriodMonthTaxTypeCheck().setCustomerServicePeriodMonthId(customerServicePeriodMonthId)
                .setTaxType(row.getTaxTypeName()).setReportType(row.getReportType())).collect(Collectors.toList()));
    }

    @Override
    public void deleteByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return;
        }
        remove(new LambdaQueryWrapper<CustomerServicePeriodMonthTaxTypeCheck>()
                .eq(CustomerServicePeriodMonthTaxTypeCheck::getCustomerServicePeriodMonthId, customerServicePeriodMonthId));
    }

    @Override
    public List<CustomerServiceTaxTypeCheckVO> selectTaxTypeCheckByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return Collections.emptyList();
        }
        List<CustomerServicePeriodMonthTaxTypeCheck> periodMonthTaxTypeChecks = selectByCustomerServicePeriodMonthId(customerServicePeriodMonthId);
        if (ObjectUtils.isEmpty(periodMonthTaxTypeChecks)) {
            return Collections.emptyList();
        }
        return periodMonthTaxTypeChecks.stream().map(row -> CustomerServiceTaxTypeCheckVO.builder()
                .id(row.getId())
                .taxTypeName(row.getTaxType())
                .reportType(row.getReportType())
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<CustomerServicePeriodMonthTaxTypeCheck> selectByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodMonthTaxTypeCheck>()
                .eq(CustomerServicePeriodMonthTaxTypeCheck::getCustomerServicePeriodMonthId, customerServicePeriodMonthId));
    }

    @Override
    public void saveNewPeriodTaxCheck(Integer nowPeriod) {
        baseMapper.saveNewPeriodTaxCheckByCustomer(nowPeriod);
        baseMapper.saveNewPeriodTaxCheckBySmallCustomer(nowPeriod);
        baseMapper.saveNewPeriodTaxCheckByCommlyCustomer(nowPeriod);
    }

    @Override
    public void saveNewPeriodTaxCheckByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd) {
        baseMapper.saveNewPeriodTaxCheckByByCustomerServiceIds(customerServiceIds, periodStart, periodEnd);
        baseMapper.saveNewPeriodTaxCheckBySmallCustomerAndCustomerServiceId(customerServiceIds, periodStart, periodEnd);
        baseMapper.saveNewPeriodTaxCheckByCommlyCustomerAndCustomerServiceId(customerServiceIds, periodStart, periodEnd);
    }

    @Override
    @Transactional
    public void removeAndCreateByXqy(List<Long> periodIds, List<OpenApiSyncItem> itemList) {
        if (ObjectUtils.isEmpty(periodIds)) {
            return;
        }
        if (!ObjectUtils.isEmpty(itemList)) {
            Map<Integer, List<String>> map = new HashMap<>();
            List<CustomerServicePeriodMonthTaxTypeCheck> newList = Lists.newArrayList();
            LocalDateTime now = LocalDateTime.now();
            for (OpenApiSyncItem row : itemList) {
                if (!StringUtils.isEmpty(row.getTaxPeriodEnd())) {
                    try {
                        LocalDateTime end = LocalDateTime.parse(row.getTaxPeriodEnd(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        if (end.isBefore(now)) {
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("时间格式转换异常,:{}", row.getTaxPeriodEnd());
                    }
                }
                String taxType;
                if (Objects.equals(row.getItemCategoryName(), "增值税")) {
                    taxType = "增值税";
                } else if (Objects.equals(row.getItemCategoryName(), "个人所得税")) {
                    if (!StringUtils.isEmpty(row.getItemName())) {
                        if (row.getItemName().contains("工资薪金")) {
                            taxType = "个人所得税";
                        } else {
                            taxType = "个人所得税-" + row.getItemName();
                        }
                    } else {
                        taxType = "个人所得税";
                    }
                } else {
                    if (StringUtils.isEmpty(row.getItemName())) {
                        taxType = row.getItemCategoryName();
                    } else {
                        taxType = row.getItemCategoryName() + "-" + row.getItemName();
                    }
                }
                if (!Objects.equals("社保", taxType)) {
                    Integer reportType = REPORT_TYPE_MAP.getOrDefault(row.getReportType(), 1);
                    if (!map.containsKey(reportType)) {
                        map.put(reportType, Lists.newArrayList(taxType));
                        newList.add(new CustomerServicePeriodMonthTaxTypeCheck()
                                .setTaxType(taxType)
                                .setReportType(reportType));
                    } else {
                        List<String> taxTypes = map.get(reportType);
                        if (!taxTypes.contains(taxType)) {
                            taxTypes.add(taxType);
                            map.put(reportType, taxTypes);
                            newList.add(new CustomerServicePeriodMonthTaxTypeCheck()
                                    .setTaxType(taxType)
                                    .setReportType(reportType));
                        }
                    }
                }
            }
            if (!ObjectUtils.isEmpty(newList)) {
                remove(new LambdaQueryWrapper<CustomerServicePeriodMonthTaxTypeCheck>()
                        .in(CustomerServicePeriodMonthTaxTypeCheck::getCustomerServicePeriodMonthId, periodIds));
                List<CustomerServicePeriodMonthTaxTypeCheck> lastList = Lists.newArrayList();
                for (Long periodId : periodIds) {
                    for (CustomerServicePeriodMonthTaxTypeCheck row : newList) {
                        CustomerServicePeriodMonthTaxTypeCheck check = new CustomerServicePeriodMonthTaxTypeCheck();
                        BeanUtils.copyProperties(row, check);
                        check.setCustomerServicePeriodMonthId(periodId);
                        lastList.add(check);
                    }
                }
                saveBatch(lastList);
            }
        }
    }

    @Override
    @Transactional
    public void deleteAndSaveNewPeriodTaxCheckByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd, List<Long> customerServicePeriodMonthIds) {
        if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            remove(new LambdaQueryWrapper<CustomerServicePeriodMonthTaxTypeCheck>()
                    .in(CustomerServicePeriodMonthTaxTypeCheck::getCustomerServicePeriodMonthId, customerServicePeriodMonthIds));
        }
        if (!ObjectUtils.isEmpty(customerServiceIds)) {
            saveNewPeriodTaxCheckByCustomerServiceIds(customerServiceIds, periodStart, periodEnd);
        }
    }
}
