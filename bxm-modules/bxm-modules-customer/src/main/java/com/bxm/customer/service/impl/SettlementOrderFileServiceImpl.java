package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.SettlementOrderFileMapper;
import com.bxm.customer.domain.SettlementOrderFile;
import com.bxm.customer.service.ISettlementOrderFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 结算单附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
public class SettlementOrderFileServiceImpl extends ServiceImpl<SettlementOrderFileMapper, SettlementOrderFile> implements ISettlementOrderFileService
{
    @Autowired
    private SettlementOrderFileMapper settlementOrderFileMapper;

    /**
     * 查询结算单附件
     * 
     * @param id 结算单附件主键
     * @return 结算单附件
     */
    @Override
    public SettlementOrderFile selectSettlementOrderFileById(Long id)
    {
        return settlementOrderFileMapper.selectSettlementOrderFileById(id);
    }

    /**
     * 查询结算单附件列表
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结算单附件
     */
    @Override
    public List<SettlementOrderFile> selectSettlementOrderFileList(SettlementOrderFile settlementOrderFile)
    {
        return settlementOrderFileMapper.selectSettlementOrderFileList(settlementOrderFile);
    }

    /**
     * 新增结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    @Override
    public int insertSettlementOrderFile(SettlementOrderFile settlementOrderFile)
    {
        settlementOrderFile.setCreateTime(DateUtils.getNowDate());
        return settlementOrderFileMapper.insertSettlementOrderFile(settlementOrderFile);
    }

    /**
     * 修改结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    @Override
    public int updateSettlementOrderFile(SettlementOrderFile settlementOrderFile)
    {
        settlementOrderFile.setUpdateTime(DateUtils.getNowDate());
        return settlementOrderFileMapper.updateSettlementOrderFile(settlementOrderFile);
    }

    /**
     * 批量删除结算单附件
     * 
     * @param ids 需要删除的结算单附件主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderFileByIds(Long[] ids)
    {
        return settlementOrderFileMapper.deleteSettlementOrderFileByIds(ids);
    }

    /**
     * 删除结算单附件信息
     * 
     * @param id 结算单附件主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderFileById(Long id)
    {
        return settlementOrderFileMapper.deleteSettlementOrderFileById(id);
    }

    @Override
    public List<SettlementOrderFile> selectBySettlementOrderIdAndFileType(Long settlementOrderId, Integer fileType) {
        if (Objects.isNull(settlementOrderId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SettlementOrderFile>()
                .eq(SettlementOrderFile::getSettlementOrderId, settlementOrderId)
                .eq(!Objects.isNull(fileType), SettlementOrderFile::getFileType, fileType));
    }

    @Override
    @Transactional
    public void removeAndSaveNewBySettlementOrderIdAndFileType(Long settlementOrderId, List<CommonFileVO> files, Integer fileType) {
        if (Objects.isNull(settlementOrderId)) {
            return;
        }
        update(new LambdaUpdateWrapper<SettlementOrderFile>().eq(SettlementOrderFile::getSettlementOrderId, settlementOrderId)
                .eq(!Objects.isNull(fileType), SettlementOrderFile::getFileType, fileType)
                .eq(SettlementOrderFile::getIsDel, false).set(SettlementOrderFile::getIsDel, true));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(file -> new SettlementOrderFile().setSettlementOrderId(settlementOrderId)
                    .setFileType(fileType)
                    .setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName())
                    .setIsDel(false)).collect(Collectors.toList()));
        }
    }
}
