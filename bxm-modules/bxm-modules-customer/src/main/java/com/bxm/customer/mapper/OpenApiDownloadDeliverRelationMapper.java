package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiDownloadDeliverRelation;

/**
 * 个税申报下载轮询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-06
 */
@Mapper
public interface OpenApiDownloadDeliverRelationMapper extends BaseMapper<OpenApiDownloadDeliverRelation>
{
    /**
     * 查询个税申报下载轮询
     * 
     * @param id 个税申报下载轮询主键
     * @return 个税申报下载轮询
     */
    public OpenApiDownloadDeliverRelation selectOpenApiDownloadDeliverRelationById(Long id);

    /**
     * 查询个税申报下载轮询列表
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 个税申报下载轮询集合
     */
    public List<OpenApiDownloadDeliverRelation> selectOpenApiDownloadDeliverRelationList(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 新增个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    public int insertOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 修改个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    public int updateOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 删除个税申报下载轮询
     * 
     * @param id 个税申报下载轮询主键
     * @return 结果
     */
    public int deleteOpenApiDownloadDeliverRelationById(Long id);

    /**
     * 批量删除个税申报下载轮询
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiDownloadDeliverRelationByIds(Long[] ids);
}
