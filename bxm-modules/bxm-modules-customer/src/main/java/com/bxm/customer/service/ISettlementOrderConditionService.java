package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.SettlementOrderCondition;

/**
 * 结算单条件Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface ISettlementOrderConditionService extends IService<SettlementOrderCondition>
{
    /**
     * 查询结算单条件
     * 
     * @param id 结算单条件主键
     * @return 结算单条件
     */
    public SettlementOrderCondition selectSettlementOrderConditionById(Long id);

    /**
     * 查询结算单条件列表
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结算单条件集合
     */
    public List<SettlementOrderCondition> selectSettlementOrderConditionList(SettlementOrderCondition settlementOrderCondition);

    /**
     * 新增结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    public int insertSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition);

    /**
     * 修改结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    public int updateSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition);

    /**
     * 批量删除结算单条件
     * 
     * @param ids 需要删除的结算单条件主键集合
     * @return 结果
     */
    public int deleteSettlementOrderConditionByIds(Long[] ids);

    /**
     * 删除结算单条件信息
     * 
     * @param id 结算单条件主键
     * @return 结果
     */
    public int deleteSettlementOrderConditionById(Long id);

    List<SettlementOrderCondition> selectBySettlementOrderId(Long settlementOrderId);

    void removeAndSaveNewBySettlementOrderId(Long settlementOrderId, List<SettlementOrderCondition> settlementConditions);
}
