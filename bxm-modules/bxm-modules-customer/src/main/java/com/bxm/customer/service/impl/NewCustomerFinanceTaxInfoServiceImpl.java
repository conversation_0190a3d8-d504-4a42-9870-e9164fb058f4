package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.TaxDisk;
import com.bxm.common.core.enums.TaxMethod;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.NewCustomerFinanceTaxInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.NewCustomerTaxTypeCheck;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerServiceFinanceTaxInfoDTO;
import com.bxm.customer.mapper.NewCustomerFinanceTaxInfoMapper;
import com.bxm.customer.service.INewCustomerFinanceTaxInfoService;
import com.bxm.customer.service.INewCustomerTaxTypeCheckService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 新户流转财税信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerFinanceTaxInfoServiceImpl extends ServiceImpl<NewCustomerFinanceTaxInfoMapper, NewCustomerFinanceTaxInfo> implements INewCustomerFinanceTaxInfoService
{
    @Autowired
    private NewCustomerFinanceTaxInfoMapper newCustomerFinanceTaxInfoMapper;

    @Autowired
    private INewCustomerTaxTypeCheckService newCustomerTaxTypeCheckService;

    /**
     * 查询新户流转财税信息
     * 
     * @param id 新户流转财税信息主键
     * @return 新户流转财税信息
     */
    @Override
    public NewCustomerFinanceTaxInfo selectNewCustomerFinanceTaxInfoById(Long id)
    {
        return newCustomerFinanceTaxInfoMapper.selectNewCustomerFinanceTaxInfoById(id);
    }

    /**
     * 查询新户流转财税信息列表
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 新户流转财税信息
     */
    @Override
    public List<NewCustomerFinanceTaxInfo> selectNewCustomerFinanceTaxInfoList(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo)
    {
        return newCustomerFinanceTaxInfoMapper.selectNewCustomerFinanceTaxInfoList(newCustomerFinanceTaxInfo);
    }

    /**
     * 新增新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo)
    {
        return newCustomerFinanceTaxInfoMapper.insertNewCustomerFinanceTaxInfo(newCustomerFinanceTaxInfo);
    }

    /**
     * 修改新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo)
    {
        return newCustomerFinanceTaxInfoMapper.updateNewCustomerFinanceTaxInfo(newCustomerFinanceTaxInfo);
    }

    /**
     * 批量删除新户流转财税信息
     * 
     * @param ids 需要删除的新户流转财税信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerFinanceTaxInfoByIds(Long[] ids)
    {
        return newCustomerFinanceTaxInfoMapper.deleteNewCustomerFinanceTaxInfoByIds(ids);
    }

    /**
     * 删除新户流转财税信息信息
     * 
     * @param id 新户流转财税信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerFinanceTaxInfoById(Long id)
    {
        return newCustomerFinanceTaxInfoMapper.deleteNewCustomerFinanceTaxInfoById(id);
    }

    @Override
    public NewCustomerFinanceTaxInfo selectByNewCustomerTransferId(Long newCustomerTransferId) {
        if (Objects.isNull(newCustomerTransferId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<NewCustomerFinanceTaxInfo>().eq(NewCustomerFinanceTaxInfo::getCustomerId, newCustomerTransferId), false);
    }

    @Override
    public Map<Long, NewCustomerFinanceTaxInfo> selectMapByNewCustomerTransferIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerFinanceTaxInfo>().in(NewCustomerFinanceTaxInfo::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.toMap(NewCustomerFinanceTaxInfo::getCustomerId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    @Transactional
    public String editCustomerServiceFinanceTaxInfo(NewCustomerServiceFinanceTaxInfoDTO dto) {
        NewCustomerFinanceTaxInfo oldTaxInfo = selectByNewCustomerTransferId(dto.getNewCustomerTransferId());
        Integer operType;
        if (Objects.isNull(oldTaxInfo)) {
            NewCustomerFinanceTaxInfo taxInfo = new NewCustomerFinanceTaxInfo();
            BeanUtils.copyProperties(dto, taxInfo);
            taxInfo.setCustomerId(dto.getNewCustomerTransferId());
            save(taxInfo);
            operType = 1;
        } else {
            update(new LambdaUpdateWrapper<NewCustomerFinanceTaxInfo>()
                    .eq(NewCustomerFinanceTaxInfo::getId, oldTaxInfo.getId())
                    .set(NewCustomerFinanceTaxInfo::getAccountingSystem, dto.getAccountingSystem())
                    .set(NewCustomerFinanceTaxInfo::getFinanceRecord, dto.getFinanceRecord())
                    .set(NewCustomerFinanceTaxInfo::getReportSent, dto.getReportSent())
                    .set(NewCustomerFinanceTaxInfo::getLastAccountMonth, dto.getLastAccountMonth())
                    .set(NewCustomerFinanceTaxInfo::getNotAccountReason, dto.getNotAccountReason())
                    .set(NewCustomerFinanceTaxInfo::getOtherRemarks, dto.getOtherRemarks())
                    .set(NewCustomerFinanceTaxInfo::getIncomeMain, dto.getIncomeMain())
                    .set(NewCustomerFinanceTaxInfo::getIncomeOther, dto.getIncomeOther())
                    .set(NewCustomerFinanceTaxInfo::getCost, dto.getCost())
                    .set(NewCustomerFinanceTaxInfo::getExpense, dto.getExpense())
                    .set(NewCustomerFinanceTaxInfo::getProfit, dto.getProfit())
                    .set(NewCustomerFinanceTaxInfo::getOffsetLoss, dto.getOffsetLoss())
                    .set(NewCustomerFinanceTaxInfo::getTotalSalary, dto.getTotalSalary())
                    .set(NewCustomerFinanceTaxInfo::getWelfareFee, dto.getWelfareFee())
                    .set(NewCustomerFinanceTaxInfo::getEntertainmentFee, dto.getEntertainmentFee())
                    .set(NewCustomerFinanceTaxInfo::getOtherAdjustment, dto.getOtherAdjustment())
                    .set(NewCustomerFinanceTaxInfo::getTaxMethod, dto.getTaxMethod())
                    .set(NewCustomerFinanceTaxInfo::getEzTaxAccount, dto.getEzTaxAccount())
                    .set(NewCustomerFinanceTaxInfo::getTaxDisk, dto.getTaxDisk())
                    .set(NewCustomerFinanceTaxInfo::getTaxPassword, dto.getTaxPassword()));
            operType = 2;
        }
        return getOperContent(oldTaxInfo, dto, operType);
    }

    @Override
    public NewCustomerServiceFinanceTaxInfoDTO buildTaxInfo(NewCustomerInfo newCustomerInfo) {
        NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo = selectByNewCustomerTransferId(newCustomerInfo.getId());
        boolean hasFinanceTaxInfo = Objects.nonNull(newCustomerFinanceTaxInfo);
        return NewCustomerServiceFinanceTaxInfoDTO.builder()
                .newCustomerTransferId(newCustomerInfo.getId())
                .isNewCustomer(newCustomerInfo.getIsNewCustomer())
                .accountingSystem(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getAccountingSystem() : null)
                .financeRecord(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getFinanceRecord() : null)
                .reportSent(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getReportSent() : null)
                .lastAccountMonth(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getLastAccountMonth() : null)
                .notAccountReason(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getNotAccountReason() : null)
                .otherRemarks(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getOtherRemarks() : null)
                .incomeMain(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getIncomeMain() : null)
                .incomeOther(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getIncomeOther() : null)
                .cost(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getCost() : null)
                .expense(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getExpense() : null)
                .profit(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getProfit() : null)
                .offsetLoss(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getOffsetLoss() : null)
                .totalSalary(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getTotalSalary() : null)
                .welfareFee(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getWelfareFee() : null)
                .entertainmentFee(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getEntertainmentFee() : null)
                .otherAdjustment(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getOtherAdjustment() : null)
                .taxMethod(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getTaxMethod() : null)
                .ezTaxAccount(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getEzTaxAccount() : null)
                .taxDisk(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getTaxDisk() : null)
                .taxPassword(hasFinanceTaxInfo ? newCustomerFinanceTaxInfo.getTaxPassword() : null)
                .taxTypeCheckList(newCustomerTaxTypeCheckService.selectCustomerTaxTypeCheckByNewCustomerTransferId(newCustomerInfo.getId(), newCustomerInfo.getTaxType()))
                .build();
    }

    @Override
    @Transactional
    public void updateByTaxInfo(NewCustomerServiceFinanceTaxInfoDTO dto) {
        NewCustomerFinanceTaxInfo oldTaxInfo = selectByNewCustomerTransferId(dto.getNewCustomerTransferId());
        if (Objects.isNull(oldTaxInfo)) {
            NewCustomerFinanceTaxInfo taxInfo = new NewCustomerFinanceTaxInfo();
            BeanUtils.copyProperties(dto, taxInfo);
            taxInfo.setCustomerId(dto.getNewCustomerTransferId());
            save(taxInfo);
        } else {
            update(new LambdaUpdateWrapper<NewCustomerFinanceTaxInfo>()
                    .eq(NewCustomerFinanceTaxInfo::getId, oldTaxInfo.getId())
                    .set(NewCustomerFinanceTaxInfo::getAccountingSystem, dto.getAccountingSystem())
                    .set(NewCustomerFinanceTaxInfo::getFinanceRecord, dto.getFinanceRecord())
                    .set(NewCustomerFinanceTaxInfo::getReportSent, dto.getReportSent())
                    .set(NewCustomerFinanceTaxInfo::getLastAccountMonth, dto.getLastAccountMonth())
                    .set(NewCustomerFinanceTaxInfo::getNotAccountReason, dto.getNotAccountReason())
                    .set(NewCustomerFinanceTaxInfo::getOtherRemarks, dto.getOtherRemarks())
                    .set(NewCustomerFinanceTaxInfo::getIncomeMain, dto.getIncomeMain())
                    .set(NewCustomerFinanceTaxInfo::getIncomeOther, dto.getIncomeOther())
                    .set(NewCustomerFinanceTaxInfo::getCost, dto.getCost())
                    .set(NewCustomerFinanceTaxInfo::getExpense, dto.getExpense())
                    .set(NewCustomerFinanceTaxInfo::getProfit, dto.getProfit())
                    .set(NewCustomerFinanceTaxInfo::getOffsetLoss, dto.getOffsetLoss())
                    .set(NewCustomerFinanceTaxInfo::getTotalSalary, dto.getTotalSalary())
                    .set(NewCustomerFinanceTaxInfo::getWelfareFee, dto.getWelfareFee())
                    .set(NewCustomerFinanceTaxInfo::getEntertainmentFee, dto.getEntertainmentFee())
                    .set(NewCustomerFinanceTaxInfo::getOtherAdjustment, dto.getOtherAdjustment())
                    .set(NewCustomerFinanceTaxInfo::getTaxMethod, dto.getTaxMethod())
                    .set(NewCustomerFinanceTaxInfo::getEzTaxAccount, dto.getEzTaxAccount())
                    .set(NewCustomerFinanceTaxInfo::getTaxDisk, dto.getTaxDisk())
                    .set(NewCustomerFinanceTaxInfo::getTaxPassword, dto.getTaxPassword()));
        }
        List<NewCustomerTaxTypeCheck> taxTypeList = newCustomerTaxTypeCheckService.selectByNewCustomerTransferId(dto.getNewCustomerTransferId());
        List<NewCustomerTaxTypeCheck> addData = Lists.newArrayList();
        List<NewCustomerTaxTypeCheck> deleteData = Lists.newArrayList();
        List<NewCustomerTaxTypeCheck> updateData = Lists.newArrayList();
        if (ObjectUtils.isEmpty(taxTypeList)) {
            if (!ObjectUtils.isEmpty(dto.getTaxTypeCheckList())) {
                dto.getTaxTypeCheckList().forEach(taxType ->
                        addData.add(new NewCustomerTaxTypeCheck().setCustomerId(dto.getNewCustomerTransferId())
                                .setReportType(taxType.getReportType())
                                .setTaxType(taxType.getTaxTypeName()))
                );
            }
        } else {
            Map<Long, NewCustomerTaxTypeCheck> checkMap = taxTypeList.stream().collect(Collectors.toMap(NewCustomerTaxTypeCheck::getId, Function.identity()));
            if (!ObjectUtils.isEmpty(dto.getTaxTypeCheckList())) {
                dto.getTaxTypeCheckList().forEach(taxType -> {
                    if (Objects.isNull(taxType.getId())) {
                        // id为空 代表是新增的
                        addData.add(new NewCustomerTaxTypeCheck().setCustomerId(dto.getNewCustomerTransferId())
                                .setReportType(taxType.getReportType())
                                .setTaxType(taxType.getTaxTypeName()));
                    } else {
                        // id不为空 代表是原本就存在的
                        NewCustomerTaxTypeCheck oldCheck = checkMap.get(taxType.getId());
                        NewCustomerTaxTypeCheck newCheck = new NewCustomerTaxTypeCheck().setId(taxType.getId())
                                .setTaxType(taxType.getTaxTypeName()).setReportType(taxType.getReportType())
                                .setCustomerId(dto.getNewCustomerTransferId());
                        if (!newCheck.equals(oldCheck)) {
                            updateData.add(newCheck);
                        }
                        checkMap.remove(taxType.getId());
                    }
                });
                if (!ObjectUtils.isEmpty(checkMap)) {
                    for (Map.Entry<Long, NewCustomerTaxTypeCheck> entry : checkMap.entrySet()) {
                        deleteData.add(entry.getValue());
                    }
                }
            } else {
                deleteData = taxTypeList;
            }
        }
        if (!ObjectUtils.isEmpty(addData)) {
            newCustomerTaxTypeCheckService.saveBatch(addData);
        }
        if (!ObjectUtils.isEmpty(updateData)) {
            newCustomerTaxTypeCheckService.updateBatchById(updateData);
        }
        if (!ObjectUtils.isEmpty(deleteData)) {
            newCustomerTaxTypeCheckService.removeByIds(deleteData.stream().map(NewCustomerTaxTypeCheck::getId).collect(Collectors.toList()));
        }
    }

    private String getOperContent(NewCustomerFinanceTaxInfo oldTaxInfo, NewCustomerServiceFinanceTaxInfoDTO dto, Integer operType) {
        StringBuilder operContent = new StringBuilder();
        if (operType == 1) {
            operContent.append("新增财务信息：");
            if (!Objects.isNull(dto.getFinanceRecord())) {
                operContent.append(String.format("%s为%s，", "财务制度", dto.getFinanceRecord() ? "已备案" : "未备案"));
            }
            if (!Objects.isNull(dto.getReportSent())) {
                operContent.append(String.format("%s为%s，", "会计报表", dto.getReportSent() ? "已送报" : "未送报"));
            }
            if (!StringUtils.isEmpty(dto.getAccountingSystem())) {
                operContent.append(String.format("%s为%s，", "做账系统", dto.getAccountingSystem()));
            }
            if (!Objects.isNull(dto.getLastAccountMonth())) {
                operContent.append(String.format("%s为%s，", "最后入账月份", dto.getLastAccountMonth()));
            }
            if (!StringUtils.isEmpty(dto.getNotAccountReason())) {
                operContent.append(String.format("%s为%s，", "未入账原因", dto.getNotAccountReason()));
            }
            if (!StringUtils.isEmpty(dto.getOtherRemarks())) {
                operContent.append(String.format("%s为%s，", "其他备注", dto.getOtherRemarks()));
            }
            if (!Objects.isNull(dto.getIncomeMain())) {
                operContent.append(String.format("%s为%s，", "主营收入", dto.getIncomeMain().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getIncomeOther())) {
                operContent.append(String.format("%s为%s，", "营业外收入", dto.getIncomeOther().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getCost())) {
                operContent.append(String.format("%s为%s，", "成本", dto.getCost().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getExpense())) {
                operContent.append(String.format("%s为%s，", "费用", dto.getExpense().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getProfit())) {
                operContent.append(String.format("%s为%s，", "利润", dto.getProfit().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getOffsetLoss())) {
                operContent.append(String.format("%s为%s，", "可弥补损益", dto.getOffsetLoss().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getTotalSalary())) {
                operContent.append(String.format("%s为%s，", "工资总额", dto.getTotalSalary().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getWelfareFee())) {
                operContent.append(String.format("%s为%s，", "福利费", dto.getWelfareFee().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getEntertainmentFee())) {
                operContent.append(String.format("%s为%s，", "招待费", dto.getEntertainmentFee().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getOtherAdjustment())) {
                operContent.append(String.format("%s为%s，", "其他调增项", dto.getOtherAdjustment().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.isNull(dto.getTaxMethod())) {
                operContent.append(String.format("%s为%s，", "个税申报方式", TaxMethod.getByCode(dto.getTaxMethod()).getName()));
            }
            if (!Objects.isNull(dto.getEzTaxAccount())) {
                operContent.append(String.format("%s为%s，", "易捷账个税账号", dto.getEzTaxAccount() ? "已维护" : "未维护"));
            }
            if (!Objects.isNull(dto.getTaxDisk())) {
                operContent.append(String.format("%s为%s，", "税盘", TaxDisk.getByCode(dto.getTaxDisk()).getName()));
            }
            if (!StringUtils.isEmpty(dto.getTaxPassword())) {
                operContent.append(String.format("%s为%s，", "税盘密码", dto.getTaxPassword()));
            }
        } else {
            if (!Objects.equals(oldTaxInfo.getFinanceRecord(), dto.getFinanceRecord())) {
                operContent.append(String.format("%s（%s）修改为%s，", "财务制度", Objects.isNull(oldTaxInfo.getFinanceRecord()) ? "无" : (oldTaxInfo.getFinanceRecord() ? "已备案" : "未备案"), Objects.isNull(dto.getFinanceRecord()) ? "无" : (dto.getFinanceRecord() ? "已备案" : "未备案")));
            }
            if (!Objects.equals(oldTaxInfo.getReportSent(), dto.getReportSent())) {
                operContent.append(String.format("%s（%s）修改为%s，", "会计报表", Objects.isNull(oldTaxInfo.getReportSent()) ? "无" : (oldTaxInfo.getReportSent() ? "已送报" : "未送报"), Objects.isNull(dto.getReportSent()) ? "无" : (dto.getReportSent() ? "已送报" : "未送报")));
            }
            if (!Objects.equals(oldTaxInfo.getAccountingSystem(), dto.getAccountingSystem())) {
                operContent.append(String.format("%s（%s）修改为%s，", "做账系统", StringUtils.isEmpty(oldTaxInfo.getAccountingSystem()) ? "无" : oldTaxInfo.getAccountingSystem(), StringUtils.isEmpty(dto.getAccountingSystem()) ? "无" : dto.getAccountingSystem()));
            }
            if (!Objects.equals(oldTaxInfo.getLastAccountMonth(), dto.getLastAccountMonth())) {
                operContent.append(String.format("%s（%s）修改为%s，", "最后入账月份", Objects.isNull(oldTaxInfo.getLastAccountMonth()) ? "无" : oldTaxInfo.getLastAccountMonth(), Objects.isNull(dto.getLastAccountMonth()) ? "无" : dto.getLastAccountMonth()));
            }
            if (!Objects.equals(oldTaxInfo.getNotAccountReason(), dto.getNotAccountReason())) {
                operContent.append(String.format("%s（%s）修改为%s，", "未入账原因", StringUtils.isEmpty(oldTaxInfo.getNotAccountReason()) ? "无" : oldTaxInfo.getNotAccountReason(), Objects.isNull(dto.getNotAccountReason()) ? "无" : dto.getNotAccountReason()));
            }
            if (!Objects.equals(oldTaxInfo.getOtherRemarks(), dto.getOtherRemarks())) {
                operContent.append(String.format("%s（%s）修改为%s，", "其他备注", StringUtils.isEmpty(oldTaxInfo.getOtherRemarks()) ? "无" : oldTaxInfo.getOtherRemarks(), Objects.isNull(dto.getOtherRemarks()) ? "无" : dto.getOtherRemarks()));
            }
            if (!Objects.equals(oldTaxInfo.getIncomeMain(), dto.getIncomeMain())) {
                operContent.append(String.format("%s（%s）修改为%s，", "主营收入", Objects.isNull(oldTaxInfo.getIncomeMain()) ? "无" : oldTaxInfo.getIncomeMain().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getIncomeMain()) ? "无" : dto.getIncomeMain().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getIncomeOther(), dto.getIncomeOther())) {
                operContent.append(String.format("%s（%s）修改为%s，", "营业外收入", Objects.isNull(oldTaxInfo.getIncomeOther()) ? "无" : oldTaxInfo.getIncomeOther().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getIncomeOther()) ? "无" : dto.getIncomeOther().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getCost(), dto.getCost())) {
                operContent.append(String.format("%s（%s）修改为%s，", "成本", Objects.isNull(oldTaxInfo.getCost()) ? "无" : oldTaxInfo.getCost().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getCost()) ? "无" : dto.getCost().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getExpense(), dto.getExpense())) {
                operContent.append(String.format("%s（%s）修改为%s，", "费用", Objects.isNull(oldTaxInfo.getExpense()) ? "无" : oldTaxInfo.getExpense().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getExpense()) ? "无" : dto.getExpense().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getProfit(), dto.getProfit())) {
                operContent.append(String.format("%s（%s）修改为%s，", "利润", Objects.isNull(oldTaxInfo.getProfit()) ? "无" : oldTaxInfo.getProfit().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getProfit()) ? "无" : dto.getProfit().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getOffsetLoss(), dto.getOffsetLoss())) {
                operContent.append(String.format("%s（%s）修改为%s，", "可弥补损益", Objects.isNull(oldTaxInfo.getOffsetLoss()) ? "无" : oldTaxInfo.getOffsetLoss().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getOffsetLoss()) ? "无" : dto.getOffsetLoss().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getTotalSalary(), dto.getTotalSalary())) {
                operContent.append(String.format("%s（%s）修改为%s，", "工资总额", Objects.isNull(oldTaxInfo.getTotalSalary()) ? "无" : oldTaxInfo.getTotalSalary().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getTotalSalary()) ? "无" : dto.getTotalSalary().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getWelfareFee(), dto.getWelfareFee())) {
                operContent.append(String.format("%s（%s）修改为%s，", "福利费", Objects.isNull(oldTaxInfo.getWelfareFee()) ? "无" : oldTaxInfo.getWelfareFee().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getWelfareFee()) ? "无" : dto.getWelfareFee().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getEntertainmentFee(), dto.getEntertainmentFee())) {
                operContent.append(String.format("%s（%s）修改为%s，", "招待费", Objects.isNull(oldTaxInfo.getEntertainmentFee()) ? "无" : oldTaxInfo.getEntertainmentFee().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getEntertainmentFee()) ? "无" : dto.getEntertainmentFee().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getOtherAdjustment(), dto.getOtherAdjustment())) {
                operContent.append(String.format("%s（%s）修改为%s，", "其他调增项", Objects.isNull(oldTaxInfo.getOtherAdjustment()) ? "无" : oldTaxInfo.getOtherAdjustment().stripTrailingZeros().toPlainString(), Objects.isNull(dto.getOtherAdjustment()) ? "无" : dto.getOtherAdjustment().stripTrailingZeros().toPlainString()));
            }
            if (!Objects.equals(oldTaxInfo.getTaxMethod(), dto.getTaxMethod())) {
                operContent.append(String.format("%s（%s）修改为%s，", "个税申报方式", Objects.isNull(oldTaxInfo.getTaxMethod()) ? "无" : TaxMethod.getByCode(oldTaxInfo.getTaxMethod()).getName(), Objects.isNull(dto.getTaxMethod()) ? "无" : TaxMethod.getByCode(dto.getTaxMethod()).getName()));
            }
            if (!Objects.equals(oldTaxInfo.getEzTaxAccount(), dto.getEzTaxAccount())) {
                operContent.append(String.format("%s（%s）修改为%s，", "易捷账个税账号", Objects.isNull(oldTaxInfo.getEzTaxAccount()) ? "无" : (oldTaxInfo.getEzTaxAccount() ? "已维护" : "未维护"), Objects.isNull(dto.getEzTaxAccount()) ? "无" : (dto.getEzTaxAccount() ? "已维护" : "未维护")));
            }
            if (!Objects.equals(oldTaxInfo.getTaxDisk(), dto.getTaxDisk())) {
                operContent.append(String.format("%s（%s）修改为%s，", "税盘", Objects.isNull(oldTaxInfo.getTaxDisk()) ? "无" : TaxDisk.getByCode(oldTaxInfo.getTaxDisk()).getName(), Objects.isNull(dto.getTaxDisk()) ? "无" : TaxDisk.getByCode(dto.getTaxDisk()).getName()));
            }
            if (!Objects.equals(oldTaxInfo.getTaxPassword(), dto.getTaxPassword())) {
                operContent.append(String.format("%s（%s）修改为%s，", "税盘密码", StringUtils.isEmpty(oldTaxInfo.getTaxPassword()) ? "无" : oldTaxInfo.getTaxPassword(), StringUtils.isEmpty(dto.getTaxPassword()) ? "无" : dto.getTaxPassword()));
            }
            if (!StringUtils.isEmpty(operContent)) {
                return "编辑财务信息：" + operContent;
            }
        }
        return operContent.toString();
    }
}
