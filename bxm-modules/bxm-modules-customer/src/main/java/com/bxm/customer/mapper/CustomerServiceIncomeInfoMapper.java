package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceIncomeInfo;

/**
 * 客户服务收入信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface CustomerServiceIncomeInfoMapper extends BaseMapper<CustomerServiceIncomeInfo>
{
    /**
     * 查询客户服务收入信息
     * 
     * @param id 客户服务收入信息主键
     * @return 客户服务收入信息
     */
    public CustomerServiceIncomeInfo selectCustomerServiceIncomeInfoById(Long id);

    /**
     * 查询客户服务收入信息列表
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 客户服务收入信息集合
     */
    public List<CustomerServiceIncomeInfo> selectCustomerServiceIncomeInfoList(CustomerServiceIncomeInfo customerServiceIncomeInfo);

    /**
     * 新增客户服务收入信息
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 结果
     */
    public int insertCustomerServiceIncomeInfo(CustomerServiceIncomeInfo customerServiceIncomeInfo);

    /**
     * 修改客户服务收入信息
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 结果
     */
    public int updateCustomerServiceIncomeInfo(CustomerServiceIncomeInfo customerServiceIncomeInfo);

    /**
     * 删除客户服务收入信息
     * 
     * @param id 客户服务收入信息主键
     * @return 结果
     */
    public int deleteCustomerServiceIncomeInfoById(Long id);

    /**
     * 批量删除客户服务收入信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceIncomeInfoByIds(Long[] ids);
}
