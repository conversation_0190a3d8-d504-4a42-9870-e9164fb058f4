package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.domain.vo.accoutingCashier.CustomerServiceFileNameVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceCashierAccountingFile;
import org.apache.ibatis.annotations.Param;

/**
 * 客户账务附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Mapper
public interface CustomerServiceCashierAccountingFileMapper extends BaseMapper<CustomerServiceCashierAccountingFile>
{
    /**
     * 查询客户账务附件
     * 
     * @param id 客户账务附件主键
     * @return 客户账务附件
     */
    public CustomerServiceCashierAccountingFile selectCustomerServiceCashierAccountingFileById(Long id);

    /**
     * 查询客户账务附件列表
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 客户账务附件集合
     */
    public List<CustomerServiceCashierAccountingFile> selectCustomerServiceCashierAccountingFileList(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 新增客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    public int insertCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 修改客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    public int updateCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 删除客户账务附件
     * 
     * @param id 客户账务附件主键
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingFileById(Long id);

    /**
     * 批量删除客户账务附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingFileByIds(Long[] ids);

    List<CustomerServiceCashierAccountingFile> selectByBatchCustomerServiceIdAndFileName(@Param("voList") List<CustomerServiceFileNameVO> voList);
}
