package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CCustomerServiceChangeRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户服务变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Mapper
public interface CCustomerServiceChangeRecordMapper extends BaseMapper<CCustomerServiceChangeRecord>
{
    /**
     * 查询客户服务变更记录
     * 
     * @param id 客户服务变更记录主键
     * @return 客户服务变更记录
     */
    public CCustomerServiceChangeRecord selectCCustomerServiceChangeRecordById(Long id);

    /**
     * 查询客户服务变更记录列表
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 客户服务变更记录集合
     */
    public List<CCustomerServiceChangeRecord> selectCCustomerServiceChangeRecordList(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 新增客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    public int insertCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 修改客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    public int updateCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord);

    /**
     * 删除客户服务变更记录
     * 
     * @param id 客户服务变更记录主键
     * @return 结果
     */
    public int deleteCCustomerServiceChangeRecordById(Long id);

    /**
     * 批量删除客户服务变更记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCCustomerServiceChangeRecordByIds(Long[] ids);
}
