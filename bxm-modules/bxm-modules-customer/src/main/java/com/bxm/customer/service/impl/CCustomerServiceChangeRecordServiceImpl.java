package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.CustomerServiceChangeType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CCustomerServiceChangeRecord;
import com.bxm.customer.mapper.CCustomerServiceChangeRecordMapper;
import com.bxm.customer.service.ICCustomerServiceChangeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

/**
 * 客户服务变更记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
public class CCustomerServiceChangeRecordServiceImpl extends ServiceImpl<CCustomerServiceChangeRecordMapper, CCustomerServiceChangeRecord> implements ICCustomerServiceChangeRecordService
{
    @Autowired
    private CCustomerServiceChangeRecordMapper cCustomerServiceChangeRecordMapper;

    /**
     * 查询客户服务变更记录
     * 
     * @param id 客户服务变更记录主键
     * @return 客户服务变更记录
     */
    @Override
    public CCustomerServiceChangeRecord selectCCustomerServiceChangeRecordById(Long id)
    {
        return cCustomerServiceChangeRecordMapper.selectCCustomerServiceChangeRecordById(id);
    }

    /**
     * 查询客户服务变更记录列表
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 客户服务变更记录
     */
    @Override
    public List<CCustomerServiceChangeRecord> selectCCustomerServiceChangeRecordList(CCustomerServiceChangeRecord cCustomerServiceChangeRecord)
    {
        return cCustomerServiceChangeRecordMapper.selectCCustomerServiceChangeRecordList(cCustomerServiceChangeRecord);
    }

    /**
     * 新增客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    @Override
    public int insertCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord)
    {
        cCustomerServiceChangeRecord.setCreateTime(DateUtils.getNowDate());
        return cCustomerServiceChangeRecordMapper.insertCCustomerServiceChangeRecord(cCustomerServiceChangeRecord);
    }

    /**
     * 修改客户服务变更记录
     * 
     * @param cCustomerServiceChangeRecord 客户服务变更记录
     * @return 结果
     */
    @Override
    public int updateCCustomerServiceChangeRecord(CCustomerServiceChangeRecord cCustomerServiceChangeRecord)
    {
        cCustomerServiceChangeRecord.setUpdateTime(DateUtils.getNowDate());
        return cCustomerServiceChangeRecordMapper.updateCCustomerServiceChangeRecord(cCustomerServiceChangeRecord);
    }

    /**
     * 批量删除客户服务变更记录
     * 
     * @param ids 需要删除的客户服务变更记录主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServiceChangeRecordByIds(Long[] ids)
    {
        return cCustomerServiceChangeRecordMapper.deleteCCustomerServiceChangeRecordByIds(ids);
    }

    /**
     * 删除客户服务变更记录信息
     * 
     * @param id 客户服务变更记录主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServiceChangeRecordById(Long id)
    {
        return cCustomerServiceChangeRecordMapper.deleteCCustomerServiceChangeRecordById(id);
    }

    @Override
    public List<CCustomerServiceChangeRecord> selectNotDoneBatchByCustomerIdAndChangeType(List<Long> customerServiceIds, CustomerServiceChangeType changeType) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CCustomerServiceChangeRecord>()
                .in(CCustomerServiceChangeRecord::getCustomerServiceId, customerServiceIds)
                .eq(CCustomerServiceChangeRecord::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceChangeRecord::getIsDone, Boolean.FALSE)
                .eq(CCustomerServiceChangeRecord::getChangeType, changeType.getCode()));
    }
}
