package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerDeliverCountryTaxExportDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceDeliverDTO;
import com.bxm.customer.domain.CustomerServiceRepairAccount;
import com.bxm.customer.domain.dto.CustomerServicePeriodYearDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.inAccount.InAccountDTO;
import com.bxm.customer.domain.dto.repairAccount.*;
import com.bxm.customer.domain.vo.repairAccount.*;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.ICustomerServiceRepairAccountService;
import com.bxm.customer.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 补账Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/repairAccount")
@Api(tags = "补账")
public class CustomerServiceRepairAccountController extends BaseController {
    @Autowired
    private ICustomerServiceRepairAccountService customerServiceRepairAccountService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    /**
     * 查询补账列表
     */
    @RequiresPermissions("customer:account:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询补账列表", notes = "查询补账列表")
    public TableDataInfo list(CustomerServiceRepairAccount customerServiceRepairAccount) {
        startPage();
        List<CustomerServiceRepairAccount> list = customerServiceRepairAccountService.selectCustomerServiceRepairAccountList(customerServiceRepairAccount);
        return getDataTable(list);
    }

    /**
     * 导出补账列表
     */
    @RequiresPermissions("customer:account:export")
    @Log(title = "补账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出补账列表", notes = "导出补账列表")
    public void export(HttpServletResponse response, CustomerServiceRepairAccount customerServiceRepairAccount) {
        List<CustomerServiceRepairAccount> list = customerServiceRepairAccountService.selectCustomerServiceRepairAccountList(customerServiceRepairAccount);
        ExcelUtil<CustomerServiceRepairAccount> util = new ExcelUtil<CustomerServiceRepairAccount>(CustomerServiceRepairAccount.class);
        util.exportExcel(response, list, "补账数据");
    }

    /**
     * 获取补账详细信息
     */
    @RequiresPermissions("customer:account:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取补账详细信息", notes = "获取补账详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(customerServiceRepairAccountService.selectCustomerServiceRepairAccountById(id));
    }

    /**
     * 新增补账
     */
    @RequiresPermissions("customer:account:add")
    @Log(title = "补账", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增补账", notes = "新增补账")
    public AjaxResult add(@RequestBody CustomerServiceRepairAccount customerServiceRepairAccount) {
        return toAjax(customerServiceRepairAccountService.insertCustomerServiceRepairAccount(customerServiceRepairAccount));
    }

    /**
     * 修改补账
     */
    @RequiresPermissions("customer:account:edit")
    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改补账", notes = "修改补账")
    public AjaxResult edit(@RequestBody CustomerServiceRepairAccount customerServiceRepairAccount) {
        return toAjax(customerServiceRepairAccountService.updateCustomerServiceRepairAccount(customerServiceRepairAccount));
    }

    /**
     * 删除补账
     */
    @RequiresPermissions("customer:account:remove")
    @Log(title = "补账", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除补账", notes = "删除补账")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(customerServiceRepairAccountService.deleteCustomerServiceRepairAccountByIds(ids));
    }

    //****** start self method ******  批量操作:
    @GetMapping("/repairAccountList")
    @ApiOperation(value = "分页获取补账列表", notes = "分页获取补账列表")
    public Result<IPage<RepairAccountDTO>> repairAccountList(@RequestHeader("deptId") Long deptId, RepairAccountVO vo) {
        return Result.ok(customerServiceRepairAccountService.repairAccountList(deptId, vo));
    }

    //@RequiresPermissions("customer:customerService:exportCustomerServicePeriodMonthList")
    @Log(title = "补账", businessType = BusinessType.EXPORT)
    @PostMapping("/exportInAccountList")
    @ApiOperation(value = "导出补账服务列表", notes = "导出补账服务列表")
    public void exportRepairAccountList(HttpServletResponse response, @RequestHeader("deptId") Long deptId, RepairAccountVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<RepairAccountDTO> list = customerServiceRepairAccountService.repairAccountList(deptId, vo).getRecords();
        ExcelUtil<RepairAccountDTO> util = new ExcelUtil<>(RepairAccountDTO.class);
        util.exportExcel(response, list, "补账服务");
    }

    @PostMapping("/exportInAccountListAndUpload")
    @ApiOperation(value = "导出补账服务列表(上传oss)", notes = "导出补账服务列表(上传oss)")
    public Result exportInAccountListAndUpload(@RequestHeader("deptId") Long deptId, RepairAccountVO vo) {
        String title = "服务-补账服务" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_REPAIR_SERVICE);
        CompletableFuture.runAsync(() -> {
            try {
                List<RepairAccountDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RepairAccountDTO> l = customerServiceRepairAccountService.repairAccountList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<RepairAccountDTO> util = new ExcelUtil<>(RepairAccountDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @GetMapping("/getFirstPeriod")
    @ApiOperation(value = "获取首个账期信息", notes = "获取首个账期信息")
    public Result<FirstPeriodDTO> getFirstPeriod(
            @RequestHeader("deptId") Long deptId, @RequestParam("customerServiceId") Long customerServiceId
    ) {
        return Result.ok(customerServiceRepairAccountService.getFirstPeriod(customerServiceId));
    }

    @GetMapping("/getRepairAccountPeriodEnd")
    @ApiOperation(value = "获取补账的服务结束时间", notes = "获取补账的服务结束时间")
    public Result<RepairAccountPeriodEndDTO> getRepairAccountPeriodEnd(
            @RequestHeader("deptId") Long deptId, @RequestParam("customerServiceId") Long customerServiceId
    ) {
        return Result.ok(customerServiceRepairAccountService.getRepairAccountPeriodEnd(customerServiceId));
    }

    @Log(title = "补账", businessType = BusinessType.INSERT)
    @PostMapping("/addRepairAccountBase")
    @ApiOperation(value = "新增，基础信息", notes = "新增，基础信息")
    public Result<Long> addRepairAccountBase(@RequestHeader("deptId") Long deptId, @RequestBody @Valid AddRepairAccountBaseVO vo) {
        return Result.ok(customerServiceRepairAccountService.addRepairAccountBase(deptId, vo));
    }

    @GetMapping("/getRepairAccountBase")
    @ApiOperation(value = "获取基础信息", notes = "获取基础信息")
    public Result<RepairAccountBaseDTO> getRepairAccountBase(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceRepairAccountService.getRepairAccountBase(id));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRepairAccountBase")
    @ApiOperation(value = "编辑基础信息", notes = "编辑基础信息")
    public Result<Boolean> updateRepairAccountBase(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateRepairAccountBaseVO vo) {
        customerServiceRepairAccountService.updateRepairAccountBase(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getRepairAccountInstrument")
    @ApiOperation(value = "根据补账id获取票据信息", notes = "根据补账id获取票据信息")
    public Result<RepairAccountInstrumentDTO> getRepairAccountInstrument(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceRepairAccountService.getRepairAccountInstrument(id));
    }

    @PostMapping("/checkCanSubmit")
    @ApiOperation(value = "一并校验可提交规则，false 校验不通过", notes = "一并校验可提交规则，false 校验不通过")
    public Result<CheckCanSubmitDTO> checkCanSubmit(@RequestHeader("deptId") Long deptId, @RequestBody @Valid RepairAccountInstrumentDTO vo) {
        return Result.ok(customerServiceRepairAccountService.checkCanSubmit(vo));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/saveDocHandoverInstrument")
    @ApiOperation(value = "保存", notes = "保存")
    public Result<Boolean> saveDocHandoverInstrument(@RequestHeader("deptId") Long deptId, @RequestBody @Valid RepairAccountInstrumentDTO vo) {
        customerServiceRepairAccountService.saveDocHandoverInstrument(deptId, vo, false);
        return Result.ok();
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/submitDocHandoverInstrument")
    @ApiOperation(value = "提交", notes = "提交")
    public Result<Boolean> submitDocHandoverInstrument(@RequestHeader("deptId") Long deptId, @RequestBody @Valid RepairAccountInstrumentDTO vo) {
        customerServiceRepairAccountService.submitDocHandoverInstrument(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getDetailTitles")
    @ApiOperation(value = "获取详情标题头列表", notes = "获取详情标题头列表")
    public Result<List<RepairAccountTitleDTO>> getDetailTitles(
            @RequestHeader("deptId") Long deptId, @RequestParam("id") Long id
    ) {
        return Result.ok(customerServiceRepairAccountService.getDetailTitles(id));
    }

    //****** START 二二二二二二二二二

    @GetMapping("/getRepairAccountFull")
    @ApiOperation(value = "获取补账详情", notes = "获取补账详情")
    public Result<RepairAccountFullDTO> getRepairAccountFull(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceRepairAccountService.getRepairAccountFullV2(id));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/batchOperateSubmit")
    @ApiOperation(value = "提交-批量", notes = "提交-批量")
    public Result<TCommonOperateDTO<CustomerServiceRepairAccount>> batchOperateSubmit(@RequestHeader("deptId") Long deptId, @RequestBody List<Long> ids) {
        return Result.ok(customerServiceRepairAccountService.batchOperateSubmit(deptId, ids));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/batchOperateDelete")
    @ApiOperation(value = "删除-批量", notes = "删除-批量")
    public Result<TCommonOperateDTO<CustomerServiceRepairAccount>> batchOperateDelete(@RequestHeader("deptId") Long deptId, @RequestBody List<Long> ids) {
        return Result.ok(customerServiceRepairAccountService.batchOperateDelete(deptId, ids));
    }

    @GetMapping("/getAccountingDeptIdFullPathForSingle")
    @ApiOperation(value = "单体操作的时候，默认回显当前客户服务的会计小组", notes = "单体操作的时候，默认回显当前客户服务的会计小组")
    public Result<List<Long>> getAccountingDeptIdFullPathForSingle(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceRepairAccountService.getAccountingDeptIdFullPathForSingle(id));
    }

    @GetMapping("/getAccountingSelectTips")
    @ApiOperation(value = "选择会计组别后，显示当前会计上限提示。", notes = "选择会计组别后，显示当前会计上限提示。")
    public Result<AccountingSelectTipsDTO> getAccountingSelectTips(@RequestHeader("deptId") Long deptId, @RequestParam("accountingDeptId") Long accountingDeptId) {
        return Result.ok(customerServiceRepairAccountService.getAccountingSelectTips(accountingDeptId));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/batchOperateAssign")
    @ApiOperation(value = "分派-批量", notes = "分派-批量")
    public Result<TCommonOperateDTO<CustomerServiceRepairAccount>> batchOperateAssign(@RequestHeader("deptId") Long deptId, @RequestBody @Valid BatchOperateAssignRepairAccountVO vo) {
        return Result.ok(customerServiceRepairAccountService.batchOperateAssign(deptId, vo));
    }

    @Log(title = "补账", businessType = BusinessType.UPDATE)
    @PostMapping("/batchOperateBack")
    @ApiOperation(value = "退回-批量", notes = "退回-批量 ")
    public Result<TCommonOperateDTO<CustomerServiceRepairAccount>> batchOperateBack(@RequestHeader("deptId") Long deptId, @RequestBody @Valid BatchOperateBatchRepairAccountVO vo) {
        return Result.ok(customerServiceRepairAccountService.batchOperateBack(deptId, vo));
    }

    @PostMapping("/getRepairAccountOrder")
    @ApiOperation("抢单，单个操作，传id，权限字符：customer:repairAccount:getRepairAccountOrder")
    @RequiresPermissions("customer:repairAccount:getRepairAccountOrder")
    public Result getRepairAccountOrder(@RequestHeader("deptId") Long deptId, @RequestBody CommonIdVO vo) {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("repairAccount:getRepairAccountOrder:" + vo.getId(), userId + "", 5)) {
            try {
                customerServiceRepairAccountService.getRepairAccountOrder(deptId, vo);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("repairAccount:getRepairAccountOrder:" + vo.getId(), userId + "");
            }
        } else {
            return Result.fail("当前补账已被其他人抢单");
        }
    }
}
