package com.bxm.customer.validation;

import com.bxm.customer.utils.ValidateUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 身份证号验证器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class IdNumberValidator implements ConstraintValidator<IdNumber, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(IdNumber constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return ValidateUtil.isValidIdNumber(value, allowEmpty);
    }
}
