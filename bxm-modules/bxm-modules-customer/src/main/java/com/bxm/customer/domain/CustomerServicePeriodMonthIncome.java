package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户服务账期收入对象 c_customer_service_period_month_income
 * 
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ApiModel("客户服务账期收入对象")
@Accessors(chain = true)
@TableName("c_customer_service_period_month_income")
public class CustomerServicePeriodMonthIncome extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id */
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    @TableField(exist = false)
    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("账期")
    @TableField("period")
    @Excel(name = "账期")
    private Integer period;

    @TableField(exist = false)
    @Excel(name = "信用代码")
    private String creditCode;

    @TableField(exist = false)
    @ApiModelProperty("纳税人识别号")
    @Excel(name = "纳税人识别号")
    private String customerTaxNumber;

    @TableField(exist = false)
    @ApiModelProperty("服务编号")
    private String serviceNumber;

    @TableField(exist = false)
    private Long accountingTopDeptId;

    @Excel(name = "业务公司")
    @TableField(exist = false)
    private String businessDeptName;

    @TableField(exist = false)
    @Excel(name = "顾问小组")
    private String advisorDeptInfo;

    @TableField(exist = false)
    @Excel(name = "会计区域")
    private String accountingTopDeptName;

    @TableField(exist = false)
    @Excel(name = "会计小组")
    private String accountingDeptInfo;

    /** 涉税机构纳税识别号 */
    @TableField("tax_number")
    @ApiModelProperty(value = "涉税机构纳税识别号")
    private String taxNumber;

    /** 登入密码 */
    @TableField("login_password")
    @ApiModelProperty(value = "登入密码")
    private String loginPassword;

    /** 实名人 */
    @TableField("real_name_contact")
    @ApiModelProperty(value = "实名人")
    private String realNameContact;

    /** 手机号 */
    @TableField("real_name_contact_mobile")
    @ApiModelProperty(value = "手机号")
    private String realNameContactMobile;

    /** 执行结果 */
    @TableField("result")
    @ApiModelProperty(value = "执行结果")
    private String result;

    /** 全量开票金额 */
    @TableField("all_ticket_amount")
    @ApiModelProperty(value = "全量开票金额")
    private BigDecimal allTicketAmount;

    @Excel(name = "全量发票开票金额")
    @TableField(exist = false)
    private String allTicketAmountStr;

    /** 全量开票税额 */
    @TableField("all_ticket_tax_amount")
    @ApiModelProperty(value = "全量开票税额")
    private BigDecimal allTicketTaxAmount;

    @Excel(name = "全量发票开票税额")
    @TableField(exist = false)
    private String allTicketTaxAmountStr;

    /** 开票截止时间 */
    @TableField("ticket_time")
    @ApiModelProperty(value = "开票截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ticketTime;

    @TableField("special_ticket_amount")
    @ApiModelProperty(value = "专票金额")
    private BigDecimal specialTicketAmount;

    @Excel(name = "专票金额")
    @TableField(exist = false)
    private String specialTicketAmountStr;

    @TableField("normal_ticket_amount")
    @ApiModelProperty(value = "普票金额")
    private BigDecimal normalTicketAmount;

    @Excel(name = "普票金额")
    @TableField(exist = false)
    private String normalTicketAmountStr;

    @TableField(exist = false)
    @Excel(name = "开票取数截止时间")
    private String ticketTimeStr;

    /** 开票数据查询金额 */
    @TableField("ticket_search_amount")
    @ApiModelProperty(value = "开票数据查询金额")
    private BigDecimal ticketSearchAmount;

    /** 开票数据查询税额 */
    @TableField("ticket_search_tax_amount")
    @ApiModelProperty(value = "开票数据查询税额")
    private BigDecimal ticketSearchTaxAmount;

    /** 无票收入 */
    @TableField("no_ticket_income_amount")
    @ApiModelProperty(value = "无票收入")
    private BigDecimal noTicketIncomeAmount;

    @Excel(name = "无票收入")
    @TableField(exist = false)
    private String noTicketIncomeAmountStr;

    /** 无票收入更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("no_ticket_income_time")
    @ApiModelProperty(value = "无票收入更新时间, yyyy-MM-dd HH:mm:ss")
    private LocalDateTime noTicketIncomeTime;

    @Excel(name = "无票收入更新时间")
    @TableField(exist = false)
    private String noTicketIncomeTimeStr;

    /** 开具发票取票结果 */
    @TableField("do_ticket_get_result")
    @ApiModelProperty(value = "开具发票取票结果")
    private String doTicketGetResult;

    /** 取得发票取票结果 */
    @TableField("receive_ticket_get_result")
    @ApiModelProperty(value = "取得发票取票结果")
    private String receiveTicketGetResult;

    /** 开始取数时间 */
    @TableField("result_start_time")
    @ApiModelProperty(value = "开始取数时间")
    private String resultStartTime;

    /** 结束取数时间 */
    @TableField("result_end_time")
    @ApiModelProperty(value = "结束取数时间")
    private String resultEndTime;

    /** 销项导入情况 */
    @TableField("output_import_result")
    @ApiModelProperty(value = "销项导入情况")
    private String outputImportResult;

    /** 进项导入情况 */
    @TableField("input_import_result")
    @ApiModelProperty(value = "进项导入情况")
    private String inputImportResult;

    /** 发票导入结果 */
    @TableField("ticket_import_result")
    @ApiModelProperty(value = "发票导入结果")
    private String ticketImportResult;

    @TableField("total_invoice_amount")
    @ApiModelProperty(value = "全量取得发票金额")
    private BigDecimal totalInvoiceAmount; // 全量取得发票金额

    @TableField("total_invoice_tax_amount")
    @ApiModelProperty(value = "全量取得发票税额")
    private BigDecimal totalInvoiceTaxAmount; // 全量取得发票税额

    @TableField(exist = false)
    @ApiModelProperty(value = "全量取得发票金额")
    @Excel(name = "全量取得发票金额")
    private String totalInvoiceAmountStr; // 全量取得发票金额

    @TableField(exist = false)
    @ApiModelProperty(value = "全量取得发票税额")
    @Excel(name = "全量取得发票税额")
    private String totalInvoiceTaxAmountStr; // 全量取得发票税额

    @ApiModelProperty("账期筛选开始，yyyyMM")
    @TableField(exist = false)
    private Integer periodStart;

    @ApiModelProperty("账期筛选结束，yyyyMM")
    @TableField(exist = false)
    private Integer periodEnd;

    @ApiModelProperty("关键字搜索")
    @TableField(exist = false)
    private String keyWord;

    @ApiModelProperty("备注")
    @TableField("remark")
    @Excel(name = "备注")
    private String remark;

    @ApiModelProperty("文件json")
    @TableField("files")
    private String files;

    @ApiModelProperty("附件数量")
    @TableField(exist = false)
    @Excel(name = "附件")
    private Integer fileCount;

    @TableField(exist = false)
    @ApiModelProperty("附件列表")
    private List<CommonFileVO> fileList;

    @ApiModelProperty("业务公司id")
    @TableField(exist = false)
    private Long businessDeptId;

    @ApiModelProperty("顾问部门id")
    @TableField(exist = false)
    private Long advisorDeptId;

    @ApiModelProperty("会计部门id")
    @TableField(exist = false)
    private Long accountingDeptId;

    @TableField(exist = false)
    @ApiModelProperty("开票时间最小值,yyyy-MM-dd HH:mm:ss")
    private String ticketTimeMin;

    @TableField(exist = false)
    @ApiModelProperty("开票时间最大值,yyyy-MM-dd HH:mm:ss")
    private String ticketTimeMax;

    @TableField(exist = false)
    @ApiModelProperty("全量开票金额最小值")
    private BigDecimal allTicketAmountMin;

    @TableField(exist = false)
    @ApiModelProperty("全量开票金额最大值")
    private BigDecimal allTicketAmountMax;

    @TableField("rpa_time")
    @ApiModelProperty(value = "rpa最后取数时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime rpaTime;

    @TableField("input_file")
    @ApiModelProperty(value = "进项文件")
    private String inputFile;

    @TableField("output_file")
    @ApiModelProperty(value = "销项文件")
    private String outputFile;

    @TableField("input_invoice_count")
    @ApiModelProperty(value = "进项发票张数")
    private String inputInvoiceCount;
    
    @TableField(exist = false)
    @Excel(name = "rpa最后取数时间")
    private String rpaTimeStr;

    @TableField("rpa_result")
    @ApiModelProperty(value = "rpa取数结果,1-成功，2-失败")
    private Integer rpaResult;

    @TableField(exist = false)
    @Excel(name = "rpa取数结果")
    private String rpaResultStr;

    @TableField(exist = false)
    @ApiModelProperty(value = "无票收入金额最小值")
    private BigDecimal noTicketIncomeAmountMin;

    @TableField(exist = false)
    @ApiModelProperty(value = "无票收入金额最小值")
    private BigDecimal noTicketIncomeAmountMax;

    @TableField(exist = false)
    @ApiModelProperty(value = "是否有附件，0-否，1-是")
    private Integer hasFiles;

    @TableField(exist = false)
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @TableField(exist = false)
    private Integer deptType;

    @TableField(exist = false)
    private List<Long> queryDeptIds;

    @TableField(exist = false)
    private Long queryDeptId;

    @TableField(exist = false)
    private Long deptId;

    @TableField(exist = false)
    private Integer customerEndPeriod;

    @TableField(exist = false)
    @ApiModelProperty("批量查询批次号")
    private String batchNo;

    @ApiModelProperty("导出类型，1-收入附件，多个用逗号隔开，不带附件就不传这个参数")
    @TableField(exist = false)
    private String exportTypes;
}
