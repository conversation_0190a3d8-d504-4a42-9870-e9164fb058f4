package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerTaxTypeCheck;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;

import java.util.List;
import java.util.Map;

/**
 * 客户服务税种核定Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface ICustomerTaxTypeCheckService extends IService<CustomerTaxTypeCheck>
{
    /**
     * 查询客户服务税种核定
     * 
     * @param id 客户服务税种核定主键
     * @return 客户服务税种核定
     */
    public CustomerTaxTypeCheck selectCustomerTaxTypeCheckById(Long id);

    /**
     * 查询客户服务税种核定列表
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 客户服务税种核定集合
     */
    public List<CustomerTaxTypeCheck> selectCustomerTaxTypeCheckList(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 新增客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    public int insertCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 修改客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    public int updateCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 批量删除客户服务税种核定
     * 
     * @param ids 需要删除的客户服务税种核定主键集合
     * @return 结果
     */
    public int deleteCustomerTaxTypeCheckByIds(Long[] ids);

    /**
     * 删除客户服务税种核定信息
     * 
     * @param id 客户服务税种核定主键
     * @return 结果
     */
    public int deleteCustomerTaxTypeCheckById(Long id);

    List<CustomerServiceTaxTypeCheckVO> selectCustomerTaxTypeCheckByCustomerServiceId(Long customerServiceId, Integer taxType);

    List<CustomerTaxTypeCheck> selectByCustomerServiceId(Long customerServiceId);

    void removeAndCreateByXqy(Long customerServiceId, List<OpenApiSyncItem> itemList, String operName, Long deptId);

    void removeAndCreateByYsb(Long customerServiceId, List<OpenApiSyncItem> itemList, String operName, Long deptId);

    void insuranceCheck(Long customerServiceId, List<OpenApiSyncItem> itemList, String createBy, Long deptId);

    Map<Long, List<CustomerTaxTypeCheck>> getBatchByCustomerServiceId(List<Long> customerServiceIds);
}
