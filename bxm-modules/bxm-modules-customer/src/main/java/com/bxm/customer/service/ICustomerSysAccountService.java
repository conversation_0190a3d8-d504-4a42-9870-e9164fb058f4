package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerSysAccount;

/**
 * 客户服务系统账号Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface ICustomerSysAccountService extends IService<CustomerSysAccount>
{
    /**
     * 查询客户服务系统账号
     * 
     * @param id 客户服务系统账号主键
     * @return 客户服务系统账号
     */
    public CustomerSysAccount selectCustomerSysAccountById(Long id);

    /**
     * 查询客户服务系统账号列表
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 客户服务系统账号集合
     */
    public List<CustomerSysAccount> selectCustomerSysAccountList(CustomerSysAccount customerSysAccount);

    /**
     * 新增客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    public int insertCustomerSysAccount(CustomerSysAccount customerSysAccount);

    /**
     * 修改客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    public int updateCustomerSysAccount(CustomerSysAccount customerSysAccount);

    /**
     * 批量删除客户服务系统账号
     * 
     * @param ids 需要删除的客户服务系统账号主键集合
     * @return 结果
     */
    public int deleteCustomerSysAccountByIds(Long[] ids);

    /**
     * 删除客户服务系统账号信息
     * 
     * @param id 客户服务系统账号主键
     * @return 结果
     */
    public int deleteCustomerSysAccountById(Long id);

    List<CustomerSysAccount> selectByCustomerServiceId(Long customerServiceId);

    long selectCountByCustomerServiceId(Long customerServiceId);
}
