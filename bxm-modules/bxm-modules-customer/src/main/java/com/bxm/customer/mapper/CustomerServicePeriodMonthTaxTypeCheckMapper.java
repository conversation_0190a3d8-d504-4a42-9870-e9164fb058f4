package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck;
import org.apache.ibatis.annotations.Param;

/**
 * 服务月账期税种核定Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Mapper
public interface CustomerServicePeriodMonthTaxTypeCheckMapper extends BaseMapper<CustomerServicePeriodMonthTaxTypeCheck>
{
    /**
     * 查询服务月账期税种核定
     * 
     * @param id 服务月账期税种核定主键
     * @return 服务月账期税种核定
     */
    public CustomerServicePeriodMonthTaxTypeCheck selectCustomerServicePeriodMonthTaxTypeCheckById(Long id);

    /**
     * 查询服务月账期税种核定列表
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 服务月账期税种核定集合
     */
    public List<CustomerServicePeriodMonthTaxTypeCheck> selectCustomerServicePeriodMonthTaxTypeCheckList(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 新增服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    public int insertCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 修改服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    public int updateCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 删除服务月账期税种核定
     * 
     * @param id 服务月账期税种核定主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthTaxTypeCheckById(Long id);

    /**
     * 批量删除服务月账期税种核定
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthTaxTypeCheckByIds(Long[] ids);

    void saveNewPeriodTaxCheckByCustomer(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodTaxCheckBySmallCustomer(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodTaxCheckByCommlyCustomer(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodTaxCheckByByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                    @Param("periodStart") Integer periodStart,
                                                    @Param("periodEnd") Integer periodEnd);

    void saveNewPeriodTaxCheckBySmallCustomerAndCustomerServiceId(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                                  @Param("periodStart") Integer periodStart,
                                                                  @Param("periodEnd") Integer periodEnd);

    void saveNewPeriodTaxCheckByCommlyCustomerAndCustomerServiceId(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                                   @Param("periodStart") Integer periodStart,
                                                                   @Param("periodEnd") Integer periodEnd);
}
