package com.bxm.customer.service.strategy.importoperation;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationRequest;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.dto.valueAdded.BatchOperationErrorDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.ValueAddedExportOperationType;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import com.bxm.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 扣款导入策略实现
 *
 * 处理扣款操作的导入逻辑：
 * 1. 验证交付单状态必须为"已确认待扣款"
 * 2. 修改状态为"已扣款"
 * 3. 保存相关附件文件到value_added_file表
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class DeductionImportStrategy implements ImportOperationStrategy {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedFileService fileService;

    @Override
    public ValueAddedExportOperationType getSupportedOperationType() {
        return ValueAddedExportOperationType.DEDUCTION;
    }

    @Override
    public BatchImportOperationResult executeImport(
            List<ValueAddedDeliveryOrder> orders,
            BatchImportOperationRequest request,
            Map<String, Object> templateData,
            Map<String, String> extractedFiles) {

        log.info("Starting deduction import operation, order count: {}", orders.size());

        LocalDateTime startTime = LocalDateTime.now();
        List<String> successOrderNos = new ArrayList<>();
        List<BatchOperationErrorDTO> errors = new ArrayList<>();

        // 验证模板数据
        try {
            validateTemplateData(templateData, request.getDeliveryOrderNoList());
        } catch (Exception e) {
            log.error("Template data validation failed: {}", e.getMessage());
            throw new IllegalArgumentException("Template data validation failed: " + e.getMessage());
        }

        // 处理每个交付单
        for (ValueAddedDeliveryOrder order : orders) {
            try {
                // 验证交付单状态
                validateOrderStatus(order);

                // 修改状态为已扣款
                String targetStatus = getTargetStatus(order.getStatus());
                if (targetStatus != null) {
                    StatusChangeRequestDTO changeRequest = StatusChangeRequestDTO.builder()
                            .deliveryOrderNo(order.getDeliveryOrderNo())
                            .targetStatus(targetStatus)
                            .reason("批量扣款导入")
                            .operatorId(SecurityUtils.getUserId())
                            .operatorName(SecurityUtils.getUsername())
                            .remark("批量扣款导入操作")
                            .build();

                    deliveryOrderService.changeStatus(changeRequest);
                    log.info("Delivery order status changed successfully: {} -> {}", order.getDeliveryOrderNo(), targetStatus);
                }

                // Process file saving (deduction related voucher files)
                if (extractedFiles != null && !extractedFiles.isEmpty()) {
                    int savedFileCount = processFileSaving(order, extractedFiles, request);
                    log.info("Delivery order {} saved deduction voucher file count: {}", order.getDeliveryOrderNo(), savedFileCount);
                }

                successOrderNos.add(order.getDeliveryOrderNo());

            } catch (Exception e) {
                log.warn("Delivery order {} deduction processing failed: {}", order.getDeliveryOrderNo(), e.getMessage());
                errors.add(BatchOperationErrorDTO.builder()
                        .deliveryOrderNo(order.getDeliveryOrderNo())
                        .customerName(order.getCustomerName())
                        .build());
            }
        }

        LocalDateTime endTime = LocalDateTime.now();
        long processingTime = java.time.Duration.between(startTime, endTime).toMillis();

        return BatchImportOperationResult.builder()
                .operationDescription(getOperationDescription())
                .totalCount(orders.size())
                .successCount(successOrderNos.size())
                .errorCount(errors.size())
                .successOrderNos(successOrderNos)
                .errors(errors)
                .startTime(startTime)
                .endTime(endTime)
                .processingTimeMs(processingTime)
                .build();
    }

    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        if (!ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow deduction operation, current status: %s, required status: %s",
                            order.getDeliveryOrderNo(),
                            ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription(),
                            ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getDescription())
            );
        }
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 扣款操作：已确认待扣款 -> 已扣款
        if (ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode();
        }
        return null;
    }

    @Override
    public void validateTemplateData(Map<String, Object> templateData, List<String> orderNos) {
        // Validate template data consistency with request parameters
        if (templateData == null || templateData.isEmpty()) {
            throw new IllegalArgumentException("Template data cannot be empty");
        }

        // Deduction operation template validation:
        // 1. Validate delivery result field
        // 2. Validate deduction amount and other key information
        log.info("Deduction template data validation passed, contains {} delivery orders", orderNos.size());
    }

    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationRequest request) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Check if file name is related to delivery order number
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件（扣款凭证）
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量扣款导入凭证");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("Deduction voucher file saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("Deduction voucher file save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 扣款操作的文件通常是扣款凭证或相关证明文件
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        String upperFileName = fileName.toUpperCase();
        String upperOrderNo = deliveryOrderNo.toUpperCase();

        // Deduction file matching rules:
        // 1. File name contains delivery order number
        // 2. Or file name contains keywords like "deduction", "voucher"
        return upperFileName.contains(upperOrderNo) ||
               upperFileName.contains("DEDUCTION") ||
               upperFileName.contains("VOUCHER") ||
               upperFileName.contains("PAYMENT") ||
               upperFileName.contains("RECEIPT");
    }
}
