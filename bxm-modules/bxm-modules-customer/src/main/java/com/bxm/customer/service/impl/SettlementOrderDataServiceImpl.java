package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.accountingCashier.BankPaymentResult;
import com.bxm.common.core.enums.inAccount.InAccountDeliverResult;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.SettlementOrderData;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataCountDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.mapper.SettlementOrderDataMapper;
import com.bxm.customer.service.ICBusinessTagRelationService;
import com.bxm.customer.service.ISettlementOrderDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算单关联数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
public class SettlementOrderDataServiceImpl extends ServiceImpl<SettlementOrderDataMapper, SettlementOrderData> implements ISettlementOrderDataService
{
    @Autowired
    private SettlementOrderDataMapper settlementOrderDataMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    /**
     * 查询结算单关联数据
     * 
     * @param id 结算单关联数据主键
     * @return 结算单关联数据
     */
    @Override
    public SettlementOrderData selectSettlementOrderDataById(Long id)
    {
        return settlementOrderDataMapper.selectSettlementOrderDataById(id);
    }

    /**
     * 查询结算单关联数据列表
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结算单关联数据
     */
    @Override
    public List<SettlementOrderData> selectSettlementOrderDataList(SettlementOrderData settlementOrderData)
    {
        return settlementOrderDataMapper.selectSettlementOrderDataList(settlementOrderData);
    }

    /**
     * 新增结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    @Override
    public int insertSettlementOrderData(SettlementOrderData settlementOrderData)
    {
        settlementOrderData.setCreateTime(DateUtils.getNowDate());
        return settlementOrderDataMapper.insertSettlementOrderData(settlementOrderData);
    }

    /**
     * 修改结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    @Override
    public int updateSettlementOrderData(SettlementOrderData settlementOrderData)
    {
        settlementOrderData.setUpdateTime(DateUtils.getNowDate());
        return settlementOrderDataMapper.updateSettlementOrderData(settlementOrderData);
    }

    /**
     * 批量删除结算单关联数据
     * 
     * @param ids 需要删除的结算单关联数据主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderDataByIds(Long[] ids)
    {
        return settlementOrderDataMapper.deleteSettlementOrderDataByIds(ids);
    }

    /**
     * 删除结算单关联数据信息
     * 
     * @param id 结算单关联数据主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderDataById(Long id)
    {
        return settlementOrderDataMapper.deleteSettlementOrderDataById(id);
    }

    @Override
    @Transactional
    public void removeAndSaveByBatchNoAndBusinessId(String batchNo, Long businessDeptId, Long settlementOrderId, Integer settlementType, Boolean isSupplement) {
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode())) {
            if (isSupplement) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(Collections.singletonList(settlementOrderId), BusinessSettlementStatus.SETTLED.getCode());
            } else {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(Collections.singletonList(settlementOrderId), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
            }
        } else if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            settlementOrderDataMapper.updateInAccountDataPrepayStatus(Collections.singletonList(settlementOrderId), PeriodPrepayStatus.UNPREPAID.getCode());
        } else {
            settlementOrderDataMapper.updateNewUserPrepaymentDataSettlementStatus(Collections.singletonList(settlementOrderId), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
        }
        settlementOrderDataMapper.delete(new LambdaQueryWrapper<SettlementOrderData>()
                .eq(SettlementOrderData::getSettlementOrderId, settlementOrderId));
        settlementOrderDataMapper.saveBySettlementOrderDataTemp(batchNo, businessDeptId, settlementOrderId, settlementType, isSupplement);
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode())) {
            if (isSupplement) {
                customerServicePeriodMonthMapper.updateSettlementStatusBySettlementOrderDatas(settlementOrderId, BusinessSettlementStatus.SUPPLEMENT_SETTLEMENTING.getCode());
            } else {
                customerServicePeriodMonthMapper.updateSettlementStatusBySettlementOrderDatas(settlementOrderId, BusinessSettlementStatus.SETTLEMENTING.getCode());
            }
        } else if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            customerServicePeriodMonthMapper.updatePrepayStatusBySettlementOrderDatas(settlementOrderId, PeriodPrepayStatus.PREPAID.getCode());
        } else {
            customerServiceMapper.updateSettlementStatusBySettlementOrderDatas(settlementOrderId);
        }
    }

    @Override
    public IPage<SettlementOrderDataDTO> settlementOrderDataListBySettlementOrderId(SettlementOrderDataSearchVO vo) {
        IPage<SettlementOrderDataDTO> iPage = new Page<>(vo.getPageNum(), vo.getPageSize());
        buildSearchVO(vo);
        List<SettlementOrderDataDTO> data = settlementOrderDataMapper.settlementOrderDataListBySettlementOrderId(iPage, vo);
        if (!ObjectUtils.isEmpty(data)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(SettlementOrderDataDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
            List<Long> periodIds = data.stream().filter(d -> d.getBusinessType() == 2).map(SettlementOrderDataDTO::getBusinessId).collect(Collectors.toList());
            Map<Long, List<TagDTO>> periodTagMap = ObjectUtils.isEmpty(periodIds) ? Maps.newHashMap() :
                    businessTagRelationService.getTagsByBusinessTypeForList(periodIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            data.forEach(d -> {
                d.setCustomerServiceTags(tagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
                d.setCustomerServiceTaxTypeStr(TaxType.getByCode(d.getCustomerServiceTaxType()).getDesc());
                d.setCustomerServiceAdvisorInfo((StringUtils.isEmpty(d.getCustomerServiceAdvisorDeptName()) ? "" : d.getCustomerServiceAdvisorDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAdvisorEmployeeName()) ? "" : "（" + d.getCustomerServiceAdvisorEmployeeName() + "）"));
                d.setCustomerServiceAccountingInfo((StringUtils.isEmpty(d.getCustomerServiceAccountingDeptName()) ? "" : d.getCustomerServiceAccountingDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAccountingEmployeeName()) ? "" : "（" + d.getCustomerServiceAccountingEmployeeName() + "）"));
                d.setCustomerServiceFirstAccountPeriod(StringUtils.isEmpty(d.getCustomerServiceFirstAccountPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getCustomerServiceFirstAccountPeriod())));
                d.setPeriod(StringUtils.isEmpty(d.getPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getPeriod())));
                d.setPeriodServiceTypeStr(CustomerServicePeriodMonthServiceType.getByCode(d.getPeriodServiceType()).getName());
                d.setPeriodTaxTypeStr(TaxType.getByCode(d.getPeriodTaxType()).getDesc());
                d.setPeriodTags(d.getBusinessType() == 2 ? periodTagMap.getOrDefault(d.getBusinessId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")) : "");
                d.setPeriodAdvisorInfo(StringUtils.isEmpty(d.getPeriodAdvisorDeptName()) ? "" : d.getPeriodAdvisorDeptName() + (StringUtils.isEmpty(d.getPeriodAdvisorEmployeeName()) ? "" : "（" + d.getPeriodAdvisorEmployeeName() + "）"));
                d.setPeriodAccountingInfo(StringUtils.isEmpty(d.getPeriodAccountingDeptName()) ? "" : d.getPeriodAccountingDeptName() + (StringUtils.isEmpty(d.getPeriodAccountingEmployeeName()) ? "" : "（" + d.getPeriodAccountingEmployeeName() + "）"));
                d.setPeriodAccountStatusStr(AccountingStatus.getByCode(d.getPeriodAccountStatus()).getName());
                d.setPeriodInAccountDeliverResultStr(AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountDeliverResult()).getName());
                d.setPeriodBankPaymentInputResultStr(Objects.isNull(d.getPeriodBankPaymentInputResult()) ? "" : BankPaymentResult.getByCode(d.getPeriodBankPaymentInputResult()).getDesc());
                d.setPeriodInAccountStatusStr(Objects.isNull(d.getPeriodInAccountStatus()) ? "" : AccountingCashierDeliverStatus.getByCode(d.getPeriodInAccountStatus()).getName());
                d.setPeriodInAccountResultStr(Objects.isNull(d.getPeriodInAccountResult()) ? "" : AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountResult()).getName());
                d.setCreateInfo((StringUtils.isEmpty(d.getCreateDeptName()) ? "" : d.getCreateDeptName()) + (StringUtils.isEmpty(d.getCreateBy()) ? "" : "（" + d.getCreateBy() + "）"));
                d.setPeriodSettlementStatusStr(Objects.isNull(d.getPeriodSettlementStatus()) ? "" : BusinessSettlementStatus.getByCode(d.getPeriodSettlementStatus()).getShowName());
                d.setPeriodPrepayStatusStr(Objects.isNull(d.getPeriodPrepayStatus()) ? "" : PeriodPrepayStatus.getByCode(d.getPeriodPrepayStatus()).getDesc());
            });
        }
        iPage.setRecords(data);
        return iPage;
    }

    @Override
    public List<SettlementOrderDataCountDTO> selectBatchSettlementOrderDataCount(List<Long> settlementOrderIds) {
        if (ObjectUtils.isEmpty(settlementOrderIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchSettlementOrderDataCount(settlementOrderIds);
    }

    private void buildSearchVO(SettlementOrderDataSearchVO vo) {
        if (!StringUtils.isEmpty(vo.getCustomerServiceTagName())) {
            if (!vo.getCustomerServiceTagName().contains("&")) {
                vo.setCustomerServiceTagType(1);
                vo.setCustomerServiceTagNames(vo.getCustomerServiceTagName());
                vo.setCustomerTagCount(1);
            } else {
                vo.setCustomerServiceTagType(2);
                vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
                vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("&").length);
//                if (vo.getCustomerServiceTagNames().contains("&")) {
//                    vo.setCustomerServiceTagType(2);
//                    vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("&").length);
//                } else {
//                    vo.setCustomerServiceTagType(3);
//                    vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("/").length);
//                }
            }
        }
        if (!StringUtils.isEmpty(vo.getPeriodStart())) {
            vo.setPeriodStart(DateUtils.yearMonthToPeriod(vo.getPeriodStart()) + "");
        }
        if (!StringUtils.isEmpty(vo.getPeriodEnd())) {
            vo.setPeriodEnd(DateUtils.yearMonthToPeriod(vo.getPeriodEnd()) + "");
        }
        if (!StringUtils.isEmpty(vo.getCustomerServiceFirstPeriodStart())) {
            vo.setCustomerServiceFirstPeriodStart(DateUtils.yearMonthToPeriod(vo.getCustomerServiceFirstPeriodStart()) + "");
        }
        if (!StringUtils.isEmpty(vo.getCustomerServiceFirstPeriodEnd())) {
            vo.setCustomerServiceFirstPeriodEnd(DateUtils.yearMonthToPeriod(vo.getCustomerServiceFirstPeriodEnd()) + "");
        }
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            if (!vo.getPeriodTagName().contains("&")) {
                vo.setPeriodTagType(1);
                vo.setPeriodTagNames(vo.getPeriodTagName());
                vo.setPeriodTagCount(1);
            } else {
                vo.setPeriodTagType(2);
                vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
                vo.setPeriodTagCount(vo.getPeriodTagName().split("&").length);
//                if (vo.getPeriodTagNames().contains("&")) {
//                    vo.setPeriodTagType(2);
//                    vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setPeriodTagCount(vo.getPeriodTagName().split("&").length);
//                } else {
//                    vo.setPeriodTagType(3);
//                    vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setPeriodTagCount(vo.getPeriodTagName().split("/").length);
//                }
            }
        }
    }
}
