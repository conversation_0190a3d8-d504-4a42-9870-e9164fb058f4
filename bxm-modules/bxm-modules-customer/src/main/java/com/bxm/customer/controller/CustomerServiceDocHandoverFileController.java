package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceDocHandoverFile;
import com.bxm.customer.domain.vo.docHandover.file.FileByDocHandoverVO;
import com.bxm.customer.domain.vo.docHandover.file.SaveFileVO;
import com.bxm.customer.service.ICustomerServiceDocHandoverFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 材料、资料 附件Controller
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@RestController
@RequestMapping("/handoverFile")
@Api(tags = "材料、资料 附件")
public class CustomerServiceDocHandoverFileController extends BaseController {
    @Autowired
    private ICustomerServiceDocHandoverFileService customerServiceDocHandoverFileService;

    /**
     * 查询材料、资料 附件列表
     */
    @RequiresPermissions("customer:file:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询材料、资料 附件列表", notes = "查询材料、资料 附件列表")
    public TableDataInfo list(CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        startPage();
        List<CustomerServiceDocHandoverFile> list = customerServiceDocHandoverFileService.selectCustomerServiceDocHandoverFileList(customerServiceDocHandoverFile);
        return getDataTable(list);
    }

    /**
     * 导出材料、资料 附件列表
     */
    @RequiresPermissions("customer:file:export")
    @Log(title = "材料、资料 附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出材料、资料 附件列表", notes = "导出材料、资料 附件列表")
    public void export(HttpServletResponse response, CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        List<CustomerServiceDocHandoverFile> list = customerServiceDocHandoverFileService.selectCustomerServiceDocHandoverFileList(customerServiceDocHandoverFile);
        ExcelUtil<CustomerServiceDocHandoverFile> util = new ExcelUtil<CustomerServiceDocHandoverFile>(CustomerServiceDocHandoverFile.class);
        util.exportExcel(response, list, "材料、资料 附件数据");
    }

    /**
     * 获取材料、资料 附件详细信息
     */
    @RequiresPermissions("customer:file:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取材料、资料 附件详细信息", notes = "获取材料、资料 附件详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(customerServiceDocHandoverFileService.selectCustomerServiceDocHandoverFileById(id));
    }

    /**
     * 新增材料、资料 附件
     */
    @RequiresPermissions("customer:file:add")
    @Log(title = "材料、资料 附件", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增材料、资料 附件", notes = "新增材料、资料 附件")
    public AjaxResult add(@RequestBody CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        return toAjax(customerServiceDocHandoverFileService.insertCustomerServiceDocHandoverFile(customerServiceDocHandoverFile));
    }

    /**
     * 修改材料、资料 附件
     */
    @RequiresPermissions("customer:file:edit")
    @Log(title = "材料、资料 附件", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改材料、资料 附件", notes = "修改材料、资料 附件")
    public AjaxResult edit(@RequestBody CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        return toAjax(customerServiceDocHandoverFileService.updateCustomerServiceDocHandoverFile(customerServiceDocHandoverFile));
    }

    /**
     * 删除材料、资料 附件
     */
    @RequiresPermissions("customer:file:remove")
    @Log(title = "材料、资料 附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除材料、资料 附件", notes = "删除材料、资料 附件")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(customerServiceDocHandoverFileService.deleteCustomerServiceDocHandoverFileByIds(ids));
    }

    //****** start self method ******
    @GetMapping("/selectByDocHandover")
    @ApiOperation(value = "根据 材料ID 获取附件", notes = "根据 材料ID 获取附件")
    public Result<List<CustomerServiceDocHandoverFile>> selectByDocHandover(@RequestHeader("deptId") Long deptId,
                                                                            FileByDocHandoverVO vo
    ) {
        return Result.ok(
                customerServiceDocHandoverFileService.selectByDocHandover(vo.getCustomerServiceDocHandoverId(), vo.getDocHandoverFileTypes())
        );
    }

    @GetMapping("/selectMapByDocHandover")
    @ApiOperation(value = "根据 材料ID 获取附件，并根据类型转成MAP", notes = "根根据 材料ID 获取附件，并根据类型转成MAP")
    public Result<Map<Integer, List<CustomerServiceDocHandoverFile>>> selectMapByDocHandover(@RequestHeader("deptId") Long deptId,
                                                                                             FileByDocHandoverVO vo
    ) {
        return Result.ok(
                customerServiceDocHandoverFileService.selectMapByDocHandover(vo.getCustomerServiceDocHandoverId(), vo.getDocHandoverFileTypes())
        );
    }

    @GetMapping("/selectMapSubKeyByDocHandover")
    @ApiOperation(value = "根据 材料ID 获取附件，MAP，key是主类型+子类型", notes = "根据 材料ID 获取附件，MAP，key是主类型+子类型P")
    public Result<Map<String, List<CustomerServiceDocHandoverFile>>> selectMapSubKeyByDocHandover(@RequestHeader("deptId") Long deptId,
                                                                                                  FileByDocHandoverVO vo
    ) {
        return Result.ok(
                customerServiceDocHandoverFileService.selectMapSubKeyByDocHandover(vo.getCustomerServiceDocHandoverId(), vo.getDocHandoverFileTypes())
        );
    }

    @PostMapping("/deleteByDocHandoverId")
    @ApiOperation(value = "根据 材料ID 删除附件", notes = "根据 材料ID 删除附件")
    public Result<Boolean> deleteByDocHandoverId(@RequestHeader("deptId") Long deptId, FileByDocHandoverVO vo) {
        customerServiceDocHandoverFileService.deleteByDocHandoverId(vo.getCustomerServiceDocHandoverId(), vo.getDocHandoverFileTypes());
        return Result.ok();
    }

    @PostMapping("/saveFile")
    @ApiOperation(value = "保存附件", notes = "保存附件")
    public Result<Boolean> saveFile(@RequestHeader("deptId") Long deptId, SaveFileVO vo) {
        customerServiceDocHandoverFileService.saveFile(
                vo.getCustomerServiceDocId(),
                vo.getFiles(),
                vo.getDocHandoverFileType(),
                vo.getSubFileType()
        );
        return Result.ok();
    }

    @PostMapping("/covToCommonFileVO")
    @ApiOperation(value = "原始附件处理成前端可用数据", notes = "原始附件处理成前端可用数据")
    public Result<List<CommonFileVO>> covToCommonFileVO(@RequestHeader("deptId") Long deptId, List<CustomerServiceDocHandoverFile> files) {
        return Result.ok(customerServiceDocHandoverFileService.covToCommonFileVO(files));
    }
}
