package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.inAccount.InAccountDeliverResult;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonOperDTO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.settlementOrder.*;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementBusinessPriceGetVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderCreateVO;
import com.bxm.customer.domain.vo.settlementOrder.task.BusinessDeptPeriodListForTaskVO;
import com.bxm.customer.domain.vo.settlementOrder.task.DeleteBusinessPeriodListForTaskVO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 结算单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
@Slf4j
public class SettlementOrderServiceImpl extends ServiceImpl<SettlementOrderMapper, SettlementOrder> implements ISettlementOrderService {
    private static final String PERIOD_TAX_TYPE = "periodTaxType";

    private static final String PERIOD_BANK_PAYMENT_INPUT_TIME = "periodBankPaymentInputTime";

    private static final String PERIOD_IN_ACCOUNT_IN_TIME = "periodInAccountInTime";

    private static final String PERIOD_IN_ACCOUNT_END_TIME = "periodInAccountEndTime";

    private static final String PERIOD_IN_ACCOUNT_STATUS = "periodInAccountStatus";

    private static final String PERIOD_IN_ACCOUNT_DELIVER_RESULT = "periodInAccountDeliverResult";

    private static final String SETTLEMENT_STATUS = "settlementStatus";

    private static final String PERIOD_SERVICE_TYPE = "periodServiceType";

    private static final String PERIOD_ACCOUNTING_STATUS = "periodAccountingStatus";

    private static final String PERIOD_TAGS = "periodTags";

    private static final String PERIOD_TIME = "periodTime";

    private static final String CUSTOMER_SERVICE_TAX_TYPE = "customerServiceTaxType";

    private static final String CUSTOMER_SERVICE_TAGS = "customerServiceTags";

    private static final String PERIOD_RANGE = "periodRange";

    private static final String CUSTOMER_SERVICE_CREATE_TIME = "customerServiceCreateTime";

    private static final String CUSTOMER_SERVICE_FIRST_ACCOUNT_PERIOD = "customerServiceFirstAccountPeriod";

    private static final String CUSTOMER_SERVICE_RANGE = "customerServiceRange";

    private static final String COMMON_START_TIME = "startTime";

    private static final String COMMON_END_TIME = "endTime";

    private static final String PERIOD_IN_ACCOUNT_RESULT = "periodInAccountResult";

    private static final String PERIOD_BANK_PAYMENT_INPUT_RESULT = "periodBankPaymentInputResult";

    private static final String PERIOD_PREPAY_STATUS = "periodPrepayStatus";

    private static final String CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID = "customerServiceAccountingTopDeptIds";

    private static final String PERIOD_ACCOUNTING_TOP_DEPT_ID = "periodAccountingTopDeptIds";

    private static final List<String> singleConditionKeys = Lists.newArrayList(PERIOD_TAX_TYPE, PERIOD_IN_ACCOUNT_DELIVER_RESULT, PERIOD_SERVICE_TYPE, PERIOD_ACCOUNTING_STATUS, CUSTOMER_SERVICE_TAX_TYPE, PERIOD_IN_ACCOUNT_RESULT, PERIOD_BANK_PAYMENT_INPUT_RESULT, SETTLEMENT_STATUS, PERIOD_PREPAY_STATUS, CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID, PERIOD_ACCOUNTING_TOP_DEPT_ID, PERIOD_IN_ACCOUNT_STATUS);

    private static final List<String> timeConditionKeys = Lists.newArrayList(PERIOD_BANK_PAYMENT_INPUT_TIME, PERIOD_IN_ACCOUNT_IN_TIME, PERIOD_IN_ACCOUNT_END_TIME, CUSTOMER_SERVICE_CREATE_TIME, CUSTOMER_SERVICE_FIRST_ACCOUNT_PERIOD, PERIOD_TIME);

    private static final List<String> tagConditionKeys = Lists.newArrayList(PERIOD_TAGS, CUSTOMER_SERVICE_TAGS);

    private static final List<String> fileConditionKeys = Lists.newArrayList(PERIOD_RANGE, CUSTOMER_SERVICE_RANGE);

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private SettlementOrderMapper settlementOrderMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    private FileService fileService;

    @Autowired
    private SettlementOrderDataTempMapper settlementOrderDataTempMapper;

    @Autowired
    private ISettlementOrderDataTempService settlementOrderDataTempService;

    @Autowired
    private ISettlementOrderFileService settlementOrderFileService;

    @Autowired
    private ISettlementOrderConditionService settlementOrderConditionService;

    @Autowired
    private ISettlementOrderDataService settlementOrderDataService;

    @Autowired
    private SettlementOrderDataMapper settlementOrderDataMapper;

    @Autowired
    private IBillSettlementOrderRelationService billSettlementOrderRelationService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    private static final String ACCESS_KEY = "LTAI5t9TQPHCxG8vU9XXjfWa";

    private static final String SECRET_KEY = "******************************";

    private static final String ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";

    private static final String INNER_ENDPOINT = "oss-cn-hangzhou-internal.aliyuncs.com";

    private static final String BUCKET_NAME = "bxm410";

    /**
     * 查询结算单
     *
     * @param id 结算单主键
     * @return 结算单
     */
    @Override
    public SettlementOrder selectSettlementOrderById(Long id) {
        return settlementOrderMapper.selectSettlementOrderById(id);
    }

    /**
     * 查询结算单列表
     *
     * @param settlementOrder 结算单
     * @return 结算单
     */
    @Override
    public List<SettlementOrder> selectSettlementOrderList(SettlementOrder settlementOrder) {
        return settlementOrderMapper.selectSettlementOrderList(settlementOrder);
    }

    /**
     * 新增结算单
     *
     * @param settlementOrder 结算单
     * @return 结果
     */
    @Override
    public int insertSettlementOrder(SettlementOrder settlementOrder) {
        settlementOrder.setCreateTime(DateUtils.getNowDate());
        return settlementOrderMapper.insertSettlementOrder(settlementOrder);
    }

    /**
     * 修改结算单
     *
     * @param settlementOrder 结算单
     * @return 结果
     */
    @Override
    public int updateSettlementOrder(SettlementOrder settlementOrder) {
        settlementOrder.setUpdateTime(DateUtils.getNowDate());
        return settlementOrderMapper.updateSettlementOrder(settlementOrder);
    }

    /**
     * 批量删除结算单
     *
     * @param ids 需要删除的结算单主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderByIds(Long[] ids) {
        return settlementOrderMapper.deleteSettlementOrderByIds(ids);
    }

    /**
     * 删除结算单信息
     *
     * @param id 结算单主键
     * @return 结果
     */
    @Override
    public int deleteSettlementOrderById(Long id) {
        return settlementOrderMapper.deleteSettlementOrderById(id);
    }

    @Override
    public SettlementOrderDeptPriceDTO businessDeptPriceList(SettlementBusinessPriceGetVO vo) {
        String batchNo;
        if (!Objects.isNull(vo.getId())) {
            SettlementOrder settlementOrder = getById(vo.getId());
            if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
                throw new ServiceException("结算单不存在");
            }
            batchNo = settlementOrder.getSettlementBatchNo();
        } else {
            if (!StringUtils.isEmpty(vo.getBatchNo())) {
                batchNo = vo.getBatchNo();
            } else {
                batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            }
        }
        if (!ObjectUtils.isEmpty(vo.getSettlementConditions()) && vo.getSettlementConditions().stream().anyMatch(c -> Objects.equals(c.getConditionType(), PERIOD_TAGS))) {
            SettlementOrderCondition condition = vo.getSettlementConditions().stream().filter(c -> Objects.equals(c.getConditionType(), PERIOD_TAGS)).findFirst().get();
            String tagName = getConditionValue(condition.getConditionValue()).get("tagName");
            if (tagName.contains("/") && tagName.contains("&")) {
                throw new ServiceException("标签条件不支持");
            }
        }
        if (!ObjectUtils.isEmpty(vo.getSettlementConditions()) && vo.getSettlementConditions().stream().anyMatch(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS))) {
            SettlementOrderCondition condition = vo.getSettlementConditions().stream().filter(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS)).findFirst().get();
            String tagName = getConditionValue(condition.getConditionValue()).get("tagName");
            if (tagName.contains("/") && tagName.contains("&")) {
                throw new ServiceException("标签条件不支持");
            }
        }
        if (!Objects.isNull(vo.getId()) || !StringUtils.isEmpty(vo.getBatchNo())) {
            settlementOrderDataTempMapper.delete(new LambdaQueryWrapper<SettlementOrderDataTemp>().eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo)
                    .in(SettlementOrderDataTemp::getBusinessDeptId, vo.getBusinessDeptIds()));
        }
        String insertSql;
        if (Objects.equals(vo.getSettlementType(), SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(vo.getSettlementType(), SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            insertSql = "insert into c_settlement_order_data_temp " + buildPeriodMonthConditionSql(vo.getSettlementConditions(), vo.getBusinessDeptIds(), vo.getIsSupplement(), batchNo, vo.getId(), SettlementType.getByCode(vo.getSettlementType()));
        } else {
            insertSql = "insert into c_settlement_order_data_temp " + buildCustomerServiceConditionSql(vo.getSettlementConditions(), vo.getBusinessDeptIds(), batchNo, vo.getId());
        }
        settlementOrderDataTempMapper.insertBySql(insertSql);
        List<SysDept> businessDeptList = remoteDeptService.getByDeptIds(vo.getBusinessDeptIds()).getDataThrowException();
        return SettlementOrderDeptPriceDTO.builder()
                .batchNo(batchNo)
                .businessDeptPriceList(settlementOrderDataTempService.getBusinessDeptPriceDataByBatchNo(batchNo, businessDeptList, SettlementType.getByCode(vo.getSettlementType()).getUnit(), vo.getPrice()))
                .build();
    }

    @Override
    public BusinessPeriodListForTaskDTO businessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo) {
        String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
        String insertSql = "insert into c_settlement_order_data_temp " + buildPeriodMonthConditionSql(vo.getSettlementConditions(), null, null, batchNo, null, SettlementType.TASK_PERIOD);

        //重复任务不派
        insertSql += " and c_customer_service_period_month.id not in (select distinct biz_id from c_business_task where is_del = 0 and status != 3 and status != 4 and type = " + vo.getType() + " and item_type = " + vo.getItemType() + ")";

        settlementOrderDataTempMapper.insertBySql(insertSql);
        return BusinessPeriodListForTaskDTO.builder()
                .batchNo(batchNo)
                .result(settlementOrderDataTempService.getResultByBatchNo(batchNo))
                .build();
    }

    @Override
    public BusinessPeriodListForTaskDTO freshBusinessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo) {
        return BusinessPeriodListForTaskDTO.builder()
                .batchNo(vo.getBatchNo())
                .result(settlementOrderDataTempService.getResultByBatchNo(vo.getBatchNo()))
                .build();
    }

    @Override
    public void deleteBusinessPeriodListForTask(DeleteBusinessPeriodListForTaskVO vo) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return;
        }
        settlementOrderDataTempService.removeByIds(vo.getIds());
    }

    @Override
    public SettlementOrderDeptPriceDTO freshBusinessDeptPriceList(SettlementBusinessPriceGetVO vo) {
        List<SysDept> businessDeptList = remoteDeptService.getByDeptIds(vo.getBusinessDeptIds()).getDataThrowException();
        return SettlementOrderDeptPriceDTO.builder()
                .batchNo(vo.getBatchNo())
                .businessDeptPriceList(settlementOrderDataTempService.getBusinessDeptPriceDataByBatchNo(vo.getBatchNo(), businessDeptList, SettlementType.getByCode(vo.getSettlementType()).getUnit(), vo.getPrice()))
                .build();
    }

    @Override
    @Transactional
    public void createSettlementOrder(SettlementOrderCreateVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessPrice())) {
            return;
        }
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        vo.getBusinessPrice().forEach(dto -> createSettlementOrderByBusinessDeptPrice(dto, vo, deptId, userId, employeeName));
        settlementOrderDataTempMapper.delete(new LambdaQueryWrapper<SettlementOrderDataTemp>().eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, vo.getBatchNo()));
    }

    @Override
    @Transactional
    public void modifySettlementOrder(SettlementOrderCreateVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessPrice())) {
            return;
        }
        SettlementBusinessDeptPriceDTO dto = vo.getBusinessPrice().get(0);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        modifySettlementOrderByBusinessDeptPrice(dto, vo, deptId, userId, employeeName);
        settlementOrderDataTempMapper.delete(new LambdaQueryWrapper<SettlementOrderDataTemp>().eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, vo.getBatchNo())
                .eq(SettlementOrderDataTemp::getBusinessDeptId, dto.getBusinessDeptId()));
    }

    @Override
    public String uploadDetailFile(MultipartFile file, Integer settlementType, Integer isSupplement, String batchNo, Long settlementOrderId, Long businessDeptId) {
        try {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode()) || Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
                ExcelUtil<SettlementOrderPeriodUploadDTO> util = new ExcelUtil<>(SettlementOrderPeriodUploadDTO.class);
                List<SettlementOrderPeriodUploadDTO> periodDataList = util.importExcel(file.getInputStream());
                SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, (long) periodDataList.size());
                redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
                asyncService.checkPeriodDatas(periodDataList, uuid, isSupplement, batchNo, settlementOrderId, businessDeptId, settlementType);
            } else {
                ExcelUtil<SettlementOrderCustomerUploadDTO> util = new ExcelUtil<>(SettlementOrderCustomerUploadDTO.class);
                List<SettlementOrderCustomerUploadDTO> customerDataList = util.importExcel(file.getInputStream());
                SettlementOrderUploadCheckResultDTO checkResultDTO = new SettlementOrderUploadCheckResultDTO(uuid, (long) customerDataList.size());
                redisService.setCacheObject(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT + uuid, checkResultDTO, 60 * 60L, TimeUnit.SECONDS);
                asyncService.checkCustomerServiceDatas(customerDataList, uuid, batchNo, settlementOrderId, businessDeptId);
            }
            return uuid;
        } catch (Exception e) {
            log.error("文件解析异常:{}", e.getMessage());
            throw new ServiceException("文件解析异常");
        }
    }

    @Override
    @Transactional
    public AddDataResultDTO confirmAddData(String uploadBatchNo, String batchNo, Long settlementOrderId, Integer settlementType, Long businessDeptId) {
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode())  || Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode()) || Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            List<SettlementOrderPeriodUploadDTO> periodDataList = redisService.getLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT_LIST + uploadBatchNo, 2000);
            if (ObjectUtils.isEmpty(periodDataList)) {
                return new AddDataResultDTO();
            }
            List<SettlementOrderPeriodUploadDTO> successPeriodList = periodDataList.stream().filter(row -> StringUtils.isEmpty(row.getCheckError()) && !Objects.isNull(row.getCustomerServicePeriodMonthId())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(successPeriodList)) {
                return new AddDataResultDTO();
            }
            if (!Objects.isNull(settlementOrderId)) {
                List<Long> customerServicePeriodMonthIds = successPeriodList.stream().map(SettlementOrderPeriodUploadDTO::getCustomerServicePeriodMonthId).collect(Collectors.toList());
                settlementOrderDataMapper.saveByPeriodIds(customerServicePeriodMonthIds, settlementOrderId);
                SettlementOrder settlementOrder = getById(settlementOrderId);
                if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode())) {
                    if (Objects.isNull(settlementOrder.getIsSupplement()) || !settlementOrder.getIsSupplement()) {
                        customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                                .in(CustomerServicePeriodMonth::getId, customerServicePeriodMonthIds)
                                .set(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.SETTLEMENTING.getCode()));
                    } else {
                        customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                                .in(CustomerServicePeriodMonth::getId, customerServicePeriodMonthIds)
                                .set(CustomerServicePeriodMonth::getSettlementStatus, BusinessSettlementStatus.SUPPLEMENT_SETTLEMENTING.getCode()));
                    }
                }
                if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
                    customerServicePeriodMonthService.update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                            .in(CustomerServicePeriodMonth::getId, customerServicePeriodMonthIds)
                            .set(CustomerServicePeriodMonth::getPrepayStatus, PeriodPrepayStatus.PREPAID.getCode()));
                }
            } else {
                settlementOrderDataTempMapper.saveByPeriodIds(successPeriodList.stream().map(SettlementOrderPeriodUploadDTO::getCustomerServicePeriodMonthId).collect(Collectors.toList()), batchNo, businessDeptId);
            }
            return new AddDataResultDTO((long) successPeriodList.size(), (long) successPeriodList.size(), 0L);
        } else {
            List<SettlementOrderCustomerUploadDTO> customerDataList = redisService.getLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT_LIST + uploadBatchNo, 2000);
            if (ObjectUtils.isEmpty(customerDataList)) {
                return new AddDataResultDTO();
            }
            List<SettlementOrderCustomerUploadDTO> successPeriodList = customerDataList.stream().filter(row -> StringUtils.isEmpty(row.getCheckError()) && !Objects.isNull(row.getCustomerServiceId())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(successPeriodList)) {
                return new AddDataResultDTO();
            }
            if (!Objects.isNull(settlementOrderId)) {
//                settlementOrderDataMapper.saveByCustomerIds(successPeriodList.stream().map(SettlementOrderCustomerUploadDTO::getCustomerServiceId).collect(Collectors.toList()), settlementOrderId);
//                customerServiceService.update(new LambdaUpdateWrapper<CCustomerService>()
//                        .eq(CCustomerService::getSettlementStatus, BusinessSettlementStatus.WAIT_SETTLEMENT.getCode())
//                        .in(CCustomerService::getId, successPeriodList.stream().map(SettlementOrderCustomerUploadDTO::getCustomerServiceId).collect(Collectors.toList()))
//                        .set(CCustomerService::getSettlementStatus, BusinessSettlementStatus.SETTLEMENTING.getCode()));
                List<Long> customerServiceIds = successPeriodList.stream().map(SettlementOrderCustomerUploadDTO::getCustomerServiceId).collect(Collectors.toList());
                settlementOrderDataMapper.saveByCustomerIds(customerServiceIds, settlementOrderId);
                customerServiceService.update(new LambdaUpdateWrapper<CCustomerService>()
                        .eq(CCustomerService::getSettlementStatus, BusinessSettlementStatus.WAIT_SETTLEMENT.getCode())
                        .in(CCustomerService::getId, customerServiceIds)
                        .set(CCustomerService::getSettlementStatus, BusinessSettlementStatus.SETTLEMENTING.getCode()));
            } else {
                settlementOrderDataTempMapper.saveByCustomerIds(successPeriodList.stream().map(SettlementOrderCustomerUploadDTO::getCustomerServiceId).collect(Collectors.toList()), batchNo, businessDeptId);
            }
            return new AddDataResultDTO((long) successPeriodList.size(), (long) successPeriodList.size(), 0L);
        }
    }

    @Override
    public List<SettlementPushReviewDTO> pushReview(List<Long> ids) {
        List<SettlementOrder> settlementOrders = baseMapper.selectBatchIds(ids).stream().filter(row -> !row.getIsDel()).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(settlementOrders)) {
            return Lists.newArrayList();
        }
        Map<Long, Long> settlementOrderDataCountMap = settlementOrderDataMapper.selectBatchSettlementOrderDataCount(settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
        settlementOrders.forEach(settlementOrder -> {
            settlementOrder.setDataCount(settlementOrderDataCountMap.getOrDefault(settlementOrder.getId(), 0L));
            settlementOrder.setTotalPrice(settlementOrder.getPrice().multiply(new BigDecimal(settlementOrder.getDataCount())));
            settlementOrder.setSettlementPrice(settlementOrder.getTotalPrice().subtract(Objects.isNull(settlementOrder.getDiscountPrice()) ? BigDecimal.ZERO : settlementOrder.getDiscountPrice()));
        });
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(settlementOrders.stream().map(SettlementOrder::getBusinessTopDeptId).distinct().collect(Collectors.toList()));
        deptIds.addAll(settlementOrders.stream().map(SettlementOrder::getBusinessDeptId).distinct().collect(Collectors.toList()));
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SettlementOrder>> settlementOrderMap = settlementOrders.stream().collect(Collectors.groupingBy(SettlementOrder::getBusinessDeptId));
        List<Long> businessDeptIds = new ArrayList<>(settlementOrderMap.keySet()).stream().sorted(Comparator.comparing(Long::intValue)).collect(Collectors.toList());
        Map<Long, Long> dataCountMap = ObjectUtils.isEmpty(settlementOrders) ? Maps.newHashMap() :
                settlementOrderDataMapper.selectBatchSettlementOrderDataCount(settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SettlementOrderDataCountDTO::getId, SettlementOrderDataCountDTO::getDataCount));
        return businessDeptIds.stream().map(businessDeptId -> {
            List<SettlementOrder> settlementOrderList = settlementOrderMap.get(businessDeptId);
            List<SettlementPushReviewOrderDTO> settlementOrderDTO = settlementOrderList.stream().map(settlementOrder -> {
                Long dataCount = dataCountMap.getOrDefault(settlementOrder.getId(), 0L);
                BigDecimal totalPrice = settlementOrder.getPrice().multiply(new BigDecimal(dataCount));
                return SettlementPushReviewOrderDTO.builder()
                        .settlementOrderId(settlementOrder.getId())
                        .settlementOrderTitle(settlementOrder.getSettlementTitle())
                        .settlementType(settlementOrder.getSettlementType())
                        .settlementTypeName(SettlementType.getByCode(settlementOrder.getSettlementType()).getName() + (settlementOrder.getIsSupplement() ? "（补差）" : ""))
                        .unit(settlementOrder.getUnit())
                        .dataCount(dataCount)
                        .price(settlementOrder.getPrice())
                        .totalPrice(totalPrice)
                        .discountPrice(settlementOrder.getDiscountPrice())
                        .settlementPrice(totalPrice.subtract(Objects.isNull(settlementOrder.getDiscountPrice()) ? BigDecimal.ZERO : settlementOrder.getDiscountPrice()))
                        .build();
            }).collect(Collectors.toList());
            Long businessTopDeptId = settlementOrderList.get(0).getBusinessTopDeptId();
            BigDecimal totalPrice = settlementOrderDTO.stream().map(row -> Objects.isNull(row.getTotalPrice()) ? BigDecimal.ZERO : row.getTotalPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal discountPrice = settlementOrderDTO.stream().map(row -> Objects.isNull(row.getDiscountPrice()) ? BigDecimal.ZERO : row.getDiscountPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
            return SettlementPushReviewDTO.builder()
                    .businessTopDeptId(businessTopDeptId)
                    .businessTopDeptName(deptNameMap.getOrDefault(businessTopDeptId, ""))
                    .businessDeptId(businessDeptId)
                    .businessDeptName(deptNameMap.getOrDefault(businessDeptId, ""))
                    .settlementOrderCount((long) settlementOrderList.size())
                    .totalPrice(totalPrice)
                    .discountPrice(discountPrice)
                    .oughtPrice(totalPrice.subtract(discountPrice))
                    .settlementOrderList(settlementOrderDTO)
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void deleteSettlementOrder(Long id) {
        SettlementOrder settlementOrder = getById(id);
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            throw new ServiceException("结算单不存在");
        }
        if (!SettlementOrderStatus.canDeleteStatus().contains(settlementOrder.getStatus())) {
            throw new ServiceException("结算单状态不符，无法删除");
        }
        updateById(new SettlementOrder().setId(id).setIsDel(true));
        if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode())) {
            if (settlementOrder.getIsSupplement()) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(Collections.singletonList(id), BusinessSettlementStatus.SETTLED.getCode());
            } else {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(Collections.singletonList(id), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
            }
        } else if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            settlementOrderDataMapper.updateInAccountDataPrepayStatus(Collections.singletonList(id), PeriodPrepayStatus.UNPREPAID.getCode());
        } else {
            settlementOrderDataMapper.updateNewUserPrepaymentDataSettlementStatus(Collections.singletonList(id), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
        }
        billSettlementOrderRelationService.deleteBySettlementOrderId(id);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("删除结算单")
                    .setOperName(employeeName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void deleteSettlementOrderByBill(List<Long> settlementOrderIds) {
        List<SettlementOrder> settlementOrders = list(new LambdaQueryWrapper<SettlementOrder>()
                .eq(SettlementOrder::getIsDel, false).in(SettlementOrder::getId, settlementOrderIds));
        if (!ObjectUtils.isEmpty(settlementOrders)) {
            update(new LambdaUpdateWrapper<SettlementOrder>()
                    .in(SettlementOrder::getId, settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()))
                    .set(SettlementOrder::getIsDel, true)
                    .set(SettlementOrder::getBillNo, null)
                    .set(SettlementOrder::getBillTitle, null));
            List<SettlementOrder> inAccountSupplementSettlementOrders = settlementOrders.stream().filter(settlementOrder -> Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode()) && settlementOrder.getIsSupplement()).collect(Collectors.toList());
            List<SettlementOrder> inAccountNotSupplementSettlementOrders = settlementOrders.stream().filter(settlementOrder -> Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode()) && !settlementOrder.getIsSupplement()).collect(Collectors.toList());
            List<SettlementOrder> inAccountPrepaySettlementOrders = settlementOrders.stream().filter(settlementOrder -> Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())).collect(Collectors.toList());
            List<SettlementOrder> newUserPrepaymentSettlementOrders = settlementOrders.stream().filter(settlementOrder -> Objects.equals(settlementOrder.getSettlementType(), SettlementType.NEW_USER_PREPAYMENT.getCode())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(inAccountSupplementSettlementOrders)) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(inAccountSupplementSettlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), BusinessSettlementStatus.SETTLED.getCode());
            }
            if (!ObjectUtils.isEmpty(inAccountNotSupplementSettlementOrders)) {
                settlementOrderDataMapper.updateInAccountDataSettlementStatus(inAccountNotSupplementSettlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
            }
            if (!ObjectUtils.isEmpty(inAccountPrepaySettlementOrders)) {
                settlementOrderDataMapper.updateInAccountDataPrepayStatus(inAccountPrepaySettlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), PeriodPrepayStatus.UNPREPAID.getCode());
            }
            if (!ObjectUtils.isEmpty(newUserPrepaymentSettlementOrders)) {
                settlementOrderDataMapper.updateNewUserPrepaymentDataSettlementStatus(newUserPrepaymentSettlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()), BusinessSettlementStatus.WAIT_SETTLEMENT.getCode());
            }
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
            settlementOrders.forEach(settlementOrder -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("因删除账单删除结算单")
                            .setOperName(employeeName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public void reBackSettlementOrderByBill(List<Long> settlementOrderIds) {
        List<SettlementOrder> settlementOrders = list(new LambdaQueryWrapper<SettlementOrder>()
                .eq(SettlementOrder::getIsDel, false).in(SettlementOrder::getId, settlementOrderIds));
        if (!ObjectUtils.isEmpty(settlementOrders)) {
            update(new LambdaUpdateWrapper<SettlementOrder>()
                    .in(SettlementOrder::getId, settlementOrders.stream().map(SettlementOrder::getId).collect(Collectors.toList()))
                    .set(SettlementOrder::getStatus, SettlementOrderStatus.WAIT_PUSH.getCode())
                    .set(SettlementOrder::getIsWaitForEdit, false)
                    .set(SettlementOrder::getBillNo, null)
                    .set(SettlementOrder::getBillTitle, null));
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
            settlementOrders.forEach(settlementOrder -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                            .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                            .setDeptId(deptId)
                            .setOperType("因删除账单退回结算单")
                            .setOperName(employeeName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
    }

    @Override
    @Transactional
    public CommonOperateResultDTO deleteSettlementOrderBatch(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new CommonOperateResultDTO();
        }
        List<SettlementOrder> totalList = list(new LambdaQueryWrapper<SettlementOrder>().eq(SettlementOrder::getIsDel, false)
                .in(SettlementOrder::getId, ids));
        if (ObjectUtils.isEmpty(totalList)) {
            return new CommonOperateResultDTO();
        }
        List<SettlementOrder> successList = Lists.newArrayList();
        List<SettlementOrder> failList = Lists.newArrayList();
        for (SettlementOrder settlementOrder : totalList) {
            try {
                deleteSettlementOrder(settlementOrder.getId());
                successList.add(settlementOrder);
            } catch (Exception e) {
                log.error("删除结算单失败:{}", e.getMessage());
                failList.add(settlementOrder);
            }
        }
        return CommonOperateResultDTO.builder().totalList(totalList.stream().map(settlementOrder -> CommonOperDTO.builder().id(settlementOrder.getId()).name(settlementOrder.getSettlementTitle()).build()).collect(Collectors.toList()))
                .successList(successList.stream().map(settlementOrder -> CommonOperDTO.builder().id(settlementOrder.getId()).name(settlementOrder.getSettlementTitle()).build()).collect(Collectors.toList()))
                .failList(failList.stream().map(settlementOrder -> CommonOperDTO.builder().id(settlementOrder.getId()).name(settlementOrder.getSettlementTitle()).build()).collect(Collectors.toList())).build();
    }

    private void modifySettlementOrderByBusinessDeptPrice(SettlementBusinessDeptPriceDTO dto, SettlementOrderCreateVO vo, Long deptId, Long userId, String employeeName) {
        SettlementOrder settlementOrder = getById(vo.getId());
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            throw new ServiceException("结算单不存在");
        }
        BigDecimal totalPrice = vo.getPrice().multiply(new BigDecimal(dto.getDataCount()));
        update(new LambdaUpdateWrapper<SettlementOrder>().eq(SettlementOrder::getId, vo.getId())
                .set(SettlementOrder::getSettlementTitle, vo.getSettlementTitle())
                .set(SettlementOrder::getPrice, vo.getPrice())
                .set(SettlementOrder::getTotalPrice, totalPrice)
                .set(SettlementOrder::getDataCount, dto.getDataCount())
                .set(SettlementOrder::getDiscountPrice, Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice())
                .set(SettlementOrder::getSettlementPrice, totalPrice.subtract(Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice()))
                .set(SettlementOrder::getRemark, vo.getRemark()));
        settlementOrderFileService.removeAndSaveNewBySettlementOrderIdAndFileType(settlementOrder.getId(), vo.getFiles(), SettlementOrderFileType.CREATE_FILE.getCode());
        settlementOrderConditionService.removeAndSaveNewBySettlementOrderId(settlementOrder.getId(), vo.getSettlementConditions());
        settlementOrderDataService.removeAndSaveByBatchNoAndBusinessId(vo.getBatchNo(), dto.getBusinessDeptId(), settlementOrder.getId(), vo.getSettlementType(), vo.getIsSupplement());
        String operContent = buildModifySettlementOrderOperContent(vo, settlementOrder.getSettlementType(), dto.getDiscountPrice());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑结算单")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(operContent)
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private String buildModifySettlementOrderOperContent(SettlementOrderCreateVO vo, Integer settlementType, BigDecimal discountPrice) {
        Map<String, Object> operContentMap = new HashMap<>();
        operContentMap.put("结算单标题", vo.getSettlementTitle());
        operContentMap.put("结算类型", SettlementType.getByCode(settlementType).getName());
        operContentMap.put("结算单价", vo.getPrice().stripTrailingZeros().toPlainString());
        operContentMap.put("优惠价", Objects.isNull(discountPrice) ? "-" : discountPrice.stripTrailingZeros().toPlainString());
        operContentMap.put("结算说明", StringUtils.isEmpty(vo.getRemark()) ? "-" : vo.getRemark());
        Map<String, String> conditionMap = buildConditionMap(vo.getSettlementConditions());
        if (!ObjectUtils.isEmpty(conditionMap)) {
            operContentMap.put("结算条件", conditionMap);
        }
        return JSONObject.toJSONString(operContentMap);
    }

    private Map<String, String> buildConditionMap(List<SettlementOrderCondition> settlementConditions) {
        Map<String, String> map = new HashMap<>();
        if (ObjectUtils.isEmpty(settlementConditions)) {
            return map;
        }
        settlementConditions.forEach(condition -> {
            if (tagConditionKeys.contains(condition.getConditionType())) {
                String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
                if (Objects.equals("1", tagIncludeFlag)) {
                    map.put(condition.getConditionTypeName() + "（包含）", getConditionValueNameByConditionValueAndConditionType(condition.getConditionType(), condition.getConditionValue()));
                } else {
                    map.put(condition.getConditionTypeName() + "（不包含）", getConditionValueNameByConditionValueAndConditionType(condition.getConditionType(), condition.getConditionValue()));
                }
            } else {
                map.put(condition.getConditionTypeName(), getConditionValueNameByConditionValueAndConditionType(condition.getConditionType(), condition.getConditionValue()));
            }
        });
        return map;
    }

    private String getConditionValueNameByConditionValueAndConditionType(String conditionType, String conditionValue) {
        String result;
        if (singleConditionKeys.contains(conditionType)) {
            switch (conditionType) {
                case PERIOD_TAX_TYPE:
                case CUSTOMER_SERVICE_TAX_TYPE:
                    result = TaxType.getByCode(Integer.parseInt(getConditionValue(conditionValue).get(conditionType))).getDesc();
                    break;
                case PERIOD_IN_ACCOUNT_DELIVER_RESULT:
                case PERIOD_IN_ACCOUNT_RESULT:
                    result = AccountingCashierDeliverResult.getMultiNamesByCodes(getConditionValue(conditionValue).get(conditionType));
                    break;
                case PERIOD_IN_ACCOUNT_STATUS:
                    result = AccountingCashierDeliverStatus.getMultiNamesByCodes(getConditionValue(conditionValue).get(conditionType));
                    break;
                case PERIOD_BANK_PAYMENT_INPUT_RESULT:
                    result = BankPaymentInputResult.getMultiNamesByCodes(getConditionValue(conditionValue).get(conditionType));
                    break;
                case PERIOD_ACCOUNTING_STATUS:
                    result = AccountingStatus.getByCode(Integer.parseInt(getConditionValue(conditionValue).get(conditionType))).getName();
                    break;
                case PERIOD_SERVICE_TYPE:
                    result = CustomerServicePeriodMonthServiceType.getByCode(Integer.parseInt(getConditionValue(conditionValue).get(conditionType))).getName();
                    break;
                case SETTLEMENT_STATUS:
                    result = BusinessSettlementStatus.getMultiNamesByCodes(getConditionValue(conditionValue).get(conditionType));
                    break;
                case PERIOD_PREPAY_STATUS:
                    result = PeriodPrepayStatus.getMultiNamesByCodes(getConditionValue(conditionValue).get(conditionType));
                    break;
                case CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID:
                case PERIOD_ACCOUNTING_TOP_DEPT_ID:
                    List<Long> deptIds = Arrays.stream(getConditionValue(conditionValue).get(conditionType).split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<SysDept> depts = remoteDeptService.getByDeptIds(deptIds).getDataThrowException();
                    result = ObjectUtils.isEmpty(depts) ? "" : depts.stream().map(SysDept::getDeptName).collect(Collectors.joining("，"));
                    break;
                default:
                    result = "";
                    break;
            }
        } else if (timeConditionKeys.contains(conditionType)) {
            String startTime = getConditionValue(conditionValue).get(COMMON_START_TIME);
            String endTime = getConditionValue(conditionValue).get(COMMON_END_TIME);
            result = startTime + " ~ " + endTime;
        } else if (tagConditionKeys.contains(conditionType)) {
            String tagName = getConditionValue(conditionValue).get("tagName");
            if (tagName.contains("&")) {
                result = tagName.replaceAll("&", " 和 ");
            } else if (tagName.contains("/")) {
                result = tagName.replaceAll("/", " 或 ");
            } else {
                result = tagName;
            }
        } else if (fileConditionKeys.contains(conditionType)) {
            String fileUrl = getConditionValue(conditionValue).get("fileUrl");
            String fileName = getConditionValue(conditionValue).get("fileName");
            Map<String, String> fileMap = new HashMap<>();
            fileMap.put("fileUrl", fileUrl);
            fileMap.put("fileName", fileName);
            fileMap.put("fullFileUrl", fileService.getFullFileUrl(fileUrl));
            result = JSONObject.toJSONString(fileMap);
        } else {
            result = "";
        }
        return result;
    }

    private void createSettlementOrderByBusinessDeptPrice(SettlementBusinessDeptPriceDTO dto, SettlementOrderCreateVO vo, Long deptId, Long userId, String employeeName) {
        if (dto.getDataCount() == 0L) {
            return;
        }
        String batchNo = Constants.SETTLEMENT_NO_PREFIX + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + deptId;
        BigDecimal totalPrice = vo.getPrice().multiply(new BigDecimal(dto.getDataCount()));
        SettlementOrder settlementOrder = new SettlementOrder().setSettlementBatchNo(vo.getBatchNo())
                .setSettlementOrderNo(batchNo)
                .setSettlementTitle(vo.getSettlementTitle())
                .setBusinessTopDeptId(vo.getBusinessTopDeptId())
                .setBusinessDeptId(dto.getBusinessDeptId())
                .setSettlementType(vo.getSettlementType())
                .setIsSupplement(vo.getIsSupplement())
                .setUnit(SettlementType.getByCode(vo.getSettlementType()).getUnit())
                .setDataCount(dto.getDataCount())
                .setPrice(vo.getPrice())
                .setTotalPrice(totalPrice)
                .setDiscountPrice(Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice())
                .setSettlementPrice(totalPrice.subtract(Objects.isNull(dto.getDiscountPrice()) ? BigDecimal.ZERO : dto.getDiscountPrice()))
                .setRemark(vo.getRemark())
                .setStatus(SettlementOrderStatus.WAIT_PUSH.getCode())
                .setIsWaitForEdit(false);
        save(settlementOrder);
        settlementOrderFileService.removeAndSaveNewBySettlementOrderIdAndFileType(settlementOrder.getId(), vo.getFiles(), SettlementOrderFileType.CREATE_FILE.getCode());
        settlementOrderConditionService.removeAndSaveNewBySettlementOrderId(settlementOrder.getId(), vo.getSettlementConditions());
        settlementOrderDataService.removeAndSaveByBatchNoAndBusinessId(vo.getBatchNo(), dto.getBusinessDeptId(), settlementOrder.getId(), vo.getSettlementType(), vo.getIsSupplement());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(settlementOrder.getId())
                    .setBusinessType(BusinessLogBusinessType.SETTLEMENT_ORDER.getCode())
                    .setDeptId(deptId)
                    .setOperType("新建结算单")
                    .setOperName(employeeName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private String buildPeriodMonthConditionSql(List<SettlementOrderCondition> settlementConditions, List<Long> businessDeptIds, Boolean isSupplement, String batchNo, Long settlementOrderId, SettlementType settlementType) {
        StringBuilder conditionSql = new StringBuilder("select null,'" + batchNo + "',c_customer_service_period_month.id,2,c_customer_service_period_month.business_dept_id,c_customer_service.id,c_customer_service.tax_type,c_customer_service.advisor_dept_id,sd3.dept_name,se3.employee_name," +
                " c_customer_service.accounting_dept_id,sd4.dept_name,se4.employee_name,c_customer_service.first_account_period,c_customer_service.accounting_remark," +
                " c_customer_service_period_month.period,c_customer_service_period_month.service_type,c_customer_service_period_month.tax_type,c_customer_service_period_month.advisor_dept_id,sd1.dept_name,se1.employee_name," +
                " c_customer_service_period_month.accounting_dept_id,sd2.dept_name,se2.employee_name,c_customer_service_period_month.accounting_status,c_customer_service_cashier_accounting.deliver_result,c_customer_service_period_month.bank_payment_result,c_customer_service_cashier_accounting.deliver_status,c_customer_service_cashier_accounting.deliver_result,null,c_customer_service_cashier_accounting.in_time,c_customer_service_cashier_accounting.end_time,c_customer_service_cashier_accounting.rpa_remark,c_customer_service_period_month.settlement_status,c_customer_service_period_month.prepay_status," +
                " null,null,'',NOW(),'',NOW() " +
                " from c_customer_service_period_month left join c_customer_service_cashier_accounting on c_customer_service_period_month.id = c_customer_service_cashier_accounting.customer_service_period_month_id and c_customer_service_cashier_accounting.is_del = 0 and c_customer_service_cashier_accounting.`type` = 1 join c_customer_service on c_customer_service_period_month.customer_service_id = c_customer_service.id and c_customer_service.is_del = 0" +
                " left join sys_dept sd1 on c_customer_service_period_month.advisor_dept_id = sd1.dept_id and sd1.del_flag = 0 " +
                " left join sys_dept sd2 on c_customer_service_period_month.accounting_dept_id = sd2.dept_id and sd2.del_flag = 0 " +
                " left join sys_dept sd3 on c_customer_service.advisor_dept_id = sd3.dept_id and sd3.del_flag = 0 " +
                " left join sys_dept sd4 on c_customer_service.accounting_dept_id = sd4.dept_id and sd4.del_flag = 0 " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se1 on c_customer_service_period_month.advisor_dept_id = se1.dept_id " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se2 on c_customer_service_period_month.accounting_dept_id = se2.dept_id " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se3 on c_customer_service.advisor_dept_id = se3.dept_id " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se4 on c_customer_service.accounting_dept_id = se4.dept_id ");
        if (!ObjectUtils.isEmpty(settlementConditions) && settlementConditions.stream().anyMatch(c -> Objects.equals(c.getConditionType(), PERIOD_TAGS))) {
            SettlementOrderCondition condition = settlementConditions.stream().filter(c -> Objects.equals(c.getConditionType(), PERIOD_TAGS)).findFirst().get();
            String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
            String tagName = getConditionValue(condition.getConditionValue()).get("tagName");
            if (Objects.equals("1", tagIncludeFlag)) {
                if (tagName.contains("/")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 2" +
                            ") period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id");
                } else if (tagName.contains("&")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 2" +
                            "    GROUP BY br.business_id HAVING COUNT(DISTINCT br.tag_id) = " + tagName.split("&").length +
                            ") period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id");
                } else {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 2" +
                            ") period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id");
                }
            } else {
                if (tagName.contains("&")) {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 2" +
                            ") period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id");
                } else {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 2" +
                            ") period_tag_filter ON c_customer_service_period_month.id = period_tag_filter.business_id");
                }
            }
        }
        if (!ObjectUtils.isEmpty(settlementConditions) && settlementConditions.stream().anyMatch(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS))) {
            SettlementOrderCondition condition = settlementConditions.stream().filter(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS)).findFirst().get();
            String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
            String tagName = getConditionValue(condition.getConditionValue()).get("tagName");
            if (Objects.equals("1", tagIncludeFlag)) {
                if (tagName.contains("/")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else if (tagName.contains("&")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            "    GROUP BY br.business_id HAVING COUNT(DISTINCT br.tag_id) = " + tagName.split("&").length +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                }
            } else {
                if (tagName.contains("&")) {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                }
            }
        }
        conditionSql.append(" where 1 = 1");
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT)) {
            if (Objects.isNull(settlementOrderId)) {
                if (isSupplement) {
                    conditionSql.append(" and c_customer_service_period_month.settlement_status = 4");
                } else {
                    conditionSql.append(" and c_customer_service_period_month.settlement_status = 2");
                }
                conditionSql.append(" and c_customer_service_period_month.id not in (select distinct business_id from c_settlement_order_data where business_type = 2 and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 1 and is_supplement = ").append(isSupplement ? 1 : 0).append("))");
            } else {
                if (isSupplement) {
                    conditionSql.append(" and ((c_customer_service_period_month.settlement_status = 4");
                } else {
                    conditionSql.append(" and ((c_customer_service_period_month.settlement_status = 2");
                }
                conditionSql.append(" and c_customer_service_period_month.id not in (select distinct business_id from c_settlement_order_data where business_type = 2 and settlement_order_id != ").append(settlementOrderId).append(" and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 1 and status != 5 and is_supplement = ").append(isSupplement ? 1 : 0).append("))) or c_customer_service_period_month.id IN (SELECT DISTINCT business_id FROM c_settlement_order_data WHERE settlement_order_id = ").append(settlementOrderId).append("))");
            }
        } else if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT)) {
            if (Objects.isNull(settlementOrderId)) {
                conditionSql.append(" and c_customer_service_period_month.prepay_status = 1");
                conditionSql.append(" and c_customer_service_period_month.id not in (select distinct business_id from c_settlement_order_data where business_type = 2 and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 5))");
            } else {
                conditionSql.append(" and ((c_customer_service_period_month.prepay_status = 1");
                conditionSql.append(" and c_customer_service_period_month.id not in (select distinct business_id from c_settlement_order_data where business_type = 2 and settlement_order_id != ").append(settlementOrderId).append(" and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 5))) or c_customer_service_period_month.id IN (SELECT DISTINCT business_id FROM c_settlement_order_data WHERE settlement_order_id = ").append(settlementOrderId).append("))");
            }
        }
        if (!ObjectUtils.isEmpty(businessDeptIds)) {
            String deptIds = StringUtils.join(businessDeptIds, ",");
            conditionSql.append(" and c_customer_service_period_month.business_dept_id in (").append(deptIds).append(")");
        }
        //如果是 账期任务，需要如下默认条件
//        if (Objects.equals(settlementType, SettlementType.TASK_PERIOD)) {
//            conditionSql.append(" and c_customer_service_in_account.bank_payment_input_time is null");
//
//            //重复任务不派
//            //conditionSql.append(" and c_customer_service_period_month.id not in (select distinct biz_id from c_business_task where status != 3 and status != 4 ");
//        }

        if (!ObjectUtils.isEmpty(settlementConditions)) {
            for (SettlementOrderCondition condition : settlementConditions) {
                if (Objects.equals(condition.getConditionType(), PERIOD_TAX_TYPE)) {
                    conditionSql.append(" and c_customer_service_period_month.tax_type = ").append(getConditionValue(condition.getConditionValue()).get(PERIOD_TAX_TYPE));
                }
//                if (Objects.equals(condition.getConditionType(), PERIOD_BANK_PAYMENT_INPUT_TIME)) {
//                    String startTime = getConditionValue(condition.getConditionValue()).get(COMMON_START_TIME);
//                    String endTime = getConditionValue(condition.getConditionValue()).get(COMMON_END_TIME);
//                    conditionSql.append(" and c_customer_service_in_account.bank_payment_input_time between '").append(startTime).append("' and '").append(endTime).append("'");
//                }
                if (Objects.equals(condition.getConditionType(), PERIOD_IN_ACCOUNT_IN_TIME)) {
                    String startTime = getConditionValue(condition.getConditionValue()).get(COMMON_START_TIME);
                    String endTime = getConditionValue(condition.getConditionValue()).get(COMMON_END_TIME);
                    conditionSql.append(" and c_customer_service_cashier_accounting.in_time between '").append(startTime).append("' and '").append(endTime).append("'");
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_IN_ACCOUNT_END_TIME)) {
                    String startTime = getConditionValue(condition.getConditionValue()).get(COMMON_START_TIME);
                    String endTime = getConditionValue(condition.getConditionValue()).get(COMMON_END_TIME);
                    conditionSql.append(" and c_customer_service_cashier_accounting.end_time between '").append(startTime).append("' and '").append(endTime).append("'");
                }
//                if (Objects.equals(condition.getConditionType(), PERIOD_IN_ACCOUNT_DELIVER_RESULT)) {
//                    String value = getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_DELIVER_RESULT);
//                    if (value.contains("0")) {
//                        conditionSql.append(" and (c_customer_service_in_account.deliver_result is null or ").append("c_customer_service_in_account.deliver_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_DELIVER_RESULT)).append("))");
//                    } else {
//                        conditionSql.append(" and c_customer_service_in_account.deliver_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_DELIVER_RESULT)).append(")");
//                    }
//                }
                if (Objects.equals(condition.getConditionType(), PERIOD_IN_ACCOUNT_STATUS)) {
                    String value = getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_STATUS);
                    if (value.contains("0")) {
                        conditionSql.append(" and (c_customer_service_cashier_accounting.deliver_status is null or ").append("c_customer_service_cashier_accounting.deliver_status in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_STATUS)).append("))");
                    } else {
                        conditionSql.append(" and c_customer_service_cashier_accounting.deliver_status in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_STATUS)).append(")");
                    }
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_IN_ACCOUNT_RESULT)) {
                    String value = getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_RESULT);
                    if (value.contains("0")) {
                        conditionSql.append(" and (c_customer_service_cashier_accounting.deliver_result is null or ").append("c_customer_service_cashier_accounting.deliver_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_RESULT)).append("))");
                    } else {
                        conditionSql.append(" and c_customer_service_cashier_accounting.deliver_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_IN_ACCOUNT_RESULT)).append(")");
                    }
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_BANK_PAYMENT_INPUT_RESULT)) {
                    String value = getConditionValue(condition.getConditionValue()).get(PERIOD_BANK_PAYMENT_INPUT_RESULT);
                    if (value.contains("0")) {
                        conditionSql.append(" and (c_customer_service_period_month.bank_payment_result is null or ").append("c_customer_service_period_month.bank_payment_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_BANK_PAYMENT_INPUT_RESULT)).append("))");
                    } else {
                        conditionSql.append(" and c_customer_service_period_month.bank_payment_result in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_BANK_PAYMENT_INPUT_RESULT)).append(")");
                    }
                }
                if (Objects.equals(condition.getConditionType(), SETTLEMENT_STATUS)) {
                    String settlementStatus = getConditionValue(condition.getConditionValue()).get(SETTLEMENT_STATUS);
                    String sqlValue = settlementStatus;
                    if (settlementStatus.contains("4")) {
                        sqlValue = sqlValue + ",5,6";
                    }
                    conditionSql.append(" and c_customer_service_period_month.settlement_status in (").append(sqlValue).append(")");
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_PREPAY_STATUS)) {
                    conditionSql.append(" and c_customer_service_period_month.prepay_status in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_PREPAY_STATUS)).append(")");
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_SERVICE_TYPE)) {
                    conditionSql.append(" and c_customer_service_period_month.service_type = ").append(getConditionValue(condition.getConditionValue()).get(PERIOD_SERVICE_TYPE));
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_ACCOUNTING_STATUS)) {
                    conditionSql.append(" and c_customer_service_period_month.accounting_status = ").append(getConditionValue(condition.getConditionValue()).get(PERIOD_ACCOUNTING_STATUS));
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID)) {
                    conditionSql.append(" and c_customer_service.accounting_top_dept_id in (").append(getConditionValue(condition.getConditionValue()).get(CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID)).append(")");
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_ACCOUNTING_TOP_DEPT_ID)) {
                    conditionSql.append(" and c_customer_service_period_month.accounting_top_dept_id in (").append(getConditionValue(condition.getConditionValue()).get(PERIOD_ACCOUNTING_TOP_DEPT_ID)).append(")");
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_TAGS)) {
                    String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
                    if (Objects.equals("1", tagIncludeFlag)) {
                        conditionSql.append(" and period_tag_filter.business_id IS NOT NULL");
                    } else {
                        conditionSql.append(" and period_tag_filter.business_id IS NULL");
                    }
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_TAX_TYPE)) {
                    conditionSql.append(" and c_customer_service.tax_type = ").append(getConditionValue(condition.getConditionValue()).get(CUSTOMER_SERVICE_TAX_TYPE));
                }
                if (Objects.equals(condition.getConditionType(), PERIOD_TIME)) {
                    String startTime = getConditionValue(condition.getConditionValue()).get(COMMON_START_TIME);
                    String endTime = getConditionValue(condition.getConditionValue()).get(COMMON_END_TIME);
                    conditionSql.append(" and c_customer_service_period_month.period between ").append(startTime).append(" and ").append(endTime);
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_TAGS)) {
                    String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
                    if (Objects.equals("1", tagIncludeFlag)) {
                        conditionSql.append(" and customer_tag_filter.business_id IS NOT NULL");
                    } else {
                        conditionSql.append(" and customer_tag_filter.business_id IS NULL");
                    }
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_RANGE)) {
                    String fileUrl = getConditionValue(condition.getConditionValue()).get("fileUrl");
                    List<Long> customerServiceIds = getCustomerServiceIdsByFileUrl(fileUrl);
                    if (!ObjectUtils.isEmpty(customerServiceIds)) {
                        conditionSql.append(" and c_customer_service.id in (").append(customerServiceIds.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")");
                    } else {
                        conditionSql.append(" and 1 = 2");
                    }
                }

                if (Objects.equals(condition.getConditionType(), PERIOD_RANGE)) {
                    String fileUrl = getConditionValue(condition.getConditionValue()).get("fileUrl");
                    List<Long> customerServicePeriodMonthIds = getPeriodIdsByFileUrl(fileUrl, isSupplement);
                    if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
                        conditionSql.append(" and c_customer_service_period_month.id in (").append(customerServicePeriodMonthIds.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")");
                    } else {
                        conditionSql.append(" and 1 = 2");
                    }
                }

                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_CREATE_TIME)) {
                    String startTime = getConditionValue(condition.getConditionValue()).get("startTime") + " 00:00:00";
                    String endTime = getConditionValue(condition.getConditionValue()).get("endTime") + " 23:59:59";
                    conditionSql.append(" and c_customer_service.create_time between '").append(startTime).append("' and '").append(endTime).append("'");
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_FIRST_ACCOUNT_PERIOD)) {
                    Integer startPeriod = DateUtils.yearMonthToPeriod(getConditionValue(condition.getConditionValue()).get("startTime"));
                    Integer endPeriod = DateUtils.yearMonthToPeriod(getConditionValue(condition.getConditionValue()).get("endTime"));
                    conditionSql.append(" and c_customer_service.first_account_period between ").append(startPeriod).append(" and ").append(endPeriod);
                }

            }
        }
//        log.debug("账期查询sql:{}", conditionSql);
        return conditionSql.toString();
    }

    private String buildCustomerServiceConditionSql(List<SettlementOrderCondition> settlementConditions, List<Long> businessDeptIds, String batchNo, Long settlementOrderId) {
        StringBuilder conditionSql = new StringBuilder("select null,'" + batchNo + "',c_customer_service.id,1,c_customer_service.business_dept_id,c_customer_service.id,c_customer_service.tax_type,c_customer_service.advisor_dept_id,sd1.dept_name,se1.employee_name," +
                " c_customer_service.accounting_dept_id,sd2.dept_name,se2.employee_name,c_customer_service.first_account_period,c_customer_service.accounting_remark," +
                " null,null,null,null,null,null," +
                " null,null,null,null,null,null,null,null,null,null,null,null,null,null," +
                " sys_employee.dept_id,sd.dept_name,c_new_customer_info.create_employee_name,c_customer_service.create_time,'',NOW() " +
                " from c_customer_service " +
                " left join c_new_customer_info on c_customer_service.new_customer_id = c_new_customer_info.id and c_new_customer_info.is_del = 0 " +
                " left join sys_employee on c_new_customer_info.create_employee_id = sys_employee.employee_id " +
                " left join sys_dept sd on sys_employee.dept_id = sd.dept_id and sd.del_flag = 0 " +
                " left join sys_dept sd1 on c_customer_service.advisor_dept_id = sd1.dept_id and sd1.del_flag = 0 " +
                " left join sys_dept sd2 on c_customer_service.accounting_dept_id = sd2.dept_id and sd2.del_flag = 0 " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se1 on c_customer_service.advisor_dept_id = se1.dept_id " +
                " left join (select dept_id, group_concat(employee_name) as employee_name from sys_employee group by dept_id) se2 on c_customer_service.accounting_dept_id = se2.dept_id ");
        if (!ObjectUtils.isEmpty(settlementConditions) && settlementConditions.stream().anyMatch(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS))) {
            SettlementOrderCondition condition = settlementConditions.stream().filter(c -> Objects.equals(c.getConditionType(), CUSTOMER_SERVICE_TAGS)).findFirst().get();
            String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
            String tagName = getConditionValue(condition.getConditionValue()).get("tagName");
            if (Objects.equals("1", tagIncludeFlag)) {
                if (tagName.contains("/")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else if (tagName.contains("&")) {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            "    GROUP BY br.business_id HAVING COUNT(DISTINCT br.tag_id) = " + tagName.split("&").length +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else {
                    conditionSql.append(" JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                }
            } else {
                if (tagName.contains("&")) {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name IN (" + Arrays.stream(tagName.split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")) + ")" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                } else {
                    conditionSql.append(" LEFT JOIN (" +
                            "    SELECT DISTINCT br.business_id" +
                            "    FROM c_business_tag_relation br" +
                            "    INNER JOIN c_tag t ON br.tag_id = t.id" +
                            "    WHERE t.is_del = 0" +
                            "      AND t.tag_name = '" + tagName + "'" +
                            "      AND br.business_type = 1" +
                            ") customer_tag_filter ON c_customer_service.id = customer_tag_filter.business_id");
                }
            }
        }
        conditionSql.append(" where c_customer_service.is_del = 0");
        if (Objects.isNull(settlementOrderId)) {
            conditionSql.append(" and c_customer_service.settlement_status in (2,3)");
            conditionSql.append(" and c_customer_service.id not in (select distinct business_id from c_settlement_order_data where business_type = 1 and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 2))");
        } else {
            conditionSql.append(" and ((c_customer_service.settlement_status in (2,3)");
            conditionSql.append(" and c_customer_service.id not in (select distinct business_id from c_settlement_order_data where business_type = 1 and settlement_order_id != ").append(settlementOrderId).append(" and settlement_order_id in (select id from c_settlement_order where is_del = 0 and settlement_type = 2))) or c_customer_service.id in (SELECT DISTINCT business_id FROM c_settlement_order_data WHERE settlement_order_id = ").append(settlementOrderId).append("))");
        }
        if (!ObjectUtils.isEmpty(businessDeptIds)) {
            String deptIds = StringUtils.join(businessDeptIds, ",");
            conditionSql.append(" and c_customer_service.business_dept_id in (").append(deptIds).append(")");
        }
        if (!ObjectUtils.isEmpty(settlementConditions)) {
            List<String> inCustomerAndTagNames = Lists.newArrayList();
            List<String> inCustomerOrTagNames = Lists.newArrayList();
            List<String> notInCustomerTagNames = Lists.newArrayList();
            for (SettlementOrderCondition condition : settlementConditions) {
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_TAX_TYPE)) {
                    conditionSql.append(" and c_customer_service.tax_type = ").append(getConditionValue(condition.getConditionValue()).get(CUSTOMER_SERVICE_TAX_TYPE));
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_TAGS)) {
                    String tagIncludeFlag = getConditionValue(condition.getConditionValue()).get("tagIncludeFlag");
                    if (Objects.equals("1", tagIncludeFlag)) {
                        conditionSql.append(" and customer_tag_filter.business_id IS NOT NULL");
                    } else {
                        conditionSql.append(" and customer_tag_filter.business_id IS NULL");
                    }
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_CREATE_TIME)) {
                    String startTime = getConditionValue(condition.getConditionValue()).get("startTime") + " 00:00:00";
                    String endTime = getConditionValue(condition.getConditionValue()).get("endTime") + " 23:59:59";
                    conditionSql.append(" and c_customer_service.create_time between '").append(startTime).append("' and '").append(endTime).append("'");
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_FIRST_ACCOUNT_PERIOD)) {
                    Integer startPeriod = DateUtils.yearMonthToPeriod(getConditionValue(condition.getConditionValue()).get("startTime"));
                    Integer endPeriod = DateUtils.yearMonthToPeriod(getConditionValue(condition.getConditionValue()).get("endTime"));
                    conditionSql.append(" and c_customer_service.first_account_period between ").append(startPeriod).append(" and ").append(endPeriod);
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_RANGE)) {
                    String fileUrl = getConditionValue(condition.getConditionValue()).get("fileUrl");
                    List<Long> customerServiceIds = getCustomerServiceIdsByFileUrl(fileUrl);
                    if (!ObjectUtils.isEmpty(customerServiceIds)) {
                        conditionSql.append(" and c_customer_service.id in (").append(customerServiceIds.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")");
                    } else {
                        conditionSql.append(" and 1 = 2");
                    }
                }
                if (Objects.equals(condition.getConditionType(), CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID)) {
                    conditionSql.append(" and c_customer_service.accounting_top_dept_id = ").append(getConditionValue(condition.getConditionValue()).get(CUSTOMER_SERVICE_ACCOUNTING_TOP_DEPT_ID));
                }
            }
            if (!ObjectUtils.isEmpty(inCustomerAndTagNames)) {
                conditionSql.append(" and c_customer_service.id in ( SELECT business_id FROM c_business_tag_relation WHERE tag_id IN ( SELECT id FROM c_tag WHERE tag_name IN (").append(inCustomerAndTagNames.stream().map(row -> "'" + row + "'").collect(Collectors.joining(","))).append(")) AND business_type = 1 GROUP BY business_id HAVING COUNT(DISTINCT tag_id) = ").append(inCustomerAndTagNames.size()).append(")");
            }
            if (!ObjectUtils.isEmpty(inCustomerOrTagNames)) {
                conditionSql.append(" and c_customer_service.id in ( SELECT c_business_tag_relation.business_id FROM c_business_tag_relation JOIN c_tag ON c_business_tag_relation.tag_id = c_tag.id WHERE c_tag.is_del = 0 AND c_tag.tag_name IN (").append(inCustomerOrTagNames.stream().map(row -> "'" + row + "'").collect(Collectors.joining(","))).append(") AND c_business_tag_relation.business_type = 1)");
            }
            if (!ObjectUtils.isEmpty(notInCustomerTagNames)) {
                conditionSql.append(" and c_customer_service.id not in ( SELECT c_business_tag_relation.business_id FROM c_business_tag_relation JOIN c_tag ON c_business_tag_relation.tag_id = c_tag.id WHERE c_tag.is_del = 0 AND c_tag.tag_name IN (").append(notInCustomerTagNames.stream().map(row -> "'" + row + "'").collect(Collectors.joining(","))).append(") AND c_business_tag_relation.business_type = 1)");
            }
        }
//        log.debug("服务查询sql:{}", conditionSql);
        return conditionSql.toString();
    }

    private List<Long> getPeriodIdsByFileUrl(String fileUrl, Boolean isSupplement) {
        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(INNER_ENDPOINT, ACCESS_KEY, SECRET_KEY);

        try {
            // 获取 OSS 中的文件对象
            OSSObject ossObject = ossClient.getObject(BUCKET_NAME, fileUrl);

            // 获取 InputStream
            InputStream inputStream = ossObject.getObjectContent();

            ExcelUtil<SettlementOrderPeriodUploadDTO> util = new ExcelUtil<>(SettlementOrderPeriodUploadDTO.class);
            List<SettlementOrderPeriodUploadDTO> dataList = util.importExcel(inputStream);
            // 处理完毕后关闭 InputStream
            inputStream.close();
            return getPeriodIdsByDataList(dataList, isSupplement);
        } catch (Exception e) {
            log.error("处理文件失败:{}", e.getMessage());
            throw new ServiceException("文件获取失败");
        } finally {
            // 关闭 OSSClient
            ossClient.shutdown();
        }
    }

    private List<Long> getCustomerServiceIdsByFileUrl(String fileUrl) {
        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(INNER_ENDPOINT, ACCESS_KEY, SECRET_KEY);

        try {
            // 获取 OSS 中的文件对象
            OSSObject ossObject = ossClient.getObject(BUCKET_NAME, fileUrl);

            // 获取 InputStream
            InputStream inputStream = ossObject.getObjectContent();

            ExcelUtil<SettlementOrderCustomerUploadDTO> util = new ExcelUtil<>(SettlementOrderCustomerUploadDTO.class);
            List<SettlementOrderCustomerUploadDTO> dataList = util.importExcel(inputStream);
            // 处理完毕后关闭 InputStream
            inputStream.close();
            return getCustomerServiceIdsByDataList(dataList);
        } catch (Exception e) {
            log.error("处理文件失败:{}", e.getMessage());
            throw new ServiceException("文件获取失败");
        } finally {
            // 关闭 OSSClient
            ossClient.shutdown();
        }
    }

    private List<Long> getCustomerServiceIdsByDataList(List<SettlementOrderCustomerUploadDTO> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        dataList = dataList.stream().filter(d -> !StringUtils.isEmpty(d.getCreditCode())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .in(CCustomerService::getCreditCode, dataList.stream().map(SettlementOrderCustomerUploadDTO::getCreditCode).collect(Collectors.toList()))
                .eq(CCustomerService::getIsDel, false)
                .eq(CCustomerService::getSettlementStatus, BusinessSettlementStatus.WAIT_SETTLEMENT.getCode())
                .select(CCustomerService::getId)).stream().map(CCustomerService::getId).collect(Collectors.toList());
    }

    private List<Long> getPeriodIdsByDataList(List<SettlementOrderPeriodUploadDTO> dataList, Boolean isSupplement) {
        if (ObjectUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }
        return customerServicePeriodMonthMapper.selectPeriodIdsBatchByCreditCodeAndPeriod(dataList, isSupplement);
    }

    private Map<String, String> getConditionValue(String conditionValue) {
        return JSONObject.parseObject(conditionValue, Map.class);
    }

    public static void main(String[] args) {
//        SettlementOrderCondition condition1 = new SettlementOrderCondition().setConditionType(PERIOD_TAX_TYPE).setConditionValue("{\"periodTaxType\":\"1\"}");
//        SettlementOrderCondition condition2 = new SettlementOrderCondition().setConditionType(PERIOD_SERVICE_TYPE).setConditionValue("{\"periodServiceType\":\"1\"}");
//        SettlementOrderCondition condition3 = new SettlementOrderCondition().setConditionType(PERIOD_IN_ACCOUNT_END_TIME).setConditionValue("{\"startTime\":\"2024-09-01\",\"endTime\":\"2024-09-10\"}");
//        SettlementOrderCondition condition4 = new SettlementOrderCondition().setConditionType(PERIOD_TAGS).setConditionValue("{\"periodTagIncludeFlag\":\"2\",\"periodTagName\":\"医保&社保\"}");
//        List<SettlementOrderCondition> conditions = Arrays.asList(condition1, condition2, condition3, condition4);
//        System.out.println(buildPeriodMonthConditionSql(conditions, Lists.newArrayList(1L, 2L), false));
    }
}
