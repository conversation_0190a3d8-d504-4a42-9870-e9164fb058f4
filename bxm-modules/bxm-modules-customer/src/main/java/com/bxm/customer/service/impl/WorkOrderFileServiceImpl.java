package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.workOrder.WorkOrderFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.WorkOrderFile;
import com.bxm.customer.mapper.WorkOrderFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.IWorkOrderFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class WorkOrderFileServiceImpl extends ServiceImpl<WorkOrderFileMapper, WorkOrderFile> implements IWorkOrderFileService {

    @Autowired
    private FileService fileService;

    @Override
    public List<CommonFileVO> getByWorkOrderIdAndFileType(Long workOrderId, Integer workOrderFileType) {
        if (Objects.isNull(workOrderId)) {
            return Collections.emptyList();
        }
        List<WorkOrderFile> files = list(new LambdaQueryWrapper<WorkOrderFile>()
                .eq(WorkOrderFile::getWorkOrderId, workOrderId)
                .eq(WorkOrderFile::getIsDel, false)
                .eq(!Objects.isNull(workOrderFileType), WorkOrderFile::getFileType, workOrderFileType));
        if (ObjectUtils.isEmpty(files)) {
            return Collections.emptyList();
        }
        Map<String, RemoteAliFileDTO> fileInfoMap = fileService.getFileInfoBatch(files.stream().map(WorkOrderFile::getFileUrl).collect(Collectors.toList()));
        return files.stream().map(row -> {
            RemoteAliFileDTO fileInfo = fileInfoMap.get(row.getFileUrl());
            return CommonFileVO.builder()
                    .fileUrl(row.getFileUrl())
                    .fileName(row.getFileName())
                    .fullFileUrl(Objects.isNull(fileInfo) ? "" : fileInfo.getFullUrl())
                    .fileSize(Objects.isNull(fileInfo) ? 0L : fileInfo.getFileSize())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveNewFile(Long workOrderId, List<CommonFileVO> files, WorkOrderFileType workOrderFileType, String operName) {
        if (Objects.isNull(workOrderId) || ObjectUtils.isEmpty(files) || Objects.isNull(workOrderFileType)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        saveBatch(files.stream().map(file -> {
            WorkOrderFile workOrderFile = new WorkOrderFile().setWorkOrderId(workOrderId)
                    .setFileType(workOrderFileType.getCode())
                    .setFileName(file.getFileName())
                    .setFileUrl(file.getFileUrl());
            workOrderFile.setCreateBy(operName);
            workOrderFile.setCreateTime(now);
            return workOrderFile;
        }).collect(Collectors.toList()));
    }
}
