package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.SettlementOrderCondition;

/**
 * 结算单条件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Mapper
public interface SettlementOrderConditionMapper extends BaseMapper<SettlementOrderCondition>
{
    /**
     * 查询结算单条件
     * 
     * @param id 结算单条件主键
     * @return 结算单条件
     */
    public SettlementOrderCondition selectSettlementOrderConditionById(Long id);

    /**
     * 查询结算单条件列表
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结算单条件集合
     */
    public List<SettlementOrderCondition> selectSettlementOrderConditionList(SettlementOrderCondition settlementOrderCondition);

    /**
     * 新增结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    public int insertSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition);

    /**
     * 修改结算单条件
     * 
     * @param settlementOrderCondition 结算单条件
     * @return 结果
     */
    public int updateSettlementOrderCondition(SettlementOrderCondition settlementOrderCondition);

    /**
     * 删除结算单条件
     * 
     * @param id 结算单条件主键
     * @return 结果
     */
    public int deleteSettlementOrderConditionById(Long id);

    /**
     * 批量删除结算单条件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettlementOrderConditionByIds(Long[] ids);
}
