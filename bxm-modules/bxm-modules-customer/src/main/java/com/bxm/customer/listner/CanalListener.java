//package com.bxm.customer.listner;
//
//import com.alibaba.otter.canal.client.CanalConnector;
//import com.alibaba.otter.canal.protocol.CanalEntry;
//import com.alibaba.otter.canal.protocol.Message;
//import com.bxm.common.core.enums.DeliverType;
//import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
//import com.bxm.common.core.utils.StringUtils;
//import com.bxm.customer.domain.CustomerServicePeriodMonth;
//import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
//import com.bxm.customer.service.ICCustomerServiceService;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//
//@Component
//@Slf4j
//public class CanalListener {
//
//    private final CanalConnector canalConnector;
//
//    public CanalListener(CanalConnector canalConnector) {
//        this.canalConnector = canalConnector;
//    }
//
//    @Value("${spring.profiles.active}")
//    private String environment;
//
//    @Autowired
//    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;
//
//    @Autowired
//    private ICCustomerServiceService customerServiceService;
//
//    @PostConstruct
//    public void start() {
//        // 启动 Canal 客户端监听线程
//        String database = "prod".equalsIgnoreCase(environment) ? "bxm-prod" : "bxm-test";
//        List<String> tables = Lists.newArrayList();
//        tables.add(database + ".c_customer_deliver");
//        tables.add(database + ".c_customer_service_cashier_accounting");
//
//        // 创建线程池
////        ExecutorService executorService = Executors.newFixedThreadPool(10); // 根据需求调整线程池大小
//
//        new Thread(() -> {
//            try {
//                canalConnector.connect();
//                canalConnector.subscribe(String.join(",", tables)); // 监听指定数据库和表
//                canalConnector.rollback();
//
//                while (true) {
//                    Message message = null;
//                    long batchId = -1;
//
//                    try {
//                        message = canalConnector.getWithoutAck(100); // 获取最多 100 条消息
//                        batchId = message.getId();
//                        List<CanalEntry.Entry> entries = message.getEntries();
//
//                        if (batchId != -1 && entries.size() > 0) {
//                            // 立即确认消息，不等待处理完成
//                            canalConnector.ack(batchId);
//                            CompletableFuture.runAsync(() -> {
//                                handleEntries(entries); // 处理消息
//                            });
//                        } else {
//                            // 如果没有数据，短暂休眠，避免空循环
//                            Thread.sleep(100);
//                        }
//                    } catch (Exception e) {
//                        log.error("处理消息失败，进行回滚，原因: {}, batchId:{}", e.getMessage(), batchId);
//                        if (batchId != -1) {
//                            canalConnector.rollback(batchId); // 回滚消息
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                log.error("消费线程异常终止: {}", e.getMessage());
//            } finally {
//                canalConnector.disconnect(); // 确保资源释放
//                log.info("Canal 客户端已断开连接");
//            }
//        }).start();
//    }
//
//
//    @PreDestroy
//    public void stop() {
//        canalConnector.disconnect();
//    }
//
//    private void handleEntries(List<CanalEntry.Entry> entries) {
//        for (CanalEntry.Entry entry : entries) {
//            if (entry.getEntryType() != CanalEntry.EntryType.ROWDATA) {
//                continue;
//            }
//
//            try {
//                CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
//                String tableName = entry.getHeader().getTableName();
//                CanalEntry.EventType eventType = rowChange.getEventType();
//
//                for (CanalEntry.RowData rowData : rowChange.getRowDatasList()) {
//                    if (eventType == CanalEntry.EventType.INSERT) {
//                        handleInsert(tableName, rowData.getAfterColumnsList());
//                    } else if (eventType == CanalEntry.EventType.UPDATE) {
//                        handleUpdate(tableName, rowData.getBeforeColumnsList(), rowData.getAfterColumnsList());
//                    } else if (eventType == CanalEntry.EventType.DELETE) {
//                        handleDelete(tableName, rowData.getBeforeColumnsList());
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    private void handleInsert(String tableName, List<CanalEntry.Column> columns) {
////        System.out.println("INSERT INTO " + tableName + ": " + JSON.toJSONString(columns));
//        if (Objects.equals("c_customer_deliver", tableName)) {
//            String customerServicePeriodMonthId = getColumnValue(columns, "customer_service_period_month_id");
//            String status = getColumnValue(columns, "status");
//            String deliverType = getColumnValue(columns, "deliver_type");
//            if (StringUtils.isEmpty(customerServicePeriodMonthId) || StringUtils.isEmpty(status) || StringUtils.isEmpty(deliverType)) {
//                return;
//            }
//            Integer deliverTypeInt = Integer.parseInt(deliverType);
//            if (DeliverType.binlogUpdateDeliverType().contains(deliverTypeInt)) {
//                updateCustomerServicePeriodMonthDeliverStatus(Long.parseLong(customerServicePeriodMonthId), Integer.parseInt(status), deliverTypeInt);
//            }
//        }
//    }
//
//    private void handleUpdate(String tableName, List<CanalEntry.Column> beforeColumns, List<CanalEntry.Column> afterColumns) {
////        System.out.println("UPDATE " + tableName + ": BEFORE: " + JSON.toJSONString(beforeColumns) +
////                ", AFTER: " + JSON.toJSONString(afterColumns));
//        if (Objects.equals("c_customer_deliver", tableName)) {
//            log.info("tableName:{}, before:{}, after:{}", tableName, beforeColumns, afterColumns);
//            String beforeIsDel = getColumnValue(beforeColumns, "is_del");
//            String afterIsDel = getColumnValue(afterColumns, "is_del");
//            if (Objects.equals("0", beforeIsDel) && Objects.equals("1", afterIsDel)) {
//                // 代表是删除
//                String customerServicePeriodMonthId = getColumnValue(afterColumns, "customer_service_period_month_id");
//                if (StringUtils.isEmpty(customerServicePeriodMonthId)) {
//                    return;
//                }
//                customerServicePeriodMonthMapper.updateDeliverStatusById(Long.parseLong(customerServicePeriodMonthId));
//            } else {
//                String beforeStatus = getColumnValue(beforeColumns, "status");
//                String afterStatus = getColumnValue(afterColumns, "status");
//                if (!Objects.equals(beforeStatus, afterStatus)) {
//                    String customerServicePeriodMonthId = getColumnValue(afterColumns, "customer_service_period_month_id");
//                    String deliverType = getColumnValue(afterColumns, "deliver_type");
//                    if (StringUtils.isEmpty(customerServicePeriodMonthId) || StringUtils.isEmpty(afterStatus) || StringUtils.isEmpty(deliverType)) {
//                        return;
//                    }
//                    Integer deliverTypeInt = Integer.parseInt(deliverType);
//                    if (DeliverType.binlogUpdateDeliverType().contains(deliverTypeInt)) {
//                        updateCustomerServicePeriodMonthDeliverStatus(Long.parseLong(customerServicePeriodMonthId), Integer.parseInt(afterStatus), deliverTypeInt);
//                    }
//                }
//            }
//        } else if (Objects.equals("c_customer_service_cashier_accounting", tableName)) {
//            String type = getColumnValue(afterColumns, "type");
//            if (!Objects.equals(type, AccountingCashierType.INCOME.getCode().toString())) {
//                return;
//            }
//            String beforeIsDel = getColumnValue(beforeColumns, "is_del");
//            String afterIsDel = getColumnValue(afterColumns, "is_del");
//            if (Objects.equals("0", beforeIsDel) && Objects.equals("1", afterIsDel)) {
//                // 代表是删除
//                String customerServiceId = getColumnValue(afterColumns, "customer_service_id");
//                String id = getColumnValue(afterColumns, "id");
//                String period = getColumnValue(afterColumns, "period");
//                if (StringUtils.isEmpty(customerServiceId)) {
//                    return;
//                }
//                customerServiceService.updateLastInAccountIdByDelete(Long.parseLong(customerServiceId), Long.parseLong(id), period);
//            } else {
//                String afterMajorIncomeTotal = getColumnValue(afterColumns, "major_income_total");
//                String majorCostTotal = getColumnValue(afterColumns, "major_cost_total");
//                String profitTotal = getColumnValue(afterColumns, "profit_total");
//                String priorYearExpenseIncrease = getColumnValue(afterColumns, "prior_year_expense_increase");
//                String taxReportCount = getColumnValue(afterColumns, "tax_report_count");
//                String taxReportSalaryTotal = getColumnValue(afterColumns, "tax_report_salary_total");
//                if (!StringUtils.isEmpty(afterMajorIncomeTotal) || !StringUtils.isEmpty(majorCostTotal) || !StringUtils.isEmpty(profitTotal) || !StringUtils.isEmpty(priorYearExpenseIncrease) || !StringUtils.isEmpty(taxReportCount) || !StringUtils.isEmpty(taxReportSalaryTotal)) {
//                    String customerServiceId = getColumnValue(afterColumns, "customer_service_id");
//                    String id = getColumnValue(afterColumns, "id");
//                    String period = getColumnValue(afterColumns, "period");
//                    if (StringUtils.isEmpty(customerServiceId)) {
//                        return;
//                    }
//                    customerServiceService.updateLastInAccountId(Long.parseLong(customerServiceId), Long.parseLong(id), period);
//                }
//            }
//        }
//    }
//
//    private void handleDelete(String tableName, List<CanalEntry.Column> columns) {
////        System.out.println("DELETE FROM " + tableName + ": " + JSON.toJSONString(columns));
//    }
//
//    // 工具方法：提取列值
//    private String getColumnValue(List<CanalEntry.Column> columns, String columnName) {
//        for (CanalEntry.Column column : columns) {
//            if (column.getName().equalsIgnoreCase(columnName)) {
//                return column.getValue();
//            }
//        }
//        return null;
//    }
//
//    private void updateCustomerServicePeriodMonthDeliverStatus(Long customerServicePeriodMonthId, Integer status, Integer deliverType) {
//        CustomerServicePeriodMonth customerServicePeriodMonth = new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId);
//        if (Objects.equals(deliverType, DeliverType.MEDICAL_INSURANCE.getCode())) {
//            customerServicePeriodMonth.setMedicalDeliverStatus(status);
//        } else if (Objects.equals(deliverType, DeliverType.SOCIAL_INSURANCE.getCode())) {
//            customerServicePeriodMonth.setSocialDeliverStatus(status);
//        } else if (Objects.equals(deliverType, DeliverType.TAX.getCode())) {
//            customerServicePeriodMonth.setPersonTaxDeliverStatus(status);
//        } else if (Objects.equals(deliverType, DeliverType.TAX_OPERATING_INCOME.getCode())) {
//            customerServicePeriodMonth.setOperationTaxDeliverStatus(status);
//        } else if (Objects.equals(deliverType, DeliverType.NATIONAL_TAX.getCode())) {
//            customerServicePeriodMonth.setNationalTaxDeliverStatus(status);
//        } else if (Objects.equals(deliverType, DeliverType.PRE_AUTH.getCode())) {
//            customerServicePeriodMonth.setPreAuthDeliverStatus(status);
//        } else {
//            return;
//        }
//        customerServicePeriodMonthMapper.updateById(customerServicePeriodMonth);
//    }
//}
