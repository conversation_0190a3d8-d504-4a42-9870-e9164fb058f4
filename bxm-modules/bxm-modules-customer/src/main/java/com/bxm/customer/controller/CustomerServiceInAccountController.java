package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.Logical;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServiceInAccount;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.inAccount.*;
import com.bxm.customer.domain.vo.inAccount.*;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.ICustomerServiceInAccountService;
import com.bxm.customer.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 入账、入账交付Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/inAccount")
@Api(tags = "入账、入账交付")
public class CustomerServiceInAccountController extends BaseController {
    @Autowired
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    /**
     * 查询入账、入账交付列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询入账、入账交付列表", notes = "查询入账、入账交付列表")
    public TableDataInfo list(CustomerServiceInAccount customerServiceInAccount) {
        startPage();
        List<CustomerServiceInAccount> list = customerServiceInAccountService.selectCustomerServiceInAccountList(customerServiceInAccount);
        return getDataTable(list);
    }

    /**
     * 导出入账、入账交付列表
     */
    @RequiresPermissions("customer:account:export")
    @Log(title = "入账、入账交付", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出入账、入账交付列表", notes = "导出入账、入账交付列表")
    public void export(HttpServletResponse response, CustomerServiceInAccount customerServiceInAccount) {
        List<CustomerServiceInAccount> list = customerServiceInAccountService.selectCustomerServiceInAccountList(customerServiceInAccount);
        ExcelUtil<CustomerServiceInAccount> util = new ExcelUtil<CustomerServiceInAccount>(CustomerServiceInAccount.class);
        util.exportExcel(response, list, "入账、入账交付数据");
    }

    /**
     * 获取入账、入账交付详细信息
     */
    @RequiresPermissions("customer:account:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取入账、入账交付详细信息", notes = "获取入账、入账交付详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(customerServiceInAccountService.selectCustomerServiceInAccountById(id));
    }

    /**
     * 新增入账、入账交付
     */
    @RequiresPermissions("customer:account:add")
    @Log(title = "入账、入账交付", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增入账、入账交付", notes = "新增入账、入账交付")
    public AjaxResult add(@RequestBody CustomerServiceInAccount customerServiceInAccount) {
        return toAjax(customerServiceInAccountService.insertCustomerServiceInAccount(customerServiceInAccount));
    }

    /**
     * 修改入账、入账交付
     */
    @RequiresPermissions("customer:account:edit")
    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改入账、入账交付", notes = "修改入账、入账交付")
    public AjaxResult edit(@RequestBody CustomerServiceInAccount customerServiceInAccount) {
        return toAjax(customerServiceInAccountService.updateCustomerServiceInAccount(customerServiceInAccount));
    }

    /**
     * 删除入账、入账交付
     */
    @RequiresPermissions("customer:account:remove")
    @Log(title = "入账、入账交付", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除入账、入账交付", notes = "删除入账、入账交付")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(customerServiceInAccountService.deleteCustomerServiceInAccountByIds(ids));
    }

    //****** start self method ******  批量操作:
    //@RequiresPermissions("customer:handover:docHandoverList")
    @GetMapping("/inAccountList")
    @ApiOperation(value = "分页获取入账交付", notes = "分页获取入账交付")
    @RequiresPermissions(value = {"customer:inAccount:list", "customer:inAccount:miniList"}, logical = Logical.OR)
    public Result<IPage<InAccountDTO>> inAccountList(@RequestHeader("deptId") Long deptId, InAccountVO vo) {
        return Result.ok(customerServiceInAccountService.inAccountList(deptId, vo));
    }

    @ApiIgnore
    @PostMapping("/inAccountListInner")
    @ApiOperation(value = "分页获取入账交付-内部使用", notes = "分页获取入账交付-内部使用")
    public Result<List<InAccountDTO>> inAccountListInner(@RequestBody InAccountVO vo) {
        return Result.ok(customerServiceInAccountService.inAccountList(vo.getDeptId(), vo).getRecords());
    }

    //@RequiresPermissions("customer:customerService:exportCustomerServicePeriodMonthList")
    @Log(title = "入账、入账交付", businessType = BusinessType.EXPORT)
    @PostMapping("/exportInAccountList")
    @ApiOperation(value = "导出入账交付列表", notes = "导出入账交付列表")
    public void exportInAccountList(HttpServletResponse response, @RequestHeader("deptId") Long deptId, InAccountVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<InAccountDTO> list = customerServiceInAccountService.inAccountList(deptId, vo).getRecords();
        ExcelUtil<InAccountDTO> util = new ExcelUtil<>(InAccountDTO.class);
        util.exportExcel(response, list, "入账交付");
    }

    @PostMapping("/exportInAccountListAndUpload")
    public Result exportInAccountListAndUpload(HttpServletResponse response, @RequestHeader("deptId") Long deptId, InAccountVO vo) {
        String title = "交付-入账" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_ACCOUNTING);
        CompletableFuture.runAsync(() -> {
            try {
                List<InAccountDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<InAccountDTO> l = customerServiceInAccountService.inAccountList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<InAccountDTO> util = new ExcelUtil<>(InAccountDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @GetMapping("/getInAccountUpdateShow")
    @ApiOperation(value = "获取 编辑入账交付 的回显内容", notes = "获取 编辑入账交付 的回显内容")
    public Result<InAccountUpdateShowDTO> getInAccountUpdateShow(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceInAccountService.getInAccountUpdateShow(id));
    }

    @GetMapping("/getInAccountUpdateShowV2")
    @ApiOperation(value = "获取 编辑入账交付 的回显内容-V2", notes = "获取 编辑入账交付 的回显内容-V2")
    public Result<InAccountUpdateShowV2DTO> getInAccountUpdateShowV2(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceInAccountService.getInAccountUpdateShowV2(id));
    }

    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInAccount")
    @ApiOperation(value = "编辑入账交付", notes = "编辑入账交付")
    @RequiresPermissions("customer:inAccount:updateInAccount")
    public Result<Boolean> updateInAccount(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateInAccountVO vo) {
        customerServiceInAccountService.updateInAccount(deptId, vo);
        return Result.ok();
    }

    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInAccountV2")
    @ApiOperation(value = "编辑入账交付V2", notes = "编辑入账交付V2")
    @RequiresPermissions("customer:inAccount:updateInAccount")
    public Result<Integer> updateInAccountV2(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateInAccountV2VO vo) {
        logger.info("updateInAccount vo = {}", new Gson().toJson(vo));
        //return Result.ok(customerServiceInAccountService.updateInAccountV2(deptId, vo));
        return Result.ok(customerServiceInAccountService.updateInAccountV2_refactoring(deptId, vo));
    }

    @ApiIgnore
    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInAccountV2Inner")
    @ApiOperation(value = "编辑入账交付V2-内部调用", notes = "编辑入账交付V2-内部调用")
    public Result<Integer> updateInAccountV2Inner(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateInAccountV2VO vo) {
        logger.info("updateInAccountV2InnerupdateInAccountV2Inner vo = {}", new Gson().toJson(vo));
        //return Result.ok(customerServiceInAccountService.updateInAccountV2Inner(deptId, vo));
        return Result.ok(customerServiceInAccountService.updateInAccountV2Inner_refactoring(deptId, vo));
    }

    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInAccountV3")
    @ApiOperation(value = "编辑入账交付V3", notes = "编辑入账交付V3")
    @RequiresPermissions("customer:inAccount:updateInAccount")
    public Result<Integer> updateInAccountV3(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateInAccountV3VO vo) {
        return Result.ok(customerServiceInAccountService.updateInAccountV3(deptId, vo));
    }

    @ApiIgnore
    @Log(title = "入账、入账交付", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInAccountV3Inner")
    @ApiOperation(value = "编辑入账交付V3-内部调用", notes = "编辑入账交付V3-内部调用")
    public Result<Integer> updateInAccountV3Inner(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateInAccountV3VO vo) {
        logger.info("updateInAccountV3Inner vo = {}", new Gson().toJson(vo));
        return Result.ok(customerServiceInAccountService.updateInAccountV3Inner(deptId, vo));
    }

    @GetMapping("/getInAccountDetail")
    @ApiOperation(value = "获取入账交付详情", notes = "获取入账交付详情")
    public Result<InAccountDetailDTO> getInAccountDetail(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceInAccountService.getInAccountDetail(id));
    }

    /**
     * 批量入账或批量结账
     */
    @Log(title = "入账、入账交付", businessType = BusinessType.FROZE)
    @PostMapping("/inAccountInBatch")
    @ApiOperation(value = "批量入账或批量结账", notes = "批量入账或批量结账")
    @RequiresPermissions("customer:inAccount:inAccountInBatch")
    public Result<TCommonOperateDTO<CustomerServiceInAccount>> inAccountInBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid BatchOperateAccountInVO vo) {
        return Result.ok(customerServiceInAccountService.inAccountInBatch(deptId, vo));
    }

    @GetMapping("/getInAccountRpaUpdateShow")
    @ApiOperation(value = "获取RPA更新的回显内容", notes = "获取RPA更新的回显内容")
    public Result<InAccountRpaUpdateShowDTO> getInAccountRpaUpdateShow(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceInAccountService.getInAccountRpaUpdateShow(id));
    }

    @Log(title = "入账、入账交付", businessType = BusinessType.FROZE)
    @PostMapping("/inAccountRpaUpdateBatch")
    @ApiOperation(value = "批量RPA更新", notes = "批量RPA更新")
    public Result<TCommonOperateDTO<CustomerServiceInAccount>> inAccountRpaUpdateBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid BatchOperateInAccountRpaUpdateVO vo) {
        return Result.ok(customerServiceInAccountService.inAccountRpaUpdateBatch(deptId, vo));
    }

    @ApiIgnore
    @Log(title = "入账、入账交付", businessType = BusinessType.FROZE)
    @PostMapping("/inAccountRpaUpdateInner")
    @ApiOperation(value = "RPA更新-inner", notes = "RPA更新-inner")
    public Result<Integer> inAccountRpaUpdateInner(@RequestHeader("deptId") Long deptId, @RequestBody OperateInAccountRpaUpdateVO vo) {
        logger.info("inAccountRpaUpdateBatchInner vo = {}", new Gson().toJson(vo));
        return Result.ok(customerServiceInAccountService.inAccountRpaUpdateInner(deptId, vo));
    }

    //海绵要删除的
    // TODO: 2024/7/25  海绵要删除的
    @Autowired
    private CCustomerServiceMapper cCustomerServiceMapper;
    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    //test
    @GetMapping("/addInAccountFromPeriodTest")
    @ApiOperation(value = "从账期生成入账交付单", notes = "从账期生成入账交付单")
    public Result<Boolean> addInAccountFromPeriodTest(@RequestParam("customerServiceId") Long customerServiceId, @RequestParam("periodId") Long periodId) {

        CCustomerService cCustomerService = cCustomerServiceMapper.selectById(customerServiceId);
        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(periodId);

        customerServiceInAccountService.addInAccountFromPeriod(cCustomerService, customerServicePeriodMonth);

        return Result.ok();
    }

    @GetMapping("/addInAccountForHistory")
    @ApiOperation(value = "历史数据处理：历史账期生成对应入账交付单", notes = "历史数据处理：历史账期生成对应入账交付单")
    public Result<Boolean> addInAccountForHistory() {
        customerServiceInAccountService.addInAccountForHistory();
        return Result.ok();
    }

    @GetMapping("/getByPeriodId")
    @ApiOperation("内部接口，批量交付会调用")
    public Result<CustomerServiceInAccount> getByPeriodId(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(customerServiceInAccountService.getByPeriodId(customerServicePeriodMonthId));
    }

    @PostMapping("/getByPeriodIdList")
    @ApiOperation("内部接口，批量交付会调用")
    public Result<List<CustomerServiceInAccount>> getByPeriodIdList(@RequestBody List<Long> periodIds) {
        return Result.ok(customerServiceInAccountService.getByPeriodIdList(periodIds));
    }

    @PostMapping("/urgeInAccount")
    @ApiOperation("催账，单个操作传id")
    public Result urgeInAccount(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        customerServiceInAccountService.urgeInAccount(vo.getId(), SecurityUtils.getUserId(), deptId);
        return Result.ok();
    }
}
