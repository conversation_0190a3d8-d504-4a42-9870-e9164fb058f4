package com.bxm.customer.domain.dto;

import com.bxm.customer.domain.dto.accoutingCashier.CustomerServicePeriodMonthAccountingCashierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/14 15:27
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceMonthPeriodDetailDTO {
    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名称")
    private String customerName;

    @ApiModelProperty("服务编号")
    private String serviceNumber;

    @ApiModelProperty("信用代码")
    private String creditCode;

    @ApiModelProperty("税号")
    private String taxNumber;

    @ApiModelProperty("服务类型,1-代账，2-补账")
    private Integer serviceType;

    //******** START 服务信息
    @ApiModelProperty("标签")
    private List<TagDTO> tagList;

    @ApiModelProperty("业务公司id")
    private Long advisorTopDeptId;

    @ApiModelProperty("业务公司id全路径")
    private List<Long> advisorTopDeptIdPath;

    @ApiModelProperty("业务公司名称")
    private String advisorTopDeptName;

    @ApiModelProperty("业务公司id——冗余")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称——冗余")
    private String businessDeptName;

    @ApiModelProperty("顾问组别id")
    private Long advisorDeptId;

    @ApiModelProperty("顾问组别id全路径")
    private List<Long> advisorDeptIdPath;

    @ApiModelProperty("顾问组别名称")
    private String advisorDeptName;

    @ApiModelProperty("顾问员工Ids")
    private List<Long> advisorEmployeeIds;

    @ApiModelProperty("顾问员工名称")
    private String advisorEmployeeName;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    @ApiModelProperty("会计区域id全路径")
    private List<Long> accountingTopDeptIdPath;

    @ApiModelProperty("会计区域名称")
    private String accountingTopDeptName;

    @ApiModelProperty("会计组别id")
    private Long accountingDeptId;

    @ApiModelProperty("会计组别id全路径")
    private List<Long> accountingDeptIdPath;

    @ApiModelProperty("会计组别名称")
    private String accountingDeptName;

    @ApiModelProperty("会计员工ids")
    private List<Long> accountingEmployeeIds;

    @ApiModelProperty("会计员工名称")
    private String accountingEmployeeName;

    @ApiModelProperty("纳税人性质")
    private Integer taxType;
    //******** END 服务信息

    //******** START 交付
    @ApiModelProperty(value = "预认证")
    private CustomerServiceMonthPeriodDetailDeliverDTO preAuth;

    @ApiModelProperty(value = "医保")
    private CustomerServiceMonthPeriodDetailDeliverDTO medicalInsurance;

    @ApiModelProperty(value = "社保")
    private CustomerServiceMonthPeriodDetailDeliverDTO socialInsurance;

    @ApiModelProperty(value = "个税（工资薪金）")
    private CustomerServiceMonthPeriodDetailDeliverDTO tax;

    @ApiModelProperty(value = "个税（经营所得）")
    private CustomerServiceMonthPeriodDetailDeliverDTO taxOperating;

    @ApiModelProperty(value = "国税")
    private CustomerServiceMonthPeriodDetailDeliverDTO nationalTax;
    //******** START 交付

    //******** START 账务进度
//    @ApiModelProperty("入账交付单id")
//    private Long inAccountId;
//
//    @ApiModelProperty("入账时间，返回null表示未完成")
//    private String inTime;
//
//    @ApiModelProperty("结账时间，返回null表示未完成")
//    private String endTime;
//
//    @ApiModelProperty("有无材料交接")
//    private Boolean hasDocHandoverFile;
//
//    @ApiModelProperty("材料交接状态")
//    private String docHandoverFileStatus;

    @ApiModelProperty("银行流水，若有链接，需要调用接口/bxmCustomer/accountingCashier/bankPaymentByPeriodId获取银行流水列表")
    private CustomerServicePeriodMonthAccountingCashierDTO bankPayment;

    @ApiModelProperty("入账，若有链接，需要调用接口/bxmCustomer/accountingCashier/inAccountByPeriodId获取入账/改账列表")
    private CustomerServicePeriodMonthAccountingCashierDTO inAccount;

    @ApiModelProperty("结账状态，1-未入账，2-已入账未结账，3-已结账")
    private Integer settleAccountStatus;

    @ApiModelProperty("结账状态")
    private String settleAccountStatusStr;

    @ApiModelProperty("材料数量，需要调用接口/bxmCustomer/accountingCashier/materialFilesByPeriodId获取账期材料列表")
    private Long materialFileCount;
    //******** END 账务进度

    @ApiModelProperty("顾问备注")
    private String advisorRemark;

    @ApiModelProperty("是否可以修改顾问备注")
    private Boolean canModifyAdvisorRemark;

    @ApiModelProperty("会计备注")
    private String accountingRemark;

    @ApiModelProperty("是否可以修改会计备注")
    private Boolean canModifyAccountingRemark;
}
