package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.inAccount.InAccountFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceInAccountFile;
import com.bxm.customer.domain.vo.inAccount.file.GetByInAccountAndTypeVO;
import com.bxm.customer.mapper.CustomerServiceInAccountFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.ICustomerServiceInAccountFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 入账交付 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class CustomerServiceInAccountFileServiceImpl extends ServiceImpl<CustomerServiceInAccountFileMapper, CustomerServiceInAccountFile> implements ICustomerServiceInAccountFileService {
    @Autowired
    private CustomerServiceInAccountFileMapper customerServiceInAccountFileMapper;

    @Autowired
    private FileService fileService;

    /**
     * 查询入账交付 附件
     *
     * @param id 入账交付 附件主键
     * @return 入账交付 附件
     */
    @Override
    public CustomerServiceInAccountFile selectCustomerServiceInAccountFileById(Long id) {
        return customerServiceInAccountFileMapper.selectCustomerServiceInAccountFileById(id);
    }

    /**
     * 查询入账交付 附件列表
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 入账交付 附件
     */
    @Override
    public List<CustomerServiceInAccountFile> selectCustomerServiceInAccountFileList(CustomerServiceInAccountFile customerServiceInAccountFile) {
        return customerServiceInAccountFileMapper.selectCustomerServiceInAccountFileList(customerServiceInAccountFile);
    }

    /**
     * 新增入账交付 附件
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    @Override
    public int insertCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile) {
        customerServiceInAccountFile.setCreateTime(DateUtils.getNowDate());
        return customerServiceInAccountFileMapper.insertCustomerServiceInAccountFile(customerServiceInAccountFile);
    }

    /**
     * 修改入账交付 附件
     *
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    @Override
    public int updateCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile) {
        customerServiceInAccountFile.setUpdateTime(DateUtils.getNowDate());
        return customerServiceInAccountFileMapper.updateCustomerServiceInAccountFile(customerServiceInAccountFile);
    }

    /**
     * 批量删除入账交付 附件
     *
     * @param ids 需要删除的入账交付 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInAccountFileByIds(Long[] ids) {
        return customerServiceInAccountFileMapper.deleteCustomerServiceInAccountFileByIds(ids);
    }

    /**
     * 删除入账交付 附件信息
     *
     * @param id 入账交付 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInAccountFileById(Long id) {
        return customerServiceInAccountFileMapper.deleteCustomerServiceInAccountFileById(id);
    }

    @Override
    public List<CustomerServiceInAccountFile> selectByFileType(List<Long> inAccountIds, List<InAccountFileType> inAccountFileTypes) {
        List<CustomerServiceInAccountFile> result = Collections.emptyList();

        if (ObjectUtils.isEmpty(inAccountFileTypes)) {
            return result;
        }

        result = list(
                new LambdaQueryWrapper<CustomerServiceInAccountFile>()
                        .in(CustomerServiceInAccountFile::getCustomerServiceInAccountId, inAccountIds)
                        .in(CustomerServiceInAccountFile::getFileType, inAccountFileTypes.stream().map(InAccountFileType::getCode).distinct().collect(Collectors.toList()))
        );

        return result;
    }

    @Override
    public Map<Long, Map<Integer, List<CustomerServiceInAccountFile>>> selectMapByFileType(List<Long> inAccountIds, List<InAccountFileType> inAccountFileTypes) {
        List<CustomerServiceInAccountFile> files = selectByFileType(inAccountIds, inAccountFileTypes);
        return files.stream()
                .collect(
                        Collectors.groupingBy(CustomerServiceInAccountFile::getCustomerServiceInAccountId,
                                Collectors.groupingBy(CustomerServiceInAccountFile::getFileType)));
    }

    @Override
    public List<CustomerServiceInAccountFile> selectByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes) {
        List<CustomerServiceInAccountFile> result = Collections.emptyList();
        if (inAccountId == null) {
            return result;
        }

        if (ObjectUtils.isEmpty(inAccountFileTypes)) {
            result = list(new LambdaQueryWrapper<CustomerServiceInAccountFile>()
                    .eq(CustomerServiceInAccountFile::getCustomerServiceInAccountId, inAccountId)
            );
        } else {
            result = list(new LambdaQueryWrapper<CustomerServiceInAccountFile>()
                    .eq(CustomerServiceInAccountFile::getCustomerServiceInAccountId, inAccountId)
                    .in(CustomerServiceInAccountFile::getFileType, inAccountFileTypes.stream().map(InAccountFileType::getCode).distinct().collect(Collectors.toList()))
            );
        }

        return result;
    }

    @Override
    public Map<Integer, List<CustomerServiceInAccountFile>> selectMapByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes) {
        List<CustomerServiceInAccountFile> files = selectByInAccount(inAccountId, inAccountFileTypes);
        return files.stream().collect(Collectors.groupingBy(CustomerServiceInAccountFile::getFileType));
    }

    @Override
    public Map<String, List<CustomerServiceInAccountFile>> selectMapSubKeyByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes) {
        List<CustomerServiceInAccountFile> files = selectByInAccount(inAccountId, inAccountFileTypes);
        return files.stream().collect(Collectors.groupingBy(row -> key(row.getFileType(), row.getSubFileType())));
    }

    @Override
    public void deleteByInAccount(Long inAccountId, List<InAccountFileType> inAccountFileTypes) {
        if (inAccountId != null) {
            if (ObjectUtils.isEmpty(inAccountFileTypes)) {
                remove(new LambdaQueryWrapper<CustomerServiceInAccountFile>()
                        .eq(CustomerServiceInAccountFile::getCustomerServiceInAccountId, inAccountId));
            } else {
                remove(new LambdaQueryWrapper<CustomerServiceInAccountFile>()
                        .eq(CustomerServiceInAccountFile::getCustomerServiceInAccountId, inAccountId)
                        .in(CustomerServiceInAccountFile::getFileType, inAccountFileTypes.stream().map(InAccountFileType::getCode).distinct().collect(Collectors.toList()))
                );
            }
        }
    }

    @Override
    public void saveFile(Long inAccountId, List<CommonFileVO> files, InAccountFileType inAccountFileTypes, String subFileType) {
        if (!ObjectUtils.isEmpty(files)) {
            Integer fileType = inAccountFileTypes.getCode();
            saveBatch(
                    files.stream().map(f -> new CustomerServiceInAccountFile()
                            .setCustomerServiceInAccountId(inAccountId)
                            .setFileName(f.getFileName())
                            .setFileType(fileType)
                            .setSubFileType(subFileType)
                            .setFileUrl(f.getFileUrl()))
                            .collect(Collectors.toList())
            );
        }
    }

    @Override
    public List<CommonFileVO> covToCommonFileVO(List<CustomerServiceInAccountFile> files) {
        Map<String, RemoteAliFileDTO> fileInfoMap = ObjectUtils.isEmpty(files) ? Maps.newHashMap() :
                fileService.getFileInfoBatch(files.stream().map(CustomerServiceInAccountFile::getFileUrl).collect(Collectors.toList()));
        return ObjectUtils.isEmpty(files) ? Lists.newArrayList()
                : files.stream()
                .map(f -> {
                    RemoteAliFileDTO fileInfo = fileInfoMap.get(f.getFileUrl());
                    return CommonFileVO.builder()
                            .fileName(f.getFileName())
                            .fileUrl(f.getFileUrl())
                            .fullFileUrl(Objects.isNull(fileInfo) ? "" : fileInfo.getFullUrl())
                            .fileSize(Objects.isNull(fileInfo) ? 0 : fileInfo.getFileSize())
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<CommonFileVO>> getByInAccountAndTypes(GetByInAccountAndTypeVO vo) {
        Map<Integer, List<CommonFileVO>> result = Maps.newHashMap();

        Long inAccountId = vo.getInAccountId();
        List<Integer> inAccountFileTypes = vo.getInAccountFileTypes();

        if (inAccountId == null) {
            return result;
        }

        Map<Integer, List<CustomerServiceInAccountFile>> source = selectMapByInAccount(
                inAccountId,
                ObjectUtils.isEmpty(inAccountFileTypes) ? Lists.newArrayList() : inAccountFileTypes.stream().map(InAccountFileType::getByCode).collect(Collectors.toList())
        );
        for (Map.Entry<Integer, List<CustomerServiceInAccountFile>> entry : source.entrySet()) {
            result.put(
                    entry.getKey(),
                    covToCommonFileVO(entry.getValue())
            );
        }

        return result;
    }

    public static String key(Integer fileType, String subFileType) {
        return fileType + "_" + subFileType;
    }
}
