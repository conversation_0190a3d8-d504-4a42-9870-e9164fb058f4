package com.bxm.customer.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.DownloadStatus;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.PersonTaxRpaVO;
import com.bxm.thirdpart.api.domain.ReportTableDownloadSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.OpenApiDownloadDeliverRelationMapper;
import com.bxm.customer.domain.OpenApiDownloadDeliverRelation;
import com.bxm.customer.service.IOpenApiDownloadDeliverRelationService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 个税申报下载轮询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-06
 */
@Service
public class OpenApiDownloadDeliverRelationServiceImpl extends ServiceImpl<OpenApiDownloadDeliverRelationMapper, OpenApiDownloadDeliverRelation> implements IOpenApiDownloadDeliverRelationService
{
    @Autowired
    private OpenApiDownloadDeliverRelationMapper openApiDownloadDeliverRelationMapper;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    /**
     * 查询个税申报下载轮询
     * 
     * @param id 个税申报下载轮询主键
     * @return 个税申报下载轮询
     */
    @Override
    public OpenApiDownloadDeliverRelation selectOpenApiDownloadDeliverRelationById(Long id)
    {
        return openApiDownloadDeliverRelationMapper.selectOpenApiDownloadDeliverRelationById(id);
    }

    /**
     * 查询个税申报下载轮询列表
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 个税申报下载轮询
     */
    @Override
    public List<OpenApiDownloadDeliverRelation> selectOpenApiDownloadDeliverRelationList(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation)
    {
        return openApiDownloadDeliverRelationMapper.selectOpenApiDownloadDeliverRelationList(openApiDownloadDeliverRelation);
    }

    /**
     * 新增个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    @Override
    public int insertOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation)
    {
        openApiDownloadDeliverRelation.setCreateTime(DateUtils.getNowDate());
        return openApiDownloadDeliverRelationMapper.insertOpenApiDownloadDeliverRelation(openApiDownloadDeliverRelation);
    }

    /**
     * 修改个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    @Override
    public int updateOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation)
    {
        openApiDownloadDeliverRelation.setUpdateTime(DateUtils.getNowDate());
        return openApiDownloadDeliverRelationMapper.updateOpenApiDownloadDeliverRelation(openApiDownloadDeliverRelation);
    }

    /**
     * 批量删除个税申报下载轮询
     * 
     * @param ids 需要删除的个税申报下载轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiDownloadDeliverRelationByIds(Long[] ids)
    {
        return openApiDownloadDeliverRelationMapper.deleteOpenApiDownloadDeliverRelationByIds(ids);
    }

    /**
     * 删除个税申报下载轮询信息
     * 
     * @param id 个税申报下载轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiDownloadDeliverRelationById(Long id)
    {
        return openApiDownloadDeliverRelationMapper.deleteOpenApiDownloadDeliverRelationById(id);
    }

    @Override
    public void createRelation(Long deliverId, String downloadId) {
        save(new OpenApiDownloadDeliverRelation().setDeliverId(deliverId)
                .setDownloadId(downloadId)
                .setStatus(DownloadStatus.PENDING.getCode()));
    }

    @Override
    public void updateStatusByDownloadId(String downloadId, String downloadUrl, String status) {
        update(new LambdaUpdateWrapper<OpenApiDownloadDeliverRelation>()
                .eq(OpenApiDownloadDeliverRelation::getDownloadId, downloadId)
                .set(OpenApiDownloadDeliverRelation::getDwonloadUrl, downloadUrl)
                .set(OpenApiDownloadDeliverRelation::getStatus, status));
    }

    @Override
    @Transactional
    public void rpaReportTableDownloadTask() {
        List<OpenApiDownloadDeliverRelation> relations = list(new LambdaQueryWrapper<OpenApiDownloadDeliverRelation>()
                .eq(OpenApiDownloadDeliverRelation::getStatus, DownloadStatus.PENDING.getCode()));
        if (ObjectUtils.isEmpty(relations)) {
            return;
        }
        updateBatchById(relations.stream().map(relation -> new OpenApiDownloadDeliverRelation().setId(relation.getId()).setStatus(DownloadStatus.SEARCHING.getCode())).collect(Collectors.toList()));

        remoteThirdpartService.personalIncomeTtaxDownloadCheck(ReportTableDownloadSearchVO.builder()
                        .downloadIds(relations.stream().map(OpenApiDownloadDeliverRelation::getDownloadId).distinct().collect(Collectors.toList()))
                .build(), SecurityConstants.INNER);
    }
}
