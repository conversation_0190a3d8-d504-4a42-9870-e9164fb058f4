package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerSysAccount;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferSysAccountDTO;

/**
 * 新户流转系统账号Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerSysAccountService extends IService<NewCustomerSysAccount>
{
    /**
     * 查询新户流转系统账号
     * 
     * @param id 新户流转系统账号主键
     * @return 新户流转系统账号
     */
    public NewCustomerSysAccount selectNewCustomerSysAccountById(Long id);

    /**
     * 查询新户流转系统账号列表
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 新户流转系统账号集合
     */
    public List<NewCustomerSysAccount> selectNewCustomerSysAccountList(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 新增新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    public int insertNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 修改新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    public int updateNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 批量删除新户流转系统账号
     * 
     * @param ids 需要删除的新户流转系统账号主键集合
     * @return 结果
     */
    public int deleteNewCustomerSysAccountByIds(Long[] ids);

    /**
     * 删除新户流转系统账号信息
     * 
     * @param id 新户流转系统账号主键
     * @return 结果
     */
    public int deleteNewCustomerSysAccountById(Long id);

    List<NewCustomerSysAccount> selectByCustomerServiceId(Long newCustomerTransferId);

    Map<Long, List<NewCustomerSysAccount>> selectMapByCustomerServiceIds(List<Long> newCustomerTransferIds);

    void removeAndCreateSysAccount(Long newCustomerTransferId, List<NewCustomerTransferSysAccountDTO> sysAccountList);
}
