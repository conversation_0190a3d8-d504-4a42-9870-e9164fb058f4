package com.bxm.customer.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.BusinessDdlRecordMapper;
import com.bxm.customer.domain.BusinessDdlRecord;
import com.bxm.customer.service.IBusinessDdlRecordService;

/**
 * ddl修改记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
public class BusinessDdlRecordServiceImpl extends ServiceImpl<BusinessDdlRecordMapper, BusinessDdlRecord> implements IBusinessDdlRecordService
{
    @Autowired
    private BusinessDdlRecordMapper businessDdlRecordMapper;

    /**
     * 查询ddl修改记录
     * 
     * @param id ddl修改记录主键
     * @return ddl修改记录
     */
    @Override
    public BusinessDdlRecord selectBusinessDdlRecordById(Long id)
    {
        return businessDdlRecordMapper.selectBusinessDdlRecordById(id);
    }

    /**
     * 查询ddl修改记录列表
     * 
     * @param businessDdlRecord ddl修改记录
     * @return ddl修改记录
     */
    @Override
    public List<BusinessDdlRecord> selectBusinessDdlRecordList(BusinessDdlRecord businessDdlRecord)
    {
        return businessDdlRecordMapper.selectBusinessDdlRecordList(businessDdlRecord);
    }

    /**
     * 新增ddl修改记录
     * 
     * @param businessDdlRecord ddl修改记录
     * @return 结果
     */
    @Override
    public int insertBusinessDdlRecord(BusinessDdlRecord businessDdlRecord)
    {
        businessDdlRecord.setCreateTime(DateUtils.getNowDate());
        return businessDdlRecordMapper.insertBusinessDdlRecord(businessDdlRecord);
    }

    /**
     * 修改ddl修改记录
     * 
     * @param businessDdlRecord ddl修改记录
     * @return 结果
     */
    @Override
    public int updateBusinessDdlRecord(BusinessDdlRecord businessDdlRecord)
    {
        businessDdlRecord.setUpdateTime(DateUtils.getNowDate());
        return businessDdlRecordMapper.updateBusinessDdlRecord(businessDdlRecord);
    }

    /**
     * 批量删除ddl修改记录
     * 
     * @param ids 需要删除的ddl修改记录主键
     * @return 结果
     */
    @Override
    public int deleteBusinessDdlRecordByIds(Long[] ids)
    {
        return businessDdlRecordMapper.deleteBusinessDdlRecordByIds(ids);
    }

    /**
     * 删除ddl修改记录信息
     * 
     * @param id ddl修改记录主键
     * @return 结果
     */
    @Override
    public int deleteBusinessDdlRecordById(Long id)
    {
        return businessDdlRecordMapper.deleteBusinessDdlRecordById(id);
    }

    @Override
    @Async
    public void saveDdlRecord(Long businessId, BusinessLogBusinessType businessType, Long userId, String ddl) {
        BusinessDdlRecord businessDdlRecord = new BusinessDdlRecord().setBusinessId(businessId).setBusinessType(businessType.getCode()).setDdl(StringUtils.isEmpty(ddl) ? null : LocalDate.parse(ddl, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        businessDdlRecord.setCreateBy(userId.toString());
        save(businessDdlRecord);
    }
}
