package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationRequest;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.enums.ValueAddedExportOperationType;

import java.util.List;
import java.util.Map;

/**
 * 导入操作策略接口
 *
 * 定义不同操作类型的导入处理策略
 * 每种操作类型对应不同的业务逻辑：
 * 1. DELIVERY - 交付操作：修改状态为已交付待确认
 * 2. SUPPLEMENT_DELIVERY - 补充附件：不修改状态，仅添加文件
 * 3. DEDUCTION - 扣款操作：修改状态为已扣款
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface ImportOperationStrategy {

    /**
     * 获取策略支持的操作类型
     *
     * @return 支持的操作类型
     */
    ValueAddedExportOperationType getSupportedOperationType();

    /**
     * 执行导入操作
     *
     * 包含完整的导入流程：
     * 1. 验证交付单状态
     * 2. 解析模板文件
     * 3. 处理附件文件（如果有）
     * 4. 执行状态修改（根据操作类型）
     * 5. 保存文件记录
     *
     * @param orders 交付单列表
     * @param request 导入请求参数
     * @param templateData 从Excel模板解析的数据
     * @param extractedFiles 解压后的文件信息（文件名 -> 文件路径）
     * @return 导入操作结果
     */
    BatchImportOperationResult executeImport(
            List<ValueAddedDeliveryOrder> orders,
            BatchImportOperationRequest request,
            Map<String, Object> templateData,
            Map<String, String> extractedFiles
    );

    /**
     * 验证交付单状态是否允许当前操作
     *
     * @param order 交付单
     * @throws IllegalArgumentException 当状态不允许操作时抛出
     */
    void validateOrderStatus(ValueAddedDeliveryOrder order);

    /**
     * 获取目标状态（如果需要修改状态）
     *
     * @param currentStatus 当前状态
     * @return 目标状态，如果不需要修改状态则返回null
     */
    String getTargetStatus(String currentStatus);

    /**
     * 验证模板数据
     *
     * @param templateData 模板数据
     * @param orderNos 交付单编号列表
     * @throws IllegalArgumentException 当模板数据验证失败时抛出
     */
    void validateTemplateData(Map<String, Object> templateData, List<String> orderNos);

    /**
     * 处理文件保存
     *
     * @param order 交付单
     * @param extractedFiles 解压后的文件信息
     * @param request 导入请求
     * @return 保存成功的文件数量
     */
    int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationRequest request
    );

    /**
     * 获取操作描述
     *
     * @return 操作描述
     */
    default String getOperationDescription() {
        return getSupportedOperationType().getDescription();
    }
}
