package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceOtherInfo;

/**
 * 客户服务其他信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface CustomerServiceOtherInfoMapper extends BaseMapper<CustomerServiceOtherInfo>
{
    /**
     * 查询客户服务其他信息
     * 
     * @param id 客户服务其他信息主键
     * @return 客户服务其他信息
     */
    public CustomerServiceOtherInfo selectCustomerServiceOtherInfoById(Long id);

    /**
     * 查询客户服务其他信息列表
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 客户服务其他信息集合
     */
    public List<CustomerServiceOtherInfo> selectCustomerServiceOtherInfoList(CustomerServiceOtherInfo customerServiceOtherInfo);

    /**
     * 新增客户服务其他信息
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 结果
     */
    public int insertCustomerServiceOtherInfo(CustomerServiceOtherInfo customerServiceOtherInfo);

    /**
     * 修改客户服务其他信息
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 结果
     */
    public int updateCustomerServiceOtherInfo(CustomerServiceOtherInfo customerServiceOtherInfo);

    /**
     * 删除客户服务其他信息
     * 
     * @param id 客户服务其他信息主键
     * @return 结果
     */
    public int deleteCustomerServiceOtherInfoById(Long id);

    /**
     * 批量删除客户服务其他信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceOtherInfoByIds(Long[] ids);
}
