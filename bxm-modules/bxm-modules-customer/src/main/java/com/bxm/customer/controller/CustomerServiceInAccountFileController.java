package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceInAccountFile;
import com.bxm.customer.domain.vo.inAccount.file.GetByInAccountAndTypeVO;
import com.bxm.customer.service.ICustomerServiceInAccountFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 入账交付 附件Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/inAccountFile")
@Api(tags = "入账交付 附件")
public class CustomerServiceInAccountFileController extends BaseController {
    @Autowired
    private ICustomerServiceInAccountFileService customerServiceInAccountFileService;

    /**
     * 查询入账交付 附件列表
     */
    @RequiresPermissions("customer:file:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询入账交付 附件列表", notes = "查询入账交付 附件列表")
    public TableDataInfo list(CustomerServiceInAccountFile customerServiceInAccountFile) {
        startPage();
        List<CustomerServiceInAccountFile> list = customerServiceInAccountFileService.selectCustomerServiceInAccountFileList(customerServiceInAccountFile);
        return getDataTable(list);
    }

    /**
     * 导出入账交付 附件列表
     */
    @RequiresPermissions("customer:file:export")
    @Log(title = "入账交付 附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出入账交付 附件列表", notes = "导出入账交付 附件列表")
    public void export(HttpServletResponse response, CustomerServiceInAccountFile customerServiceInAccountFile) {
        List<CustomerServiceInAccountFile> list = customerServiceInAccountFileService.selectCustomerServiceInAccountFileList(customerServiceInAccountFile);
        ExcelUtil<CustomerServiceInAccountFile> util = new ExcelUtil<CustomerServiceInAccountFile>(CustomerServiceInAccountFile.class);
        util.exportExcel(response, list, "入账交付 附件数据");
    }

    /**
     * 获取入账交付 附件详细信息
     */
    @RequiresPermissions("customer:file:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取入账交付 附件详细信息", notes = "获取入账交付 附件详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(customerServiceInAccountFileService.selectCustomerServiceInAccountFileById(id));
    }

    /**
     * 新增入账交付 附件
     */
    @RequiresPermissions("customer:file:add")
    @Log(title = "入账交付 附件", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增入账交付 附件", notes = "新增入账交付 附件")
    public AjaxResult add(@RequestBody CustomerServiceInAccountFile customerServiceInAccountFile) {
        return toAjax(customerServiceInAccountFileService.insertCustomerServiceInAccountFile(customerServiceInAccountFile));
    }

    /**
     * 修改入账交付 附件
     */
    @RequiresPermissions("customer:file:edit")
    @Log(title = "入账交付 附件", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改入账交付 附件", notes = "修改入账交付 附件")
    public AjaxResult edit(@RequestBody CustomerServiceInAccountFile customerServiceInAccountFile) {
        return toAjax(customerServiceInAccountFileService.updateCustomerServiceInAccountFile(customerServiceInAccountFile));
    }

    /**
     * 删除入账交付 附件
     */
    @RequiresPermissions("customer:file:remove")
    @Log(title = "入账交付 附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除入账交付 附件", notes = "删除入账交付 附件")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(customerServiceInAccountFileService.deleteCustomerServiceInAccountFileByIds(ids));
    }

    //****** start self method ******
    @GetMapping("/getByInAccountAndTypes")
    @ApiOperation(value = "根据入账交付ID和附件类型获取附件", notes = "根据入账交付ID和附件类型获取附件")
    public Result<Map<Integer, List<CommonFileVO>>> getByInAccountAndTypes(
            @RequestHeader("deptId") Long deptId,
            GetByInAccountAndTypeVO vo
    ) {
        return Result.ok(customerServiceInAccountFileService.getByInAccountAndTypes(vo));
    }
}
