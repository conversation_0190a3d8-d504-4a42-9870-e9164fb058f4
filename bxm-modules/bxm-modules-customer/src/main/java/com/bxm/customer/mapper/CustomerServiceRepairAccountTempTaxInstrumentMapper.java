package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument;

/**
 * 补账临时的 材料交接票据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceRepairAccountTempTaxInstrumentMapper extends BaseMapper<CustomerServiceRepairAccountTempTaxInstrument>
{
    /**
     * 查询补账临时的 材料交接票据
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 补账临时的 材料交接票据
     */
    public CustomerServiceRepairAccountTempTaxInstrument selectCustomerServiceRepairAccountTempTaxInstrumentById(Long id);

    /**
     * 查询补账临时的 材料交接票据列表
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 补账临时的 材料交接票据集合
     */
    public List<CustomerServiceRepairAccountTempTaxInstrument> selectCustomerServiceRepairAccountTempTaxInstrumentList(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 新增补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 修改补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 删除补账临时的 材料交接票据
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentById(Long id);

    /**
     * 批量删除补账临时的 材料交接票据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentByIds(Long[] ids);
}
