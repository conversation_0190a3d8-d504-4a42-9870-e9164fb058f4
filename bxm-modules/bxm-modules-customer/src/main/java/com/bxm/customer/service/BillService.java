package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.dto.bill.BillDTO;
import com.bxm.customer.domain.dto.bill.BillDetailDTO;
import com.bxm.customer.domain.dto.bill.BillReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewOrderDTO;
import com.bxm.customer.domain.vo.bill.RejectBillVO;
import com.bxm.customer.domain.vo.bill.RemovePushVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderPushVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BillService {

    @Autowired
    private IBillService billService;

    public IPage<BillDTO> billList(Long deptId, String billTitle, Long businessDeptId, Integer status, String createTimeStart, String createTimeEnd, Integer pageNum, Integer pageSize) {
        return billService.billList(deptId, billTitle, businessDeptId, status, createTimeStart, createTimeEnd, pageNum, pageSize);
    }

    public BillDetailDTO billDetail(Long id) {
        return billService.billDetail(id);
    }

    public void delete(CommonIdVO vo) {
        billService.delete(vo);
    }

    public List<SettlementPushReviewDTO> rePushReview(CommonIdVO vo) {
        return billService.rePushReview(vo);
    }

    public void confirmRePush(SettlementOrderPushVO vo) {
        billService.confirmRePush(vo);
    }

    public void removePush(RemovePushVO vo) {
        billService.removePush(vo);
    }

    public void revokeBill(CommonIdVO vo) {
        billService.revokeBill(vo);
    }

    public List<SettlementPushReviewOrderDTO> settlementListByBillId(Long billId) {
        return billService.settlementListByBillId(billId);
    }

    public void rejectBill(RejectBillVO vo) {
        billService.rejectBill(vo);
    }

    public void confirmBill(CommonIdVO vo) {
        billService.confirmBill(vo);
    }

    public BillReviewDTO billReview(Long billId) {
        return billService.billReview(billId);
    }
}
