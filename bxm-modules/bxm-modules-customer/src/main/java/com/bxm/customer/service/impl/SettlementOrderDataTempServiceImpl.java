package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverResult;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.accountingCashier.BankPaymentResult;
import com.bxm.common.core.enums.inAccount.InAccountDeliverResult;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.SettlementOrderDataTemp;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementBusinessDeptPriceDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskItemDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import com.bxm.customer.mapper.SettlementOrderDataTempMapper;
import com.bxm.customer.service.ICBusinessTagRelationService;
import com.bxm.customer.service.ISettlementOrderDataTempService;
import com.bxm.system.api.domain.SysDept;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算单关联数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
public class SettlementOrderDataTempServiceImpl extends ServiceImpl<SettlementOrderDataTempMapper, SettlementOrderDataTemp> implements ISettlementOrderDataTempService
{
    @Autowired
    private SettlementOrderDataTempMapper settlementOrderDataTempMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Override
    public List<SettlementBusinessDeptPriceDTO> getBusinessDeptPriceDataByBatchNo(String batchNo, List<SysDept> businessDeptList, String unit, BigDecimal price) {
        if (StringUtils.isEmpty(batchNo)) {
            return Collections.emptyList();
        }
        List<SettlementBusinessDeptPriceDTO> result = Lists.newArrayList();
        List<SettlementOrderDataTemp> dataTempList = list(new LambdaQueryWrapper<SettlementOrderDataTemp>().eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo));
        Map<Long, List<SettlementOrderDataTemp>> dataTempMap = dataTempList.stream().collect(Collectors.groupingBy(SettlementOrderDataTemp::getBusinessDeptId));
        businessDeptList.forEach(businessDept -> {
            List<SettlementOrderDataTemp> businessDeptData = dataTempMap.getOrDefault(businessDept.getDeptId(), Lists.newArrayList());
            BigDecimal totalPrice = price.multiply(new BigDecimal(businessDeptData.size()));
            result.add(SettlementBusinessDeptPriceDTO.builder()
                            .businessDeptId(businessDept.getDeptId())
                            .businessDeptName(businessDept.getDeptName())
                            .dataCount((long) businessDeptData.size())
                            .unit(unit)
                            .totalPrice(totalPrice)
                            .settlementPrice(totalPrice)
                    .build());
        });
        return result;
    }

    @Override
    public List<BusinessPeriodListForTaskItemDTO> getResultByBatchNo(String batchNo) {
        if (StringUtils.isEmpty(batchNo)) {
            return Collections.emptyList();
        }

        List<BusinessPeriodListForTaskItemDTO> result = Lists.newArrayList();

        List<SettlementOrderDataTemp> dataTempList = list(new LambdaQueryWrapper<SettlementOrderDataTemp>().eq(SettlementOrderDataTemp::getSettlementOrderBatchNo, batchNo));
        Map<Integer, List<SettlementOrderDataTemp>> dataTempMap = dataTempList.stream().collect(Collectors.groupingBy(SettlementOrderDataTemp::getPeriod));
        List<Integer> periods = Lists.newArrayList(dataTempMap.keySet()).stream().sorted((o1, o2) -> -o1.compareTo(o2)).collect(Collectors.toList());

        for (Integer row : periods) {
            BusinessPeriodListForTaskItemDTO temp = BusinessPeriodListForTaskItemDTO.builder()
                    .dataCount(dataTempMap.get(row).size())
                    .period(row)
                    .periodStr(DateUtils.periodToYeaMonth(row))
                    .build();

            result.add(temp);
        }
        return result;
    }

    @Override
    public IPage<SettlementOrderDataDTO> settlementOrderDataListByBatchNo(SettlementOrderDataSearchVO vo) {
        IPage<SettlementOrderDataDTO> iPage = new Page<>(vo.getPageNum(), vo.getPageSize());
        buildSearchVO(vo);
        List<SettlementOrderDataDTO> data = settlementOrderDataTempMapper.settlementOrderDataListByBatchNo(iPage, vo);
        if (!ObjectUtils.isEmpty(data)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(SettlementOrderDataDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
            List<Long> periodIds = data.stream().filter(d -> d.getBusinessType() == 2).map(SettlementOrderDataDTO::getBusinessId).collect(Collectors.toList());
            Map<Long, List<TagDTO>> periodTagMap = ObjectUtils.isEmpty(periodIds) ? Maps.newHashMap() :
                    businessTagRelationService.getTagsByBusinessTypeForList(periodIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            data.forEach(d -> {
                d.setCustomerServiceTags(tagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
                d.setCustomerServiceTaxTypeStr(TaxType.getByCode(d.getCustomerServiceTaxType()).getDesc());
                d.setCustomerServiceAdvisorInfo((StringUtils.isEmpty(d.getCustomerServiceAdvisorDeptName()) ? "" : d.getCustomerServiceAdvisorDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAdvisorEmployeeName()) ? "" : "（" + d.getCustomerServiceAdvisorEmployeeName() + "）"));
                d.setCustomerServiceAccountingInfo((StringUtils.isEmpty(d.getCustomerServiceAccountingDeptName()) ? "" : d.getCustomerServiceAccountingDeptName()) + (StringUtils.isEmpty(d.getCustomerServiceAccountingEmployeeName()) ? "" : "（" + d.getCustomerServiceAccountingEmployeeName() + "）"));
                d.setCustomerServiceFirstAccountPeriod(StringUtils.isEmpty(d.getCustomerServiceFirstAccountPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getCustomerServiceFirstAccountPeriod())));
                d.setPeriod(StringUtils.isEmpty(d.getPeriod()) ? "" : DateUtils.periodToYeaMonth(Integer.parseInt(d.getPeriod())));
                d.setPeriodServiceTypeStr(CustomerServicePeriodMonthServiceType.getByCode(d.getPeriodServiceType()).getName());
                d.setPeriodTaxTypeStr(TaxType.getByCode(d.getPeriodTaxType()).getDesc());
                d.setPeriodTags(d.getBusinessType() == 2 ? periodTagMap.getOrDefault(d.getBusinessId(), Lists.newArrayList()).stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")) : "");
                d.setPeriodAdvisorInfo(StringUtils.isEmpty(d.getPeriodAdvisorDeptName()) ? "" : d.getPeriodAdvisorDeptName() + (StringUtils.isEmpty(d.getPeriodAdvisorEmployeeName()) ? "" : "（" + d.getPeriodAdvisorEmployeeName() + "）"));
                d.setPeriodAccountingInfo(StringUtils.isEmpty(d.getPeriodAccountingDeptName()) ? "" : d.getPeriodAccountingDeptName() + (StringUtils.isEmpty(d.getPeriodAccountingEmployeeName()) ? "" : "（" + d.getPeriodAccountingEmployeeName() + "）"));
                d.setPeriodAccountStatusStr(AccountingStatus.getByCode(d.getPeriodAccountStatus()).getName());
                d.setPeriodInAccountDeliverResultStr(AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountDeliverResult()).getName());
                d.setPeriodBankPaymentInputResultStr(Objects.isNull(d.getPeriodBankPaymentInputResult()) ? "" : BankPaymentResult.getByCode(d.getPeriodBankPaymentInputResult()).getDesc());
                d.setPeriodInAccountStatusStr(Objects.isNull(d.getPeriodInAccountStatus()) ? "" : AccountingCashierDeliverStatus.getByCode(d.getPeriodInAccountStatus()).getName());
                d.setPeriodInAccountResultStr(Objects.isNull(d.getPeriodInAccountResult()) ? "" : AccountingCashierDeliverResult.getByCode(d.getPeriodInAccountResult()).getName());
                d.setCreateInfo((StringUtils.isEmpty(d.getCreateDeptName()) ? "" : d.getCreateDeptName()) + (StringUtils.isEmpty(d.getCreateBy()) ? "" : "（" + d.getCreateBy() + "）"));
                d.setPeriodSettlementStatusStr(Objects.isNull(d.getPeriodSettlementStatus()) ? "" : BusinessSettlementStatus.getByCode(d.getPeriodSettlementStatus()).getShowName());
                d.setPeriodPrepayStatusStr(Objects.isNull(d.getPeriodPrepayStatus()) ? "" : PeriodPrepayStatus.getByCode(d.getPeriodPrepayStatus()).getDesc());
            });
        }
        iPage.setRecords(data);
        return iPage;
    }

    @Override
    public void deleteSettlementDataTemp(String jobParam) {
        String startTime;
        String endTime;
        if (!StringUtils.isEmpty(jobParam)) {
            startTime = jobParam + " 00:00:00";
            endTime = jobParam + " 23:59:59";
        } else {
            LocalDate now = LocalDate.now();
            // 删除前天的
            startTime = "";
            endTime = now.minusDays(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59";
        }
        settlementOrderDataTempMapper.delete(new LambdaQueryWrapper<SettlementOrderDataTemp>()
                .le(SettlementOrderDataTemp::getCreateTime, endTime)
                .ge(!StringUtils.isEmpty(startTime), SettlementOrderDataTemp::getCreateTime, startTime));
    }

    private void buildSearchVO(SettlementOrderDataSearchVO vo) {
        if (!StringUtils.isEmpty(vo.getCustomerServiceTagName())) {
            if (!vo.getCustomerServiceTagName().contains("&")) {
                vo.setCustomerServiceTagType(1);
                vo.setCustomerServiceTagNames(vo.getCustomerServiceTagName());
                vo.setCustomerTagCount(1);
            } else {
                vo.setCustomerServiceTagType(2);
                vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
                vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("&").length);
//                if (vo.getCustomerServiceTagNames().contains("&")) {
//                    vo.setCustomerServiceTagType(2);
//                    vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("&").length);
//                } else {
//                    vo.setCustomerServiceTagType(3);
//                    vo.setCustomerServiceTagNames(Arrays.stream(vo.getCustomerServiceTagName().split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setCustomerTagCount(vo.getCustomerServiceTagName().split("/").length);
//                }
            }
        }
        if (!StringUtils.isEmpty(vo.getPeriodStart())) {
            vo.setPeriodStart(DateUtils.yearMonthToPeriod(vo.getPeriodStart()) + "");
        }
        if (!StringUtils.isEmpty(vo.getPeriodEnd())) {
            vo.setPeriodEnd(DateUtils.yearMonthToPeriod(vo.getPeriodEnd()) + "");
        }
        if (!StringUtils.isEmpty(vo.getCustomerServiceFirstPeriodStart())) {
            vo.setCustomerServiceFirstPeriodStart(DateUtils.yearMonthToPeriod(vo.getCustomerServiceFirstPeriodStart()) + "");
        }
        if (!StringUtils.isEmpty(vo.getCustomerServiceFirstPeriodEnd())) {
            vo.setCustomerServiceFirstPeriodEnd(DateUtils.yearMonthToPeriod(vo.getCustomerServiceFirstPeriodEnd()) + "");
        }
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            if (!vo.getPeriodTagName().contains("&")) {
                vo.setPeriodTagType(1);
                vo.setPeriodTagNames(vo.getPeriodTagName());
                vo.setPeriodTagCount(1);
            } else {
                vo.setPeriodTagType(2);
                vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
                vo.setPeriodTagCount(vo.getPeriodTagName().split("&").length);
//                if (vo.getPeriodTagNames().contains("&")) {
//                    vo.setPeriodTagType(2);
//                    vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("&")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setPeriodTagCount(vo.getPeriodTagName().split("&").length);
//                } else {
//                    vo.setPeriodTagType(3);
//                    vo.setPeriodTagNames(Arrays.stream(vo.getPeriodTagName().split("/")).map(row -> "'" + row + "'").collect(Collectors.joining(",")));
//                    vo.setPeriodTagCount(vo.getPeriodTagName().split("/").length);
//                }
            }
        }
    }
}
