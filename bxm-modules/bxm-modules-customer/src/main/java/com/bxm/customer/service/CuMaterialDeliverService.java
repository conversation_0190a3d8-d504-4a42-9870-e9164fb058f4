package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import com.bxm.customer.mapper.CuCustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.MaterialDeliverMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@Service
@Slf4j
public class CuMaterialDeliverService  extends ServiceImpl<MaterialDeliverMapper, MaterialDeliver> {

    @Autowired
    private CuCustomerServiceBankAccountMapper cuCustomerServiceBankAccountMapper;


    private final CuMaterialDeliverFileInventoryService cuMaterialDeliverFileInventoryService;

    public CuMaterialDeliverService(CuMaterialDeliverFileInventoryService materialDeliverFileInventoryService) {
        this.cuMaterialDeliverFileInventoryService = materialDeliverFileInventoryService;
    }

    @Transactional
    public void insertMaterialDeliverAndFiles(MaterialDeliver materialDeliver, List<MaterialDeliverFileInventory> fileInventoryList) {
        // 插入 MaterialDeliver
        save(materialDeliver);  // 插入 MaterialDeliver 对象

        // 获取插入的 materialDeliverId
        Long materialDeliverId = materialDeliver.getId(); // 注意：要确保 id 自动生成

        // 设置 MaterialDeliverFileInventory 的 materialDeliverId
        for (MaterialDeliverFileInventory fileInventory : fileInventoryList) {
            fileInventory.setMaterialDeliverId(materialDeliverId);
        }

        // 插入 List<MaterialDeliverFileInventory>
        cuMaterialDeliverFileInventoryService.saveBatch(fileInventoryList);
    }



    public CustomerServiceBankAccount findByBankAccountNumber(String bankAccountNumber) {
        return cuCustomerServiceBankAccountMapper.findByBankAccountNumber(bankAccountNumber);
    }


    // 检查日期格式是否符合 yyyyMMdd
    public static boolean isValidDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setLenient(false); // 不允许宽松解析
        try {
            sdf.parse(dateStr); // 如果解析成功，则说明格式正确
            return true;
        } catch (ParseException e) {
            return false; // 如果抛出异常，则说明格式不正确
        }
    }



    /**
     * 检查单元格内容是否有效（非null，非空字符串，且非“-”）
     * @param cell 要检查的单元格
     * @return 是否有效
     */
    public static boolean isValidCell(Cell cell) {
        if (cell == null) {
            return false; // 单元格为空
        }

        // 获取单元格的类型
        switch (cell.getCellType()) {
            case STRING:
                // 对字符串类型单元格，去掉空格并检查
                String stringValue = cell.getStringCellValue().trim();
                return !stringValue.isEmpty() && !stringValue.equals("-"); // 非空且不等于 "-"
            case NUMERIC:
                // 对数值类型单元格，转换为字符串并检查
                String numericValue = String.valueOf(cell.getNumericCellValue()).trim();
                return !numericValue.isEmpty() && !numericValue.equals("-"); // 非空且不等于 "-"
            default:
                // 对其他类型不做处理，直接返回 true
                return true;
        }
    }

    /**
     * 验证一行数据的完整性
     * @param row Excel 表格的某一行
     * @return 检查结果信息
     */
    private static String validateRowData(Row row) {
        boolean isValid = true; // 默认认为数据是完整的
        String resultMessage = "数据完整"; // 初始标记为数据完整

        // 检查文件名（第一列）
        Cell fileNameCell = row.getCell(0);
        if (fileNameCell == null || fileNameCell.getStringCellValue().trim().equals("-")) {
            isValid = false;
            resultMessage = "数据不完整";
        }

        // 检查归属客户（第二列）
        Cell customerCell = row.getCell(1);
        if (customerCell == null || customerCell.getStringCellValue().trim().equals("-")) {
            isValid = false;
            resultMessage = "数据不完整";
        }

        // 检查银行账号（第三列）
        Cell bankAccountCell = row.getCell(2);
        if (bankAccountCell == null || bankAccountCell.getStringCellValue().trim().equals("-")) {
            isValid = false;
            resultMessage = "数据不完整";
        }

       /* // 检查开始时间（第四列）是否为空，并且是否符合格式 yyyyMMdd
        Cell startDateCell = row.getCell(3);
        if (startDateCell == null || startDateCell.getStringCellValue().trim().equals("-")) {
            isValid = false;
            resultMessage = "数据不完整";
        } else {
            String startDate = startDateCell.getStringCellValue().trim();
            if (!isValidDate(startDate)) {
                isValid = false;
                resultMessage = "开始时间格式错误";
            }
        }*/

        return resultMessage; // 返回检查结果
    }


}
