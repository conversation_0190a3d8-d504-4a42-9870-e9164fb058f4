package com.bxm.customer.domain.dto.businessTask;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/27 10:19
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessTaskForManageDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

//    @Excel(name = "服务甲方公司")
    @ApiModelProperty(value = "服务甲方公司")
    private String customerServiceBusinessDeptName;

    @Excel(name = "是否亿企账套")
    @ApiModelProperty(value = "是否亿企账套")
    private String isYiqiZhangtao;

    @ApiModelProperty(value = "任务对应的业务的业务ID，账期任务就是账期ID")
    private Long bizId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @Excel(name = "账期")
    @ApiModelProperty(value = "账期 文本")
    private String periodStr;

    @Excel(name = "银行名称")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    @Excel(name = "银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("银行账户")
//    @Excel(name = "银行账户")
    private String bankInfo;

    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般")
    private Integer taxType;

    private LocalDateTime createTime;

    @Excel(name = "业务公司")
    @ApiModelProperty(value = "业务公司")
    private String periodBusinessDeptName;

    @ApiModelProperty(value = "纳税人性质-文本")
    @Excel(name = "纳税人性质")
    private String taxTypeStr;

    @ApiModelProperty(value = "事项，1-银行流水")
    private Integer itemType;

    @Excel(name = "事项")
    @ApiModelProperty(value = "事项，1-银行流水")
    private String itemTypeStr;

    @ApiModelProperty(value = "执行人ID")
    private Long executeUserId;

    @Excel(name = "执行人名称")
    @ApiModelProperty(value = "执行人名称")
    private String executeUserName;

    @Excel(name = "账期会计")
    @ApiModelProperty("账期会计")
    private String accountingEmployeeNameFull;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "截止日期")
    private LocalDate deadline;

    @ApiModelProperty(value = "任务状态：1-待完成、2-待审核、3-已完结、4-已关闭、5-异常")
    private Integer status;

    @ApiModelProperty("纸质")
    private Integer mediumPaper;

    @Excel(name = "纸质")
    @ApiModelProperty("纸质-文案")
    private String mediumPaperStr;

    @ApiModelProperty("电子")
    private Integer mediumElectric;

    @Excel(name = "电子")
    @ApiModelProperty("电子-文案")
    private String mediumElectricStr;

    @ApiModelProperty("银企")
    private Integer mediumBank;

    @Excel(name = "银企")
    @ApiModelProperty("银企-文案")
    private String mediumBankStr;

    @ApiModelProperty("事项备忘")
    @Excel(name = "事项备忘")
    private String mattersNotesStr;

    @Excel(name = "任务状态")
    @ApiModelProperty(value = "任务状态 文案")
    private String statusStr;

    @Excel(name = "是否有流水")
    @ApiModelProperty("是否有流水")
    private String hasBankPaymentStr;

    @ApiModelProperty("材料数量")
    @Excel(name = "材料")
    private Long bankMaterialFileCount;

    @ApiModelProperty(value = "完成结果：1-正常完成、2-已开户无流水、3-未开户、4-银行部分缺、5-无需交付、6-无法完成")
    private Integer finishResult;

    @Excel(name = "完成结果")
    @ApiModelProperty(value = "完成结果 文案")
    private String finishResultStr;

    private LocalDateTime firstCompleteTime;

    @Excel(name = "首次完成时间")
    @ApiModelProperty(value = "首次完成时间 文案")
    private String firstCompleteTimeStr;

    @Excel(name = "备注")
    private String remark;

    @ApiModelProperty(value = "创建备注")
    @Excel(name = "新建备注")
    private String createRemark;

    @ApiModelProperty(value = "完成备注")
    @Excel(name = "完成备注")
    private String finishRemark;

    @ApiModelProperty(value = "处理异常备注")
    @Excel(name = "异常备注")
    private String dealExceptionRemark;

    @ApiModelProperty(value = "审核备注")
    @Excel(name = "审核备注")
    private String checkRemark;

    @ApiModelProperty(value = "派单时间")
    @Excel(name = "派单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime executeTime;

    @ApiModelProperty(value = "最后操作人")
    private Long lastOperateUserId;

    @Excel(name = "最后操作人姓名")
    @ApiModelProperty(value = "最后操作人姓名")
    private String lastOperateUserName;

    @ApiModelProperty(value = "最后操作类型")
    private Integer lastOperateType;

    @Excel(name = "最后操作类型")
    @ApiModelProperty(value = "最后操作类型 文案")
    private String lastOperateTypeStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后操作时间")
    private LocalDateTime lastOperateTime;

    @ApiModelProperty(value = "账期会计部门ID")
    private Long periodAccountingDeptId;

    @ApiModelProperty(value = "是否可审核")
    private Boolean canCheck;

    @ApiModelProperty(value = "事项备忘", hidden = true)
    private String mattersNotes;

    @ApiModelProperty(value = "是否有流水，0-否，1-是", hidden = true)
    private Integer hasBankPayment;

    @Excel(name = "创建人")
    private String createBy;

    @Excel(name = "创建时间")
    private String createTimeStr;
}
