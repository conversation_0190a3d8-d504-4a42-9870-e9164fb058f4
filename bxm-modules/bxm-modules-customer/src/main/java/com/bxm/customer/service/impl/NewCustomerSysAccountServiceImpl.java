package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.NewCustomerSysAccount;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferSysAccountDTO;
import com.bxm.customer.mapper.NewCustomerSysAccountMapper;
import com.bxm.customer.service.INewCustomerSysAccountService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新户流转系统账号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerSysAccountServiceImpl extends ServiceImpl<NewCustomerSysAccountMapper, NewCustomerSysAccount> implements INewCustomerSysAccountService
{
    @Autowired
    private NewCustomerSysAccountMapper newCustomerSysAccountMapper;

    /**
     * 查询新户流转系统账号
     * 
     * @param id 新户流转系统账号主键
     * @return 新户流转系统账号
     */
    @Override
    public NewCustomerSysAccount selectNewCustomerSysAccountById(Long id)
    {
        return newCustomerSysAccountMapper.selectNewCustomerSysAccountById(id);
    }

    /**
     * 查询新户流转系统账号列表
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 新户流转系统账号
     */
    @Override
    public List<NewCustomerSysAccount> selectNewCustomerSysAccountList(NewCustomerSysAccount newCustomerSysAccount)
    {
        return newCustomerSysAccountMapper.selectNewCustomerSysAccountList(newCustomerSysAccount);
    }

    /**
     * 新增新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    @Override
    public int insertNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount)
    {
        newCustomerSysAccount.setCreateTime(DateUtils.getNowDate());
        return newCustomerSysAccountMapper.insertNewCustomerSysAccount(newCustomerSysAccount);
    }

    /**
     * 修改新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    @Override
    public int updateNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount)
    {
        newCustomerSysAccount.setUpdateTime(DateUtils.getNowDate());
        return newCustomerSysAccountMapper.updateNewCustomerSysAccount(newCustomerSysAccount);
    }

    /**
     * 批量删除新户流转系统账号
     * 
     * @param ids 需要删除的新户流转系统账号主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerSysAccountByIds(Long[] ids)
    {
        return newCustomerSysAccountMapper.deleteNewCustomerSysAccountByIds(ids);
    }

    /**
     * 删除新户流转系统账号信息
     * 
     * @param id 新户流转系统账号主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerSysAccountById(Long id)
    {
        return newCustomerSysAccountMapper.deleteNewCustomerSysAccountById(id);
    }

    @Override
    public List<NewCustomerSysAccount> selectByCustomerServiceId(Long newCustomerTransferId) {
        if (Objects.isNull(newCustomerTransferId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerSysAccount>()
                .eq(NewCustomerSysAccount::getCustomerId, newCustomerTransferId)
                .eq(NewCustomerSysAccount::getIsDel, false));
    }

    @Override
    public Map<Long, List<NewCustomerSysAccount>> selectMapByCustomerServiceIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerSysAccount>()
                .in(NewCustomerSysAccount::getCustomerId, newCustomerTransferIds)
                .eq(NewCustomerSysAccount::getIsDel, false))
                .stream()
                .collect(Collectors.groupingBy(NewCustomerSysAccount::getCustomerId));
    }

    @Override
    @Transactional
    public void removeAndCreateSysAccount(Long newCustomerTransferId, List<NewCustomerTransferSysAccountDTO> sysAccountList) {
        update(new LambdaUpdateWrapper<NewCustomerSysAccount>()
                .eq(NewCustomerSysAccount::getCustomerId, newCustomerTransferId)
                .eq(NewCustomerSysAccount::getIsDel, false)
                .set(NewCustomerSysAccount::getIsDel, true));
        if (!ObjectUtils.isEmpty(sysAccountList)) {
            saveBatch(sysAccountList.stream().map(sysAccount -> {
                NewCustomerSysAccount newCustomerSysAccount = new NewCustomerSysAccount();
                BeanUtils.copyProperties(sysAccount, newCustomerSysAccount);
                newCustomerSysAccount.setIsDel(false);
                newCustomerSysAccount.setCustomerId(newCustomerTransferId);
                newCustomerSysAccount.setSysType(sysAccount.getSysAccountType());
                newCustomerSysAccount.setSysTypeName(sysAccount.getSysAccountTypeName());
                newCustomerSysAccount.setId(null);
                newCustomerSysAccount.setCreateTime(null);
                newCustomerSysAccount.setUpdateTime(null);
                return newCustomerSysAccount;
            }).collect(Collectors.toList()));
        }
    }
}
