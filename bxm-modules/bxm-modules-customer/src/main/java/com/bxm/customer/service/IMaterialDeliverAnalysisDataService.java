package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.MaterialDeliverAnalysisData;

/**
 * 材料交接单数据解析结果Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IMaterialDeliverAnalysisDataService extends IService<MaterialDeliverAnalysisData>
{
    /**
     * 查询材料交接单数据解析结果
     * 
     * @param id 材料交接单数据解析结果主键
     * @return 材料交接单数据解析结果
     */
    public MaterialDeliverAnalysisData selectMaterialDeliverAnalysisDataById(Long id);

    /**
     * 查询材料交接单数据解析结果列表
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 材料交接单数据解析结果集合
     */
    public List<MaterialDeliverAnalysisData> selectMaterialDeliverAnalysisDataList(MaterialDeliverAnalysisData materialDeliverAnalysisData);

    /**
     * 新增材料交接单数据解析结果
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 结果
     */
    public int insertMaterialDeliverAnalysisData(MaterialDeliverAnalysisData materialDeliverAnalysisData);

    /**
     * 修改材料交接单数据解析结果
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 结果
     */
    public int updateMaterialDeliverAnalysisData(MaterialDeliverAnalysisData materialDeliverAnalysisData);

    /**
     * 批量删除材料交接单数据解析结果
     * 
     * @param ids 需要删除的材料交接单数据解析结果主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverAnalysisDataByIds(Long[] ids);

    /**
     * 删除材料交接单数据解析结果信息
     * 
     * @param id 材料交接单数据解析结果主键
     * @return 结果
     */
    public int deleteMaterialDeliverAnalysisDataById(Long id);
}
