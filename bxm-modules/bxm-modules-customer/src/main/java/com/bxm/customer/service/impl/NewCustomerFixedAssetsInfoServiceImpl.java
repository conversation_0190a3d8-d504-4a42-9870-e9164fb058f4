package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferFixedAssetsInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.NewCustomerFixedAssetsInfoMapper;
import com.bxm.customer.domain.NewCustomerFixedAssetsInfo;
import com.bxm.customer.service.INewCustomerFixedAssetsInfoService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 新户流转固定资产信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerFixedAssetsInfoServiceImpl extends ServiceImpl<NewCustomerFixedAssetsInfoMapper, NewCustomerFixedAssetsInfo> implements INewCustomerFixedAssetsInfoService
{
    @Autowired
    private NewCustomerFixedAssetsInfoMapper newCustomerFixedAssetsInfoMapper;

    /**
     * 查询新户流转固定资产信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 新户流转固定资产信息
     */
    @Override
    public NewCustomerFixedAssetsInfo selectNewCustomerFixedAssetsInfoById(Long id)
    {
        return newCustomerFixedAssetsInfoMapper.selectNewCustomerFixedAssetsInfoById(id);
    }

    /**
     * 查询新户流转固定资产信息列表
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 新户流转固定资产信息
     */
    @Override
    public List<NewCustomerFixedAssetsInfo> selectNewCustomerFixedAssetsInfoList(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo)
    {
        return newCustomerFixedAssetsInfoMapper.selectNewCustomerFixedAssetsInfoList(newCustomerFixedAssetsInfo);
    }

    /**
     * 新增新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo)
    {
        return newCustomerFixedAssetsInfoMapper.insertNewCustomerFixedAssetsInfo(newCustomerFixedAssetsInfo);
    }

    /**
     * 修改新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo)
    {
        return newCustomerFixedAssetsInfoMapper.updateNewCustomerFixedAssetsInfo(newCustomerFixedAssetsInfo);
    }

    /**
     * 批量删除新户流转固定资产信息
     * 
     * @param ids 需要删除的新户流转固定资产信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerFixedAssetsInfoByIds(Long[] ids)
    {
        return newCustomerFixedAssetsInfoMapper.deleteNewCustomerFixedAssetsInfoByIds(ids);
    }

    /**
     * 删除新户流转固定资产信息信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerFixedAssetsInfoById(Long id)
    {
        return newCustomerFixedAssetsInfoMapper.deleteNewCustomerFixedAssetsInfoById(id);
    }

    @Override
    public List<NewCustomerFixedAssetsInfo> selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerFixedAssetsInfo>()
                .eq(NewCustomerFixedAssetsInfo::getCustomerId, customerId));
    }

    @Override
    public Integer countByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return 0;
        }
        return count(new LambdaQueryWrapper<NewCustomerFixedAssetsInfo>()
                .eq(NewCustomerFixedAssetsInfo::getCustomerId, customerId));
    }

    @Override
    public void deleteByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return;
        }
        remove(new LambdaQueryWrapper<NewCustomerFixedAssetsInfo>()
                .eq(NewCustomerFixedAssetsInfo::getCustomerId, customerId));
    }

    @Override
    @Transactional
    public void removeAndSaveNew(Long customerId, List<NewCustomerTransferFixedAssetsInfoDTO> fixedAssetsList) {
        remove(new LambdaQueryWrapper<NewCustomerFixedAssetsInfo>().eq(NewCustomerFixedAssetsInfo::getCustomerId, customerId));
        if (!ObjectUtils.isEmpty(fixedAssetsList)) {
            saveBatch(fixedAssetsList.stream().map(item -> new NewCustomerFixedAssetsInfo().setCustomerId(customerId)
                    .setAssetName(item.getAssetName()).setOccurrenceYear(item.getOccurrenceYear())).collect(Collectors.toList()));
        }
    }

    @Override
    public Map<Long, List<NewCustomerFixedAssetsInfo>> selectMapByCustomerIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerFixedAssetsInfo>()
                .in(NewCustomerFixedAssetsInfo::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.groupingBy(NewCustomerFixedAssetsInfo::getCustomerId));
    }
}
