package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 待提交状态变更策略
 *
 * 处理从"已保存待提交"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. SAVED_PENDING_SUBMIT -> SUBMITTED_PENDING_DELIVERY (正常提交)
 * 2. SAVED_PENDING_SUBMIT -> DRAFT (退回草稿)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class PendingSubmitStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
               targetStatus == ValueAddedDeliveryOrderStatus.DRAFT;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            // 验证提交到已提交待交付状态
            validateSubmitToDelivery(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DRAFT) {
            // 验证退回到草稿状态
            validateReturnToDraft(order, request);
        } else {
            throwUnsupportedTransition("待提交", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }

    /**
     * 验证提交到已提交待交付状态
     */
    private void validateSubmitToDelivery(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateNotEmpty(order.getCustomerName(), "客户企业名称");
        validateCreditCode(order);

        // 验证增值事项相关信息
        if (order.getValueAddedItemTypeId() == null) {
            throw new IllegalArgumentException("增值事项类型不能为空");
        }

        if (order.getDdl() == null) {
            throw new IllegalArgumentException("交付截止日期不能为空");
        }

        // 执行账期校验（补账/改账类型的特殊校验）
        try {
            valueAddedAccountPeriodService.validateAccountPeriodForSubmit(order);
            log.info("Account period validation passed for order: {}", request.getDeliveryOrderNo());
        } catch (IllegalArgumentException e) {
            log.error("Account period validation failed for order: {}, error: {}", request.getDeliveryOrderNo(), e.getMessage());
            throw e; // 重新抛出校验异常
        } catch (Exception e) {
            log.error("Unexpected error during account period validation for order: {}", request.getDeliveryOrderNo(), e);
            throw new RuntimeException("账期校验过程中发生系统异常: " + e.getMessage(), e);
        }

        // 生成增值账期记录
        try {
            valueAddedAccountPeriodService.generateValueAddedPeriodRecords(order);
            log.info("ValueAddedPeriodMonth records generated successfully for order: {}", request.getDeliveryOrderNo());
        } catch (Exception e) {
            log.error("Failed to generate ValueAddedPeriodMonth records for order: {}", request.getDeliveryOrderNo(), e);
            throw new RuntimeException("生成增值账期记录失败: " + e.getMessage(), e);
        }

        logValidationPassed("Submit", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回到草稿状态
     */
    private void validateReturnToDraft(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateReturnOperation(request, "草稿状态");
        logValidationPassed("Return to draft", request.getDeliveryOrderNo());
    }
}
