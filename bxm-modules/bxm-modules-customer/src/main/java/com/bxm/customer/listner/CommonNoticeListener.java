package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.customer.domain.vo.CommonDeductionVO;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerCommonNotice" + "${spring.profiles.active}", topic = "openapiCommonNotice_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "common")
public class CommonNoticeListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到公共通知消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            CommonNoticeVO commonNoticeVO = JSONObject.parseObject(new String(message.getBody()), CommonNoticeVO.class);
            xqyService.commonNotice(commonNoticeVO);
        } catch (Exception e) {
            log.error("处理公共通知消息异常:{}", e.getMessage());
        }
    }
}
