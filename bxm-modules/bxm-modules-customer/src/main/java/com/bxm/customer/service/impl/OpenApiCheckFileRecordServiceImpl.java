package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.BusinessTask;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.OpenApiCheckFileRecord;
import com.bxm.customer.mapper.OpenApiCheckFileRecordMapper;
import com.bxm.customer.service.IOpenApiCheckFileRecordService;
import com.bxm.thirdpart.api.domain.CheckFilesVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检验文件定时轮询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-23
 */
@Service
public class OpenApiCheckFileRecordServiceImpl extends ServiceImpl<OpenApiCheckFileRecordMapper, OpenApiCheckFileRecord> implements IOpenApiCheckFileRecordService
{
    @Autowired
    private OpenApiCheckFileRecordMapper openApiCheckFileRecordMapper;

    /**
     * 查询检验文件定时轮询
     * 
     * @param id 检验文件定时轮询主键
     * @return 检验文件定时轮询
     */
    @Override
    public OpenApiCheckFileRecord selectOpenApiCheckFileRecordById(Long id)
    {
        return openApiCheckFileRecordMapper.selectOpenApiCheckFileRecordById(id);
    }

    /**
     * 查询检验文件定时轮询列表
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 检验文件定时轮询
     */
    @Override
    public List<OpenApiCheckFileRecord> selectOpenApiCheckFileRecordList(OpenApiCheckFileRecord openApiCheckFileRecord)
    {
        return openApiCheckFileRecordMapper.selectOpenApiCheckFileRecordList(openApiCheckFileRecord);
    }

    /**
     * 新增检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    @Override
    public int insertOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord)
    {
        openApiCheckFileRecord.setCreateTime(DateUtils.getNowDate());
        return openApiCheckFileRecordMapper.insertOpenApiCheckFileRecord(openApiCheckFileRecord);
    }

    /**
     * 修改检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    @Override
    public int updateOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord)
    {
        openApiCheckFileRecord.setUpdateTime(DateUtils.getNowDate());
        return openApiCheckFileRecordMapper.updateOpenApiCheckFileRecord(openApiCheckFileRecord);
    }

    /**
     * 批量删除检验文件定时轮询
     * 
     * @param ids 需要删除的检验文件定时轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiCheckFileRecordByIds(Long[] ids)
    {
        return openApiCheckFileRecordMapper.deleteOpenApiCheckFileRecordByIds(ids);
    }

    /**
     * 删除检验文件定时轮询信息
     * 
     * @param id 检验文件定时轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiCheckFileRecordById(Long id)
    {
        return openApiCheckFileRecordMapper.deleteOpenApiCheckFileRecordById(id);
    }

    @Override
    @Transactional
    public void createCheckFileTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, BusinessTask businessTask, CheckFilesVO checkFileVO, Long afterTime) {
        if (!Objects.isNull(customerServiceCashierAccounting)) {
            List<OpenApiCheckFileRecord> records = list(new LambdaQueryWrapper<OpenApiCheckFileRecord>()
                    .eq(OpenApiCheckFileRecord::getCustomerServiceCashierAccountingId, customerServiceCashierAccounting.getId())
                    .eq(OpenApiCheckFileRecord::getStatus, 1));
            if (!ObjectUtils.isEmpty(records)) {
                updateBatchById(records.stream().map(row -> new OpenApiCheckFileRecord().setId(row.getId()).setStatus(5)).collect(Collectors.toList()));
            }
        }
        LocalDateTime searchTime = LocalDateTime.now().plusSeconds(afterTime);
        checkFileVO.setFirstRunTime(searchTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        save(new OpenApiCheckFileRecord().setBusinessTaskId(Objects.isNull(businessTask) ? null : businessTask.getId())
                .setCustomerServiceCashierAccountingId(Objects.isNull(customerServiceCashierAccounting) ? null : customerServiceCashierAccounting.getId())
                .setStatus(1)
                .setCheckFileParam(JSON.toJSONString(checkFileVO))
                .setSearchTime(searchTime));
    }

    @Override
    @Transactional
    public void createCheckFileTask(Long customerServiceCashierAccountingId, Long businessTaskId, CheckFilesVO checkFileVO, Long afterTime) {
        if (!Objects.isNull(customerServiceCashierAccountingId)) {
            List<OpenApiCheckFileRecord> records = list(new LambdaQueryWrapper<OpenApiCheckFileRecord>()
                    .eq(OpenApiCheckFileRecord::getCustomerServiceCashierAccountingId, customerServiceCashierAccountingId)
                    .eq(OpenApiCheckFileRecord::getStatus, 1));
            if (!ObjectUtils.isEmpty(records)) {
                updateBatchById(records.stream().map(row -> new OpenApiCheckFileRecord().setId(row.getId()).setStatus(5)).collect(Collectors.toList()));
            }
        }
        LocalDateTime searchTime = LocalDateTime.now().plusSeconds(afterTime);
        checkFileVO.setFirstRunTime(searchTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        save(new OpenApiCheckFileRecord().setBusinessTaskId(businessTaskId)
                .setCustomerServiceCashierAccountingId(customerServiceCashierAccountingId)
                .setStatus(1)
                .setCheckFileParam(JSON.toJSONString(checkFileVO))
                .setSearchTime(searchTime));
    }

    @Override
    public void closeCheckFileTask(Long searchTaskId) {
        if (Objects.isNull(searchTaskId)) {
            return;
        }
        updateById(new OpenApiCheckFileRecord().setId(searchTaskId).setStatus(2));
    }

    @Override
    public void overTimeClose(Long searchTaskId) {
        if (Objects.isNull(searchTaskId)) {
            return;
        }
        updateById(new OpenApiCheckFileRecord().setId(searchTaskId).setStatus(3));
    }

    @Override
    public void continueCheckFileTask(Long taskId) {
        if (Objects.isNull(taskId)) {
            return;
        }
        updateById(new OpenApiCheckFileRecord().setId(taskId).setStatus(1));
    }
}
