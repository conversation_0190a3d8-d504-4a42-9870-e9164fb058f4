package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverPeriodInventoryDTO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverPeriodInventorySearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.MaterialDeliverPeriodInventory;
import org.apache.ibatis.annotations.Param;

/**
 * 材料交接单账期清单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Mapper
public interface MaterialDeliverPeriodInventoryMapper extends BaseMapper<MaterialDeliverPeriodInventory>
{
    /**
     * 查询材料交接单账期清单
     * 
     * @param id 材料交接单账期清单主键
     * @return 材料交接单账期清单
     */
    public MaterialDeliverPeriodInventory selectMaterialDeliverPeriodInventoryById(Long id);

    /**
     * 查询材料交接单账期清单列表
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 材料交接单账期清单集合
     */
    public List<MaterialDeliverPeriodInventory> selectMaterialDeliverPeriodInventoryList(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 新增材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    public int insertMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 修改材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    public int updateMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 删除材料交接单账期清单
     * 
     * @param id 材料交接单账期清单主键
     * @return 结果
     */
    public int deleteMaterialDeliverPeriodInventoryById(Long id);

    /**
     * 批量删除材料交接单账期清单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverPeriodInventoryByIds(Long[] ids);


    @Insert("<script>" +
            "INSERT INTO c_material_deliver_period_inventory (material_deliver_id, customer_name, bank_name, bank_account_number, period, customer_service_id) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.materialDeliverId}, #{item.customerName}, #{item.bankName}, #{item.bankAccountNumber}, #{item.period}, #{item.customerServiceId})" +
            "</foreach>" +
            "</script>")
    void insertBatch(@Param("list") List<MaterialDeliverPeriodInventory> periodInventories);

    List<MaterialDeliverPeriodInventoryDTO> periodInventoryPageList(IPage<MaterialDeliverPeriodInventoryDTO> result,
                                                                    @Param("vo") MaterialDeliverPeriodInventorySearchVO vo,
                                                                    @Param("userDept") UserDeptDTO userDept,
                                                                    @Param("customerServiceIds") List<Long> customerServiceIds,
                                                                    @Param("errorMsgList") List<String> errorMsgList);
}
