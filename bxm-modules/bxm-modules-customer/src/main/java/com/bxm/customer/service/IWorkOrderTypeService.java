package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.WorkOrderType;
import com.bxm.customer.domain.dto.workOrder.WorkOrderTypeDTO;

/**
 * 工单类型配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IWorkOrderTypeService extends IService<WorkOrderType>
{
    /**
     * 查询工单类型配置
     * 
     * @param id 工单类型配置主键
     * @return 工单类型配置
     */
    public WorkOrderType selectWorkOrderTypeById(Long id);

    /**
     * 查询工单类型配置列表
     * 
     * @param workOrderType 工单类型配置
     * @return 工单类型配置集合
     */
    public List<WorkOrderType> selectWorkOrderTypeList(WorkOrderType workOrderType);

    /**
     * 新增工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    public int insertWorkOrderType(WorkOrderType workOrderType);

    /**
     * 修改工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    public int updateWorkOrderType(WorkOrderType workOrderType);

    /**
     * 批量删除工单类型配置
     * 
     * @param ids 需要删除的工单类型配置主键集合
     * @return 结果
     */
    public int deleteWorkOrderTypeByIds(Long[] ids);

    /**
     * 删除工单类型配置信息
     * 
     * @param id 工单类型配置主键
     * @return 结果
     */
    public int deleteWorkOrderTypeById(Long id);

    List<WorkOrderTypeDTO> workOrderTypeListCanSee(Long deptId);

    WorkOrderType selectByWorkOrderType(Integer workOrderType);

    List<WorkOrderTypeDTO> workOrderTypeList();
}
