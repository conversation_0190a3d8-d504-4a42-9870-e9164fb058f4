package com.bxm.customer.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 税号验证注解
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = TaxNumberValidator.class)
public @interface TaxNumber {

    String message() default "税号格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 是否允许为空
     */
    boolean allowEmpty() default true;
}
