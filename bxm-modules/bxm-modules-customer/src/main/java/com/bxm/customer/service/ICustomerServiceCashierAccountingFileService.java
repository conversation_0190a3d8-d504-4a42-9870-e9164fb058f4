package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceCashierAccountingFile;

/**
 * 客户账务附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
public interface ICustomerServiceCashierAccountingFileService extends IService<CustomerServiceCashierAccountingFile>
{
    /**
     * 查询客户账务附件
     * 
     * @param id 客户账务附件主键
     * @return 客户账务附件
     */
    public CustomerServiceCashierAccountingFile selectCustomerServiceCashierAccountingFileById(Long id);

    /**
     * 查询客户账务附件列表
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 客户账务附件集合
     */
    public List<CustomerServiceCashierAccountingFile> selectCustomerServiceCashierAccountingFileList(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 新增客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    public int insertCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 修改客户账务附件
     * 
     * @param customerServiceCashierAccountingFile 客户账务附件
     * @return 结果
     */
    public int updateCustomerServiceCashierAccountingFile(CustomerServiceCashierAccountingFile customerServiceCashierAccountingFile);

    /**
     * 批量删除客户账务附件
     * 
     * @param ids 需要删除的客户账务附件主键集合
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingFileByIds(Long[] ids);

    /**
     * 删除客户账务附件信息
     * 
     * @param id 客户账务附件主键
     * @return 结果
     */
    public int deleteCustomerServiceCashierAccountingFileById(Long id);

    List<CustomerServiceCashierAccountingFile> selectBatchByCashierAccountingIds(List<Long> accountingCashierIds);

    List<CustomerServiceCashierAccountingFile> selectByAccountingCashierIdAndFileType(Long accountingCashierId, Integer fileType);

    List<CustomerServiceCashierAccountingFile> selectByAccountingCashierIdsAndFileType(List<Long> accountingCashierIds, Integer fileType);

    void saveAccountingCashierFile(Long accountingCashierId, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType, Integer subFileType);

    void saveAccountingCashierFile(Long accountingCashierId, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType);

    void logicDeleteByCashierAccountingIdAndFileType(Long accountingCashierId, AccountingCashierFileType accountingCashierFileType);

    void logicDeleteByCashierAccountingIdAndFileType(Long accountingCashierId, AccountingCashierFileType accountingCashierFileType, Integer subFileType);

    void logicDeleteByCashierAccountingIdsAndFileType(List<Long> accountingCashierIds, AccountingCashierFileType accountingCashierFileType);

    void logicDeleteByCashierAccountingIdsAndFileType(List<Long> accountingCashierIds, AccountingCashierFileType accountingCashierFileType, Integer subFileType);

    void saveAccountingCashierFileBatch(List<Long> accountingCashierIds, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType);

    void saveAccountingCashierFileBatch(List<Long> accountingCashierIds, List<CommonFileVO> files, AccountingCashierFileType accountingCashierFileType, Integer subFileType);
}
