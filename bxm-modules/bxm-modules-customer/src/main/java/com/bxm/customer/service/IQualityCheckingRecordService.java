package com.bxm.customer.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingRecordVO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingResultCloseVO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;

/**
 * 质检记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IQualityCheckingRecordService extends IService<QualityCheckingRecord>
{
    /**
     * 查询质检记录
     * 
     * @param id 质检记录主键
     * @return 质检记录
     */
    public QualityCheckingRecord selectQualityCheckingRecordById(Long id);

    /**
     * 查询质检记录列表
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 质检记录集合
     */
    public List<QualityCheckingRecord> selectQualityCheckingRecordList(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 新增质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    public int insertQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 修改质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    public int updateQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 批量删除质检记录
     * 
     * @param ids 需要删除的质检记录主键集合
     * @return 结果
     */
    public int deleteQualityCheckingRecordByIds(Long[] ids);

    /**
     * 删除质检记录信息
     * 
     * @param id 质检记录主键
     * @return 结果
     */
    public int deleteQualityCheckingRecordById(Long id);

    IPage<QualityCheckingRecordDTO> qualityCheckingRecordPageList(QualityCheckingRecordVO vo);

    TCommonOperateDTO<QualityCheckingRecord> closeQualityRecord(Long deptId, QualityCheckingResultCloseVO vo);

    boolean existsCheckingRecordByQualityCheckingResultId(Long qualityCheckingResultId);

    Map<Long, Boolean> existsCheckingRecordByQualityCheckingResultIds(List<Long> qualityCheckingResultIds);

    // 创建一个新的质检记录
    QualityCheckingRecord createNewQualityCheckingRecord(QualityCheckingResult checkingResult, OperateUserInfoDTO operateUserInfo, LocalDateTime operTime);

    QualityCheckingRecord createNewQualityCheckingRecord(QualityCheckingResult checkingResult, Long userId, Long deptId, String operName, LocalDateTime operTime, String batchNo);

    Integer getCheckedTimesByQualityCheckingResultId(Long qualityCheckingResultId);

    // 查询正在进行中的质检记录
    List<QualityCheckingRecord> getCheckingRecordList(List<Long> customerServicePeriodMonthIds);
}
