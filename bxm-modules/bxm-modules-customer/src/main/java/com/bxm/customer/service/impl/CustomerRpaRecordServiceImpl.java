package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.RpaRecordStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.CustomerRpaRecordDetail;
import com.bxm.customer.domain.dto.CustomerDeliverRpaDTO;
import com.bxm.customer.domain.vo.RpaRecordConfirmVO;
import com.bxm.customer.service.ICustomerRpaRecordDetailService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerRpaRecordMapper;
import com.bxm.customer.domain.CustomerRpaRecord;
import com.bxm.customer.service.ICustomerRpaRecordService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * rap交付记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
public class CustomerRpaRecordServiceImpl extends ServiceImpl<CustomerRpaRecordMapper, CustomerRpaRecord> implements ICustomerRpaRecordService
{
    @Autowired
    private CustomerRpaRecordMapper customerRpaRecordMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private ICustomerRpaRecordDetailService customerRpaRecordDetailService;

    /**
     * 查询rap交付记录
     * 
     * @param id rap交付记录主键
     * @return rap交付记录
     */
    @Override
    public CustomerRpaRecord selectCustomerRpaRecordById(Long id)
    {
        return customerRpaRecordMapper.selectCustomerRpaRecordById(id);
    }

    /**
     * 查询rap交付记录列表
     * 
     * @param customerRpaRecord rap交付记录
     * @return rap交付记录
     */
    @Override
    public List<CustomerRpaRecord> selectCustomerRpaRecordList(CustomerRpaRecord customerRpaRecord)
    {
        return customerRpaRecordMapper.selectCustomerRpaRecordList(customerRpaRecord);
    }

    /**
     * 新增rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    @Override
    public int insertCustomerRpaRecord(CustomerRpaRecord customerRpaRecord)
    {
        customerRpaRecord.setCreateTime(DateUtils.getNowDate());
        return customerRpaRecordMapper.insertCustomerRpaRecord(customerRpaRecord);
    }

    /**
     * 修改rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    @Override
    public int updateCustomerRpaRecord(CustomerRpaRecord customerRpaRecord)
    {
        customerRpaRecord.setUpdateTime(DateUtils.getNowDate());
        return customerRpaRecordMapper.updateCustomerRpaRecord(customerRpaRecord);
    }

    /**
     * 批量删除rap交付记录
     * 
     * @param ids 需要删除的rap交付记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordByIds(Long[] ids)
    {
        return customerRpaRecordMapper.deleteCustomerRpaRecordByIds(ids);
    }

    /**
     * 删除rap交付记录信息
     * 
     * @param id rap交付记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordById(Long id)
    {
        return customerRpaRecordMapper.deleteCustomerRpaRecordById(id);
    }

    @Override
    public IPage<CustomerDeliverRpaDTO> rpaRecordList(String customerName, Integer rpaType, Long deptId, Integer pageNum, Integer pageSize) {
        IPage<CustomerDeliverRpaDTO> result = new Page<>(pageNum, pageSize);
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException(false);
        if (Objects.isNull(userDeptDTO) || (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds()))) {
            return result;
        }
        LambdaQueryWrapper<CustomerRpaRecord> queryWrapper = new LambdaQueryWrapper<CustomerRpaRecord>()
                .eq(!Objects.isNull(rpaType), CustomerRpaRecord::getRpaType, rpaType)
                .in(!ObjectUtils.isEmpty(userDeptDTO.getDeptIds()), CustomerRpaRecord::getDeptId, userDeptDTO.getDeptIds());
        if (!StringUtils.isEmpty(customerName)) {
            queryWrapper.inSql(CustomerRpaRecord::getId, "select rpa_record_id from c_customer_rpa_record_detail where customer_name like concat('%', '" + customerName + "', '%')");
        }
        queryWrapper.orderByDesc(CustomerRpaRecord::getCreateTime);
        IPage<CustomerRpaRecord> iPage = page(new Page<>(pageNum, pageSize), queryWrapper);
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<Long> deptIds = iPage.getRecords().stream().map(CustomerRpaRecord::getDeptId).distinct().collect(Collectors.toList());
            List<SysDept> deptList = remoteDeptService.getByDeptIds(deptIds).getDataThrowException(false);
            Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(deptList) ? Maps.newHashMap() :
                    deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            List<SysEmployee> employees = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException(false);
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                    employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            List<CustomerRpaRecordDetail> details = customerRpaRecordDetailService.list(new LambdaQueryWrapper<CustomerRpaRecordDetail>().in(CustomerRpaRecordDetail::getRpaRecordId, iPage.getRecords().stream().map(CustomerRpaRecord::getId).collect(Collectors.toList())));
            Map<Long, List<CustomerRpaRecordDetail>> detailMap = ObjectUtils.isEmpty(details) ? Maps.newHashMap() :
                    details.stream().collect(Collectors.groupingBy(CustomerRpaRecordDetail::getRpaRecordId));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                SysDept dept = deptMap.get(row.getDeptId());
                List<SysEmployee> employeeList = employeeMap.get(row.getDeptId());
                List<CustomerRpaRecordDetail> detailList = detailMap.getOrDefault(row.getId(), Lists.newArrayList());
                return CustomerDeliverRpaDTO.builder()
                        .id(row.getId())
                        .rpaType(row.getRpaType())
                        .operType(row.getOperType())
                        .period(row.getPeriod())
                        .deptInfo((Objects.isNull(dept) ? "" : dept.getDeptName()) + (ObjectUtils.isEmpty(employeeList) ? "" :
                                ("(" + employeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("、")) + ")")))
                        .createTime(row.getCreateTime())
                        .totalDataCount(row.getTotalDataCount())
                        .importDataCount(detailList.stream().filter(d -> Objects.equals(d.getStatus(), 2)).count())
                        .status(row.getStatus())
                        .confirmRemark(row.getConfirmRemark())
                        .hasException(row.getHasErrorData())
                        .build();
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void rpaConfirm(RpaRecordConfirmVO vo, Long deptId) {
        CustomerRpaRecord rpaRecord = getById(vo.getRpaRecordId());
        if (Objects.isNull(rpaRecord)) {
            throw new ServiceException("上传记录不存在");
        }
        if (!Objects.equals(rpaRecord.getStatus(), RpaRecordStatus.CONFIRM.getStatus())) {
            throw new ServiceException("只有待确认可进行确认");
        }
        if (vo.getConfirmType() == 1) {
            updateById(new CustomerRpaRecord().setId(vo.getRpaRecordId())
                    .setConfirmRemark(vo.getConfirmRemark())
                    .setConfirmFiles(ObjectUtils.isEmpty(vo.getConfirmFiles()) ? "" : JSONArray.toJSONString(vo.getConfirmFiles()))
                    .setStatus(RpaRecordStatus.CONFIRMED.getStatus()));
            List<CustomerRpaRecordDetail> details = customerRpaRecordDetailService.selectByRpaRecordId(vo.getRpaRecordId());
            if (!ObjectUtils.isEmpty(details)) {
                details = details.stream().filter(d -> d.getStatus() == 0).collect(Collectors.toList());
                customerRpaRecordDetailService.saveRpaToCustomerDeliverByDetails(details, deptId);
            }
        } else {
            updateById(new CustomerRpaRecord().setId(vo.getRpaRecordId())
                    .setConfirmRemark(vo.getConfirmRemark())
                    .setConfirmFiles(ObjectUtils.isEmpty(vo.getConfirmFiles()) ? "" : JSONArray.toJSONString(vo.getConfirmFiles()))
                    .setStatus(RpaRecordStatus.RETURN.getStatus()));
        }
    }

    @Override
    public void rpaRecordRetry(CommonIdVO vo, Long deptId) {
        CustomerRpaRecord rpaRecord = getById(vo.getId());
        if (Objects.isNull(rpaRecord)) {
            throw new ServiceException("上传记录不存在");
        }
        List<CustomerRpaRecordDetail> failDetails = customerRpaRecordDetailService.selectFailByRpaRecordId(vo.getId());
        if (ObjectUtils.isEmpty(failDetails)) {
            return;
        }
        customerRpaRecordDetailService.saveRpaToCustomerDeliverByDetails(failDetails, deptId);
    }

    @Override
    public void rpaDetailRetry(CommonIdVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds()) && !Objects.isNull(vo.getId())) {
            vo.setIds(Collections.singletonList(vo.getId()));
        }
        customerRpaRecordDetailService.saveRpaToCustomerDeliverByDetailIds(vo.getIds(), deptId);
    }
}
