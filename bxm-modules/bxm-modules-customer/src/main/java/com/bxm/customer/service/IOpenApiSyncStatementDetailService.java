package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiSyncStatementDetail;

import java.util.List;

/**
 * 医社保个人明细查询记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface IOpenApiSyncStatementDetailService extends IService<OpenApiSyncStatementDetail>
{
    /**
     * 查询医社保个人明细查询记录
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 医社保个人明细查询记录
     */
    public OpenApiSyncStatementDetail selectOpenApiSyncStatementDetailById(Long id);

    /**
     * 查询医社保个人明细查询记录列表
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 医社保个人明细查询记录集合
     */
    public List<OpenApiSyncStatementDetail> selectOpenApiSyncStatementDetailList(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 新增医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    public int insertOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 修改医社保个人明细查询记录
     * 
     * @param openApiSyncStatementDetail 医社保个人明细查询记录
     * @return 结果
     */
    public int updateOpenApiSyncStatementDetail(OpenApiSyncStatementDetail openApiSyncStatementDetail);

    /**
     * 批量删除医社保个人明细查询记录
     * 
     * @param ids 需要删除的医社保个人明细查询记录主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncStatementDetailByIds(Long[] ids);

    /**
     * 删除医社保个人明细查询记录信息
     * 
     * @param id 医社保个人明细查询记录主键
     * @return 结果
     */
    public int deleteOpenApiSyncStatementDetailById(Long id);

    List<OpenApiSyncStatementDetail> selectBySyncRecordId(Long recordId);
}
