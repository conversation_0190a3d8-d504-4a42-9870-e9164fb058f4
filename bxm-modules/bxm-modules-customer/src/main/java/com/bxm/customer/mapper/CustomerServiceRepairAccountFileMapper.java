package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountFile;

/**
 * 补账 附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceRepairAccountFileMapper extends BaseMapper<CustomerServiceRepairAccountFile>
{
    /**
     * 查询补账 附件
     * 
     * @param id 补账 附件主键
     * @return 补账 附件
     */
    public CustomerServiceRepairAccountFile selectCustomerServiceRepairAccountFileById(Long id);

    /**
     * 查询补账 附件列表
     * 
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 补账 附件集合
     */
    public List<CustomerServiceRepairAccountFile> selectCustomerServiceRepairAccountFileList(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 新增补账 附件
     * 
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 修改补账 附件
     * 
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 删除补账 附件
     * 
     * @param id 补账 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountFileById(Long id);

    /**
     * 批量删除补账 附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountFileByIds(Long[] ids);
}
