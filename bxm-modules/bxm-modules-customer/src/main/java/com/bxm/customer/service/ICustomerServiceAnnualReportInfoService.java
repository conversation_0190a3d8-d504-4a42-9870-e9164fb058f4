package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceAnnualReportInfo;

/**
 * 客户服务工商年报信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICustomerServiceAnnualReportInfoService extends IService<CustomerServiceAnnualReportInfo>
{
    /**
     * 查询客户服务工商年报信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 客户服务工商年报信息
     */
    public CustomerServiceAnnualReportInfo selectCustomerServiceAnnualReportInfoById(Long id);

    /**
     * 查询客户服务工商年报信息列表
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 客户服务工商年报信息集合
     */
    public List<CustomerServiceAnnualReportInfo> selectCustomerServiceAnnualReportInfoList(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 新增客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    public int insertCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 修改客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    public int updateCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 批量删除客户服务工商年报信息
     * 
     * @param ids 需要删除的客户服务工商年报信息主键集合
     * @return 结果
     */
    public int deleteCustomerServiceAnnualReportInfoByIds(Long[] ids);

    /**
     * 删除客户服务工商年报信息信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 结果
     */
    public int deleteCustomerServiceAnnualReportInfoById(Long id);
}
