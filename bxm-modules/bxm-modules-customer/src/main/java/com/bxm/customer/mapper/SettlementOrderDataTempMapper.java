package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.SettlementOrderDataTemp;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataCountDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 结算单关联数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Mapper
public interface SettlementOrderDataTempMapper extends BaseMapper<SettlementOrderDataTemp>
{

    void insertBySql(@Param("insertSql") String insertSql);

    List<SettlementOrderDataDTO> settlementOrderDataListByBatchNo(IPage<SettlementOrderDataDTO> iPage,
                                                                  @Param("vo") SettlementOrderDataSearchVO vo);

    void saveByPeriodIds(@Param("periodMonthIds") List<Long> periodMonthIds,
                         @Param("batchNo") String batchNo,
                         @Param("businessDeptId") Long businessDeptId);

    void saveByCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                           @Param("batchNo") String batchNo,
                           @Param("businessDeptId") Long businessDeptId);
}
