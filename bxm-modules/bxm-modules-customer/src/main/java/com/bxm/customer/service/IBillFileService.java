package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.BillFile;

import java.util.List;

/**
 * 结算单附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface IBillFileService extends IService<BillFile>
{
    List<CommonFileVO> selectBillFileByBillId(Long billId, Integer fileType);

    void removeAndSaveNewFile(Long billId, List<CommonFileVO> files, Integer fileType);
}
