package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryV2DTO;

import java.util.List;
import java.util.Map;

/**
 * 材料交接单文件清单Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IMaterialDeliverFileInventoryService extends IService<MaterialDeliverFileInventory>
{
    /**
     * 查询材料交接单文件清单
     * 
     * @param id 材料交接单文件清单主键
     * @return 材料交接单文件清单
     */
    public MaterialDeliverFileInventory selectMaterialDeliverFileInventoryById(Long id);

    /**
     * 查询材料交接单文件清单列表
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 材料交接单文件清单集合
     */
    public List<MaterialDeliverFileInventory> selectMaterialDeliverFileInventoryList(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 新增材料交接单文件清单
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    public int insertMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 修改材料交接单文件清单
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    public int updateMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 批量删除材料交接单文件清单
     * 
     * @param ids 需要删除的材料交接单文件清单主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryByIds(Long[] ids);

    /**
     * 删除材料交接单文件清单信息
     * 
     * @param id 材料交接单文件清单主键
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryById(Long id);

    void generatePeriodInventoryFromFileInventory(Long materialDeliverId);

    void updateFileInventoryAndSyncPeriod(List<Long> fileInventoryIds, MaterialDeliverFileInventory updateData);

    void deleteFileInventoryAndSyncPeriod(List<Long> fileInventoryIds);

    void logicDeleteById(Long id);

    IPage<MaterialFileInventoryDTO> materialFileInventoryPageList(MaterialDeliver materialDeliver, Integer pageNum, Integer pageSize);

    IPage<MaterialFileInventoryV2DTO> materialFileInventoryPageListV2(MaterialDeliver materialDeliver, Integer pageNum, Integer pageSize);

    List<CommonFileVO> materialFileInventoryList(MaterialDeliver materialDeliver);

    void deleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds);

    void logicDeleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds);

    void updateMaterialDeliverFileInventoryStatus(List<Long> ids);

    void updateMaterialDeliverFileInventoryStatusV2(List<Long> ids);

    void updateMaterialDeliverFileInventoryStatus(MaterialDeliverFileInventory materialDeliverFileInventory, Map<Long, MaterialDeliver> materialDeliverMap, String batchNo);

    void updateMaterialDeliverFileInventoryStatusV2(MaterialDeliverFileInventory materialDeliverFileInventory, Map<Long, MaterialDeliver> materialDeliverMap, String batchNo);

    List<MaterialDeliverFileInventory> selectCanNotPushFileInventory(List<Long> materialDeliverIds);
}
