package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.domain.CustomerDeliver;
import com.bxm.customer.domain.OpenApiSupplementRecord;
import com.bxm.customer.domain.OpenApiSyncCustomer;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.vo.*;

import java.util.List;

/**
 * 交付Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface ICustomerDeliverService extends IService<CustomerDeliver>
{
    /**
     * 查询交付
     * 
     * @param id 交付主键
     * @return 交付
     */
    public CustomerDeliver selectCustomerDeliverById(Long id);

    /**
     * 查询交付列表
     * 
     * @param customerDeliver 交付
     * @return 交付集合
     */
    public List<CustomerDeliver> selectCustomerDeliverList(CustomerDeliver customerDeliver);

    /**
     * 新增交付
     * 
     * @param customerDeliver 交付
     * @return 结果
     */
    public int insertCustomerDeliver(CustomerDeliver customerDeliver);

    /**
     * 修改交付
     * 
     * @param customerDeliver 交付
     * @return 结果
     */
    public int updateCustomerDeliver(CustomerDeliver customerDeliver);

    /**
     * 批量删除交付
     * 
     * @param ids 需要删除的交付主键集合
     * @return 结果
     */
    public int deleteCustomerDeliverByIds(Long[] ids);

    /**
     * 删除交付信息
     * 
     * @param id 交付主键
     * @return 结果
     */
    public int deleteCustomerDeliverById(Long id);

    IPage<CustomerDeliverDTO> deliverList(CustomerDeliverSearchVO vo, Long deptId);

    List<CustomerDeliverForCustomerServiceDetailDTO> deliverListByCustomerServiceId(Long customerServiceId);

    CustomerDeliverDetailDTO deliverDetail(Long id);

    void add(CustomerDeliverVO vo, Long deptId);

    void saveDeliver(CustomerDeliverVO vo, Long deptId);

    void remoteCreate(RemoteCustomerDeliverCreateVO vo);

    void edit(CustomerDeliverVO vo);

    void saveEdit(CustomerDeliverVO vo);

    CommonDeliverOperateDTO delete(Long[] ids);

    CommonDeliverOperateDTO confirm(CommonIdVO vo);

    CommonDeliverOperateDTO remoteConfirm(List<RemoteConfirmDeliverVO> voList);

    CommonDeliverOperateDTO reBack(CommonIdVO vo);

    CommonDeliverOperateDTO submit(CommonIdVO vo);

    void saveReport(CommonIdVO vo);

    void report(CustomerDeliverReportVO vo);

    void xqyRepeatConfirm(CustomerDeliverReportVO vo);

    void xqyDeduction(CustomerDeliverReportVO vo);

    void remoteReport(RemoteCustomerDeliverReportVO vo);

    void deduction(CustomerDeliverDeductionVO vo);

    void remoteDeduction(RemoteCustomerDeliverDeductionVO vo);

    void exceptionDeal(CustomerDeliverExceptionVO vo);

    void remoteExceptionDeal(RemoteCustomerDeliverExceptionVO vo);

    @Deprecated
    void supplement(CustomerDeliverVO vo);

    @Deprecated
    void remoteSupplement(RemoteCustomerDeliverCreateVO vo);

    void auth(CustomerDeliverAuthVO vo);

    void xqyAuth(CustomerDeliverAuthVO vo);

    void remoteAuth(RemoteCustomerDeliverAuthVO vo);

    CustomerDeliver selectByCustomerServiceIdAndPeriodAndDeliverType(Long customerServiceId, Integer period, Integer deliverType);

    Boolean checkCustomerServiceTabTypeExists(Long customerServiceId, Integer period, Integer tabType);

    List<CustomerDeliver> selectByCustomerServicePeriodMonthIdAndDeliverTypes(Long customerServicePeriodMonthId, List<Integer> deliverTypes);

    List<CustomerDeliver> selectByCustomerServicePeriodMonthIdsAndDeliverTypes(List<Long> customerServicePeriodMonthIds, List<Integer> deliverTypes);

    Boolean checkCustomerServiceDeliverTypeExists(Long customerServiceId, Integer period, Integer deliverType, String taxCheckType);

    List<DeliverFileCommonDTO> getDeliverFiles(DeliverFileGetVO vo);

    CustomerDeliver getByCustomerServiceIdAndPeriodIdAndDeliverType(Long periodId, Long customerServiceId, Integer deliverType);

    CommonDeliverOperateDTO reportDeductionSubmit(CommonIdVO vo);

    void createCountryTaxDeliver(String jobParam);

    void createPersonTaxDeliver(String jobParam);

    void createPersonOperatingTaxDeliver(String jobParam);

    void createMedicalSocialSecurityDeliver(String jobParam);

    void preAuthDeliverConfirmTask();

    List<Integer> getPeriodSelect(Integer deliverType);

    void correctionsReport(CustomerDeliverVO vo);

    void correctionsReportResult(CustomerDeliverReportVO vo);

    void supplementReportFiles(CommonIdVO vo, Long deptId);

    void supplementReportFilesForXqy(Long deliverId, String fileUrl, String fileName, String officalFilename, String operName, String batchNo);

    List<CustomerDeliverDTO> remoteDeliverList(CustomerDeliverSearchVO vo, Long deptId, Long userId);

    List<CommonDeptCountDTO> remoteDeliverAccountingDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    List<CommonDeptCountDTO> remoteDeliverAdvisorDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    List<CommonDeptCountDTO> deliverCustomerServiceAdvisorDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    List<CommonDeptCountDTO> deliverCustomerServiceAccountingDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    List<CommonDeptCountDTO> deliverPeriodAdvisorDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    List<CommonDeptCountDTO> deliverPeriodAccountingDeptCountList(Long deptId, CustomerDeliverSearchVO vo);

    void batchDeliverCheckResult(RemoteCustomerDeliverCheckResultVO vo);

    void remoteSupplementReportFiles(List<RemoteSupplementReportFilesVO> voList);

    void remoteSupplementFiles(RemoteSupplementReportFilesVO vo);

    void supplementReportFilesForXqy(Long deliverId, List<OpenApiSupplementRecord> supplementList, String operName, Long deptId);

    void supplementReportFilesForCommon(Long deliverId, List<OpenApiSupplementRecord> supplementList, String operName, String sourceName, Integer fileType, Long deptId);

    DeliverPersonChangeInfoDTO getSamePeriodPersonChangeInfo(Long customerPeriodMonthId);

    void correctionsReportV2(CustomerDeliverVO vo);

    CommonDeliverOperateDTO receiveDeliverChanged(CommonIdVO vo);

    CommonDeliverOperateDTO submitNewDeliver(CommonIdVO vo);

    void deductionForCommon(Long deliverId, Integer result, List<CommonFileVO> files, String operateName, String sourceName, String remark, Long deptId);

    void removeAndSaveNewStatementDetailFile(Long deliverId, String operName, CommonFileVO build, Long deptId);

    void preAuthConfirm(PreAuthConfirmVO vo);

    void remotePreAuthConfirm(PreAuthConfirmVO vo);

    void preAuthCorrectionsAuth(CustomerDeliverVO vo);

    CommonDeliverOperateDTO preAuthReceiveDeliverChanged(CommonIdVO vo);

    void rejectDeliver(CommonIdVO vo);

    void remoteReject(RemoteRejectVO vo);

    void remoteSupplementAuthFiles(List<RemoteSupplementReportFilesVO> voList);

    void check(CustomerDeliverCheckVO vo);

    void remoteConfirmSingle(RemoteConfirmDeliverVO vo);

    void overDeliver(RemoteOverDeliverVO vo);

    void remoteOverDeliver(RemoteOverDeliverVO vo);

    void dealOverException(RemoteDealOverExceptionDeliverVO vo);

    void remoteDealOverException(RemoteDealOverExceptionDeliverVO vo);

    CommonDeliverOperateDTO submitAuthBatch(CommonIdVO vo);

    String getPreAuthRemind(Long id);

    void preAuthRemind(OpenApiSyncCustomer syncCustomer, String operateName, Long deptId);

    List<CustomerDeliver> getByPeriodIdsAndDeliverTypes(RemoteDeliverSearchVO vo);

    void commentDeliver(CustomerDeliverCommentVO vo, Long deptId);

    void modifyMattersNotes(MattersNotesModifyVO vo, Long deptId);

    CommonDeliverOperateDTO commonSubmit(SubmitOrReBackVO vo, Long deptId);

    CommonDeliverOperateDTO commonReBack(SubmitOrReBackVO vo, Long deptId);

    void reportV2(ReportV2VO vo, Long deptId);

    void remoteReportV2(ReportV2VO vo, Long deptId);

    void deductionV2(DeductionV2VO vo, Long deptId);

    void remoteDeductionV2(DeductionV2VO vo, Long deptId);

    void supplementReportFilesV2(SupplementReportFilesV2VO vo, Long deptId);

    void remoteSupplementReportFilesV2(SupplementReportFilesV2VO vo, Long deptId);

    void remoteUpdateTaxReportTotalAmount(RemoteUpdateTaxReportTotalAmountVO vo, Long deptId);

    CommonDeliverOperateDTO autoReport(List<Long> ids, Long deptId);

    CommonDeliverOperateDTO autoDeduction(List<Long> ids, Long deptId);

    CommonDeliverOperateDTO autoCheck(List<Long> ids, Long deptId);

    void dealPersonTaxReport(CommonNoticeVO commonNoticeVO);

    void dealPersonTaxDeduction(CommonNoticeVO commonNoticeVO);

    void dealPersonTaxStatusSearch(CommonNoticeVO commonNoticeVO);

    void dealPersonTaxCheck(CommonNoticeVO commonNoticeVO);

    void dealPersonTaxTableDownload(CommonNoticeVO commonNoticeVO);

    void rpaPersonTaxCheckTask();

    void dealReportTableDownload(CommonNoticeVO commonNoticeVO);

    void xqyAnuualReportComplete(ReportV2VO vo);

    CommonDeliverOperateDTO modifyDdl(CustomerDeliverModifyDdlVO vo, Long deptId);

    List<CommonFileVO> getPersonTaxDeliverFiles(String taxNumber, Integer period, Long deptId);
}
