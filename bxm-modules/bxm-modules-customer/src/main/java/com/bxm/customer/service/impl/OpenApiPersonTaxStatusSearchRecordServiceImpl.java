package com.bxm.customer.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.OpenApiPersonTaxStatusSearchRecordMapper;
import com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord;
import com.bxm.customer.service.IOpenApiPersonTaxStatusSearchRecordService;

/**
 * 个税状态查询定时轮询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class OpenApiPersonTaxStatusSearchRecordServiceImpl extends ServiceImpl<OpenApiPersonTaxStatusSearchRecordMapper, OpenApiPersonTaxStatusSearchRecord> implements IOpenApiPersonTaxStatusSearchRecordService
{
    @Autowired
    private OpenApiPersonTaxStatusSearchRecordMapper openApiPersonTaxStatusSearchRecordMapper;

    /**
     * 查询个税状态查询定时轮询
     * 
     * @param id 个税状态查询定时轮询主键
     * @return 个税状态查询定时轮询
     */
    @Override
    public OpenApiPersonTaxStatusSearchRecord selectOpenApiPersonTaxStatusSearchRecordById(Long id)
    {
        return openApiPersonTaxStatusSearchRecordMapper.selectOpenApiPersonTaxStatusSearchRecordById(id);
    }

    /**
     * 查询个税状态查询定时轮询列表
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 个税状态查询定时轮询
     */
    @Override
    public List<OpenApiPersonTaxStatusSearchRecord> selectOpenApiPersonTaxStatusSearchRecordList(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord)
    {
        return openApiPersonTaxStatusSearchRecordMapper.selectOpenApiPersonTaxStatusSearchRecordList(openApiPersonTaxStatusSearchRecord);
    }

    /**
     * 新增个税状态查询定时轮询
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 结果
     */
    @Override
    public int insertOpenApiPersonTaxStatusSearchRecord(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord)
    {
        openApiPersonTaxStatusSearchRecord.setCreateTime(DateUtils.getNowDate());
        return openApiPersonTaxStatusSearchRecordMapper.insertOpenApiPersonTaxStatusSearchRecord(openApiPersonTaxStatusSearchRecord);
    }

    /**
     * 修改个税状态查询定时轮询
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 结果
     */
    @Override
    public int updateOpenApiPersonTaxStatusSearchRecord(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord)
    {
        openApiPersonTaxStatusSearchRecord.setUpdateTime(DateUtils.getNowDate());
        return openApiPersonTaxStatusSearchRecordMapper.updateOpenApiPersonTaxStatusSearchRecord(openApiPersonTaxStatusSearchRecord);
    }

    /**
     * 批量删除个税状态查询定时轮询
     * 
     * @param ids 需要删除的个税状态查询定时轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiPersonTaxStatusSearchRecordByIds(Long[] ids)
    {
        return openApiPersonTaxStatusSearchRecordMapper.deleteOpenApiPersonTaxStatusSearchRecordByIds(ids);
    }

    /**
     * 删除个税状态查询定时轮询信息
     * 
     * @param id 个税状态查询定时轮询主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiPersonTaxStatusSearchRecordById(Long id)
    {
        return openApiPersonTaxStatusSearchRecordMapper.deleteOpenApiPersonTaxStatusSearchRecordById(id);
    }

    @Override
    public void createPersonTaxStatusSearchRecord(Long deliverId, String searchParam, LocalDateTime operTime) {
        save(new OpenApiPersonTaxStatusSearchRecord().setDeliverId(deliverId)
                .setStatus(1)
                .setSearchTime((Objects.isNull(operTime) ? LocalDateTime.now() : operTime).plusHours(1L))
                .setSearchStatusParam(searchParam));
    }

    @Override
    public void closeTaskById(Long taskId) {
        if (!Objects.isNull(taskId)) {
            updateById(new OpenApiPersonTaxStatusSearchRecord().setStatus(2).setId(taskId));
        }
    }

    @Override
    public void continueTaskById(Long taskId) {
        if (!Objects.isNull(taskId)) {
            updateById(new OpenApiPersonTaxStatusSearchRecord().setStatus(1).setId(taskId));
        }
    }
}
