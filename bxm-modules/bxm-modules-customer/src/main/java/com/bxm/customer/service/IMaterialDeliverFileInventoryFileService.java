package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.MaterialDeliverFileInventoryFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.MaterialDeliverFileInventoryFile;

import java.util.List;

/**
 * 文件清单附件Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-12
 */
public interface IMaterialDeliverFileInventoryFileService extends IService<MaterialDeliverFileInventoryFile>
{
    /**
     * 查询文件清单附件
     * 
     * @param id 文件清单附件主键
     * @return 文件清单附件
     */
    public MaterialDeliverFileInventoryFile selectMaterialDeliverFileInventoryFileById(Long id);

    /**
     * 查询文件清单附件列表
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 文件清单附件集合
     */
    public List<MaterialDeliverFileInventoryFile> selectMaterialDeliverFileInventoryFileList(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 新增文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    public int insertMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 修改文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    public int updateMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 批量删除文件清单附件
     * 
     * @param ids 需要删除的文件清单附件主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryFileByIds(Long[] ids);

    /**
     * 删除文件清单附件信息
     * 
     * @param id 文件清单附件主键
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryFileById(Long id);

    void saveFiles(Long materialDeliverFileInventoryId, List<CommonFileVO> files, MaterialDeliverFileInventoryFileType fileType);

    List<MaterialDeliverFileInventoryFile> selectByBatchFileInventoryIdAndFileType(List<Long> materialDeliverFileInventoryIds, Integer fileType);

    List<CommonFileVO> getMaterialFileInventoryFile(Long materialFileInventoryId, Integer fileType);

    void deleteByMaterialDeliverFileInventoryIdAndFileType(Long materialFileInventoryId, Integer fileType);

    void deleteByMaterialDeliverFileInventoryIdsAndFileType(List<Long> materialDeliverFileInventoryIds, Integer fileType);
}
