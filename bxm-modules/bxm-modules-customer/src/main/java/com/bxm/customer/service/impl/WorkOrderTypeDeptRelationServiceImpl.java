package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.WorkOrderTypeDeptRelationMapper;
import com.bxm.customer.domain.WorkOrderTypeDeptRelation;
import com.bxm.customer.service.IWorkOrderTypeDeptRelationService;

/**
 * 工单类型组织可见Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Service
public class WorkOrderTypeDeptRelationServiceImpl extends ServiceImpl<WorkOrderTypeDeptRelationMapper, WorkOrderTypeDeptRelation> implements IWorkOrderTypeDeptRelationService
{
    @Autowired
    private WorkOrderTypeDeptRelationMapper workOrderTypeDeptRelationMapper;

    /**
     * 查询工单类型组织可见
     * 
     * @param id 工单类型组织可见主键
     * @return 工单类型组织可见
     */
    @Override
    public WorkOrderTypeDeptRelation selectWorkOrderTypeDeptRelationById(Long id)
    {
        return workOrderTypeDeptRelationMapper.selectWorkOrderTypeDeptRelationById(id);
    }

    /**
     * 查询工单类型组织可见列表
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 工单类型组织可见
     */
    @Override
    public List<WorkOrderTypeDeptRelation> selectWorkOrderTypeDeptRelationList(WorkOrderTypeDeptRelation workOrderTypeDeptRelation)
    {
        return workOrderTypeDeptRelationMapper.selectWorkOrderTypeDeptRelationList(workOrderTypeDeptRelation);
    }

    /**
     * 新增工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    @Override
    public int insertWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation)
    {
        workOrderTypeDeptRelation.setCreateTime(DateUtils.getNowDate());
        return workOrderTypeDeptRelationMapper.insertWorkOrderTypeDeptRelation(workOrderTypeDeptRelation);
    }

    /**
     * 修改工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    @Override
    public int updateWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation)
    {
        workOrderTypeDeptRelation.setUpdateTime(DateUtils.getNowDate());
        return workOrderTypeDeptRelationMapper.updateWorkOrderTypeDeptRelation(workOrderTypeDeptRelation);
    }

    /**
     * 批量删除工单类型组织可见
     * 
     * @param ids 需要删除的工单类型组织可见主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderTypeDeptRelationByIds(Long[] ids)
    {
        return workOrderTypeDeptRelationMapper.deleteWorkOrderTypeDeptRelationByIds(ids);
    }

    /**
     * 删除工单类型组织可见信息
     * 
     * @param id 工单类型组织可见主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderTypeDeptRelationById(Long id)
    {
        return workOrderTypeDeptRelationMapper.deleteWorkOrderTypeDeptRelationById(id);
    }
}
