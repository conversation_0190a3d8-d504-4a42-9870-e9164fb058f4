package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierDetailDTO {

    @ApiModelProperty("账务交付单id")
    private Long id;

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户服务是否有凭票入账标签")
    private Boolean customerServiceHasTicketTag;

    @ApiModelProperty("账期是否有凭票入账标签")
    private Boolean periodHasTicketTag;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("账务交付单标题")
    private String title;

    @ApiModelProperty("事项备忘（改账没有）")
    private String mattersNotes;

    @ApiModelProperty("银行材料/入账材料/改账材料")
    private List<CommonFileVO> materialFiles;

    @ApiModelProperty("对账单材料")
    private List<CommonFileVO> checkFiles;

    @ApiModelProperty("回单材料")
    private List<CommonFileVO> receiptFiles;

    @ApiModelProperty("交付要求")
    private String deliverRequire;

    @ApiModelProperty("交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付")
    private Integer deliverStatus;

    @ApiModelProperty("交付状态")
    private String deliverStatusStr;

    @ApiModelProperty("交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    private Integer deliverResult;

    @ApiModelProperty("交付结果")
    private String deliverResultStr;

    @ApiModelProperty("银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    private Integer bankPaymentResult;

    @ApiModelProperty("银行流水结果（银行流水的列表通过账期id去获取）")
    private String bankPaymentResultStr;

    @ApiModelProperty("完成时间")
    private String completeTime;

    @ApiModelProperty("完成备注")
    private String deliverRemark;

    @ApiModelProperty("结账结果，1-未入账，2-已入账未结账，3-已结账")
    private Integer settleAccountStatus;

    @ApiModelProperty("结账结果")
    private String settleAccountStatusStr;

    @ApiModelProperty("结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endTime;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    private Integer materialIntegrity;

    @ApiModelProperty("材料完整度（文案）")
    private String materialIntegrityStr;

    @ApiModelProperty("完成附件")
    private List<CommonFileVO> deliverFiles;

    @ApiModelProperty("银行流水任务列表，银行流水交付单详情才会返回")
    private List<BankPaymentTaskDTO> bankPaymentTaskList;

    @ApiModelProperty("关联账务交付单列表，改账详情才会返回")
    private List<AccountingCashierSimpleDTO> relationAccountingCashierList;

    @ApiModelProperty("账务数据，入账详情才会返回")
    private AccountingCashierInAccountDTO accountingCashierInAccountInfo;

    @ApiModelProperty("ddl")
    private String ddl;

    @ApiModelProperty("是否关闭交付，1-是，0-否")
    private Integer isClose;
}
