package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已扣款状态变更策略
 *
 * 处理从"已扣款"状态到其他状态的转换变更
 *
 * 注意：已扣款是终态状态，一般情况下不允许转换到其他状态
 * 仅在特殊情况下允许转换：
 * 1. DEDUCTION_COMPLETED -> DEDUCTION_EXCEPTION (发现扣款异常，需要处理)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionCompletedStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            return false;
        }

        // 已扣款状态是终态，仅在特殊情况下允许转换到扣款异常状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            // 验证转换到扣款异常状态
            validateToDeductionException(order, request);
        } else {
            throwUnsupportedTransition("已扣款", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    /**
     * 验证转换到扣款异常状态
     *
     * 这种转换通常发生在以下情况：
     * 1. 发现扣款金额错误
     * 2. 客户投诉扣款问题
     * 3. 系统扣款异常需要人工处理
     */
    private void validateToDeductionException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateReason(request.getReason(), 20, "从已扣款状态转换到异常状态");
        validateRemark(request.getRemark(), "从终态转换");

        log.warn("Converting from DEDUCTION_COMPLETED to DEDUCTION_EXCEPTION for order: {}, reason: {}",
                request.getDeliveryOrderNo(), request.getReason());
    }
}
