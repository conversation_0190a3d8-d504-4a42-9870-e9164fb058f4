package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiSyncCustomer;

/**
 * 第三方申报同步客户Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IOpenApiSyncCustomerService extends IService<OpenApiSyncCustomer>
{
    /**
     * 查询第三方申报同步客户
     * 
     * @param id 第三方申报同步客户主键
     * @return 第三方申报同步客户
     */
    public OpenApiSyncCustomer selectOpenApiSyncCustomerById(Long id);

    /**
     * 查询第三方申报同步客户列表
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 第三方申报同步客户集合
     */
    public List<OpenApiSyncCustomer> selectOpenApiSyncCustomerList(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 新增第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    public int insertOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 修改第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    public int updateOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 批量删除第三方申报同步客户
     * 
     * @param ids 需要删除的第三方申报同步客户主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncCustomerByIds(Long[] ids);

    /**
     * 删除第三方申报同步客户信息
     * 
     * @param id 第三方申报同步客户主键
     * @return 结果
     */
    public int deleteOpenApiSyncCustomerById(Long id);

    List<OpenApiSyncCustomer> selectBySyncRecordId(Long recordId);
}
