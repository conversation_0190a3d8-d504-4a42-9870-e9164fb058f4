package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.BillSettlementOrderRelation;

/**
 * 账单结算单关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@Mapper
public interface BillSettlementOrderRelationMapper extends BaseMapper<BillSettlementOrderRelation>
{
    /**
     * 查询账单结算单关系
     * 
     * @param id 账单结算单关系主键
     * @return 账单结算单关系
     */
    public BillSettlementOrderRelation selectBillSettlementOrderRelationById(Long id);

    /**
     * 查询账单结算单关系列表
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 账单结算单关系集合
     */
    public List<BillSettlementOrderRelation> selectBillSettlementOrderRelationList(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 新增账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    public int insertBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 修改账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    public int updateBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation);

    /**
     * 删除账单结算单关系
     * 
     * @param id 账单结算单关系主键
     * @return 结果
     */
    public int deleteBillSettlementOrderRelationById(Long id);

    /**
     * 批量删除账单结算单关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBillSettlementOrderRelationByIds(Long[] ids);
}
