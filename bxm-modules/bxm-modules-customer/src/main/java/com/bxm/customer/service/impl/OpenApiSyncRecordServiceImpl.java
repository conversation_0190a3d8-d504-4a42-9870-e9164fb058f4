package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.OpenApiSyncRecordMapper;
import com.bxm.customer.domain.OpenApiSyncRecord;
import com.bxm.customer.service.IOpenApiSyncRecordService;

/**
 * 第三方申报同步Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class OpenApiSyncRecordServiceImpl extends ServiceImpl<OpenApiSyncRecordMapper, OpenApiSyncRecord> implements IOpenApiSyncRecordService
{
    @Autowired
    private OpenApiSyncRecordMapper openApiSyncRecordMapper;

    /**
     * 查询第三方申报同步
     * 
     * @param id 第三方申报同步主键
     * @return 第三方申报同步
     */
    @Override
    public OpenApiSyncRecord selectOpenApiSyncRecordById(Long id)
    {
        return openApiSyncRecordMapper.selectOpenApiSyncRecordById(id);
    }

    /**
     * 查询第三方申报同步列表
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 第三方申报同步
     */
    @Override
    public List<OpenApiSyncRecord> selectOpenApiSyncRecordList(OpenApiSyncRecord openApiSyncRecord)
    {
        return openApiSyncRecordMapper.selectOpenApiSyncRecordList(openApiSyncRecord);
    }

    /**
     * 新增第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    @Override
    public int insertOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord)
    {
        openApiSyncRecord.setCreateTime(DateUtils.getNowDate());
        return openApiSyncRecordMapper.insertOpenApiSyncRecord(openApiSyncRecord);
    }

    /**
     * 修改第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    @Override
    public int updateOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord)
    {
        openApiSyncRecord.setUpdateTime(DateUtils.getNowDate());
        return openApiSyncRecordMapper.updateOpenApiSyncRecord(openApiSyncRecord);
    }

    /**
     * 批量删除第三方申报同步
     * 
     * @param ids 需要删除的第三方申报同步主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncRecordByIds(Long[] ids)
    {
        return openApiSyncRecordMapper.deleteOpenApiSyncRecordByIds(ids);
    }

    /**
     * 删除第三方申报同步信息
     * 
     * @param id 第三方申报同步主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncRecordById(Long id)
    {
        return openApiSyncRecordMapper.deleteOpenApiSyncRecordById(id);
    }
}
