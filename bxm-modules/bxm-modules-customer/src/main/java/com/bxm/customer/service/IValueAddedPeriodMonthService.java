package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedPeriodMonth;

/**
 * 增值期间月度表Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IValueAddedPeriodMonthService extends IService<ValueAddedPeriodMonth> {

    /**
     * 检查指定条件下是否存在账期记录
     *
     * @param businessTopDeptId 顶级业务部门ID
     * @param creditCode 统一社会信用代码
     * @param periodStart 账期开始时间
     * @param periodEnd 账期结束时间
     * @return 是否存在记录
     */
    boolean checkPeriodExists(Long businessTopDeptId, String creditCode, Integer periodStart, Integer periodEnd);
}
