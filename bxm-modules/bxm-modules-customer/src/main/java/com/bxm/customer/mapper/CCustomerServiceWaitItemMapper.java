package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.CCustomerServiceWaitItem;
import com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户服务待确认事项Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-12
 */
@Mapper
public interface CCustomerServiceWaitItemMapper extends BaseMapper<CCustomerServiceWaitItem>
{
    /**
     * 查询客户服务待确认事项
     * 
     * @param id 客户服务待确认事项主键
     * @return 客户服务待确认事项
     */
    public CCustomerServiceWaitItem selectCCustomerServiceWaitItemById(Long id);

    /**
     * 查询客户服务待确认事项列表
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 客户服务待确认事项集合
     */
    public List<CCustomerServiceWaitItem> selectCCustomerServiceWaitItemList(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 新增客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    public int insertCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 修改客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    public int updateCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem);

    /**
     * 删除客户服务待确认事项
     * 
     * @param id 客户服务待确认事项主键
     * @return 结果
     */
    public int deleteCCustomerServiceWaitItemById(Long id);

    /**
     * 批量删除客户服务待确认事项
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCCustomerServiceWaitItemByIds(Long[] ids);

    /**
     * 客户服务待确认事项列表
     *
     * @param iPage 分页参数
     * @param itemType 待确认事项类型
     * @param keyWord 搜索关键字
     * @param deptIds 部门id集合 根据请求头里的deptId查询出来的结果集，决定数据权限
     * @param jumpType 跳转类型
     * @param queryDeptIds 查询部门id集合 根据下拉框里选择的deptId查询出来的结果集，决定下拉选择的结果
     * @param tagName 标签名称
     * @param tagIncludeFlag 标签包含标识
     * @param customerServiceIds 客户服务id集合
     * @param advisorSearchDeptIds 查询顾问部门id集合 根据搜索框输入的查询部门查询出来的结果集
     * @param accountingSearchDeptIds 查询会计部门id集合 根据搜索框输入的查询部门查询出来的结果集
     **/
    List<CustomerServiceWaitItemDTO> customerServiceWaitItemList(IPage<CustomerServiceWaitItemDTO> iPage, @Param("itemType") Integer itemType, @Param("keyWord") String keyWord, @Param("deptIds") List<Long> deptIds, @Param("jumpType") Integer jumpType, @Param("queryDeptIds") List<Long> queryDeptIds,
                                                                 @Param("tagName") String tagName, @Param("tagIncludeFlag") Integer tagIncludeFlag, @Param("customerServiceIds") List<Long> customerServiceIds, @Param("advisorSearchDeptIds") List<Long> advisorSearchDeptIds, @Param("accountingSearchDeptIds") List<Long> accountingSearchDeptIds, @Param("isAdmin") Integer isAdmin,
                                                                 @Param("taxType") Integer taxType);

    List<CCustomerServiceWaitItem> selectByDeptIdsAndItemType(@Param("deptIds") List<Long> deptIds, @Param("itemType") Integer itemType);
}
