package com.bxm.customer.service.impl;

import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.file.api.RemoteFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerDeliverTemplateMapper;
import com.bxm.customer.domain.CustomerDeliverTemplate;
import com.bxm.customer.service.ICustomerDeliverTemplateService;

/**
 * 交付模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Service
public class CustomerDeliverTemplateServiceImpl extends ServiceImpl<CustomerDeliverTemplateMapper, CustomerDeliverTemplate> implements ICustomerDeliverTemplateService
{
    @Autowired
    private CustomerDeliverTemplateMapper customerDeliverTemplateMapper;

    @Autowired
    private RemoteFileService remoteFileService;

    /**
     * 查询交付模板
     * 
     * @param id 交付模板主键
     * @return 交付模板
     */
    @Override
    public CustomerDeliverTemplate selectCustomerDeliverTemplateById(Long id)
    {
        return customerDeliverTemplateMapper.selectCustomerDeliverTemplateById(id);
    }

    /**
     * 查询交付模板列表
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 交付模板
     */
    @Override
    public List<CustomerDeliverTemplate> selectCustomerDeliverTemplateList(CustomerDeliverTemplate customerDeliverTemplate)
    {
        return customerDeliverTemplateMapper.selectCustomerDeliverTemplateList(customerDeliverTemplate);
    }

    /**
     * 新增交付模板
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 结果
     */
    @Override
    public int insertCustomerDeliverTemplate(CustomerDeliverTemplate customerDeliverTemplate)
    {
        customerDeliverTemplate.setCreateTime(DateUtils.getNowDate());
        return customerDeliverTemplateMapper.insertCustomerDeliverTemplate(customerDeliverTemplate);
    }

    /**
     * 修改交付模板
     * 
     * @param customerDeliverTemplate 交付模板
     * @return 结果
     */
    @Override
    public int updateCustomerDeliverTemplate(CustomerDeliverTemplate customerDeliverTemplate)
    {
        customerDeliverTemplate.setUpdateTime(DateUtils.getNowDate());
        return customerDeliverTemplateMapper.updateCustomerDeliverTemplate(customerDeliverTemplate);
    }

    /**
     * 批量删除交付模板
     * 
     * @param ids 需要删除的交付模板主键
     * @return 结果
     */
    @Override
    public int deleteCustomerDeliverTemplateByIds(Long[] ids)
    {
        return customerDeliverTemplateMapper.deleteCustomerDeliverTemplateByIds(ids);
    }

    /**
     * 删除交付模板信息
     * 
     * @param id 交付模板主键
     * @return 结果
     */
    @Override
    public int deleteCustomerDeliverTemplateById(Long id)
    {
        return customerDeliverTemplateMapper.deleteCustomerDeliverTemplateById(id);
    }

    @Override
    public CustomerDeliverTemplate selectByDeliverTypeAndOperType(Integer deliverType, Integer operType) {
        return getOne(new LambdaQueryWrapper<CustomerDeliverTemplate>()
                .eq(CustomerDeliverTemplate::getDeliverType, deliverType)
                .eq(CustomerDeliverTemplate::getOperType, operType));
    }

    @Override
    public String getDeliverTemplate(Integer deliverType, Integer operType, Integer period) {
        if (deliverType == 4 && operType == 1 && !Objects.isNull(period) && period % 100 == 12) {
            operType = 99;
        }
        CustomerDeliverTemplate template = selectByDeliverTypeAndOperType(deliverType, operType);
        return Objects.isNull(template) ? "" : remoteFileService.getFullFileUrl(template.getFileUrl()).getDataThrowException();
    }
}
