package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.QualityCheckingItemMapper;
import com.bxm.customer.domain.QualityCheckingItem;
import com.bxm.customer.service.IQualityCheckingItemService;

/**
 * 质检项目配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class QualityCheckingItemServiceImpl extends ServiceImpl<QualityCheckingItemMapper, QualityCheckingItem> implements IQualityCheckingItemService
{
    @Autowired
    private QualityCheckingItemMapper qualityCheckingItemMapper;

    /**
     * 查询质检项目配置
     * 
     * @param id 质检项目配置主键
     * @return 质检项目配置
     */
    @Override
    public QualityCheckingItem selectQualityCheckingItemById(Long id)
    {
        return qualityCheckingItemMapper.selectQualityCheckingItemById(id);
    }

    /**
     * 查询质检项目配置列表
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 质检项目配置
     */
    @Override
    public List<QualityCheckingItem> selectQualityCheckingItemList(QualityCheckingItem qualityCheckingItem)
    {
        return qualityCheckingItemMapper.selectQualityCheckingItemList(qualityCheckingItem);
    }

    /**
     * 新增质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    @Override
    public int insertQualityCheckingItem(QualityCheckingItem qualityCheckingItem)
    {
        qualityCheckingItem.setCreateTime(DateUtils.getNowDate());
        return qualityCheckingItemMapper.insertQualityCheckingItem(qualityCheckingItem);
    }

    /**
     * 修改质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    @Override
    public int updateQualityCheckingItem(QualityCheckingItem qualityCheckingItem)
    {
        qualityCheckingItem.setUpdateTime(DateUtils.getNowDate());
        return qualityCheckingItemMapper.updateQualityCheckingItem(qualityCheckingItem);
    }

    /**
     * 批量删除质检项目配置
     * 
     * @param ids 需要删除的质检项目配置主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingItemByIds(Long[] ids)
    {
        return qualityCheckingItemMapper.deleteQualityCheckingItemByIds(ids);
    }

    /**
     * 删除质检项目配置信息
     * 
     * @param id 质检项目配置主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingItemById(Long id)
    {
        return qualityCheckingItemMapper.deleteQualityCheckingItemById(id);
    }
}
