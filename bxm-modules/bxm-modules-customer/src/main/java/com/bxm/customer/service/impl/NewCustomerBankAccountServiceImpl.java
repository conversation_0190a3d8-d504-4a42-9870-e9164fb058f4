package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BankDirectStatus;
import com.bxm.common.core.enums.BankReceiptStatus;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.NewCustomerBankAccount;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferBankAccountDTO;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.NewCustomerBankAccountMapper;
import com.bxm.customer.service.INewCustomerBankAccountService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysEmployee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新户流转银行账号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
@Slf4j
public class NewCustomerBankAccountServiceImpl extends ServiceImpl<NewCustomerBankAccountMapper, NewCustomerBankAccount> implements INewCustomerBankAccountService
{
    @Autowired
    private NewCustomerBankAccountMapper newCustomerBankAccountMapper;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    /**
     * 查询新户流转银行账号
     * 
     * @param id 新户流转银行账号主键
     * @return 新户流转银行账号
     */
    @Override
    public NewCustomerBankAccount selectNewCustomerBankAccountById(Long id)
    {
        return newCustomerBankAccountMapper.selectNewCustomerBankAccountById(id);
    }

    /**
     * 查询新户流转银行账号列表
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 新户流转银行账号
     */
    @Override
    public List<NewCustomerBankAccount> selectNewCustomerBankAccountList(NewCustomerBankAccount newCustomerBankAccount)
    {
        return newCustomerBankAccountMapper.selectNewCustomerBankAccountList(newCustomerBankAccount);
    }

    /**
     * 新增新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    @Override
    public int insertNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount)
    {
        return newCustomerBankAccountMapper.insertNewCustomerBankAccount(newCustomerBankAccount);
    }

    /**
     * 修改新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    @Override
    public int updateNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount)
    {
        return newCustomerBankAccountMapper.updateNewCustomerBankAccount(newCustomerBankAccount);
    }

    /**
     * 批量删除新户流转银行账号
     * 
     * @param ids 需要删除的新户流转银行账号主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerBankAccountByIds(Long[] ids)
    {
        return newCustomerBankAccountMapper.deleteNewCustomerBankAccountByIds(ids);
    }

    /**
     * 删除新户流转银行账号信息
     * 
     * @param id 新户流转银行账号主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerBankAccountById(Long id)
    {
        return newCustomerBankAccountMapper.deleteNewCustomerBankAccountById(id);
    }

    @Override
    public List<NewCustomerBankAccount> selectByNewCustomerId(Long newCustomerId) {
        if (Objects.isNull(newCustomerId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerBankAccount>()
                .eq(NewCustomerBankAccount::getCustomerId, newCustomerId));
    }

    @Override
    public Integer countByNewCustomerId(Long newCustomerId) {
        if (Objects.isNull(newCustomerId)) {
            return 0;
        }
        return count(new LambdaQueryWrapper<NewCustomerBankAccount>()
                .eq(NewCustomerBankAccount::getCustomerId, newCustomerId));
    }

    @Override
    public Map<Long, List<NewCustomerBankAccount>> selectMapByNewCustomerIds(List<Long> newCustomerIds) {
        if (ObjectUtils.isEmpty(newCustomerIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerBankAccount>()
                .in(NewCustomerBankAccount::getCustomerId, newCustomerIds)).stream().collect(Collectors.groupingBy(NewCustomerBankAccount::getCustomerId));
    }

    @Override
    @Transactional
    public void deleteCustomerServiceBankAccount(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        NewCustomerBankAccount bankAccount = getById(id);
        if (Objects.isNull(bankAccount)) {
            throw new ServiceException("银行账号不存在");
        }
        removeById(id);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bankAccount.getCustomerId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("删除银行账号")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent("删除（" + bankAccount.getBankName() + bankAccount.getBankAccountNumber() + "）")
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void addNewCustomerTransferBankAccount(NewCustomerTransferBankAccountDTO vo, NewCustomerInfo newCustomerInfo) {
        if (checkBankAccountExists(newCustomerInfo.getBusinessTopDeptId(), vo.getBankAccountNumber(), null, newCustomerInfo.getCreditCode(), newCustomerInfo.getId())) {
            throw new ServiceException("该账号已存在");
        }
        NewCustomerBankAccount customerServiceBankAccount = new NewCustomerBankAccount()
                .setCustomerId(vo.getNewCustomerTransferId())
                .setBankName(vo.getBankName())
                .setBankAccountNumber(vo.getBankAccountNumber())
                .setPassword(vo.getPassword())
                .setDepositName(vo.getDepositName())
                .setPhoneNumber(vo.getPhoneNumber())
                .setAccountOpenDate(vo.getAccountOpenDate())
                .setAccountCloseDate(vo.getAccountCloseDate())
                .setReceiptAccountNumber(vo.getReceiptAccountNumber())
                .setReceiptStatus(vo.getReceiptStatus())
                .setBankDirect(vo.getBankDirect())
                .setRemarks(vo.getRemarks())
                .setIsPutOnTaxRecord(vo.getIsPutOnTaxRecord());
        save(customerServiceBankAccount);
        String operContent = getOperContent(null, vo, 1);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getNewCustomerTransferId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("新增银行信息")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent(operContent)
                    .setOperUserId(userId)
                    .setOperRemark(vo.getRemarks()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void modifyNewCustomerTransferAccount(NewCustomerTransferBankAccountDTO vo, NewCustomerInfo newCustomerInfo) {
        NewCustomerBankAccount oldBankAccount = getById(vo.getId());
        if (Objects.isNull(oldBankAccount)) {
            throw new ServiceException("该银行账号不存在");
        }
        if (checkBankAccountExists(newCustomerInfo.getBusinessTopDeptId(), vo.getBankAccountNumber(), vo.getId(), newCustomerInfo.getCreditCode(), newCustomerInfo.getId())) {
            throw new ServiceException("该账号已存在");
        }
        update(new LambdaUpdateWrapper<NewCustomerBankAccount>()
                .eq(NewCustomerBankAccount::getId, vo.getId())
                .set(NewCustomerBankAccount::getCustomerId, vo.getNewCustomerTransferId())
                .set(NewCustomerBankAccount::getBankName, vo.getBankName())
                .set(NewCustomerBankAccount::getBankAccountNumber, vo.getBankAccountNumber())
                .set(NewCustomerBankAccount::getPassword, vo.getPassword())
                .set(NewCustomerBankAccount::getPhoneNumber, vo.getPhoneNumber())
                .set(NewCustomerBankAccount::getAccountOpenDate, vo.getAccountOpenDate())
                .set(NewCustomerBankAccount::getAccountCloseDate, vo.getAccountCloseDate())
                .set(NewCustomerBankAccount::getReceiptAccountNumber, vo.getReceiptAccountNumber())
                .set(NewCustomerBankAccount::getReceiptStatus, vo.getReceiptStatus())
                .set(NewCustomerBankAccount::getBankDirect, vo.getBankDirect())
                .set(NewCustomerBankAccount::getDepositName, vo.getDepositName())
                .set(NewCustomerBankAccount::getRemarks, vo.getRemarks())
                .set(NewCustomerBankAccount::getIsPutOnTaxRecord, vo.getIsPutOnTaxRecord()));
        String operContent = getOperContent(oldBankAccount, vo, 2);
        if (!StringUtils.isEmpty(operContent)) {
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getNewCustomerTransferId())
                        .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑银行信息")
                        .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                        .setOperContent(operContent)
                        .setOperUserId(userId)
                        .setOperRemark(vo.getRemarks()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    public Boolean checkBankAccountExists(Long businessTopDeptId, String account, Long id, String creditCode, Long newCustomerTransferId) {
        return count(new LambdaQueryWrapper<NewCustomerBankAccount>().eq(NewCustomerBankAccount::getCustomerId, newCustomerTransferId)
                .eq(NewCustomerBankAccount::getBankAccountNumber, account)
                .ne(!Objects.isNull(id), NewCustomerBankAccount::getId, id)) > 0 ||
                baseMapper.getBusinessTopDeptBankAccountCount(businessTopDeptId, account, id, creditCode) > 0 ||
                customerServiceBankAccountMapper.getBusinessTopDeptBankAccountCount(businessTopDeptId, account, null, creditCode) > 0;

    }

    @Override
    public void deleteByNewCustomerId(Long newCustomerTransferId) {
        if (Objects.isNull(newCustomerTransferId)) {
            return;
        }
        remove(new LambdaQueryWrapper<NewCustomerBankAccount>()
                .eq(NewCustomerBankAccount::getCustomerId, newCustomerTransferId));
    }

    @Override
    @Transactional
    public void removeAndCreateBankAccount(Long newCustomerTransferId, List<NewCustomerTransferBankAccountDTO> bankList) {
        baseMapper.delete(new LambdaQueryWrapper<NewCustomerBankAccount>()
                .eq(NewCustomerBankAccount::getCustomerId, newCustomerTransferId));
        if (!ObjectUtils.isEmpty(bankList)) {
            saveBatch(bankList.stream().map(bank -> {
                NewCustomerBankAccount newCustomerBankAccount = new NewCustomerBankAccount();
                BeanUtils.copyProperties(bank, newCustomerBankAccount);
                newCustomerBankAccount.setCustomerId(newCustomerTransferId);
                newCustomerBankAccount.setId(null);
                newCustomerBankAccount.setCreateTime(null);
                newCustomerBankAccount.setUpdateTime(null);
                return newCustomerBankAccount;
            }).collect(Collectors.toList()));
        }
    }

    private String getOperContent(NewCustomerBankAccount oldBankAccount, NewCustomerTransferBankAccountDTO dto, Integer operType) {
        StringBuilder operContent = new StringBuilder();
        if (operType == 1) {
            operContent.append("新增银行信息，");
            operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : dto.getBankAccountNumber()));
            operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            operContent.append(String.format("%s为%s，", "开户时间", Objects.isNull(dto.getAccountOpenDate()) ? "空" : dto.getAccountOpenDate().toString()));
            operContent.append(String.format("%s为%s，", "销户时间", Objects.isNull(dto.getAccountCloseDate()) ? "空" : dto.getAccountCloseDate().toString()));
            operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            operContent.append(String.format("%s为%s，", "回单卡托管", Objects.isNull(dto.getReceiptStatus()) ? "空" : BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            operContent.append(String.format("%s为%s，", "企银直连", Objects.isNull(dto.getBankDirect()) ? "空" : BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            operContent.append(String.format("%s为%s，", "税局备案", Objects.isNull(dto.getIsPutOnTaxRecord()) ? "空" : dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            return operContent.substring(0, operContent.length() - 1);
        } else {
            if (!Objects.equals(oldBankAccount.getBankName(), dto.getBankName())) {
                operContent.append(String.format("%s为%s，", "银行名称", StringUtils.isEmpty(dto.getBankName()) ? "空" : dto.getBankName()));
            }
            if (!Objects.equals(oldBankAccount.getBankAccountNumber(), dto.getBankAccountNumber())) {
                operContent.append(String.format("%s为%s，", "银行账号", StringUtils.isEmpty(dto.getBankAccountNumber()) ? "空" : dto.getBankAccountNumber()));
            }
            if (!Objects.equals(oldBankAccount.getPassword(), dto.getPassword())) {
                operContent.append(String.format("%s为%s，", "银行密码", StringUtils.isEmpty(dto.getPassword()) ? "空" : dto.getPassword()));
            }
            if (!Objects.equals(oldBankAccount.getDepositName(), dto.getDepositName())) {
                operContent.append(String.format("%s为%s，", "开户行", StringUtils.isEmpty(dto.getDepositName()) ? "空" : dto.getDepositName()));
            }
            if (!Objects.equals(oldBankAccount.getPhoneNumber(), dto.getPhoneNumber())) {
                operContent.append(String.format("%s为%s，", "手机号", StringUtils.isEmpty(dto.getPhoneNumber()) ? "空" : dto.getPhoneNumber()));
            }
            if (!Objects.equals(oldBankAccount.getAccountOpenDate(), dto.getAccountOpenDate())) {
                operContent.append(String.format("%s为%s，", "开户时间", Objects.isNull(dto.getAccountOpenDate()) ? "空" : dto.getAccountOpenDate().toString()));
            }
            if (!Objects.equals(oldBankAccount.getAccountCloseDate(), dto.getAccountCloseDate())) {
                operContent.append(String.format("%s为%s，", "销户时间", Objects.isNull(dto.getAccountCloseDate()) ? "空" : dto.getAccountCloseDate().toString()));
            }
            if (!Objects.equals(oldBankAccount.getReceiptAccountNumber(), dto.getReceiptAccountNumber())) {
                operContent.append(String.format("%s为%s，", "回单卡账号", StringUtils.isEmpty(dto.getReceiptAccountNumber()) ? "空" : dto.getReceiptAccountNumber()));
            }
            if (!Objects.equals(oldBankAccount.getReceiptStatus(), dto.getReceiptStatus())) {
                operContent.append(String.format("%s为%s，", "回单卡托管", Objects.isNull(dto.getReceiptStatus()) ? "空" : BankReceiptStatus.getBankReceiptStatusByReceiptStatus(dto.getReceiptStatus()).getName()));
            }
            if (!Objects.equals(oldBankAccount.getBankDirect(), dto.getBankDirect())) {
                operContent.append(String.format("%s为%s，", "企银直连", Objects.isNull(dto.getBankDirect()) ? "空" : BankDirectStatus.getBankDirectStatusByBankDirect(dto.getBankDirect()).getName()));
            }
            if (!Objects.equals(oldBankAccount.getRemarks(), dto.getRemarks())) {
                operContent.append(String.format("%s为%s，", "备注说明", StringUtils.isEmpty(dto.getRemarks()) ? "空" : dto.getRemarks()));
            }
            if (!Objects.equals(oldBankAccount.getIsPutOnTaxRecord(), dto.getIsPutOnTaxRecord())) {
                operContent.append(String.format("%s为%s，", "税局备案", Objects.isNull(dto.getIsPutOnTaxRecord()) ? "空" : dto.getIsPutOnTaxRecord() == 0 ? "否" : "是"));
            }
            if (!StringUtils.isEmpty(operContent.toString())) {
                return "修改（" + oldBankAccount.getBankName() + oldBankAccount.getBankAccountNumber() + "）的" + operContent.substring(0, operContent.length() - 1);
            }
            return "";
        }
    }
}
