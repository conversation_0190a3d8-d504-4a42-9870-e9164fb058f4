package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 客户服务月度账期对象 c_customer_service_period_month
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
@ApiModel("客户服务月度账期对象")
@Accessors(chain = true)
@TableName("c_customer_service_period_month")
public class CustomerServicePeriodMonth extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 业务部门id */
    @Excel(name = "业务部门id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @Excel(name = "顶级业务部门id")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 顾问部门id */
    @Excel(name = "顾问部门id")
    @TableField("advisor_dept_id")
    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    /** 顾问顶级部门id */
    @Excel(name = "顾问顶级部门id")
    @TableField("advisor_top_dept_id")
    @ApiModelProperty(value = "顾问顶级部门id")
    private Long advisorTopDeptId;

    /** 会计部门id */
    @Excel(name = "会计部门id")
//    @TableField(value = "accounting_dept_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    /** 会计顶级部门id */
    @Excel(name = "会计顶级部门id")
    @TableField("accounting_top_dept_id")
    @ApiModelProperty(value = "会计顶级部门id")
    private Long accountingTopDeptId;

    @Excel(name = "服务状态", readConverterExp = "1=服务中,2=已结束,3=冻结中")
    @TableField("service_status")
    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;


    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @Excel(name = "纳税人性质，1-小规模，2-一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 服务编号 */
    @Excel(name = "服务编号")
    @TableField("service_number")
    @ApiModelProperty(value = "服务编号")
    private String serviceNumber;

    /** 服务类型，1-代账，2-补账 */
    @Excel(name = "服务类型，1-代账，2-补账")
    @TableField("service_type")
    @ApiModelProperty(value = "服务类型，1-代账，2-补账")
    private Integer serviceType;

    /** 创建源类型，空或1是自己模块常规创建、2是从补账创建 */
    @Excel(name = "创建源类型，空或1是自己模块常规创建、2是从补账创建")
    @TableField("add_from_type")
    @ApiModelProperty(value = "创建源类型，空或1是自己模块常规创建、2是从补账创建")
    private Integer addFromType;

    /** 创建源id */
    @Excel(name = "创建源id")
    @TableField("add_from_id")
    @ApiModelProperty(value = "创建源id")
    private Long addFromId;

    @TableField("accounting_status")
    @ApiModelProperty(value = "账务状态，1-正常，2-无需做账")
    private Integer accountingStatus;

    @TableField("settlement_status")
    @ApiModelProperty(value = "结算状态，1-不可结算，2-可结算，3-未结算，4-已结算")
    private Integer settlementStatus;

    @Excel(name = "预收状态，1-未预收，2-预收中，3-已预收")
    @TableField("prepay_status")
    @ApiModelProperty(value = "预收状态，1-未预收，2-预收中，3-已预收")
    private Integer prepayStatus;

    @Excel(name = "银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    @TableField("bank_payment_result")
    @ApiModelProperty(value = "银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    private Integer bankPaymentResult;

    @Excel(name = "入账交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提")
    @TableField("in_account_status")
    @ApiModelProperty(value = "入账交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提")
    private Integer inAccountStatus;

    @Excel(name = "结账状态，1未入账、2已入账未结账、3已入账已结账")
    @TableField("settle_account_status")
    @ApiModelProperty(value = "结账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountStatus;

    @Excel(name = "医保交付单状态，-2无需交付，-1待创建")
    @TableField("medical_deliver_status")
    @ApiModelProperty(value = "医保交付单状态，-2无需交付，-1待创建")
    private Integer medicalDeliverStatus;

    @Excel(name = "社保交付单状态，-2无需交付，-1待创建")
    @TableField("social_deliver_status")
    @ApiModelProperty(value = "社保交付单状态，-2无需交付，-1待创建")
    private Integer socialDeliverStatus;

    @Excel(name = "个税-工资薪金交付单状态，-2无需交付，-1待创建")
    @TableField("person_tax_deliver_status")
    @ApiModelProperty(value = "个税-工资薪金交付单状态，-2无需交付，-1待创建")
    private Integer personTaxDeliverStatus;

    @Excel(name = "个税-经营所得交付单状态，-2无需交付，-1待创建")
    @TableField("operation_tax_deliver_status")
    @ApiModelProperty(value = "个税-经营所得交付单状态，-2无需交付，-1待创建")
    private Integer operationTaxDeliverStatus;

    @Excel(name = "国税交付单状态，-2无需交付，-1待创建")
    @TableField("national_tax_deliver_status")
    @ApiModelProperty(value = "国税交付单状态，-2无需交付，-1待创建")
    private Integer nationalTaxDeliverStatus;

    @Excel(name = "预认证交付单状态，-2无需交付，-1待创建")
    @TableField("pre_auth_deliver_status")
    @ApiModelProperty(value = "预认证交付单状态，-2无需交付，-1待创建")
    private Integer preAuthDeliverStatus;

    @Excel(name = "年份")
    @TableField("year")
    @ApiModelProperty(value = "年份")
    private Integer year;

    @TableField(exist = false)
    private Boolean hasMedical;

    @TableField(exist = false)
    private Boolean hasSocial;

    @TableField(exist = false)
    @ApiModelProperty("是否有凭票入账标签")
    private Boolean hasTicket;

    @TableField(exist = false)
    @ApiModelProperty("无票收入")
    private BigDecimal noTicketIncome;
}
