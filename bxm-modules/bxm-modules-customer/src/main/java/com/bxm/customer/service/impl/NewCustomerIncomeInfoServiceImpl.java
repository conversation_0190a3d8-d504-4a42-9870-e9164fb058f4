package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.NewCustomerIncomeInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferMonthIncomeDTO;
import com.bxm.customer.mapper.NewCustomerIncomeInfoMapper;
import com.bxm.customer.service.INewCustomerIncomeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新户流转收入信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerIncomeInfoServiceImpl extends ServiceImpl<NewCustomerIncomeInfoMapper, NewCustomerIncomeInfo> implements INewCustomerIncomeInfoService
{
    @Autowired
    private NewCustomerIncomeInfoMapper newCustomerIncomeInfoMapper;

    /**
     * 查询新户流转收入信息
     * 
     * @param id 新户流转收入信息主键
     * @return 新户流转收入信息
     */
    @Override
    public NewCustomerIncomeInfo selectNewCustomerIncomeInfoById(Long id)
    {
        return newCustomerIncomeInfoMapper.selectNewCustomerIncomeInfoById(id);
    }

    /**
     * 查询新户流转收入信息列表
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 新户流转收入信息
     */
    @Override
    public List<NewCustomerIncomeInfo> selectNewCustomerIncomeInfoList(NewCustomerIncomeInfo newCustomerIncomeInfo)
    {
        return newCustomerIncomeInfoMapper.selectNewCustomerIncomeInfoList(newCustomerIncomeInfo);
    }

    /**
     * 新增新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo)
    {
        return newCustomerIncomeInfoMapper.insertNewCustomerIncomeInfo(newCustomerIncomeInfo);
    }

    /**
     * 修改新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo)
    {
        return newCustomerIncomeInfoMapper.updateNewCustomerIncomeInfo(newCustomerIncomeInfo);
    }

    /**
     * 批量删除新户流转收入信息
     * 
     * @param ids 需要删除的新户流转收入信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerIncomeInfoByIds(Long[] ids)
    {
        return newCustomerIncomeInfoMapper.deleteNewCustomerIncomeInfoByIds(ids);
    }

    /**
     * 删除新户流转收入信息信息
     * 
     * @param id 新户流转收入信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerIncomeInfoById(Long id)
    {
        return newCustomerIncomeInfoMapper.deleteNewCustomerIncomeInfoById(id);
    }

    @Override
    public List<NewCustomerIncomeInfo> selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerIncomeInfo>()
                .eq(NewCustomerIncomeInfo::getCustomerId, customerId));
    }

    @Override
    public Integer countByCustomerId(Long newCustomerId) {
        if (Objects.isNull(newCustomerId)) {
            return 0;
        }
        return count(new LambdaQueryWrapper<NewCustomerIncomeInfo>()
                .eq(NewCustomerIncomeInfo::getCustomerId, newCustomerId));
    }

    @Override
    @Transactional
    public void removeAndSaveNew(Long newCustomerId, List<NewCustomerTransferMonthIncomeDTO> incomeList) {
        remove(new LambdaQueryWrapper<NewCustomerIncomeInfo>().eq(NewCustomerIncomeInfo::getCustomerId, newCustomerId));
        if (!ObjectUtils.isEmpty(incomeList) && incomeList.stream().anyMatch(row -> !Objects.isNull(row.getAmount()))) {
            saveBatch(incomeList.stream().filter(row -> !Objects.isNull(row.getAmount())).map(item -> new NewCustomerIncomeInfo().setCustomerId(newCustomerId)
                    .setMonth(item.getYearMonth()).setAmount(item.getAmount())).collect(Collectors.toList()));
        }
    }

    @Override
    public Map<Long, List<NewCustomerIncomeInfo>> selectMapByCustomerIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerIncomeInfo>()
                .in(NewCustomerIncomeInfo::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.groupingBy(NewCustomerIncomeInfo::getCustomerId));
    }

    @Override
    @Transactional
    public void createDefaultIncomeList(NewCustomerInfo newCustomerInfo) {
        LocalDate startDateTemp = LocalDate.now().minusMonths(12);
        LocalDate registerDateTemp = DateUtils.strToLocalDate(newCustomerInfo.getRegistrationDate(), "yyyy-MM-dd");
        LocalDate startDate = LocalDate.of(startDateTemp.getYear(), startDateTemp.getMonth(), 1);
        LocalDate registerDate = LocalDate.of(registerDateTemp.getYear(), registerDateTemp.getMonth(), 1);
        List<Integer> yearMonthList = getThis12YearMonthList(!registerDate.isBefore(startDate) ? registerDate : startDate);
        saveBatch(yearMonthList.stream().map(yearMonth -> new NewCustomerIncomeInfo().setCustomerId(newCustomerInfo.getId())
                .setMonth(yearMonth).setAmount(BigDecimal.ZERO)).collect(Collectors.toList()));
    }

    private List<Integer> getThis12YearMonthList(LocalDate startDate) {
        List<Integer> yearMonthList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate endDate = LocalDate.of(now.getYear(), now.getMonthValue(), 1);
        while (!startDate.isAfter(endDate)) {
            yearMonthList.add(startDate.getYear() * 100 + startDate.getMonthValue());
            startDate = startDate.plusMonths(1);
        }
        return yearMonthList;
    }
}
