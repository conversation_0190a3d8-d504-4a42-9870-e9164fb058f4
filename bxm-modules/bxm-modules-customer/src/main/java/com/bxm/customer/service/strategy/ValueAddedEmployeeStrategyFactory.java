package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.enums.ValueAddedBizType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增值员工upsert策略工厂
 * 负责管理和提供不同业务类型的策略实例
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class ValueAddedEmployeeStrategyFactory {

    @Autowired
    private List<ValueAddedEmployeeUpsertStrategy> strategies;

    private final Map<Integer, ValueAddedEmployeeUpsertStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (ValueAddedEmployeeUpsertStrategy strategy : strategies) {
            Integer bizType = strategy.getSupportedBizType();
            if (bizType != null) {
                strategyMap.put(bizType, strategy);
                log.info("Registered upsert strategy for business type: {} -> {}", 
                        bizType, strategy.getClass().getSimpleName());
            }
        }
        log.info("Total {} upsert strategies registered", strategyMap.size());
    }

    /**
     * 根据业务类型获取对应的策略
     *
     * @param bizType 业务类型代码
     * @return 对应的策略实例
     * @throws IllegalArgumentException 当业务类型不支持时抛出
     */
    public ValueAddedEmployeeUpsertStrategy getStrategy(Integer bizType) {
        if (bizType == null) {
            throw new IllegalArgumentException("Business type cannot be null");
        }

        ValueAddedEmployeeUpsertStrategy strategy = strategyMap.get(bizType);
        if (strategy == null) {
            ValueAddedBizType bizTypeEnum = ValueAddedBizType.getByCode(bizType);
            String bizTypeName = bizTypeEnum != null ? bizTypeEnum.getName() : "Unknown";
            throw new IllegalArgumentException("Unsupported business type: " + bizType + " (" + bizTypeName + ")");
        }

        return strategy;
    }

    /**
     * 检查是否支持指定的业务类型
     *
     * @param bizType 业务类型代码
     * @return 是否支持
     */
    public boolean isSupported(Integer bizType) {
        return bizType != null && strategyMap.containsKey(bizType);
    }

    /**
     * 获取所有支持的业务类型
     *
     * @return 支持的业务类型代码集合
     */
    public java.util.Set<Integer> getSupportedBizTypes() {
        return strategyMap.keySet();
    }

    /**
     * 获取策略数量
     *
     * @return 已注册的策略数量
     */
    public int getStrategyCount() {
        return strategyMap.size();
    }
}
