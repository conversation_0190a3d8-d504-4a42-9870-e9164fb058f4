package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.customer.domain.vo.CommonReportVO;
import com.bxm.customer.domain.vo.xqy.XqyReportVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerCommonReport" + "${spring.profiles.active}", topic = "openapiCommonReport_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "common")
public class CommonReportListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到公共申报消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            CommonReportVO commonReportVO = JSONObject.parseObject(new String(message.getBody()), CommonReportVO.class);
            xqyService.commonReport(commonReportVO);
        } catch (Exception e) {
            log.error("处理鑫启易申报消息异常:{}", e.getMessage());
        }
    }
}
