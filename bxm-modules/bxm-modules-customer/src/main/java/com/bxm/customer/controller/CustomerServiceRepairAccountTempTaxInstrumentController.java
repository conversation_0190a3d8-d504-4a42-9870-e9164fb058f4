package com.bxm.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument;
import com.bxm.customer.service.ICustomerServiceRepairAccountTempTaxInstrumentService;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.page.TableDataInfo;

/**
 * 补账临时的 材料交接票据Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/repairAccountTempTaxInstrument")
@Api(tags = "补账临时的 材料交接票据")
public class CustomerServiceRepairAccountTempTaxInstrumentController extends BaseController
{
    @Autowired
    private ICustomerServiceRepairAccountTempTaxInstrumentService customerServiceRepairAccountTempTaxInstrumentService;

    /**
     * 查询补账临时的 材料交接票据列表
     */
    @RequiresPermissions("customer:instrument:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询补账临时的 材料交接票据列表", notes = "查询补账临时的 材料交接票据列表")
    public TableDataInfo list(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        startPage();
        List<CustomerServiceRepairAccountTempTaxInstrument> list = customerServiceRepairAccountTempTaxInstrumentService.selectCustomerServiceRepairAccountTempTaxInstrumentList(customerServiceRepairAccountTempTaxInstrument);
        return getDataTable(list);
    }

    /**
     * 导出补账临时的 材料交接票据列表
     */
    @RequiresPermissions("customer:instrument:export")
    @Log(title = "补账临时的 材料交接票据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出补账临时的 材料交接票据列表", notes = "导出补账临时的 材料交接票据列表")
    public void export(HttpServletResponse response, CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        List<CustomerServiceRepairAccountTempTaxInstrument> list = customerServiceRepairAccountTempTaxInstrumentService.selectCustomerServiceRepairAccountTempTaxInstrumentList(customerServiceRepairAccountTempTaxInstrument);
        ExcelUtil<CustomerServiceRepairAccountTempTaxInstrument> util = new ExcelUtil<CustomerServiceRepairAccountTempTaxInstrument>(CustomerServiceRepairAccountTempTaxInstrument.class);
        util.exportExcel(response, list, "补账临时的 材料交接票据数据");
    }

    /**
     * 获取补账临时的 材料交接票据详细信息
     */
    @RequiresPermissions("customer:instrument:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取补账临时的 材料交接票据详细信息", notes = "获取补账临时的 材料交接票据详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customerServiceRepairAccountTempTaxInstrumentService.selectCustomerServiceRepairAccountTempTaxInstrumentById(id));
    }

    /**
     * 新增补账临时的 材料交接票据
     */
    @RequiresPermissions("customer:instrument:add")
    @Log(title = "补账临时的 材料交接票据", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增补账临时的 材料交接票据", notes = "新增补账临时的 材料交接票据")
    public AjaxResult add(@RequestBody CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        return toAjax(customerServiceRepairAccountTempTaxInstrumentService.insertCustomerServiceRepairAccountTempTaxInstrument(customerServiceRepairAccountTempTaxInstrument));
    }

    /**
     * 修改补账临时的 材料交接票据
     */
    @RequiresPermissions("customer:instrument:edit")
    @Log(title = "补账临时的 材料交接票据", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改补账临时的 材料交接票据", notes = "修改补账临时的 材料交接票据")
    public AjaxResult edit(@RequestBody CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        return toAjax(customerServiceRepairAccountTempTaxInstrumentService.updateCustomerServiceRepairAccountTempTaxInstrument(customerServiceRepairAccountTempTaxInstrument));
    }

    /**
     * 删除补账临时的 材料交接票据
     */
    @RequiresPermissions("customer:instrument:remove")
    @Log(title = "补账临时的 材料交接票据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除补账临时的 材料交接票据", notes = "删除补账临时的 材料交接票据")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(customerServiceRepairAccountTempTaxInstrumentService.deleteCustomerServiceRepairAccountTempTaxInstrumentByIds(ids));
    }
}
