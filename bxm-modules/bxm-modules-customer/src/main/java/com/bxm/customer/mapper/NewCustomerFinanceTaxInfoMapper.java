package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerFinanceTaxInfo;

/**
 * 新户流转财税信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerFinanceTaxInfoMapper extends BaseMapper<NewCustomerFinanceTaxInfo>
{
    /**
     * 查询新户流转财税信息
     * 
     * @param id 新户流转财税信息主键
     * @return 新户流转财税信息
     */
    public NewCustomerFinanceTaxInfo selectNewCustomerFinanceTaxInfoById(Long id);

    /**
     * 查询新户流转财税信息列表
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 新户流转财税信息集合
     */
    public List<NewCustomerFinanceTaxInfo> selectNewCustomerFinanceTaxInfoList(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 新增新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    public int insertNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 修改新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    public int updateNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 删除新户流转财税信息
     * 
     * @param id 新户流转财税信息主键
     * @return 结果
     */
    public int deleteNewCustomerFinanceTaxInfoById(Long id);

    /**
     * 批量删除新户流转财税信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerFinanceTaxInfoByIds(Long[] ids);
}
