package com.bxm.customer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "special.employee")
@Getter
@Setter
public class SpecialEmployeeIdProperties
{
    private Long zdz;
}
