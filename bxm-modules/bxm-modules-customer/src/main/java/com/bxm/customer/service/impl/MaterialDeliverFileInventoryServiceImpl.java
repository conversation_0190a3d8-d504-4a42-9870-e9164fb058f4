package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodBankDTO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryV2DTO;
import com.bxm.customer.domain.vo.accoutingCashier.CustomerServiceFileNameVO;
import com.bxm.customer.mapper.*;
import com.bxm.customer.service.IMaterialDeliverFileInventoryFileService;
import com.bxm.customer.service.IMaterialDeliverFileInventoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 材料交接单文件清单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Service
public class MaterialDeliverFileInventoryServiceImpl extends ServiceImpl<MaterialDeliverFileInventoryMapper, MaterialDeliverFileInventory> implements IMaterialDeliverFileInventoryService
{
    @Autowired
    private MaterialDeliverFileInventoryMapper materialDeliverFileInventoryMapper;

    @Autowired
    private MaterialDeliverPeriodInventoryMapper materialDeliverPeriodInventoryMapper;

    @Autowired
    private CustomerServiceCashierAccountingFileMapper customerServiceCashierAccountingFileMapper;

    @Autowired
    private MaterialDeliverMapper materialDeliverMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IMaterialDeliverFileInventoryFileService materialDeliverFileInventoryFileService;

    /**
     * 查询材料交接单文件清单
     * 
     * @param id 材料交接单文件清单主键
     * @return 材料交接单文件清单
     */
    @Override
    public MaterialDeliverFileInventory selectMaterialDeliverFileInventoryById(Long id)
    {
        return materialDeliverFileInventoryMapper.selectMaterialDeliverFileInventoryById(id);
    }

    /**
     * 查询材料交接单文件清单列表
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 材料交接单文件清单
     */
    @Override
    public List<MaterialDeliverFileInventory> selectMaterialDeliverFileInventoryList(MaterialDeliverFileInventory materialDeliverFileInventory)
    {
        return materialDeliverFileInventoryMapper.selectMaterialDeliverFileInventoryList(materialDeliverFileInventory);
    }

    /**
     * 新增材料交接单文件清单
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    @Override
    public int insertMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory)
    {
        materialDeliverFileInventory.setCreateTime(DateUtils.getNowDate());
        return materialDeliverFileInventoryMapper.insertMaterialDeliverFileInventory(materialDeliverFileInventory);
    }

    /**
     * 修改材料交接单文件清单
     * 
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    @Override
    public int updateMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory)
    {
        materialDeliverFileInventory.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverFileInventoryMapper.updateMaterialDeliverFileInventory(materialDeliverFileInventory);
    }

    /**
     * 批量删除材料交接单文件清单
     * 
     * @param ids 需要删除的材料交接单文件清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileInventoryByIds(Long[] ids)
    {
        return materialDeliverFileInventoryMapper.deleteMaterialDeliverFileInventoryByIds(ids);
    }

    /**
     * 删除材料交接单文件清单信息
     * 
     * @param id 材料交接单文件清单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverFileInventoryById(Long id)
    {
        return materialDeliverFileInventoryMapper.deleteMaterialDeliverFileInventoryById(id);
    }

    /**
     * 根据 materialDeliverId 查询 fileInventories 并生成账期清单
     * @param materialDeliverId 材料交接单 ID
     */
    @Override
    @Transactional
    public void generatePeriodInventoryFromFileInventory(Long materialDeliverId) {
        // 根据 materialDeliverId 查询文件清单
        List<MaterialDeliverFileInventory> fileInventories = baseMapper.selectList(
                new QueryWrapper<MaterialDeliverFileInventory>().eq("material_deliver_id", materialDeliverId).eq("is_del", 0)
        );

        if (fileInventories.isEmpty()) {
            throw new ServiceException("未找到对应的文件清单记录");
        }

        // 遍历文件清单，生成账期清单
        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            generatePeriodInventory(fileInventory);
        }
    }

    /**
     * 更新文件清单表并同步账期清单
     * @param fileInventoryIds 文件清单记录的 ID 列表
     */
    @Override
    @Transactional
    public void updateFileInventoryAndSyncPeriod(List<Long> fileInventoryIds, MaterialDeliverFileInventory updateData) {
        if (fileInventoryIds == null || fileInventoryIds.isEmpty()) {
            throw new IllegalArgumentException("文件清单记录 ID 列表不能为空");
        }

        // 查询所有指定的文件清单
        List<MaterialDeliverFileInventory> fileInventories = baseMapper.selectBatchIds(fileInventoryIds);

        if (fileInventories.isEmpty()) {
            throw new RuntimeException("未找到对应的文件清单记录");
        }

        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            // 更新文件清单表
            fileInventory.setCustomerName(updateData.getCustomerName());
            fileInventory.setBankName(updateData.getBankName());
            fileInventory.setBankAccountNumber(updateData.getBankAccountNumber());
            fileInventory.setStartDate(updateData.getStartDate());
            fileInventory.setEndDate(updateData.getEndDate());
            baseMapper.updateById(fileInventory);

            // 删除旧的账期清单
            deletePeriodInventoryByCustomerName(fileInventory.getCustomerName());

            // 重新生成账期清单
            generatePeriodInventory(fileInventory);
        }
    }

    /**
     * 删除文件清单记录和其关联的账期清单
     * @param fileInventoryIds 文件清单记录的 ID 列表
     */
    @Override
    @Transactional
    public void deleteFileInventoryAndSyncPeriod(List<Long> fileInventoryIds) {
        if (fileInventoryIds == null || fileInventoryIds.isEmpty()) {
            throw new IllegalArgumentException("文件清单记录 ID 列表不能为空");
        }

        // 查询所有指定的文件清单
        List<MaterialDeliverFileInventory> fileInventories = baseMapper.selectBatchIds(fileInventoryIds);

        if (fileInventories.isEmpty()) {
            throw new RuntimeException("未找到对应的文件清单记录");
        }

        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            // 删除文件清单记录
            logicDeleteById(fileInventory.getId());

            // 删除关联的账期清单
            deletePeriodInventoryByCustomerName(fileInventory.getCustomerName());
        }
    }

    @Override
    public void logicDeleteById(Long id) {
        updateById(new MaterialDeliverFileInventory().setId(id).setIsDel(true));
    }

    @Override
    public IPage<MaterialFileInventoryDTO> materialFileInventoryPageList(MaterialDeliver materialDeliver, Integer pageNum, Integer pageSize) {
        IPage<MaterialFileInventoryDTO> result = new Page<>(pageNum, pageSize);
        IPage<MaterialDeliverFileInventory> iPage = page(new Page<>(pageNum, pageSize), buildMaterialDeliverFileInventoryQueryWrapper(materialDeliver, null, null, null));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<CustomerServiceFileNameVO> customerServiceFileName = iPage.getRecords().stream().filter(row -> !Objects.isNull(row.getCustomerServiceId()) && !StringUtils.isEmpty(row.getFileName()))
                    .map(row -> CustomerServiceFileNameVO.builder().customerServiceId(row.getCustomerServiceId()).fileName(row.getFileName()).build()).collect(Collectors.toList());
            List<CustomerServiceCashierAccountingFile> existsCustomerServiceCashierAccountingFileList = ObjectUtils.isEmpty(customerServiceFileName) || Objects.equals(materialDeliver.getPushStatus(), MaterialDeliverPushStatus.PUSHED.getCode()) ? Lists.newArrayList() :
                    customerServiceCashierAccountingFileMapper.selectByBatchCustomerServiceIdAndFileName(customerServiceFileName);
            Map<String, List<CustomerServiceCashierAccountingFile>> existsCustomerServiceCashierAccountingFileMap = existsCustomerServiceCashierAccountingFileList.stream()
                    .collect(Collectors.groupingBy(row -> row.getCustomerServiceId() + "_" + row.getFileName()));
            List<Long> customerServiceIds = iPage.getRecords().stream().map(MaterialDeliverFileInventory::getCustomerServiceId)
                    .filter(customerServiceId -> !Objects.isNull(customerServiceId)).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, row -> row));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                MaterialFileInventoryDTO dto = MaterialFileInventoryDTO.builder()
                        .id(row.getId())
                        .fileName(row.getFileName())
                        .fileUrl(row.getFileUrl())
                        .fileSize(row.getFileSize())
                        .analysisResult(row.getAnalysisResult())
                        .analysisResultStr(Objects.isNull(row.getAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(row.getAnalysisResult()).getName())
                        .pushResult(row.getPushResult())
                        .pushResultStr(Objects.isNull(row.getPushResult()) ? "-" : MaterialDeliverPushResult.getByCode(row.getPushResult()).getName())
                        .errorMsg(row.getErrorMsg())
                        .customerName(row.getCustomerName())
                        .bankName(row.getBankName())
                        .bankAccountNumber(row.getBankAccountNumber())
                        .bankInfo((StringUtils.isEmpty(row.getBankName()) ? "" : row.getBankName()) + (StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankAccountNumber() + "）")))
                        .remark(row.getRemark())
                        .startDate(Objects.isNull(row.getStartDate()) ? "" : DateUtils.localDateToStr(row.getStartDate(), "yyyy-MM-dd"))
                        .endDate(Objects.isNull(row.getEndDate()) ? "" : DateUtils.localDateToStr(row.getEndDate(), "yyyy-MM-dd"))
                        .customerServiceId(row.getCustomerServiceId())
                        .build();
                if (Objects.equals(materialDeliver.getPushStatus(), MaterialDeliverPushStatus.PUSHED.getCode()) || Objects.isNull(row.getCustomerServiceId()) || StringUtils.isEmpty(row.getFileName())) {
                    dto.setIsRepeat(null);
                } else {
                    dto.setIsRepeat(existsCustomerServiceCashierAccountingFileMap.containsKey(row.getCustomerServiceId() + "_" + row.getFileName()) ? 1 : null);
                }
                if (!Objects.isNull(row.getCustomerServiceId())) {
                    CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                    if (!Objects.isNull(customerService)) {
                        dto.setCustomerServiceName(customerService.getCustomerName());
                        dto.setIsCustomerNameNotSame(!Objects.equals(dto.getCustomerServiceName(), dto.getCustomerName()) ? 1 : null);
                    }
                }
                return dto;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public IPage<MaterialFileInventoryV2DTO> materialFileInventoryPageListV2(MaterialDeliver materialDeliver, Integer pageNum, Integer pageSize) {
        IPage<MaterialFileInventoryV2DTO> result = new Page<>(pageNum, pageSize);
        IPage<MaterialDeliverFileInventory> iPage = page(new Page<>(pageNum, pageSize), buildMaterialDeliverFileInventoryQueryWrapperV2(materialDeliver));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<Long> customerServiceIds = iPage.getRecords().stream().map(MaterialDeliverFileInventory::getCustomerServiceId)
                    .filter(customerServiceId -> !Objects.isNull(customerServiceId)).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, row -> row));
            Map<Long, List<MaterialDeliverFileInventoryFile>> fileMap = materialDeliverFileInventoryFileService.selectByBatchFileInventoryIdAndFileType(iPage.getRecords().stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()), null)
                    .stream().collect(Collectors.groupingBy(MaterialDeliverFileInventoryFile::getFileInventoryId));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                MaterialFileInventoryV2DTO dto = MaterialFileInventoryV2DTO.builder()
                        .id(row.getId())
                        .analysisResult(row.getAnalysisResult())
                        .analysisResultStr(Objects.isNull(row.getAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(row.getAnalysisResult()).getName())
                        .pushResult(row.getPushResult())
                        .pushResultStr(Objects.isNull(row.getPushResult()) ? "-" : MaterialDeliverPushResult.getByCode(row.getPushResult()).getName())
                        .errorMsg(row.getErrorMsg())
                        .customerName(row.getCustomerName())
                        .bankName(row.getBankName())
                        .bankAccountNumber(row.getBankAccountNumber())
                        .bankInfo((StringUtils.isEmpty(row.getBankName()) ? "" : row.getBankName()) + (StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankAccountNumber() + "）")))
                        .startDate(Objects.isNull(row.getStartDate()) ? "" : DateUtils.localDateToStr(row.getStartDate(), "yyyyMM"))
                        .endDate(Objects.isNull(row.getEndDate()) ? "" : DateUtils.localDateToStr(row.getEndDate(), "yyyyMM"))
                        .period(row.getPeriod())
                        .customerServiceId(row.getCustomerServiceId())
                        .build();
                List<MaterialDeliverFileInventoryFile> files = fileMap.get(row.getId());
                dto.setCheckFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().filter(f -> Objects.equals(f.getFileType(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode()))
                        .map(f -> CommonFileVO.builder().fileUrl(f.getFileUrl()).fileName(f.getFileName()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
                dto.setReceiptFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().filter(f -> Objects.equals(f.getFileType(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode()))
                        .map(f -> CommonFileVO.builder().fileUrl(f.getFileUrl()).fileName(f.getFileName()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
                dto.setCheckFileCount((long) dto.getCheckFiles().size());
                dto.setReceiptFileCount((long) dto.getReceiptFiles().size());
                if (!Objects.isNull(row.getCustomerServiceId())) {
                    CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                    if (!Objects.isNull(customerService)) {
                        dto.setCustomerServiceName(customerService.getCustomerName());
                        dto.setIsCustomerNameNotSame(!Objects.equals(dto.getCustomerServiceName(), dto.getCustomerName()) ? 1 : null);
                    }
                }
                return dto;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<CommonFileVO> materialFileInventoryList(MaterialDeliver materialDeliver) {
        List<MaterialDeliverFileInventory> fileInventories = list(buildMaterialDeliverFileInventoryQueryWrapper(materialDeliver, null, null, null));
        return ObjectUtils.isEmpty(fileInventories) ? Lists.newArrayList() :
                fileInventories.stream().filter(row -> !StringUtils.isEmpty(row.getFileUrl())).map(row ->
                        CommonFileVO.builder()
                                .fileSize(row.getFileSize())
                                .fileUrl(row.getFileUrl())
                                .fileName(row.getFileName())
                                .build()).collect(Collectors.toList());
    }

    @Override
    public void deleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds) {
        if (!ObjectUtils.isEmpty(materialDeliverIds)) {
            return;
        }
        remove(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .in(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverIds));
    }

    @Override
    @Transactional
    public void logicDeleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds) {
        if (!ObjectUtils.isEmpty(materialDeliverIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<MaterialDeliverFileInventory>().in(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverIds)
                .set(MaterialDeliverFileInventory::getIsDel, true));
    }

    @Override
    public void updateMaterialDeliverFileInventoryStatus(List<Long> ids) {
        List<MaterialDeliverFileInventory> fileInventories = list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false).in(MaterialDeliverFileInventory::getId, ids));
        if (ObjectUtils.isEmpty(fileInventories)) {
            return;
        }
        Long materialDeliverId = fileInventories.get(0).getMaterialDeliverId();
        MaterialDeliver materialDeliver = materialDeliverMapper.selectById(materialDeliverId);
        Integer materialDeliverType = materialDeliver.getMaterialDeliverType();
        List<String> bankAccountNumbers = fileInventories.stream().map(MaterialDeliverFileInventory::getBankAccountNumber).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<RemoteCustomerPeriodBankDTO> customerServiceBankAccounts = Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode()) && !ObjectUtils.isEmpty(bankAccountNumbers) ?
                customerServiceMapper.getCustomerPeriodBankList(bankAccountNumbers) : Lists.newArrayList();
        Map<String, List<RemoteCustomerPeriodBankDTO>> bankMap = customerServiceBankAccounts.stream().collect(Collectors.groupingBy(RemoteCustomerPeriodBankDTO::getBankAccountNumber));
        List<Long> customerServiceIds = fileInventories.stream().map(MaterialDeliverFileInventory::getCustomerServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = !Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode()) && !ObjectUtils.isEmpty(customerServiceIds) ?
                customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)) : Lists.newArrayList();
        Map<Long, List<CustomerServicePeriodMonth>> periodMap = customerServicePeriodMonths.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId));
        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
                checkBankFlowData(fileInventory, bankMap);
            } else {
                checkInAccountData(fileInventory, periodMap);
            }
        }
        Integer materialDeliverAnalysisResult;
        if (count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverId)
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
        } else {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
        }
        if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
            materialDeliverMapper.updateById(new MaterialDeliver().setId(materialDeliverId).setAnalysisResult(materialDeliverAnalysisResult));
        }
    }

    @Override
    public void updateMaterialDeliverFileInventoryStatusV2(List<Long> ids) {
        List<MaterialDeliverFileInventory> fileInventories = list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false).in(MaterialDeliverFileInventory::getId, ids));
        if (ObjectUtils.isEmpty(fileInventories)) {
            return;
        }
        Long materialDeliverId = fileInventories.get(0).getMaterialDeliverId();
        MaterialDeliver materialDeliver = materialDeliverMapper.selectById(materialDeliverId);
        Integer materialDeliverType = materialDeliver.getMaterialDeliverType();
        List<String> bankAccountNumbers = fileInventories.stream().map(MaterialDeliverFileInventory::getBankAccountNumber).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<RemoteCustomerPeriodBankDTO> customerServiceBankAccounts = Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode()) && !ObjectUtils.isEmpty(bankAccountNumbers) ?
                customerServiceMapper.getCustomerPeriodBankList(bankAccountNumbers) : Lists.newArrayList();
        Map<String, List<RemoteCustomerPeriodBankDTO>> bankMap = customerServiceBankAccounts.stream().collect(Collectors.groupingBy(RemoteCustomerPeriodBankDTO::getBankAccountNumber));
        List<Long> customerServiceIds = fileInventories.stream().map(MaterialDeliverFileInventory::getCustomerServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CustomerServicePeriodMonth> customerServicePeriodMonths = !Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode()) && !ObjectUtils.isEmpty(customerServiceIds) ?
                customerServicePeriodMonthMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)) : Lists.newArrayList();
        Map<Long, List<CustomerServicePeriodMonth>> periodMap = customerServicePeriodMonths.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonth::getCustomerServiceId));
        for (MaterialDeliverFileInventory fileInventory : fileInventories) {
            if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
                checkBankFlowDataV2(fileInventory, bankMap);
            } else {
                checkInAccountDataV2(fileInventory, periodMap);
            }
        }
        Integer materialDeliverAnalysisResult;
        if (count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverId)
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
        } else {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
        }
        if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
            materialDeliverMapper.updateById(new MaterialDeliver().setId(materialDeliverId).setAnalysisResult(materialDeliverAnalysisResult));
        }
    }

    private void checkInAccountDataV2(MaterialDeliverFileInventory fileInventory, Map<Long, List<CustomerServicePeriodMonth>> periodMap) {
        MaterialDeliverFileInventory fileInventoryUpdate = new MaterialDeliverFileInventory();
        String errorMsg = "";
        if (Objects.isNull(fileInventory.getCustomerServiceId())) {
            errorMsg = addErrorMsg(errorMsg, "客户不存在");
        } else {
            List<CustomerServicePeriodMonth> periodMonthList = periodMap.get(fileInventory.getCustomerServiceId());
            if (ObjectUtils.isEmpty(periodMonthList) || periodMonthList.stream().noneMatch(periodDTO -> Objects.equals(periodDTO.getPeriod().toString(), fileInventory.getPeriod()))) {
                errorMsg = addErrorMsg(errorMsg, "账期不存在");
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            fileInventoryUpdate.setErrorMsg("");
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.NORMAL.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        } else {
            fileInventoryUpdate.setErrorMsg(errorMsg);
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        }
    }

    @Override
    @Transactional
    public void updateMaterialDeliverFileInventoryStatus(MaterialDeliverFileInventory materialDeliverFileInventory, Map<Long, MaterialDeliver> materialDeliverMap, String batchNo) {
        Long materialDeliverId = materialDeliverFileInventory.getMaterialDeliverId();
        MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverId);
        Integer materialDeliverType = materialDeliver.getMaterialDeliverType();
        if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
            checkBankFlowData(materialDeliverFileInventory, batchNo);
        } else {
            checkInAccountData(materialDeliverFileInventory, batchNo);
        }
    }

    @Override
    @Transactional
    public void updateMaterialDeliverFileInventoryStatusV2(MaterialDeliverFileInventory materialDeliverFileInventory, Map<Long, MaterialDeliver> materialDeliverMap, String batchNo) {
        Long materialDeliverId = materialDeliverFileInventory.getMaterialDeliverId();
        MaterialDeliver materialDeliver = materialDeliverMap.get(materialDeliverId);
        Integer materialDeliverType = materialDeliver.getMaterialDeliverType();
        if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
            checkBankFlowDataV2(materialDeliverFileInventory, batchNo);
        } else {
            checkInAccountDataV2(materialDeliverFileInventory, batchNo);
        }
    }

    private void checkInAccountDataV2(MaterialDeliverFileInventory fileInventory, String batchNo) {
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        String errorMsg = "";
        if (!StringUtils.isEmpty(fileInventory.getCreditCode()) && !Objects.equals("-", fileInventory.getCreditCode())) {
            List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getCreditCode, fileInventory.getCreditCode()).eq(CCustomerService::getIsDel, false));
            if (ObjectUtils.isEmpty(customerServiceList)) {
                errorMsg = addErrorMsg(errorMsg, "客户不存在");
            } else {
                Long customerServiceId;
                if (customerServiceList.size() == 1) {
                    customerServiceId = customerServiceList.get(0).getId();
                } else {
                    CCustomerService customerService = customerServiceList.stream().filter(row -> Objects.equals(row.getServiceStatus(), ServiceStatus.SERVICE.getCode())).findFirst().orElse(null);
                    if (Objects.isNull(customerService)) {
                        customerServiceId = customerServiceList.get(0).getId();
                    } else {
                        customerServiceId = customerService.getId();
                    }
                }
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerServiceId, customerServiceId);
                CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthMapper.selectOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId).eq(CustomerServicePeriodMonth::getPeriod, fileInventory.getPeriod()).last("limit 1"));
                if (Objects.isNull(periodMonth)) {
                    errorMsg = addErrorMsg(errorMsg, "账期不存在");
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.NORMAL.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, "");
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        } else {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, errorMsg);
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        }
    }

    private void checkBankFlowDataV2(MaterialDeliverFileInventory fileInventory, String batchNo) {
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        String errorMsg = "";
        if (!StringUtils.isEmpty(fileInventory.getBankAccountNumber()) && !Objects.equals("-", fileInventory.getBankAccountNumber())) {
            CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                    .eq(CustomerServiceBankAccount::getBankAccountNumber, fileInventory.getBankAccountNumber()).last("limit 1"));
            if (Objects.isNull(customerServiceBankAccount)) {
                errorMsg = addErrorMsg(errorMsg, "银行不存在");
            } else {
                updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, customerServiceBankAccount.getBankAccountNumber());
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerServiceId, customerServiceBankAccount.getCustomerServiceId());
                if (!Objects.isNull(fileInventory.getStartDate())) {
                    Integer startPeriod = Integer.parseInt(fileInventory.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    Integer endPeriod = Objects.isNull(fileInventory.getEndDate()) ? startPeriod : Integer.parseInt(fileInventory.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    LocalDate start = LocalDate.parse(startPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    LocalDate end = LocalDate.parse(endPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    List<RemoteCustomerPeriodBankDTO> customerPeriodBankList = customerServiceMapper.getCustomerPeriodBankList(Collections.singletonList(fileInventory.getBankAccountNumber()));
                    while (!start.isAfter(end)) {
                        LocalDate thisPeriod = start;
                        if (customerPeriodBankList.stream().noneMatch(row -> Objects.equals(row.getPeriod(), Integer.parseInt(thisPeriod.format(DateTimeFormatter.ofPattern("yyyyMM")))))) {
                            errorMsg = addErrorMsg(errorMsg, "账期不存在");
                            break;
                        }
                        start = start.plusMonths(1);
                    }
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.NORMAL.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, "");
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        } else {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, errorMsg);
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        }
    }

    @Override
    public List<MaterialDeliverFileInventory> selectCanNotPushFileInventory(List<Long> materialDeliverIds) {
        if (ObjectUtils.isEmpty(materialDeliverIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectCanNotPushFileInventory(materialDeliverIds);
    }

    private void checkInAccountData(MaterialDeliverFileInventory fileInventory, String batchNo) {
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        String errorMsg = "";
        if (StringUtils.isEmpty(fileInventory.getFileName()) || Objects.equals("-", fileInventory.getFileName())
                || StringUtils.isEmpty(fileInventory.getCustomerName()) || Objects.equals("-", fileInventory.getCustomerName())
                || StringUtils.isEmpty(fileInventory.getCreditCode()) || Objects.equals("-", fileInventory.getCreditCode())
                || Objects.isNull(fileInventory.getPeriod())) {
            errorMsg = addErrorMsg(errorMsg, "数据不完整");
        }
        if (!StringUtils.isEmpty(fileInventory.getCreditCode()) && !Objects.equals("-", fileInventory.getCreditCode())) {
            List<CCustomerService> customerServiceList = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                    .eq(CCustomerService::getCreditCode, fileInventory.getCreditCode()).eq(CCustomerService::getIsDel, false));
            if (ObjectUtils.isEmpty(customerServiceList)) {
                errorMsg = addErrorMsg(errorMsg, "客户不存在");
            } else {
                Long customerServiceId;
                if (customerServiceList.size() == 1) {
                    customerServiceId = customerServiceList.get(0).getId();
                } else {
                    CCustomerService customerService = customerServiceList.stream().filter(row -> Objects.equals(row.getServiceStatus(), ServiceStatus.SERVICE.getCode())).findFirst().orElse(null);
                    if (Objects.isNull(customerService)) {
                        customerServiceId = customerServiceList.get(0).getId();
                    } else {
                        customerServiceId = customerService.getId();
                    }
                }
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerServiceId, customerServiceId);
                CustomerServicePeriodMonth periodMonth = customerServicePeriodMonthMapper.selectOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId).eq(CustomerServicePeriodMonth::getPeriod, fileInventory.getPeriod()).last("limit 1"));
                if (Objects.isNull(periodMonth)) {
                    errorMsg = addErrorMsg(errorMsg, "账期不存在");
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.NORMAL.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, "");
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        } else {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, errorMsg);
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        }
    }

    private void checkBankFlowData(MaterialDeliverFileInventory fileInventory, String batchNo) {
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        String errorMsg = "";
        if (StringUtils.isEmpty(fileInventory.getFileName()) || Objects.equals("-", fileInventory.getFileName())
                || StringUtils.isEmpty(fileInventory.getCustomerName())
                || StringUtils.isEmpty(fileInventory.getBankAccountNumber()) || Objects.equals("-", fileInventory.getBankAccountNumber())
                || Objects.isNull(fileInventory.getStartDate())) {
            errorMsg = addErrorMsg(errorMsg, "数据不完整");
        }
        if (!StringUtils.isEmpty(fileInventory.getBankAccountNumber()) && !Objects.equals("-", fileInventory.getBankAccountNumber())) {
            CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                    .eq(CustomerServiceBankAccount::getBankAccountNumber, fileInventory.getBankAccountNumber()).last("limit 1"));
            if (Objects.isNull(customerServiceBankAccount)) {
                errorMsg = addErrorMsg(errorMsg, "银行不存在");
            } else {
                updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, customerServiceBankAccount.getBankAccountNumber());
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerServiceId, customerServiceBankAccount.getCustomerServiceId());
                if (!Objects.isNull(fileInventory.getStartDate())) {
                    Integer startPeriod = Integer.parseInt(fileInventory.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    Integer endPeriod = Objects.isNull(fileInventory.getEndDate()) ? startPeriod : Integer.parseInt(fileInventory.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    LocalDate start = LocalDate.parse(startPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    LocalDate end = LocalDate.parse(endPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    List<RemoteCustomerPeriodBankDTO> customerPeriodBankList = customerServiceMapper.getCustomerPeriodBankList(Collections.singletonList(fileInventory.getBankAccountNumber()));
                    while (!start.isAfter(end)) {
                        LocalDate thisPeriod = start;
                        if (customerPeriodBankList.stream().noneMatch(row -> Objects.equals(row.getPeriod(), Integer.parseInt(thisPeriod.format(DateTimeFormatter.ofPattern("yyyyMM")))))) {
                            errorMsg = addErrorMsg(errorMsg, "账期不存在");
                            break;
                        }
                        start = start.plusMonths(1);
                    }
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.NORMAL.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, "");
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        } else {
            updateWrapper.set(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            updateWrapper.set(MaterialDeliverFileInventory::getErrorMsg, errorMsg);
            updateWrapper.eq(MaterialDeliverFileInventory::getId, fileInventory.getId());
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
            update(updateWrapper);
        }
    }

    private void checkInAccountData(MaterialDeliverFileInventory fileInventory, Map<Long, List<CustomerServicePeriodMonth>> periodMap) {
        MaterialDeliverFileInventory fileInventoryUpdate = new MaterialDeliverFileInventory();
        String errorMsg = "";
        if (StringUtils.isEmpty(fileInventory.getFileName()) || Objects.equals("-", fileInventory.getFileName())
                || StringUtils.isEmpty(fileInventory.getCustomerName()) || Objects.equals("-", fileInventory.getCustomerName())
                || StringUtils.isEmpty(fileInventory.getCreditCode()) || Objects.equals("-", fileInventory.getCreditCode())
                || Objects.isNull(fileInventory.getPeriod())) {
            errorMsg = addErrorMsg(errorMsg, "数据不完整");
        }
        if (Objects.isNull(fileInventory.getCustomerServiceId())) {
            errorMsg = addErrorMsg(errorMsg, "客户不存在");
        } else {
            List<CustomerServicePeriodMonth> periodMonthList = periodMap.get(fileInventory.getCustomerServiceId());
            if (ObjectUtils.isEmpty(periodMonthList) || periodMonthList.stream().noneMatch(periodDTO -> Objects.equals(periodDTO.getPeriod().toString(), fileInventory.getPeriod()))) {
                errorMsg = addErrorMsg(errorMsg, "账期不存在");
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            fileInventoryUpdate.setErrorMsg("");
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.NORMAL.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        } else {
            fileInventoryUpdate.setErrorMsg(errorMsg);
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        }
    }

    private String addErrorMsg(String origin, String errorMsg) {
        if (StringUtils.isEmpty(origin)) {
            return errorMsg;
        }
        return origin + "; " + errorMsg;
    }

    private void checkBankFlowData(MaterialDeliverFileInventory fileInventory, Map<String, List<RemoteCustomerPeriodBankDTO>> bankMap) {
        MaterialDeliverFileInventory fileInventoryUpdate = new MaterialDeliverFileInventory();
        String errorMsg = "";
        if (StringUtils.isEmpty(fileInventory.getFileName()) || Objects.equals("-", fileInventory.getFileName())
                || StringUtils.isEmpty(fileInventory.getCustomerName())
                || StringUtils.isEmpty(fileInventory.getBankAccountNumber()) || Objects.equals("-", fileInventory.getBankAccountNumber())
                || Objects.isNull(fileInventory.getStartDate())) {
            errorMsg = addErrorMsg(errorMsg, "数据不完整");
        }
        if (!StringUtils.isEmpty(fileInventory.getBankAccountNumber())) {
            List<RemoteCustomerPeriodBankDTO> periodBankList = bankMap.get(fileInventory.getBankAccountNumber());
            if (ObjectUtils.isEmpty(periodBankList)) {
                errorMsg = addErrorMsg(errorMsg, "银行不存在");
            } else {
                if (!Objects.isNull(fileInventory.getStartDate())) {
                    Integer startPeriod = Integer.parseInt(fileInventory.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    Integer endPeriod = Objects.isNull(fileInventory.getEndDate()) ? startPeriod : Integer.parseInt(fileInventory.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                    LocalDate start = LocalDate.parse(startPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    LocalDate end = LocalDate.parse(endPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                    while (!start.isAfter(end)) {
                        LocalDate thisPeriod = start;
                        if (periodBankList.stream().noneMatch(row -> Objects.equals(row.getPeriod(), Integer.parseInt(thisPeriod.format(DateTimeFormatter.ofPattern("yyyyMM")))))) {
                            errorMsg = addErrorMsg(errorMsg, "账期不存在");
                            break;
                        }
                        start = start.plusMonths(1);
                    }
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            fileInventoryUpdate.setErrorMsg("");
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.NORMAL.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        } else {
            fileInventoryUpdate.setErrorMsg(errorMsg);
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        }
    }

    private void checkBankFlowDataV2(MaterialDeliverFileInventory fileInventory, Map<String, List<RemoteCustomerPeriodBankDTO>> bankMap) {
        MaterialDeliverFileInventory fileInventoryUpdate = new MaterialDeliverFileInventory();
        String errorMsg = "";
        List<RemoteCustomerPeriodBankDTO> periodBankList = bankMap.get(fileInventory.getBankAccountNumber());
        if (ObjectUtils.isEmpty(periodBankList)) {
            errorMsg = addErrorMsg(errorMsg, "银行不存在");
        } else {
            if (!Objects.isNull(fileInventory.getStartDate())) {
                Integer startPeriod = Integer.parseInt(fileInventory.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                Integer endPeriod = Objects.isNull(fileInventory.getEndDate()) ? startPeriod : Integer.parseInt(fileInventory.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")).substring(0, 6));
                LocalDate start = LocalDate.parse(startPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                LocalDate end = LocalDate.parse(endPeriod + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
                while (!start.isAfter(end)) {
                    LocalDate thisPeriod = start;
                    if (periodBankList.stream().noneMatch(row -> Objects.equals(row.getPeriod(), Integer.parseInt(thisPeriod.format(DateTimeFormatter.ofPattern("yyyyMM")))))) {
                        errorMsg = addErrorMsg(errorMsg, "账期不存在 ");
                        break;
                    }
                    start = start.plusMonths(1);
                }
            }
        }
        if (StringUtils.isEmpty(errorMsg)) {
            fileInventoryUpdate.setErrorMsg("");
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.NORMAL.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        } else {
            fileInventoryUpdate.setErrorMsg(errorMsg);
            fileInventoryUpdate.setAnalysisResult(MaterialDeliverAnalysisResult.EXCEPTION.getCode());
            fileInventoryUpdate.setId(fileInventory.getId());
            updateById(fileInventoryUpdate);
        }
    }

    private LambdaQueryWrapper<MaterialDeliverFileInventory> buildMaterialDeliverFileInventoryQueryWrapper(MaterialDeliver materialDeliver, Integer analysisResult, String errorMsg, Integer isRepeat) {
        LambdaQueryWrapper<MaterialDeliverFileInventory> queryWrapper = new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliver.getId());
        if (!Objects.isNull(analysisResult)) {
            queryWrapper.eq(MaterialDeliverFileInventory::getAnalysisResult, analysisResult);
        }
        if (!StringUtils.isEmpty(errorMsg)) {
            List<String> errorMsgList = Arrays.asList(errorMsg.split(","));
            queryWrapper.in(MaterialDeliverFileInventory::getErrorMsg, errorMsgList);
        }
        if (!Objects.isNull(isRepeat)) {
            if (isRepeat == 0) {
                queryWrapper.isNull(MaterialDeliverFileInventory::getRepeatId);
            } else {
                queryWrapper.isNotNull(MaterialDeliverFileInventory::getRepeatId);
            }
        }
        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getBankName)
                    .orderByAsc(MaterialDeliverFileInventory::getStartDate)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getPeriod)
                    .orderByAsc(MaterialDeliverFileInventory::getFileNumber)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getId);
        }
        return queryWrapper;
    }

    private LambdaQueryWrapper<MaterialDeliverFileInventory> buildMaterialDeliverFileInventoryQueryWrapperV2(MaterialDeliver materialDeliver) {
        LambdaQueryWrapper<MaterialDeliverFileInventory> queryWrapper = new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliver.getId());
        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getBankName)
                    .orderByAsc(MaterialDeliverFileInventory::getStartDate)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.TICKET_IN_ACCOUNT.getCode())) {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getCustomerName)
                    .orderByAsc(MaterialDeliverFileInventory::getPeriod)
                    .orderByAsc(MaterialDeliverFileInventory::getFileNumber)
                    .orderByAsc(MaterialDeliverFileInventory::getId);
        } else {
            queryWrapper.orderByAsc(MaterialDeliverFileInventory::getId);
        }
        return queryWrapper;
    }

    /**
     * 生成账期清单
     */
    private void generatePeriodInventory(MaterialDeliverFileInventory fileInventory) {
        List<MaterialDeliverPeriodInventory> periodInventories = new ArrayList<>();

        LocalDate startDate = fileInventory.getStartDate();
        LocalDate endDate = fileInventory.getEndDate();

        if (startDate != null) {
            // 如果 endDate 为空，则设置为当前月份的第一天
            if (endDate == null) {
                endDate = LocalDate.now().withDayOfMonth(1);
            }

            // 生成账期清单
            LocalDate current = startDate;
            while (!current.isAfter(endDate)) {
                MaterialDeliverPeriodInventory periodInventory = new MaterialDeliverPeriodInventory();
                periodInventory.setMaterialDeliverId(fileInventory.getMaterialDeliverId());
                periodInventory.setCustomerName(fileInventory.getCustomerName());
                periodInventory.setBankName(fileInventory.getBankName());
                periodInventory.setBankAccountNumber(fileInventory.getBankAccountNumber());
                periodInventory.setCustomerServiceId(fileInventory.getCustomerServiceId());
                periodInventory.setPeriod(Integer.parseInt(current.format(DateTimeFormatter.ofPattern("yyyyMM"))));

                periodInventories.add(periodInventory);
                current = current.plusMonths(1); // 前进到下一个月
            }

            // 批量插入
            if (!periodInventories.isEmpty()) {
                materialDeliverPeriodInventoryMapper.insertBatch(periodInventories);
            }
        }
    }

    /**
     * 删除账期清单表的相关数据（根据 customer_name 匹配）
     */
    private void deletePeriodInventoryByCustomerName(String customerName) {
        materialDeliverPeriodInventoryMapper.delete(
                new QueryWrapper<MaterialDeliverPeriodInventory>()
                        .eq("customer_name", customerName));
    }
}
