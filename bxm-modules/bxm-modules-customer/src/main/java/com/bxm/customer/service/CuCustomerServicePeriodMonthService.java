package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class CuCustomerServicePeriodMonthService extends ServiceImpl<CustomerServicePeriodMonthMapper, CustomerServicePeriodMonth> {

    // 检查是否存在记录
    public boolean checkRecordExists(Long customerServiceId, Integer period) {
        QueryWrapper<CustomerServicePeriodMonth> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_service_id", customerServiceId)
                .eq("period", period);
        // 使用 count 查询符合条件的记录数，如果大于 0，则表示记录存在
        return count(queryWrapper) > 0;
    }

}
