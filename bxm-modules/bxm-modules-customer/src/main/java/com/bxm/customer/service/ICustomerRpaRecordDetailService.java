package com.bxm.customer.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerRpaRecordDetail;
import com.bxm.customer.domain.dto.CustomerDeliverRpaDetailDTO;

/**
 * rap明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface ICustomerRpaRecordDetailService extends IService<CustomerRpaRecordDetail>
{
    /**
     * 查询rap明细
     * 
     * @param id rap明细主键
     * @return rap明细
     */
    public CustomerRpaRecordDetail selectCustomerRpaRecordDetailById(Long id);

    /**
     * 查询rap明细列表
     * 
     * @param customerRpaRecordDetail rap明细
     * @return rap明细集合
     */
    public List<CustomerRpaRecordDetail> selectCustomerRpaRecordDetailList(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 新增rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    public int insertCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 修改rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    public int updateCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 批量删除rap明细
     * 
     * @param ids 需要删除的rap明细主键集合
     * @return 结果
     */
    public int deleteCustomerRpaRecordDetailByIds(Long[] ids);

    /**
     * 删除rap明细信息
     * 
     * @param id rap明细主键
     * @return 结果
     */
    public int deleteCustomerRpaRecordDetailById(Long id);

    List<CustomerRpaRecordDetail> selectByRpaRecordId(Long rpaRecordId);

    IPage<CustomerDeliverRpaDetailDTO> rpaDetailList(Long rpaRecordId, String customerName, Integer status, Integer pageNum, Integer pageSize);

    void saveRpaToCustomerDeliverByDetailIds(List<Long> rpaDetailIds, Long deptId);

    void saveRpaToCustomerDeliverByDetails(List<CustomerRpaRecordDetail> details, Long deptId);

    List<CustomerRpaRecordDetail> selectFailByRpaRecordId(Long rpaRecordId);
}
