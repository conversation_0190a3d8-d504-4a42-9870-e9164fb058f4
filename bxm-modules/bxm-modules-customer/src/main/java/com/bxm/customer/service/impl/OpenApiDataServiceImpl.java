package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.OpenApiData;
import com.bxm.customer.mapper.OpenApiDataMapper;
import com.bxm.customer.service.IOpenApiDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpenApiDataServiceImpl extends ServiceImpl<OpenApiDataMapper, OpenApiData> implements IOpenApiDataService {

    @Autowired
    private OpenApiDataMapper openApiDataMapper;
}
