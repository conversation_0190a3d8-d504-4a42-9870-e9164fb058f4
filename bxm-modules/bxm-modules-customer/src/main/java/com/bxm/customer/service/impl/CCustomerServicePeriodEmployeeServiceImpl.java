package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CCustomerServicePeriodEmployee;
import com.bxm.customer.mapper.CCustomerServicePeriodEmployeeMapper;
import com.bxm.customer.service.ICCustomerServicePeriodEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 账期服务人员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
public class CCustomerServicePeriodEmployeeServiceImpl extends ServiceImpl<CCustomerServicePeriodEmployeeMapper, CCustomerServicePeriodEmployee> implements ICCustomerServicePeriodEmployeeService
{
    @Autowired
    private CCustomerServicePeriodEmployeeMapper cCustomerServicePeriodEmployeeMapper;

    /**
     * 查询账期服务人员
     * 
     * @param id 账期服务人员主键
     * @return 账期服务人员
     */
    @Override
    public CCustomerServicePeriodEmployee selectCCustomerServicePeriodEmployeeById(Long id)
    {
        return cCustomerServicePeriodEmployeeMapper.selectCCustomerServicePeriodEmployeeById(id);
    }

    /**
     * 查询账期服务人员列表
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 账期服务人员
     */
    @Override
    public List<CCustomerServicePeriodEmployee> selectCCustomerServicePeriodEmployeeList(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee)
    {
        return cCustomerServicePeriodEmployeeMapper.selectCCustomerServicePeriodEmployeeList(cCustomerServicePeriodEmployee);
    }

    /**
     * 新增账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    @Override
    public int insertCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee)
    {
        cCustomerServicePeriodEmployee.setCreateTime(DateUtils.getNowDate());
        return cCustomerServicePeriodEmployeeMapper.insertCCustomerServicePeriodEmployee(cCustomerServicePeriodEmployee);
    }

    /**
     * 修改账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    @Override
    public int updateCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee)
    {
        cCustomerServicePeriodEmployee.setUpdateTime(DateUtils.getNowDate());
        return cCustomerServicePeriodEmployeeMapper.updateCCustomerServicePeriodEmployee(cCustomerServicePeriodEmployee);
    }

    /**
     * 批量删除账期服务人员
     * 
     * @param ids 需要删除的账期服务人员主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServicePeriodEmployeeByIds(Long[] ids)
    {
        return cCustomerServicePeriodEmployeeMapper.deleteCCustomerServicePeriodEmployeeByIds(ids);
    }

    /**
     * 删除账期服务人员信息
     * 
     * @param id 账期服务人员主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServicePeriodEmployeeById(Long id)
    {
        return cCustomerServicePeriodEmployeeMapper.deleteCCustomerServicePeriodEmployeeById(id);
    }

    @Override
    public void deleteByPeriodId(Long periodId) {
        if (!Objects.isNull(periodId)) {
            remove(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                    .eq(CCustomerServicePeriodEmployee::getPeriodId, periodId));
        }
    }

    @Override
    public void saveNewPeriodEmployee(Integer nowPeriod) {
        baseMapper.saveNewPeriodAdvisorEmployee(nowPeriod);
        baseMapper.saveNewPeriodAccountingEmployee(nowPeriod);
    }

    @Override
    public void saveNewPeriodEmployeeByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd) {
        baseMapper.saveNewPeriodAdvisorEmployeeByCustomerServiceIds(customerServiceIds, periodStart, periodEnd);
        baseMapper.saveNewPeriodAccountingEmployeeByCustomerServiceIds(customerServiceIds, periodStart, periodEnd);
    }

    @Override
    public List<CCustomerServicePeriodEmployee> selectByPeriodIdsAndEmployeeType(List<Long> customerServicePeriodMonthIdList, Integer employeeType) {
        if (ObjectUtils.isEmpty(customerServicePeriodMonthIdList)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>().eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, employeeType)
                .in(CCustomerServicePeriodEmployee::getPeriodId, customerServicePeriodMonthIdList));
    }
}
