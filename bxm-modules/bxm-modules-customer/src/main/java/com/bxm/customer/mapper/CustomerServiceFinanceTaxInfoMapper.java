package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceFinanceTaxInfo;

/**
 * 客户服务财税信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface CustomerServiceFinanceTaxInfoMapper extends BaseMapper<CustomerServiceFinanceTaxInfo>
{
    /**
     * 查询客户服务财税信息
     * 
     * @param id 客户服务财税信息主键
     * @return 客户服务财税信息
     */
    public CustomerServiceFinanceTaxInfo selectCustomerServiceFinanceTaxInfoById(Long id);

    /**
     * 查询客户服务财税信息列表
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 客户服务财税信息集合
     */
    public List<CustomerServiceFinanceTaxInfo> selectCustomerServiceFinanceTaxInfoList(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo);

    /**
     * 新增客户服务财税信息
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 结果
     */
    public int insertCustomerServiceFinanceTaxInfo(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo);

    /**
     * 修改客户服务财税信息
     * 
     * @param customerServiceFinanceTaxInfo 客户服务财税信息
     * @return 结果
     */
    public int updateCustomerServiceFinanceTaxInfo(CustomerServiceFinanceTaxInfo customerServiceFinanceTaxInfo);

    /**
     * 删除客户服务财税信息
     * 
     * @param id 客户服务财税信息主键
     * @return 结果
     */
    public int deleteCustomerServiceFinanceTaxInfoById(Long id);

    /**
     * 批量删除客户服务财税信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceFinanceTaxInfoByIds(Long[] ids);
}
