package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.api.domain.vo.RemoteCustomerServicePeriodYearSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServicePeriodYearVO;
import com.bxm.customer.domain.CustomerServicePeriodYear;
import com.bxm.customer.domain.vo.CustomerServicePeriodYearVO;

/**
 * 客户服务年度账期Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface ICustomerServicePeriodYearService extends IService<CustomerServicePeriodYear>
{
    /**
     * 查询客户服务年度账期
     * 
     * @param id 客户服务年度账期主键
     * @return 客户服务年度账期
     */
    public CustomerServicePeriodYear selectCustomerServicePeriodYearById(Long id);

    /**
     * 查询客户服务年度账期列表
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 客户服务年度账期集合
     */
    public List<CustomerServicePeriodYear> selectCustomerServicePeriodYearList(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 新增客户服务年度账期
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    public int insertCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 修改客户服务年度账期
     * 
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    public int updateCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear);

    /**
     * 批量删除客户服务年度账期
     * 
     * @param ids 需要删除的客户服务年度账期主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodYearByIds(Long[] ids);

    /**
     * 删除客户服务年度账期信息
     * 
     * @param id 客户服务年度账期主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodYearById(Long id);

    void addCustomerServicePeriodYear(Long customerServiceId, List<Integer> yearPeriodList);

    void saveCustomerServicePeriodYear(List<CustomerServicePeriodYear> yearPeriodList);

    void updateYearPeriodList(List<CustomerServicePeriodYear> updateYearPeriods);

    void deleteYearPeriodList(List<Long> deleteYearPeriods);

    List<CustomerServicePeriodYear> selectByCustomerServiceId(Long customerServiceId);

    Integer selectCountByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period);

    void modifyCustomerServicePeriodYear(CustomerServicePeriodYearVO vo);

    List<CustomerServicePeriodYear> getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(RemoteCustomerServicePeriodYearSearchVO vo);

    void remoteUpdateCustomerServicePeriodYear(RemoteCustomerServicePeriodYearVO vo);
}
