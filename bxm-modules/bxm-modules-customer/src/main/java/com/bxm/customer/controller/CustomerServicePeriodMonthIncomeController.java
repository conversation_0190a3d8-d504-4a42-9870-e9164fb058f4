package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.CommonExcelUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.customize.annotation.TimeParamHandler;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteAccountingCashierDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeVO;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.domain.dto.CustomerServiceIncomeInfoDTO;
import com.bxm.customer.domain.dto.CustomerServicePeriodMonthIncomeDetailDTO;
import com.bxm.customer.domain.dto.CustomerServiceYearIncomeInfoDTO;
import com.bxm.customer.domain.vo.CustomerServiceIncomeInfoSearchVO;
import com.bxm.customer.domain.vo.CustomerServiceYearIncomeInfoSearchVO;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.ICCustomerServiceService;
import com.bxm.customer.service.ICustomerServicePeriodMonthIncomeService;
import com.bxm.customer.service.IDownloadRecordService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 客户服务账期收入Controller
 * 
 * <AUTHOR>
 * @date 2024-05-13
 */
@RestController
@RequestMapping("/income")
@Api(tags = "客户服务账期收入")
public class CustomerServicePeriodMonthIncomeController extends BaseController
{
    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    /**
     * 查询客户服务账期收入列表
     */
    @RequiresPermissions("customer:income:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询客户服务账期收入列表", notes = "查询客户服务账期收入列表")
    public Result<IPage<CustomerServicePeriodMonthIncome>> list(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome,
                                                           @RequestHeader("deptId") Long deptId,
                                                           @RequestParam("pageNum") Integer pageNum,
                                                           @RequestParam("pageSize") Integer pageSize)
    {
        return Result.ok(customerServicePeriodMonthIncomeService.incomeList(customerServicePeriodMonthIncome, deptId, pageNum, pageSize));
    }

    @GetMapping("/getIncomeFiles")
    @ApiOperation("查看收入附件")
    public Result<List<CommonFileVO>> getIncomeFiles(@RequestParam("id") @Param("收入id") Long id) {
        return Result.ok(customerServicePeriodMonthIncomeService.getIncomeFiles(id));
    }

    /**
     * 导出客户服务账期收入列表
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出客户服务账期收入列表", notes = "导出客户服务账期收入列表")
    public void export(HttpServletResponse response, CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome, @RequestHeader("deptId") Long deptId)
    {
        // pageSize=-1代表不分页
        if (Objects.isNull(customerServicePeriodMonthIncome.getPeriodStart()) || Objects.isNull(customerServicePeriodMonthIncome.getPeriodEnd())) {
            Map<String, LocalDate> dateMap = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
            customerServicePeriodMonthIncome.setPeriodStart(Integer.parseInt(dateMap.get("startDate").format(DateTimeFormatter.ofPattern(DateUtils.YYYYMM))));
            customerServicePeriodMonthIncome.setPeriodEnd(Integer.parseInt(dateMap.get("endDate").format(DateTimeFormatter.ofPattern(DateUtils.YYYYMM))));
        } else {
            LocalDate start = DateUtils.strToLocalDate(customerServicePeriodMonthIncome.getPeriodStart() + "01", DateUtils.YYYYMMDD);
            LocalDate end = DateUtils.strToLocalDate(customerServicePeriodMonthIncome.getPeriodEnd() + "01", DateUtils.YYYYMMDD);
            int startYear = start.getYear();
            int endYear = end.getYear();
            int startMonth = start.getMonthValue();
            int endMonth = end.getMonthValue();
            // 根据以上4个值判断是否在12个月内
            if (endYear - startYear > 1 || (endYear - startYear == 1 && endMonth >= startMonth)) {
                throw new ServiceException("单次导出最多12个月内");
            }
        }
        List<CustomerServicePeriodMonthIncome> list = customerServicePeriodMonthIncomeService.incomeList(customerServicePeriodMonthIncome, deptId, 1, -1).getRecords();
        ExcelUtil<CustomerServicePeriodMonthIncome> util = new ExcelUtil<CustomerServicePeriodMonthIncome>(CustomerServicePeriodMonthIncome.class);
        util.exportExcel(response, list, "客户服务账期收入数据");
    }

    @PostMapping("/exportAndUpload")
    @ApiOperation(value = "导出客户服务账期收入列表", notes = "导出客户服务账期收入列表")
    public Result exportAndUpload(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome, @RequestHeader("deptId") Long deptId)
    {
        // pageSize=-1代表不分页
        if (Objects.isNull(customerServicePeriodMonthIncome.getPeriodStart()) || Objects.isNull(customerServicePeriodMonthIncome.getPeriodEnd())) {
            Map<String, LocalDate> dateMap = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
            customerServicePeriodMonthIncome.setPeriodStart(Integer.parseInt(dateMap.get("startDate").format(DateTimeFormatter.ofPattern(DateUtils.YYYYMM))));
            customerServicePeriodMonthIncome.setPeriodEnd(Integer.parseInt(dateMap.get("endDate").format(DateTimeFormatter.ofPattern(DateUtils.YYYYMM))));
        } else {
            LocalDate start = DateUtils.strToLocalDate(customerServicePeriodMonthIncome.getPeriodStart() + "01", DateUtils.YYYYMMDD);
            LocalDate end = DateUtils.strToLocalDate(customerServicePeriodMonthIncome.getPeriodEnd() + "01", DateUtils.YYYYMMDD);
            int startYear = start.getYear();
            int endYear = end.getYear();
            int startMonth = start.getMonthValue();
            int endMonth = end.getMonthValue();
            // 根据以上4个值判断是否在12个月内
            if (endYear - startYear > 1 || (endYear - startYear == 1 && endMonth >= startMonth)) {
                return Result.fail("单次导出最多12个月内");
            }
        }
        customerServicePeriodMonthIncome.setDeptId(deptId);
        String title = "收入" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, customerServicePeriodMonthIncome, DownloadType.INCOME);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServicePeriodMonthIncome> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                while (true) {
                    List<CustomerServicePeriodMonthIncome> l = customerServicePeriodMonthIncomeService.incomeList(customerServicePeriodMonthIncome, deptId, pageNum, pageSize).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                if (StringUtils.isEmpty(customerServicePeriodMonthIncome.getExportTypes())) {
                    ExcelUtil<CustomerServicePeriodMonthIncome> util = new ExcelUtil<>(CustomerServicePeriodMonthIncome.class);
                    asyncService.uploadExport(util, list, title, downloadRecordId);
                } else {
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, CustomerServicePeriodMonthIncome.class);
                    dataMap.put(title, list);
                    asyncService.uploadExport(customerServicePeriodMonthIncomeService.buildFiles(customerServicePeriodMonthIncome, list), dataMap, sheetClassMap, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @PostMapping("/addPeriodMonthIncome")
    @ApiOperation("新增月度收入,权限字符：customer:income:add")
    @RequiresPermissions("customer:income:add")
    public Result addPeriodMonthIncome(@RequestBody CustomerServicePeriodMonthIncome income,
                                       @RequestHeader("deptId") Long deptId)
    {
        customerServicePeriodMonthIncomeService.addPeriodMonthIncome(income, deptId);
        return Result.ok();
    }

    @PostMapping("/modifyPeriodMonthIncome")
    @ApiOperation("编辑月度收入,权限字符：customer:income:modify")
    @RequiresPermissions("customer:income:modify")
    public Result modifyPeriodMonthIncome(@RequestBody CustomerServicePeriodMonthIncome income,
                                          @RequestHeader("deptId") Long deptId)
    {
        customerServicePeriodMonthIncomeService.modifyPeriodMonthIncome(income, deptId);
        return Result.ok();
    }

    @ApiOperation("收入详情,操作记录的businessType=4")
    @GetMapping("/detail")
    public Result<CustomerServicePeriodMonthIncomeDetailDTO> detail(@RequestParam("id") @ApiParam("收入id") Long id) {
        return Result.ok(customerServicePeriodMonthIncomeService.detail(id));
    }

    @PostMapping("/getCustomerServiceIncomeByCustomerServiceIdAndPeriod")
    @ApiIgnore
    @InnerAuth
    public Result<List<CustomerServicePeriodMonthIncome>> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(@RequestBody List<RemoteCustomerServiceIncomeSearchVO> voList) {
        return Result.ok(customerServicePeriodMonthIncomeService.getCustomerServiceIncomeByCustomerServiceIdAndPeriod(voList));
    }

    @PostMapping("/remoteUpdateOrCreateIncome")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateOrCreateIncome(@RequestBody RemoteCustomerServiceIncomeVO vo) {
        customerServicePeriodMonthIncomeService.remoteUpdateOrCreateIncome(vo);
        return Result.ok();
    }

    @PostMapping("/remoteUpdateCustomerIncome")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateCustomerIncome(@RequestBody List<Long> customerServiceIds) {
        customerServicePeriodMonthIncomeService.remoteUpdateCustomerIncome(customerServiceIds);
        return Result.ok();
    }

    @PostMapping("/customerServiceIncomeInfo")
    @ApiOperation("收入-预警统计")
    @TimeParamHandler
    public Result<IPage<CustomerServiceIncomeInfoDTO>> customerServiceIncomeInfo(@RequestHeader("deptId") Long deptId,
                                                                                 @RequestBody CustomerServiceIncomeInfoSearchVO vo) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(customerServiceService.customerServiceIncomeInfo(vo.getDeptId(), vo));
    }

    @PostMapping("/customerServiceIncomeInfoExportAndUpload")
    @ApiOperation("收入-预警统计（异步导出）")
    @TimeParamHandler
    public Result customerServiceIncomeInfoExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                           CustomerServiceIncomeInfoSearchVO vo) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            throw new ServiceException("当前公司不存在");
        }
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        String title = "收入-预警统计" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.INCOME_WARNING);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServiceIncomeInfoDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                while (true) {
                    vo.setPageNum(pageNum);
                    vo.setPageSize(pageSize);
                    List<CustomerServiceIncomeInfoDTO> l = customerServiceService.customerServiceIncomeInfo(vo.getDeptId(), vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<CustomerServiceIncomeInfoDTO> util = new ExcelUtil<>(CustomerServiceIncomeInfoDTO.class);
                if (Objects.equals(1, sysDept.getDeptType())) {
                    util.hideColumn("accountingTopDeptName", "accountingInfo");
                }
                LocalDate now = LocalDate.now();
                Field thisMonthIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthIncomeStr");
                Excel thisMonthIncomeStrAnnotation = thisMonthIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthIncomeStrAnnotation, "name", now.getMonthValue() + "月");

                Field thisMonthPreOneIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreOneIncomeStr");
                Excel thisMonthPreOneIncomeStrAnnotation = thisMonthPreOneIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreOneIncomeStrAnnotation, "name", now.minusMonths(1).getMonthValue() + "月");

                Field thisMonthPreTwoIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreTwoIncomeStr");
                Excel thisMonthPreTwoIncomeStrAnnotation = thisMonthPreTwoIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreTwoIncomeStrAnnotation, "name", now.minusMonths(2).getMonthValue() + "月");

                Field thisMonthPreThreeIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreThreeIncomeStr");
                Excel thisMonthPreThreeIncomeStrAnnotation = thisMonthPreThreeIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreThreeIncomeStrAnnotation, "name", now.minusMonths(3).getMonthValue() + "月");

                Field thisMonthPreFourIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreFourIncomeStr");
                Excel thisMonthPreFourIncomeStrAnnotation = thisMonthPreFourIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreFourIncomeStrAnnotation, "name", now.minusMonths(4).getMonthValue() + "月");

                Field thisMonthPreFiveIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreFiveIncomeStr");
                Excel thisMonthPreFiveIncomeStrAnnotation = thisMonthPreFiveIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreFiveIncomeStrAnnotation, "name", now.minusMonths(5).getMonthValue() + "月");

                Field thisMonthPreSixIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreSixIncomeStr");
                Excel thisMonthPreSixIncomeStrAnnotation = thisMonthPreSixIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreSixIncomeStrAnnotation, "name", now.minusMonths(6).getMonthValue() + "月");

                Field thisMonthPreSevenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreSevenIncomeStr");
                Excel thisMonthPreSevenIncomeStrAnnotation = thisMonthPreSevenIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreSevenIncomeStrAnnotation, "name", now.minusMonths(7).getMonthValue() + "月");

                Field thisMonthPreEightIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreEightIncomeStr");
                Excel thisMonthPreEightIncomeStrAnnotation = thisMonthPreEightIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreEightIncomeStrAnnotation, "name", now.minusMonths(8).getMonthValue() + "月");

                Field thisMonthPreNineIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreNineIncomeStr");
                Excel thisMonthPreNineIncomeStrAnnotation = thisMonthPreNineIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreNineIncomeStrAnnotation, "name", now.minusMonths(9).getMonthValue() + "月");

                Field thisMonthPreTenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreTenIncomeStr");
                Excel thisMonthPreTenIncomeStrAnnotation = thisMonthPreTenIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreTenIncomeStrAnnotation, "name", now.minusMonths(10).getMonthValue() + "月");

                Field thisMonthPreElevenIncomeStr = CustomerServiceIncomeInfoDTO.class.getDeclaredField("thisMonthPreElevenIncomeStr");
                Excel thisMonthPreElevenIncomeStrAnnotation = thisMonthPreElevenIncomeStr.getAnnotation(Excel.class);
                CommonExcelUtils.setAnnotationValue(thisMonthPreElevenIncomeStrAnnotation, "name", now.minusMonths(11).getMonthValue() + "月");

                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @PostMapping("/customerServiceYearIncomeInfo")
    @ApiOperation("收入-年统计")
    @TimeParamHandler
    public Result<IPage<CustomerServiceYearIncomeInfoDTO>> customerServiceYearIncomeInfo(@RequestHeader("deptId") Long deptId,
                                                                                         @RequestBody CustomerServiceYearIncomeInfoSearchVO vo) {
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        return Result.ok(customerServiceService.customerServiceYearIncomeInfo(vo.getDeptId(), vo));
    }

    @PostMapping("/customerServiceYearIncomeInfoExportAndUpload")
    @ApiOperation("收入-年统计（异步导出）")
    @TimeParamHandler
    public Result customerServiceYearIncomeInfoExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                           CustomerServiceYearIncomeInfoSearchVO vo) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            throw new ServiceException("当前公司不存在");
        }
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        String title = "收入-年统计" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.INCOME_YEAR_WARNING);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerServiceYearIncomeInfoDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                while (true) {
                    vo.setPageNum(pageNum);
                    vo.setPageSize(pageSize);
                    List<CustomerServiceYearIncomeInfoDTO> l = customerServiceService.customerServiceYearIncomeInfo(vo.getDeptId(), vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<CustomerServiceYearIncomeInfoDTO> util = new ExcelUtil<>(CustomerServiceYearIncomeInfoDTO.class);
                if (Objects.equals(1, sysDept.getDeptType())) {
                    util.hideColumn("accountingTopDeptName", "accountingInfo");
                }
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }
}
