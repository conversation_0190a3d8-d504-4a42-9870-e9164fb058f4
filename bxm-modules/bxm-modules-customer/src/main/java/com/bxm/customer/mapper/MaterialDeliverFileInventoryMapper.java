package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import org.apache.ibatis.annotations.Param;

/**
 * 材料交接单文件清单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Mapper
public interface MaterialDeliverFileInventoryMapper extends BaseMapper<MaterialDeliverFileInventory>
{
    /**
     * 查询材料交接单文件清单
     *
     * @param id 材料交接单文件清单主键
     * @return 材料交接单文件清单
     */
    public MaterialDeliverFileInventory selectMaterialDeliverFileInventoryById(Long id);

    /**
     * 查询材料交接单文件清单列表
     *
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 材料交接单文件清单集合
     */
    public List<MaterialDeliverFileInventory> selectMaterialDeliverFileInventoryList(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 新增材料交接单文件清单
     *
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    public int insertMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 修改材料交接单文件清单
     *
     * @param materialDeliverFileInventory 材料交接单文件清单
     * @return 结果
     */
    public int updateMaterialDeliverFileInventory(MaterialDeliverFileInventory materialDeliverFileInventory);

    /**
     * 删除材料交接单文件清单
     *
     * @param id 材料交接单文件清单主键
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryById(Long id);

    /**
     * 批量删除材料交接单文件清单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryByIds(Long[] ids);

    List<MaterialDeliverFileInventory> selectCanNotPushFileInventory(@Param("materialDeliverIds") List<Long> materialDeliverIds);
}
