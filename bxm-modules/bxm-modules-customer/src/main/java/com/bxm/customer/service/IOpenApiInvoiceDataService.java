package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiData;
import com.bxm.customer.domain.OpenApiInvoiceData;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IOpenApiInvoiceDataService extends IService<OpenApiInvoiceData>
{

    OpenApiInvoiceData selectByRecordId(Long recordId);

    List<OpenApiInvoiceData> selectListByRecordId(Long recordId);
}
