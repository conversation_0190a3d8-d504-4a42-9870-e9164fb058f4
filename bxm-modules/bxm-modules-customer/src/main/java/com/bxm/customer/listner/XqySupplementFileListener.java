package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.customer.domain.vo.xqy.XqyReportVO;
import com.bxm.customer.domain.vo.xqy.XqySupplementVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerSupplement" + "${spring.profiles.active}", topic = "openapiSupplement_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "xqy")
public class XqySupplementFileListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到鑫启易补充消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            XqySupplementVO xqySupplementVO = JSONObject.parseObject(new String(message.getBody()), XqySupplementVO.class);
            xqyService.supplementFile(xqySupplementVO);
        } catch (Exception e) {
            log.error("处理鑫启易补充消息异常:{}", e.getMessage());
        }
    }
}
