package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.SettlementType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.Bill;
import com.bxm.customer.domain.SettlementOrder;
import com.bxm.customer.domain.dto.settlementOrder.*;
import com.bxm.customer.domain.vo.settlementOrder.*;
import com.bxm.customer.mapper.SettlementOrderMapper;
import com.bxm.customer.service.SettlementOrderService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysDept;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/settlement/order")
@Api(tags = "结算单相关")
public class SettlementOrderController {

    @Autowired
    private SettlementOrderService settlementOrderService;

    @Autowired
    private RemoteDeptService remoteDeptService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SettlementOrderMapper settlementOrderMapper;

    @GetMapping("/settlementOrderList")
    @ApiOperation("结算单列表")
    @RequiresPermissions("customer:settlementOrder:list")
    public Result<IPage<SettlementOrderDTO>> settlementOrderList(@RequestHeader("deptId") Long deptId, SettlementOrderSearchVO vo) {
        return Result.ok(settlementOrderService.settlementOrderList(deptId, vo));
    }

    @PostMapping("/exportSettlementOrderList")
    @ApiOperation("导出结算单列表")
    public void exportSettlementOrderList(HttpServletResponse response, @RequestHeader("deptId") Long deptId, SettlementOrderSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<SettlementOrderDTO> list = settlementOrderService.settlementOrderList(deptId, vo).getRecords();
        ExcelUtil<SettlementOrderDTO> util = new ExcelUtil<>(SettlementOrderDTO.class);
        util.exportExcel(response, list, "结算单列表");
    }

    @GetMapping("/settlementOrderDetail")
    @ApiOperation("结算单详情")
    public Result<SettlementOrderDetailDTO> settlementOrderDetail(@RequestParam("id") @ApiParam("结算单id") Long id) {
        return Result.ok(settlementOrderService.settlementOrderDetail(id));
    }

    @PostMapping("/businessDeptPriceList")
    @ApiOperation("创建/编辑结算单-下一步获取结算结果")
    public Result<SettlementOrderDeptPriceDTO> businessDeptPriceList(@RequestBody SettlementBusinessPriceGetVO vo) {
        return Result.ok(settlementOrderService.businessDeptPriceList(vo));
    }

    @PostMapping("/freshBusinessDeptPriceList")
    @ApiOperation("获取结算结果（重新获取）")
    public Result<SettlementOrderDeptPriceDTO> freshBusinessDeptPriceList(@RequestBody SettlementBusinessPriceGetVO vo) {
        return Result.ok(settlementOrderService.freshBusinessDeptPriceList(vo));
    }

    @PostMapping("/createSettlementOrder")
    @ApiOperation("创建结算单")
    public Result createSettlementOrder(@RequestBody SettlementOrderCreateVO vo) {
        settlementOrderService.createSettlementOrder(vo);
        return Result.ok();
    }

    @PostMapping("/modifySettlementOrder")
    @ApiOperation("编辑结算单")
    public Result modifySettlementOrder(@RequestBody SettlementOrderCreateVO vo) {
        settlementOrderService.modifySettlementOrder(vo);
        return Result.ok();
    }

    @GetMapping("/settlementOrderDataListByConditions")
    @ApiOperation("查看结算单数据明细（创建和编辑的时候查看,需要传batchNo）")
    public Result<IPage<SettlementOrderDataDTO>> settlementOrderDataListByConditions(SettlementOrderDataSearchVO vo) {
        return Result.ok(settlementOrderService.settlementOrderDataListByConditions(vo));
    }

    @GetMapping("/settlementOrderDataListBySettlementOrderId")
    @ApiOperation("查看结算单数据明细（查看结算单详情时查看,需要传settlementOrderId）")
    public Result<IPage<SettlementOrderDataDTO>> settlementOrderDataListBySettlementOrderId(SettlementOrderDataSearchVO vo) {
        return Result.ok(settlementOrderService.settlementOrderDataListBySettlementOrderId(vo));
    }

    @PostMapping("/deleteSettlementOrder")
    @ApiOperation("删除结算单，单个操作，传id")
    public Result deleteSettlementOrder(@RequestBody CommonIdVO vo) {
        settlementOrderService.deleteSettlementOrder(vo.getId());
        return Result.ok();
    }

    @PostMapping("/deleteSettlementOrderBatch")
    @ApiOperation("删除结算单，批量操作，传ids")
    public Result<CommonOperateResultDTO> deleteSettlementOrderBatch(@RequestBody CommonIdVO vo) {
        return Result.ok(settlementOrderService.deleteSettlementOrderBatch(vo.getIds()));
    }

    @PostMapping("/exportSettlementOrderDataListByConditions")
    @ApiOperation("导出查看结算单数据明细（创建和编辑的时候导出,需要传batchNo）")
    public void exportSettlementOrderDataListByConditions(HttpServletResponse response, SettlementOrderDataSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        if (Objects.isNull(vo.getBusinessDeptId())) {
            throw new ServiceException("businessDeptId不能为空");
        }
        SysDept businessDept = remoteDeptService.getDeptInfo(vo.getBusinessDeptId()).getDataThrowException();
        if (Objects.isNull(businessDept)) {
            throw new ServiceException("业务公司不存在");
        }
        List<SettlementOrderDataDTO> data = settlementOrderService.settlementOrderDataListByConditions(vo).getRecords();
        if (Objects.equals(vo.getSettlementType(), SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(vo.getSettlementType(), SettlementType.TASK_PERIOD.getCode())) {
            List<SettlementOrderPeriodDataDTO> exports = data.stream().map(d -> {
                SettlementOrderPeriodDataDTO dto = new SettlementOrderPeriodDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setPeriodInAccountBankPaymentInputTime(Objects.isNull(d.getPeriodInAccountBankPaymentInputTime()) ? "" : d.getPeriodInAccountBankPaymentInputTime().toString());
                dto.setPeriodInAccountInTime(Objects.isNull(d.getPeriodInAccountInTime()) ? "" : d.getPeriodInAccountInTime().toString());
                dto.setPeriodInAccountEndTime(Objects.isNull(d.getPeriodInAccountEndTime()) ? "" : d.getPeriodInAccountEndTime().toString());
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderPeriodDataDTO> util = new ExcelUtil<>(SettlementOrderPeriodDataDTO.class);
            util.exportExcel(response, exports, businessDept.getDeptName() + " " + vo.getSettlementOrderTitle() + "明细");
        } else {
            List<SettlementOrderCustomerDataDTO> exports = data.stream().map(d -> {
                SettlementOrderCustomerDataDTO dto = new SettlementOrderCustomerDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setCreateTime(Objects.isNull(d.getCreateTime()) ? "" : d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderCustomerDataDTO> util = new ExcelUtil<>(SettlementOrderCustomerDataDTO.class);
            util.exportExcel(response, exports, businessDept.getDeptName() + " " + vo.getSettlementOrderTitle() + "明细");
        }
    }

    @PostMapping("/exportDataListByConditionsForPeriodTask")
    @ApiOperation("导出查看账期任务数据明细（创建和编辑的时候导出,需要传batchNo）")
    public void exportDataListByConditionsForPeriodTask(HttpServletResponse response, SettlementOrderDataSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<SettlementOrderDataDTO> data = settlementOrderService.settlementOrderDataListByConditions(vo).getRecords();
        List<SettlementOrderPeriodDataDTO> exports = data.stream().map(d -> {
            SettlementOrderPeriodDataDTO dto = new SettlementOrderPeriodDataDTO();
            BeanUtils.copyProperties(d, dto);
            dto.setPeriodInAccountBankPaymentInputTime(Objects.isNull(d.getPeriodInAccountBankPaymentInputTime()) ? "" : d.getPeriodInAccountBankPaymentInputTime().toString());
            dto.setPeriodInAccountInTime(Objects.isNull(d.getPeriodInAccountInTime()) ? "" : d.getPeriodInAccountInTime().toString());
            dto.setPeriodInAccountEndTime(Objects.isNull(d.getPeriodInAccountEndTime()) ? "" : d.getPeriodInAccountEndTime().toString());
            return dto;
        }).collect(Collectors.toList());
        ExcelUtil<SettlementOrderPeriodDataDTO> util = new ExcelUtil<>(SettlementOrderPeriodDataDTO.class);
        util.exportExcel(response, exports, "账期任务明细");
    }

    @PostMapping("/exportSettlementOrderDataListBySettlementOrderId")
    @ApiOperation("导出查看结算单数据明细（查看结算单详情的时候导出,需要传settlementOrderId）")
    public void exportSettlementOrderDataListBySettlementOrderId(HttpServletResponse response, SettlementOrderDataSearchVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        if (Objects.isNull(vo.getBusinessDeptId())) {
            throw new ServiceException("businessDeptId不能为空");
        }
        SysDept businessDept = remoteDeptService.getDeptInfo(vo.getBusinessDeptId()).getDataThrowException();
        if (Objects.isNull(businessDept)) {
            throw new ServiceException("业务公司不存在");
        }
        SettlementOrder settlementOrder = settlementOrderMapper.selectById(vo.getSettlementOrderId());
        if (Objects.isNull(settlementOrder) || settlementOrder.getIsDel()) {
            throw new ServiceException("结算单不存在");
        }
        List<SettlementOrderDataDTO> data = settlementOrderService.settlementOrderDataListBySettlementOrderId(vo).getRecords();
        if (Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(settlementOrder.getSettlementType(), SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            List<SettlementOrderPeriodDataDTO> exports = data.stream().map(d -> {
                SettlementOrderPeriodDataDTO dto = new SettlementOrderPeriodDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setPeriodInAccountBankPaymentInputTime(Objects.isNull(d.getPeriodInAccountBankPaymentInputTime()) ? "" : d.getPeriodInAccountBankPaymentInputTime().toString());
                dto.setPeriodInAccountInTime(Objects.isNull(d.getPeriodInAccountInTime()) ? "" : d.getPeriodInAccountInTime().toString());
                dto.setPeriodInAccountEndTime(Objects.isNull(d.getPeriodInAccountEndTime()) ? "" : d.getPeriodInAccountEndTime().toString());
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderPeriodDataDTO> util = new ExcelUtil<>(SettlementOrderPeriodDataDTO.class);
            util.exportExcel(response, exports, businessDept.getDeptName() + " " + vo.getSettlementOrderTitle() + "明细");
        } else {
            List<SettlementOrderCustomerDataDTO> exports = data.stream().map(d -> {
                SettlementOrderCustomerDataDTO dto = new SettlementOrderCustomerDataDTO();
                BeanUtils.copyProperties(d, dto);
                dto.setCreateTime(Objects.isNull(d.getCreateTime()) ? "" : d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil<SettlementOrderCustomerDataDTO> util = new ExcelUtil<>(SettlementOrderCustomerDataDTO.class);
            util.exportExcel(response, exports, businessDept.getDeptName() + " " + vo.getSettlementOrderTitle() + "明细");
        }
    }

    @PostMapping("/uploadDetailFile")
    @ApiOperation("上传明细文件并校验，会返回一个批次号")
    public Result<String> uploadDetailFile(MultipartFile file,
                                           @RequestParam("settlementType") @ApiParam("结算类型，1-入账结算， 2-新户预收, 3-账期任务") Integer settlementType,
                                           @RequestParam("isSupplement") @ApiParam("是否补差，1-是，0-否") Integer isSupplement,
                                           @RequestParam(value = "batchNo", required = false) @ApiParam("结算单批次号，创建结算单的追加明细传这个字段") String batchNo,
                                           @RequestParam(value = "settlementOrderId", required = false) @ApiParam("结算单id，编辑结算单的追加明细传这个字段") Long settlementOrderId,
                                           @RequestParam("businessDeptId") @ApiParam("业务公司id") Long businessDeptId) {
        return Result.ok(settlementOrderService.uploadDetailFile(file, settlementType, isSupplement, batchNo, settlementOrderId, businessDeptId));
    }

    @GetMapping("/getCheckResult")
    @ApiOperation("轮询获取校验结果，根据totalDataCount、checkDataCount和isComplete展示进度条以及结果")
    public Result<SettlementOrderUploadCheckResultDTO> getCheckResult(@RequestParam("uploadBatchNo") @ApiParam("上传批次号") String uploadBatchNo,
                                                                      @RequestParam("settlementType") @ApiParam("结算类型，1-入账结算， 2-新户预收, 3-账期任务") Integer settlementType) {
        return Result.ok(settlementOrderService.getCheckResult(uploadBatchNo, settlementType));
    }

    @PostMapping("/downloadErrorFile")
    @ApiOperation("下载错误文件")
    public void downloadErrorFile(HttpServletResponse response, @RequestParam("uploadBatchNo") @ApiParam("上传批次号") String uploadBatchNo, @RequestParam("settlementType") @ApiParam("结算类型，1-入账结算， 2-新户预收, 3-账期任务") Integer settlementType) {
        SettlementOrderUploadCheckResultDTO checkResult = settlementOrderService.getCheckResult(uploadBatchNo, settlementType);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("解析未完成");
        }
        if (Objects.equals(settlementType, SettlementType.IN_ACCOUNT.getCode()) || Objects.equals(settlementType, SettlementType.TASK_PERIOD.getCode()) || Objects.equals(settlementType, SettlementType.IN_ACCOUNT_PREPAYMENT.getCode())) {
            List<SettlementOrderPeriodUploadDTO> periodDataList = redisService.getLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_PERIOD_CHECK_RESULT_LIST + uploadBatchNo, 2000);
            List<SettlementOrderPeriodUploadDTO> errorDataList = ObjectUtils.isEmpty(periodDataList) ? Lists.newArrayList() : periodDataList.stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
            ExcelUtil<SettlementOrderPeriodUploadDTO> util = new ExcelUtil<>(SettlementOrderPeriodUploadDTO.class);
            util.exportExcel(response, errorDataList, "异常数据");
        } else {
            List<SettlementOrderCustomerUploadDTO> customerDataList = redisService.getLargeCacheList(CacheConstants.SETTLEMENT_ORDER_UPLOAD_CUSTOMER_SERVICE_CHECK_RESULT_LIST + uploadBatchNo, 2000);
            List<SettlementOrderCustomerUploadDTO> errorDataList = ObjectUtils.isEmpty(customerDataList) ? Lists.newArrayList() : customerDataList.stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
            ExcelUtil<SettlementOrderCustomerUploadDTO> util = new ExcelUtil<>(SettlementOrderCustomerUploadDTO.class);
            util.exportExcel(response, errorDataList, "异常数据");
        }
    }

    @GetMapping("/confirmAddData")
    @ApiOperation("确认追加数据")
    public Result<AddDataResultDTO> confirmAddData(@RequestParam("uploadBatchNo") @ApiParam("上传批次号") String uploadBatchNo,
                                 @RequestParam(value = "batchNo", required = false) @ApiParam("结算单批次号，创建结算单的追加明细传这个字段") String batchNo,
                                 @RequestParam(value = "settlementOrderId", required = false) @ApiParam("结算单id，编辑结算单的追加明细传这个字段") Long settlementOrderId,
                                 @RequestParam("settlementType") @ApiParam("结算类型，1-入账结算， 2-新户预收, 3-账期任务") Integer settlementType,
                                 @RequestParam("businessDeptId") @ApiParam("业务公司id") Long businessDeptId) {
        return Result.ok(settlementOrderService.confirmAddData(uploadBatchNo, batchNo, settlementOrderId, settlementType, businessDeptId));
    }

    @PostMapping("/deleteSettlementOrderDataWhenCreate")
    @ApiOperation("删除结算单明细，创建/编辑结算单时，批量操作传ids")
    public Result deleteSettlementOrderDataWhenCreate(@RequestBody CommonIdVO vo) {
        settlementOrderService.deleteSettlementOrderDataWhenCreate(vo);
        return Result.ok();
    }

    @PostMapping("/deleteSettlementOrderDataWhenModify")
    @ApiOperation("删除结算单明细，获取结算单详情时，批量操作传ids")
    public Result deleteSettlementOrderDataWhenModify(@RequestBody CommonIdVO vo) {
        settlementOrderService.deleteSettlementOrderDataWhenModify(vo);
        return Result.ok();
    }

    @PostMapping("/pushReview")
    @ApiOperation("结算单推送预览（获取各业务公司结算单情况），传ids")
    public Result<List<SettlementPushReviewDTO>> pushReview(@RequestBody CommonIdVO vo) {
        return Result.ok(settlementOrderService.pushReview(vo));
    }

    @PostMapping("/confirmPush")
    @ApiOperation("确认推送")
    public Result confirmPush(@RequestBody SettlementOrderPushVO vo) {
        settlementOrderService.confirmPush(vo);
        return Result.ok();
    }

    @GetMapping("/getSameBusinessDeptBillList")
    @ApiOperation("追加-获取同公司账单列表")
    public Result<List<Bill>> getSameBusinessDeptBillList(@RequestParam("settlementOrderId") @ApiParam("结算单id") Long settlementOrderId) {
        return Result.ok(settlementOrderService.getSameBusinessDeptBillList(settlementOrderId));
    }

    @PostMapping("/settlementAppendToBill")
    @ApiOperation("结算单追加至账单")
    public Result settlementAppendToBill(@RequestBody SettlementAppendBillVO vo) {
        settlementOrderService.settlementAppendToBill(vo);
        return Result.ok();
    }
}