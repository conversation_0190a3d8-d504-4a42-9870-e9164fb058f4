package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BorrowOrderFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.BorrowAttachmentMapper;
import com.bxm.customer.domain.BorrowAttachment;
import com.bxm.customer.service.IBorrowAttachmentService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 借阅附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
@Service
public class BorrowAttachmentServiceImpl extends ServiceImpl<BorrowAttachmentMapper, BorrowAttachment> implements IBorrowAttachmentService
{
    @Autowired
    private BorrowAttachmentMapper borrowAttachmentMapper;

    /**
     * 查询借阅附件
     * 
     * @param id 借阅附件主键
     * @return 借阅附件
     */
    @Override
    public BorrowAttachment selectBorrowAttachmentById(Long id)
    {
        return borrowAttachmentMapper.selectBorrowAttachmentById(id);
    }

    /**
     * 查询借阅附件列表
     * 
     * @param borrowAttachment 借阅附件
     * @return 借阅附件
     */
    @Override
    public List<BorrowAttachment> selectBorrowAttachmentList(BorrowAttachment borrowAttachment)
    {
        return borrowAttachmentMapper.selectBorrowAttachmentList(borrowAttachment);
    }

    /**
     * 新增借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    @Override
    public int insertBorrowAttachment(BorrowAttachment borrowAttachment)
    {
        borrowAttachment.setCreateTime(DateUtils.getNowDate());
        return borrowAttachmentMapper.insertBorrowAttachment(borrowAttachment);
    }

    /**
     * 修改借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    @Override
    public int updateBorrowAttachment(BorrowAttachment borrowAttachment)
    {
        borrowAttachment.setUpdateTime(DateUtils.getNowDate());
        return borrowAttachmentMapper.updateBorrowAttachment(borrowAttachment);
    }

    /**
     * 批量删除借阅附件
     * 
     * @param ids 需要删除的借阅附件主键
     * @return 结果
     */
    @Override
    public int deleteBorrowAttachmentByIds(Long[] ids)
    {
        return borrowAttachmentMapper.deleteBorrowAttachmentByIds(ids);
    }

    /**
     * 删除借阅附件信息
     * 
     * @param id 借阅附件主键
     * @return 结果
     */
    @Override
    public int deleteBorrowAttachmentById(Long id)
    {
        return borrowAttachmentMapper.deleteBorrowAttachmentById(id);
    }

    @Override
    public List<BorrowAttachment> selectByBorrowOrderIdAndFileType(Long borrowOrderId, BorrowOrderFileType fileType) {
        if (Objects.isNull(borrowOrderId) || Objects.isNull(fileType)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BorrowAttachment>().eq(BorrowAttachment::getBorrowOrderId, borrowOrderId)
                .eq(BorrowAttachment::getIsDel, Boolean.FALSE).eq(BorrowAttachment::getFileType, fileType.getCode()));
    }

    @Override
    @Transactional
    public void removeAndSaveFile(Long borrowOrderId, BorrowOrderFileType borrowOrderFileType, List<CommonFileVO> files) {
        if (Objects.isNull(borrowOrderId) || Objects.isNull(borrowOrderFileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<BorrowAttachment>()
                .eq(BorrowAttachment::getBorrowOrderId, borrowOrderId)
                .eq(BorrowAttachment::getIsDel, Boolean.FALSE)
                .eq(BorrowAttachment::getFileType, borrowOrderFileType.getCode())
                .set(BorrowAttachment::getIsDel, Boolean.TRUE));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(f -> new BorrowAttachment().setBorrowOrderId(borrowOrderId)
                    .setFileType(borrowOrderFileType.getCode())
                    .setFileName(f.getFileName())
                    .setFileUrl(f.getFileUrl())
                    .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
        }
    }
}
