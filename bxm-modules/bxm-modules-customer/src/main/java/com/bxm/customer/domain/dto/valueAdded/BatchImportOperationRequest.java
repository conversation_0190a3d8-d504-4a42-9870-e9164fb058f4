package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量导入操作请求DTO
 *
 * 用于增值交付单批量导入操作的请求参数封装
 * 支持交付、补充交付附件、扣款三种操作类型
 * 包含Excel模板文件和压缩文件的处理
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导入操作请求DTO")
public class BatchImportOperationRequest {

    /**
     * 交付单编号列表
     */
    @NotEmpty(message = "交付单编号列表不能为空")
    @Size(max = 1000, message = "单次批量导入最多支持1000条记录")
    @ApiModelProperty(value = "交付单编号列表", required = true,
                     example = "[\"VAD2508051430001A1C\", \"VAD2508051430002A1C\"]")
    private List<String> deliveryOrderNos;

    /**
     * 操作类型：DELIVERY-交付、SUPPLEMENT_DELIVERY-补充交付附件、DEDUCTION-扣款
     */
    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型", required = true,
                     allowableValues = "DELIVERY,SUPPLEMENT_DELIVERY,DEDUCTION",
                     example = "DELIVERY")
    private ValueAddedBatchImportOperationType operation;

    /**
     * 交付单模板Excel文件（batchExportImportOperationTemplate导出的模板）
     */
    @NotNull(message = "交付单模板文件不能为空")
    @ApiModelProperty(value = "交付单模板Excel文件", required = true)
    private MultipartFile templateFile;

    /**
     * 附件压缩文件（支持zip包、rar包）
     */
    @ApiModelProperty(value = "附件压缩文件，支持zip包、rar包")
    private MultipartFile attachmentFile;



    /**
     * 获取操作描述
     */
    public String getOperationDescription() {
        if (operation == null) {
            return "未知操作";
        }
        return operation.getDescription();
    }

    /**
     * 获取交付单数量
     */
    public int getOrderCount() {
        return deliveryOrderNos == null ? 0 : deliveryOrderNos.size();
    }

    /**
     * 获取交付单编号列表（为了与服务层兼容）
     */
    public List<String> getDeliveryOrderNoList() {
        return deliveryOrderNos;
    }

    /**
     * 验证表单参数
     */
    public void validate() {
        if (deliveryOrderNos == null || deliveryOrderNos.isEmpty()) {
            throw new IllegalArgumentException("交付单编号列表不能为空");
        }

        if (operation == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        if (templateFile == null || templateFile.isEmpty()) {
            throw new IllegalArgumentException("交付单模板文件不能为空");
        }

        // 验证交付单编号数量
        if (deliveryOrderNos.size() > 1000) {
            throw new IllegalArgumentException("单次批量导入最多支持1000条记录");
        }

        // 验证交付单编号格式
        for (String orderNo : deliveryOrderNos) {
            if (orderNo == null || orderNo.trim().isEmpty()) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }
        }

        // 验证文件格式
        validateFiles();
    }

    /**
     * 验证文件参数
     */
    public void validateFiles() {
        if (templateFile == null || templateFile.isEmpty()) {
            throw new IllegalArgumentException("Template file cannot be empty");
        }

        // Validate template file format
        String templateFileName = templateFile.getOriginalFilename();
        if (templateFileName == null ||
            (!templateFileName.toLowerCase().endsWith(".xlsx") &&
             !templateFileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("Template file must be Excel format (.xlsx or .xls)");
        }

        // If attachment file exists, validate format
        if (attachmentFile != null && !attachmentFile.isEmpty()) {
            String attachmentFileName = attachmentFile.getOriginalFilename();
            if (attachmentFileName == null ||
                (!attachmentFileName.toLowerCase().endsWith(".zip") &&
                 !attachmentFileName.toLowerCase().endsWith(".rar"))) {
                throw new IllegalArgumentException("Attachment file must be compressed format (.zip or .rar)");
            }
        }
    }

    /**
     * 判断是否有附件文件
     */
    public boolean hasAttachmentFile() {
        return attachmentFile != null && !attachmentFile.isEmpty();
    }
}
