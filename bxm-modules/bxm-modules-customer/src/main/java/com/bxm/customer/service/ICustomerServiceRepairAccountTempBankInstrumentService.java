package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument;

/**
 * 补账临时的 材料交接银行票据Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceRepairAccountTempBankInstrumentService extends IService<CustomerServiceRepairAccountTempBankInstrument>
{
    /**
     * 查询补账临时的 材料交接银行票据
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 补账临时的 材料交接银行票据
     */
    public CustomerServiceRepairAccountTempBankInstrument selectCustomerServiceRepairAccountTempBankInstrumentById(Long id);

    /**
     * 查询补账临时的 材料交接银行票据列表
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 补账临时的 材料交接银行票据集合
     */
    public List<CustomerServiceRepairAccountTempBankInstrument> selectCustomerServiceRepairAccountTempBankInstrumentList(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 新增补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 修改补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument);

    /**
     * 批量删除补账临时的 材料交接银行票据
     * 
     * @param ids 需要删除的补账临时的 材料交接银行票据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempBankInstrumentByIds(Long[] ids);

    /**
     * 删除补账临时的 材料交接银行票据信息
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempBankInstrumentById(Long id);
}
