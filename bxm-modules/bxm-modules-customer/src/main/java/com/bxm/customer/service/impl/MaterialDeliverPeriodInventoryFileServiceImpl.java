package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.MaterialDeliverPeriodInventoryFile;
import com.bxm.customer.mapper.MaterialDeliverPeriodInventoryFileMapper;
import com.bxm.customer.service.IMaterialDeliverPeriodInventoryFileService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 材料交接单文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Service
public class MaterialDeliverPeriodInventoryFileServiceImpl extends ServiceImpl<MaterialDeliverPeriodInventoryFileMapper, MaterialDeliverPeriodInventoryFile> implements IMaterialDeliverPeriodInventoryFileService
{

    @Override
    @Transactional
    public void saveByFiles(Long periodInventoryId, List<CommonFileVO> files) {
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(row -> new MaterialDeliverPeriodInventoryFile().setPeriodInventoryId(periodInventoryId)
                    .setFileUrl(row.getFileUrl()).setFileSize(row.getFileSize()).setFileName(row.getFileName()).setFileNumber(row.getFileNumber()).setRemark(row.getFileRemark())
                    .setFileType(row.getDeliverFileType())).collect(Collectors.toList()));
        }
    }
}
