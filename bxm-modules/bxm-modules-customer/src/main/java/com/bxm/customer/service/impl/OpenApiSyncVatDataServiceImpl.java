package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.OpenApiSyncVatData;
import com.bxm.customer.mapper.OpenApiSyncVatDataMapper;
import com.bxm.customer.service.IOpenApiSyncVatDataService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class OpenApiSyncVatDataServiceImpl extends ServiceImpl<OpenApiSyncVatDataMapper, OpenApiSyncVatData> implements IOpenApiSyncVatDataService {

    @Override
    public OpenApiSyncVatData selectBySyncCustomerId(Long sycCustomerId) {
        return getOne(new LambdaQueryWrapper<OpenApiSyncVatData>().eq(OpenApiSyncVatData::getSyncCustomerId, sycCustomerId), false);
    }

    @Override
    public List<OpenApiSyncVatData> selectBySyncRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiSyncVatData>().eq(OpenApiSyncVatData::getSycRecordId, recordId));
    }
}
