package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceRepairAccountTempBankInstrumentMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempBankInstrument;
import com.bxm.customer.service.ICustomerServiceRepairAccountTempBankInstrumentService;

/**
 * 补账临时的 材料交接银行票据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class CustomerServiceRepairAccountTempBankInstrumentServiceImpl extends ServiceImpl<CustomerServiceRepairAccountTempBankInstrumentMapper, CustomerServiceRepairAccountTempBankInstrument> implements ICustomerServiceRepairAccountTempBankInstrumentService
{
    @Autowired
    private CustomerServiceRepairAccountTempBankInstrumentMapper customerServiceRepairAccountTempBankInstrumentMapper;

    /**
     * 查询补账临时的 材料交接银行票据
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 补账临时的 材料交接银行票据
     */
    @Override
    public CustomerServiceRepairAccountTempBankInstrument selectCustomerServiceRepairAccountTempBankInstrumentById(Long id)
    {
        return customerServiceRepairAccountTempBankInstrumentMapper.selectCustomerServiceRepairAccountTempBankInstrumentById(id);
    }

    /**
     * 查询补账临时的 材料交接银行票据列表
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 补账临时的 材料交接银行票据
     */
    @Override
    public List<CustomerServiceRepairAccountTempBankInstrument> selectCustomerServiceRepairAccountTempBankInstrumentList(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument)
    {
        return customerServiceRepairAccountTempBankInstrumentMapper.selectCustomerServiceRepairAccountTempBankInstrumentList(customerServiceRepairAccountTempBankInstrument);
    }

    /**
     * 新增补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    @Override
    public int insertCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument)
    {
        customerServiceRepairAccountTempBankInstrument.setCreateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountTempBankInstrumentMapper.insertCustomerServiceRepairAccountTempBankInstrument(customerServiceRepairAccountTempBankInstrument);
    }

    /**
     * 修改补账临时的 材料交接银行票据
     * 
     * @param customerServiceRepairAccountTempBankInstrument 补账临时的 材料交接银行票据
     * @return 结果
     */
    @Override
    public int updateCustomerServiceRepairAccountTempBankInstrument(CustomerServiceRepairAccountTempBankInstrument customerServiceRepairAccountTempBankInstrument)
    {
        customerServiceRepairAccountTempBankInstrument.setUpdateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountTempBankInstrumentMapper.updateCustomerServiceRepairAccountTempBankInstrument(customerServiceRepairAccountTempBankInstrument);
    }

    /**
     * 批量删除补账临时的 材料交接银行票据
     * 
     * @param ids 需要删除的补账临时的 材料交接银行票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountTempBankInstrumentByIds(Long[] ids)
    {
        return customerServiceRepairAccountTempBankInstrumentMapper.deleteCustomerServiceRepairAccountTempBankInstrumentByIds(ids);
    }

    /**
     * 删除补账临时的 材料交接银行票据信息
     * 
     * @param id 补账临时的 材料交接银行票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountTempBankInstrumentById(Long id)
    {
        return customerServiceRepairAccountTempBankInstrumentMapper.deleteCustomerServiceRepairAccountTempBankInstrumentById(id);
    }
}
