package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 扣款异常状态变更策略
 *
 * 处理从"扣款异常(待确认)"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. DEDUCTION_EXCEPTION -> DEDUCTION_CLOSED (关闭扣款)
 * 2. DEDUCTION_EXCEPTION -> CONFIRMED_PENDING_DEDUCTION (重新扣款)
 * 3. DEDUCTION_EXCEPTION -> DEDUCTION_COMPLETED (异常处理完成，直接完成扣款)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class DeductionExceptionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED ||
               targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_CLOSED) {
            // 验证关闭扣款
            validateCloseDeduction(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            // 验证重新扣款
            validateRetryDeduction(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            // 验证异常处理完成
            validateExceptionResolved(order, request);
        } else {
            throwUnsupportedTransition("扣款异常", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION;
    }

    /**
     * 验证关闭扣款
     *
     * 适用场景：
     * 1. 客户拒绝扣款
     * 2. 扣款金额争议无法解决
     * 3. 业务取消，不再扣款
     */
    private void validateCloseDeduction(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateCloseOperation(request, "扣款");
        logValidationPassed("Close deduction", request.getDeliveryOrderNo());
    }

    /**
     * 验证重新扣款
     *
     * 适用场景：
     * 1. 异常问题已解决，可以重新尝试扣款
     * 2. 客户信息已更新，重新扣款
     */
    private void validateRetryDeduction(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateReason(request.getReason(), 5, "重新扣款");
        validateCustomerInfo(order);
        validateContactInfo(order);

        logValidationPassed("Retry deduction", request.getDeliveryOrderNo());
    }

    /**
     * 验证异常处理完成
     *
     * 适用场景：
     * 1. 异常已通过其他方式解决（如线下处理）
     * 2. 扣款已通过其他渠道完成
     */
    private void validateExceptionResolved(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateReason(request.getReason(), 10, "异常解决");
        validateRemark(request.getRemark(), "异常解决");

        logValidationPassed("Exception resolution", request.getDeliveryOrderNo());
    }
}
