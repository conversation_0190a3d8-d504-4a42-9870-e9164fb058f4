package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskAddVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskFinishVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchVO;
import com.bxm.customer.domain.BusinessTask;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.businessTask.*;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskDTO;
import com.bxm.customer.domain.vo.accoutingCashier.BatchUpdateBankAccountNumberVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskCommentVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskMattersNotesModifyVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.customer.domain.vo.businessTask.operate.*;
import com.bxm.customer.domain.vo.settlementOrder.task.BusinessDeptPeriodListForTaskVO;
import com.bxm.customer.domain.vo.settlementOrder.task.DeleteBusinessPeriodListForTaskVO;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.IBusinessTaskService;
import com.bxm.customer.service.IDownloadRecordService;
import com.bxm.customer.service.SettlementOrderService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 业务任务Controller
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/task")
@Api(tags = "业务任务")
public class BusinessTaskController extends BaseController {
    @Autowired
    private IBusinessTaskService businessTaskService;

    @Autowired
    private SettlementOrderService settlementOrderService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    /**
     * 查询业务任务列表
     */
    @RequiresPermissions("customer:task:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询业务任务列表", notes = "查询业务任务列表")
    public TableDataInfo list(BusinessTask businessTask) {
        startPage();
        List<BusinessTask> list = businessTaskService.selectBusinessTaskList(businessTask);
        return getDataTable(list);
    }

    /**
     * 导出业务任务列表
     */
    @RequiresPermissions("customer:task:export")
    @Log(title = "业务任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出业务任务列表", notes = "导出业务任务列表")
    public void export(HttpServletResponse response, BusinessTask businessTask) {
        List<BusinessTask> list = businessTaskService.selectBusinessTaskList(businessTask);
        ExcelUtil<BusinessTask> util = new ExcelUtil<BusinessTask>(BusinessTask.class);
        util.exportExcel(response, list, "业务任务数据");
    }

    /**
     * 获取业务任务详细信息
     */
    @RequiresPermissions("customer:task:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取业务任务详细信息", notes = "获取业务任务详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(businessTaskService.selectBusinessTaskById(id));
    }

    /**
     * 新增业务任务
     */
    @RequiresPermissions("customer:task:add")
    @Log(title = "业务任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增业务任务", notes = "新增业务任务")
    public AjaxResult add(@RequestBody BusinessTask businessTask) {
        return toAjax(businessTaskService.insertBusinessTask(businessTask));
    }

    /**
     * 修改业务任务
     */
    @RequiresPermissions("customer:task:edit")
    @Log(title = "业务任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改业务任务", notes = "修改业务任务")
    public AjaxResult edit(@RequestBody BusinessTask businessTask) {
        return toAjax(businessTaskService.updateBusinessTask(businessTask));
    }

    /**
     * 删除业务任务
     */
    @RequiresPermissions("customer:task:remove")
    @Log(title = "业务任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除业务任务", notes = "删除业务任务")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(businessTaskService.deleteBusinessTaskByIds(ids));
    }

    //****** start self method ******
    @RequestMapping("/businessTaskListForPeriod")
    @ApiOperation(value = "DS-分页获取任务-账期任务", notes = "分页获取任务-账期任务")
    public Result<IPage<BusinessTaskForPeriodDTO>> businessTaskList(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        return Result.ok(businessTaskService.businessTaskListForPeriod(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBusinessTaskListForPeriod")
    @ApiOperation(value = "DS-导出任务列表-账期任务", notes = "导出任务列表-账期任务")
    public void exportBusinessTaskListForPeriod(HttpServletResponse response, @RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        List<BusinessTaskForPeriodDTO> list = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 5000;
        vo.setPageSize(pageSize);
        while (true) {
            vo.setPageNum(pageNum);
            List<BusinessTaskForPeriodDTO> l = businessTaskService.businessTaskListForPeriod(deptId, vo).getRecords();
            if (!ObjectUtils.isEmpty(l)) {
                list.addAll(l);
                pageNum++;
            } else {
                break;
            }
        }
        ExcelUtil<BusinessTaskForPeriodDTO> util = new ExcelUtil<>(BusinessTaskForPeriodDTO.class);
        util.exportExcel(response, list, "账期任务");
    }

    @PostMapping("/exportBusinessTaskListForPeriodAndUpload")
    @ApiOperation(value = "DS-导出任务列表-账期任务", notes = "导出任务列表-账期任务")
    public Result exportBusinessTaskListForPeriodAndUpload(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        String title = "账期任务" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.BUSINESS_TASK_FOR_PERIOD);
        CompletableFuture.runAsync(() -> {
            try {
                List<BusinessTaskForPeriodDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<BusinessTaskForPeriodDTO> l = businessTaskService.businessTaskListForPeriod(vo.getDeptId(), vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<BusinessTaskForPeriodDTO> util = new ExcelUtil<>(BusinessTaskForPeriodDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @RequestMapping("/businessTaskListForManage")
    @ApiOperation(value = "DS-分页获取任务-任务管理", notes = "分页获取任务-任务管理")
    public Result<IPage<BusinessTaskForManageDTO>> businessTaskListForManage(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        return Result.ok(businessTaskService.businessTaskListForManage(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBusinessTaskListForManage")
    @ApiOperation(value = "DS-导出任务列表-任务管理", notes = "导出任务列表-任务管理")
    public void exportBusinessTaskListForManage(HttpServletResponse response, @RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        List<BusinessTaskForManageDTO> list = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 5000;
        vo.setPageSize(pageSize);
        while (true) {
            vo.setPageNum(pageNum);
            List<BusinessTaskForManageDTO> l = businessTaskService.businessTaskListForManage(deptId, vo).getRecords();
            if (!ObjectUtils.isEmpty(l)) {
                list.addAll(l);
                pageNum++;
            } else {
                break;
            }
        }
        ExcelUtil<BusinessTaskForManageDTO> util = new ExcelUtil<>(BusinessTaskForManageDTO.class);
        util.exportExcel(response, list, "任务管理");
    }

    @PostMapping("/exportBusinessTaskListForManagerAndUpload")
    @ApiOperation(value = "DS-导出任务列表-任务管理", notes = "导出任务列表-任务管理")
    public Result exportBusinessTaskListForManagerAndUpload(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        String title = "任务管理" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.BUSINESS_TASK_FOR_MANAGER);
        CompletableFuture.runAsync(() -> {
            try {
                List<BusinessTaskForManageDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<BusinessTaskForManageDTO> l = businessTaskService.businessTaskListForManage(vo.getDeptId(), vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<BusinessTaskForManageDTO> util = new ExcelUtil<>(BusinessTaskForManageDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @RequestMapping("/businessTaskListForMy")
    @ApiOperation(value = "DS-分页获取任务-我的任务", notes = "分页获取任务-我的任务")
    public Result<IPage<BusinessTaskForMyDTO>> businessTaskListForMy(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        return Result.ok(businessTaskService.businessTaskListForMy(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBusinessTaskListForMy")
    @ApiOperation(value = "DS-导出任务列表-我的任务", notes = "导出任务列表-我的任务")
    public void exportBusinessTaskListForMy(HttpServletResponse response, @RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        List<BusinessTaskForMyDTO> list = Lists.newArrayList();
        Integer pageNum = 1;
        Integer pageSize = 5000;
        vo.setPageSize(pageSize);
        while (true) {
            vo.setPageNum(pageNum);
            List<BusinessTaskForMyDTO> l = businessTaskService.businessTaskListForMy(deptId, vo).getRecords();
            if (!ObjectUtils.isEmpty(l)) {
                list.addAll(l);
                pageNum++;
            } else {
                break;
            }
        }
        ExcelUtil<BusinessTaskForMyDTO> util = new ExcelUtil<>(BusinessTaskForMyDTO.class);
        util.exportExcel(response, list, "我的任务");
    }

    @PostMapping("/exportBusinessTaskListForMyAndUpload")
    @ApiOperation(value = "DS-导出任务列表-我的任务", notes = "导出任务列表-我的任务")
    public Result exportBusinessTaskListForMyAndUpload(@RequestHeader("deptId") Long deptId, BusinessTaskVO vo) {
        String title = "任务管理" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.BUSINESS_TASK_FOR_MY);
        CompletableFuture.runAsync(() -> {
            try {
                List<BusinessTaskForMyDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<BusinessTaskForMyDTO> l = businessTaskService.businessTaskListForMy(vo.getDeptId(), vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<BusinessTaskForMyDTO> util = new ExcelUtil<>(BusinessTaskForMyDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @GetMapping("/getDetail")
    @ApiOperation(value = "DS-获取任务详情", notes = "获取任务详情")
    public Result<BusinessTaskDetailDTO> getDetail(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(businessTaskService.businessTaskDetail(deptId, id));
    }

    @GetMapping("/getAdminUser")
    @ApiOperation(value = "DS-获取监管人", notes = "获取监管人")
    public Result<List<SelectDTO>> getAdminUser(@RequestHeader("deptId") Long deptId) {
        return Result.ok(businessTaskService.getAdminUser(deptId));
    }

    @GetMapping("/getExecuteUser")
    @ApiOperation(value = "DS-获取执行人", notes = "获取执行人")
    public Result<List<SelectDTO>> getExecuteUser(@RequestHeader("deptId") Long deptId) {
        return Result.ok(businessTaskService.getExecuteUser(deptId));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/addBatch")
    @ApiOperation(value = "DS-新增", notes = "新增")
    public Result<TCommonOperateDTO<BusinessTask>> addBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid AddBatchVO vo) {
        return Result.ok(businessTaskService.addBatch(deptId, vo));
    }

    @PostMapping("/remoteAddSingle")
    @ApiIgnore
    @InnerAuth
    public Result remoteAddSingle(@RequestBody RemoteBusinessTaskAddVO vo) {
        businessTaskService.remoteAddSingle(vo);
        return Result.ok();
    }

    @PostMapping("/getByPeriodIdsAndTypeAndItemType")
    @ApiIgnore
    @InnerAuth
    public Result<List<BusinessTask>> getByPeriodIdsAndTypeAndItemType(@RequestBody RemoteBusinessTaskSearchVO vo) {
        return Result.ok(businessTaskService.getByPeriodIdsAndTypeAndItemType(vo));
    }

    @PostMapping("/getByPeriodAndBankAccountNumber")
    @ApiIgnore
    @InnerAuth
    public Result<List<BusinessTask>> getByPeriodAndBankAccountNumber(@RequestBody RemoteBusinessTaskSearchV2VO vo) {
        return Result.ok(businessTaskService.getByPeriodAndBankAccountNumber(vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/updateBatch")
    @ApiOperation(value = "DS-修改", notes = "修改")
    public Result<TCommonOperateDTO<BusinessTask>> updateBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateBatchVO vo) {
        return Result.ok(businessTaskService.updateBatch(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/closeBatch")
    @ApiOperation(value = "DS-关闭", notes = "关闭")
    public Result<TCommonOperateDTO<BusinessTask>> closeBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid CloseBatchVO vo) {
        return Result.ok(businessTaskService.closeBatch(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/deleteBatch")
    @ApiOperation(value = "DS-删除", notes = "删除")
    public Result<TCommonOperateDTO<BusinessTask>> deleteBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DeleteBatchVO vo) {
        return Result.ok(businessTaskService.deleteBatch(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/distributeBatch")
    @ApiOperation(value = "DS-分单", notes = "分单")
    public Result<TCommonOperateDTO<BusinessTask>> distributeBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DistributeBatchVO vo) {
        return Result.ok(businessTaskService.distributeBatch(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/assignBatch")
    @ApiOperation(value = "DS-派单", notes = "派单")
    public Result<TCommonOperateDTO<BusinessTask>> assignBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid AssignBatchVO vo) {
        return Result.ok(businessTaskService.assignBatch(deptId, vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/finishBatch")
    @ApiOperation(value = "DS-完成", notes = "完成")
    public Result<TCommonOperateDTO<BusinessTask>> finishBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid FinishBatchVO vo) {
        return Result.ok(businessTaskService.finishBatch(deptId, vo));
    }

    @PostMapping("/remoteFinishSingle")
    @ApiIgnore
    @InnerAuth
    public Result remoteFinishSingle(@RequestBody RemoteBusinessTaskFinishVO vo) {
        businessTaskService.remoteFinishSingle(vo);
        return Result.ok();
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/checkBatch")
    @ApiOperation(value = "DS-审核", notes = "审核")
    public Result<TCommonOperateDTO<BusinessTask>> checkBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid CheckBatchVO vo) {
        return Result.ok(businessTaskService.checkBatch(deptId, vo));
    }

    @PostMapping("/checkSingle")
    @ApiOperation(value = "DS-审核(单个)", notes = "审核(单个)")
    public Result checkSingle(@RequestHeader("deptId") Long deptId, @RequestBody @Valid CheckSingleVO vo) {
        businessTaskService.checkSingle(deptId, vo);
        return Result.ok();
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/handleExceptionBatch")
    @ApiOperation(value = "DS-处理异常", notes = "处理异常")
    public Result<TCommonOperateDTO<BusinessTask>> handleExceptionBatch(@RequestHeader("deptId") Long deptId, @RequestBody @Valid HandleExceptionBatchVO vo) {
        return Result.ok(businessTaskService.handleExceptionBatch(deptId, vo));
    }

    //******** START 仿照 结算单 的 任务的几个接口
    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/businessPeriodListForTask")
    @ApiOperation("DS-新建任务选了条件后的下一步")
    public Result<BusinessPeriodListForTaskDTO> businessPeriodListForTask(@RequestBody @Valid BusinessDeptPeriodListForTaskVO vo) {
        if (vo.getType() == null) {
            throw new ServiceException("type 不能为空");
        }
        if (vo.getItemType() == null) {
            throw new ServiceException("itemType 不能为空");
        }

        return Result.ok(settlementOrderService.businessPeriodListForTask(vo));
    }

    @PostMapping("/freshBusinessPeriodListForTask")
    @ApiOperation("DS-重新获取预生成任务单前的信息")
    public Result<BusinessPeriodListForTaskDTO> freshBusinessPeriodListForTask(@RequestBody @Valid BusinessDeptPeriodListForTaskVO vo) {
        return Result.ok(settlementOrderService.freshBusinessPeriodListForTask(vo));
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/deleteBusinessPeriodListForTask")
    @ApiOperation("DS-删除结算单明细")
    public Result<Boolean> deleteBusinessPeriodListForTask(@RequestBody DeleteBusinessPeriodListForTaskVO vo) {
        settlementOrderService.deleteBusinessPeriodListForTask(vo);
        return Result.ok();
    }

    @Log(title = "业务任务", businessType = BusinessType.OTHER)
    @PostMapping("/createTask")
    @ApiOperation("DS-生成任务单")
    public Result<TCommonOperateDTO<BusinessTask>> createTask(@RequestHeader("deptId") Long deptId, @RequestBody @Valid CreateTaskVO vo) {
        return Result.ok(businessTaskService.createTask(deptId, vo));
    }
    //******** END 仿照 结算单 的 任务的几个接口

    @PostMapping("/comment")
    @ApiOperation("DS-评论")
    public Result comment(@RequestHeader("deptId") Long deptId, @RequestBody @Valid BusinessTaskCommentVO vo) {
        if (StringUtils.isEmpty(vo.getContent()) && ObjectUtils.isEmpty(vo.getFiles())) {
            return Result.fail("内容和附件不能都为空");
        }
        businessTaskService.comment(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getBusinessTaskFiles")
    @ApiOperation("查看任务附件")
    public Result<List<CommonFileVO>> getBusinessTaskFiles(@RequestParam("id") @ApiParam("任务id") Long id,
                                                           @RequestParam("fileType") @ApiParam("查看文件类型,10-银行材料") Integer fileType) {
        return Result.ok(businessTaskService.getBusinessTaskFiles(id, fileType));
    }

    @PostMapping("/modifyMattersNotes")
    @ApiOperation("编辑事项备忘")
    public Result modifyMattersNotes(@RequestBody BusinessTaskMattersNotesModifyVO vo, @RequestHeader("deptId") Long deptId) {
        businessTaskService.modifyMattersNotes(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/batchUpdateBankAccountNumber")
    @ApiOperation("批量编辑银行账号")
    public Result<TCommonOperateDTO<BusinessTask>> batchUpdateBankAccountNumber(@RequestBody BatchUpdateBankAccountNumberVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(businessTaskService.batchUpdateBankAccountNumber(vo, deptId));
    }

    @PostMapping("/managerDelete")
    @ApiOperation("管理删除")
    public Result<TCommonOperateDTO<BusinessTask>> managerDelete(@RequestBody DeleteBatchVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(businessTaskService.managerDelete(vo, deptId));
    }
}
