package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.WorkOrderTypeDeptRelation;

/**
 * 工单类型组织可见Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
public interface IWorkOrderTypeDeptRelationService extends IService<WorkOrderTypeDeptRelation>
{
    /**
     * 查询工单类型组织可见
     * 
     * @param id 工单类型组织可见主键
     * @return 工单类型组织可见
     */
    public WorkOrderTypeDeptRelation selectWorkOrderTypeDeptRelationById(Long id);

    /**
     * 查询工单类型组织可见列表
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 工单类型组织可见集合
     */
    public List<WorkOrderTypeDeptRelation> selectWorkOrderTypeDeptRelationList(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 新增工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    public int insertWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 修改工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    public int updateWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 批量删除工单类型组织可见
     * 
     * @param ids 需要删除的工单类型组织可见主键集合
     * @return 结果
     */
    public int deleteWorkOrderTypeDeptRelationByIds(Long[] ids);

    /**
     * 删除工单类型组织可见信息
     * 
     * @param id 工单类型组织可见主键
     * @return 结果
     */
    public int deleteWorkOrderTypeDeptRelationById(Long id);
}
