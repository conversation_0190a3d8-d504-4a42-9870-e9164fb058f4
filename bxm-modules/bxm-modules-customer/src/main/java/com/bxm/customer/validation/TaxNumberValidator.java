package com.bxm.customer.validation;

import com.bxm.common.core.utils.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 税号验证器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class TaxNumberValidator implements ConstraintValidator<TaxNumber, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(TaxNumber constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(value)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(value)) {
            return false;
        }

        // 去除空格
        value = value.trim();

        // 统一社会信用代码：18位，第1位为数字或大写字母，第2-9位为数字，第10-17位为数字或大写字母，第18位为数字或大写字母
        if (value.length() == 18) {
            return value.matches("[0-9A-Z][0-9]{8}[0-9A-Z]{8}[0-9A-Z]");
        }

        // 旧版税号：15位数字
        if (value.length() == 15) {
            return value.matches("\\d{15}");
        }

        return false;
    }
}
