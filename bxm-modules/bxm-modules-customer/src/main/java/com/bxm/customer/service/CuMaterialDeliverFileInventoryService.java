package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import com.bxm.customer.mapper.MaterialDeliverFileInventoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class CuMaterialDeliverFileInventoryService extends ServiceImpl<MaterialDeliverFileInventoryMapper, MaterialDeliverFileInventory> {

    // 检查是否存在相同的数据
    public boolean checkRecordExists(String materialFileName, String period, Long customerServiceId) {
        QueryWrapper<MaterialDeliverFileInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("material_file_name", materialFileName)
                .eq("period", period)
                .eq("customer_service_id", customerServiceId)
                .eq("is_del", 0);  // 确保不是删除记录

        // 使用 count 查询符合条件的记录数，如果大于 0，则表示记录存在
        return count(queryWrapper) > 0;
    }

}
