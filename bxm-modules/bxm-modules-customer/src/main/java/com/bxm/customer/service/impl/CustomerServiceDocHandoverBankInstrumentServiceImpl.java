package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceDocHandoverBankInstrumentMapper;
import com.bxm.customer.domain.CustomerServiceDocHandoverBankInstrument;
import com.bxm.customer.service.ICustomerServiceDocHandoverBankInstrumentService;

/**
 * 材料交接银行票据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
public class CustomerServiceDocHandoverBankInstrumentServiceImpl extends ServiceImpl<CustomerServiceDocHandoverBankInstrumentMapper, CustomerServiceDocHandoverBankInstrument> implements ICustomerServiceDocHandoverBankInstrumentService
{
    @Autowired
    private CustomerServiceDocHandoverBankInstrumentMapper customerServiceDocHandoverBankInstrumentMapper;

    /**
     * 查询材料交接银行票据
     * 
     * @param id 材料交接银行票据主键
     * @return 材料交接银行票据
     */
    @Override
    public CustomerServiceDocHandoverBankInstrument selectCustomerServiceDocHandoverBankInstrumentById(Long id)
    {
        return customerServiceDocHandoverBankInstrumentMapper.selectCustomerServiceDocHandoverBankInstrumentById(id);
    }

    /**
     * 查询材料交接银行票据列表
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 材料交接银行票据
     */
    @Override
    public List<CustomerServiceDocHandoverBankInstrument> selectCustomerServiceDocHandoverBankInstrumentList(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument)
    {
        return customerServiceDocHandoverBankInstrumentMapper.selectCustomerServiceDocHandoverBankInstrumentList(customerServiceDocHandoverBankInstrument);
    }

    /**
     * 新增材料交接银行票据
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 结果
     */
    @Override
    public int insertCustomerServiceDocHandoverBankInstrument(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument)
    {
        customerServiceDocHandoverBankInstrument.setCreateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverBankInstrumentMapper.insertCustomerServiceDocHandoverBankInstrument(customerServiceDocHandoverBankInstrument);
    }

    /**
     * 修改材料交接银行票据
     * 
     * @param customerServiceDocHandoverBankInstrument 材料交接银行票据
     * @return 结果
     */
    @Override
    public int updateCustomerServiceDocHandoverBankInstrument(CustomerServiceDocHandoverBankInstrument customerServiceDocHandoverBankInstrument)
    {
        customerServiceDocHandoverBankInstrument.setUpdateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverBankInstrumentMapper.updateCustomerServiceDocHandoverBankInstrument(customerServiceDocHandoverBankInstrument);
    }

    /**
     * 批量删除材料交接银行票据
     * 
     * @param ids 需要删除的材料交接银行票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverBankInstrumentByIds(Long[] ids)
    {
        return customerServiceDocHandoverBankInstrumentMapper.deleteCustomerServiceDocHandoverBankInstrumentByIds(ids);
    }

    /**
     * 删除材料交接银行票据信息
     * 
     * @param id 材料交接银行票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverBankInstrumentById(Long id)
    {
        return customerServiceDocHandoverBankInstrumentMapper.deleteCustomerServiceDocHandoverBankInstrumentById(id);
    }
}
