package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderCountDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDetailDTO;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.domain.vo.workOrder.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IWorkOrderService extends IService<WorkOrder>
{

    IPage<WorkOrderDTO> workOrderList(WorkOrderSearchVO vo, Long deptId);

    WorkOrderDetailDTO workOrderDetail(Long id, Long deptId);

    void createWorkOrder(WorkOrderCreateVO vo, Long deptId);

    void followUp(WorkOrderFollowUpVO vo, Long deptId);

    Boolean remoteFollowUp(WorkOrderFollowUpVO vo);

    void commentWorkOrder(WorkOrderCommentVO vo, Long deptId);

    void transmit(WorkOrderTransmitVO vo, Long deptId);

    Boolean remoteTransmit(WorkOrderTransmitVO vo);

    WorkOrderCountDTO workOrderStatistic(Long deptId);

    TCommonOperateDTO<WorkOrder> transmitBatch(WorkOrderTransmitBatchVO vo, Long deptId);

    void restartWorkOrder(WorkOrderRestartVO vo, Long deptId);

    List<CommonDeptCountDTO> workOrderInitiateDeptList(UserDeptDTO userDeptDTO, Integer tabType);

    List<CommonDeptCountDTO> workOrderUndertakeDeptList(UserDeptDTO userDeptDTO, Integer tabType);

    void workOrderCloseTask();

    void workOrderConfirmTask();

    void createMaterialPushWorkOrder(String customerName, String bankName, String bankAccountNumber, OperateUserInfoDTO operateUserInfoDTO, List<CommonFileVO> files, String materialDeliverNumber);

    void updateWorkOrderDeptId(Long customerServiceId, Long oldDeptId, Long newDeptId, String operName, Long deptId, Long userId, String dispatchType);

    void confirmWorkOrder(WorkOrderConfirmVO vo, Long deptId);

    TCommonOperateDTO<WorkOrder> modifyDdl(WorkOrderModifyDdlVO vo, Long deptId);

    WorkOrderDetailDTO remoteWorkOrderDetail(Long id, Long userId);

    List<WorkOrderDTO> remoteWorkOrderListForXm(WorkOrderSearchVO vo);

    Boolean remoteCommentWorkOrder(WorkOrderCommentVO vo);

    Boolean remoteConfirmWorkOrder(WorkOrderConfirmVO vo);
}
