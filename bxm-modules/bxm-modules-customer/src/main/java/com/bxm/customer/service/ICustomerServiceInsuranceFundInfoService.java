package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceInsuranceFundInfo;

/**
 * 客户服务五险一金信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICustomerServiceInsuranceFundInfoService extends IService<CustomerServiceInsuranceFundInfo>
{
    /**
     * 查询客户服务五险一金信息
     * 
     * @param id 客户服务五险一金信息主键
     * @return 客户服务五险一金信息
     */
    public CustomerServiceInsuranceFundInfo selectCustomerServiceInsuranceFundInfoById(Long id);

    /**
     * 查询客户服务五险一金信息列表
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 客户服务五险一金信息集合
     */
    public List<CustomerServiceInsuranceFundInfo> selectCustomerServiceInsuranceFundInfoList(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo);

    /**
     * 新增客户服务五险一金信息
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 结果
     */
    public int insertCustomerServiceInsuranceFundInfo(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo);

    /**
     * 修改客户服务五险一金信息
     * 
     * @param customerServiceInsuranceFundInfo 客户服务五险一金信息
     * @return 结果
     */
    public int updateCustomerServiceInsuranceFundInfo(CustomerServiceInsuranceFundInfo customerServiceInsuranceFundInfo);

    /**
     * 批量删除客户服务五险一金信息
     * 
     * @param ids 需要删除的客户服务五险一金信息主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInsuranceFundInfoByIds(Long[] ids);

    /**
     * 删除客户服务五险一金信息信息
     * 
     * @param id 客户服务五险一金信息主键
     * @return 结果
     */
    public int deleteCustomerServiceInsuranceFundInfoById(Long id);
}
