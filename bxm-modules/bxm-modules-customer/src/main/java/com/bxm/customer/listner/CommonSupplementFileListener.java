package com.bxm.customer.listner;

import com.alibaba.fastjson.JSONObject;
import com.bxm.common.core.enums.DeliverFileType;
import com.bxm.customer.domain.vo.xqy.XqySupplementVO;
import com.bxm.customer.service.XqyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "customerCommonSupplement" + "${spring.profiles.active}", topic = "openapiCommonSupplement_" + "${spring.profiles.active}", consumeMode = ConsumeMode.ORDERLY, selectorExpression = "common")
public class CommonSupplementFileListener implements RocketMQListener<MessageExt> {

    @Autowired
    private XqyService xqyService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到公共补充消息，messageId:{}, message:{}", message.getMsgId(), new String(message.getBody()));
        try {
            XqySupplementVO xqySupplementVO = JSONObject.parseObject(new String(message.getBody()), XqySupplementVO.class);
            if (Objects.isNull(xqySupplementVO.getFileType())) {
                xqySupplementVO.setFileType(DeliverFileType.REPORT.getCode());
            }
            xqyService.commonSupplementFile(xqySupplementVO);
        } catch (Exception e) {
            log.error("处理公共补充消息异常:{}", e.getMessage());
        }
    }
}
