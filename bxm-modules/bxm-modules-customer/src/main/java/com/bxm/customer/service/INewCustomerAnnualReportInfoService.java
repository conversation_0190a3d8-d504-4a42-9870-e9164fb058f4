package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerAnnualReportInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferOtherInfoDTO;

/**
 * 新户流转工商年报信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerAnnualReportInfoService extends IService<NewCustomerAnnualReportInfo>
{
    /**
     * 查询新户流转工商年报信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 新户流转工商年报信息
     */
    public NewCustomerAnnualReportInfo selectNewCustomerAnnualReportInfoById(Long id);

    /**
     * 查询新户流转工商年报信息列表
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 新户流转工商年报信息集合
     */
    public List<NewCustomerAnnualReportInfo> selectNewCustomerAnnualReportInfoList(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 新增新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    public int insertNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 修改新户流转工商年报信息
     * 
     * @param newCustomerAnnualReportInfo 新户流转工商年报信息
     * @return 结果
     */
    public int updateNewCustomerAnnualReportInfo(NewCustomerAnnualReportInfo newCustomerAnnualReportInfo);

    /**
     * 批量删除新户流转工商年报信息
     * 
     * @param ids 需要删除的新户流转工商年报信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerAnnualReportInfoByIds(Long[] ids);

    /**
     * 删除新户流转工商年报信息信息
     * 
     * @param id 新户流转工商年报信息主键
     * @return 结果
     */
    public int deleteNewCustomerAnnualReportInfoById(Long id);

    NewCustomerTransferOtherInfoDTO buildOtherInfo(NewCustomerInfo newCustomerInfo);

    NewCustomerAnnualReportInfo selectByCustomerId(Long customerId);

    Map<Long, NewCustomerAnnualReportInfo> selectMapByCustomerIds(List<Long> customerIds);

    void updateByOtherInfo(NewCustomerTransferOtherInfoDTO otherInfo);
}
