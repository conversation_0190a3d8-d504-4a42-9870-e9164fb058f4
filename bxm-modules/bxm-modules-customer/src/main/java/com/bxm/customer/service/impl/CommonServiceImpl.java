package com.bxm.customer.service.impl;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.service.CommonService;
import com.bxm.system.api.RemoteDeptService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Override
    public List<Long> getAllChildrenDeptIdsByDeptIds(Long deptId, String deptIds) {
        Set<Long> deptIdsSet = new HashSet<>();
        if (!Objects.isNull(deptId)) {
            deptIdsSet.addAll(remoteDeptService.getAllChildrenIdByTopDeptId(deptId).getDataThrowException());
        }
        if (!StringUtils.isEmpty(deptIds)) {
            for (String deptIdStr : deptIds.split(",")) {
                deptIdsSet.addAll(remoteDeptService.getAllChildrenIdByTopDeptId(Long.parseLong(deptIdStr)).getDataThrowException());
            }
        }
        return ObjectUtils.isEmpty(deptIdsSet) ? Lists.newArrayList() : new ArrayList<>(deptIdsSet);
    }
}
