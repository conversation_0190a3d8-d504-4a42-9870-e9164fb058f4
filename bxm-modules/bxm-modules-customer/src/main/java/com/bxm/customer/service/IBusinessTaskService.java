package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskAddVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskFinishVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.accoutingCashier.BankPaymentTaskDTO;
import com.bxm.customer.domain.dto.businessTask.*;
import com.bxm.customer.domain.vo.accoutingCashier.BatchUpdateBankAccountNumberVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskCommentVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskMattersNotesModifyVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.customer.domain.vo.businessTask.operate.*;
import com.bxm.thirdpart.api.domain.BanksEnterprisesExtractDTO;
import com.bxm.thirdpart.api.domain.CheckFilesDTO;
import com.bxm.thirdpart.api.domain.GenerateVoucherDTO;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 业务任务Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IBusinessTaskService extends IService<BusinessTask> {
    /**
     * 查询业务任务
     *
     * @param id 业务任务主键
     * @return 业务任务
     */
    public BusinessTask selectBusinessTaskById(Long id);

    /**
     * 查询业务任务列表
     *
     * @param businessTask 业务任务
     * @return 业务任务集合
     */
    public List<BusinessTask> selectBusinessTaskList(BusinessTask businessTask);

    /**
     * 新增业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    public int insertBusinessTask(BusinessTask businessTask);

    /**
     * 修改业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    public int updateBusinessTask(BusinessTask businessTask);

    /**
     * 批量删除业务任务
     *
     * @param ids 需要删除的业务任务主键集合
     * @return 结果
     */
    public int deleteBusinessTaskByIds(Long[] ids);

    /**
     * 删除业务任务信息
     *
     * @param id 业务任务主键
     * @return 结果
     */
    public int deleteBusinessTaskById(Long id);

    //****** start self method ******
    //分页获取任务-账期任务
    IPage<BusinessTaskForPeriodDTO> businessTaskListForPeriod(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForPeriod(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForPeriod(Long deptId, BusinessTaskVO vo);

    //分页获取任务-任务管理
    IPage<BusinessTaskForManageDTO> businessTaskListForManage(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForManager(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForManager(Long deptId, BusinessTaskVO vo);

    //分页获取任务-我的任务
    IPage<BusinessTaskForMyDTO> businessTaskListForMy(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskCountForMy(Long deptId, BusinessTaskVO vo);

    List<BusinessTaskAccountingDeptCountDTO> businessTaskBusinessDeptCountForMy(Long deptId, BusinessTaskVO vo);

    //恩物详情
    BusinessTaskDetailDTO businessTaskDetail(Long deptId, Long id);

    //获取监管人
    List<SelectDTO> getAdminUser(Long deptId);

    //执行人
    List<SelectDTO> getExecuteUser(Long deptId);

    //批量新建
    TCommonOperateDTO<BusinessTask> addBatch(Long deptId, AddBatchVO vo);

    void remoteAddSingle(RemoteBusinessTaskAddVO vo);

    //批量修改
    TCommonOperateDTO<BusinessTask> updateBatch(Long deptId, UpdateBatchVO vo);

    TCommonOperateDTO<BusinessTask> closeBatch(Long deptId, CloseBatchVO vo);

    TCommonOperateDTO<BusinessTask> deleteBatch(Long deptId, DeleteBatchVO vo);

    TCommonOperateDTO<BusinessTask> distributeBatch(Long deptId, DistributeBatchVO vo);

    TCommonOperateDTO<BusinessTask> assignBatch(Long deptId, AssignBatchVO vo);

    TCommonOperateDTO<BusinessTask> finishBatch(Long deptId, FinishBatchVO vo);

    void remoteFinishSingle(RemoteBusinessTaskFinishVO vo);

    TCommonOperateDTO<BusinessTask> checkBatch(Long deptId, CheckBatchVO vo);

    void checkSingle(Long deptId, CheckSingleVO vo);

    TCommonOperateDTO<BusinessTask> handleExceptionBatch(Long deptId, HandleExceptionBatchVO vo);

    TCommonOperateDTO<BusinessTask> createTask(Long deptId, CreateTaskVO vo);

    List<BusinessTask> getByPeriodIdsAndTypeAndItemType(RemoteBusinessTaskSearchVO vo);

    void comment(Long deptId, BusinessTaskCommentVO vo);

    List<BusinessTask> selectWaitFinishTask(Integer itemType, Long userId);

    List<BankPaymentTaskDTO> getBankPaymentTaskListByPeriodIdAndBankAccountNumber(Long customerServicePeriodMonthId, String bankAccountNumber);

    void createBankTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CustomerServiceCashierAccountingFile> files, Long deptId, Long userId, String operName, LocalDateTime operTime, String ddl, Long adminUserId);

    Long createBankTaskV2(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime);

    void closeOldBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, Long deptId, Long userId, String operName, LocalDateTime operTime, String operRemark);

    BusinessTask createBankTaskV3(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime, Integer finishResult, Integer status, R banksEnterprisesExtractResp);

    BusinessTask createBankTaskV4(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, Long deptId, Long userId, String operName, LocalDateTime operTime, Integer finishResult, Integer status);

    List<CommonFileVO> getBusinessTaskFiles(Long id, Integer fileType);

    void modifyMattersNotes(BusinessTaskMattersNotesModifyVO vo, Long deptId);

    List<BusinessTask> getByPeriodAndBankAccountNumber(RemoteBusinessTaskSearchV2VO vo);

    void updateBankInfoByCustomerServiceId(Long customerServiceId, CustomerServiceBankAccount oldBankAccount, String bankAccountNumber, String bankName, Long deptId, Long userId, String operName);

    void exceptionDealByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> checkFileResp);

    void noFlowByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> checkFileResp);

    void normalCompleteByCheckFile(BusinessTask businessTask, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> checkFileResp);

    void waitInAccountByCheckFile(BusinessTask businessTask, LocalDateTime operTime);

    void exceptionDealByExtract(Long businessTaskId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType);

    void noFlowDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType);

    Long waitInAccountDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType);

    Boolean checkBusinessTaskClose(CCustomerService customerService, CustomerServicePeriodMonth customerServicePeriodMonth);

    void autoDispatchBusinessTask(Long businessTaskId, CCustomerService customerService);

    Long exceptionWaitDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType);

    void normalCompleteDeal(Long businessTaskId, LocalDateTime operTime, R resp, String operator, Long userId, Long deptId, String uuid, Integer checkType);

    TCommonOperateDTO<BusinessTask> batchUpdateBankAccountNumber(BatchUpdateBankAccountNumberVO vo, Long deptId);

    TCommonOperateDTO<BusinessTask> managerDelete(DeleteBatchVO vo, Long deptId);

    boolean existsNeedCheckTask(CustomerServiceCashierAccounting customerServiceCashierAccounting);

    Map<String, List<BusinessTask>> getBatchNeedCheckBusinessTaskByAccountingCashier(List<CustomerServiceCashierAccounting> cashierList);

    void dealBusinessTask(Integer deliverStatus, CustomerServiceCashierAccounting customerServiceCashierAccounting, String operName, Long userId, Long deptId, String operRemark);

    void dealBusinessTask(Integer deliverStatus, List<CustomerServiceCashierAccounting> cashierList, String operName, Long userId, Long deptId);

    void businessTaskGenerateVoucher(Long businessTaskId, LocalDateTime operTime, R<GenerateVoucherDTO> generateVoucherResp, String operator, Long userId, Long deptId, String uuid, Object o);

    void overTimeClose(Long businessTaskId, LocalDateTime operTime, Long userId, Long deptId, String operator);

    void noDispatchClose(Long businessTaskId, LocalDateTime operTime, Long deptId, Long userId, String operName);

    void checkFileClose(Long businessTaskId, LocalDateTime operTime, R<CheckFilesDTO> checkFileResp, String operator, Long userId, Long deptId, String uuid, Integer checkType);

    void waitInAccountBusinessTaskAutoCloseTask();
}
