package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.dto.RemoteSendQualityCheckingTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteQualityCheckingCreateVO;
import com.bxm.customer.api.domain.vo.RemoteSendQualityCheckingTaskVO;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.qualityChecking.*;
import com.bxm.customer.domain.vo.qualityChecking.*;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.IDownloadRecordService;
import com.bxm.customer.service.QualityCheckingService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/quality/checking")
@Api(tags = "质检相关")
public class QualityCheckingController {

    @Autowired
    private QualityCheckingService qualityCheckingService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    @GetMapping("/qualityCheckingResultPageList")
    @ApiOperation("质检结果列表")
    public Result<IPage<QualityCheckingResultDTO>> qualityCheckingResultPageList(@RequestHeader("deptId") Long deptId,
                                                                                 QualityCheckingResultVO vo) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(qualityCheckingService.qualityCheckingResultPageList(vo));
    }

    @PostMapping("/qualityCheckingResultExportAndUpload")
    @ApiOperation("质检结果导出（异步导出）")
    public Result qualityCheckingResultExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                      QualityCheckingResultVO vo) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        String title = "质检结果" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.QUALITY_CHECKING_RESULT);
        CompletableFuture.runAsync(() -> {
            try {
                List<QualityCheckingResultDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<QualityCheckingResultDTO> l = qualityCheckingService.qualityCheckingResultPageList(vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                if (StringUtils.isEmpty(vo.getExportTypes())) {
                    ExcelUtil<QualityCheckingResultDTO> util = new ExcelUtil<>(QualityCheckingResultDTO.class);
                    asyncService.uploadExport(util, list, title, downloadRecordId);
                } else {
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, QualityCheckingResultDTO.class);
                    dataMap.put(title, list);
                    asyncService.uploadExport(qualityCheckingService.buildQualityCheckingResultFiles(vo.getExportTypes(), list), dataMap, sheetClassMap, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @GetMapping("/qualityCheckingRecordPageList")
    @ApiOperation("质检记录列表")
    public Result<IPage<QualityCheckingRecordDTO>> qualityCheckingRecordPageList(@RequestHeader("deptId") Long deptId,
                                                                                 QualityCheckingRecordVO vo) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(qualityCheckingService.qualityCheckingRecordPageList(vo));
    }

    @PostMapping("/qualityCheckingRecordExportAndUpload")
    @ApiOperation("质检记录导出（异步导出）")
    public Result qualityCheckingRecordExportAndUpload(@RequestHeader("deptId") Long deptId,
                                                       QualityCheckingRecordVO vo) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        String title = "质检记录" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.QUALITY_CHECKING_RECORD);
        CompletableFuture.runAsync(() -> {
            try {
                List<QualityCheckingRecordDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<QualityCheckingRecordDTO> l = qualityCheckingService.qualityCheckingRecordPageList(vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                if (StringUtils.isEmpty(vo.getExportTypes())) {
                    ExcelUtil<QualityCheckingRecordDTO> util = new ExcelUtil<>(QualityCheckingRecordDTO.class);
                    asyncService.uploadExport(util, list, title, downloadRecordId);
                } else {
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, QualityCheckingRecordDTO.class);
                    dataMap.put(title, list);
                    asyncService.uploadExport(qualityCheckingService.buildQualityCheckingRecordFiles(vo.getExportTypes(), list), dataMap, sheetClassMap, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/checkQualityResult")
    @ApiOperation("发起检查，单个检查传qualityCheckingResultId，批量检查传qualityCheckingResultIds，区分一下")
    public Result<TCommonOperateDTO<QualityCheckingResult>> checkQualityResult(@RequestHeader("deptId") Long deptId,
                                                                  @RequestBody QualityCheckingResultCheckVO vo) {
        return Result.ok(qualityCheckingService.checkQualityResult(deptId, vo));
    }

    @PostMapping("/closeQualityRecord")
    @ApiOperation("关闭质检记录，单个关闭传qualityCheckingRecordId，单个关闭传qualityCheckingRecordIds，区分一下")
    public Result<TCommonOperateDTO<QualityCheckingRecord>> closeQualityRecord(@RequestHeader("deptId") Long deptId,
                                                                  @RequestBody QualityCheckingResultCloseVO vo) {
        return Result.ok(qualityCheckingService.closeQualityRecord(deptId, vo));
    }

    @PostMapping("/modifyQualityResult")
    @ApiOperation("编辑，单个编辑传qualityCheckingResultId，批量编辑传qualityCheckingResultIds，区分一下")
    public Result<TCommonOperateDTO<QualityCheckingResult>> modifyQualityResult(@RequestHeader("deptId") Long deptId,
                                                                   @RequestBody @Valid QualityCheckingResultModifyVO vo) {
        return Result.ok(qualityCheckingService.modifyQualityResult(deptId, vo));
    }

    @PostMapping("/downloadOperateErrorResultRecord/{batchNo}")
    @ApiOperation("统一的下载操作异常的记录表格-质检结果的操作异常数据（同步导出）")
    public void downloadOperateErrorResultRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<QualityCheckingResultOperateDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.QUALITY_CHECKING_RESULT_OPERATE_ERROR_RECORD + batchNo, 500);
        errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
        ExcelUtil<QualityCheckingResultOperateDTO> util = new ExcelUtil<>(QualityCheckingResultOperateDTO.class);
        util.exportExcel(response, errorDTOList, "质检结果操作异常记录");
    }

    @PostMapping("/downloadOperateErrorRecordRecord/{batchNo}")
    @ApiOperation("统一的下载操作异常的记录表格-质检记录的操作异常数据（同步导出）")
    public void downloadOperateErrorRecordRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo) {
        List<QualityCheckingRecordOperateDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.QUALITY_CHECKING_RECORD_OPERATE_ERROR_RECORD + batchNo, 500);
        errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
        ExcelUtil<QualityCheckingRecordOperateDTO> util = new ExcelUtil<>(QualityCheckingRecordOperateDTO.class);
        util.exportExcel(response, errorDTOList, "质检记录操作异常记录");
    }

    @GetMapping("/qualityCheckingResultDetail")
    @ApiOperation("质检详情，操作记录businessType=19")
    public Result<QualityCheckingResultDetailDTO> qualityCheckingResultDetail(@RequestHeader("deptId") Long deptId,
                                                                              @RequestParam("qualityCheckingResultId") @ApiParam("质检结果id") Long qualityCheckingResultId) {
        return Result.ok(qualityCheckingService.qualityCheckingResultDetail(deptId, qualityCheckingResultId));
    }

    @PostMapping("/getCheckingRecordList")
    @ApiIgnore
    @InnerAuth
    public Result<List<QualityCheckingRecord>> getCheckingRecordList(@RequestBody List<Long> customerServicePeriodMonthIds) {
        return Result.ok(qualityCheckingService.getCheckingRecordList(customerServicePeriodMonthIds));
    }

    @PostMapping("/remoteCreateQualityChecking")
    @ApiIgnore
    @InnerAuth
    public Result<RemoteCustomerPeriodDTO> remoteCreateQualityChecking(@RequestBody RemoteQualityCheckingCreateVO vo) {
        return Result.ok(qualityCheckingService.remoteCreateQualityChecking(vo));
    }

    @PostMapping("/remoteSendQualityCheckingTask")
    @ApiIgnore
    @InnerAuth
    public Result remoteSendQualityCheckingTask(@RequestBody RemoteSendQualityCheckingTaskVO vo) {
        qualityCheckingService.remoteSendQualityCheckingTask(vo);
        return Result.ok();
    }
}
