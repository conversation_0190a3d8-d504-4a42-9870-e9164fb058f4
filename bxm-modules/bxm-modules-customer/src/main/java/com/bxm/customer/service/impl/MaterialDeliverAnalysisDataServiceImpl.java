package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.MaterialDeliverAnalysisDataMapper;
import com.bxm.customer.domain.MaterialDeliverAnalysisData;
import com.bxm.customer.service.IMaterialDeliverAnalysisDataService;

/**
 * 材料交接单数据解析结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Service
public class MaterialDeliverAnalysisDataServiceImpl extends ServiceImpl<MaterialDeliverAnalysisDataMapper, MaterialDeliverAnalysisData> implements IMaterialDeliverAnalysisDataService
{
    @Autowired
    private MaterialDeliverAnalysisDataMapper materialDeliverAnalysisDataMapper;

    /**
     * 查询材料交接单数据解析结果
     * 
     * @param id 材料交接单数据解析结果主键
     * @return 材料交接单数据解析结果
     */
    @Override
    public MaterialDeliverAnalysisData selectMaterialDeliverAnalysisDataById(Long id)
    {
        return materialDeliverAnalysisDataMapper.selectMaterialDeliverAnalysisDataById(id);
    }

    /**
     * 查询材料交接单数据解析结果列表
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 材料交接单数据解析结果
     */
    @Override
    public List<MaterialDeliverAnalysisData> selectMaterialDeliverAnalysisDataList(MaterialDeliverAnalysisData materialDeliverAnalysisData)
    {
        return materialDeliverAnalysisDataMapper.selectMaterialDeliverAnalysisDataList(materialDeliverAnalysisData);
    }

    /**
     * 新增材料交接单数据解析结果
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 结果
     */
    @Override
    public int insertMaterialDeliverAnalysisData(MaterialDeliverAnalysisData materialDeliverAnalysisData)
    {
        materialDeliverAnalysisData.setCreateTime(DateUtils.getNowDate());
        return materialDeliverAnalysisDataMapper.insertMaterialDeliverAnalysisData(materialDeliverAnalysisData);
    }

    /**
     * 修改材料交接单数据解析结果
     * 
     * @param materialDeliverAnalysisData 材料交接单数据解析结果
     * @return 结果
     */
    @Override
    public int updateMaterialDeliverAnalysisData(MaterialDeliverAnalysisData materialDeliverAnalysisData)
    {
        materialDeliverAnalysisData.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverAnalysisDataMapper.updateMaterialDeliverAnalysisData(materialDeliverAnalysisData);
    }

    /**
     * 批量删除材料交接单数据解析结果
     * 
     * @param ids 需要删除的材料交接单数据解析结果主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverAnalysisDataByIds(Long[] ids)
    {
        return materialDeliverAnalysisDataMapper.deleteMaterialDeliverAnalysisDataByIds(ids);
    }

    /**
     * 删除材料交接单数据解析结果信息
     * 
     * @param id 材料交接单数据解析结果主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverAnalysisDataById(Long id)
    {
        return materialDeliverAnalysisDataMapper.deleteMaterialDeliverAnalysisDataById(id);
    }
}
