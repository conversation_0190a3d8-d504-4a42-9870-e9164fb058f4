package com.bxm.customer.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "year")
public class YearProperties
{

    private String list;

    public String getList() {
        return list;
    }

    public void setList(String list) {
        this.list = list;
    }
}
