package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiPersonTaxStatusSearchRecord;

/**
 * 个税状态查询定时轮询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Mapper
public interface OpenApiPersonTaxStatusSearchRecordMapper extends BaseMapper<OpenApiPersonTaxStatusSearchRecord>
{
    /**
     * 查询个税状态查询定时轮询
     * 
     * @param id 个税状态查询定时轮询主键
     * @return 个税状态查询定时轮询
     */
    public OpenApiPersonTaxStatusSearchRecord selectOpenApiPersonTaxStatusSearchRecordById(Long id);

    /**
     * 查询个税状态查询定时轮询列表
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 个税状态查询定时轮询集合
     */
    public List<OpenApiPersonTaxStatusSearchRecord> selectOpenApiPersonTaxStatusSearchRecordList(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord);

    /**
     * 新增个税状态查询定时轮询
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 结果
     */
    public int insertOpenApiPersonTaxStatusSearchRecord(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord);

    /**
     * 修改个税状态查询定时轮询
     * 
     * @param openApiPersonTaxStatusSearchRecord 个税状态查询定时轮询
     * @return 结果
     */
    public int updateOpenApiPersonTaxStatusSearchRecord(OpenApiPersonTaxStatusSearchRecord openApiPersonTaxStatusSearchRecord);

    /**
     * 删除个税状态查询定时轮询
     * 
     * @param id 个税状态查询定时轮询主键
     * @return 结果
     */
    public int deleteOpenApiPersonTaxStatusSearchRecordById(Long id);

    /**
     * 批量删除个税状态查询定时轮询
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiPersonTaxStatusSearchRecordByIds(Long[] ids);
}
