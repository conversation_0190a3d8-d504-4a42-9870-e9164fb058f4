package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteAccountingCashierSimpleDTO;
import com.bxm.customer.api.domain.vo.RemoteAccountingCashierPeriodTypeVO;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.CustomerServiceCashierAccountingFile;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.accoutingCashier.*;
import com.bxm.customer.domain.vo.accoutingCashier.*;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.ICCustomerServiceService;
import com.bxm.customer.service.ICustomerServiceCashierAccountingService;
import com.bxm.customer.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 客户账务Controller
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@RestController
@RequestMapping("/accountingCashier")
@Api(tags = "客户账务")
public class CustomerServiceCashierAccountingController extends BaseController
{
    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @GetMapping("/accountingCashierList")
    @ApiOperation("账务列表查询")
    public Result<IPage<AccountingCashierDTO>> accountingCashierList(AccountingCashierSearchVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.accountingCashierList(vo));
    }

    @PostMapping("/remoteAccountingCashierList")
    @ApiIgnore
    @InnerAuth
    public Result<List<AccountingCashierDTO>> remoteAccountingCashierList(@RequestBody AccountingCashierSearchVO vo) {
        return Result.ok(customerServiceCashierAccountingService.accountingCashierList(vo).getRecords());
    }

    @PostMapping("/accountingCashierExportAndUpload")
    @ApiOperation("账务列表导出（异步导出）")
    public Result accountingCashierExportAndUpload(AccountingCashierSearchVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "账务-" + AccountingCashierType.getByCode(vo.getType()).getName() + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.CUSTOMER_SERVICE_ACCOUNTING_CASHIER);
        CompletableFuture.runAsync(() -> {
            try {
                List<AccountingCashierDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<AccountingCashierDTO> l = customerServiceCashierAccountingService.accountingCashierList(vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    List<AccountingCashierInAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierInAccountExportDTO dto = new AccountingCashierInAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    ExcelUtil<AccountingCashierInAccountExportDTO> util = new ExcelUtil<>(AccountingCashierInAccountExportDTO.class);
                    asyncService.uploadExport(util, exports, title, downloadRecordId);
                } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    List<AccountingCashierBankExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierBankExportDTO dto = new AccountingCashierBankExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    ExcelUtil<AccountingCashierBankExportDTO> util = new ExcelUtil<>(AccountingCashierBankExportDTO.class);
                    asyncService.uploadExport(util, exports, title, downloadRecordId);
                } else if (Objects.equals(vo.getType(), AccountingCashierType.CHANGE.getCode())) {
                    List<AccountingCashierModifyAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierModifyAccountExportDTO dto = new AccountingCashierModifyAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    ExcelUtil<AccountingCashierModifyAccountExportDTO> util = new ExcelUtil<>(AccountingCashierModifyAccountExportDTO.class);
                    asyncService.uploadExport(util, exports, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @GetMapping("/getAccountingCashierFiles")
    @ApiOperation("查看账务交付单附件列表")
    public Result<List<CommonFileVO>> getAccountingCashierFiles(@RequestParam("accountingCashierId") @Param("账务交付单id") Long accountingCashierId,
                                                                @RequestParam(value = "fileType", required = false) @ApiParam("附件类型，1-材料附件，2-交付附件，3-rpa附件") Integer fileType) {
        return Result.ok(customerServiceCashierAccountingService.getAccountingCashierFiles(accountingCashierId, fileType));
    }

    @GetMapping("/detail")
    @ApiOperation("账务交付单详情，操作记录businessType=12")
    public Result<AccountingCashierDetailDTO> detail(@RequestParam("accountingCashierId") @Param("账务交付单id") Long accountingCashierId) {
        return Result.ok(customerServiceCashierAccountingService.detail(accountingCashierId));
    }

    @PostMapping("/modifyMattersNotes")
    @ApiOperation("编辑事项备忘")
    public Result modifyMattersNotes(@RequestBody AccountingCashierMattersNotesModifyVO vo, @RequestHeader("deptId") Long deptId) {
        customerServiceCashierAccountingService.modifyMattersNotes(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/createAccountingCashier")
    @ApiOperation("创建账务交付单")
    public Result createAccountingCashier(@RequestBody AccountingCashierCreateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        Long userId = vo.getUserId();
        String lockKey = "lock:customerServiceAccountingCashier:add:" + vo.getCustomerServicePeriodMonthId() + ":" + vo.getType() + (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode()) ? (":" + vo.getBankAccountNumber()) : "");
        if (redisService.lockNotWait(lockKey, userId.toString(), 5L)) {
            try {
                customerServiceCashierAccountingService.createAccountingCashier(vo);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock(lockKey, userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    @PostMapping("/remoteCreateAccountingCashier")
    @ApiIgnore
    @InnerAuth
    public Result remoteCreateAccountingCashier(@RequestBody AccountingCashierCreateVO vo) {
        customerServiceCashierAccountingService.remoteCreateAccountingCashier(vo);
        return Result.ok();
    }

    @PostMapping("/modifyAccountingCashier")
    @ApiOperation("编辑账务交付单")
    public Result modifyAccountingCashier(@RequestBody AccountingCashierModifyVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        customerServiceCashierAccountingService.modifyAccountingCashier(vo);
        return Result.ok();
    }

    @PostMapping("/submit")
    @ApiOperation("提交，单个提交传id，批量提交传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> submit(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.submit(vo));
    }

    @PostMapping("/submitV2")
    @ApiOperation("提交，单个提交传id，批量提交传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> submitV2(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.submitV2(vo));
    }

    @PostMapping("/waitSubmitSubmit")
    @ApiOperation("(交付待提交)提交，单个提交传id，批量提交传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> waitSubmitSubmit(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.waitSubmitSubmit(vo));
    }

    @PostMapping("/delete")
    @ApiOperation("删除，单个删除传id，批量删除传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> delete(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.delete(vo));
    }

    @PostMapping("/deliver")
    @ApiOperation("交付，单个交付传id，批量交付传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> deliver(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.deliver(vo));
    }

    @PostMapping("/remoteDeliver")
    @ApiIgnore
    @InnerAuth
    public Result remoteDeliver(@RequestBody AccountingCashierOperateVO vo) {
        customerServiceCashierAccountingService.remoteDeliver(vo);
        return Result.ok();
    }

    @PostMapping("/dealException")
    @ApiOperation("处理异常，单个处理异常传id，批量处理异常传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> dealException(@RequestBody AccountingCashierDealExceptionVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.dealException(vo));
    }

    @PostMapping("/remoteDealException")
    @ApiIgnore
    @InnerAuth
    public Result remoteDealException(@RequestBody AccountingCashierDealExceptionVO vo) {
        customerServiceCashierAccountingService.remoteDealException(vo);
        return Result.ok();
    }

    @PostMapping("/changeAccountingCashier")
    @ApiOperation("变更交付账务交付单，只有单个操作传id")
    public Result changeAccountingCashier(@RequestBody AccountingCashierChangeDeliverVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        customerServiceCashierAccountingService.changeAccountingCashier(vo);
        return Result.ok();
    }

    @PostMapping("/reBackAccountingCashier")
    @ApiOperation("退回账务交付单，单个退回传id，批量退回传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> reBackAccountingCashier(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.reBackAccountingCashier(vo));
    }

    @PostMapping("/rpaUpdate")
    @ApiOperation("rpa更新，只有单个操作传id")
    public Result rpaUpdate(@RequestBody AccountingCashierRpaUpdateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        customerServiceCashierAccountingService.rpaUpdate(vo);
        return Result.ok();
    }

    @PostMapping("/remoteRpaUpdate")
    @ApiIgnore
    @InnerAuth
    public Result remoteRpaUpdate(@RequestBody AccountingCashierRpaUpdateVO vo) {
        customerServiceCashierAccountingService.remoteRpaUpdate(vo);
        return Result.ok();
    }

    @PostMapping("/updateProfit")
    @ApiOperation("更新利润，只有单个操作传id")
    public Result updateProfit(@RequestBody AccountingCashierUpdateProfitVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        customerServiceCashierAccountingService.updateProfit(vo);
        return Result.ok();
    }

    @PostMapping("/remoteUpdateProfit")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateProfit(@RequestBody AccountingCashierUpdateProfitVO vo) {
        customerServiceCashierAccountingService.remoteUpdateProfit(vo);
        return Result.ok();
    }

    @PostMapping("/supplementFiles")
    @ApiOperation("补充材料，单个接收变更传id，批量接收变更传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> supplementFiles(@RequestBody AccountingCashierSupplementFileVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.supplementFiles(vo));
    }

    @PostMapping("/remoteSupplementFiles")
    @ApiIgnore
    @InnerAuth
    public Result remoteSupplementFiles(@RequestBody AccountingCashierSupplementFileVO vo) {
        customerServiceCashierAccountingService.remoteSupplementFiles(vo);
        return Result.ok();
    }

    @PostMapping("/checkFiles")
    @ApiOperation("补充核对，单个操作传id，批量操作传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> checkFiles(@RequestBody AccountingCashierCheckFileVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.checkFiles(vo));
    }

    @PostMapping("/remoteCheckFiles")
    @ApiIgnore
    @InnerAuth
    public Result remoteCheckFiles(@RequestBody AccountingCashierCheckFileVO vo) {
        customerServiceCashierAccountingService.remoteCheckFiles(vo);
        return Result.ok();
    }

    @PostMapping("/receiveChange")
    @ApiOperation("接收变更，单个接收变更传id，批量接收变更传ids，因为批量操作需要返回操作结果，所以需要区分一下参数")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> receiveChange(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.receiveChange(vo));
    }

    @PostMapping("/createBankTask")
    @ApiOperation("创建银行流水任务")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> createBankTask(@RequestBody AccountingCashierBankCreateTaskVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.createBankTask(vo));
    }

    @PostMapping("/downloadOperateErrorRecord/{batchNo}/{type}")
    @ApiOperation("统一的下载操作异常的记录表格（同步导出）")
    public void downloadOperateErrorRecord(HttpServletResponse response, @PathVariable("batchNo") String batchNo, @PathVariable("type") @ApiParam("账务类型，1-入账，2-流水，3-改账") Integer type) {
        if (Objects.equals(type, AccountingCashierType.INCOME.getCode())) {
            List<AccountingCashierInAccountErrorExportDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, 500);
            errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
            ExcelUtil<AccountingCashierInAccountErrorExportDTO> util = new ExcelUtil<>(AccountingCashierInAccountErrorExportDTO.class);
            util.exportExcel(response, errorDTOList, "交付单操作异常记录");
        } else if (Objects.equals(type, AccountingCashierType.FLOW.getCode())) {
            List<AccountingCashierBankErrorExportDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, 500);
            errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
            ExcelUtil<AccountingCashierBankErrorExportDTO> util = new ExcelUtil<>(AccountingCashierBankErrorExportDTO.class);
            util.exportExcel(response, errorDTOList, "交付单操作异常记录");
        } else if (Objects.equals(type, AccountingCashierType.CHANGE.getCode())) {
            List<AccountingCashierModifyAccountErrorExportDTO> errorDTOList = redisService.getLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, 500);
            errorDTOList = ObjectUtils.isEmpty(errorDTOList) ? Lists.newArrayList() : errorDTOList;
            ExcelUtil<AccountingCashierModifyAccountErrorExportDTO> util = new ExcelUtil<>(AccountingCashierModifyAccountErrorExportDTO.class);
            util.exportExcel(response, errorDTOList, "交付单操作异常记录");
        } else {
            throw new ServiceException("类型错误");
        }
    }

    @PostMapping("/remoteUpdateBankPaymentResultSettleAccountStatus")
    @ApiIgnore
    @InnerAuth
    public Result remoteUpdateBankPaymentResultSettleAccountStatus(@RequestBody UpdateBankPaymentResultSettleAccountStatusVO vo) {
        customerServiceCashierAccountingService.remoteUpdateBankPaymentResultSettleAccountStatus(vo);
        return Result.ok();
    }

    @GetMapping("/bankPaymentByPeriodId")
    @ApiOperation("获取账期对应的所有银行流水交付单")
    public Result<List<AccountingCashierBankSimpleVO>> bankPaymentByPeriodId(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(customerServiceCashierAccountingService.bankPaymentByPeriodId(customerServicePeriodMonthId));
    }

    @GetMapping("/inAccountByPeriodId")
    @ApiOperation("获取账期对应的入账/改账交付单")
    public Result<List<AccountingCashierInAccountSimpleVO>> inAccountByPeriodId(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(customerServiceCashierAccountingService.inAccountByPeriodId(customerServicePeriodMonthId));
    }

    @GetMapping("/materialFilesByPeriodId")
    @ApiOperation("账期材料明细列表（批量删除/bxmCustomer/accountingCashier/deleteMaterialFiles，打包下载/bxmFile/customer/deliver/materialFilesExportAndUpload）")
    public Result<List<MaterialFileSimpleVO>> materialFilesByPeriodId(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId,
                                                                       @RequestParam(value = "fileType", required = false) @ApiParam("文件类型，1-入账材料，2-银行流水，3-改账材料") Integer fileType,
                                                                       @RequestParam(value = "bankName", required = false) @ApiParam("银行名称，数据来源接口/bxmCustomer/select/materialFileBankSelect") String bankName,
                                                                       @RequestParam(value = "fileRemark", required = false) @ApiParam("文件备注") String fileRemark) {
        return Result.ok(customerServiceCashierAccountingService.materialFilesByPeriodId(customerServicePeriodMonthId, fileType, bankName, fileRemark));
    }

    @PostMapping("/deleteMaterialFiles")
    @ApiOperation("删除账期材料明细（传参直接是id的数组，不需要key）（删除附件操作基本不会出现异常情况，暂时不考虑异常情况的下载）")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccountingFile>> deleteMaterialFiles(@RequestBody List<Long> ids, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceCashierAccountingService.deleteMaterialFiles(ids, deptId));
    }

    @PostMapping("/getMaterialFilesByIds")
    @ApiIgnore
    @InnerAuth
    public Result<List<MaterialFileSimpleErrorVO>> getMaterialFilesByIds(@RequestBody List<Long> ids) {
        return Result.ok(customerServiceCashierAccountingService.getMaterialFilesByIds(ids));
    }

    @PostMapping("/getBankAccountNumberByListBankAccountNumber")
    @ApiIgnore
    @InnerAuth
    public Result<List<String>> getBankAccountNumberByListBankAccountNumber(@RequestBody List<String> bankAccountNumbers) {
        return Result.ok(customerServiceCashierAccountingService.getBankAccountNumberByListBankAccountNumber(bankAccountNumbers));
    }

    @PostMapping("/getAccountingCashierByPeriodIdsAndAccountingCashierType")
    @ApiIgnore
    @InnerAuth
    public Result<List<RemoteAccountingCashierSimpleDTO>> getAccountingCashierByPeriodIdsAndAccountingCashierType(@RequestBody RemoteAccountingCashierPeriodTypeVO vo) {
        return Result.ok(customerServiceCashierAccountingService.getAccountingCashierByPeriodIdsAndAccountingCashierType(vo));
    }

    @PostMapping("/batchUpdateBankAccountNumber")
    @ApiOperation("批量编辑银行账号，银行流水交付单才有这个操作")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> batchUpdateBankAccountNumber(@RequestBody BatchUpdateBankAccountNumberVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerServiceCashierAccountingService.batchUpdateBankAccountNumber(vo, deptId));
    }

    @PostMapping("/managerDelete")
    @ApiOperation("管理删除（参数type记得传）")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> managerDelete(@RequestBody AccountingCashierOperateVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.managerDelete(vo));
    }

    @PostMapping("/modifyDdl")
    @ApiOperation("批量修改ddl（参数type记得传）")
    public Result<TCommonOperateDTO<CustomerServiceCashierAccounting>> modifyDdl(@RequestBody CustomerServiceCashierAccountingModifyDdlVO vo,
                                                     @RequestHeader("deptId") Long deptId) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(customerServiceCashierAccountingService.modifyDdl(vo, deptId));
    }

//    @PostMapping("/updateAccountingCashierStatusByCustomerServiceIds")
//    @ApiIgnore
//    @InnerAuth
//    public Result updateAccountingCashierStatusByCustomerServiceIds(@RequestBody List<Long> customerServiceIds) {
//        if (ObjectUtils.isEmpty(customerServiceIds)) {
//            return Result.ok();
//        }
//        customerServiceIds.forEach(customerServiceId -> customerServiceService.updatePeriodBankPaymentByCustomerServiceId(customerServiceId));
//        return Result.ok();
//    }
}
