package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerTaxTypeCheck;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerServiceTaxTypeCheckVO;

/**
 * 新户流转税种核定Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerTaxTypeCheckService extends IService<NewCustomerTaxTypeCheck>
{
    /**
     * 查询新户流转税种核定
     * 
     * @param id 新户流转税种核定主键
     * @return 新户流转税种核定
     */
    public NewCustomerTaxTypeCheck selectNewCustomerTaxTypeCheckById(Long id);

    /**
     * 查询新户流转税种核定列表
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 新户流转税种核定集合
     */
    public List<NewCustomerTaxTypeCheck> selectNewCustomerTaxTypeCheckList(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 新增新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    public int insertNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 修改新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    public int updateNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 批量删除新户流转税种核定
     * 
     * @param ids 需要删除的新户流转税种核定主键集合
     * @return 结果
     */
    public int deleteNewCustomerTaxTypeCheckByIds(Long[] ids);

    /**
     * 删除新户流转税种核定信息
     * 
     * @param id 新户流转税种核定主键
     * @return 结果
     */
    public int deleteNewCustomerTaxTypeCheckById(Long id);

    List<NewCustomerServiceTaxTypeCheckVO> selectCustomerTaxTypeCheckByNewCustomerTransferId(Long newCustomerTransferId, Integer taxType);

    List<NewCustomerTaxTypeCheck> selectByNewCustomerTransferId(Long newCustomerTransferId);

    void removeAndCreateTaxTypeCheck(Long newCustomerTransferId, List<NewCustomerServiceTaxTypeCheckVO> taxTypeCheckList);
}
