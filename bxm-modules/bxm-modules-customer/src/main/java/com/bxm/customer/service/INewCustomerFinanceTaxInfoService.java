package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerFinanceTaxInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerServiceFinanceTaxInfoDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerServiceTaxTypeCheckDTO;

/**
 * 新户流转财税信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerFinanceTaxInfoService extends IService<NewCustomerFinanceTaxInfo>
{
    /**
     * 查询新户流转财税信息
     * 
     * @param id 新户流转财税信息主键
     * @return 新户流转财税信息
     */
    public NewCustomerFinanceTaxInfo selectNewCustomerFinanceTaxInfoById(Long id);

    /**
     * 查询新户流转财税信息列表
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 新户流转财税信息集合
     */
    public List<NewCustomerFinanceTaxInfo> selectNewCustomerFinanceTaxInfoList(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 新增新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    public int insertNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 修改新户流转财税信息
     * 
     * @param newCustomerFinanceTaxInfo 新户流转财税信息
     * @return 结果
     */
    public int updateNewCustomerFinanceTaxInfo(NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo);

    /**
     * 批量删除新户流转财税信息
     * 
     * @param ids 需要删除的新户流转财税信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerFinanceTaxInfoByIds(Long[] ids);

    /**
     * 删除新户流转财税信息信息
     * 
     * @param id 新户流转财税信息主键
     * @return 结果
     */
    public int deleteNewCustomerFinanceTaxInfoById(Long id);

    NewCustomerFinanceTaxInfo selectByNewCustomerTransferId(Long newCustomerTransferId);

    Map<Long, NewCustomerFinanceTaxInfo> selectMapByNewCustomerTransferIds(List<Long> newCustomerTransferIds);

    String editCustomerServiceFinanceTaxInfo(NewCustomerServiceFinanceTaxInfoDTO dto);

    NewCustomerServiceFinanceTaxInfoDTO buildTaxInfo(NewCustomerInfo newCustomerInfo);

    void updateByTaxInfo(NewCustomerServiceFinanceTaxInfoDTO taxInfo);
}
