package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.workOrder.WorkOrderFileType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.dto.CommonDeliverOperateDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderCountDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDetailDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderTypeDTO;
import com.bxm.customer.domain.vo.CustomerDeliverModifyDdlVO;
import com.bxm.customer.domain.vo.workOrder.*;
import com.bxm.customer.service.IWorkOrderFileService;
import com.bxm.customer.service.IWorkOrderService;
import com.bxm.customer.service.IWorkOrderTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/workOrder")
@Api(tags = "任务工单")
public class WorkOrderController {

    @Autowired
    private IWorkOrderService workOrderService;

    @Autowired
    private IWorkOrderFileService workOrderFileService;
    @Autowired
    private IWorkOrderTypeService workOrderTypeService;

    @GetMapping("/workOrderTypeList")
    @ApiOperation("获取所有工单类型列表")
    public Result<List<WorkOrderTypeDTO>> workOrderTypeList() {
        return Result.ok(workOrderTypeService.workOrderTypeList());
    }

    @GetMapping("/workOrderTypeListCanSee")
    @ApiOperation("获取可见工单类型列表")
    public Result<List<WorkOrderTypeDTO>> workOrderTypeListCanSee(@RequestHeader("deptId") Long deptId) {
        return Result.ok(workOrderTypeService.workOrderTypeListCanSee(deptId));
    }

    @GetMapping("/workOrderList")
    @ApiOperation("工单列表")
    public Result<IPage<WorkOrderDTO>> workOrderList(@RequestHeader("deptId") Long deptId,
                                                     WorkOrderSearchVO vo) {
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        return Result.ok(workOrderService.workOrderList(vo, vo.getDeptId()));
    }

    @PostMapping("/remoteWorkOrderList")
    @InnerAuth
    @ApiIgnore
    public Result<List<WorkOrderDTO>> remoteWorkOrderList(@RequestBody WorkOrderSearchVO vo) {
        return Result.ok(workOrderService.workOrderList(vo, vo.getDeptId()).getRecords());
    }

    @PostMapping("/remoteWorkOrderListForXm")
    @InnerAuth
    @ApiIgnore
    public Result<List<WorkOrderDTO>> remoteWorkOrderListForXm(@RequestBody WorkOrderSearchVO vo) {
        return Result.ok(workOrderService.remoteWorkOrderListForXm(vo));
    }

    @PostMapping("/exportWorkOrderList")
    @ApiOperation("导出工单列表")
    public void exportWorkOrderList(@RequestHeader("deptId") Long deptId,
                                    WorkOrderSearchVO vo, HttpServletResponse response) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        String tabName = vo.getTabType() == 1 ? "我方发起" : vo.getTabType() == 2 ? "我方承接" : "待承接";
        ExcelUtil<WorkOrderDTO> util = new ExcelUtil<>(WorkOrderDTO.class);
        util.exportExcel(response, workOrderService.workOrderList(vo, deptId).getRecords(), tabName + "工单列表");
    }

    @GetMapping("/workOrderDetail")
    @ApiOperation("工单详情（操作记录businessType=10）")
    public Result<WorkOrderDetailDTO> workOrderDetail(@RequestParam("id") @ApiParam("工单id") Long id, @RequestHeader("deptId") Long deptId) {
        return Result.ok(workOrderService.workOrderDetail(id, deptId));
    }

    @GetMapping("/remoteWorkOrderDetail")
    @ApiIgnore
    @InnerAuth
    public Result<WorkOrderDetailDTO> remoteWorkOrderDetail(@RequestParam("id") @ApiParam("工单id") Long id,
                                                            @RequestParam("userId") Long userId) {
        return Result.ok(workOrderService.remoteWorkOrderDetail(id, userId));
    }

    @PostMapping("/createWorkOrder")
    @ApiOperation("创建工单")
    public Result createWorkOrder(@RequestBody @Valid WorkOrderCreateVO vo,
                                  @RequestHeader("deptId") Long deptId) {
        workOrderService.createWorkOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/followUp")
    @ApiOperation("跟进")
    public Result followUp(@RequestBody WorkOrderFollowUpVO vo,
                           @RequestHeader("deptId") Long deptId) {
        workOrderService.followUp(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteFollowUp")
    @ApiIgnore
    @InnerAuth
    public Result<Boolean> remoteFollowUp(@RequestBody WorkOrderFollowUpVO vo) {
        return Result.ok(workOrderService.remoteFollowUp(vo));
    }

    @PostMapping("/commentWorkOrder")
    @ApiOperation("工单评论")
    public Result commentWorkOrder(@RequestBody WorkOrderCommentVO vo, @RequestHeader("deptId") Long deptId) {
        workOrderService.commentWorkOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteCommentWorkOrder")
    @ApiIgnore
    @InnerAuth
    public Result<Boolean> remoteCommentWorkOrder(@RequestBody WorkOrderCommentVO vo) {
        return Result.ok(workOrderService.remoteCommentWorkOrder(vo));
    }

    @PostMapping("/transmit")
    @ApiOperation("转交")
    public Result transmit(@RequestBody WorkOrderTransmitVO vo, @RequestHeader("deptId") Long deptId) {
        workOrderService.transmit(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteTransmit")
    @ApiIgnore
    @InnerAuth
    public Result<Boolean> remoteTransmit(@RequestBody WorkOrderTransmitVO vo) {
        return Result.ok(workOrderService.remoteTransmit(vo));
    }

    @GetMapping("/workOrderStatistic")
    @ApiOperation("工单tab数字统计")
    public Result<WorkOrderCountDTO> workOrderStatistic(@RequestHeader("deptId") Long deptId) {
        return Result.ok(workOrderService.workOrderStatistic(deptId));
    }

    @GetMapping("/getWorkOrderFiles")
    @ApiOperation("获取工单附件")
    public Result<List<CommonFileVO>> getWorkOrderFiles(@RequestParam("id") @ApiParam("工单id") Long id) {
        return Result.ok(workOrderFileService.getByWorkOrderIdAndFileType(id, WorkOrderFileType.CREATE_FILE.getCode()));
    }

    @PostMapping("/transmitBatch")
    @ApiOperation("批量转交")
    public Result<TCommonOperateDTO<WorkOrder>> transmitBatch(@RequestBody WorkOrderTransmitBatchVO vo, @RequestHeader("deptId") Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return Result.fail("请选择工单");
        }
        return Result.ok(workOrderService.transmitBatch(vo, deptId));
    }

    @PostMapping("/restartWorkOrder")
    @ApiOperation("工单重启")
    public Result restartWorkOrder(@RequestBody WorkOrderRestartVO vo, @RequestHeader("deptId") Long deptId) {
        workOrderService.restartWorkOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/confirmWorkOrder")
    @ApiOperation("确认工单")
    public Result confirmWorkOrder(@RequestBody WorkOrderConfirmVO vo, @RequestHeader("deptId") Long deptId) {
        workOrderService.confirmWorkOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteConfirmWorkOrder")
    @InnerAuth
    @ApiIgnore
    public Result remoteConfirmWorkOrder(@RequestBody WorkOrderConfirmVO vo) {
        return Result.ok(workOrderService.remoteConfirmWorkOrder(vo));
    }

    @PostMapping("/modifyDdl")
    @ApiOperation("批量修改ddl，传选中的id值")
    public Result<TCommonOperateDTO<WorkOrder>> modifyDdl(@RequestBody WorkOrderModifyDdlVO vo,
                                                     @RequestHeader("deptId") Long deptId) {
        return Result.ok(workOrderService.modifyDdl(vo, deptId));
    }
}
