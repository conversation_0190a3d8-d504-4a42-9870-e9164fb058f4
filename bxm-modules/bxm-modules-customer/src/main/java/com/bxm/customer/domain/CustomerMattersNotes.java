package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 事项备忘对象 c_customer_matters_notes
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ApiModel("事项备忘对象")
@Accessors(chain = true)
@TableName("c_customer_matters_notes")
public class CustomerMattersNotes extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 事项类型，1-空（为了和交付单类型一致，医社保用2），2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-银行流水，10-入账，11-财税风控，12-税种核定，13-险种核定，14-发票获取 */
    @Excel(name = "事项类型，1-空", readConverterExp = "为=了和交付单类型一致，医社保用2")
    @TableField("item_type")
    @ApiModelProperty(value = "事项类型，1-空（为了和交付单类型一致，医社保用2），2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报，9-银行流水，10-入账，11-财税风控，12-税种核定，13-险种核定，14-发票获取")
    private Integer itemType;

    /** 内容 */
    @Excel(name = "内容")
    @TableField("matters_notes")
    @ApiModelProperty(value = "内容")
    private String mattersNotes;

}
