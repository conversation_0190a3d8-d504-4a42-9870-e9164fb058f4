package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperDTO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.*;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import com.bxm.customer.domain.vo.newCustomer.*;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerSysAccountMapper;
import com.bxm.customer.mapper.NewCustomerInfoMapper;
import com.bxm.customer.properties.SpecialDeptIdProperties;
import com.bxm.customer.properties.SpecialDeptlistProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 新户流转客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
@Slf4j
public class NewCustomerInfoServiceImpl extends ServiceImpl<NewCustomerInfoMapper, NewCustomerInfo> implements INewCustomerInfoService
{

    private static final Integer THREAD_POOL_SIZE = 5;

    @Autowired
    private NewCustomerInfoMapper newCustomerInfoMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private INewCustomerTransferFileService newCustomerTransferFileService;

    @Autowired
    private INewCustomerBankAccountService newCustomerBankAccountService;

    @Autowired
    private INewCustomerSysAccountService newCustomerSysAccountService;

    @Autowired
    private INewCustomerTaxTypeCheckService newCustomerTaxTypeCheckService;

    @Autowired
    private INewCustomerFinanceTaxInfoService newCustomerFinanceTaxInfoService;

    @Autowired
    private INewCustomerInsuranceFundInfoService newCustomerInsuranceFundInfoService;

    @Autowired
    private INewCustomerAnnualReportInfoService newCustomerAnnualReportInfoService;

    @Autowired
    private ICTagService tagService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private INewCustomerInsuranceFundStatusService newCustomerInsuranceFundStatusService;

    @Autowired
    private INewCustomerIncomeInfoService newCustomerIncomeInfoService;

    @Autowired
    private INewCustomerOtherInfoService newCustomerOtherInfoService;

    @Autowired
    private INewCustomerFixedAssetsInfoService newCustomerFixedAssetsInfoService;

    @Value("${defaultAccountingParentDeptId}")
    private Long defaultAccountingParentDeptId;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private CustomerSysAccountMapper customerSysAccountMapper;

    @Autowired
    private ICustomerTaxTypeCheckService customerTaxTypeCheckService;

    @Autowired
    private SpecialDeptIdProperties specialDeptIdProperties;

    @Autowired
    private SpecialDeptlistProperties specialDeptlistProperties;

    /**
     * 查询新户流转客户信息
     * 
     * @param id 新户流转客户信息主键
     * @return 新户流转客户信息
     */
    @Override
    public NewCustomerInfo selectNewCustomerInfoById(Long id)
    {
        return newCustomerInfoMapper.selectNewCustomerInfoById(id);
    }

    /**
     * 查询新户流转客户信息列表
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 新户流转客户信息
     */
    @Override
    public List<NewCustomerInfo> selectNewCustomerInfoList(NewCustomerInfo newCustomerInfo)
    {
        return newCustomerInfoMapper.selectNewCustomerInfoList(newCustomerInfo);
    }

    /**
     * 新增新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerInfo(NewCustomerInfo newCustomerInfo)
    {
        return newCustomerInfoMapper.insertNewCustomerInfo(newCustomerInfo);
    }

    /**
     * 修改新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerInfo(NewCustomerInfo newCustomerInfo)
    {
        return newCustomerInfoMapper.updateNewCustomerInfo(newCustomerInfo);
    }

    /**
     * 批量删除新户流转客户信息
     * 
     * @param ids 需要删除的新户流转客户信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInfoByIds(Long[] ids)
    {
        return newCustomerInfoMapper.deleteNewCustomerInfoByIds(ids);
    }

    /**
     * 删除新户流转客户信息信息
     * 
     * @param id 新户流转客户信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInfoById(Long id)
    {
        return newCustomerInfoMapper.deleteNewCustomerInfoById(id);
    }

    @Override
    public IPage<NewCustomerTransferDTO> selectNewCustomerTransferList(Long deptId, int pageNum, int pageSize, String keyWord, Integer status, Integer taxType, Long queryDeptId, String tagName, Integer tagIncludeFlag, String businessDeptIds, Integer firstPeriodStart, Integer firstPeriodEnd) {
        IPage<NewCustomerTransferDTO> iPage = new Page<>(pageNum, pageSize);
        List<Long> customerIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(tagName)) {
            if (Objects.isNull(tagIncludeFlag)) {
                tagIncludeFlag = 1;
            }
            customerIds = businessTagRelationService.getCustomerIdsByTagNameLike(tagName, TagBusinessType.NEW_CUSTOMER_TRANSFER);
            if (ObjectUtils.isEmpty(customerIds) && tagIncludeFlag == 1) {
                return iPage;
            }
        }
        SysDept currentDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(currentDept)) {
            return iPage;
        }
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return iPage;
        }
        List<NewCustomerTransferDTO> data = baseMapper.selectNewCustomerTransferList(iPage, keyWord, status, taxType, queryDeptId, customerIds, tagName, tagIncludeFlag, userDeptDTO, currentDept.getIsHeadquarters(), StringUtils.isEmpty(businessDeptIds) ? null : Arrays.stream(businessDeptIds.split(",")).map(Long::parseLong).collect(Collectors.toList()), firstPeriodStart, firstPeriodEnd);
        if (!ObjectUtils.isEmpty(data)) {
            Map<Long, List<TagDTO>> businessTagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(NewCustomerTransferDTO::getId).collect(Collectors.toList()), TagBusinessType.NEW_CUSTOMER_TRANSFER);
            List<Long> deptIds = data.stream().map(NewCustomerTransferDTO::getAdvisorDeptId).collect(Collectors.toList());
            List<SysEmployee> employees = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(deptIds)) {
                employees = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException(false);
            }
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                    employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            List<NewCustomerBusinessTopDeptCreditCodeVO> voList = data.stream().filter(d -> !Objects.isNull(d.getBusinessTopDeptId()) && !StringUtils.isEmpty(d.getCreditCode()))
                    .map(d -> NewCustomerBusinessTopDeptCreditCodeVO.builder()
                    .businessTopDeptId(d.getBusinessTopDeptId()).creditCode(d.getCreditCode()).build()).collect(Collectors.toList());
            Map<String, List<CCustomerService>> customerServiceMap = ObjectUtils.isEmpty(voList) ? Maps.newHashMap() :
                    customerServiceMapper.selectEndCustomerServiceBatchByBusinessTopDeptIdAndCreditCode(voList).stream().collect(Collectors.groupingBy(c -> c.getBusinessTopDeptId() + "_" + c.getCreditCode()));
            data.forEach(d -> {
                List<SysEmployee> advisorEmployees = employeeMap.get(d.getAdvisorDeptId());
                d.setStatusStr(NewCustomerTransferStatus.getNameByCode(d.getStatus()));
                d.setTaxTypeStr(TaxType.getByCode(d.getTaxType()).getDesc());
                d.setTagList(businessTagMap.getOrDefault(d.getId(), Lists.newArrayList()));
                d.setTagNames(ObjectUtils.isEmpty(d.getTagList()) ? "" : d.getTagList().stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
                d.setIsZeroReport(!ObjectUtils.isEmpty(d.getTagList()) && d.getTagList().stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getLingshenbao())) ? "是" : "");
                d.setIsSanling(!ObjectUtils.isEmpty(d.getTagList()) && d.getTagList().stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getSanling())) ? "是" : "");
                d.setIsVip(!ObjectUtils.isEmpty(d.getTagList()) && d.getTagList().stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getVip())) ? "是" : "");
                d.setAdvisorDeptInfo("");
                d.setAdvisorEmployeeNames(ObjectUtils.isEmpty(advisorEmployees) ? "" : advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
                if (!StringUtils.isEmpty(d.getAdvisorDeptName())) {
                    d.setAdvisorDeptInfo(d.getAdvisorDeptName() + "(" + d.getAdvisorEmployeeNames() + ")");
                }
                d.setStartPeriod(Objects.isNull(d.getFirstPeriod()) ? "" : DateUtils.periodToYeaMonth(d.getFirstPeriod()));
                d.setFirstAccountingPeriod(Objects.isNull(d.getFirstAccountingPeriodInt()) ? "" : DateUtils.periodToYeaMonth(d.getFirstAccountingPeriodInt()));
                d.setIsSupplementPeriod(!Objects.equals(d.getStartPeriod(), d.getFirstAccountingPeriod()) ? "是" : "");
                d.setAccountingParentDeptId(defaultAccountingParentDeptId);
                if (!Objects.isNull(d.getBusinessTopDeptId()) && !StringUtils.isEmpty(d.getCreditCode())) {
                    d.setIsRestartCustomer(!ObjectUtils.isEmpty(customerServiceMap.get(d.getBusinessTopDeptId() + "_" + d.getCreditCode())));
                } else {
                    d.setIsRestartCustomer(false);
                }
                d.setIsRestartCustomerStr(d.getIsRestartCustomer() ? "是" : "否");
            });
        }
        iPage.setRecords(data);
        return iPage;
    }

    @Override
    public List<NewCustomerInfo> selectByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerInfo>().in(NewCustomerInfo::getId, ids).eq(NewCustomerInfo::getIsDel, Boolean.FALSE));
    }

    @Override
    @Transactional
    public CommonOperateResultDTO deleteNewCustomerTransfer(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new CommonOperateResultDTO();
        }
        List<NewCustomerInfo> newCustomerInfos = selectByIds(ids);
        if (ObjectUtils.isEmpty(newCustomerInfos)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = Lists.newArrayList();
        List<CommonOperDTO> successList = Lists.newArrayList();
        List<CommonOperDTO> failList = Lists.newArrayList();
        List<Integer> canDeleteStatus = NewCustomerTransferStatus.canDeleteStatus();
        newCustomerInfos.forEach(row -> {
            CommonOperDTO commonOperDTO = CommonOperDTO.builder().name(row.getCustomerName()).id(row.getId()).build();
            if (canDeleteStatus.contains(row.getStatus())) {
                successList.add(commonOperDTO);
            } else {
                failList.add(commonOperDTO);
            }
            totalList.add(commonOperDTO);
        });
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new NewCustomerInfo().setId(row.getId()).setIsDel(Boolean.TRUE)).collect(Collectors.toList()));
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                            .setDeptId(deptId)
                            .setOperType("删除新户流转")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().successList(successList).failList(failList).totalList(totalList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO submitNewCustomerTransfer(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new CommonOperateResultDTO();
        }
        List<NewCustomerInfo> newCustomerInfos = selectByIds(ids);
        if (ObjectUtils.isEmpty(newCustomerInfos)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = Lists.newArrayList();
        List<CommonOperDTO> successList = Lists.newArrayList();
        List<CommonOperDTO> failList = Lists.newArrayList();
        List<Integer> canSubmitStatus = NewCustomerTransferStatus.canSubmitStatus();
        List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdsAndBusinessType(ids, TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode());
        Map<Long, List<CBusinessTagRelation>> relationMap = ObjectUtils.isEmpty(relations) ? Maps.newHashMap() :
                relations.stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
        newCustomerInfos.forEach(row -> {
            CommonOperDTO commonOperDTO = CommonOperDTO.builder().name(row.getCustomerName()).id(row.getId()).build();
            List<Long> tagIds = relationMap.getOrDefault(row.getId(), Lists.newArrayList()).stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
            if (canSubmitStatus.contains(row.getStatus()) && Objects.equals(checkAndSaveStatus(row.getId(), row, tagIds), NewCustomerTransferStatus.UN_SUBMIT.getCode())) {
                removeDontNeedDatas(row.getId(), row, tagIds);
                successList.add(commonOperDTO);
            } else {
                failList.add(commonOperDTO);
            }
            totalList.add(commonOperDTO);
        });
        if (!ObjectUtils.isEmpty(successList)) {
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
            update(new LambdaUpdateWrapper<NewCustomerInfo>()
                    .in(NewCustomerInfo::getId, successList.stream().map(CommonOperDTO::getId).collect(Collectors.toList()))
                    .set(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode())
                    .set(NewCustomerInfo::getFirstAccountPeriod, DateUtils.getNowPeriod())
                    .set(NewCustomerInfo::getSubmitTime, LocalDateTime.now())
                    .set(NewCustomerInfo::getSortTime, LocalDateTime.now())
                    .set(NewCustomerInfo::getSubmitEmployeeName, ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName()));
            Map<Long, NewCustomerInfo> newCustomerInfoMap = newCustomerInfos.stream().collect(Collectors.toMap(NewCustomerInfo::getId, item -> item));
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                            .setDeptId(deptId)
                            .setOperType("提交新户流转")
                            .setOperName(operName)
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                NewCustomerInfo newCustomerInfo = newCustomerInfoMap.get(row.getId());
                autoTransfer(newCustomerInfo, userId, deptId, operName);
            });
        }
        return CommonOperateResultDTO.builder().successList(successList).failList(failList).totalList(totalList).build();
    }

    @Override
    @Transactional
    public CommonOperateResultDTO reBackNewCustomerTransfer(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new CommonOperateResultDTO();
        }
        List<NewCustomerInfo> newCustomerInfos = selectByIds(ids);
        if (ObjectUtils.isEmpty(newCustomerInfos)) {
            return new CommonOperateResultDTO();
        }
        List<CommonOperDTO> totalList = Lists.newArrayList();
        List<CommonOperDTO> successList = Lists.newArrayList();
        List<CommonOperDTO> failList = Lists.newArrayList();
        List<Integer> canReBackStatus = NewCustomerTransferStatus.canReBackStatus();
        newCustomerInfos.forEach(row -> {
            CommonOperDTO commonOperDTO = CommonOperDTO.builder().name(row.getCustomerName()).id(row.getId()).build();
            if (canReBackStatus.contains(row.getStatus())) {
                successList.add(commonOperDTO);
            } else {
                failList.add(commonOperDTO);
            }
            totalList.add(commonOperDTO);
        });
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new NewCustomerInfo().setId(row.getId()).setStatus(NewCustomerTransferStatus.UN_RESUBMIT.getCode())).collect(Collectors.toList()));
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                            .setDeptId(deptId)
                            .setOperType("退回新户流转")
                            .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                            .setOperUserId(userId));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        return CommonOperateResultDTO.builder().successList(successList).failList(failList).totalList(totalList).build();
    }

    @Override
    @Transactional
    public Long createNewCustomerBaseInfo(Long deptId, NewCustomerCreateVO vo) {
        if (Integer.parseInt(vo.getBaseInfo().getRegistrationDate().replaceAll("-", "").substring(0, 4)) > DateUtils.getNowPeriod()) {
            throw new ServiceException("注册日期不能大于当前月");
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept) || sysDept.getDeptType() != 1) {
            throw new ServiceException("当前业务公司有误");
        }
        Long businessTopDeptId = sysDept.getParentId();
        // 校验新户流转是否存在
        NextCheckDTO checkResult = nextCheck(deptId, vo);
        if (checkResult.getCheckResult() == 1 || checkResult.getCheckResult() == 2) {
            throw new ServiceException(checkResult.getCheckErrorMsg());
        }
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        Long employeeId = ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId();
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        NewCustomerCreateBaseInfoVO baseInfo = vo.getBaseInfo();
        NewCustomerInfo newCustomerInfo = new NewCustomerInfo().setCustomerName(baseInfo.getCustomerName())
                .setCreditCode(baseInfo.getCreditCode()).setTaxNumber(StringUtils.isEmpty(baseInfo.getTaxNumber()) ? baseInfo.getCreditCode() : baseInfo.getTaxNumber())
                .setRegistrationDate(baseInfo.getRegistrationDate())
                .setProvinceCode(baseInfo.getProvinceCode()).setProvinceName(baseInfo.getProvinceName())
                .setCityCode(baseInfo.getCityCode()).setCityName(baseInfo.getCityName())
                .setAreaCode(baseInfo.getAreaCode()).setAreaName(baseInfo.getAreaName())
                .setRegistrationRegion(baseInfo.getProvinceName() + baseInfo.getCityName() + (StringUtils.isEmpty(baseInfo.getAreaName()) ? "" : baseInfo.getAreaName()))
                .setIndustry(baseInfo.getIndustry()).setTaxType(baseInfo.getTaxType())
                .setBusinessDeptId(deptId).setBusinessTopDeptId(businessTopDeptId)
                .setAdvisorDeptId(baseInfo.getAdvisorDeptId()).setAdvisorTopDeptId(deptId)
                .setFirstAccountPeriod(DateUtils.getNowPeriod())
                .setStatus(NewCustomerTransferStatus.UN_PERFECT.getCode())
                .setIsNewCustomer(baseInfo.getIsNewCustomer())
                .setIsDel(Boolean.FALSE).setSubmitTime(LocalDateTime.now())
                .setSortTime(LocalDateTime.now())
                .setCreateEmployeeId(employeeId)
                .setCreateEmployeeName(employeeName)
                .setFirstAccountingPeriod(DateUtils.yearMonthToPeriod(baseInfo.getFirstAccountingPeriod()))
                .setCreateRemark(baseInfo.getRemark());
        save(newCustomerInfo);
        Long newCustomerTransferId = newCustomerInfo.getId();
        // 保存附件
        newCustomerTransferFileService.removeAndSaveNewFiles(newCustomerTransferId, NewCustomerTransferFileType.CREATE_FILE, baseInfo.getFiles());
        List<Long> tagIds = ObjectUtils.isEmpty(vo.getTags()) ? Lists.newArrayList() :
                vo.getTags().stream().map(TagDTO::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(tagIds)) {
            if (tagIds.contains(specialTagProperties.getSanling()) && !tagIds.contains(specialTagProperties.getLingshenbao())) {
                vo.getTags().add(TagDTO.builder().id(specialTagProperties.getLingshenbao()).tagName("0申报").fullTagName("0申报").tagType(1).isSelected(true).build());
            }
        }
        // 保存标签
        businessTagRelationService.saveByBusinessIdAndBusinessType(newCustomerTransferId, TagBusinessType.NEW_CUSTOMER_TRANSFER, vo.getTags());
        // 创建默认的收入
        newCustomerIncomeInfoService.createDefaultIncomeList(newCustomerInfo);
        if (checkResult.getCheckResult() == 3) {
            NewCustomerOtherInfoDTO endCustomerServiceInfo = getEndCustomerServiceInfo(deptId, vo);
            newCustomerBankAccountService.removeAndCreateBankAccount(newCustomerTransferId, endCustomerServiceInfo.getBankList());
            newCustomerSysAccountService.removeAndCreateSysAccount(newCustomerTransferId, endCustomerServiceInfo.getSysAccountList());
            newCustomerTaxTypeCheckService.removeAndCreateTaxTypeCheck(newCustomerTransferId, endCustomerServiceInfo.getTaxTypeCheckList());
        }
        Map<String, String> operContent = Maps.newHashMap();
        operContent.put("操作时间", LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        operContent.put("操作人", employeeName);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(newCustomerTransferId)
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("新建新户流转记录")
                    .setOperName(employeeName)
                    .setOperUserId(userId)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperRemark(baseInfo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(baseInfo.getFiles()) ? "" :
                            JSONArray.toJSONString(baseInfo.getFiles().stream().map(file -> CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList()))));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return newCustomerTransferId;
    }

    @Override
    public Long modifyNewCustomerBaseInfo(Long deptId, NewCustomerCreateVO vo) {
        NewCustomerCreateBaseInfoVO baseInfo = vo.getBaseInfo();
        baseInfo.setRegisterLocation(baseInfo.getProvinceName() + baseInfo.getCityName() + (StringUtils.isEmpty(baseInfo.getAreaName()) ? "" : baseInfo.getAreaName()));
        if (Integer.parseInt(baseInfo.getRegistrationDate().replaceAll("-", "").substring(0, 4)) > DateUtils.getNowPeriod()) {
            throw new ServiceException("注册日期不能大于当前月");
        }
        NewCustomerInfo newCustomerInfo = getById(vo.getId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户流转不存在");
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept) || sysDept.getDeptType() != 1) {
            throw new ServiceException("当前业务公司有误");
        }
        Long businessTopDeptId = sysDept.getParentId();
        NextCheckDTO checkResult = nextCheck(deptId, vo);
        if (checkResult.getCheckResult() == 1 || checkResult.getCheckResult() == 2) {
            throw new ServiceException(checkResult.getCheckErrorMsg());
        }
        String oldAdvisorDeptInfo = "";
        String newAdvisorDeptInfo = "";
        if (!Objects.isNull(newCustomerInfo.getAdvisorDeptId())) {
            SysDept advisorDept = remoteDeptService.getDeptInfo(newCustomerInfo.getAdvisorDeptId()).getDataThrowException(false);
            List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(newCustomerInfo.getAdvisorDeptId()).getDataThrowException(false);
            if (!Objects.isNull(advisorDept)) {
                oldAdvisorDeptInfo = advisorDept.getDeptName() + "(" + advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + ")";
            }
        }
        if (!Objects.isNull(vo.getBaseInfo().getAdvisorDeptId())) {
            SysDept advisorDept = remoteDeptService.getDeptInfo(vo.getBaseInfo().getAdvisorDeptId()).getDataThrowException(false);
            List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(vo.getBaseInfo().getAdvisorDeptId()).getDataThrowException(false);
            if (!Objects.isNull(advisorDept)) {
                newAdvisorDeptInfo = advisorDept.getDeptName() + "(" + advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + ")";
            }
        }
        String operContent = getModifyBaseInfoOperContent(newCustomerInfo, vo.getBaseInfo(), oldAdvisorDeptInfo, newAdvisorDeptInfo);
        update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getId())
                .set(NewCustomerInfo::getCustomerName, baseInfo.getCustomerName())
                .set(NewCustomerInfo::getCreditCode, baseInfo.getCreditCode())
                .set(NewCustomerInfo::getTaxNumber, baseInfo.getTaxNumber())
                .set(NewCustomerInfo::getRegistrationDate, baseInfo.getRegistrationDate())
                .set(NewCustomerInfo::getRegistrationRegion, baseInfo.getRegisterLocation())
                .set(NewCustomerInfo::getIndustry, baseInfo.getIndustry())
                .set(NewCustomerInfo::getTaxType, baseInfo.getTaxType())
                .set(NewCustomerInfo::getBusinessDeptId, baseInfo.getBusinessDeptId())
                .set(NewCustomerInfo::getBusinessTopDeptId, businessTopDeptId)
                .set(NewCustomerInfo::getAdvisorDeptId, baseInfo.getAdvisorDeptId())
                .set(NewCustomerInfo::getAdvisorTopDeptId, baseInfo.getBusinessDeptId())
                .set(NewCustomerInfo::getFirstAccountPeriod, DateUtils.getNowPeriod())
                .set(NewCustomerInfo::getIsNewCustomer, baseInfo.getIsNewCustomer())
                .set(NewCustomerInfo::getFirstAccountingPeriod, DateUtils.yearMonthToPeriod(baseInfo.getFirstAccountingPeriod()))
                .set(NewCustomerInfo::getProvinceCode, baseInfo.getProvinceCode())
                .set(NewCustomerInfo::getProvinceName, baseInfo.getProvinceName())
                .set(NewCustomerInfo::getCityCode, baseInfo.getCityCode())
                .set(NewCustomerInfo::getCityName, baseInfo.getCityName())
                .set(NewCustomerInfo::getAreaCode, baseInfo.getAreaCode())
                .set(NewCustomerInfo::getAreaName, baseInfo.getAreaName())
                .set(NewCustomerInfo::getCreateRemark, baseInfo.getRemark()));
        newCustomerTransferFileService.removeAndSaveNewFiles(vo.getId(), NewCustomerTransferFileType.CREATE_FILE, baseInfo.getFiles());
        // 更新标签
        businessTagRelationService.deleteByBusinessIdAndBusinessType(vo.getId(), TagBusinessType.NEW_CUSTOMER_TRANSFER);
        List<Long> tagIds = ObjectUtils.isEmpty(vo.getTags()) ? Lists.newArrayList() :
                vo.getTags().stream().map(TagDTO::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(tagIds)) {
            if (tagIds.contains(specialTagProperties.getSanling()) && !tagIds.contains(specialTagProperties.getLingshenbao())) {
                vo.getTags().add(TagDTO.builder().id(specialTagProperties.getLingshenbao()).tagName("0申报").fullTagName("0申报").tagType(1).isSelected(true).build());
            }
        }
        businessTagRelationService.saveByBusinessIdAndBusinessType(vo.getId(), TagBusinessType.NEW_CUSTOMER_TRANSFER, vo.getTags());
        if (checkResult.getCheckResult() == 3) {
            NewCustomerOtherInfoDTO endCustomerServiceInfo = getEndCustomerServiceInfo(deptId, vo);
            newCustomerBankAccountService.removeAndCreateBankAccount(vo.getId(), endCustomerServiceInfo.getBankList());
            newCustomerSysAccountService.removeAndCreateSysAccount(vo.getId(), endCustomerServiceInfo.getSysAccountList());
            newCustomerTaxTypeCheckService.removeAndCreateTaxTypeCheck(vo.getId(), endCustomerServiceInfo.getTaxTypeCheckList());
        }
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑基本信息")
                    .setOperContent(operContent)
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return vo.getId();
    }

    private Integer checkNewCustomerRepeatByNewCreditCode(String creditCode, Long businessTopDeptId, Long id) {
        return count(new LambdaQueryWrapper<NewCustomerInfo>()
                .eq(NewCustomerInfo::getCreditCode, creditCode)
                .eq(NewCustomerInfo::getBusinessTopDeptId, businessTopDeptId)
                .ne(!Objects.isNull(id), NewCustomerInfo::getId, id)
                .in(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode(), NewCustomerTransferStatus.UN_SUBMIT.getCode(), NewCustomerTransferStatus.UN_PERFECT.getCode(), NewCustomerTransferStatus.UN_RESUBMIT.getCode())
                .eq(NewCustomerInfo::getIsDel, false));
    }

    private Integer checkNewCustomerRepeatByNewCustomerName(String customerName, Long businessTopDeptId, Long id) {
        return count(new LambdaQueryWrapper<NewCustomerInfo>()
                .eq(NewCustomerInfo::getCustomerName, customerName)
                .eq(NewCustomerInfo::getBusinessTopDeptId, businessTopDeptId)
                .ne(!Objects.isNull(id), NewCustomerInfo::getId, id)
                .in(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode(), NewCustomerTransferStatus.UN_SUBMIT.getCode(), NewCustomerTransferStatus.UN_PERFECT.getCode(), NewCustomerTransferStatus.UN_RESUBMIT.getCode())
                .eq(NewCustomerInfo::getIsDel, false));
    }

    @Override
    public List<NewCustomerTransferBankAccountDTO> getNewCustomerTransferBankAccountList(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        List<NewCustomerBankAccount> newCustomerBankAccounts = newCustomerBankAccountService.selectByNewCustomerId(id);
        return ObjectUtils.isEmpty(newCustomerBankAccounts) ? Lists.newArrayList() : newCustomerBankAccounts.stream().map(r -> {
            NewCustomerTransferBankAccountDTO dto = new NewCustomerTransferBankAccountDTO();
            BeanUtils.copyProperties(r, dto);
            dto.setStatus(BankStatus.getBankStatusByCloseDate(r.getAccountCloseDate()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteNewCustomerTransferBankAccount(CommonIdVO vo) {
        newCustomerBankAccountService.deleteCustomerServiceBankAccount(vo.getId());
    }

    @Override
    public void addNewCustomerTransferBankAccount(NewCustomerTransferBankAccountDTO vo) {
        NewCustomerInfo newCustomerInfo = getById(vo.getNewCustomerTransferId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户信息不存在");
        }
        newCustomerBankAccountService.addNewCustomerTransferBankAccount(vo, newCustomerInfo);
    }

    @Override
    public void modifyNewCustomerTransferAccount(NewCustomerTransferBankAccountDTO vo) {
        NewCustomerInfo newCustomerInfo = getById(vo.getNewCustomerTransferId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户信息不存在");
        }
        newCustomerBankAccountService.modifyNewCustomerTransferAccount(vo, newCustomerInfo);
    }

    @Override
    public List<NewCustomerTransferSysAccountDTO> getNewCustomerTransferSysAccount(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        List<NewCustomerSysAccount> accounts = newCustomerSysAccountService.selectByCustomerServiceId(id);
        if (ObjectUtils.isEmpty(accounts)) {
            return Collections.emptyList();
        }
        return accounts.stream().map(account -> {
            NewCustomerTransferSysAccountDTO dto = new NewCustomerTransferSysAccountDTO();
            BeanUtils.copyProperties(account, dto);
            dto.setSysAccountType(account.getSysType());
            dto.setSysAccountTypeName(account.getSysTypeName());
            dto.setLoginType(account.getLoginType());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void addNewCustomerTransferSysAccount(NewCustomerTransferSysAccountDTO dto) {
        if (StringUtils.isEmpty(dto.getSysAccountTypeName())) {
            dto.setSysAccountTypeName(SysAccountType.getByCode(dto.getSysAccountType()).getName());
        }
        if (checkSysAccountExist(dto)) {
            throw new ServiceException("系统账号已存在");
        }
        NewCustomerSysAccount customerSysAccount = new NewCustomerSysAccount().setCustomerId(dto.getNewCustomerTransferId())
                .setSysType(dto.getSysAccountType())
                .setSysTypeName(StringUtils.isEmpty(dto.getSysAccountTypeName()) ? SysAccountType.getByCode(dto.getSysAccountType()).getName() : dto.getSysAccountTypeName())
                .setAccount(dto.getAccount())
                .setPassword(dto.getPassword())
                .setLoginType(dto.getLoginType())
                .setContact(dto.getContact())
                .setContactMobile(dto.getContactMobile())
                .setIdNumber(dto.getIdNumber())
                .setRemark(dto.getRemark())
                .setIsSameWithCreditCode(dto.getIsSameWithCreditCode());
        newCustomerSysAccountService.save(customerSysAccount);
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(dto.getNewCustomerTransferId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("新增系统账号")
                    .setOperContent(String.format("新增系统账号：%s", customerSysAccount.getSysTypeName()))
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void modifyNewCustomerTransferSysAccount(NewCustomerTransferSysAccountDTO dto) {
        NewCustomerSysAccount sysAccount = newCustomerSysAccountService.getById(dto.getId());
        if (Objects.isNull(sysAccount) || sysAccount.getIsDel()) {
            throw new ServiceException("系统账号不存在");
        }
        if (StringUtils.isEmpty(dto.getSysAccountTypeName())) {
            dto.setSysAccountTypeName(SysAccountType.getByCode(dto.getSysAccountType()).getName());
        }
        if (checkSysAccountExist(dto)) {
            throw new ServiceException("系统账号已存在");
        }
        newCustomerSysAccountService.updateById(new NewCustomerSysAccount().setId(dto.getId())
                .setSysType(dto.getSysAccountType())
                .setSysTypeName(StringUtils.isEmpty(dto.getSysAccountTypeName()) ? SysAccountType.getByCode(dto.getSysAccountType()).getName() : dto.getSysAccountTypeName())
                .setAccount(dto.getAccount())
                .setPassword(dto.getPassword())
                .setLoginType(dto.getLoginType())
                .setContact(dto.getContact())
                .setContactMobile(dto.getContactMobile())
                .setIdNumber(dto.getIdNumber())
                .setRemark(dto.getRemark())
                .setIsSameWithCreditCode(dto.getIsSameWithCreditCode()));
        String operContent = getSysAccountOperContent(sysAccount, dto);
        if (!StringUtils.isEmpty(operContent)) {
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(sysAccount.getCustomerId())
                        .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑系统账号")
                        .setOperContent(operContent)
                        .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private boolean checkSysAccountExist(NewCustomerTransferSysAccountDTO dto) {
        return newCustomerSysAccountService.count(new LambdaQueryWrapper<NewCustomerSysAccount>()
                .eq(NewCustomerSysAccount::getIsDel, false)
                .eq(NewCustomerSysAccount::getCustomerId, dto.getNewCustomerTransferId())
                .eq(NewCustomerSysAccount::getSysTypeName, dto.getSysAccountTypeName())
                .ne(!Objects.isNull(dto.getId()), NewCustomerSysAccount::getId, dto.getId())) > 0;
    }

    @Override
    @Transactional
    public void deleteNewCustomerTransferSysAccount(CommonIdVO vo) {
        if (Objects.isNull(vo.getId())) {
            return;
        }
        NewCustomerSysAccount sysAccount = newCustomerSysAccountService.getById(vo.getId());
        if (Objects.isNull(sysAccount)) {
            return;
        }
        newCustomerSysAccountService.update(new LambdaUpdateWrapper<NewCustomerSysAccount>()
                .set(NewCustomerSysAccount::getIsDel, Boolean.TRUE)
                .eq(NewCustomerSysAccount::getIsDel, Boolean.FALSE)
                .eq(NewCustomerSysAccount::getId, vo.getId()));
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(sysAccount.getCustomerId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("删除系统账号")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperContent(String.format("删除系统账号：%s", sysAccount.getSysTypeName()))
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public NewCustomerServiceFinanceTaxInfoDTO newCustomerServiceFinanceTaxInfo(Long id) {
        if (Objects.isNull(id)) {
            return new NewCustomerServiceFinanceTaxInfoDTO();
        }
        NewCustomerFinanceTaxInfo taxInfo = newCustomerFinanceTaxInfoService.selectByNewCustomerTransferId(id);
        if (Objects.isNull(taxInfo)) {
            return new NewCustomerServiceFinanceTaxInfoDTO();
        }
        NewCustomerServiceFinanceTaxInfoDTO dto = new NewCustomerServiceFinanceTaxInfoDTO();
        BeanUtils.copyProperties(taxInfo, dto);
        dto.setNewCustomerTransferId(taxInfo.getCustomerId());
        return dto;
    }

    @Override
    @Transactional
    public void editNewCustomerServiceTaxTypeCheck(NewCustomerServiceTaxTypeCheckDTO dto) {
        List<NewCustomerTaxTypeCheck> taxTypeList = newCustomerTaxTypeCheckService.selectByNewCustomerTransferId(dto.getNewCustomerTransferId());
        List<NewCustomerTaxTypeCheck> addData = Lists.newArrayList();
        List<NewCustomerTaxTypeCheck> deleteData = Lists.newArrayList();
        List<NewCustomerTaxTypeCheck> updateData = Lists.newArrayList();
        if (ObjectUtils.isEmpty(taxTypeList)) {
            if (!ObjectUtils.isEmpty(dto.getTaxTypes())) {
                dto.getTaxTypes().forEach(taxType ->
                        addData.add(new NewCustomerTaxTypeCheck().setCustomerId(dto.getNewCustomerTransferId())
                                .setReportType(taxType.getReportType())
                                .setTaxType(taxType.getTaxTypeName()))
                );
            }
        } else {
            Map<Long, NewCustomerTaxTypeCheck> checkMap = taxTypeList.stream().collect(Collectors.toMap(NewCustomerTaxTypeCheck::getId, Function.identity()));
            if (!ObjectUtils.isEmpty(dto.getTaxTypes())) {
                dto.getTaxTypes().forEach(taxType -> {
                    if (Objects.isNull(taxType.getId())) {
                        // id为空 代表是新增的
                        addData.add(new NewCustomerTaxTypeCheck().setCustomerId(dto.getNewCustomerTransferId())
                                .setReportType(taxType.getReportType())
                                .setTaxType(taxType.getTaxTypeName()));
                    } else {
                        // id不为空 代表是原本就存在的
                        NewCustomerTaxTypeCheck oldCheck = checkMap.get(taxType.getId());
                        NewCustomerTaxTypeCheck newCheck = new NewCustomerTaxTypeCheck().setId(taxType.getId())
                                .setTaxType(taxType.getTaxTypeName()).setReportType(taxType.getReportType())
                                .setCustomerId(dto.getNewCustomerTransferId());
                        if (!newCheck.equals(oldCheck)) {
                            updateData.add(newCheck);
                        }
                        checkMap.remove(taxType.getId());
                    }
                });
                if (!ObjectUtils.isEmpty(checkMap)) {
                    for (Map.Entry<Long, NewCustomerTaxTypeCheck> entry : checkMap.entrySet()) {
                        deleteData.add(entry.getValue());
                    }
                }
            } else {
                deleteData = taxTypeList;
            }
        }
        if (!ObjectUtils.isEmpty(addData)) {
            newCustomerTaxTypeCheckService.saveBatch(addData);
        }
        if (!ObjectUtils.isEmpty(updateData)) {
            newCustomerTaxTypeCheckService.updateBatchById(updateData);
        }
        if (!ObjectUtils.isEmpty(deleteData)) {
            newCustomerTaxTypeCheckService.removeByIds(deleteData.stream().map(NewCustomerTaxTypeCheck::getId).collect(Collectors.toList()));
        }
        String operContent = ObjectUtils.isEmpty(dto.getTaxTypes()) ? "" : String.format("月报税种为：%s，季报税种为：%s，次报税种为：%s，年报税种为：%s，半年报税种为：%s，无需申报税种为：%s，",
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 1).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(",")),
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 2).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(",")),
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 3).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(",")),
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 4).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(",")),
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 5).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(",")),
                dto.getTaxTypes().stream().filter(taxType -> taxType.getReportType() == 6).map(NewCustomerServiceTaxTypeCheckVO::getTaxTypeName).collect(Collectors.joining(","))
        );
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(dto.getNewCustomerTransferId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑税务信息")
                    .setOperContent(StringUtils.isEmpty(operContent) ? "" : operContent.substring(0, operContent.length() - 1))
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<NewCustomerServiceTaxTypeCheckVO> newCustomerTransferTaxTypeCheckList(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        NewCustomerInfo newCustomerInfo = getById(id);
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            return Collections.emptyList();
        }
        return newCustomerTaxTypeCheckService.selectCustomerTaxTypeCheckByNewCustomerTransferId(id, newCustomerInfo.getTaxType());

    }

    @Override
    @Transactional
    public void modifyNewCustomerServiceFinanceTaxInfo(NewCustomerServiceFinanceTaxInfoDTO vo) {
        String taxInfoOperContent = newCustomerFinanceTaxInfoService.editCustomerServiceFinanceTaxInfo(vo);
        if (!StringUtils.isEmpty(taxInfoOperContent)) {
            Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
            Long userId = SecurityUtils.getUserId();
            List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getNewCustomerTransferId())
                        .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑财税信息")
                        .setOperContent(taxInfoOperContent.substring(0, taxInfoOperContent.length() - 1))
                        .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    @Transactional
    public NewCustomerTransferDetailInfoDTO detailInfo(Long id) {
        NewCustomerInfo newCustomerInfo = getById(id);
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户流转不存在");
        }
        List<TagDTO> tagList = businessTagRelationService.getTagsByBusinessTypeForDetail(newCustomerInfo.getId(), TagBusinessType.NEW_CUSTOMER_TRANSFER);
        NewCustomerCreateBaseInfoDTO baseInfoDTO = buildBaseInfo(newCustomerInfo, tagList);
        NewCustomerServiceFinanceTaxInfoDTO taxInfoDTO = newCustomerFinanceTaxInfoService.buildTaxInfo(newCustomerInfo);
//        NewCustomerTransferInsuranceInfoDTO insuranceInfoDTO = newCustomerInsuranceFundInfoService.buildInsuranceInfo(newCustomerInfo, tagList);
//        NewCustomerTransferOtherInfoDTO otherInfoDTO = newCustomerAnnualReportInfoService.buildOtherInfo(newCustomerInfo);
        return NewCustomerTransferDetailInfoDTO.builder()
                .newCustomerTransferId(id)
                .isOpenBankAccount(newCustomerInfo.getIsOpenBankAccount())
                .baseInfo(baseInfoDTO)
                .taxInfo(taxInfoDTO)
//                .insuranceInfo(insuranceInfoDTO)
//                .otherInfo(otherInfoDTO)
                .build();
    }

    @Override
    @Transactional
    public Long saveCustomerInfo(NewCustomerTransferDetailInfoDTO vo, Long deptId) {
        NewCustomerInfo newCustomerInfo = getById(vo.getNewCustomerTransferId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户流转不存在");
        }
        update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getNewCustomerTransferId())
                .set(NewCustomerInfo::getIsOpenBankAccount, vo.getIsOpenBankAccount()));
        newCustomerInfo.setIsOpenBankAccount(vo.getIsOpenBankAccount());
        updateNewCustomerTransferOther(vo);
        List<Long> tagIds = businessTagRelationService.selectByBusinessIdAndBusinessType(vo.getNewCustomerTransferId(), TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode()).stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
        Integer finalStatus = checkAndSaveStatus(vo.getNewCustomerTransferId(), newCustomerInfo, tagIds);
        update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getNewCustomerTransferId()).set(NewCustomerInfo::getFirstAccountPeriod, DateUtils.getNowPeriod()).set(NewCustomerInfo::getStatus, finalStatus));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getNewCustomerTransferId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("保存新户流转记录")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        return vo.getNewCustomerTransferId();
    }

    private Integer checkAndSaveStatus(Long newCustomerTransferId, NewCustomerInfo newCustomerInfo, List<Long> tagIds) {
        NewCustomerFinanceTaxInfo taxInfo = newCustomerFinanceTaxInfoService.selectByNewCustomerTransferId(newCustomerTransferId);
        if (Objects.isNull(taxInfo)) {
            log.info("财税信息为空，信息不完善");
            return NewCustomerTransferStatus.UN_PERFECT.getCode();
        }
        // 检查财税信息必填项
        if (newCustomerInfo.getIsNewCustomer()) {
            if (Objects.isNull(taxInfo.getFinanceRecord()) || Objects.isNull(taxInfo.getReportSent()) ||
                    Objects.isNull(taxInfo.getTaxDisk())) {
                log.info("是新户但财税必填信息不完整，信息不完善");
                return NewCustomerTransferStatus.UN_PERFECT.getCode();
            }
        } else {
            if (Objects.isNull(taxInfo.getFinanceRecord()) || Objects.isNull(taxInfo.getReportSent()) || StringUtils.isEmpty(taxInfo.getAccountingSystem()) || Objects.isNull(taxInfo.getLastAccountMonth()) ||
                    Objects.isNull(taxInfo.getTaxDisk()) || Objects.isNull(taxInfo.getTaxMethod()) ||
                    (taxInfo.getTaxMethod() == 1 && Objects.isNull(taxInfo.getEzTaxAccount()))) {
                log.info("不是新户但财税必填信息不完整，信息不完善");
                return NewCustomerTransferStatus.UN_PERFECT.getCode();
            }
        }
//        // 检查五险一金必填项
//        NewCustomerInsuranceFundInfo insuranceFundInfo = newCustomerInsuranceFundInfoService.selectByCustomerId(newCustomerTransferId);
//        Set<Integer> typeList = newCustomerInsuranceFundStatusService.selectByCustomerId(newCustomerTransferId)
//                .stream().map(NewCustomerInsuranceFundStatus::getType).collect(Collectors.toSet());
//        if (tagIds.contains(specialTagProperties.getYibao())) {
//            if (StringUtils.isEmpty(insuranceFundInfo.getMedicalBase()) || StringUtils.isEmpty(insuranceFundInfo.getMedicalFee()) || StringUtils.isEmpty(insuranceFundInfo.getMedicalContact()) ||
//                    StringUtils.isEmpty(insuranceFundInfo.getMedicalRate()) || StringUtils.isEmpty(insuranceFundInfo.getMedicalPeople()) || !typeList.contains(1)) {
//                log.info("有医保标签但医保必填项不完整，信息不完善");
//                return NewCustomerTransferStatus.UN_PERFECT.getCode();
//            }
//        }
//        if (tagIds.contains(specialTagProperties.getShebao())) {
//            if (StringUtils.isEmpty(insuranceFundInfo.getSocialSecurityBase()) || StringUtils.isEmpty(insuranceFundInfo.getSocialSecurityPeople()) || StringUtils.isEmpty(insuranceFundInfo.getSocialSecurityContact()) ||
//                    StringUtils.isEmpty(insuranceFundInfo.getInjuryFee()) || StringUtils.isEmpty(insuranceFundInfo.getInjuryRate()) || StringUtils.isEmpty(insuranceFundInfo.getTotalContribution()) || !typeList.contains(2)) {
//                log.info("有社保标签但社保必填项不完整，信息不完善");
//                return NewCustomerTransferStatus.UN_PERFECT.getCode();
//            }
//        }
//        if (tagIds.contains(specialTagProperties.getGongjijin())) {
//            if (StringUtils.isEmpty(insuranceFundInfo.getFundBase()) || StringUtils.isEmpty(insuranceFundInfo.getFundFee()) || StringUtils.isEmpty(insuranceFundInfo.getFundContact()) ||
//                    StringUtils.isEmpty(insuranceFundInfo.getFundRate()) || StringUtils.isEmpty(insuranceFundInfo.getFundPeople()) || !typeList.contains(3)) {
//                log.info("有公积金标签但公积金必填项不完整，信息不完善");
//                return NewCustomerTransferStatus.UN_PERFECT.getCode();
//            }
//        }
//        LocalDate startDateTemp = LocalDate.now().minusMonths(12);
//        LocalDate registerDateTemp = DateUtils.strToLocalDate(newCustomerInfo.getRegistrationDate(), "yyyy-MM-dd");
//        LocalDate startDate = LocalDate.of(registerDateTemp.getYear(), registerDateTemp.getMonth(), 1);
//        LocalDate registerDate = LocalDate.of(registerDateTemp.getYear(), registerDateTemp.getMonth(), 1);
//        List<Integer> yearMonthList = getThis12YearMonthList(!registerDate.isBefore(startDateTemp) ? registerDate : startDate);
//        // 检查其他信息
//        if (newCustomerIncomeInfoService.countByCustomerId(newCustomerTransferId) < yearMonthList.size()) {
//            log.info("收入数据少于" + yearMonthList + "个月，信息不完善");
//            return NewCustomerTransferStatus.UN_PERFECT.getCode();
//        }
//        NewCustomerAnnualReportInfo newCustomerAnnualReportInfo = newCustomerAnnualReportInfoService.selectByCustomerId(newCustomerTransferId);
//        if (Objects.isNull(newCustomerAnnualReportInfo) ||
//                Objects.isNull(newCustomerAnnualReportInfo.getEBusinessLicense()) ||
//                StringUtils.isEmpty(newCustomerAnnualReportInfo.getContactName()) || StringUtils.isEmpty(newCustomerAnnualReportInfo.getContactMobile()) ||
//                StringUtils.isEmpty(newCustomerAnnualReportInfo.getContactIdNumber())) {
//            log.info("工商年报信息不完整，信息不完善");
//            return NewCustomerTransferStatus.UN_PERFECT.getCode();
//        }
//        if (DateUtils.strToLocalDate(newCustomerInfo.getRegistrationDate(), DateUtils.YYYY_MM_DD).getYear() != LocalDate.now().getYear()) {
//            NewCustomerOtherInfo newCustomerOtherInfo = newCustomerOtherInfoService.selectByCustomerId(newCustomerTransferId);
//            if (Objects.isNull(newCustomerOtherInfo) || Objects.isNull(newCustomerOtherInfo.getTaxSubmissionStatus()) ||
//                    Objects.isNull(newCustomerOtherInfo.getNextYearSupplement()) || Objects.isNull(newCustomerOtherInfo.getPreTaxProfit()) ||
//                    newCustomerFixedAssetsInfoService.countByCustomerId(newCustomerTransferId) == 0 ||
//                    Objects.isNull(newCustomerAnnualReportInfo.getStatus())) {
//                log.info("注册日期是当年但汇算清缴信息不完整，信息不完善");
//                return NewCustomerTransferStatus.UN_PERFECT.getCode();
//            }
//        }
        if (newCustomerInfo.getIsOpenBankAccount() && newCustomerBankAccountService.countByNewCustomerId(newCustomerTransferId) == 0) {
            log.info("银行已开户，但开户银行信息为空，信息不完善");
            return NewCustomerTransferStatus.UN_PERFECT.getCode();
        }
        return NewCustomerTransferStatus.UN_SUBMIT.getCode();
    }

    private List<Integer> getThis12YearMonthList(LocalDate startDate) {
        List<Integer> yearMonthList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate endDate = LocalDate.of(now.getYear(), now.getMonthValue(), 1);
        while (!startDate.isAfter(endDate)) {
            yearMonthList.add(startDate.getYear() * 100 + startDate.getMonthValue());
            startDate = startDate.plusMonths(1);
        }
        return yearMonthList;
    }

    @Override
    @Transactional
    public Long submitCustomerInfo(NewCustomerTransferDetailInfoDTO vo, Long deptId) {
        NewCustomerInfo newCustomerInfo = getById(vo.getNewCustomerTransferId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户流转不存在");
        }
        update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getNewCustomerTransferId())
                .set(NewCustomerInfo::getIsOpenBankAccount, vo.getIsOpenBankAccount()));
        newCustomerInfo.setIsOpenBankAccount(vo.getIsOpenBankAccount());
        updateNewCustomerTransferOther(vo);
        List<Long> tagIds = businessTagRelationService.selectByBusinessIdAndBusinessType(vo.getNewCustomerTransferId(), TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode()).stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
        Integer finalStatus = checkAndSaveStatus(vo.getNewCustomerTransferId(), newCustomerInfo, tagIds);
        if (!Objects.equals(finalStatus, NewCustomerTransferStatus.UN_SUBMIT.getCode())) {
            update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getNewCustomerTransferId()).set(NewCustomerInfo::getFirstAccountPeriod, DateUtils.getNowPeriod()).set(NewCustomerInfo::getStatus, finalStatus));
            return -1L;
        }
        // 删除一些不可见的数据
        removeDontNeedDatas(vo.getNewCustomerTransferId(), newCustomerInfo, tagIds);
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        update(new LambdaUpdateWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getId, vo.getNewCustomerTransferId())
                .set(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode())
                .set(NewCustomerInfo::getSubmitTime, LocalDateTime.now())
                .set(NewCustomerInfo::getSortTime, LocalDateTime.now())
                .set(NewCustomerInfo::getFirstAccountPeriod, DateUtils.getNowPeriod())
                .set(NewCustomerInfo::getSubmitEmployeeName, ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName()));
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getNewCustomerTransferId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("提交新户流转")
                    .setOperName(operName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        autoTransfer(newCustomerInfo, userId, deptId, operName);
        return vo.getNewCustomerTransferId();
    }

    private void autoTransfer(NewCustomerInfo newCustomerInfo, Long userId, Long deptId, String operName) {
        List<NewCustomerBusinessTopDeptCreditCodeVO> voList = Collections.singletonList(NewCustomerBusinessTopDeptCreditCodeVO.builder().businessTopDeptId(newCustomerInfo.getBusinessTopDeptId()).creditCode(newCustomerInfo.getCreditCode()).build());
        Map<String, List<CCustomerService>> customerServiceMap = ObjectUtils.isEmpty(voList) ? Maps.newHashMap() :
                customerServiceMapper.selectEndCustomerServiceBatchByBusinessTopDeptIdAndCreditCode(voList).stream().collect(Collectors.groupingBy(c -> c.getBusinessTopDeptId() + "_" + c.getCreditCode()));
        Map<Long, List<NewCustomerTransferFile>> fileMap = newCustomerTransferFileService.list(new LambdaQueryWrapper<NewCustomerTransferFile>()
                        .eq(NewCustomerTransferFile::getIsDel, false)
                        .eq(NewCustomerTransferFile::getCustomerId, newCustomerInfo.getId())
                        .eq(NewCustomerTransferFile::getFileType, NewCustomerTransferFileType.CREATE_FILE.getCode()))
                .stream().collect(Collectors.groupingBy(NewCustomerTransferFile::getCustomerId));
        Long targetAccountingTopDeptId = getTransferTargetAccountingTopDeptId(newCustomerInfo, fileMap);
        if (!Objects.isNull(targetAccountingTopDeptId)) {
            List<CCustomerService> endCustomerList = customerServiceMap.get(newCustomerInfo.getBusinessTopDeptId() + "_" + newCustomerInfo.getCreditCode());
            if (!ObjectUtils.isEmpty(endCustomerList)) {
                endCustomerList.sort(Comparator.comparing(CCustomerService::getEndAccountPeriod).reversed());
            }
            NewCustomerTransferVO vo = new NewCustomerTransferVO();
            vo.setServiceNumber(ObjectUtils.isEmpty(endCustomerList) ? newCustomerInfo.getId().toString() : endCustomerList.get(0).getServiceNumber());
            vo.setAccountTopDeptId(targetAccountingTopDeptId);
            vo.setId(newCustomerInfo.getId());
            autoTransfer(vo, newCustomerInfo, userId, deptId, operName);
        } else {
            log.info("新户id:{}，不是特殊组但存在备注或附件，不流转", newCustomerInfo.getId());
        }
    }

    private Long getTransferTargetAccountingTopDeptId(NewCustomerInfo newCustomerInfo, Map<Long, List<NewCustomerTransferFile>> fileMap) {
        Long targetAccountingTopDeptId;
        if (!Objects.equals(newCustomerInfo.getBusinessTopDeptId(), specialDeptIdProperties.getYtjt()) && !Objects.equals(newCustomerInfo.getBusinessTopDeptId(), specialDeptIdProperties.getJd())) {
            // 不是永泰和君道 流转到福建区
            targetAccountingTopDeptId = specialDeptIdProperties.getFjq();
        } else {
            if (Objects.equals(newCustomerInfo.getBusinessDeptId(), specialDeptIdProperties.getXm()) && Objects.equals(newCustomerInfo.getTaxType(), TaxType.COMMONLY.getCode())) {
                // 如果业务公司是厦门，且纳税人性质是一般纳税人。自动流转给福建二区
                targetAccountingTopDeptId = specialDeptIdProperties.getFjeq();
            } else {
                if (specialDeptlistProperties.getDeptList().contains(newCustomerInfo.getAdvisorDeptId())) {
                    // 是特殊组 流转到福建二区
                    targetAccountingTopDeptId = specialDeptIdProperties.getFjeq();
                } else {
                    if (!StringUtils.isEmpty(newCustomerInfo.getCreateRemark()) || fileMap.containsKey(newCustomerInfo.getId())) {
                        // 存在备注或附件 不自动流转
                        targetAccountingTopDeptId = null;
                    } else {
                        // 不存在备注和附件 流转到福建区
                        targetAccountingTopDeptId = specialDeptIdProperties.getFjq();
                    }
                }
            }
        }
        return targetAccountingTopDeptId;
    }

    @Override
    public ServiceNumberRepeatCheckResultDTO serviceNumberCheckRepeat(Long deptId, String serviceNumber) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            throw new ServiceException("当前公司有误");
        }
        Long accountingParentDeptId = sysDept.getParentId();
        List<Long> childrenDeptIds = remoteDeptService.getAllChildrenIdByTopDeptId(accountingParentDeptId).getDataThrowException();
        if (ObjectUtils.isEmpty(childrenDeptIds)) {
            return null;
        }
        CCustomerService customerService = customerServiceMapper.selectOne(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getServiceNumber, serviceNumber)
                .in(CCustomerService::getAccountingTopDeptId, childrenDeptIds)
                .eq(CCustomerService::getIsDel, Boolean.FALSE)
                .select(CCustomerService::getId, CCustomerService::getCustomerCompanyName, CCustomerService::getServiceStatus)
                .last("limit 1"));
        if (Objects.isNull(customerService)) {
            return null;
        }
        return ServiceNumberRepeatCheckResultDTO.builder()
                .id(customerService.getId())
                .customerName(customerService.getCustomerCompanyName())
                .serviceStatus(ServiceStatus.getServiceStatusByCode(customerService.getServiceStatus()).getDesc())
                .build();
    }

    @Override
    @Transactional
    public void singleTransfer(Long deptId, NewCustomerTransferVO vo) {
        NewCustomerInfo newCustomerInfo = getById(vo.getId());
        if (Objects.isNull(newCustomerInfo) || newCustomerInfo.getIsDel()) {
            throw new ServiceException("新户流转不存在");
        }
        if (!Objects.equals(newCustomerInfo.getStatus(), NewCustomerTransferStatus.UN_TRANSFER.getCode())) {
            throw new ServiceException("新户流转状态有误");
        }
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        updateById(new NewCustomerInfo().setId(vo.getId()).setAccountingTopDeptId(vo.getAccountTopDeptId())
                .setStatus(NewCustomerTransferStatus.TRANSFERRED.getCode()).setTransferTime(LocalDateTime.now())
                .setTransferEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName()));
        customerServiceService.createByNewCustomerTransfer(getNewCustomerTransferAllInfo(vo.getId(), newCustomerInfo), vo.getServiceNumber(), vo.getAccountTopDeptId(), deptId);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("完成新户流转")
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public BatchTransferCheckResultDTO checkFile(MultipartFile file) {
        if (null == file) {
            throw new ServiceException("请上传文件");
        }
        if (file.getSize() > 7 * 1024 * 512) {
            throw new ServiceException("excel文件大小不能超过3.5M");
        }
        ExcelUtil<NewCustomerWaitTransferCheckDataDTO> util = new ExcelUtil<>(NewCustomerWaitTransferCheckDataDTO.class);
        List<NewCustomerWaitTransferCheckDataDTO> data;
        try {
            data = util.importExcel(file.getInputStream());
        } catch (Exception e) {
            throw new ServiceException("文件解析异常,请稍后重试");
        }
        if (ObjectUtils.isEmpty(data)) {
            throw new ServiceException("文件内容为空");
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d -> {
                d.setCheckError("");
                d.setCreditCode(StringUtils.trim(d.getCreditCode()));
                d.setServiceNumber(StringUtils.trim(d.getServiceNumber()));
            });
        }
        Map<String, Long> errorStatistics = new HashMap<>();
        Map<String, Long> creditCodeCountMap = data.stream().filter(d -> !StringUtils.isEmpty(d.getCreditCode())).collect(Collectors.groupingBy(NewCustomerWaitTransferCheckDataDTO::getCreditCode, Collectors.counting()));
        Map<String, Long> serviceNumberCountMap = data.stream().filter(d -> !StringUtils.isEmpty(d.getServiceNumber())).collect(Collectors.groupingBy(NewCustomerWaitTransferCheckDataDTO::getServiceNumber, Collectors.counting()));
        for (NewCustomerWaitTransferCheckDataDTO dto : data) {
            if (StringUtils.isEmpty(dto.getCustomerName()) || StringUtils.isEmpty(dto.getServiceNumber()) || StringUtils.isEmpty(dto.getCreditCode())) {
                dto.setCheckError("数据异常");
                errorStatistics.put("异常-数据异常", errorStatistics.getOrDefault("异常-数据异常", 0L) + 1);
            } else {
                if (creditCodeCountMap.get(dto.getCreditCode()) > 1) {
                    dto.setCheckError("表内信用代码重复");
                    errorStatistics.put("异常-表内信用代码重复", errorStatistics.getOrDefault("异常-表内信用代码重复", 0L) + 1);
                }
                if (serviceNumberCountMap.get(dto.getServiceNumber()) > 1) {
                    dto.setCheckError("表内档案编号重复");
                    errorStatistics.put("异常-表内档案编号重复", errorStatistics.getOrDefault("异常-表内档案编号重复", 0L) + 1);
                }
            }
        }
        List<String> creditCodeList = data.stream().map(NewCustomerWaitTransferCheckDataDTO::getCreditCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, NewCustomerInfo> newCustomerInfoMap = ObjectUtils.isEmpty(creditCodeList) ? Maps.newHashMap() :
                list(new LambdaQueryWrapper<NewCustomerInfo>()
                .in(NewCustomerInfo::getCreditCode, creditCodeList).eq(NewCustomerInfo::getIsDel, false))
                .stream().collect(Collectors.toMap(NewCustomerInfo::getCreditCode, Function.identity(), (k1, k2) -> k1));
        List<String> serviceNumberList = data.stream().map(NewCustomerWaitTransferCheckDataDTO::getServiceNumber).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<CCustomerService> customerServices = ObjectUtils.isEmpty(serviceNumberList) ? Lists.newArrayList() : customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .in(CCustomerService::getServiceNumber, serviceNumberList).eq(CCustomerService::getIsDel, false)
                .select(CCustomerService::getCustomerCompanyName, CCustomerService::getServiceStatus, CCustomerService::getBusinessDeptId, CCustomerService::getServiceNumber));
        Integer serviceNumberExistCount = 0;
        Map<String, List<CCustomerService>> customerServiceMap = customerServices.stream().collect(Collectors.groupingBy(CCustomerService::getServiceNumber));
        List<SysDept> businessDeptList = remoteDeptService.getByDeptIds(customerServices.stream().map(CCustomerService::getBusinessDeptId).collect(Collectors.toList())).getDataThrowException(false);
        Map<Long, String> businessDeptMap = businessDeptList.stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        for (NewCustomerWaitTransferCheckDataDTO d : data) {
            if (!StringUtils.isEmpty(d.getCreditCode())) {
                if (!newCustomerInfoMap.containsKey(d.getCreditCode())) {
                    d.setCheckError("新户流转记录不存在");
                    errorStatistics.put("异常-新户流转记录不存在", errorStatistics.getOrDefault("异常-新户流转记录不存在", 0L) + 1);
                } else {
                    NewCustomerInfo newCustomerInfo = newCustomerInfoMap.get(d.getCreditCode());
                    d.setNewCustomerTransferId(newCustomerInfo.getId());
                    if (!Objects.equals(newCustomerInfo.getStatus(), NewCustomerTransferStatus.UN_TRANSFER.getCode())) {
                        d.setCheckError("新户流转状态不符");
                        errorStatistics.put("异常-新户流转状态不符", errorStatistics.getOrDefault("异常-新户流转状态不符", 0L) + 1);
                    }
                }
            }
            if (!StringUtils.isEmpty(d.getServiceNumber())) {
                if (customerServiceMap.containsKey(d.getServiceNumber())) {
                    d.setIsExistServiceNumber(true);
                    d.setExistCustomerServiceList(customerServiceMap.get(d.getServiceNumber()).stream().map(c -> NewCustomerWaitTransferCheckExistServiceNumberDataDTO
                            .builder().customerName(d.getCustomerName())
                            .creditCode(d.getCreditCode()).serviceNumber(d.getServiceNumber())
                            .existCustomerName(c.getCustomerCompanyName()).existCreditCode(c.getCreditCode()).existServiceNumber(c.getServiceNumber())
                            .existServiceStatus(ServiceStatus.getServiceStatusByCode(c.getServiceStatus()).getDesc())
                            .existBusinessDeptId(businessDeptMap.getOrDefault(c.getBusinessDeptId(), "")).build()).collect(Collectors.toList()));
                    serviceNumberExistCount++;
                }
            }
        }
        List<String> errorStatisticsList = errorStatistics.entrySet().stream()
                .map(entry -> entry.getKey() + "：" + entry.getValue())
                .collect(Collectors.toList());
        BatchTransferCheckResultDTO checkResult = BatchTransferCheckResultDTO.builder()
                .batchNo(uuid)
                .errorStatistics(errorStatisticsList)
                .data(data)
                .hasException(!errorStatistics.isEmpty())
                .serviceNumberExistCount(serviceNumberExistCount)
                .build();
        // 将校验结果存储到Redis
        redisService.setCacheObject(CacheConstants.BATCH_TRANSFER_CHECK_RESULT + uuid, checkResult, 60 * 60L, TimeUnit.SECONDS);
        return checkResult;
    }

    @Override
    public void downloadErrorData(String batchNo, HttpServletResponse response) {
        BatchTransferCheckResultDTO checkResult = getBatchTransferCheckResult(batchNo);
        if (Objects.isNull(checkResult)) {
            throw new ServiceException("解析未完成");
        }
        List<NewCustomerWaitTransferCheckDataDTO> errorData = checkResult.getData().stream().filter(d -> !StringUtils.isEmpty(d.getCheckError())).collect(Collectors.toList());
        ExcelUtil<NewCustomerWaitTransferCheckDataDTO> util = new ExcelUtil<>(NewCustomerWaitTransferCheckDataDTO.class);
        util.exportExcel(response, errorData, "新户流转-校验异常数据");
    }

    @Override
    public void downloadExistServiceNumberData(String batchNo, HttpServletResponse response) {
        BatchTransferCheckResultDTO checkResult = getBatchTransferCheckResult(batchNo);
        if (Objects.isNull(checkResult)) {
            throw new ServiceException("解析未完成");
        }
        List<NewCustomerWaitTransferCheckDataDTO> existData = checkResult.getData().stream().filter(d -> !Objects.isNull(d.getIsExistServiceNumber()) && d.getIsExistServiceNumber()).collect(Collectors.toList());
        List<NewCustomerWaitTransferCheckExistServiceNumberDataDTO> dataList = Lists.newArrayList();
        existData.forEach(d -> dataList.addAll(d.getExistCustomerServiceList()));
        ExcelUtil<NewCustomerWaitTransferCheckExistServiceNumberDataDTO> util = new ExcelUtil<>(NewCustomerWaitTransferCheckExistServiceNumberDataDTO.class);
        util.exportExcel(response, dataList, "新户流转-档案编号重复数据");
    }

    @Override
    @Transactional
    public void confirmBatchTransfer(String batchNo, Long accountingTopDeptId, Long deptId) {
        BatchTransferCheckResultDTO checkResult = getBatchTransferCheckResult(batchNo);
        if (Objects.isNull(checkResult)) {
            throw new ServiceException("解析未完成");
        }
        List<NewCustomerWaitTransferCheckDataDTO> noErrorData = checkResult.getData().stream().filter(d -> StringUtils.isEmpty(d.getCheckError()) && !Objects.isNull(d.getNewCustomerTransferId())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(noErrorData)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        List<Long> newCustomerIds = noErrorData.stream().map(NewCustomerWaitTransferCheckDataDTO::getNewCustomerTransferId).collect(Collectors.toList());
        List<NewCustomerInfo> list = list(new LambdaQueryWrapper<NewCustomerInfo>().eq(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode())
                .in(NewCustomerInfo::getId, newCustomerIds));
        if (ObjectUtils.isEmpty(list)) {
            return;
        }
        newCustomerIds = list.stream().map(NewCustomerInfo::getId).collect(Collectors.toList());
        Map<Long, String> idServiceNumberMap = noErrorData.stream().collect(Collectors.toMap(NewCustomerWaitTransferCheckDataDTO::getNewCustomerTransferId, NewCustomerWaitTransferCheckDataDTO::getServiceNumber));
        Map<Long, NewCustomerTransferAllInfoDTO> newCustomerTransferAllInfoMap = getNewCustomerTransferAllInfo(newCustomerIds, idServiceNumberMap);
        updateBatchById(newCustomerIds.stream().map(customerId -> new NewCustomerInfo().setId(customerId).setAccountingTopDeptId(accountingTopDeptId)
                .setStatus(NewCustomerTransferStatus.TRANSFERRED.getCode())
                .setTransferTime(LocalDateTime.now())
                .setTransferEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())).collect(Collectors.toList()));
        customerServiceService.createByNewCustomerTransferMap(newCustomerTransferAllInfoMap, accountingTopDeptId, deptId);
        newCustomerIds.forEach(customerId -> {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerId)
                        .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                        .setDeptId(deptId)
                        .setOperType("完成新户流转")
                        .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    public NextCheckDTO nextCheck(Long deptId, NewCustomerCreateVO vo) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            throw new ServiceException("业务公司不存在");
        }
        NewCustomerCreateBaseInfoVO baseInfo = vo.getBaseInfo();
        Long businessTopDeptId = sysDept.getParentId();
        if (checkNewCustomerRepeatByNewCreditCode(baseInfo.getCreditCode(), businessTopDeptId, vo.getId()) > 0) {
            return NextCheckDTO.builder().checkResult(1).checkErrorMsg("该企业已被他人提交，请勿重复提交").build();
        }
//        if (checkNewCustomerRepeatByNewCustomerName(baseInfo.getCustomerName(), businessTopDeptId, vo.getId()) > 0) {
//            return NextCheckDTO.builder().checkResult(1).checkErrorMsg("该企业已被他人提交，请勿重复提交").build();
//        }
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getBusinessTopDeptId, businessTopDeptId)
                .eq(CCustomerService::getIsDel, false)
                .eq(CCustomerService::getCreditCode, baseInfo.getCreditCode())
                .orderByAsc(CCustomerService::getServiceStatus)
                .orderByDesc(CCustomerService::getEndAccountPeriod));
        if (!ObjectUtils.isEmpty(customerServices)) {
            CCustomerService newestCustomerService = customerServices.get(0);
            if (Objects.equals(newestCustomerService.getServiceStatus(), ServiceStatus.SERVICE.getCode()) || Objects.isNull(newestCustomerService.getEndAccountPeriod())) {
                return NextCheckDTO.builder().checkResult(2).checkErrorMsg("该企业正在服务中，请勿重复提交").build();
            } else {
                if (DateUtils.yearMonthToPeriod(vo.getBaseInfo().getFirstAccountingPeriod()) <= newestCustomerService.getEndAccountPeriod()) {
                    return NextCheckDTO.builder().checkResult(2).checkErrorMsg(String.format("首个记账账期只能从%s开始", DateUtils.periodToYeaMonth(DateUtils.getNextPeriod(newestCustomerService.getEndAccountPeriod())))).build();
                }
                if (Objects.equals(newestCustomerService.getEndAccountPeriod(), DateUtils.getPrePeriod())) {
//                    return NextCheckDTO.builder().checkResult(2).checkErrorMsg("该企业上个月才结束服务，请重启服务").build();
                    return NextCheckDTO.builder().checkResult(0).build();
                } else {
                    return NextCheckDTO.builder().checkResult(3).checkErrorMsg(String.format("该企业曾于%s移出，确定要重新流转吗？", DateUtils.periodToYeaMonth(newestCustomerService.getEndAccountPeriod()))).build();
                }
            }
        } else {
            return NextCheckDTO.builder().checkResult(0).build();
        }
    }

    @Override
    public NewCustomerOtherInfoDTO getEndCustomerServiceInfo(Long deptId, NewCustomerCreateVO vo) {
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        if (Objects.isNull(sysDept)) {
            throw new ServiceException("业务公司不存在");
        }
        NewCustomerCreateBaseInfoVO baseInfo = vo.getBaseInfo();
        Long businessTopDeptId = sysDept.getParentId();
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getBusinessTopDeptId, businessTopDeptId)
                .eq(CCustomerService::getIsDel, false)
                .eq(CCustomerService::getCreditCode, baseInfo.getCreditCode())
                .orderByAsc(CCustomerService::getServiceStatus)
                .orderByDesc(CCustomerService::getEndAccountPeriod));
        if (ObjectUtils.isEmpty(customerServices)) {
            return new NewCustomerOtherInfoDTO();
        }
        CCustomerService newestCustomerService = customerServices.get(0);
        if (Objects.equals(newestCustomerService.getServiceStatus(), ServiceStatus.SERVICE.getCode()) || Objects.isNull(newestCustomerService.getEndAccountPeriod())) {
            return new NewCustomerOtherInfoDTO();
        }
        List<CustomerServiceBankAccount> bankAccountList = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                .eq(CustomerServiceBankAccount::getCustomerServiceId, newestCustomerService.getId()));
        List<CustomerSysAccount> customerSysAccounts = customerSysAccountMapper.selectList(new LambdaQueryWrapper<CustomerSysAccount>()
                .eq(CustomerSysAccount::getCustomerServiceId, newestCustomerService.getId())
                .eq(CustomerSysAccount::getIsDel, false));
        List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList = customerTaxTypeCheckService.selectCustomerTaxTypeCheckByCustomerServiceId(newestCustomerService.getId(), newestCustomerService.getTaxType());
        return NewCustomerOtherInfoDTO.builder()
                .bankList(bankAccountList.stream().map(row -> {
                    NewCustomerTransferBankAccountDTO dto = new NewCustomerTransferBankAccountDTO();
                    BeanUtils.copyProperties(row, dto);
                    dto.setId(null);
                    return dto;
                }).collect(Collectors.toList()))
                .sysAccountList(customerSysAccounts.stream().map(row -> {
                    NewCustomerTransferSysAccountDTO dto = new NewCustomerTransferSysAccountDTO();
                    BeanUtils.copyProperties(row, dto);
                    dto.setId(null);
                    dto.setSysAccountType(row.getSysType());
                    dto.setSysAccountTypeName(row.getSysTypeName());
                    return dto;
                }).collect(Collectors.toList()))
                .taxTypeCheckList(taxTypeCheckList.stream().map(row -> {
                    NewCustomerServiceTaxTypeCheckVO dto = new NewCustomerServiceTaxTypeCheckVO();
                    BeanUtils.copyProperties(row, dto);
                    dto.setId(null);
                    return dto;
                }).collect(Collectors.toList()))
                .build();
    }

    @Override
    @Transactional
    public void newCustomerAutoTransferTask() {
        List<NewCustomerInfo> data = list(new LambdaQueryWrapper<NewCustomerInfo>()
                .eq(NewCustomerInfo::getStatus, NewCustomerTransferStatus.UN_TRANSFER.getCode())
                .eq(NewCustomerInfo::getIsDel, false));
        if (ObjectUtils.isEmpty(data)) {
            log.info("=============待流转客户为空，结束任务");
            return;
        }
        List<NewCustomerBusinessTopDeptCreditCodeVO> voList = data.stream().filter(d -> !Objects.isNull(d.getBusinessTopDeptId()) && !StringUtils.isEmpty(d.getCreditCode()))
                .map(d -> NewCustomerBusinessTopDeptCreditCodeVO.builder()
                        .businessTopDeptId(d.getBusinessTopDeptId()).creditCode(d.getCreditCode()).build()).collect(Collectors.toList());
        Map<String, List<CCustomerService>> customerServiceMap = ObjectUtils.isEmpty(voList) ? Maps.newHashMap() :
                customerServiceMapper.selectEndCustomerServiceBatchByBusinessTopDeptIdAndCreditCode(voList).stream().collect(Collectors.groupingBy(c -> c.getBusinessTopDeptId() + "_" + c.getCreditCode()));
        Map<Long, List<NewCustomerTransferFile>> fileMap = newCustomerTransferFileService.list(new LambdaQueryWrapper<NewCustomerTransferFile>()
                        .eq(NewCustomerTransferFile::getIsDel, false)
                        .in(NewCustomerTransferFile::getCustomerId, data.stream().map(NewCustomerInfo::getId).collect(Collectors.toList()))
                        .eq(NewCustomerTransferFile::getFileType, NewCustomerTransferFileType.CREATE_FILE.getCode()))
                .stream().collect(Collectors.groupingBy(NewCustomerTransferFile::getCustomerId));
        ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE); // 创建一个线程池
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        data.forEach(d -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Long targetAccountingTopDeptId = getTransferTargetAccountingTopDeptId(d, fileMap);
                    if (!Objects.isNull(targetAccountingTopDeptId)) {
                        List<CCustomerService> endCustomerList = customerServiceMap.get(d.getBusinessTopDeptId() + "_" + d.getCreditCode());
                        if (!ObjectUtils.isEmpty(endCustomerList)) {
                            endCustomerList.sort(Comparator.comparing(CCustomerService::getEndAccountPeriod).reversed());
                        }
                        NewCustomerTransferVO vo = new NewCustomerTransferVO();
                        vo.setServiceNumber(ObjectUtils.isEmpty(endCustomerList) ? d.getId().toString() : endCustomerList.get(0).getServiceNumber());
                        vo.setAccountTopDeptId(targetAccountingTopDeptId);
                        vo.setId(d.getId());
                        autoTransfer(vo, d, 1L, null, "系统");
                    } else {
                        log.info("新户id:{}，不是特殊组但存在备注或附件，不流转", d.getId());
                    }
                } catch (Exception e) {
                    log.info("自动流转失败:{}，新户id:{}", e.getMessage(), d.getId());
                }
            }, executorService);

            futures.add(future);
        });

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();
    }

    private BatchTransferCheckResultDTO getBatchTransferCheckResult(String batchNo) {
        return redisService.getCacheObject(CacheConstants.BATCH_TRANSFER_CHECK_RESULT + batchNo);
    }

    private NewCustomerTransferAllInfoDTO getNewCustomerTransferAllInfo(Long newCustomerTransferId, NewCustomerInfo newCustomerInfo) {
        return NewCustomerTransferAllInfoDTO.builder()
                .newCustomerInfo(newCustomerInfo)
                .newCustomerAnnualReportInfo(newCustomerAnnualReportInfoService.selectByCustomerId(newCustomerTransferId))
                .newCustomerBankAccountList(newCustomerBankAccountService.selectByNewCustomerId(newCustomerTransferId))
                .newCustomerFinanceTaxInfo(newCustomerFinanceTaxInfoService.selectByNewCustomerTransferId(newCustomerTransferId))
                .newCustomerFixedAssetsInfoList(newCustomerFixedAssetsInfoService.selectByCustomerId(newCustomerTransferId))
                .newCustomerIncomeInfoList(newCustomerIncomeInfoService.selectByCustomerId(newCustomerTransferId))
                .newCustomerInsuranceFundInfo(newCustomerInsuranceFundInfoService.selectByCustomerId(newCustomerTransferId))
                .newCustomerInsuranceFundStatusList(newCustomerInsuranceFundStatusService.selectByCustomerId(newCustomerTransferId))
                .newCustomerOtherInfo(newCustomerOtherInfoService.selectByCustomerId(newCustomerTransferId))
                .newCustomerSysAccountList(newCustomerSysAccountService.selectByCustomerServiceId(newCustomerTransferId))
                .newCustomerTaxTypeCheckList(newCustomerTaxTypeCheckService.list(new LambdaQueryWrapper<NewCustomerTaxTypeCheck>()
                        .eq(NewCustomerTaxTypeCheck::getCustomerId, newCustomerTransferId)))
                .tagRelations(businessTagRelationService.selectByBusinessIdAndBusinessType(newCustomerTransferId, TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode()))
                .build();
    }

    private Map<Long, NewCustomerTransferAllInfoDTO> getNewCustomerTransferAllInfo(List<Long> newCustomerTransferIds, Map<Long, String> idServiceNumberMap) {
        List<NewCustomerInfo> newCustomerInfoList = selectByIds(newCustomerTransferIds);
        Map<Long, NewCustomerAnnualReportInfo> annualReportInfoMap = newCustomerAnnualReportInfoService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerBankAccount>> bankAccountMap = newCustomerBankAccountService.selectMapByNewCustomerIds(newCustomerTransferIds);
        Map<Long, NewCustomerFinanceTaxInfo> financeTaxInfoMap = newCustomerFinanceTaxInfoService.selectMapByNewCustomerTransferIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerFixedAssetsInfo>> assetInfoMap = newCustomerFixedAssetsInfoService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerIncomeInfo>> incomeInfoMap = newCustomerIncomeInfoService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, NewCustomerInsuranceFundInfo> insuranceFundInfoMap = newCustomerInsuranceFundInfoService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerInsuranceFundStatus>> insuranceStatusMap = newCustomerInsuranceFundStatusService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, NewCustomerOtherInfo> otherInfoMap = newCustomerOtherInfoService.selectMapByCustomerIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerSysAccount>> sysAccountMap = newCustomerSysAccountService.selectMapByCustomerServiceIds(newCustomerTransferIds);
        Map<Long, List<NewCustomerTaxTypeCheck>> taxCheckMap = newCustomerTaxTypeCheckService.list(new LambdaQueryWrapper<NewCustomerTaxTypeCheck>()
                .in(NewCustomerTaxTypeCheck::getCustomerId, newCustomerTransferIds)).stream()
                .collect(Collectors.groupingBy(NewCustomerTaxTypeCheck::getCustomerId));
        Map<Long, List<CBusinessTagRelation>> tagRelationMap = businessTagRelationService.selectByBusinessIdsAndBusinessType(newCustomerTransferIds, TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                .stream().collect(Collectors.groupingBy(CBusinessTagRelation::getBusinessId));
        return newCustomerInfoList.stream().collect(Collectors.toMap(NewCustomerInfo::getId, newCustomerInfo ->
                NewCustomerTransferAllInfoDTO.builder()
                        .newCustomerInfo(newCustomerInfo)
                        .newCustomerAnnualReportInfo(annualReportInfoMap.getOrDefault(newCustomerInfo.getId(), null))
                        .newCustomerBankAccountList(bankAccountMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .newCustomerFinanceTaxInfo(financeTaxInfoMap.getOrDefault(newCustomerInfo.getId(), null))
                        .newCustomerFixedAssetsInfoList(assetInfoMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .newCustomerIncomeInfoList(incomeInfoMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .newCustomerInsuranceFundInfo(insuranceFundInfoMap.getOrDefault(newCustomerInfo.getId(), null))
                        .newCustomerInsuranceFundStatusList(insuranceStatusMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .newCustomerOtherInfo(otherInfoMap.getOrDefault(newCustomerInfo.getId(), null))
                        .newCustomerSysAccountList(sysAccountMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .newCustomerTaxTypeCheckList(taxCheckMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .tagRelations(tagRelationMap.getOrDefault(newCustomerInfo.getId(), Lists.newArrayList()))
                        .serviceNumber(idServiceNumberMap.get(newCustomerInfo.getId()))
                        .build()));
    }

    private void removeDontNeedDatas(Long newCustomerTransferId, NewCustomerInfo newCustomerInfo, List<Long> tagIds) {
        if (newCustomerInfo.getIsNewCustomer()) {
            // 新户
            // 财务信息-做账系统，财务信息-最后入账账期，财务信息-未入账原因，财务信息-其他备注，财务信息-收入和其他信息收集 设置为空
            // 税务信息-个税申报方式，税务信息-易捷账个税账号 设置为空
            newCustomerFinanceTaxInfoService.update(new LambdaUpdateWrapper<NewCustomerFinanceTaxInfo>()
                    .eq(NewCustomerFinanceTaxInfo::getCustomerId, newCustomerTransferId)
                    .set(NewCustomerFinanceTaxInfo::getAccountingSystem, null)
                    .set(NewCustomerFinanceTaxInfo::getLastAccountMonth, null)
                    .set(NewCustomerFinanceTaxInfo::getNotAccountReason, null)
                    .set(NewCustomerFinanceTaxInfo::getOtherRemarks, null)
                    .set(NewCustomerFinanceTaxInfo::getIncomeMain, null)
                    .set(NewCustomerFinanceTaxInfo::getIncomeOther, null)
                    .set(NewCustomerFinanceTaxInfo::getCost, null)
                    .set(NewCustomerFinanceTaxInfo::getExpense, null)
                    .set(NewCustomerFinanceTaxInfo::getProfit, null)
                    .set(NewCustomerFinanceTaxInfo::getOffsetLoss, null)
                    .set(NewCustomerFinanceTaxInfo::getTotalSalary, null)
                    .set(NewCustomerFinanceTaxInfo::getWelfareFee, null)
                    .set(NewCustomerFinanceTaxInfo::getEntertainmentFee, null)
                    .set(NewCustomerFinanceTaxInfo::getOtherAdjustment, null)
                    .set(NewCustomerFinanceTaxInfo::getTaxMethod, null)
                    .set(NewCustomerFinanceTaxInfo::getEzTaxAccount, null));
        }
//        if (!tagIds.contains(specialTagProperties.getYibao())) {
//            // 医保相关信息设置为空
//            newCustomerInsuranceFundInfoService.update(new LambdaUpdateWrapper<NewCustomerInsuranceFundInfo>()
//                    .eq(NewCustomerInsuranceFundInfo::getCustomerId, newCustomerTransferId)
//                    .set(NewCustomerInsuranceFundInfo::getMedicalBase, null)
//                    .set(NewCustomerInsuranceFundInfo::getMedicalFee, null)
//                    .set(NewCustomerInsuranceFundInfo::getMedicalContact, null)
//                    .set(NewCustomerInsuranceFundInfo::getMedicalRate, null)
//                    .set(NewCustomerInsuranceFundInfo::getMedicalPeople, null));
//            newCustomerInsuranceFundStatusService.deleteByCustomerIdAndType(newCustomerTransferId, 1);
//        }
//        if (!tagIds.contains(specialTagProperties.getShebao())) {
//            // 社保相关信息设置为空
//            newCustomerInsuranceFundInfoService.update(new LambdaUpdateWrapper<NewCustomerInsuranceFundInfo>()
//                    .eq(NewCustomerInsuranceFundInfo::getCustomerId, newCustomerTransferId)
//                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityBase, null)
//                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityPeople, null)
//                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityContact, null)
//                    .set(NewCustomerInsuranceFundInfo::getInjuryFee, null)
//                    .set(NewCustomerInsuranceFundInfo::getInjuryRate, null)
//                    .set(NewCustomerInsuranceFundInfo::getTotalContribution, null));
//            newCustomerInsuranceFundStatusService.deleteByCustomerIdAndType(newCustomerTransferId, 2);
//        }
//        if (!tagIds.contains(specialTagProperties.getGongjijin())) {
//            // 公积金相关信息设置为空
//            newCustomerInsuranceFundInfoService.update(new LambdaUpdateWrapper<NewCustomerInsuranceFundInfo>()
//                    .eq(NewCustomerInsuranceFundInfo::getCustomerId, newCustomerTransferId)
//                    .set(NewCustomerInsuranceFundInfo::getFundBase, null)
//                    .set(NewCustomerInsuranceFundInfo::getFundFee, null)
//                    .set(NewCustomerInsuranceFundInfo::getFundContact, null)
//                    .set(NewCustomerInsuranceFundInfo::getFundRate, null)
//                    .set(NewCustomerInsuranceFundInfo::getFundPeople, null));
//            newCustomerInsuranceFundStatusService.deleteByCustomerIdAndType(newCustomerTransferId, 3);
//        }
//        if (DateUtils.strToLocalDate(newCustomerInfo.getRegistrationDate(), DateUtils.YYYY_MM_DD).getYear() != LocalDate.now().getYear()) {
//            // 汇算清缴相关信息设置为空
//            newCustomerOtherInfoService.update(new LambdaUpdateWrapper<NewCustomerOtherInfo>()
//                    .eq(NewCustomerOtherInfo::getCustomerId, newCustomerTransferId)
//                    .set(NewCustomerOtherInfo::getTaxSubmissionStatus, null)
//                    .set(NewCustomerOtherInfo::getPreTaxProfit, null)
//                    .set(NewCustomerOtherInfo::getNextYearSupplement, null)
//                    .set(NewCustomerOtherInfo::getNotes, null));
//            newCustomerFixedAssetsInfoService.deleteByCustomerId(newCustomerTransferId);
//            // 工商年报状态设置为空
//            newCustomerAnnualReportInfoService.update(new LambdaUpdateWrapper<NewCustomerAnnualReportInfo>()
//                    .eq(NewCustomerAnnualReportInfo::getCustomerId, newCustomerTransferId)
//                    .set(NewCustomerAnnualReportInfo::getStatus, null));
//        }
        if (!newCustomerInfo.getIsOpenBankAccount()) {
            // 银行账号删除
            newCustomerBankAccountService.deleteByNewCustomerId(newCustomerTransferId);
        }
    }

    private void updateNewCustomerTransferOther(NewCustomerTransferDetailInfoDTO vo) {
        newCustomerFinanceTaxInfoService.updateByTaxInfo(vo.getTaxInfo());
//        newCustomerInsuranceFundInfoService.updateByInsuranceInfo(vo.getInsuranceInfo());
//        newCustomerAnnualReportInfoService.updateByOtherInfo(vo.getOtherInfo());
    }

    private NewCustomerCreateBaseInfoDTO buildBaseInfo(NewCustomerInfo newCustomerInfo, List<TagDTO> tagList) {
        String businessDeptName = "";
        if (!Objects.isNull(newCustomerInfo.getBusinessDeptId())) {
            SysDept sysDept = remoteDeptService.getDeptInfo(newCustomerInfo.getBusinessDeptId()).getDataThrowException(false);
            businessDeptName = Objects.isNull(sysDept) ? "" : sysDept.getDeptName();
        }
        List<Long> advisorDeptIdPath = Lists.newArrayList();
        String advisorDeptName = "";
        String advisorDeptInfo = "";
        if (!Objects.isNull(newCustomerInfo.getAdvisorDeptId())) {
            SysDept advisorDept = remoteDeptService.getDeptInfo(newCustomerInfo.getAdvisorDeptId()).getDataThrowException(false);
            List<SysEmployee> advisorEmployees = remoteEmployeeService.getEmployeeListByDeptId(newCustomerInfo.getAdvisorDeptId()).getDataThrowException(false);
            if (!Objects.isNull(advisorDept)) {
                advisorDeptIdPath = Arrays.stream(advisorDept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList());
                advisorDeptIdPath.remove(0L);
                advisorDeptIdPath.add(newCustomerInfo.getAdvisorDeptId());
                advisorDeptName = advisorDept.getDeptName();
                advisorDeptInfo = advisorDept.getDeptName() + "(" + advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + ")";
            }
        }
        return NewCustomerCreateBaseInfoDTO.builder()
                .newCustomerTransferId(newCustomerInfo.getId())
                .customerName(newCustomerInfo.getCustomerName())
                .creditCode(newCustomerInfo.getCreditCode())
                .taxNumber(newCustomerInfo.getTaxNumber())
                .registrationDate(newCustomerInfo.getRegistrationDate())
                .provinceCode(newCustomerInfo.getProvinceCode())
                .provinceName(newCustomerInfo.getProvinceName())
                .cityCode(newCustomerInfo.getCityCode())
                .cityName(newCustomerInfo.getCityName())
                .areaCode(newCustomerInfo.getAreaCode())
                .areaName(newCustomerInfo.getAreaName())
                .industry(newCustomerInfo.getIndustry())
                .taxType(newCustomerInfo.getTaxType())
                .taxTypeStr(TaxType.getByCode(newCustomerInfo.getTaxType()).getDesc())
                .tags(tagList)
                .businessDeptId(newCustomerInfo.getBusinessDeptId())
                .businessDeptName(businessDeptName)
                .advisorDeptId(newCustomerInfo.getAdvisorDeptId())
                .advisorDeptIdPath(advisorDeptIdPath)
                .advisorDeptName(advisorDeptName)
                .advisorDeptInfo(advisorDeptInfo)
                .firstAccountPeriod(Objects.isNull(newCustomerInfo.getFirstAccountPeriod()) ? "" : DateUtils.periodToYeaMonth(newCustomerInfo.getFirstAccountPeriod()))
                .firstAccountingPeriod(Objects.isNull(newCustomerInfo.getFirstAccountingPeriod()) ? "" : DateUtils.periodToYeaMonth(newCustomerInfo.getFirstAccountingPeriod()))
                .isNewCustomer(newCustomerInfo.getIsNewCustomer() ? 1 : 0)
                .remark(newCustomerInfo.getCreateRemark())
                .files(newCustomerTransferFileService.getFilesByCustomerIdAndFileType(newCustomerInfo.getId(), NewCustomerTransferFileType.CREATE_FILE.getCode()))
                .build();
    }

    private String checkRepeatByNewCustomerName(String newCustomerName, Long businessTopDeptId) {
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, Boolean.FALSE)
                .eq(CCustomerService::getBusinessTopDeptId, businessTopDeptId)
                .and(queryWrapper -> queryWrapper.eq(CCustomerService::getCustomerName, newCustomerName).or().eq(CCustomerService::getCustomerCompanyName, newCustomerName)));
        return checkRepeatByCustomerServices(customerServices);
    }

    private String checkRepeatByNewCreditCode(String newCreditCode, Long businessTopDeptId) {
        List<CCustomerService> customerServices = customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                .eq(CCustomerService::getIsDel, Boolean.FALSE)
                .eq(CCustomerService::getBusinessTopDeptId, businessTopDeptId)
                .eq(CCustomerService::getCreditCode, newCreditCode));
        return checkRepeatByCustomerServices(customerServices);
    }

    private String checkRepeatByCustomerServices(List<CCustomerService> customerServices) {
        if (!ObjectUtils.isEmpty(customerServices)) {
            if (customerServices.stream().anyMatch(c -> c.getServiceStatus() != 2)) {
                return "该客户已存在，请不要重复提交";
            }
            customerServices.sort(Comparator.comparing(CCustomerService::getEndAccountPeriod).reversed());
            return String.format("该客户已经存在，目前是服务已结束，结束账期%s，如需重启请联系系统运维管理员", DateUtils.periodToYeaMonth(customerServices.get(0).getEndAccountPeriod()));
        }
        return null;
    }

    private String getSysAccountOperContent(NewCustomerSysAccount sysAccount, NewCustomerTransferSysAccountDTO dto) {
        StringBuilder operContent = new StringBuilder();
        String newSysAccountType = StringUtils.isEmpty(dto.getSysAccountTypeName()) ? SysAccountType.getByCode(dto.getSysAccountType()).getName() : dto.getSysAccountTypeName();
        if (!Objects.equals(sysAccount.getSysTypeName(), newSysAccountType)) {
            operContent.append(String.format("%s为%s，", "账号类型", newSysAccountType));
        }
        if (!Objects.equals(sysAccount.getAccount(), dto.getAccount())) {
            operContent.append(String.format("%s为%s，", "账号", dto.getAccount()));
        }
        if (!Objects.equals(sysAccount.getPassword(), dto.getPassword())) {
            operContent.append(String.format("%s为%s，", "密码", dto.getPassword()));
        }
        if (!Objects.equals(sysAccount.getContact(), dto.getContact())) {
            operContent.append(String.format("%s为%s，", "实名人员", dto.getContact()));
        }
        if (!Objects.equals(sysAccount.getContactMobile(), dto.getContactMobile())) {
            operContent.append(String.format("%s为%s，", "实名手机号", dto.getContactMobile()));
        }
        if (!Objects.equals(sysAccount.getIdNumber(), dto.getIdNumber())) {
            operContent.append(String.format("%s为%s，", "身份证号", dto.getIdNumber()));
        }
        if (!Objects.equals(sysAccount.getRemark(), dto.getRemark())) {
            operContent.append(String.format("%s为%s，", "备注", dto.getRemark()));
        }
        if (!StringUtils.isEmpty(operContent.toString())) {
            return "修改" + SysAccountType.getByCode(sysAccount.getSysType()) + "账号:" + operContent.substring(0, operContent.length() - 1);
        }
        return operContent.toString();
    }

    private String getModifyBaseInfoOperContent(NewCustomerInfo old, NewCustomerCreateBaseInfoVO vo, String oldAdvisorInfo, String newAdvisorInfo) {
        StringBuilder operContent = new StringBuilder();
        if (!Objects.equals(old.getCustomerName(), vo.getCustomerName())) {
            operContent.append(String.format("%s修改为：%s；", "客户企业名", vo.getCustomerName()));
        }
        if (!Objects.equals(old.getCreditCode(), vo.getCreditCode())) {
            operContent.append(String.format("%s修改为：%s；", "统一社会信用代码", vo.getCreditCode()));
        }
        if (!Objects.equals(old.getTaxNumber(), vo.getTaxNumber())) {
            operContent.append(String.format("%s修改为：%s；", "纳税人识别号", vo.getTaxNumber()));
        }
        if (!Objects.equals(old.getRegistrationDate(), vo.getRegistrationDate())) {
            operContent.append(String.format("%s修改为：%s；", "注册时间", vo.getRegistrationDate()));
        }
        if (!Objects.equals(old.getRegistrationRegion(), vo.getRegisterLocation())) {
            operContent.append(String.format("%s修改为：%s；", "注册区域", vo.getRegisterLocation()));
        }
        if (!Objects.equals(old.getIndustry(), vo.getIndustry())) {
            operContent.append(String.format("%s修改为：%s；", "所属行业", vo.getIndustry()));
        }
        if (!Objects.equals(old.getTaxType(), vo.getTaxType())) {
            operContent.append(String.format("%s修改为：%s；", "纳税人性质", TaxType.getByCode(vo.getTaxType()).getDesc()));
        }
        if (!Objects.equals(oldAdvisorInfo, newAdvisorInfo)) {
            operContent.append(String.format("%s修改为：%s；", "顾问组别", newAdvisorInfo));
        }
        if (!Objects.equals(old.getIsNewCustomer(), vo.getIsNewCustomer())) {
            operContent.append(String.format("%s修改为：%s；", "是否新户", vo.getIsNewCustomer() ? "是" : "否"));
        }
        if (!Objects.equals(old.getFirstAccountPeriod(), DateUtils.yearMonthToPeriod(vo.getFirstAccountPeriod()))) {
            operContent.append(String.format("%s修改为：%s；", "首个账期", vo.getFirstAccountPeriod()));
        }
        if (!Objects.equals(old.getFirstAccountingPeriod(), DateUtils.yearMonthToPeriod(vo.getFirstAccountingPeriod()))) {
            operContent.append(String.format("%s修改为：%s；", "首个记账账期", vo.getFirstAccountingPeriod()));
        }
        if (!Objects.equals(old.getCreateRemark(), vo.getRemark())) {
            operContent.append(String.format("%s修改为：%s；", "备注", vo.getRemark()));
        }
        operContent.append(getTagOperContent(old, vo.getTags()));
        if (!StringUtils.isEmpty(operContent)) {
            return "修改客户基本信息:" + operContent.substring(0, operContent.length() - 1);
        }
        return operContent.toString();
    }

    private String getTagOperContent(NewCustomerInfo old, List<TagDTO> tagsDTOS) {
        StringBuilder operContent = new StringBuilder();
        List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdAndBusinessType(old.getId(), TagBusinessType.NEW_CUSTOMER_TRANSFER.getCode());
        List<Long> oldTagIds = ObjectUtils.isEmpty(relations) ? Collections.emptyList() : relations.stream().map(CBusinessTagRelation::getTagId).collect(Collectors.toList());
        List<Long> newTagIds = ObjectUtils.isEmpty(tagsDTOS) ? Collections.emptyList() : tagsDTOS.stream().map(TagDTO::getId).collect(Collectors.toList());
        if (!oldTagIds.equals(newTagIds)) {
            List<CTag> tags = tagService.list(new LambdaQueryWrapper<CTag>().eq(CTag::getIsDel, Boolean.FALSE));
            Map<Long, CTag> tagMap = ObjectUtils.isEmpty(tags) ? Collections.emptyMap() : tags.stream().collect(Collectors.toMap(CTag::getId, Function.identity()));
            if (ObjectUtils.isEmpty(newTagIds)) {
                operContent.append(String.format("原标签（%s），清除标签；", ObjectUtils.isEmpty(relations) ? "无" : relations.stream().map(tag -> {
                    CTag ctag = tagMap.get(tag.getTagId());
                    if (Objects.isNull(ctag)) {
                        return "";
                    }
                    if (ctag.getHasParam()) {
                        return String.format(ctag.getTagName(), tag.getTagParamValue());
                    } else {
                        return ctag.getTagName();
                    }
                }).collect(Collectors.joining("、"))));
            } else {
                operContent.append(String.format("标签（%s）修改为：%s；", ObjectUtils.isEmpty(relations) ? "无" : relations.stream().map(tag -> {
                            CTag ctag = tagMap.get(tag.getTagId());
                            if (Objects.isNull(ctag)) {
                                return "";
                            }
                            if (ctag.getHasParam()) {
                                return String.format(ctag.getTagName(), tag.getTagParamValue());
                            } else {
                                return ctag.getTagName();
                            }
                        }).collect(Collectors.joining("、")),
                        tagsDTOS.stream().map(tag -> {
                            if (Objects.isNull(tag.getId())) {
                                return tag.getTagName();
                            } else {
                                CTag ctag = tagMap.get(tag.getId());
                                if (Objects.isNull(ctag)) {
                                    return "";
                                }
                                if (ctag.getHasParam()) {
                                    return String.format(ctag.getTagName(), tag.getParamValue());
                                } else {
                                    return ctag.getTagName();
                                }
                            }
                        }).collect(Collectors.joining("、"))));
            }
        }
        return operContent.toString();
    }

    @Transactional
    public void autoTransfer(NewCustomerTransferVO vo, NewCustomerInfo newCustomerInfo, Long userId, Long deptId, String operName) {
        updateById(new NewCustomerInfo().setId(vo.getId()).setAccountingTopDeptId(vo.getAccountTopDeptId())
                .setStatus(NewCustomerTransferStatus.TRANSFERRED.getCode()).setTransferTime(LocalDateTime.now())
                .setTransferEmployeeName(operName));
        customerServiceService.createByNewCustomerTransfer(getNewCustomerTransferAllInfo(vo.getId(), newCustomerInfo), vo.getServiceNumber(), vo.getAccountTopDeptId(), null);
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.NEW_CUSTOMER_TRANSFER.getCode())
                    .setDeptId(deptId)
                    .setOperType("完成新户流转")
                    .setOperName(operName)
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }
}
