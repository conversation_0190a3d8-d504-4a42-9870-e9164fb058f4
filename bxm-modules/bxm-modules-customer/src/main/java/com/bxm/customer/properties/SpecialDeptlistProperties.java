package com.bxm.customer.properties;

import com.bxm.common.core.utils.StringUtils;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "special.deptlist")
@Getter
@Setter
public class SpecialDeptlistProperties
{
    public String getList() {
        return list;
    }

    public void setList(String list) {
        this.list = list;
    }

    public List<Long> getDeptList() {
        return StringUtils.isEmpty(list) ? Lists.newArrayList() : Arrays.stream(list.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }

    private String list;
}
