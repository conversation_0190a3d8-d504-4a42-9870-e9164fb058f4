package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.docHandover.DocHandoverFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceDocHandoverFile;
import com.bxm.customer.mapper.CustomerServiceDocHandoverFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.ICustomerServiceDocHandoverFileService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 材料、资料 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
public class CustomerServiceDocHandoverFileServiceImpl extends ServiceImpl<CustomerServiceDocHandoverFileMapper, CustomerServiceDocHandoverFile> implements ICustomerServiceDocHandoverFileService {
    @Autowired
    private CustomerServiceDocHandoverFileMapper customerServiceDocHandoverFileMapper;

    @Autowired
    private FileService fileService;

    /**
     * 查询材料、资料 附件
     *
     * @param id 材料、资料 附件主键
     * @return 材料、资料 附件
     */
    @Override
    public CustomerServiceDocHandoverFile selectCustomerServiceDocHandoverFileById(Long id) {
        return customerServiceDocHandoverFileMapper.selectCustomerServiceDocHandoverFileById(id);
    }

    /**
     * 查询材料、资料 附件列表
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 材料、资料 附件
     */
    @Override
    public List<CustomerServiceDocHandoverFile> selectCustomerServiceDocHandoverFileList(CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        return customerServiceDocHandoverFileMapper.selectCustomerServiceDocHandoverFileList(customerServiceDocHandoverFile);
    }

    /**
     * 新增材料、资料 附件
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    @Override
    public int insertCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        customerServiceDocHandoverFile.setCreateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverFileMapper.insertCustomerServiceDocHandoverFile(customerServiceDocHandoverFile);
    }

    /**
     * 修改材料、资料 附件
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    @Override
    public int updateCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile) {
        customerServiceDocHandoverFile.setUpdateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverFileMapper.updateCustomerServiceDocHandoverFile(customerServiceDocHandoverFile);
    }

    /**
     * 批量删除材料、资料 附件
     *
     * @param ids 需要删除的材料、资料 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverFileByIds(Long[] ids) {
        return customerServiceDocHandoverFileMapper.deleteCustomerServiceDocHandoverFileByIds(ids);
    }

    /**
     * 删除材料、资料 附件信息
     *
     * @param id 材料、资料 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverFileById(Long id) {
        return customerServiceDocHandoverFileMapper.deleteCustomerServiceDocHandoverFileById(id);
    }

    @Override
    public List<CustomerServiceDocHandoverFile> selectByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes) {
        List<CustomerServiceDocHandoverFile> result = Collections.emptyList();
        if (customerServiceDocHandoverId == null) {
            return result;
        }

        if (ObjectUtils.isEmpty(docHandoverFileTypes)) {
            result = list(new LambdaQueryWrapper<CustomerServiceDocHandoverFile>()
                    .eq(CustomerServiceDocHandoverFile::getCustomerServiceDocId, customerServiceDocHandoverId)
            );
        } else {
            result = list(new LambdaQueryWrapper<CustomerServiceDocHandoverFile>()
                    .eq(CustomerServiceDocHandoverFile::getCustomerServiceDocId, customerServiceDocHandoverId)
                    .in(CustomerServiceDocHandoverFile::getFileType, docHandoverFileTypes.stream().map(DocHandoverFileType::getCode).distinct().collect(Collectors.toList()))
            );
        }

        return result;
    }

    @Override
    public Map<Integer, List<CustomerServiceDocHandoverFile>> selectMapByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes) {
        List<CustomerServiceDocHandoverFile> files = selectByDocHandover(customerServiceDocHandoverId, docHandoverFileTypes);
        return files.stream().collect(Collectors.groupingBy(CustomerServiceDocHandoverFile::getFileType));
    }

    @Override
    public Map<String, List<CustomerServiceDocHandoverFile>> selectMapSubKeyByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes) {
        List<CustomerServiceDocHandoverFile> files = selectByDocHandover(customerServiceDocHandoverId, docHandoverFileTypes);
        return files.stream().collect(Collectors.groupingBy(row -> key(row.getFileType(), row.getSubFileType())));
    }

    @Override
    public void deleteByDocHandoverId(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes) {
        if (customerServiceDocHandoverId != null) {
            if (ObjectUtils.isEmpty(docHandoverFileTypes)) {
                remove(new LambdaQueryWrapper<CustomerServiceDocHandoverFile>()
                        .eq(CustomerServiceDocHandoverFile::getCustomerServiceDocId, customerServiceDocHandoverId));
            } else {
                remove(new LambdaQueryWrapper<CustomerServiceDocHandoverFile>()
                        .eq(CustomerServiceDocHandoverFile::getCustomerServiceDocId, customerServiceDocHandoverId)
                        .in(CustomerServiceDocHandoverFile::getFileType, docHandoverFileTypes.stream().map(DocHandoverFileType::getCode).distinct().collect(Collectors.toList()))
                );
            }
        }
    }

    @Override
    public void saveFile(Long customerServiceDocId, List<CommonFileVO> files, DocHandoverFileType docHandoverFileType, String subFileType) {
        if (!ObjectUtils.isEmpty(files)) {
            Integer fileType = docHandoverFileType.getCode();
            saveBatch(
                    files.stream().map(f -> new CustomerServiceDocHandoverFile()
                            .setCustomerServiceDocId(customerServiceDocId)
                            .setFileName(f.getFileName())
                            .setFileType(fileType)
                            .setSubFileType(subFileType)
                            .setFileUrl(f.getFileUrl()))
                            .collect(Collectors.toList())
            );
        }
    }

    @Override
    public List<CommonFileVO> covToCommonFileVO(List<CustomerServiceDocHandoverFile> files) {
        return ObjectUtils.isEmpty(files) ? Lists.newArrayList()
                : files.stream()
                .map(f -> CommonFileVO.builder()
                        .fileName(f.getFileName())
                        .fileUrl(f.getFileUrl())
                        .fullFileUrl(fileService.getFullFileUrl(f.getFileUrl()))
                        .build())
                .collect(Collectors.toList());
    }

    public static String key(Integer fileType, String subFileType) {
        return fileType + "_" + subFileType;
    }
}
