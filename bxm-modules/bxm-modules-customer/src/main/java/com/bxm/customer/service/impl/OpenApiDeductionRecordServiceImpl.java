package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.OpenApiDeductionRecord;
import com.bxm.customer.mapper.OpenApiDeductionRecordMapper;
import com.bxm.customer.service.IOpenApiDeductionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 第三方申报同步Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class OpenApiDeductionRecordServiceImpl extends ServiceImpl<OpenApiDeductionRecordMapper, OpenApiDeductionRecord> implements IOpenApiDeductionRecordService
{
    @Autowired
    private OpenApiDeductionRecordMapper openApiDeductionRecordMapper;

}
