package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerFixedAssetsInfo;

/**
 * 新户流转固定资产信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerFixedAssetsInfoMapper extends BaseMapper<NewCustomerFixedAssetsInfo>
{
    /**
     * 查询新户流转固定资产信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 新户流转固定资产信息
     */
    public NewCustomerFixedAssetsInfo selectNewCustomerFixedAssetsInfoById(Long id);

    /**
     * 查询新户流转固定资产信息列表
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 新户流转固定资产信息集合
     */
    public List<NewCustomerFixedAssetsInfo> selectNewCustomerFixedAssetsInfoList(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 新增新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    public int insertNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 修改新户流转固定资产信息
     * 
     * @param newCustomerFixedAssetsInfo 新户流转固定资产信息
     * @return 结果
     */
    public int updateNewCustomerFixedAssetsInfo(NewCustomerFixedAssetsInfo newCustomerFixedAssetsInfo);

    /**
     * 删除新户流转固定资产信息
     * 
     * @param id 新户流转固定资产信息主键
     * @return 结果
     */
    public int deleteNewCustomerFixedAssetsInfoById(Long id);

    /**
     * 批量删除新户流转固定资产信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerFixedAssetsInfoByIds(Long[] ids);
}
