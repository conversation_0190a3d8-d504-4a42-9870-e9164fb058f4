package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceInAccountFile;

/**
 * 入账交付 附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper
public interface CustomerServiceInAccountFileMapper extends BaseMapper<CustomerServiceInAccountFile>
{
    /**
     * 查询入账交付 附件
     * 
     * @param id 入账交付 附件主键
     * @return 入账交付 附件
     */
    public CustomerServiceInAccountFile selectCustomerServiceInAccountFileById(Long id);

    /**
     * 查询入账交付 附件列表
     * 
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 入账交付 附件集合
     */
    public List<CustomerServiceInAccountFile> selectCustomerServiceInAccountFileList(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 新增入账交付 附件
     * 
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    public int insertCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 修改入账交付 附件
     * 
     * @param customerServiceInAccountFile 入账交付 附件
     * @return 结果
     */
    public int updateCustomerServiceInAccountFile(CustomerServiceInAccountFile customerServiceInAccountFile);

    /**
     * 删除入账交付 附件
     * 
     * @param id 入账交付 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceInAccountFileById(Long id);

    /**
     * 批量删除入账交付 附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInAccountFileByIds(Long[] ids);
}
