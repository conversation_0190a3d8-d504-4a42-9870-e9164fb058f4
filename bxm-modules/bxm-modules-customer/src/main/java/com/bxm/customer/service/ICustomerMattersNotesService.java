package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerMattersNotes;

/**
 * 事项备忘Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ICustomerMattersNotesService extends IService<CustomerMattersNotes>
{
    /**
     * 查询事项备忘
     * 
     * @param id 事项备忘主键
     * @return 事项备忘
     */
    public CustomerMattersNotes selectCustomerMattersNotesById(Long id);

    /**
     * 查询事项备忘列表
     * 
     * @param customerMattersNotes 事项备忘
     * @return 事项备忘集合
     */
    public List<CustomerMattersNotes> selectCustomerMattersNotesList(CustomerMattersNotes customerMattersNotes);

    /**
     * 新增事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    public int insertCustomerMattersNotes(CustomerMattersNotes customerMattersNotes);

    /**
     * 修改事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    public int updateCustomerMattersNotes(CustomerMattersNotes customerMattersNotes);

    /**
     * 批量删除事项备忘
     * 
     * @param ids 需要删除的事项备忘主键集合
     * @return 结果
     */
    public int deleteCustomerMattersNotesByIds(Long[] ids);

    /**
     * 删除事项备忘信息
     * 
     * @param id 事项备忘主键
     * @return 结果
     */
    public int deleteCustomerMattersNotesById(Long id);

    String getMattersNotesByCustomerServiceIdAndItemType(Long customerServiceId, Integer itemType);

    void saveOrUpdateMattersNotes(Long customerServiceId, Integer itemType, String mattersNotes);
}
