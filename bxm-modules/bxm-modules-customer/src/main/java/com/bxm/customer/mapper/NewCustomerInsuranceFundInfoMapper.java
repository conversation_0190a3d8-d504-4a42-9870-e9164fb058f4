package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerInsuranceFundInfo;

/**
 * 新户流转五险一金信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerInsuranceFundInfoMapper extends BaseMapper<NewCustomerInsuranceFundInfo>
{
    /**
     * 查询新户流转五险一金信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 新户流转五险一金信息
     */
    public NewCustomerInsuranceFundInfo selectNewCustomerInsuranceFundInfoById(Long id);

    /**
     * 查询新户流转五险一金信息列表
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 新户流转五险一金信息集合
     */
    public List<NewCustomerInsuranceFundInfo> selectNewCustomerInsuranceFundInfoList(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 新增新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    public int insertNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 修改新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    public int updateNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 删除新户流转五险一金信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundInfoById(Long id);

    /**
     * 批量删除新户流转五险一金信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundInfoByIds(Long[] ids);
}
