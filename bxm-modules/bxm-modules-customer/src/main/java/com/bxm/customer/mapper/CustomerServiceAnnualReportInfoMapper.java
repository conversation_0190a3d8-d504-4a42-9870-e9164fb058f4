package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceAnnualReportInfo;

/**
 * 客户服务工商年报信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface CustomerServiceAnnualReportInfoMapper extends BaseMapper<CustomerServiceAnnualReportInfo>
{
    /**
     * 查询客户服务工商年报信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 客户服务工商年报信息
     */
    public CustomerServiceAnnualReportInfo selectCustomerServiceAnnualReportInfoById(Long id);

    /**
     * 查询客户服务工商年报信息列表
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 客户服务工商年报信息集合
     */
    public List<CustomerServiceAnnualReportInfo> selectCustomerServiceAnnualReportInfoList(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 新增客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    public int insertCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 修改客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    public int updateCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo);

    /**
     * 删除客户服务工商年报信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 结果
     */
    public int deleteCustomerServiceAnnualReportInfoById(Long id);

    /**
     * 批量删除客户服务工商年报信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceAnnualReportInfoByIds(Long[] ids);
}
