package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.dto.OpenApiRecordDTO;
import com.bxm.customer.domain.vo.OpenApiRecordSearchVO;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.IDownloadRecordService;
import com.bxm.customer.service.IOpenApiNoticeRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 第三方通知/被通知记录Controller
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
@RestController
@RequestMapping("/openapi/record")
@Api(tags = "第三方通知/被通知记录")
public class OpenApiNoticeRecordController
{
    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @GetMapping("/openapiRecordList")
    @ApiOperation("第三方通知/被通知记录列表")
    public Result<IPage<OpenApiRecordDTO>> openapiRecordList(OpenApiRecordSearchVO vo) {
        return Result.ok(openApiNoticeRecordService.openapiRecordList(vo));
    }

    @PostMapping("/openapiRecordListExportAndUpload")
    @ApiOperation("第三方通知/被通知记录列表导出（异步导出）form-data传参")
    public Result openapiRecordListExportAndUpload(OpenApiRecordSearchVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "第三方通知" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.OPENAPI_RECORD);
        CompletableFuture.runAsync(() -> {
            try {
                List<OpenApiRecordDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<OpenApiRecordDTO> l = openApiNoticeRecordService.openapiRecordList(vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<OpenApiRecordDTO> util = new ExcelUtil<>(OpenApiRecordDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }
}
