package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CustomerOperatorRecord;
import com.bxm.customer.mapper.CustomerOperatorRecordMapper;
import com.bxm.customer.service.ICustomerOperatorRecordService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class CustomerOperatorRecordServiceImpl extends ServiceImpl<CustomerOperatorRecordMapper, CustomerOperatorRecord> implements ICustomerOperatorRecordService {

    @Override
    @Async
    public void addRecord(Long customerServiceId, Integer operatorType) {
        Integer period = DateUtils.getNowPeriod();
        if (count(new LambdaQueryWrapper<CustomerOperatorRecord>()
                .eq(CustomerOperatorRecord::getCustomerServiceId, customerServiceId)
                .eq(CustomerOperatorRecord::getOperatorType, operatorType)
                .eq(CustomerOperatorRecord::getOperatorMonth, period)) == 0) {
            save(new CustomerOperatorRecord().setCustomerServiceId(customerServiceId)
                    .setOperatorType(operatorType)
                    .setOperatorMonth(period)
                    .setIsDone(false));
        }
    }
}
