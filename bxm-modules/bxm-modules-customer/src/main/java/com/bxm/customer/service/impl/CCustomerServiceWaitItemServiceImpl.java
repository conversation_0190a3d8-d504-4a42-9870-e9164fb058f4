package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.ServiceWaitItemType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.CCustomerServiceWaitItem;
import com.bxm.customer.domain.dto.CustomerServiceDTO;
import com.bxm.customer.domain.dto.CustomerServiceWaitItemDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.mapper.CCustomerServiceWaitItemMapper;
import com.bxm.customer.service.ICBusinessTagRelationService;
import com.bxm.customer.service.ICCustomerServiceWaitItemService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户服务待确认事项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-12
 */
@Service
@Slf4j
public class CCustomerServiceWaitItemServiceImpl extends ServiceImpl<CCustomerServiceWaitItemMapper, CCustomerServiceWaitItem> implements ICCustomerServiceWaitItemService
{
    @Autowired
    private CCustomerServiceWaitItemMapper cCustomerServiceWaitItemMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private AsyncLogService asyncLogService;

    /**
     * 查询客户服务待确认事项
     * 
     * @param id 客户服务待确认事项主键
     * @return 客户服务待确认事项
     */
    @Override
    public CCustomerServiceWaitItem selectCCustomerServiceWaitItemById(Long id)
    {
        return cCustomerServiceWaitItemMapper.selectCCustomerServiceWaitItemById(id);
    }

    /**
     * 查询客户服务待确认事项列表
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 客户服务待确认事项
     */
    @Override
    public List<CCustomerServiceWaitItem> selectCCustomerServiceWaitItemList(CCustomerServiceWaitItem cCustomerServiceWaitItem)
    {
        return cCustomerServiceWaitItemMapper.selectCCustomerServiceWaitItemList(cCustomerServiceWaitItem);
    }

    /**
     * 新增客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    @Override
    public int insertCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem)
    {
        cCustomerServiceWaitItem.setCreateTime(DateUtils.getNowDate());
        return cCustomerServiceWaitItemMapper.insertCCustomerServiceWaitItem(cCustomerServiceWaitItem);
    }

    /**
     * 修改客户服务待确认事项
     * 
     * @param cCustomerServiceWaitItem 客户服务待确认事项
     * @return 结果
     */
    @Override
    public int updateCCustomerServiceWaitItem(CCustomerServiceWaitItem cCustomerServiceWaitItem)
    {
        cCustomerServiceWaitItem.setUpdateTime(DateUtils.getNowDate());
        return cCustomerServiceWaitItemMapper.updateCCustomerServiceWaitItem(cCustomerServiceWaitItem);
    }

    /**
     * 批量删除客户服务待确认事项
     * 
     * @param ids 需要删除的客户服务待确认事项主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServiceWaitItemByIds(Long[] ids)
    {
        return cCustomerServiceWaitItemMapper.deleteCCustomerServiceWaitItemByIds(ids);
    }

    /**
     * 删除客户服务待确认事项信息
     * 
     * @param id 客户服务待确认事项主键
     * @return 结果
     */
    @Override
    public int deleteCCustomerServiceWaitItemById(Long id)
    {
        return cCustomerServiceWaitItemMapper.deleteCCustomerServiceWaitItemById(id);
    }

    @Override
    public void updateInValidByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<CCustomerServiceWaitItem>().in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .eq(CCustomerServiceWaitItem::getItemType, itemType)
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .set(CCustomerServiceWaitItem::getIsValid, Boolean.FALSE));
    }

    @Override
    public void updateDoneStatusByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<CCustomerServiceWaitItem>().in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .eq(CCustomerServiceWaitItem::getItemType, itemType)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0)
                .set(CCustomerServiceWaitItem::getDoneStatus, 1));
    }

    @Override
    public void updateInValidByCustomerServiceIdsAndItemTypes(List<Long> customerServiceIds, List<Integer> itemTypes) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<CCustomerServiceWaitItem>().in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .in(CCustomerServiceWaitItem::getItemType, itemTypes)
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .set(CCustomerServiceWaitItem::getIsValid, Boolean.FALSE));
    }

    @Override
    public void updateDoneStatusByCustomerServiceIdsAndItemTypes(List<Long> customerServiceIds, List<Integer> itemTypes) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        update(new LambdaUpdateWrapper<CCustomerServiceWaitItem>().in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .in(CCustomerServiceWaitItem::getItemType, itemTypes)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0)
                .set(CCustomerServiceWaitItem::getDoneStatus, 1));
    }

    @Override
    public IPage<CustomerServiceWaitItemDTO> customerServiceWaitItemList(Integer itemType, String keyWord, Integer jumpType, Long deptId, List<Long> queryDeptIds, String tagName, Integer tagIncludeFlag, String advisorDeptEmployeeName, String accountingDeptEmployeeName, Integer taxType, Integer pageNum, Integer pageSize) {
        IPage<CustomerServiceWaitItemDTO> iPage = new Page<>(pageNum, pageSize);
        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(tagName)) {
            if (Objects.isNull(tagIncludeFlag)) {
                tagIncludeFlag = 1;
            }
            customerServiceIds = businessTagRelationService.getCustomerIdsByTagNameLike(tagName, TagBusinessType.CUSTOMER_SERVICE);
            if (ObjectUtils.isEmpty(customerServiceIds) && tagIncludeFlag == 1) {
                return iPage;
            }
        }
        UserDeptDTO dept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        dept.setDeptType(jumpType);
//        List<SysDept> deptList = remoteDeptService.getDeptByUserDepts(dept).getDataThrowException();
//        if (ObjectUtils.isEmpty(deptList)) {
//            return iPage;
//        }
        List<Long> advisorSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(advisorDeptEmployeeName)) {
            advisorSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(advisorDeptEmployeeName).getDataThrowException();
            if (ObjectUtils.isEmpty(advisorSearchDeptIds)) {
                return iPage;
            }
        }
        List<Long> accountingSearchDeptIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(accountingDeptEmployeeName)) {
            accountingSearchDeptIds = remoteDeptService.selectDeptIdsByDeptEmployeeName(accountingDeptEmployeeName).getDataThrowException();
            if (ObjectUtils.isEmpty(accountingSearchDeptIds)) {
                return iPage;
            }
        }
        List<CustomerServiceWaitItemDTO> data = baseMapper.customerServiceWaitItemList(iPage, itemType, keyWord, dept.getDeptIds(), jumpType, queryDeptIds, tagName, tagIncludeFlag, customerServiceIds, advisorSearchDeptIds, accountingSearchDeptIds, dept.getIsAdmin() ? 1 : 0, taxType);
        if (!ObjectUtils.isEmpty(data)) {
            List<SysEmployee> employees = Lists.newArrayList();
            List<Long> deptIds = Lists.newArrayList();
            List<Long> topDeptIds = Lists.newArrayList();
            data.forEach(d -> {
                if (!Objects.isNull(d.getAdvisorDeptId()) && !deptIds.contains(d.getAdvisorDeptId())) {
                    deptIds.add(d.getAdvisorDeptId());
                }
                if (!ObjectUtils.isEmpty(d.getAccountingDeptId()) && !deptIds.contains(d.getAccountingDeptId())) {
                    deptIds.add(d.getAccountingDeptId());
                }
                if (!ObjectUtils.isEmpty(d.getAccountingTopDeptId()) && !topDeptIds.contains(d.getAccountingTopDeptId())) {
                    topDeptIds.add(d.getAccountingTopDeptId());
                }
            });
            if (!ObjectUtils.isEmpty(deptIds)) {
                employees = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException(false);
            }
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                    employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(topDeptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(topDeptIds).getDataThrowException(false).stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            Map<Long, List<TagDTO>> businessTagMap = businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(CustomerServiceWaitItemDTO::getCustomerServiceId).distinct().collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
            data.forEach(d -> {
                List<SysEmployee> accountingEmployees = employeeMap.get(d.getAccountingDeptId());
                List<SysEmployee> advisorEmployees = employeeMap.get(d.getAdvisorDeptId());
                d.setAccountingEmployeeNames(ObjectUtils.isEmpty(accountingEmployees) ? "" : accountingEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
                d.setAdvisorEmployeeNames(ObjectUtils.isEmpty(advisorEmployees) ? "" : advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")));
                d.setTagList(businessTagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList()));
                if (!Objects.isNull(d.getAccountingTopDeptId())) {
                    SysDept accountingTopDept = deptMap.get(d.getAccountingTopDeptId());
                    if (!Objects.isNull(accountingTopDept)) {
                        d.setAccountingLeaderDeptId(Long.parseLong(accountingTopDept.getAncestors().split(",")[1]));
                    }
                }
            });
        }
        iPage.setRecords(data);
        return iPage;
    }

    @Override
    public void notDispatch(CommonIdVO vo, Long deptId, Long userId, String operName) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            return;
        }
        List<CCustomerServiceWaitItem> items = list(new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .in(CCustomerServiceWaitItem::getId, vo.getIds())
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0));
        if (ObjectUtils.isEmpty(items)) {
            return;
        }
        update(new CCustomerServiceWaitItem().setDoneStatus(2), new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .in(CCustomerServiceWaitItem::getId, items.stream().map(CCustomerServiceWaitItem::getId).collect(Collectors.toList())));
        items.forEach(row -> {
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getCustomerServiceId())
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(deptId)
                        .setOperType("重派会计")
                        .setOperContent("无需重派")
                        .setOperName(operName)
                        .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    public Integer selectCountByServiceIdsAndItemType(List<Long> customerServiceIds, Integer itemType) {
        return baseMapper.selectCount(new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0)
                .eq(CCustomerServiceWaitItem::getItemType, itemType));
    }

    @Override
    public Integer selectCountByDeptIdsAndItemType(List<Long> deptIds, List<Long> queryDeptIds, Integer itemType) {
        LambdaQueryWrapper<CCustomerServiceWaitItem> wrapper = new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0)
                .eq(CCustomerServiceWaitItem::getItemType, itemType);
        if (!ObjectUtils.isEmpty(deptIds)) {
            String deptIdsStr = deptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            wrapper.inSql(CCustomerServiceWaitItem::getCustomerServiceId, "select id from c_customer_service" +
                    " where is_del = 0 and (advisor_dept_id in (" + deptIdsStr + ") or advisor_top_dept_id in (" + deptIdsStr + ") or accounting_dept_id in (" + deptIdsStr + ") or accounting_top_dept_id in (" + deptIdsStr + "))");
        }
        if (!ObjectUtils.isEmpty(queryDeptIds)) {
            String deptIdsStr = queryDeptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            wrapper.inSql(CCustomerServiceWaitItem::getCustomerServiceId, "select id from c_customer_service" +
                    " where is_del = 0 and (advisor_dept_id in (" + deptIdsStr + ") or advisor_top_dept_id in (" + deptIdsStr + ") or accounting_dept_id in (" + deptIdsStr + ") or accounting_top_dept_id in (" + deptIdsStr + "))");
        }
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public List<CCustomerServiceWaitItem> selectByDeptIdsAndItemType(List<Long> deptIds, Integer itemType) {
        return baseMapper.selectByDeptIdsAndItemType(deptIds, itemType);
    }

    @Override
    public List<CCustomerServiceWaitItem> selectByDeptIdsAndItemType(List<Long> deptIds, List<Long> queryDeptIds, List<Integer> itemTypes) {
        LambdaQueryWrapper<CCustomerServiceWaitItem> wrapper = new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0)
                .in(CCustomerServiceWaitItem::getItemType, itemTypes)
                .select(CCustomerServiceWaitItem::getId, CCustomerServiceWaitItem::getItemType);
        if (!ObjectUtils.isEmpty(deptIds)) {
            String deptIdsStr = deptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            wrapper.inSql(CCustomerServiceWaitItem::getCustomerServiceId, "select id from c_customer_service" +
                    " where is_del = 0 and (advisor_dept_id in (" + deptIdsStr + ") or advisor_top_dept_id in (" + deptIdsStr + ") or accounting_dept_id in (" + deptIdsStr + ") or accounting_top_dept_id in (" + deptIdsStr + "))");
        }
        if (!ObjectUtils.isEmpty(queryDeptIds)) {
            String deptIdsStr = queryDeptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            wrapper.inSql(CCustomerServiceWaitItem::getCustomerServiceId, "select id from c_customer_service" +
                    " where is_del = 0 and (advisor_dept_id in (" + deptIdsStr + ") or advisor_top_dept_id in (" + deptIdsStr + ") or accounting_dept_id in (" + deptIdsStr + ") or accounting_top_dept_id in (" + deptIdsStr + "))");
        }
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Integer selectNotDoneCountByCustomerServiceIdAndItemType(Long customerServiceId, ServiceWaitItemType serviceWaitItemType) {
        return count(new LambdaQueryWrapper<CCustomerServiceWaitItem>().eq(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceId)
                .eq(CCustomerServiceWaitItem::getItemType, serviceWaitItemType.getCode())
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0));
    }

    @Override
    public List<CCustomerServiceWaitItem> selectNotDoneCountByCustomerServiceIdsAndItemType(List<Long> customerServiceIds, ServiceWaitItemType serviceWaitItemType) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CCustomerServiceWaitItem>()
                .in(CCustomerServiceWaitItem::getCustomerServiceId, customerServiceIds)
                .eq(CCustomerServiceWaitItem::getItemType, serviceWaitItemType.getCode())
                .eq(CCustomerServiceWaitItem::getIsValid, Boolean.TRUE)
                .eq(CCustomerServiceWaitItem::getDoneStatus, 0));
    }
}
