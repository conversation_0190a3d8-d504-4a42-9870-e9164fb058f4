package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerBankAccountNumberSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerBankAccountVO;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import com.bxm.customer.domain.dto.CustomerServiceBankAccountDTO;

/**
 * 服务银行账号Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICustomerServiceBankAccountService extends IService<CustomerServiceBankAccount>
{
    /**
     * 查询服务银行账号
     * 
     * @param id 服务银行账号主键
     * @return 服务银行账号
     */
    public CustomerServiceBankAccount selectCustomerServiceBankAccountById(Long id);

    /**
     * 查询服务银行账号列表
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 服务银行账号集合
     */
    public List<CustomerServiceBankAccount> selectCustomerServiceBankAccountList(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 新增服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    public int insertCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 修改服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    public int updateCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 批量删除服务银行账号
     * 
     * @param ids 需要删除的服务银行账号主键集合
     * @return 结果
     */
    public int deleteCustomerServiceBankAccountByIds(Long[] ids);

    /**
     * 删除服务银行账号信息
     * 
     * @param id 服务银行账号主键
     * @return 结果
     */
    public int deleteCustomerServiceBankAccountById(Long id);

    List<CustomerServiceBankAccount> selectByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period);

    List<CustomerServiceBankAccount> selectByCustomerServiceId(Long id);

    void deleteCustomerServiceBankAccount(Long id);

    void addCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, CCustomerService customerService);

    void modifyCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, CCustomerService customerService, CustomerServiceBankAccount oldBankAccount, Long deptId, Long userId, String operName);

    Boolean checkBankAccountExists(Long businessTopDeptId, String account, Long id, String creditCode, Long customerServiceId);

    List<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumbers(RemoteCustomerBankAccountNumberSearchVO vo);

    RemoteCustomerBankAccountDTO getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId);

    void remoteAddCustomerServiceBankAccount(RemoteCustomerBankAccountVO vo, CCustomerService customerService);

    RemoteCustomerBankAccountDTO getNewCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId);
}
