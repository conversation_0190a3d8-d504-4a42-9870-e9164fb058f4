package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.customer.service.AsyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private AsyncService asyncService;

    @GetMapping("/test")
    public Result test() {
        asyncService.test();
        return Result.ok();
    }
}
