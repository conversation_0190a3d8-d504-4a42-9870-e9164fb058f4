package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDTO;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDetailDTO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderSearchVO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderVO;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.IBorrowOrderService;
import com.bxm.customer.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 借阅单Controller
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
@RestController
@RequestMapping("/borrowOrder")
@Api(tags = "材料借阅单")
public class BorrowOrderController
{
    @Autowired
    private IBorrowOrderService borrowOrderService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @GetMapping("/borrowOrderList")
    @ApiOperation("材料借阅单列表")
    public Result<IPage<BorrowOrderDTO>> borrowOrderList(BorrowOrderSearchVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.borrowOrderList(vo, deptId));
    }

    @PostMapping("/exportBorrowOrderList")
    @ApiOperation("导出材料借阅单列表")
    public void exportBorrowOrderList(HttpServletResponse response, BorrowOrderSearchVO vo, @RequestHeader("deptId") Long deptId) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        ExcelUtil<BorrowOrderDTO> util = new ExcelUtil<>(BorrowOrderDTO.class);
        util.exportExcel(response, borrowOrderService.borrowOrderList(vo, deptId).getRecords(), "借阅单列表");
    }

    @PostMapping("/exportBorrowOrderListAndUpload")
    @ApiOperation("导出材料借阅单列表")
    public Result exportBorrowOrderListAndUpload(BorrowOrderSearchVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "材料-借阅" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.MATERIAL_BORROWING);
        CompletableFuture.runAsync(() -> {
            try {
                List<BorrowOrderDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<BorrowOrderDTO> l = borrowOrderService.borrowOrderList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<BorrowOrderDTO> util = new ExcelUtil<>(BorrowOrderDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @GetMapping("/borrowOrderDetail/{id}")
    @ApiOperation("材料借阅单详情")
    public Result<BorrowOrderDetailDTO> borrowOrderDetail(@PathVariable("id") Long id) {
        return Result.ok(borrowOrderService.borrowOrderDetail(id));
    }

    @PostMapping("/createBorrowOrder")
    @ApiOperation("创建材料借阅单")
    public Result createBorrowOrder(@RequestBody BorrowOrderVO vo, @RequestHeader("deptId") Long deptId) {
        borrowOrderService.createBorrowOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/modifyBorrowOrder")
    @ApiOperation("编辑材料借阅单")
    public Result modifyBorrowOrder(@RequestBody BorrowOrderVO vo, @RequestHeader("deptId") Long deptId) {
        borrowOrderService.modifyBorrowOrder(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/submit")
    @ApiOperation("提交/批量提交，传ids")
    public Result<CommonOperateResultDTO> submit(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.submit(vo, deptId));
    }

    @PostMapping("/outStation")
    @ApiOperation("出站/批量出站，传ids，reason，files")
    public Result<CommonOperateResultDTO> outStation(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.outStation(vo, deptId));
    }

    @PostMapping("/reBack")
    @ApiOperation("退回/批量退回，传ids，reason，files")
    public Result<CommonOperateResultDTO> reBack(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.reBack(vo, deptId));
    }

    @PostMapping("/returnBorrow")
    @ApiOperation("归还/批量归还，传ids，reason，files, returnSetting")
    public Result<CommonOperateResultDTO> returnBorrow(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.returnBorrow(vo, deptId));
    }

    @PostMapping("/check")
    @ApiOperation("验收/批量验收，传ids，reason，files")
    public Result<CommonOperateResultDTO> check(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.check(vo, deptId));
    }

    @PostMapping("/dispatch")
    @ApiOperation("移交/批量移交，传ids，targetDeptId，targetEmployeeId")
    public Result<CommonOperateResultDTO> dispatch(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.dispatch(vo, deptId));
    }

    @PostMapping("/delete")
    @ApiOperation("删除/批量删除，传ids")
    public Result<CommonOperateResultDTO> delete(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(borrowOrderService.delete(vo, deptId));
    }
}
