package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteMaterialDeliver;
import com.bxm.customer.api.domain.vo.RemoteMaterialDeliverFileInventory;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.materialDeliver.*;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierCreateVO;
import com.bxm.customer.domain.vo.accoutingCashier.CustomerServiceFileNameVO;
import com.bxm.customer.domain.vo.materialDeliver.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingFileMapper;
import com.bxm.customer.mapper.MaterialDeliverMapper;
import com.bxm.customer.service.*;
import com.bxm.file.api.RemoteFileService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 材料交接单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Service
@Slf4j
public class MaterialDeliverServiceImpl extends ServiceImpl<MaterialDeliverMapper, MaterialDeliver> implements IMaterialDeliverService
{
    @Autowired
    private MaterialDeliverMapper materialDeliverMapper;
    
    @Autowired
    private IMaterialDeliverFileService materialDeliverFileService;

    @Autowired
    private IMaterialDeliverFileInventoryService materialDeliverFileInventoryService;

    @Autowired
    private IMaterialDeliverPeriodInventoryService materialDeliverPeriodInventoryService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private ICustomerServiceDocHandoverService iCustomerServiceDocHandoverService;

    @Autowired
    private CustomerServiceCashierAccountingFileMapper customerServiceCashierAccountingFileMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IMaterialDeliverFileInventoryFileService materialDeliverFileInventoryFileService;

    @Autowired
    @Lazy
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    /**
     * 查询材料交接单
     * 
     * @param id 材料交接单主键
     * @return 材料交接单
     */
    @Override
    public MaterialDeliver selectMaterialDeliverById(Long id)
    {
        return materialDeliverMapper.selectMaterialDeliverById(id);
    }

    /**
     * 查询材料交接单列表
     * 
     * @param materialDeliver 材料交接单
     * @return 材料交接单
     */
    @Override
    public List<MaterialDeliver> selectMaterialDeliverList(MaterialDeliver materialDeliver)
    {
        return materialDeliverMapper.selectMaterialDeliverList(materialDeliver);
    }

    /**
     * 新增材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    @Override
    public int insertMaterialDeliver(MaterialDeliver materialDeliver)
    {
        materialDeliver.setCreateTime(DateUtils.getNowDate());
        return materialDeliverMapper.insertMaterialDeliver(materialDeliver);
    }

    /**
     * 修改材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    @Override
    public int updateMaterialDeliver(MaterialDeliver materialDeliver)
    {
        materialDeliver.setUpdateTime(DateUtils.getNowDate());
        return materialDeliverMapper.updateMaterialDeliver(materialDeliver);
    }

    /**
     * 批量删除材料交接单
     * 
     * @param ids 需要删除的材料交接单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverByIds(Long[] ids)
    {
        return materialDeliverMapper.deleteMaterialDeliverByIds(ids);
    }

    /**
     * 删除材料交接单信息
     * 
     * @param id 材料交接单主键
     * @return 结果
     */
    @Override
    public int deleteMaterialDeliverById(Long id)
    {
        return materialDeliverMapper.deleteMaterialDeliverById(id);
    }

    @Override
    public IPage<MaterialDeliverDTO> materialDeliverList(MaterialDeliverSearchVO vo, Long deptId) {
        IPage<MaterialDeliverDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        List<MaterialDeliverDTO> data = baseMapper.materialDeliverList(result, vo, userDept);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> commitDeptIds = data.stream().map(MaterialDeliverDTO::getCommitDeptId).distinct().collect(Collectors.toList());
            Map<Long, String> deptMap = remoteDeptService.getByDeptIds(commitDeptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
//            Map<Long, List<MaterialDeliverFileInventory>> fileInventoryMap = materialDeliverFileInventoryService.selectCanNotPushFileInventory(data.stream().map(MaterialDeliverDTO::getId).collect(Collectors.toList()))
//                    .stream().collect(Collectors.groupingBy(MaterialDeliverFileInventory::getMaterialDeliverId));
            data.forEach(d -> {
                d.setMaterialDeliverTypeStr(MaterialDeliverType.getByCode(d.getMaterialDeliverType()).getName());
                d.setMaterialDeliverAnalysisStatusStr(MaterialDeliverAnalysisStatus.getByCode(d.getMaterialDeliverAnalysisStatus()).getName());
                d.setMaterialDeliverAnalysisResultStr(Objects.isNull(d.getMaterialDeliverAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(d.getMaterialDeliverAnalysisResult()).getName());
                d.setMaterialDeliverPushStatusStr(Objects.isNull(d.getMaterialDeliverPushStatus()) ? "-" : MaterialDeliverPushStatus.getByCode(d.getMaterialDeliverPushStatus()).getName());
                d.setCommitInfo(deptMap.getOrDefault(d.getCommitDeptId(), "") + (StringUtils.isEmpty(d.getCommitUserNickName()) ? "" : ("（" + d.getCommitUserNickName() + "）")));
                d.setCreateTimeStr(d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setLastOperTimeStr(Objects.isNull(d.getLastOperTime()) ? "" : d.getLastOperTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
                d.setCanPush(getCanPushV2(d));
            });
        }
        result.setRecords(data);
        return result;
    }

    private Boolean getCanPush(MaterialDeliverDTO materialDeliver,  Map<Long, List<MaterialDeliverFileInventory>> canNotPushFileInventoryMap) {
        return Objects.equals(MaterialDeliverPushStatus.WAIT_PUSH.getCode(), materialDeliver.getMaterialDeliverPushStatus())
                && Objects.equals(materialDeliver.getMaterialDeliverAnalysisStatus(), MaterialDeliverAnalysisStatus.PARSE_SUCCESS.getCode())
                && !canNotPushFileInventoryMap.containsKey(materialDeliver.getId());
    }

    private Boolean getCanPushV2(MaterialDeliverDTO materialDeliver) {
        return Objects.equals(MaterialDeliverPushStatus.WAIT_PUSH.getCode(), materialDeliver.getMaterialDeliverPushStatus())
                && Objects.equals(materialDeliver.getMaterialDeliverAnalysisStatus(), MaterialDeliverAnalysisStatus.PARSE_SUCCESS.getCode());
    }

    @Override
    public MaterialDeliverDetailDTO materialDeliverDetail(Long id) {
        MaterialDeliver materialDeliver = checkMaterialDeliverExists(id);
        return MaterialDeliverDetailDTO.builder()
                .id(id)
                .materialDeliverNumber(materialDeliver.getMaterialDeliverNumber())
                .materialDeliverType(materialDeliver.getMaterialDeliverType())
                .materialDeliverTypeStr(MaterialDeliverType.getByCode(materialDeliver.getMaterialDeliverType()).getName())
                .analysisStatus(materialDeliver.getAnalysisStatus())
                .analysisStatusStr(MaterialDeliverAnalysisStatus.getByCode(materialDeliver.getAnalysisStatus()).getName())
                .analysisResult(materialDeliver.getAnalysisResult())
                .analysisResultStr(Objects.isNull(materialDeliver.getAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(materialDeliver.getAnalysisResult()).getName())
                .pushStatus(materialDeliver.getPushStatus())
                .pushStatusStr(Objects.isNull(materialDeliver.getPushStatus()) ? "-" : MaterialDeliverPushStatus.getByCode(materialDeliver.getPushStatus()).getName())
                .build();
    }

    @Override
    public IPage<MaterialFileInventoryDTO> materialFileInventoryPageList(Long id, Integer pageNum, Integer pageSize) {
        MaterialDeliver materialDeliver = checkMaterialDeliverExists(id);
        return materialDeliverFileInventoryService.materialFileInventoryPageList(materialDeliver, pageNum, pageSize);
    }

    @Override
    public IPage<MaterialFileInventoryV2DTO> materialFileInventoryPageListV2(Long id, Integer pageNum, Integer pageSize) {
        MaterialDeliver materialDeliver = checkMaterialDeliverExists(id);
        return materialDeliverFileInventoryService.materialFileInventoryPageListV2(materialDeliver, pageNum, pageSize);
    }

    @Override
    public List<CommonFileVO> materialFileInventoryList(Long id) {
        MaterialDeliver materialDeliver = checkMaterialDeliverExists(id);
        return materialDeliverFileInventoryService.materialFileInventoryList(materialDeliver);
    }

    @Override
    public IPage<MaterialPeriodInventoryDTO> materialPeriodInventoryPageList(Long id, Integer pageNum, Integer pageSize) {
        MaterialDeliver materialDeliver = checkMaterialDeliverExists(id);
        return materialDeliverPeriodInventoryService.materialPeriodInventoryPageList(materialDeliver.getId(), pageNum, pageSize);
    }

    @Override
    public List<CommonFileVO> getMaterialPeriodInventoryFile(Long materialPeriodInventoryId) {
        return materialDeliverPeriodInventoryService.getMaterialPeriodInventoryFile(materialPeriodInventoryId);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliver> deleteMaterialDeliver(List<Long> ids, Long deptId) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要删除的交付单");
        }
        TCommonOperateDTO<MaterialDeliver> result = new TCommonOperateDTO<>();
        List<MaterialDeliver> totalMaterialDeliver = baseMapper.selectBatchIds(ids);
        if (ObjectUtils.isEmpty(totalMaterialDeliver)) {
            return result;
        }
        result.setTotal(totalMaterialDeliver);
        List<Long> commitDeptIds = totalMaterialDeliver.stream().map(MaterialDeliver::getCommitDeptId).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(commitDeptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        List<MaterialDeliver> successList = Lists.newArrayList();
        List<MaterialDeliver> failList = Lists.newArrayList();
        List<MaterialDeliverErrorDTO> errorList = Lists.newArrayList();
        for (MaterialDeliver materialDeliver : totalMaterialDeliver) {
            if (materialDeliver.getIsDel()) {
                failList.add(materialDeliver);
                errorList.add(buildMaterialDeliverDTO(materialDeliver, deptNameMap, "交付单已删除"));
            } else if (!Objects.equals(materialDeliver.getPushStatus(), MaterialDeliverPushStatus.WAIT_PUSH.getCode())) {
                failList.add(materialDeliver);
                errorList.add(buildMaterialDeliverDTO(materialDeliver, deptNameMap, "推送状态错误"));
            } else {
                successList.add(materialDeliver);
            }
        }
        if (!ObjectUtils.isEmpty(successList)) {
            LocalDateTime operTime = LocalDateTime.now();
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
            String operType = Constants.MATERIAL_DELIVER_DELETE;
            // 删除交付单，删除解析数据
            updateBatchById(successList.stream().map(row -> new MaterialDeliver().setId(row.getId()).setIsDel(true)
                    .setLastOperName(operateUserInfoDTO.getOperName())
                    .setLastOperTime(operTime)
                    .setLastOperType(operType)).collect(Collectors.toList()));
            List<Long> lastSuccessDeliverIds = successList.stream().map(MaterialDeliver::getId).collect(Collectors.toList());
            materialDeliverFileInventoryService.logicDeleteByBatchMaterialDeliverIds(lastSuccessDeliverIds);
            materialDeliverPeriodInventoryService.logicDeleteByBatchMaterialDeliverIds(lastSuccessDeliverIds);
            // 写入操作记录
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setCreateTime(operTime)
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        result.setSuccess(successList);
        result.setFail(failList);
        String errorDataBatchNo = "";
        if (!ObjectUtils.isEmpty(errorList)) {
            errorDataBatchNo = UUID.randomUUID().toString().replaceAll("-", "");
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_OPERATE_ERROR_RECORD + errorDataBatchNo, errorList, 500, 60 * 60, TimeUnit.SECONDS);
        }
        result.setErrorDataBatchNo(errorDataBatchNo);
        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliver> stopMaterialDeliverAnalysis(List<Long> ids, Long deptId) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要中止的交付单");
        }
        TCommonOperateDTO<MaterialDeliver> result = new TCommonOperateDTO<>();
        List<MaterialDeliver> totalMaterialDeliver = baseMapper.selectBatchIds(ids);
        if (ObjectUtils.isEmpty(totalMaterialDeliver)) {
            return result;
        }
        result.setTotal(totalMaterialDeliver);
        List<Long> commitDeptIds = totalMaterialDeliver.stream().map(MaterialDeliver::getCommitDeptId).distinct().collect(Collectors.toList());
        Map<Long, String> deptNameMap = remoteDeptService.getByDeptIds(commitDeptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        List<MaterialDeliver> successList = Lists.newArrayList();
        List<MaterialDeliver> lastSuccessList = Lists.newArrayList();
        List<MaterialDeliver> failList = Lists.newArrayList();
        List<MaterialDeliverErrorDTO> errorList = Lists.newArrayList();
        for (MaterialDeliver materialDeliver : totalMaterialDeliver) {
            if (materialDeliver.getIsDel()) {
                failList.add(materialDeliver);
                errorList.add(buildMaterialDeliverDTO(materialDeliver, deptNameMap, "交付单已删除"));
            } else if (!Objects.equals(MaterialDeliverAnalysisStatus.PARSING.getCode(), materialDeliver.getAnalysisStatus())) {
                failList.add(materialDeliver);
                errorList.add(buildMaterialDeliverDTO(materialDeliver, deptNameMap, "解析状态错误"));
            } else if (StringUtils.isEmpty(materialDeliver.getAnalysisTaskId())) {
                failList.add(materialDeliver);
                errorList.add(buildMaterialDeliverDTO(materialDeliver, deptNameMap, "中止异常，解析任务id为空"));
            } else {
                successList.add(materialDeliver);
            }
        }
        if (!ObjectUtils.isEmpty(successList)) {
            successList.forEach(row -> {
                try {
                    remoteFileService.stopAnalysisTask(row.getAnalysisTaskId(), SecurityConstants.INNER);
                    lastSuccessList.add(row);
                } catch (ServiceException e) {
                    failList.add(row);
                    errorList.add(buildMaterialDeliverDTO(row, deptNameMap, e.getMessage()));
                } catch (Exception e) {
                    log.error("中止解析任务异常:{}", e.getMessage());
                    failList.add(row);
                    errorList.add(buildMaterialDeliverDTO(row, deptNameMap, "中止异常：系统错误"));
                }
            });
        }
        result.setSuccess(lastSuccessList);
        result.setFail(failList);
        String errorDataBatchNo = "";
        if (!ObjectUtils.isEmpty(errorList)) {
            errorDataBatchNo = UUID.randomUUID().toString().replaceAll("-", "");
            redisService.setLargeCacheList(CacheConstants.MATERIAL_DELIVER_OPERATE_ERROR_RECORD + errorDataBatchNo, errorList, 500, 60 * 60, TimeUnit.SECONDS);
        }
        if (!ObjectUtils.isEmpty(lastSuccessList)) {
            LocalDateTime operTime = LocalDateTime.now();
            OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
            String operType = Constants.MATERIAL_DELIVER_STOP_ANALYSIS;
            // 修改任务状态，删除解析数据
            updateBatchById(lastSuccessList.stream().map(row -> new MaterialDeliver().setId(row.getId()).setAnalysisStatus(MaterialDeliverAnalysisStatus.PARSE_STOP.getCode())
                    .setLastOperName(operateUserInfoDTO.getOperName())
                    .setLastOperTime(operTime)
                    .setLastOperType(operType)).collect(Collectors.toList()));
            List<Long> lastSuccessDeliverIds = lastSuccessList.stream().map(MaterialDeliver::getId).collect(Collectors.toList());
            materialDeliverFileInventoryService.deleteByBatchMaterialDeliverIds(lastSuccessDeliverIds);
            materialDeliverPeriodInventoryService.deleteByBatchMaterialDeliverIds(lastSuccessDeliverIds);
            // 写入操作记录
            lastSuccessList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setCreateTime(operTime)
                                    .setOperName(operateUserInfoDTO.getOperName())
                                    .setOperUserId(operateUserInfoDTO.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
        }
        result.setErrorDataBatchNo(errorDataBatchNo);
        return result;
    }

    @Override
    public List<MaterialDeliver> selectBatchByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<MaterialDeliver>()
                .in(MaterialDeliver::getId, ids)
                .eq(MaterialDeliver::getIsDel, false));
    }

    @Override
    public MaterialPushPreviewDTO materialPushPreview(List<Long> ids) {
        List<MaterialDeliver> waitPushList = list(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false)
                .eq(MaterialDeliver::getAnalysisStatus, MaterialDeliverAnalysisStatus.PARSE_SUCCESS.getCode())
                .eq(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.WAIT_PUSH.getCode())
                .in(MaterialDeliver::getId, ids));
        if (ObjectUtils.isEmpty(waitPushList)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        List<Long> canNotPushDeliverIds = materialDeliverFileInventoryService.selectCanNotPushFileInventory(waitPushList.stream().map(MaterialDeliver::getId).collect(Collectors.toList()))
                .stream().map(MaterialDeliverFileInventory::getMaterialDeliverId).distinct().collect(Collectors.toList());
        waitPushList = waitPushList.stream().filter(row -> !canNotPushDeliverIds.contains(row.getId())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(waitPushList)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        return materialDeliverPeriodInventoryService.materialPushPreview(waitPushList);
    }

    @Override
    public MaterialPushPreviewDTO materialPushPreviewV2(List<Long> ids) {
        List<MaterialDeliver> waitPushList = list(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false)
                .eq(MaterialDeliver::getAnalysisStatus, MaterialDeliverAnalysisStatus.PARSE_SUCCESS.getCode())
                .eq(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.WAIT_PUSH.getCode())
                .in(MaterialDeliver::getId, ids));
        if (ObjectUtils.isEmpty(waitPushList)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        if (ObjectUtils.isEmpty(waitPushList)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        return materialDeliverPeriodInventoryService.materialPushPreviewV2(waitPushList);
    }

    @Override
    public List<MaterialPushPreviewListDTO> getPushReviewErrorList(String batchNo) {
        return materialDeliverPeriodInventoryService.getPushReviewErrorList(batchNo);
    }

    @Override
    public void confirmPush(String batchNo, Long deptId) {
        materialDeliverPeriodInventoryService.confirmPush(batchNo, deptId);
    }

    @Override
    public void confirmPushV2(String batchNo, Long deptId) {
        List<AccountingCashierCreateVO> createVOList = materialDeliverPeriodInventoryService.confirmPushV2(batchNo, deptId);
        if (!ObjectUtils.isEmpty(createVOList)) {
            customerServiceCashierAccountingService.bankReceiptPaperUpload(createVOList);
        }
    }

    @Override
    public IPage<MaterialDeliverPeriodInventoryDTO> periodInventoryPageList(MaterialDeliverPeriodInventorySearchVO vo, Long deptId) {
        return materialDeliverPeriodInventoryService.periodInventoryPageList(vo, deptId);
    }

    @Override
    public List<CommonDeptCountDTO> getMaterialDeliverDeptSelectList(UserDeptDTO userDeptDTO, Integer materialDeliverType, Integer materialDeliverAnalysisStatus, Integer materialDeliverAnalysisResult, Integer materialDeliverPushStatus) {
        MaterialDeliverSearchVO vo = new MaterialDeliverSearchVO();
        vo.setMaterialDeliverType(Objects.isNull(materialDeliverType) ? "" : materialDeliverType.toString());
        vo.setMaterialDeliverAnalysisStatus(Objects.isNull(materialDeliverAnalysisStatus) ? "" : materialDeliverAnalysisStatus.toString());
        vo.setMaterialDeliverAnalysisResult(Objects.isNull(materialDeliverAnalysisResult) ? "" : materialDeliverAnalysisResult.toString());
        vo.setMaterialDeliverPushStatus(Objects.isNull(materialDeliverPushStatus) ? "" : materialDeliverPushStatus.toString());
        return baseMapper.materialCommitDeptCountList(userDeptDTO, vo);
    }

    @Override
    public IPage<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailPageList(MaterialDeliverInventoryDetailSearchVO vo, Long deptId) {
        IPage<MaterialDeliverInventoryDetailDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        MaterialDeliver materialDeliver = getById(vo.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            return result;
        }
        List<MaterialDeliverInventoryDetailDTO> data = baseMapper.materialDeliverInventoryDetailList(result, vo, materialDeliver.getMaterialDeliverType(), materialDeliver.getPushStatus());
        if (!ObjectUtils.isEmpty(data)) {
            List<CustomerServiceFileNameVO> customerServiceFileName = data.stream().filter(row -> !Objects.isNull(row.getCustomerServiceId()) && !StringUtils.isEmpty(row.getFileName()))
                    .map(row -> CustomerServiceFileNameVO.builder().customerServiceId(row.getCustomerServiceId()).fileName(row.getFileName()).build()).collect(Collectors.toList());
            List<CustomerServiceCashierAccountingFile> existsCustomerServiceCashierAccountingFileList = ObjectUtils.isEmpty(customerServiceFileName) || Objects.equals(materialDeliver.getPushStatus(), MaterialDeliverPushStatus.PUSHED.getCode()) ? Lists.newArrayList() :
                    customerServiceCashierAccountingFileMapper.selectByBatchCustomerServiceIdAndFileName(customerServiceFileName);
            Map<String, List<CustomerServiceCashierAccountingFile>> existsCustomerServiceCashierAccountingFileMap = existsCustomerServiceCashierAccountingFileList.stream()
                    .collect(Collectors.groupingBy(row -> row.getCustomerServiceId() + "_" + row.getFileName()));
            List<Long> customerServiceIds = data.stream().map(MaterialDeliverInventoryDetailDTO::getCustomerServiceId)
                    .filter(customerServiceId -> !Objects.isNull(customerServiceId)).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, row -> row));
            data.forEach(d -> {
                d.setAnalysisResultStr(MaterialDeliverAnalysisResult.getByCode(d.getAnalysisResult()).getName());
                if (Objects.equals(materialDeliver.getPushStatus(), MaterialDeliverPushStatus.PUSHED.getCode()) || Objects.isNull(d.getCustomerServiceId()) || StringUtils.isEmpty(d.getFileName())) {
                    d.setIsRepeat(null);
                } else {
                    d.setIsRepeat(existsCustomerServiceCashierAccountingFileMap.containsKey(d.getCustomerServiceId() + "_" + d.getFileName()) ? 1 : null);
                }
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
                d.setIsCustomerNameNotSame(null);
                if (!Objects.isNull(d.getCustomerServiceId())) {
                    CCustomerService customerService = customerServiceMap.get(d.getCustomerServiceId());
                    d.setCustomerServiceName(Objects.isNull(customerService) ? "" : customerService.getCustomerName());
                    if (!Objects.isNull(customerService)) {
                        d.setIsCustomerNameNotSame(!Objects.equals(d.getCustomerServiceName(), d.getCustomerName()) ? 1 : null);
//                        d.setCustomerName(customerService.getCustomerName());
                    }
                }
            });
        }
        result.setRecords(data);
        return result;
    }

    @Override
    public IPage<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailPageListV2(MaterialDeliverInventoryDetailSearchVO vo, Long deptId) {
        IPage<MaterialFileInventoryV2DTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        MaterialDeliver materialDeliver = getById(vo.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            return result;
        }
        List<MaterialFileInventoryV2DTO> data = baseMapper.materialDeliverInventoryDetailListV2(result, vo, materialDeliver.getMaterialDeliverType());
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> customerServiceIds = data.stream().map(MaterialFileInventoryV2DTO::getCustomerServiceId)
                    .filter(customerServiceId -> !Objects.isNull(customerServiceId)).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(customerServiceIds) ? Maps.newHashMap() :
                    customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>().eq(CCustomerService::getIsDel, false)
                            .in(CCustomerService::getId, customerServiceIds)).stream().collect(Collectors.toMap(CCustomerService::getId, row -> row));
            Map<Long, List<MaterialDeliverFileInventoryFile>> fileMap = materialDeliverFileInventoryFileService.selectByBatchFileInventoryIdAndFileType(data.stream().map(MaterialFileInventoryV2DTO::getId).collect(Collectors.toList()), null)
                    .stream().collect(Collectors.groupingBy(MaterialDeliverFileInventoryFile::getFileInventoryId));
            data.forEach(d -> {
                d.setAnalysisResultStr(MaterialDeliverAnalysisResult.getByCode(d.getAnalysisResult()).getName());
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
                d.setPushResultStr(Objects.isNull(d.getPushResult()) ? "" : MaterialDeliverPushResult.getByCode(d.getPushResult()).getName());
                List<MaterialDeliverFileInventoryFile> files = fileMap.get(d.getId());
                d.setCheckFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().filter(f -> Objects.equals(f.getFileType(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode()))
                        .map(f -> CommonFileVO.builder().fileUrl(f.getFileUrl()).fileName(f.getFileName()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
                d.setReceiptFiles(ObjectUtils.isEmpty(files) ? Lists.newArrayList() : files.stream().filter(f -> Objects.equals(f.getFileType(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode()))
                        .map(f -> CommonFileVO.builder().fileUrl(f.getFileUrl()).fileName(f.getFileName()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
                d.setCheckFileCount((long) d.getCheckFiles().size());
                d.setReceiptFileCount((long) d.getReceiptFiles().size());
                d.setIsCustomerNameNotSame(null);
                if (!Objects.isNull(d.getCustomerServiceId())) {
                    CCustomerService customerService = customerServiceMap.get(d.getCustomerServiceId());
                    d.setCustomerServiceName(Objects.isNull(customerService) ? "" : customerService.getCustomerName());
                    if (!Objects.isNull(customerService)) {
                        d.setIsCustomerNameNotSame(!Objects.equals(d.getCustomerServiceName(), d.getCustomerName()) ? 1 : null);
                    }
                }
            });
        }
        result.setRecords(data);
        return result;
    }

    private List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailListByIds(List<Long> ids) {
        List<MaterialDeliverInventoryDetailDTO> data = baseMapper.materialDeliverInventoryDetailListByIds(ids);
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d -> {
                d.setAnalysisResultStr(MaterialDeliverAnalysisResult.getByCode(d.getAnalysisResult()).getName());
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
            });
        }
        return data;
    }

    private List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailListByIdsV2(List<Long> ids) {
        List<MaterialFileInventoryV2DTO> data = baseMapper.materialDeliverInventoryDetailListByIdsV2(ids);
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d -> {
                d.setAnalysisResultStr(MaterialDeliverAnalysisResult.getByCode(d.getAnalysisResult()).getName());
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
            });
        }
        return data;
    }

    @Override
    public List<CommonFileVO> materialDeliverInventoryDetailFileList(MaterialDeliverInventoryDetailSearchVO vo) {
        MaterialDeliver materialDeliver = getById(vo.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            return Collections.emptyList();
        }
        return baseMapper.materialDeliverInventoryDetailList(vo, materialDeliver.getMaterialDeliverType(), materialDeliver.getPushStatus())
                .stream().map(row -> CommonFileVO.builder()
                        .id(row.getId())
                        .fileUrl(row.getFileUrl())
                        .fileName(row.getFileName())
                        .fileSize(row.getFileSize())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliverFileInventory> modifyMaterialDeliverFileInventory(MaterialDeliverInventoryDetailModifyVO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "编辑文件";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            modifyFileSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要编辑的数据");
            }
            TCommonOperateDTO<MaterialDeliverFileInventory> result = new TCommonOperateDTO<>();
            List<MaterialDeliverFileInventory> totalList = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                    .eq(MaterialDeliverFileInventory::getIsDel, false)
                    .in(MaterialDeliverFileInventory::getId, vo.getIds())
                    .select(MaterialDeliverFileInventory::getId, MaterialDeliverFileInventory::getMaterialDeliverId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            MaterialDeliver materialDeliver = getById(totalList.get(0).getMaterialDeliverId());
            if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
                throw new ServiceException("交接单不存在");
            }
            List<MaterialDeliverFileInventory> successList = Lists.newArrayList();
            List<MaterialDeliverFileInventory> failList = Lists.newArrayList();
            for (MaterialDeliverFileInventory materialDeliverFileInventory : totalList) {
                successList.add(materialDeliverFileInventory);
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(successList)) {
                List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIds(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                Map<String, Object> operContent = new LinkedHashMap<>();
                LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(MaterialDeliverFileInventory::getId, successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                    // 客户
                    if (!StringUtils.isEmpty(vo.getCustomerName())) {
                        updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, vo.getCustomerName());
                        operContent.put("客户", vo.getCustomerName());
                    }
                    // 银行
                    if (!StringUtils.isEmpty(vo.getBankAccountNumber())) {
                        CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                                .eq(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber()).last("limit 1"));
                        updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, vo.getBankAccountNumber());
                        if (!Objects.isNull(customerServiceBankAccount)) {
                            updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                            CCustomerService customerService = customerServiceMapper.selectById(customerServiceBankAccount.getCustomerServiceId());
                            updateWrapper
                                    .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                                    .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                        } else {
                            updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
                            updateWrapper
                                    .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                                    .set(MaterialDeliverFileInventory::getCreditCode, null);
                        }
                        operContent.put("银行", vo.getBankAccountNumber());
                    }
                    if (!StringUtils.isEmpty(vo.getFileRemark())) {
                        // 文件备注
                        updateWrapper.set(MaterialDeliverFileInventory::getRemark, vo.getFileRemark());
                        operContent.put("文件备注", vo.getFileRemark());
                    }
                    if (!Objects.isNull(vo.getStartDate()) || !Objects.isNull(vo.getEndDate())) {
                        // 开始时间
                        updateWrapper.set(MaterialDeliverFileInventory::getStartDate, vo.getStartDate());
                        operContent.put("开始时间", Objects.isNull(vo.getStartDate()) ? "空" : vo.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        // 结束时间
                        updateWrapper.set(MaterialDeliverFileInventory::getEndDate, vo.getEndDate());
                        operContent.put("结束时间", Objects.isNull(vo.getEndDate()) ? "空" : vo.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                } else {
                    // 客户
                    if (!Objects.isNull(vo.getCustomerServiceId())) {
                        CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
                        if (Objects.isNull(customerService) || customerService.getIsDel()) {
                            throw new ServiceException("客户不存在");
                        }
                        updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, customerService.getCustomerName())
                                .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                                .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                        operContent.put("客户", customerService.getCustomerName());
                    }
                    if (!StringUtils.isEmpty(vo.getFileRemark())) {
                        // 文件备注
                        updateWrapper.set(MaterialDeliverFileInventory::getRemark, vo.getFileRemark());
                        operContent.put("文件备注", vo.getFileRemark());
                    }
                    if (!Objects.isNull(vo.getPeriod())) {
                        // 账期
                        updateWrapper.set(MaterialDeliverFileInventory::getPeriod, vo.getPeriod());
                        operContent.put("账期", vo.getPeriod());
                    }
                }
                materialDeliverFileInventoryService.update(updateWrapper);
                updateById(new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                        .setLastOperType(operType).setLastOperTime(operTime));
                materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatus(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                List<CommonFileVO> files = Lists.newArrayList();
                if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
                    String fileUrl = getMaterialDeliverFileInventoryOperateFileUrl(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
                    if (!StringUtils.isEmpty(fileUrl)) {
                        files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("编辑明细").build());
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                            .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                            .setCreateTime(operTime)
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
            return result;
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliverFileInventory> modifyMaterialDeliverFileInventoryV2(MaterialDeliverInventoryDetailModifyV2VO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "编辑文件";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            modifyFileSingleV2(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要编辑的数据");
            }
            TCommonOperateDTO<MaterialDeliverFileInventory> result = new TCommonOperateDTO<>();
            List<MaterialDeliverFileInventory> totalList = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                    .eq(MaterialDeliverFileInventory::getIsDel, false)
                    .in(MaterialDeliverFileInventory::getId, vo.getIds())
                    .select(MaterialDeliverFileInventory::getId, MaterialDeliverFileInventory::getMaterialDeliverId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            MaterialDeliver materialDeliver = getById(totalList.get(0).getMaterialDeliverId());
            if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
                throw new ServiceException("交接单不存在");
            }
            List<MaterialDeliverFileInventory> successList = Lists.newArrayList();
            List<MaterialDeliverFileInventory> failList = Lists.newArrayList();
            for (MaterialDeliverFileInventory materialDeliverFileInventory : totalList) {
                successList.add(materialDeliverFileInventory);
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(successList)) {
                List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIdsV2(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                Map<String, Object> operContent = new LinkedHashMap<>();
                LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(MaterialDeliverFileInventory::getId, successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
                    // 客户
                    if (!StringUtils.isEmpty(vo.getCustomerName())) {
                        updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, vo.getCustomerName());
                        operContent.put("客户", vo.getCustomerName());
                    }
                    // 银行
                    if (!StringUtils.isEmpty(vo.getBankAccountNumber())) {
                        CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                                .eq(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber()).last("limit 1"));
                        updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, vo.getBankAccountNumber());
                        if (!Objects.isNull(customerServiceBankAccount)) {
                            updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                            CCustomerService customerService = customerServiceMapper.selectById(customerServiceBankAccount.getCustomerServiceId());
                            updateWrapper
                                    .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                                    .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                        } else {
                            updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
                            updateWrapper
                                    .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                                    .set(MaterialDeliverFileInventory::getCreditCode, null);
                        }
                        operContent.put("银行", vo.getBankAccountNumber());
                    }
                    if (!Objects.isNull(vo.getStartDate())) {
                        // 交接账期起
                        updateWrapper.set(MaterialDeliverFileInventory::getStartDate, LocalDate.parse(vo.getStartDate() + "01", DateTimeFormatter.ofPattern("yyyyMMdd")));
                        operContent.put("交接账期起", vo.getStartDate());
                        // 交接账期止
                        updateWrapper.set(MaterialDeliverFileInventory::getEndDate, Objects.isNull(vo.getEndDate()) ? null : LocalDate.parse(vo.getEndDate() + "01", DateTimeFormatter.ofPattern("yyyyMMdd")));
                        operContent.put("交接账期止", Objects.isNull(vo.getEndDate()) ? "空" : vo.getEndDate());
                    }
                } else {
                    // 客户
                    if (!Objects.isNull(vo.getCustomerServiceId())) {
                        CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
                        if (Objects.isNull(customerService) || customerService.getIsDel()) {
                            throw new ServiceException("客户不存在");
                        }
                        updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, customerService.getCustomerName())
                                .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                                .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                        operContent.put("客户", customerService.getCustomerName());
                    }
                    if (!Objects.isNull(vo.getPeriod())) {
                        // 账期
                        updateWrapper.set(MaterialDeliverFileInventory::getPeriod, vo.getPeriod());
                        operContent.put("账期", vo.getPeriod());
                    }
                }
                materialDeliverFileInventoryService.update(updateWrapper);
                updateById(new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                        .setLastOperType(operType).setLastOperTime(operTime));
                materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatusV2(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                List<CommonFileVO> files = Lists.newArrayList();
                if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
                    String fileUrl = getMaterialDeliverFileInventoryOperateFileUrlV2(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
                    if (!StringUtils.isEmpty(fileUrl)) {
                        files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("编辑明细").build());
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                            .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                            .setCreateTime(operTime)
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
            return result;
        }
    }

    private void modifyFileSingleV2(MaterialDeliverInventoryDetailModifyV2VO vo, LocalDateTime operTime, String operType) {
        MaterialDeliverFileInventory materialDeliverFileInventory = materialDeliverFileInventoryService.getById(vo.getId());
        if (Objects.isNull(materialDeliverFileInventory) || materialDeliverFileInventory.getIsDel()) {
            throw new ServiceException("明细不存在");
        }
        MaterialDeliver materialDeliver = getById(materialDeliverFileInventory.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            throw new ServiceException("交接单不存在");
        }
        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
            if (StringUtils.isEmpty(vo.getCustomerName())) {
                throw new ServiceException("客户名不能为空");
            }
            if (StringUtils.isEmpty(vo.getBankAccountNumber())) {
                throw new ServiceException("银行账号不能为空");
            }
            if (Objects.isNull(vo.getStartDate())) {
                throw new ServiceException("交接账期起不能为空");
            }
            if (ObjectUtils.isEmpty(vo.getCheckFiles()) && ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                throw new ServiceException("对账单文件和回单文件不能全为空");
            }
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            if (Objects.isNull(vo.getCustomerServiceId())) {
                throw new ServiceException("客户不能为空");
            }
            if (ObjectUtils.isEmpty(vo.getCheckFiles())) {
                throw new ServiceException("材料文件不能为空");
            }
        } else {
            if (Objects.isNull(vo.getCustomerServiceId())) {
                throw new ServiceException("客户不能为空");
            }
            if (Objects.isNull(vo.getPeriod())) {
                throw new ServiceException("账期不能为空");
            }
            if (ObjectUtils.isEmpty(vo.getCheckFiles())) {
                throw new ServiceException("材料文件不能为空");
            }
        }

        List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIdsV2(Collections.singletonList(vo.getId()));
        Map<String, Object> operContent = new LinkedHashMap<>();
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaterialDeliverFileInventory::getId, vo.getId());
        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
            // 客户
            updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, vo.getCustomerName());
            if (!Objects.equals(materialDeliverFileInventory.getCustomerName(), vo.getCustomerName())) {
                operContent.put("客户", vo.getCustomerName());
            }
            // 银行
            CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                    .eq(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber()).last("limit 1"));
            updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, vo.getBankAccountNumber());
            if (!Objects.isNull(customerServiceBankAccount)) {
                updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                CCustomerService customerService = customerServiceMapper.selectById(customerServiceBankAccount.getCustomerServiceId());
                updateWrapper
                        .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                        .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
            } else {
                updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
                updateWrapper
                        .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                        .set(MaterialDeliverFileInventory::getCreditCode, null);
            }
            if (!Objects.equals(materialDeliverFileInventory.getBankAccountNumber(), vo.getBankAccountNumber())) {
                operContent.put("银行", vo.getBankAccountNumber());
            }
            // 交接账期起
            updateWrapper.set(MaterialDeliverFileInventory::getStartDate, LocalDate.parse(vo.getStartDate() + "01", DateTimeFormatter.ofPattern("yyyyMMdd")));
            if (!Objects.equals((Objects.isNull(materialDeliverFileInventory.getStartDate()) ? "" : materialDeliverFileInventory.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMM"))), vo.getStartDate().toString())) {
                operContent.put("交接账期起", vo.getStartDate());
            }
            // 交接账期止
            updateWrapper.set(MaterialDeliverFileInventory::getEndDate, Objects.isNull(vo.getEndDate()) ? null : LocalDate.parse(vo.getEndDate() + "01", DateTimeFormatter.ofPattern("yyyyMMdd")));
            if (!Objects.equals((Objects.isNull(materialDeliverFileInventory.getEndDate()) ? "" : materialDeliverFileInventory.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMM"))), (Objects.isNull(vo.getEndDate()) ? "" : vo.getEndDate().toString()))) {
                operContent.put("交接账期止", Objects.isNull(vo.getEndDate()) ? "空" : vo.getEndDate());
            }
            materialDeliverFileInventoryFileService.deleteByMaterialDeliverFileInventoryIdAndFileType(vo.getId(), null);
            List<CommonFileVO> checkFiles = vo.getCheckFiles();
            List<CommonFileVO> receiptFiles = vo.getReceiptFiles();
            if (!ObjectUtils.isEmpty(checkFiles)) {
                materialDeliverFileInventoryFileService.saveFiles(vo.getId(), checkFiles, MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE);
                operContent.put("对账单文件", JSONArray.toJSONString(checkFiles));
            } else {
                operContent.put("对账单文件", "无");
            }
            if (!ObjectUtils.isEmpty(receiptFiles)) {
                materialDeliverFileInventoryFileService.saveFiles(vo.getId(), receiptFiles, MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE);
                operContent.put("回单文件", JSONArray.toJSONString(receiptFiles));
            } else {
                operContent.put("回单文件", "无");
            }
        } else if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
            if (Objects.isNull(customerService) || customerService.getIsDel()) {
                throw new ServiceException("客户不存在");
            }
            updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, customerService.getCustomerName())
                    .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                    .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
            if (!Objects.equals(materialDeliverFileInventory.getCustomerName(), vo.getCustomerName())) {
                operContent.put("客户", customerService.getCustomerName());
            }
            materialDeliverFileInventoryFileService.deleteByMaterialDeliverFileInventoryIdAndFileType(vo.getId(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode());
            List<CommonFileVO> checkFiles = vo.getCheckFiles();
            if (!ObjectUtils.isEmpty(checkFiles)) {
                materialDeliverFileInventoryFileService.saveFiles(vo.getId(), checkFiles, MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE);
                operContent.put("材料文件", JSONArray.toJSONString(checkFiles));
            } else {
                operContent.put("材料文件", "无");
            }
        } else {
            // 客户
            CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
            if (Objects.isNull(customerService) || customerService.getIsDel()) {
                throw new ServiceException("客户不存在");
            }
            updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, customerService.getCustomerName())
                    .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                    .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
            if (!Objects.equals(materialDeliverFileInventory.getCustomerName(), vo.getCustomerName())) {
                operContent.put("客户", customerService.getCustomerName());
            }
            // 账期
            updateWrapper.set(MaterialDeliverFileInventory::getPeriod, vo.getPeriod());
            if (!Objects.equals(materialDeliverFileInventory.getPeriod(), vo.getPeriod().toString())) {
                operContent.put("交接账期", vo.getPeriod());
            }
            materialDeliverFileInventoryFileService.deleteByMaterialDeliverFileInventoryIdAndFileType(vo.getId(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode());
            List<CommonFileVO> checkFiles = vo.getCheckFiles();
            if (!ObjectUtils.isEmpty(checkFiles)) {
                materialDeliverFileInventoryFileService.saveFiles(vo.getId(), checkFiles, MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE);
                operContent.put("材料文件", JSONArray.toJSONString(checkFiles));
            } else {
                operContent.put("材料文件", "无");
            }
        }
        materialDeliverFileInventoryService.update(updateWrapper);
        updateById(new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                .setLastOperType(operType).setLastOperTime(operTime));
        materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatusV2(Collections.singletonList(vo.getId()));
        List<CommonFileVO> files = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            String fileUrl = getMaterialDeliverFileInventoryOperateFileUrlV2(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
            if (!StringUtils.isEmpty(fileUrl)) {
                files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("编辑明细").build());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void modifyFileSingle(MaterialDeliverInventoryDetailModifyVO vo, LocalDateTime operTime, String operType) {
        MaterialDeliverFileInventory materialDeliverFileInventory = materialDeliverFileInventoryService.getById(vo.getId());
        if (Objects.isNull(materialDeliverFileInventory) || materialDeliverFileInventory.getIsDel()) {
            throw new ServiceException("文件不存在");
        }
        MaterialDeliver materialDeliver = getById(materialDeliverFileInventory.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            throw new ServiceException("交接单不存在");
        }
        List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIds(Collections.singletonList(vo.getId()));
        Map<String, Object> operContent = new LinkedHashMap<>();
        LambdaUpdateWrapper<MaterialDeliverFileInventory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MaterialDeliverFileInventory::getId, vo.getId());
        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
            // 客户
            updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, vo.getCustomerName());
            operContent.put("客户", StringUtils.isEmpty(vo.getCustomerName()) ? "空" : vo.getCustomerName());

            // 银行
            if (!StringUtils.isEmpty(vo.getBankAccountNumber())) {
                CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .eq(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber()).last("limit 1"));
                updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, vo.getBankAccountNumber());
                if (!Objects.isNull(customerServiceBankAccount)) {
                    updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
                    CCustomerService customerService = customerServiceMapper.selectById(customerServiceBankAccount.getCustomerServiceId());
                    updateWrapper
                            .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                            .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                } else {
                    updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
                    updateWrapper
                            .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                            .set(MaterialDeliverFileInventory::getCreditCode, null);
                }
                operContent.put("银行", vo.getBankAccountNumber());
            } else {
                updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, "");
                updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
                updateWrapper
                        .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                        .set(MaterialDeliverFileInventory::getCreditCode, null);
                operContent.put("银行", "空");
            }
            // 文件备注
            updateWrapper.set(MaterialDeliverFileInventory::getRemark, vo.getFileRemark());
            operContent.put("文件备注", StringUtils.isEmpty(vo.getFileRemark()) ? "空" : vo.getFileRemark());
            // 开始时间
            updateWrapper.set(MaterialDeliverFileInventory::getStartDate, vo.getStartDate());
            operContent.put("开始时间", Objects.isNull(vo.getStartDate()) ? "空" : vo.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 结束时间
            updateWrapper.set(MaterialDeliverFileInventory::getEndDate, vo.getEndDate());
            operContent.put("结束时间", Objects.isNull(vo.getEndDate()) ? "空" : vo.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            // 客户
            if (!Objects.isNull(vo.getCustomerServiceId())) {
                CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
                if (Objects.isNull(customerService) || customerService.getIsDel()) {
                    throw new ServiceException("客户不存在");
                }
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, customerService.getCustomerName())
                        .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
                        .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
                operContent.put("客户", customerService.getCustomerName());
            } else {
                updateWrapper.set(MaterialDeliverFileInventory::getCustomerName, null)
                        .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
                        .set(MaterialDeliverFileInventory::getCreditCode, null);
                operContent.put("客户", "空");
            }
            // 文件备注
            updateWrapper.set(MaterialDeliverFileInventory::getRemark, vo.getFileRemark());
            operContent.put("文件备注", StringUtils.isEmpty(vo.getFileRemark()) ? "空" : vo.getFileRemark());
            // 账期
            updateWrapper.set(MaterialDeliverFileInventory::getPeriod, vo.getPeriod());
            operContent.put("账期", Objects.isNull(vo.getPeriod()) ? "空" : vo.getPeriod());
        }
//        if (!StringUtils.isEmpty(vo.getBankAccountNumber())) {
//            CustomerServiceBankAccount customerServiceBankAccount = customerServiceBankAccountMapper.selectOne(new LambdaQueryWrapper<CustomerServiceBankAccount>()
//                    .eq(CustomerServiceBankAccount::getBankAccountNumber, vo.getBankAccountNumber()).last("limit 1"));
//            updateWrapper.set(MaterialDeliverFileInventory::getBankAccountNumber, vo.getBankAccountNumber());
//            if (!Objects.isNull(customerServiceBankAccount)) {
//                updateWrapper.set(MaterialDeliverFileInventory::getBankName, customerServiceBankAccount.getBankName());
//                CCustomerService customerService = customerServiceMapper.selectById(customerServiceBankAccount.getCustomerServiceId());
//                updateWrapper
//                        .set(MaterialDeliverFileInventory::getCustomerServiceId, customerService.getId())
//                        .set(MaterialDeliverFileInventory::getCreditCode, customerService.getCreditCode());
//            } else {
//                updateWrapper.set(MaterialDeliverFileInventory::getBankName, "");
//                updateWrapper
//                        .set(MaterialDeliverFileInventory::getCustomerServiceId, null)
//                        .set(MaterialDeliverFileInventory::getCreditCode, null);
//            }
//            operContent.put("银行", vo.getBankAccountNumber());
//        }
//        updateWrapper.set(MaterialDeliverFileInventory::getRemark, vo.getFileRemark());
//        operContent.put("文件备注", StringUtils.isEmpty(vo.getFileRemark()) ? "空" : vo.getFileRemark());
//        if (!Objects.isNull(vo.getStartDate())) {
//            updateWrapper.set(MaterialDeliverFileInventory::getStartDate, vo.getStartDate());
//            operContent.put("开始时间", vo.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
//        }
//        updateWrapper.set(MaterialDeliverFileInventory::getEndDate, vo.getEndDate());
//        if (Objects.equals(materialDeliver.getMaterialDeliverType(), MaterialDeliverType.BANK_FLOW.getCode())) {
//            operContent.put("结束时间", Objects.isNull(vo.getEndDate()) ? "空" : vo.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
//        }
//        if (!Objects.isNull(vo.getPeriod())) {
//            updateWrapper.set(MaterialDeliverFileInventory::getPeriod, vo.getPeriod());
//            operContent.put("账期", vo.getPeriod());
//        }
        materialDeliverFileInventoryService.update(updateWrapper);
        updateById(new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                .setLastOperType(operType).setLastOperTime(operTime));
        materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatus(Collections.singletonList(vo.getId()));
        List<CommonFileVO> files = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            String fileUrl = getMaterialDeliverFileInventoryOperateFileUrl(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
            if (!StringUtils.isEmpty(fileUrl)) {
                files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("编辑明细").build());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliverFileInventory> deleteMaterialDeliverFileInventory(MaterialDeliverInventoryDetailModifyVO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "删除明细";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            deleteFileSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要删除的数据");
            }
            TCommonOperateDTO<MaterialDeliverFileInventory> result = new TCommonOperateDTO<>();
            List<MaterialDeliverFileInventory> totalList = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                    .eq(MaterialDeliverFileInventory::getIsDel, false)
                    .in(MaterialDeliverFileInventory::getId, vo.getIds())
                    .select(MaterialDeliverFileInventory::getId, MaterialDeliverFileInventory::getMaterialDeliverId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            MaterialDeliver materialDeliver = getById(totalList.get(0).getMaterialDeliverId());
            if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
                throw new ServiceException("交接单不存在");
            }
            List<MaterialDeliverFileInventory> successList = Lists.newArrayList();
            List<MaterialDeliverFileInventory> failList = Lists.newArrayList();
            for (MaterialDeliverFileInventory materialDeliverFileInventory : totalList) {
                successList.add(materialDeliverFileInventory);
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(successList)) {
                List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIds(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                materialDeliverFileInventoryService.updateBatchById(successList.stream().map(row -> new MaterialDeliverFileInventory()
                        .setId(row.getId())
                        .setIsDel(true)).collect(Collectors.toList()));

                Integer materialDeliverAnalysisResult;
                if (materialDeliverFileInventoryService.count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                        .eq(MaterialDeliverFileInventory::getMaterialDeliverId, successList.get(0).getMaterialDeliverId())
                        .eq(MaterialDeliverFileInventory::getIsDel, false)
                        .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
                    materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
                } else {
                    materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
                }
                MaterialDeliver deliverUpdate = new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                        .setLastOperType(operType).setLastOperTime(operTime);
                if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
                    deliverUpdate.setAnalysisResult(materialDeliverAnalysisResult);
                }
                updateById(deliverUpdate);
                List<CommonFileVO> files = Lists.newArrayList();
                if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
                    String fileUrl = getMaterialDeliverFileInventoryOperateFileUrl(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
                    if (!StringUtils.isEmpty(fileUrl)) {
                        files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("删除明细").build());
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                            .setCreateTime(operTime)
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
            return result;
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<MaterialDeliverFileInventory> deleteMaterialDeliverFileInventoryV2(MaterialDeliverInventoryDetailModifyVO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = iCustomerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "删除明细";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            deleteFileSingleV2(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要删除的数据");
            }
            TCommonOperateDTO<MaterialDeliverFileInventory> result = new TCommonOperateDTO<>();
            List<MaterialDeliverFileInventory> totalList = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                    .eq(MaterialDeliverFileInventory::getIsDel, false)
                    .in(MaterialDeliverFileInventory::getId, vo.getIds())
                    .select(MaterialDeliverFileInventory::getId, MaterialDeliverFileInventory::getMaterialDeliverId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            MaterialDeliver materialDeliver = getById(totalList.get(0).getMaterialDeliverId());
            if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
                throw new ServiceException("交接单不存在");
            }
            List<MaterialDeliverFileInventory> successList = Lists.newArrayList();
            List<MaterialDeliverFileInventory> failList = Lists.newArrayList();
            for (MaterialDeliverFileInventory materialDeliverFileInventory : totalList) {
                successList.add(materialDeliverFileInventory);
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(successList)) {
                List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIdsV2(successList.stream().map(MaterialDeliverFileInventory::getId).collect(Collectors.toList()));
                materialDeliverFileInventoryService.updateBatchById(successList.stream().map(row -> new MaterialDeliverFileInventory()
                        .setId(row.getId())
                        .setIsDel(true)).collect(Collectors.toList()));

                Integer materialDeliverAnalysisResult;
                if (materialDeliverFileInventoryService.count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                        .eq(MaterialDeliverFileInventory::getMaterialDeliverId, successList.get(0).getMaterialDeliverId())
                        .eq(MaterialDeliverFileInventory::getIsDel, false)
                        .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
                    materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
                } else {
                    materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
                }
                MaterialDeliver deliverUpdate = new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                        .setLastOperType(operType).setLastOperTime(operTime);
                if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
                    deliverUpdate.setAnalysisResult(materialDeliverAnalysisResult);
                }
                updateById(deliverUpdate);
                List<CommonFileVO> files = Lists.newArrayList();
                if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
                    String fileUrl = getMaterialDeliverFileInventoryOperateFileUrlV2(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
                    if (!StringUtils.isEmpty(fileUrl)) {
                        files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("删除明细").build());
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                            .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                            .setCreateTime(operTime)
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
            return result;
        }
    }

    private void deleteFileSingleV2(MaterialDeliverInventoryDetailModifyVO vo, LocalDateTime operTime, String operType) {
        MaterialDeliverFileInventory materialDeliverFileInventory = materialDeliverFileInventoryService.getById(vo.getId());
        if (Objects.isNull(materialDeliverFileInventory) || materialDeliverFileInventory.getIsDel()) {
            throw new ServiceException("文件不存在");
        }
        MaterialDeliver materialDeliver = getById(materialDeliverFileInventory.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            throw new ServiceException("交接单不存在");
        }
        List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIdsV2(Collections.singletonList(vo.getId()));
        materialDeliverFileInventoryService.updateById(new MaterialDeliverFileInventory().setId(vo.getId())
                .setIsDel(true));
        Integer materialDeliverAnalysisResult;
        if (materialDeliverFileInventoryService.count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverFileInventory.getMaterialDeliverId())
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
        } else {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
        }
        MaterialDeliver deliverUpdate = new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                .setLastOperType(operType).setLastOperTime(operTime);
        if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
            deliverUpdate.setAnalysisResult(materialDeliverAnalysisResult);
        }
        updateById(deliverUpdate);
        List<CommonFileVO> files = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            String fileUrl = getMaterialDeliverFileInventoryOperateFileUrlV2(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
            if (!StringUtils.isEmpty(fileUrl)) {
                files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("删除明细").build());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void remoteCreateMaterialDeliver(RemoteMaterialDeliver vo) {
        LocalDateTime operTime = LocalDateTime.now();
        MaterialDeliver materialDeliver = new MaterialDeliver();
        BeanUtils.copyProperties(vo, materialDeliver);
        materialDeliver.setMaterialDeliverNumber(createMaterialDeliverNumber(vo.getMaterialDeliverType()));
        materialDeliver.setLastOperType("新建交接单");
        materialDeliver.setLastOperName(vo.getOperName());
        materialDeliver.setLastOperTime(operTime);
        save(materialDeliver);
        if (!ObjectUtils.isEmpty(vo.getFileInventories())) {
            for (RemoteMaterialDeliverFileInventory fileInventory : vo.getFileInventories()) {
                MaterialDeliverFileInventory materialDeliverFileInventory = new MaterialDeliverFileInventory();
                materialDeliverFileInventory.setMaterialDeliverId(materialDeliver.getId());
                materialDeliverFileInventory.setMaterialFileName(fileInventory.getFileName());
                materialDeliverFileInventory.setCustomerName(fileInventory.getCustomerName());
                materialDeliverFileInventory.setCreditCode(fileInventory.getCreditCode());
                materialDeliverFileInventory.setBankName(fileInventory.getBankName());
                materialDeliverFileInventory.setBankAccountNumber(fileInventory.getBankAccountNumber());
                materialDeliverFileInventory.setStartDate(fileInventory.getStartDate());
                materialDeliverFileInventory.setEndDate(fileInventory.getEndDate());
                materialDeliverFileInventory.setPeriod(StringUtils.isEmpty(fileInventory.getPeriod()) ? null : fileInventory.getPeriod());
                materialDeliverFileInventory.setCustomerServiceId(fileInventory.getCustomerServiceId());
                materialDeliverFileInventory.setErrorMsg(fileInventory.getErrorMsg());
                materialDeliverFileInventory.setAnalysisResult(fileInventory.getAnalysisResult());
                materialDeliverFileInventory.setPushResult(fileInventory.getPushResult());
                materialDeliverFileInventoryService.save(materialDeliverFileInventory);
                if (!ObjectUtils.isEmpty(fileInventory.getCheckFiles())) {
                    materialDeliverFileInventoryFileService.saveFiles(materialDeliverFileInventory.getId(), fileInventory.getCheckFiles(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE);
                }
                if (!ObjectUtils.isEmpty(fileInventory.getReceiptFiles())) {
                    materialDeliverFileInventoryFileService.saveFiles(materialDeliverFileInventory.getId(), fileInventory.getReceiptFiles(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE);
                }
                redisService.incrementWithTimeToLive(CacheConstants.BATCH_DELIVER_CONFIRM_DEAL_COUNT + vo.getBatchNo(), 60 * 60L, TimeUnit.SECONDS);
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("解析结果", vo.getAnalysisResultMsg());
        operContent.put("文件清单", JSONObject.toJSONString(vo.getExclFile()));
        operContent.put("文件", JSONObject.toJSONString(vo.getZipFile()));
        if (!Objects.isNull(vo.getErrorFile())) {
            operContent.put("异常文件", JSONObject.toJSONString(vo.getErrorFile()));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType("新建交接单")
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(vo.getCommitUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public String retryAnalysis(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要重试的交接单");
        }
        List<MaterialDeliver> materialDelivers = list(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false)
                .eq(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.WAIT_PUSH.getCode())
                .in(MaterialDeliver::getId, ids));
        if (ObjectUtils.isEmpty(materialDelivers)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        Map<Long, MaterialDeliver> materialDeliverMap = materialDelivers.stream().collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));
        List<MaterialDeliverFileInventory> materialDeliverFileInventories = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .in(MaterialDeliverFileInventory::getMaterialDeliverId, materialDelivers.stream().map(MaterialDeliver::getId).collect(Collectors.toList())));
        if (ObjectUtils.isEmpty(materialDeliverFileInventories)) {
            throw new ServiceException("文件清单为空");
        }
        String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
        Long totalCount = 0L;
        totalCount += materialDeliverFileInventories.size();
        totalCount += materialDelivers.size();
        redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_COUNT + batchNo, totalCount, 60 * 60L, TimeUnit.SECONDS);
        redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_DATA_COUNT + batchNo, materialDeliverFileInventories.size(), 60 * 60L, TimeUnit.SECONDS);
        CompletableFuture.runAsync(() -> {
            materialDeliverFileInventories.forEach(row -> materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatus(row, materialDeliverMap, batchNo));
            materialDelivers.forEach(row -> updateMaterialDeliverStatus(row, batchNo));
            redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_IS_COMPLETE + batchNo, "1", 60 * 60L, TimeUnit.SECONDS);
        });
        return batchNo;
    }

    @Override
    @Transactional
    public String retryAnalysisV2(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要重试的交接单");
        }
        List<MaterialDeliver> materialDelivers = list(new LambdaQueryWrapper<MaterialDeliver>()
                .eq(MaterialDeliver::getIsDel, false)
                .eq(MaterialDeliver::getPushStatus, MaterialDeliverPushStatus.WAIT_PUSH.getCode())
                .in(MaterialDeliver::getId, ids));
        if (ObjectUtils.isEmpty(materialDelivers)) {
            throw new ServiceException("没有符合条件的交接单");
        }
        Map<Long, MaterialDeliver> materialDeliverMap = materialDelivers.stream().collect(Collectors.toMap(MaterialDeliver::getId, Function.identity()));
        List<MaterialDeliverFileInventory> materialDeliverFileInventories = materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .in(MaterialDeliverFileInventory::getMaterialDeliverId, materialDelivers.stream().map(MaterialDeliver::getId).collect(Collectors.toList())));
        if (ObjectUtils.isEmpty(materialDeliverFileInventories)) {
            throw new ServiceException("文件清单为空");
        }
        String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
        Long totalCount = 0L;
        totalCount += materialDeliverFileInventories.size();
        totalCount += materialDelivers.size();
        redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_COUNT + batchNo, totalCount, 60 * 60L, TimeUnit.SECONDS);
        redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_DATA_COUNT + batchNo, materialDeliverFileInventories.size(), 60 * 60L, TimeUnit.SECONDS);
        CompletableFuture.runAsync(() -> {
            materialDeliverFileInventories.forEach(row -> materialDeliverFileInventoryService.updateMaterialDeliverFileInventoryStatusV2(row, materialDeliverMap, batchNo));
            materialDelivers.forEach(row -> updateMaterialDeliverStatus(row, batchNo));
            redisService.setCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_IS_COMPLETE + batchNo, "1", 60 * 60L, TimeUnit.SECONDS);
        });
        return batchNo;
    }

    @Override
    public MaterialRetryAnalysisDTO getRetryAnalysisProgress(String batchNo) {
        Object isComplete = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_IS_COMPLETE + batchNo);
        Object totalCountObj = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_COUNT + batchNo);
        Long totalCount = Objects.isNull(totalCountObj) ? 0L : Long.parseLong(totalCountObj.toString());
        MaterialRetryAnalysisDTO result = new MaterialRetryAnalysisDTO();
        if (Objects.isNull(isComplete)) {
            // 未完成
            Object completeCountObj = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo);
            Long completeCount = Objects.isNull(completeCountObj) ? 0L : Long.parseLong(completeCountObj.toString());
            result.setIsComplete(false);
            result.setTotalCount(totalCount);
            result.setCompleteCount(completeCount);
        } else {
            result.setIsComplete(true);
            Object totalDataCountObj = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_TOTAL_DATA_COUNT + batchNo);
            Long totalDataCount = Objects.isNull(totalDataCountObj) ? 0L : Long.parseLong(totalDataCountObj.toString());
            result.setTotalCount(totalDataCount);
            result.setCompleteCount(totalDataCount);
            Object successCountObj = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_SUCCESS_COUNT + batchNo);
            Long successCount = Objects.isNull(successCountObj) ? 0L : Long.parseLong(successCountObj.toString());
            Object failCountObj = redisService.getCacheObject(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_FAIL_COUNT + batchNo);
            Long failCount = Objects.isNull(failCountObj) ? 0L : Long.parseLong(failCountObj.toString());
            result.setSuccessCount(successCount);
            result.setFailCount(failCount);
        }
        return result;
    }

    @Override
    @Transactional
    public void createAccountingCashier(List<Long> ids, Long deptId) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<MaterialDeliverPeriodInventory> failList = materialDeliverPeriodInventoryService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventory>()
                .eq(MaterialDeliverPeriodInventory::getIsDel, false)
                .in(MaterialDeliverPeriodInventory::getId, ids)
                .eq(MaterialDeliverPeriodInventory::getPushStatus, MaterialDeliverPushResult.FAIL.getCode()));
        if (ObjectUtils.isEmpty(failList)) {
            return;
        }
        materialDeliverPeriodInventoryService.createAccountingCashier(failList, deptId);
    }

    @Override
    @Transactional
    public void createAccountingCashierV2(List<Long> ids, Long deptId) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<MaterialDeliverPeriodInventory> failList = materialDeliverPeriodInventoryService.list(new LambdaQueryWrapper<MaterialDeliverPeriodInventory>()
                .eq(MaterialDeliverPeriodInventory::getIsDel, false)
                .in(MaterialDeliverPeriodInventory::getId, ids)
                .eq(MaterialDeliverPeriodInventory::getPushStatus, MaterialDeliverPushResult.FAIL.getCode()));
        if (ObjectUtils.isEmpty(failList)) {
            return;
        }
        List<AccountingCashierCreateVO> createVOList = materialDeliverPeriodInventoryService.createAccountingCashierV2(failList, deptId);
        if (!ObjectUtils.isEmpty(createVOList)) {
            customerServiceCashierAccountingService.bankReceiptPaperUpload(createVOList);
        }
    }

    @Override
    public List<CommonFileVO> getMaterialFileInventoryFile(Long materialFileInventoryId, Integer fileType) {
        return materialDeliverFileInventoryFileService.getMaterialFileInventoryFile(materialFileInventoryId, fileType);
    }

    private void updateMaterialDeliverStatus(MaterialDeliver materialDeliver, String batchNo) {
        Integer materialDeliverAnalysisResult;
        if (materialDeliverFileInventoryService.count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliver.getId())
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
        } else {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
        }
        if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
            updateById(new MaterialDeliver().setId(materialDeliver.getId()).setAnalysisResult(materialDeliverAnalysisResult));
        }
        redisService.incrementWithTimeToLive(CacheConstants.MATERIAL_DELIVER_RETRY_ANALYSIS_COMPLETE_COUNT + batchNo, 60 * 60, TimeUnit.SECONDS);
    }

    private void deleteFileSingle(MaterialDeliverInventoryDetailModifyVO vo, LocalDateTime operTime, String operType) {
        MaterialDeliverFileInventory materialDeliverFileInventory = materialDeliverFileInventoryService.getById(vo.getId());
        if (Objects.isNull(materialDeliverFileInventory) || materialDeliverFileInventory.getIsDel()) {
            throw new ServiceException("文件不存在");
        }
        MaterialDeliver materialDeliver = getById(materialDeliverFileInventory.getMaterialDeliverId());
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            throw new ServiceException("交接单不存在");
        }
        List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailDTOS = materialDeliverInventoryDetailListByIds(Collections.singletonList(vo.getId()));
        materialDeliverFileInventoryService.updateById(new MaterialDeliverFileInventory().setId(vo.getId())
                .setIsDel(true));
        Integer materialDeliverAnalysisResult;
        if (materialDeliverFileInventoryService.count(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverFileInventory.getMaterialDeliverId())
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .eq(MaterialDeliverFileInventory::getAnalysisResult, MaterialDeliverAnalysisResult.EXCEPTION.getCode())) == 0) {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.NORMAL.getCode();
        } else {
            materialDeliverAnalysisResult = MaterialDeliverAnalysisResult.EXCEPTION.getCode();
        }
        MaterialDeliver deliverUpdate = new MaterialDeliver().setId(materialDeliver.getId()).setLastOperName(vo.getOperName())
                .setLastOperType(operType).setLastOperTime(operTime);
        if (!Objects.equals(materialDeliver.getAnalysisResult(), materialDeliverAnalysisResult)) {
            deliverUpdate.setAnalysisResult(materialDeliverAnalysisResult);
        }
        updateById(deliverUpdate);
        List<CommonFileVO> files = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            String fileUrl = getMaterialDeliverFileInventoryOperateFileUrl(materialDeliverInventoryDetailDTOS, materialDeliver.getMaterialDeliverType());
            if (!StringUtils.isEmpty(fileUrl)) {
                files.add(CommonFileVO.builder().fileUrl(fileUrl).fileName("删除明细").build());
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(materialDeliver.getId())
                    .setBusinessType(BusinessLogBusinessType.MATERIAL_DELIVER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperImages(ObjectUtils.isEmpty(files) ? "" : JSONArray.toJSONString(files))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private String getMaterialDeliverFileInventoryOperateFileUrl(List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailDTOS, Integer materialDeliverType) {
        if (ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            return "";
        }
        if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
            ExcelUtil<MaterialDeliverBankDetailDTO> excelUtil = new ExcelUtil<>(MaterialDeliverBankDetailDTO.class);
            List<MaterialDeliverBankDetailDTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverBankDetailDTO dto = new MaterialDeliverBankDetailDTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "删除明细");
        } else if (Objects.equals(materialDeliverType, MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            ExcelUtil<MaterialDeliverNormalInAccountDetailDTO> excelUtil = new ExcelUtil<>(MaterialDeliverNormalInAccountDetailDTO.class);
            List<MaterialDeliverNormalInAccountDetailDTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverNormalInAccountDetailDTO dto = new MaterialDeliverNormalInAccountDetailDTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "删除明细");
        } else if (Objects.equals(materialDeliverType, MaterialDeliverType.TICKET_IN_ACCOUNT.getCode())) {
            ExcelUtil<MaterialDeliverTicketInAccountDetailDTO> excelUtil = new ExcelUtil<>(MaterialDeliverTicketInAccountDetailDTO.class);
            List<MaterialDeliverTicketInAccountDetailDTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverTicketInAccountDetailDTO dto = new MaterialDeliverTicketInAccountDetailDTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "删除明细");
        } else {
            return "";
        }
    }

    private String getMaterialDeliverFileInventoryOperateFileUrlV2(List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailDTOS, Integer materialDeliverType) {
        if (ObjectUtils.isEmpty(materialDeliverInventoryDetailDTOS)) {
            return "";
        }
        if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
            ExcelUtil<MaterialDeliverBankDetailV2DTO> excelUtil = new ExcelUtil<>(MaterialDeliverBankDetailV2DTO.class);
            List<MaterialDeliverBankDetailV2DTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverBankDetailV2DTO dto = new MaterialDeliverBankDetailV2DTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "明细");
        } else if (Objects.equals(materialDeliverType, MaterialDeliverType.NORMAL_IN_ACCOUNT.getCode())) {
            ExcelUtil<MaterialDeliverNormalInAccountDetailV2DTO> excelUtil = new ExcelUtil<>(MaterialDeliverNormalInAccountDetailV2DTO.class);
            List<MaterialDeliverNormalInAccountDetailV2DTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverNormalInAccountDetailV2DTO dto = new MaterialDeliverNormalInAccountDetailV2DTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "明细");
        } else if (Objects.equals(materialDeliverType, MaterialDeliverType.TICKET_IN_ACCOUNT.getCode())) {
            ExcelUtil<MaterialDeliverTicketInAccountDetailV2DTO> excelUtil = new ExcelUtil<>(MaterialDeliverTicketInAccountDetailV2DTO.class);
            List<MaterialDeliverTicketInAccountDetailV2DTO> export = materialDeliverInventoryDetailDTOS.stream().map(d -> {
                MaterialDeliverTicketInAccountDetailV2DTO dto = new MaterialDeliverTicketInAccountDetailV2DTO();
                BeanUtils.copyProperties(d, dto);
                return dto;
            }).collect(Collectors.toList());
            return excelUtil.exportExcelAndUpload(export, "明细");
        } else {
            return "";
        }
    }

    private MaterialDeliverErrorDTO buildMaterialDeliverDTO(MaterialDeliver materialDeliver, Map<Long, String> deptMap, String errorMessage) {
        MaterialDeliverErrorDTO dto = new MaterialDeliverErrorDTO();
        BeanUtils.copyProperties(materialDeliver, dto);
        dto.setMaterialDeliverTypeStr(MaterialDeliverType.getByCode(dto.getMaterialDeliverType()).getName());
        dto.setMaterialDeliverAnalysisStatusStr(MaterialDeliverAnalysisStatus.getByCode(dto.getMaterialDeliverAnalysisStatus()).getName());
        dto.setMaterialDeliverAnalysisResultStr(Objects.isNull(dto.getMaterialDeliverAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(dto.getMaterialDeliverAnalysisResult()).getName());
        dto.setMaterialDeliverPushStatusStr(Objects.isNull(dto.getMaterialDeliverPushStatus()) ? "-" : MaterialDeliverPushStatus.getByCode(dto.getMaterialDeliverPushStatus()).getName());
        dto.setCommitInfo(deptMap.getOrDefault(dto.getCommitDeptId(), "") + (StringUtils.isEmpty(dto.getCommitUserNickName()) ? "" : ("（" + dto.getCommitUserNickName() + "）")));
        dto.setCreateTimeStr(dto.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        dto.setLastOperTimeStr(Objects.isNull(dto.getLastOperTime()) ? "" : dto.getLastOperTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        dto.setErrorReason(errorMessage);
        return dto;
    }

    private MaterialDeliverErrorDTO buildMaterialDeliverDTO(MaterialDeliver materialDeliver, String errorMessage) {
        SysDept sysDept = remoteDeptService.getDeptInfo(materialDeliver.getCommitDeptId()).getDataThrowException();
        MaterialDeliverErrorDTO dto = new MaterialDeliverErrorDTO();
        BeanUtils.copyProperties(materialDeliver, dto);
        dto.setMaterialDeliverTypeStr(MaterialDeliverType.getByCode(dto.getMaterialDeliverType()).getName());
        dto.setMaterialDeliverAnalysisStatusStr(MaterialDeliverAnalysisStatus.getByCode(dto.getMaterialDeliverAnalysisStatus()).getName());
        dto.setMaterialDeliverAnalysisResultStr(Objects.isNull(dto.getMaterialDeliverAnalysisResult()) ? "-" : MaterialDeliverAnalysisResult.getByCode(dto.getMaterialDeliverAnalysisResult()).getName());
        dto.setMaterialDeliverPushStatusStr(Objects.isNull(dto.getMaterialDeliverPushStatus()) ? "-" : MaterialDeliverPushStatus.getByCode(dto.getMaterialDeliverPushStatus()).getName());
        dto.setCommitInfo((Objects.isNull(sysDept) ? "" : sysDept.getDeptName()) + (StringUtils.isEmpty(dto.getCommitUserNickName()) ? "" : ("（" + dto.getCommitUserNickName() + "）")));
        dto.setCreateTimeStr(dto.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        dto.setLastOperTimeStr(Objects.isNull(dto.getLastOperTime()) ? "" : dto.getLastOperTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        dto.setErrorReason(errorMessage);
        return dto;
    }

    private MaterialDeliver checkMaterialDeliverExists(Long id) {
        MaterialDeliver materialDeliver = getById(id);
        if (Objects.isNull(materialDeliver) || materialDeliver.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        return materialDeliver;
    }

    private String createMaterialDeliverNumber(Integer materialDeliverType) {
        if (Objects.equals(materialDeliverType, MaterialDeliverType.BANK_FLOW.getCode())) {
            return "BANK" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        } else if (Objects.equals(materialDeliverType, MaterialDeliverType.TICKET_IN_ACCOUNT.getCode())) {
            return "ACCOT" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        } else {
            return "ACCON" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        }
    }
}
