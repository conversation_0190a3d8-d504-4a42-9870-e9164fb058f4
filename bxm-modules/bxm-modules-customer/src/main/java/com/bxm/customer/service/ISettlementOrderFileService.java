package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.SettlementOrderFile;

import java.util.List;

/**
 * 结算单附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface ISettlementOrderFileService extends IService<SettlementOrderFile>
{
    /**
     * 查询结算单附件
     * 
     * @param id 结算单附件主键
     * @return 结算单附件
     */
    public SettlementOrderFile selectSettlementOrderFileById(Long id);

    /**
     * 查询结算单附件列表
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结算单附件集合
     */
    public List<SettlementOrderFile> selectSettlementOrderFileList(SettlementOrderFile settlementOrderFile);

    /**
     * 新增结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    public int insertSettlementOrderFile(SettlementOrderFile settlementOrderFile);

    /**
     * 修改结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    public int updateSettlementOrderFile(SettlementOrderFile settlementOrderFile);

    /**
     * 批量删除结算单附件
     * 
     * @param ids 需要删除的结算单附件主键集合
     * @return 结果
     */
    public int deleteSettlementOrderFileByIds(Long[] ids);

    /**
     * 删除结算单附件信息
     * 
     * @param id 结算单附件主键
     * @return 结果
     */
    public int deleteSettlementOrderFileById(Long id);

    List<SettlementOrderFile> selectBySettlementOrderIdAndFileType(Long settlementOrderId, Integer fileType);

    void removeAndSaveNewBySettlementOrderIdAndFileType(Long settlementOrderId, List<CommonFileVO> files, Integer fileType);
}
