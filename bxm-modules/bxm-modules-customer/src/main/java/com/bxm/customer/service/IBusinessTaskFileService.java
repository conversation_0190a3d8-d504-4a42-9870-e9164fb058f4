package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.businessTask.BusinessTaskFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.BusinessTaskFile;

import java.util.List;
import java.util.Map;

/**
 * 业务任务的附件Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IBusinessTaskFileService extends IService<BusinessTaskFile> {
    /**
     * 查询业务任务的附件
     *
     * @param id 业务任务的附件主键
     * @return 业务任务的附件
     */
    public BusinessTaskFile selectBusinessTaskFileById(Long id);

    /**
     * 查询业务任务的附件列表
     *
     * @param businessTaskFile 业务任务的附件
     * @return 业务任务的附件集合
     */
    public List<BusinessTaskFile> selectBusinessTaskFileList(BusinessTaskFile businessTaskFile);

    /**
     * 新增业务任务的附件
     *
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    public int insertBusinessTaskFile(BusinessTaskFile businessTaskFile);

    /**
     * 修改业务任务的附件
     *
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    public int updateBusinessTaskFile(BusinessTaskFile businessTaskFile);

    /**
     * 批量删除业务任务的附件
     *
     * @param ids 需要删除的业务任务的附件主键集合
     * @return 结果
     */
    public int deleteBusinessTaskFileByIds(Long[] ids);

    /**
     * 删除业务任务的附件信息
     *
     * @param id 业务任务的附件主键
     * @return 结果
     */
    public int deleteBusinessTaskFileById(Long id);

    //****** start self method ******
    List<BusinessTaskFile> selectBatchByMainIdsAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes);

    Map<Long, Map<Integer, List<BusinessTaskFile>>> selectMapBatchByMainIdsAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes);

    //List<BusinessTaskFile> selectByMainIdAndFileTypes(Long mainId, List<BusinessTaskFileType> fileTypes);

    //Map<Integer, List<BusinessTaskFile>> selectMapByMainIdAndFileTypes(Long mainId, List<BusinessTaskFileType> fileTypes);

    Map<Long, Map<String, List<BusinessTaskFile>>> selectMapSubKeyByMainIdAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes);

    void deleteByMainIdAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes);

    void saveFile(Long mainId, List<CommonFileVO> files, BusinessTaskFileType fileType, String subFileType);

    List<CommonFileVO> covToCommonFileVO(List<BusinessTaskFile> files);
}
