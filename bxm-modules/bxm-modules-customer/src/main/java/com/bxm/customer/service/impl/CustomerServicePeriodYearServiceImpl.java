package com.bxm.customer.service.impl;

import java.time.LocalDateTime;
import java.util.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.FullYearClosing;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.RemoteCustomerServicePeriodYearSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServicePeriodYearVO;
import com.bxm.customer.domain.vo.CustomerServicePeriodYearVO;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysEmployee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServicePeriodYearMapper;
import com.bxm.customer.domain.CustomerServicePeriodYear;
import com.bxm.customer.service.ICustomerServicePeriodYearService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 客户服务年度账期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Service
@Slf4j
public class CustomerServicePeriodYearServiceImpl extends ServiceImpl<CustomerServicePeriodYearMapper, CustomerServicePeriodYear> implements ICustomerServicePeriodYearService
{
    @Autowired
    private CustomerServicePeriodYearMapper customerServicePeriodYearMapper;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    /**
     * 查询客户服务年度账期
     *
     * @param id 客户服务年度账期主键
     * @return 客户服务年度账期
     */
    @Override
    public CustomerServicePeriodYear selectCustomerServicePeriodYearById(Long id)
    {
        return customerServicePeriodYearMapper.selectCustomerServicePeriodYearById(id);
    }

    /**
     * 查询客户服务年度账期列表
     *
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 客户服务年度账期
     */
    @Override
    public List<CustomerServicePeriodYear> selectCustomerServicePeriodYearList(CustomerServicePeriodYear customerServicePeriodYear)
    {
        return customerServicePeriodYearMapper.selectCustomerServicePeriodYearList(customerServicePeriodYear);
    }

    /**
     * 新增客户服务年度账期
     *
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    @Override
    public int insertCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear)
    {
        customerServicePeriodYear.setCreateTime(DateUtils.getNowDate());
        return customerServicePeriodYearMapper.insertCustomerServicePeriodYear(customerServicePeriodYear);
    }

    /**
     * 修改客户服务年度账期
     *
     * @param customerServicePeriodYear 客户服务年度账期
     * @return 结果
     */
    @Override
    public int updateCustomerServicePeriodYear(CustomerServicePeriodYear customerServicePeriodYear)
    {
        customerServicePeriodYear.setUpdateTime(DateUtils.getNowDate());
        return customerServicePeriodYearMapper.updateCustomerServicePeriodYear(customerServicePeriodYear);
    }

    /**
     * 批量删除客户服务年度账期
     *
     * @param ids 需要删除的客户服务年度账期主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodYearByIds(Long[] ids)
    {
        return customerServicePeriodYearMapper.deleteCustomerServicePeriodYearByIds(ids);
    }

    /**
     * 删除客户服务年度账期信息
     *
     * @param id 客户服务年度账期主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodYearById(Long id)
    {
        return customerServicePeriodYearMapper.deleteCustomerServicePeriodYearById(id);
    }

    @Override
    @Async
    public void addCustomerServicePeriodYear(Long customerServiceId, List<Integer> yearPeriodList) {
        if (ObjectUtils.isEmpty(yearPeriodList)) {
            return;
        }
        if (Objects.isNull(customerServiceId)) {
            return;
        }
        yearPeriodList.forEach(yearPeriod -> {
            if (count(new LambdaQueryWrapper<CustomerServicePeriodYear>()
                    .eq(CustomerServicePeriodYear::getCustomerServiceId, customerServiceId)
                    .eq(CustomerServicePeriodYear::getPeriod, yearPeriod)) == 0) {
                CustomerServicePeriodYear customerServicePeriodYear = new CustomerServicePeriodYear();
                customerServicePeriodYear.setCustomerServiceId(customerServiceId);
                customerServicePeriodYear.setPeriod(yearPeriod);
                customerServicePeriodYear.setPeriodLastMonth(yearPeriod * 100 + 12);
                save(customerServicePeriodYear);
            }
        });
    }

    @Override
    @Transactional
    @Async
    public void saveCustomerServicePeriodYear(List<CustomerServicePeriodYear> yearPeriodList) {
        if (ObjectUtils.isEmpty(yearPeriodList)) {
            return;
        }
        yearPeriodList.forEach(yearPeriod -> {
            if (count(new LambdaQueryWrapper<CustomerServicePeriodYear>()
                    .eq(CustomerServicePeriodYear::getCustomerServiceId, yearPeriod.getCustomerServiceId())
                    .eq(CustomerServicePeriodYear::getPeriod, yearPeriod.getPeriod())) == 0) {
                save(yearPeriod);
            }
        });
    }

    @Override
    public void updateYearPeriodList(List<CustomerServicePeriodYear> updateYearPeriods) {
        if (ObjectUtils.isEmpty(updateYearPeriods)) {
            return;
        }
    }

    @Override
    @Transactional
    public void deleteYearPeriodList(List<Long> deleteYearPeriods) {
        if (ObjectUtils.isEmpty(deleteYearPeriods)) {
            return;
        }
        removeByIds(deleteYearPeriods);
    }

    @Override
    public List<CustomerServicePeriodYear> selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodYear>().eq(CustomerServicePeriodYear::getCustomerServiceId, customerServiceId)
                .orderByAsc(CustomerServicePeriodYear::getPeriod));
    }

    @Override
    public Integer selectCountByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period) {
        if (Objects.isNull(customerServiceId) || Objects.isNull(period)) {
            return 0;
        }
        return count(new LambdaQueryWrapper<CustomerServicePeriodYear>()
                .eq(CustomerServicePeriodYear::getPeriod, period)
                .eq(CustomerServicePeriodYear::getCustomerServiceId, customerServiceId));
    }

    @Override
    @Transactional
    public void modifyCustomerServicePeriodYear(CustomerServicePeriodYearVO vo) {
        Long userId = SecurityUtils.getUserId();
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        LocalDateTime operTime = LocalDateTime.now();
        updateCustomerServicePeriodYear(vo, operName, operTime, userId, deptId);
    }

    private void updateCustomerServicePeriodYear(CustomerServicePeriodYearVO vo, String operName, LocalDateTime operTime, Long userId, Long deptId) {
        CustomerServicePeriodYear yearPeriod = getById(vo.getId());
        if (Objects.isNull(yearPeriod)) {
            throw new ServiceException("年度账期不存在");
        }
        updateById(new CustomerServicePeriodYear().setId(vo.getId())
                .setLastYearDeductible(vo.getLastYearDeductible())
                .setPriorYearEstimationNotReversed(vo.getPriorYearEstimationNotReversed())
                .setPriorYearDepreciationAdjustment(vo.getPriorYearDepreciationAdjustment())
                .setPriorYearExpenseIncrease(vo.getPriorYearExpenseIncrease())
                .setFullYearClosing(vo.getFullYearClosing()));
        Map<String, String> operContent = new HashMap<>();
        if (!StringUtils.isEmpty(vo.getLastYearDeductible())) {
            operContent.put("上年可弥补损益", vo.getLastYearDeductible());
        }
        if (!StringUtils.isEmpty(vo.getPriorYearEstimationNotReversed())) {
            operContent.put("上年暂估未冲", vo.getPriorYearEstimationNotReversed());
        }
        if (!StringUtils.isEmpty(vo.getPriorYearDepreciationAdjustment())) {
            operContent.put("上年折旧调整", vo.getPriorYearDepreciationAdjustment());
        }
        if (!StringUtils.isEmpty(vo.getPriorYearExpenseIncrease())) {
            operContent.put("本年费用调增", vo.getPriorYearExpenseIncrease());
        }
        if (!Objects.isNull(vo.getFullYearClosing())) {
            operContent.put("全年结账", FullYearClosing.getByCode(vo.getFullYearClosing()).getDesc());
        }
        operContent.put("年份", yearPeriod.getPeriod().toString());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(yearPeriod.getCustomerServiceId())
                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                    .setDeptId(deptId)
                    .setOperType("编辑年度信息")
                    .setOperName(operName)
                    .setCreateTime(operTime)
                    .setOperUserId(userId)
                    .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    public List<CustomerServicePeriodYear> getCustomerServicePeriodYearByCustomerServiceIdsAndPeriod(RemoteCustomerServicePeriodYearSearchVO vo) {
        if (ObjectUtils.isEmpty(vo.getCustomerServiceIds())) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodYear>()
                .eq(CustomerServicePeriodYear::getPeriod, vo.getPeriod())
                .in(CustomerServicePeriodYear::getCustomerServiceId, vo.getCustomerServiceIds())
                .select(CustomerServicePeriodYear::getId, CustomerServicePeriodYear::getCustomerServiceId));
    }

    @Override
    @Transactional
    public void remoteUpdateCustomerServicePeriodYear(RemoteCustomerServicePeriodYearVO vo) {
        if (Objects.isNull(vo.getId())) {
            return;
        }
        LocalDateTime operTime = LocalDateTime.now();
        updateCustomerServicePeriodYear(CustomerServicePeriodYearVO.builder()
                .id(vo.getId())
                .lastYearDeductible(vo.getLastYearDeductible())
                .priorYearExpenseIncrease(vo.getPriorYearExpenseIncrease())
                .priorYearDepreciationAdjustment(vo.getPriorYearDepreciationAdjustment())
                .priorYearEstimationNotReversed(vo.getPriorYearEstimationNotReversed())
                .fullYearClosing(vo.getFullYearClosing())
                .remark(vo.getRemark())
                .files(vo.getFiles())
                .build(), vo.getOperName(), operTime, vo.getUserId(), vo.getDeptId());
    }
}
