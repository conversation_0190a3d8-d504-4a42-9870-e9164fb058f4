package com.bxm.customer.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.MaterialDeliverPeriodInventory;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverPeriodInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPeriodInventoryDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPushPreviewDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialPushPreviewListDTO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierCreateVO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverPeriodInventorySearchVO;

/**
 * 材料交接单账期清单Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IMaterialDeliverPeriodInventoryService extends IService<MaterialDeliverPeriodInventory>
{
    /**
     * 查询材料交接单账期清单
     * 
     * @param id 材料交接单账期清单主键
     * @return 材料交接单账期清单
     */
    public MaterialDeliverPeriodInventory selectMaterialDeliverPeriodInventoryById(Long id);

    /**
     * 查询材料交接单账期清单列表
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 材料交接单账期清单集合
     */
    public List<MaterialDeliverPeriodInventory> selectMaterialDeliverPeriodInventoryList(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 新增材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    public int insertMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 修改材料交接单账期清单
     * 
     * @param materialDeliverPeriodInventory 材料交接单账期清单
     * @return 结果
     */
    public int updateMaterialDeliverPeriodInventory(MaterialDeliverPeriodInventory materialDeliverPeriodInventory);

    /**
     * 批量删除材料交接单账期清单
     * 
     * @param ids 需要删除的材料交接单账期清单主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverPeriodInventoryByIds(Long[] ids);

    /**
     * 删除材料交接单账期清单信息
     * 
     * @param id 材料交接单账期清单主键
     * @return 结果
     */
    public int deleteMaterialDeliverPeriodInventoryById(Long id);

    IPage<MaterialPeriodInventoryDTO> materialPeriodInventoryPageList(Long materialDeliverId, Integer pageNum, Integer pageSize);

    List<CommonFileVO> getMaterialPeriodInventoryFile(Long materialPeriodInventoryId);

    void deleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds);

    void logicDeleteByBatchMaterialDeliverIds(List<Long> materialDeliverIds);

    MaterialPushPreviewDTO materialPushPreview(List<MaterialDeliver> waitPushList);

    List<MaterialPushPreviewListDTO> getPushReviewErrorList(String batchNo);

    void confirmPush(String batchNo, Long deptId);

    List<AccountingCashierCreateVO> confirmPushV2(String batchNo, Long deptId);

    IPage<MaterialDeliverPeriodInventoryDTO> periodInventoryPageList(MaterialDeliverPeriodInventorySearchVO vo, Long deptId);

    void createAccountingCashier(List<MaterialDeliverPeriodInventory> failList, Long deptId);

    List<AccountingCashierCreateVO> createAccountingCashierV2(List<MaterialDeliverPeriodInventory> failList, Long deptId);

    MaterialPushPreviewDTO materialPushPreviewV2(List<MaterialDeliver> waitPushList);
}
