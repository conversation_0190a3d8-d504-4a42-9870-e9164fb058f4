package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceRepairAccountTempTaxInstrumentMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument;
import com.bxm.customer.service.ICustomerServiceRepairAccountTempTaxInstrumentService;

/**
 * 补账临时的 材料交接票据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class CustomerServiceRepairAccountTempTaxInstrumentServiceImpl extends ServiceImpl<CustomerServiceRepairAccountTempTaxInstrumentMapper, CustomerServiceRepairAccountTempTaxInstrument> implements ICustomerServiceRepairAccountTempTaxInstrumentService
{
    @Autowired
    private CustomerServiceRepairAccountTempTaxInstrumentMapper customerServiceRepairAccountTempTaxInstrumentMapper;

    /**
     * 查询补账临时的 材料交接票据
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 补账临时的 材料交接票据
     */
    @Override
    public CustomerServiceRepairAccountTempTaxInstrument selectCustomerServiceRepairAccountTempTaxInstrumentById(Long id)
    {
        return customerServiceRepairAccountTempTaxInstrumentMapper.selectCustomerServiceRepairAccountTempTaxInstrumentById(id);
    }

    /**
     * 查询补账临时的 材料交接票据列表
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 补账临时的 材料交接票据
     */
    @Override
    public List<CustomerServiceRepairAccountTempTaxInstrument> selectCustomerServiceRepairAccountTempTaxInstrumentList(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        return customerServiceRepairAccountTempTaxInstrumentMapper.selectCustomerServiceRepairAccountTempTaxInstrumentList(customerServiceRepairAccountTempTaxInstrument);
    }

    /**
     * 新增补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    @Override
    public int insertCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        customerServiceRepairAccountTempTaxInstrument.setCreateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountTempTaxInstrumentMapper.insertCustomerServiceRepairAccountTempTaxInstrument(customerServiceRepairAccountTempTaxInstrument);
    }

    /**
     * 修改补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    @Override
    public int updateCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument)
    {
        customerServiceRepairAccountTempTaxInstrument.setUpdateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountTempTaxInstrumentMapper.updateCustomerServiceRepairAccountTempTaxInstrument(customerServiceRepairAccountTempTaxInstrument);
    }

    /**
     * 批量删除补账临时的 材料交接票据
     * 
     * @param ids 需要删除的补账临时的 材料交接票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentByIds(Long[] ids)
    {
        return customerServiceRepairAccountTempTaxInstrumentMapper.deleteCustomerServiceRepairAccountTempTaxInstrumentByIds(ids);
    }

    /**
     * 删除补账临时的 材料交接票据信息
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentById(Long id)
    {
        return customerServiceRepairAccountTempTaxInstrumentMapper.deleteCustomerServiceRepairAccountTempTaxInstrumentById(id);
    }
}
