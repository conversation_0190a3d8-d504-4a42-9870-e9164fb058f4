package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.api.domain.dto.RemoteCustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.inAccount.*;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.CommonInAccountVO;
import com.bxm.customer.domain.vo.inAccount.*;

import java.util.List;

/**
 * 入账、入账交付Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceInAccountService extends IService<CustomerServiceInAccount> {
    /**
     * 查询入账、入账交付
     *
     * @param id 入账、入账交付主键
     * @return 入账、入账交付
     */
    public CustomerServiceInAccount selectCustomerServiceInAccountById(Long id);

    /**
     * 查询入账、入账交付列表
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 入账、入账交付集合
     */
    public List<CustomerServiceInAccount> selectCustomerServiceInAccountList(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 新增入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    public int insertCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 修改入账、入账交付
     *
     * @param customerServiceInAccount 入账、入账交付
     * @return 结果
     */
    public int updateCustomerServiceInAccount(CustomerServiceInAccount customerServiceInAccount);

    /**
     * 批量删除入账、入账交付
     *
     * @param ids 需要删除的入账、入账交付主键集合
     * @return 结果
     */
    public int deleteCustomerServiceInAccountByIds(Long[] ids);

    /**
     * 删除入账、入账交付信息
     *
     * @param id 入账、入账交付主键
     * @return 结果
     */
    public int deleteCustomerServiceInAccountById(Long id);

    //****** start self method ******  批量操作:
    //分页获取入账交付
    IPage<InAccountDTO> inAccountList(Long deptId, InAccountVO vo);

    //获取 编辑入账交付 的回显内容
    InAccountUpdateShowDTO getInAccountUpdateShow(Long id);

    //获取 编辑入账交付 的回显内容-V2
    InAccountUpdateShowV2DTO getInAccountUpdateShowV2(Long id);

    //编辑入账交付
    void updateInAccount(Long deptId, UpdateInAccountVO vo);

    Integer updateInAccountV2(Long deptId, UpdateInAccountV2VO vo);

    Integer updateInAccountV2Inner(Long deptId, UpdateInAccountV2VO vo);

    //提供给WEB端用
    //禅道：581
    Integer updateInAccountV2_refactoring(Long deptId, UpdateInAccountV2VO vo);

    //内部用
    //禅道：581
    Integer updateInAccountV2Inner_refactoring(Long deptId, UpdateInAccountV2VO vo);

    Integer updateInAccountV3(Long deptId, UpdateInAccountV3VO vo);

    //内部用
    Integer updateInAccountV3Inner(Long deptId, UpdateInAccountV3VO vo);

    //获取入账交付详情
    InAccountDetailDTO getInAccountDetail(Long id);

    //批量入账、批量结账
    TCommonOperateDTO<CustomerServiceInAccount> inAccountInBatch(Long deptId, BatchOperateAccountInVO vo);

    //获取RPA更新的回显内容
    InAccountRpaUpdateShowDTO getInAccountRpaUpdateShow(Long id);

    //批量RPA更新
    TCommonOperateDTO<CustomerServiceInAccount> inAccountRpaUpdateBatch(Long deptId, BatchOperateInAccountRpaUpdateVO vo);

    //单个更新，给内部调用
    Integer inAccountRpaUpdateInner(Long deptId, OperateInAccountRpaUpdateVO vo);

    // 根据客户id列表查询 当年最后一个有结账时间的入账交付单数据
    //最后结账月的定义改了：
    //https://jxwf25.axshare.com/?id=1bcko4&p=%E5%AE%A2%E6%88%B7%E5%88%97%E8%A1%A8%E5%8F%96%E6%95%B0&g=1
    //取数逻辑调整（最后结账月）：
    //第一步：定位到账期，取最近一个以下字段任一有值的账期，结账时间/本年累计主营收入/本年累计主营成本/本年累计会计利润/个税申报人数/本年个税申报工资总额
    //第二步：以该账期为最后结账月，同时显示为该账期对应的字段值，没有显示-
    List<CustomerServiceInAccount> selectLastInAccountByCustomerIds(List<Long> customerServiceIds);

    List<CustomerServiceInAccount> selectBatchByCustomerId(List<Long> customerServiceIds);

    //****** start self method 提供给龙总内部调用 ******
    //从账期生成入账交付单
    void addInAccountFromPeriod(CCustomerService cCustomerService, CustomerServicePeriodMonth customerServicePeriodMonth);

    //删除入账交付单
    void softDeleteCustomerServiceInAccountFromRepairAccount(Long customerServiceId, List<Integer> periods);

    //历史数据处理：历史账期生成对应入账交付单
    //每次都是找历史上还没有生成账期的入账交付单
    void addInAccountForHistory();

    //客户服务的账期，没入账交接单的账期，生成入账交付单
    void addInAccountForHistoryByCustomerService(Long customerServiceId);

    void addInAccountForHistoryByCustomerServiceV2(List<CCustomerService> customerServices);

    // 根据账期id查询入账交付单
    CustomerServiceInAccount getByPeriodId(Long customerServicePeriodMonthId);

    CommonIdsSearchVO wholeLevelSearchV2(Integer wholeLevel);

    List<CustomerServiceInAccount> getByPeriodIdList(List<Long> periodIds);

    void updateByCommonNotice(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceInAccount customerServiceInAccount);

    void updateByCommonNoticeV2(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceInAccount customerServiceInAccount);

    void rpaInAccountTask();

    List<RemoteCustomerInAccountMaxPeriodDTO> getCustomerInAccountMaxPeriod(List<Long> customerServiceIds);

    boolean checkInAccountIsEnd(Long customerServiceId, Integer period);

    void urgeInAccount(Long inAccountId, Long userId, Long deptId);

    List<RemoteCustomerInAccountMaxPeriodDTO> getCustomerInAccountGroupRelation(List<Long> customerServiceIds);
}
