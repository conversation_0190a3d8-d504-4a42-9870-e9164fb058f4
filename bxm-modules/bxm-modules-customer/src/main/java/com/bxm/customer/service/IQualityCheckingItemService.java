package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.QualityCheckingItem;

/**
 * 质检项目配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IQualityCheckingItemService extends IService<QualityCheckingItem>
{
    /**
     * 查询质检项目配置
     * 
     * @param id 质检项目配置主键
     * @return 质检项目配置
     */
    public QualityCheckingItem selectQualityCheckingItemById(Long id);

    /**
     * 查询质检项目配置列表
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 质检项目配置集合
     */
    public List<QualityCheckingItem> selectQualityCheckingItemList(QualityCheckingItem qualityCheckingItem);

    /**
     * 新增质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    public int insertQualityCheckingItem(QualityCheckingItem qualityCheckingItem);

    /**
     * 修改质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    public int updateQualityCheckingItem(QualityCheckingItem qualityCheckingItem);

    /**
     * 批量删除质检项目配置
     * 
     * @param ids 需要删除的质检项目配置主键集合
     * @return 结果
     */
    public int deleteQualityCheckingItemByIds(Long[] ids);

    /**
     * 删除质检项目配置信息
     * 
     * @param id 质检项目配置主键
     * @return 结果
     */
    public int deleteQualityCheckingItemById(Long id);
}
