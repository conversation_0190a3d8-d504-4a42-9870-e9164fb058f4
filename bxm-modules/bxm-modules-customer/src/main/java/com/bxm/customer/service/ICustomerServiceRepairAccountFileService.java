package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.repairAccount.RepairAccountFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceRepairAccountFile;

import java.util.List;
import java.util.Map;

/**
 * 补账 附件Service接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceRepairAccountFileService extends IService<CustomerServiceRepairAccountFile> {
    /**
     * 查询补账 附件
     *
     * @param id 补账 附件主键
     * @return 补账 附件
     */
    public CustomerServiceRepairAccountFile selectCustomerServiceRepairAccountFileById(Long id);

    /**
     * 查询补账 附件列表
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 补账 附件集合
     */
    public List<CustomerServiceRepairAccountFile> selectCustomerServiceRepairAccountFileList(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 新增补账 附件
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 修改补账 附件
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile);

    /**
     * 批量删除补账 附件
     *
     * @param ids 需要删除的补账 附件主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountFileByIds(Long[] ids);

    /**
     * 删除补账 附件信息
     *
     * @param id 补账 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountFileById(Long id);

    //****** start self method ******

    //根据 补账ID 获取附件
    List<CustomerServiceRepairAccountFile> selectByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes);

    //根据 补账ID 获取附件，并根据类型转成MAP
    Map<Integer, List<CustomerServiceRepairAccountFile>> selectMapByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes);

    //根据 补账ID 获取附件，MAP，key是主类型+子类型
    Map<String, List<CustomerServiceRepairAccountFile>> selectMapSubKeyByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes);

    //根据 补账ID 删除附件
    void deleteByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes);

    //保存附件
    void saveFile(Long customerServiceRepairAccountId, List<CommonFileVO> files, RepairAccountFileType repairAccountFileType, String subFileType);

    //原始附件处理成前端可用数据
    List<CommonFileVO> covToCommonFileVO(List<CustomerServiceRepairAccountFile> files);
}
