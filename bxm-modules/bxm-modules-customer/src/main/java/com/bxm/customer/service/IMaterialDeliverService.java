package com.bxm.customer.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.vo.RemoteMaterialDeliver;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.MaterialDeliverFileInventory;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.materialDeliver.*;
import com.bxm.customer.domain.vo.materialDeliver.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;

/**
 * 材料交接单Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
public interface IMaterialDeliverService extends IService<MaterialDeliver>
{
    /**
     * 查询材料交接单
     * 
     * @param id 材料交接单主键
     * @return 材料交接单
     */
    public MaterialDeliver selectMaterialDeliverById(Long id);

    /**
     * 查询材料交接单列表
     * 
     * @param materialDeliver 材料交接单
     * @return 材料交接单集合
     */
    public List<MaterialDeliver> selectMaterialDeliverList(MaterialDeliver materialDeliver);

    /**
     * 新增材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    public int insertMaterialDeliver(MaterialDeliver materialDeliver);

    /**
     * 修改材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    public int updateMaterialDeliver(MaterialDeliver materialDeliver);

    /**
     * 批量删除材料交接单
     * 
     * @param ids 需要删除的材料交接单主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverByIds(Long[] ids);

    /**
     * 删除材料交接单信息
     * 
     * @param id 材料交接单主键
     * @return 结果
     */
    public int deleteMaterialDeliverById(Long id);

    IPage<MaterialDeliverDTO> materialDeliverList(MaterialDeliverSearchVO vo, Long deptId);

    MaterialDeliverDetailDTO materialDeliverDetail(Long id);

    IPage<MaterialFileInventoryDTO> materialFileInventoryPageList(Long id, Integer pageNum, Integer pageSize);

    IPage<MaterialFileInventoryV2DTO> materialFileInventoryPageListV2(Long id, Integer pageNum, Integer pageSize);

    List<CommonFileVO> materialFileInventoryList(Long id);

    IPage<MaterialPeriodInventoryDTO> materialPeriodInventoryPageList(Long id, Integer pageNum, Integer pageSize);

    List<CommonFileVO> getMaterialPeriodInventoryFile(Long materialPeriodInventoryId);

    TCommonOperateDTO<MaterialDeliver> deleteMaterialDeliver(List<Long> ids, Long deptId);

    TCommonOperateDTO<MaterialDeliver> stopMaterialDeliverAnalysis(List<Long> ids, Long deptId);

    List<MaterialDeliver> selectBatchByIds(List<Long> ids);

    MaterialPushPreviewDTO materialPushPreview(List<Long> ids);

    MaterialPushPreviewDTO materialPushPreviewV2(List<Long> ids);

    List<MaterialPushPreviewListDTO> getPushReviewErrorList(String batchNo);

    void confirmPush(String batchNo, Long deptId);

    void confirmPushV2(String batchNo, Long deptId);

    IPage<MaterialDeliverPeriodInventoryDTO> periodInventoryPageList(MaterialDeliverPeriodInventorySearchVO vo, Long deptId);

    List<CommonDeptCountDTO> getMaterialDeliverDeptSelectList(UserDeptDTO userDeptDTO, Integer materialDeliverType, Integer materialDeliverAnalysisStatus, Integer materialDeliverAnalysisResult, Integer materialDeliverPushStatus);

    IPage<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailPageList(MaterialDeliverInventoryDetailSearchVO vo, Long deptId);

    IPage<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailPageListV2(MaterialDeliverInventoryDetailSearchVO vo, Long deptId);

    List<CommonFileVO> materialDeliverInventoryDetailFileList(MaterialDeliverInventoryDetailSearchVO vo);

    TCommonOperateDTO<MaterialDeliverFileInventory> modifyMaterialDeliverFileInventory(MaterialDeliverInventoryDetailModifyVO vo, Long deptId);

    TCommonOperateDTO<MaterialDeliverFileInventory> modifyMaterialDeliverFileInventoryV2(MaterialDeliverInventoryDetailModifyV2VO vo, Long deptId);

    TCommonOperateDTO<MaterialDeliverFileInventory> deleteMaterialDeliverFileInventory(MaterialDeliverInventoryDetailModifyVO vo, Long deptId);

    TCommonOperateDTO<MaterialDeliverFileInventory> deleteMaterialDeliverFileInventoryV2(MaterialDeliverInventoryDetailModifyVO vo, Long deptId);

    void remoteCreateMaterialDeliver(RemoteMaterialDeliver vo);

    String retryAnalysis(List<Long> ids);

    String retryAnalysisV2(List<Long> ids);

    MaterialRetryAnalysisDTO getRetryAnalysisProgress(String batchNo);

    void createAccountingCashier(List<Long> ids, Long deptId);

    void createAccountingCashierV2(List<Long> ids, Long deptId);

    List<CommonFileVO> getMaterialFileInventoryFile(Long materialFileInventoryId, Integer fileType);
}
