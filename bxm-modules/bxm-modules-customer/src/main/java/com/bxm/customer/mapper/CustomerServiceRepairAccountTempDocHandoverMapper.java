package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempDocHandover;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 补账的临时材料交接Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface CustomerServiceRepairAccountTempDocHandoverMapper extends BaseMapper<CustomerServiceRepairAccountTempDocHandover> {
    /**
     * 查询补账的临时材料交接
     *
     * @param id 补账的临时材料交接主键
     * @return 补账的临时材料交接
     */
    public CustomerServiceRepairAccountTempDocHandover selectCustomerServiceRepairAccountTempDocHandoverById(Long id);

    /**
     * 查询补账的临时材料交接列表
     *
     * @param CustomerServiceRepairAccountTempDocHandover 补账的临时材料交接
     * @return 补账的临时材料交接集合
     */
    public List<CustomerServiceRepairAccountTempDocHandover> selectCustomerServiceRepairAccountTempDocHandoverList(CustomerServiceRepairAccountTempDocHandover CustomerServiceRepairAccountTempDocHandover);

    /**
     * 新增补账的临时材料交接
     *
     * @param CustomerServiceRepairAccountTempDocHandover 补账的临时材料交接
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountTempDocHandover(CustomerServiceRepairAccountTempDocHandover CustomerServiceRepairAccountTempDocHandover);

    /**
     * 修改补账的临时材料交接
     *
     * @param CustomerServiceRepairAccountTempDocHandover 补账的临时材料交接
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountTempDocHandover(CustomerServiceRepairAccountTempDocHandover CustomerServiceRepairAccountTempDocHandover);

    /**
     * 删除补账的临时材料交接
     *
     * @param id 补账的临时材料交接主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempDocHandoverById(Long id);

    /**
     * 批量删除补账的临时材料交接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempDocHandoverByIds(Long[] ids);

    void insertCustomerServiceRepairAccountTempDocHandoverWithBatchNum(CustomerServiceRepairAccountTempDocHandover CustomerServiceRepairAccountTempDocHandover);
}
