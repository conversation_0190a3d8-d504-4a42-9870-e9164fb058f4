package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.QualityCheckingFile;

/**
 * 质检附件Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IQualityCheckingFileService extends IService<QualityCheckingFile>
{
    /**
     * 查询质检附件
     * 
     * @param id 质检附件主键
     * @return 质检附件
     */
    public QualityCheckingFile selectQualityCheckingFileById(Long id);

    /**
     * 查询质检附件列表
     * 
     * @param qualityCheckingFile 质检附件
     * @return 质检附件集合
     */
    public List<QualityCheckingFile> selectQualityCheckingFileList(QualityCheckingFile qualityCheckingFile);

    /**
     * 新增质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    public int insertQualityCheckingFile(QualityCheckingFile qualityCheckingFile);

    /**
     * 修改质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    public int updateQualityCheckingFile(QualityCheckingFile qualityCheckingFile);

    /**
     * 批量删除质检附件
     * 
     * @param ids 需要删除的质检附件主键集合
     * @return 结果
     */
    public int deleteQualityCheckingFileByIds(Long[] ids);

    /**
     * 删除质检附件信息
     * 
     * @param id 质检附件主键
     * @return 结果
     */
    public int deleteQualityCheckingFileById(Long id);

    Map<Long, List<CommonFileVO>> getBatchByBusinessIdAndBusinessType(Integer businessType, List<Long> businessIds);

    List<CommonFileVO> getByBusinessIdAndBusinessType(Integer businessType, Long businessId, Integer fileType);

    void removeAndSaveNewFiles(Integer businessType, Long businessId, List<CommonFileVO> files, Integer fileType);

    void removeAndSaveNewFiles(Integer businessType, List<Long> businessIds, List<CommonFileVO> files, Integer fileType);
}
