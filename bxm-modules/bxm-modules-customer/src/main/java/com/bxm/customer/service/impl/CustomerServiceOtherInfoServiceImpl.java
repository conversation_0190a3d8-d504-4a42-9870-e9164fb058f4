package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceOtherInfoMapper;
import com.bxm.customer.domain.CustomerServiceOtherInfo;
import com.bxm.customer.service.ICustomerServiceOtherInfoService;

/**
 * 客户服务其他信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceOtherInfoServiceImpl extends ServiceImpl<CustomerServiceOtherInfoMapper, CustomerServiceOtherInfo> implements ICustomerServiceOtherInfoService
{
    @Autowired
    private CustomerServiceOtherInfoMapper customerServiceOtherInfoMapper;

    /**
     * 查询客户服务其他信息
     * 
     * @param id 客户服务其他信息主键
     * @return 客户服务其他信息
     */
    @Override
    public CustomerServiceOtherInfo selectCustomerServiceOtherInfoById(Long id)
    {
        return customerServiceOtherInfoMapper.selectCustomerServiceOtherInfoById(id);
    }

    /**
     * 查询客户服务其他信息列表
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 客户服务其他信息
     */
    @Override
    public List<CustomerServiceOtherInfo> selectCustomerServiceOtherInfoList(CustomerServiceOtherInfo customerServiceOtherInfo)
    {
        return customerServiceOtherInfoMapper.selectCustomerServiceOtherInfoList(customerServiceOtherInfo);
    }

    /**
     * 新增客户服务其他信息
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceOtherInfo(CustomerServiceOtherInfo customerServiceOtherInfo)
    {
        return customerServiceOtherInfoMapper.insertCustomerServiceOtherInfo(customerServiceOtherInfo);
    }

    /**
     * 修改客户服务其他信息
     * 
     * @param customerServiceOtherInfo 客户服务其他信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceOtherInfo(CustomerServiceOtherInfo customerServiceOtherInfo)
    {
        return customerServiceOtherInfoMapper.updateCustomerServiceOtherInfo(customerServiceOtherInfo);
    }

    /**
     * 批量删除客户服务其他信息
     * 
     * @param ids 需要删除的客户服务其他信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceOtherInfoByIds(Long[] ids)
    {
        return customerServiceOtherInfoMapper.deleteCustomerServiceOtherInfoByIds(ids);
    }

    /**
     * 删除客户服务其他信息信息
     * 
     * @param id 客户服务其他信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceOtherInfoById(Long id)
    {
        return customerServiceOtherInfoMapper.deleteCustomerServiceOtherInfoById(id);
    }
}
