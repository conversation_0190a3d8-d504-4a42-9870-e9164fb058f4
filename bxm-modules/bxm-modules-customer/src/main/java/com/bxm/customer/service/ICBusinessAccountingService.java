package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.AccountingBusinessType;
import com.bxm.customer.domain.CBusinessAccounting;

import java.util.List;

/**
 * 会计Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface ICBusinessAccountingService extends IService<CBusinessAccounting>
{
    /**
     * 查询会计
     * 
     * @param id 会计主键
     * @return 会计
     */
    public CBusinessAccounting selectCBusinessAccountingById(Long id);

    /**
     * 查询会计列表
     * 
     * @param cBusinessAccounting 会计
     * @return 会计集合
     */
    public List<CBusinessAccounting> selectCBusinessAccountingList(CBusinessAccounting cBusinessAccounting);

    /**
     * 新增会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    public int insertCBusinessAccounting(CBusinessAccounting cBusinessAccounting);

    /**
     * 修改会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    public int updateCBusinessAccounting(CBusinessAccounting cBusinessAccounting);

    /**
     * 批量删除会计
     * 
     * @param ids 需要删除的会计主键集合
     * @return 结果
     */
    public int deleteCBusinessAccountingByIds(Long[] ids);

    /**
     * 删除会计信息
     * 
     * @param id 会计主键
     * @return 结果
     */
    public int deleteCBusinessAccountingById(Long id);

    Long getAccountingByBusinessIdAndBusinessType(Long businessId, AccountingBusinessType accountingBusinessType);
}
