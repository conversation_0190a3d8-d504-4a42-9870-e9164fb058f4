package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceOperate;
import com.bxm.customer.domain.vo.customerServiceOperate.AddCustomerServiceOperateVO;

/**
 * 客户服务操作记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ICustomerServiceOperateService extends IService<CustomerServiceOperate>
{
    /**
     * 查询客户服务操作记录
     * 
     * @param id 客户服务操作记录主键
     * @return 客户服务操作记录
     */
    public CustomerServiceOperate selectCustomerServiceOperateById(Long id);

    /**
     * 查询客户服务操作记录列表
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 客户服务操作记录集合
     */
    public List<CustomerServiceOperate> selectCustomerServiceOperateList(CustomerServiceOperate customerServiceOperate);

    /**
     * 新增客户服务操作记录
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 结果
     */
    public int insertCustomerServiceOperate(CustomerServiceOperate customerServiceOperate);

    /**
     * 修改客户服务操作记录
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 结果
     */
    public int updateCustomerServiceOperate(CustomerServiceOperate customerServiceOperate);

    /**
     * 批量删除客户服务操作记录
     * 
     * @param ids 需要删除的客户服务操作记录主键集合
     * @return 结果
     */
    public int deleteCustomerServiceOperateByIds(Long[] ids);

    /**
     * 删除客户服务操作记录信息
     * 
     * @param id 客户服务操作记录主键
     * @return 结果
     */
    public int deleteCustomerServiceOperateById(Long id);

    //****** start self method ******
    //记录操作
    void addSimple(AddCustomerServiceOperateVO vo);

    //记录操作-批量
    void addSimpleBatch(List<AddCustomerServiceOperateVO> vo);
}
