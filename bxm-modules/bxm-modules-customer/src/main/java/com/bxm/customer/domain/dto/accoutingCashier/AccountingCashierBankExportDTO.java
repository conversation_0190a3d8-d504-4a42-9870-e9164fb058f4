package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierBankExportDTO {

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("银行名称")
    @Excel(name = "银行")
    private String bankName;

    @ApiModelProperty("银行账号")
    @Excel(name = "银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("服务纳税人性质")
    @Excel(name = "服务纳税人性质")
    private String customerServiceTaxTypeStr;

    @ApiModelProperty("客户标签")
    @Excel(name = "服务标签")
    private String customerServiceTagNames;

    @ApiModelProperty("账期纳税人性质")
    @Excel(name = "账期纳税人性质")
    private String periodTaxTypeStr;

    @ApiModelProperty("账期标签")
    @Excel(name = "账期标签")
    private String periodTagNames;

    @ApiModelProperty("服务业务公司名称")
    @Excel(name = "服务业务公司")
    private String customerServiceBusinessDeptName;

    @ApiModelProperty("服务会计区域名称")
    @Excel(name = "服务会计区域")
    private String customerServiceAccountingTopDeptName;

    @ApiModelProperty("服务顾问")
    @Excel(name = "服务顾问")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty("服务会计")
    @Excel(name = "服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty("账期会计")
    @Excel(name = "账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty("是否有银行流水（文案）")
    @Excel(name = "银行流水")
    private String hasBankPaymentStr;

    @ApiModelProperty("交付要求")
    @Excel(name = "交付要求")
    private String deliverRequire;

    @ApiModelProperty("材料介质（文案）")
    @Excel(name = "材料介质")
    private String materialMediaStr;

    @ApiModelProperty("介质材料数量")
    @Excel(name = "材料数")
    private Long materialMediaFileCount;

    @ApiModelProperty("交付状态名称")
    @Excel(name = "交付状态")
    private String deliverStatusStr;

    @ApiModelProperty("交付结果名称")
    @Excel(name = "交付结果")
    private String deliverResultStr;

    @ApiModelProperty("完成时间（格式化后的）")
    @Excel(name = "完成时间")
    private String completeTimeStr;

    @Excel(name = "ddl")
    private String ddlStr;

    @ApiModelProperty("交付备注")
    @Excel(name = "交付备注")
    private String deliverRemark;

    @ApiModelProperty("交付附件数")
    @Excel(name = "交付附件数")
    private Long deliverFileCount;

    @ApiModelProperty("材料完整度（文案）")
    @Excel(name = "材料完整度")
    private String materialIntegrityStr;

    @Excel(name = "最后操作人")
    private String lastOperName;

    @Excel(name = "最后操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOperTime;

    @Excel(name = "最后操作类型")
    private String lastOperType;

    @Excel(name = "最后操作备注")
    private String lastOperRemark;

    @Excel(name = "创建人")
    private String createBy;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Excel(name = "最后完成时间", dateFormat = "yyyy-MM-dd")
    private LocalDate lastInTime;
}
