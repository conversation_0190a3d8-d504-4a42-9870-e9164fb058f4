package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeSearchVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeVO;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.domain.dto.CustomerServicePeriodMonthIncomeDetailDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户服务账期收入Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-13
 */
public interface ICustomerServicePeriodMonthIncomeService extends IService<CustomerServicePeriodMonthIncome>
{
    /**
     * 查询客户服务账期收入
     * 
     * @param id 客户服务账期收入主键
     * @return 客户服务账期收入
     */
    public CustomerServicePeriodMonthIncome selectCustomerServicePeriodMonthIncomeById(Long id);

    /**
     * 查询客户服务账期收入列表
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 客户服务账期收入集合
     */
    public List<CustomerServicePeriodMonthIncome> selectCustomerServicePeriodMonthIncomeList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 新增客户服务账期收入
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    public int insertCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 修改客户服务账期收入
     * 
     * @param customerServicePeriodMonthIncome 客户服务账期收入
     * @return 结果
     */
    public int updateCustomerServicePeriodMonthIncome(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome);

    /**
     * 批量删除客户服务账期收入
     * 
     * @param ids 需要删除的客户服务账期收入主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthIncomeByIds(Long[] ids);

    /**
     * 删除客户服务账期收入信息
     * 
     * @param id 客户服务账期收入主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthIncomeById(Long id);

    IPage<CustomerServicePeriodMonthIncome> incomeList(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome, Long deptId, Integer pageNum, Integer pageSize);

    List<CustomerServicePeriodMonthIncome> selectByCustomerServiceId(Long customerServiceId);

    List<CustomerServicePeriodMonthIncome> selectBatchByCustomerServiceId(List<Long> serviceIds);

    void addPeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId);

    void addPeriodMonthIncomeInner(CustomerServicePeriodMonthIncome income, String operator, Long deptId);

    void modifyPeriodMonthIncome(CustomerServicePeriodMonthIncome income, Long deptId);

    void modifyPeriodMonthIncomeInner(CustomerServicePeriodMonthIncome income, String operator, Long deptId);

    void modifyPeriodMonthIncomeXqy(CustomerServicePeriodMonthIncome income, String operator, Long deptId);

    Boolean checkPeriodMonthIncomeExists(Long customerServiceId, Integer period, Long id);

    void updateCustomerServiceIncome(CustomerServicePeriodMonthIncome income);

    void updateCustomerServiceIncome(Long customerServiceId);

    void batchUpdateCustomerServiceIncome(List<Long> customerServiceIds, Integer period);

    List<CommonFileVO> getIncomeFiles(Long id);

    CustomerServicePeriodMonthIncomeDetailDTO detail(Long id);

    List<CustomerServicePeriodMonthIncome> getCustomerServiceIncomeByCustomerServiceIdAndPeriod(List<RemoteCustomerServiceIncomeSearchVO> voList);

    void remoteUpdateOrCreateIncome(RemoteCustomerServiceIncomeVO vo);

    void remoteUpdateCustomerIncome(List<Long> customerServiceIds);

    void createIncomeByStartAndEnd(Long customerServiceId, Integer startPeriod, Integer endPeriod);

    void addPeriodMonthIncomeInputOutput(CustomerServicePeriodMonthIncome income, Long userId, String createBy, String sourceName);

    void modifyPeriodMonthIncomeInputOutput(CustomerServicePeriodMonthIncome update, Long userId, String createBy, String sourceName);

    void updateIncomeNoTicketIncome(Long customerServiceId, Integer period, BigDecimal nationalTaxNoTicketIncome, Long deptId, Long userId, String operName, LocalDateTime operTime);

    List<CommonFileVO> buildFiles(CustomerServicePeriodMonthIncome customerServicePeriodMonthIncome, List<CustomerServicePeriodMonthIncome> list);
}
