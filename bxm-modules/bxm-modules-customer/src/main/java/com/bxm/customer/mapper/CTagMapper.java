package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CTag;

/**
 * 标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface CTagMapper extends BaseMapper<CTag>
{
    /**
     * 查询标签
     * 
     * @param id 标签主键
     * @return 标签
     */
    public CTag selectCTagById(Long id);

    /**
     * 查询标签列表
     * 
     * @param cTag 标签
     * @return 标签集合
     */
    public List<CTag> selectCTagList(CTag cTag);

    /**
     * 新增标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    public int insertCTag(CTag cTag);

    /**
     * 修改标签
     * 
     * @param cTag 标签
     * @return 结果
     */
    public int updateCTag(CTag cTag);

    /**
     * 删除标签
     * 
     * @param id 标签主键
     * @return 结果
     */
    public int deleteCTagById(Long id);

    /**
     * 批量删除标签
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCTagByIds(Long[] ids);
}
