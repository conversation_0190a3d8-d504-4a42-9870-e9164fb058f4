package com.bxm.customer.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.customer.domain.BorrowOrder;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDTO;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDetailDTO;
import com.bxm.customer.domain.dto.workBench.BorrowOrderWorkBenchDTO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderSearchVO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderVO;

/**
 * 借阅单Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
public interface IBorrowOrderService extends IService<BorrowOrder>
{
    /**
     * 查询借阅单
     * 
     * @param id 借阅单主键
     * @return 借阅单
     */
    public BorrowOrder selectBorrowOrderById(Long id);

    /**
     * 查询借阅单列表
     * 
     * @param borrowOrder 借阅单
     * @return 借阅单集合
     */
    public List<BorrowOrder> selectBorrowOrderList(BorrowOrder borrowOrder);

    /**
     * 新增借阅单
     * 
     * @param borrowOrder 借阅单
     * @return 结果
     */
    public int insertBorrowOrder(BorrowOrder borrowOrder);

    /**
     * 修改借阅单
     * 
     * @param borrowOrder 借阅单
     * @return 结果
     */
    public int updateBorrowOrder(BorrowOrder borrowOrder);

    /**
     * 批量删除借阅单
     * 
     * @param ids 需要删除的借阅单主键集合
     * @return 结果
     */
    public int deleteBorrowOrderByIds(Long[] ids);

    /**
     * 删除借阅单信息
     * 
     * @param id 借阅单主键
     * @return 结果
     */
    public int deleteBorrowOrderById(Long id);

    IPage<BorrowOrderDTO> borrowOrderList(BorrowOrderSearchVO vo, Long deptId);

    BorrowOrderDetailDTO borrowOrderDetail(Long id);

    void createBorrowOrder(BorrowOrderVO vo, Long deptId);

    void modifyBorrowOrder(BorrowOrderVO vo, Long deptId);

    CommonOperateResultDTO submit(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO outStation(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO reBack(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO returnBorrow(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO check(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO dispatch(CommonIdVO vo, Long deptId);

    CommonOperateResultDTO delete(CommonIdVO vo, Long deptId);

    Integer selectBorrowOrderCount();

    List<BorrowOrder> selectByIds(List<Long> ids);

    BorrowOrderWorkBenchDTO borrowOrderStatistic(Long deptId);
}
