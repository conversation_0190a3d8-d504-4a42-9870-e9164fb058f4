package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.domain.dto.accoutingCashier.CustomerServicePeriodMonthAccountingCashierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 18:59
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceMonthPeriodV2DTO {
    @ApiModelProperty(value = "账期id")
    private Long id;

    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中，确认后这里是针对客户服务状态值的筛选，不是对账期状态值的筛选。最新沟通：取账期的状态，且只有 1=服务中/正常,3=冻结中/冻结")
    private Integer serviceStatus;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中，确认后这里是针对客户服务状态值的筛选，不是对账期状态值的筛选。最新沟通：取账期的状态，且只有 1=服务中/正常,3=冻结中/冻结")
    private String serviceStatusStr;

    @ApiModelProperty(value = "全量开票金额")
    private BigDecimal allTicketAmount;

    @ApiModelProperty(value = "全量开票金额")
    private String allTicketAmountStr;

    @ApiModelProperty(value = "预认证")
    private CustomerServiceMonthPeriodItemDTO preAuth;

    @ApiModelProperty(value = "医保")
    private CustomerServiceMonthPeriodItemDTO medicalInsurance;

    @ApiModelProperty(value = "社保")
    private CustomerServiceMonthPeriodItemDTO socialInsurance;

    @ApiModelProperty(value = "个税（工资薪金）")
    private CustomerServiceMonthPeriodItemDTO tax;

    @ApiModelProperty(value = "国税")
    private CustomerServiceMonthPeriodItemDTO nationalTax;

    @ApiModelProperty(value = "个税（经营所得）")
    private CustomerServiceMonthPeriodItemDTO taxOperating;

    @ApiModelProperty("服务类型，1-代账，2-补账")
    private Integer serviceType;

    @ApiModelProperty("服务类型名称")
    private String serviceTypeStr;

    @ApiModelProperty("材料数量")
    @Excel(name = "材料")
    private Long materialCount;

    @ApiModelProperty("银行流水状态，若有链接，需要调用接口/bxmCustomer/accountingCashier/bankPaymentByPeriodId获取银行流水列表")
    private CustomerServicePeriodMonthAccountingCashierDTO bankPaymentAccountingCashier;

    @ApiModelProperty(hidden = true)
    private Integer bankPaymentResult;

    @ApiModelProperty("入账状态，若有链接，直接根据账务id跳转详情")
    private CustomerServicePeriodMonthAccountingCashierDTO inAccountingCashier;

    @ApiModelProperty(hidden = true)
    private Integer inAccountStatus;

    @ApiModelProperty("结账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountStatus;

    @ApiModelProperty("银行流水结果文案")
    private String bankPaymentResultStr;

    @ApiModelProperty("入账状态文案")
    private String inAccountStatusStr;

    @ApiModelProperty("结账")
    private String settleAccountStatusStr;

    //账期不存在的none数据
    public static CustomerServiceMonthPeriodV2DTO nonePeriod(Long customerServiceId, Integer period, CustomerServicePeriodMonthIncome income) {
        return CustomerServiceMonthPeriodV2DTO.builder()
                .allTicketAmount(income == null ? null : income.getAllTicketAmount())
                .allTicketAmountStr("-")
                .customerServiceId(customerServiceId)
                .id(null)
                .medicalInsurance(CustomerServiceMonthPeriodItemDTO.none())
                .nationalTax(CustomerServiceMonthPeriodItemDTO.none())
                .period(period)
                .preAuth(CustomerServiceMonthPeriodItemDTO.none())
                .socialInsurance(CustomerServiceMonthPeriodItemDTO.none())
                .tax(CustomerServiceMonthPeriodItemDTO.none())
                .taxOperating(CustomerServiceMonthPeriodItemDTO.none())
                .serviceType(null)
                .serviceTypeStr(null)
                .materialCount(0L)
                .bankPaymentAccountingCashier(null)
                .bankPaymentResult(null)
                .bankPaymentResultStr(null)
                .inAccountingCashier(null)
                .inAccountStatus(null)
                .inAccountStatusStr(null)
                .settleAccountStatus(null)
                .build();
    }
}
