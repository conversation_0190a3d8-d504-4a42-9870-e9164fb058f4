package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiSyncRecord;

/**
 * 第三方申报同步Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IOpenApiSyncRecordService extends IService<OpenApiSyncRecord>
{
    /**
     * 查询第三方申报同步
     * 
     * @param id 第三方申报同步主键
     * @return 第三方申报同步
     */
    public OpenApiSyncRecord selectOpenApiSyncRecordById(Long id);

    /**
     * 查询第三方申报同步列表
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 第三方申报同步集合
     */
    public List<OpenApiSyncRecord> selectOpenApiSyncRecordList(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 新增第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    public int insertOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 修改第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    public int updateOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 批量删除第三方申报同步
     * 
     * @param ids 需要删除的第三方申报同步主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncRecordByIds(Long[] ids);

    /**
     * 删除第三方申报同步信息
     * 
     * @param id 第三方申报同步主键
     * @return 结果
     */
    public int deleteOpenApiSyncRecordById(Long id);
}
