package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.WorkOrderTypeDeptRelation;

/**
 * 工单类型组织可见Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Mapper
public interface WorkOrderTypeDeptRelationMapper extends BaseMapper<WorkOrderTypeDeptRelation>
{
    /**
     * 查询工单类型组织可见
     * 
     * @param id 工单类型组织可见主键
     * @return 工单类型组织可见
     */
    public WorkOrderTypeDeptRelation selectWorkOrderTypeDeptRelationById(Long id);

    /**
     * 查询工单类型组织可见列表
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 工单类型组织可见集合
     */
    public List<WorkOrderTypeDeptRelation> selectWorkOrderTypeDeptRelationList(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 新增工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    public int insertWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 修改工单类型组织可见
     * 
     * @param workOrderTypeDeptRelation 工单类型组织可见
     * @return 结果
     */
    public int updateWorkOrderTypeDeptRelation(WorkOrderTypeDeptRelation workOrderTypeDeptRelation);

    /**
     * 删除工单类型组织可见
     * 
     * @param id 工单类型组织可见主键
     * @return 结果
     */
    public int deleteWorkOrderTypeDeptRelationById(Long id);

    /**
     * 批量删除工单类型组织可见
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderTypeDeptRelationByIds(Long[] ids);
}
