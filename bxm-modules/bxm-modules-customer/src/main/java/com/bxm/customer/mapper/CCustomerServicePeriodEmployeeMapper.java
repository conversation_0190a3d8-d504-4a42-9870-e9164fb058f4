package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CCustomerServicePeriodEmployee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 账期服务人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Mapper
public interface CCustomerServicePeriodEmployeeMapper extends BaseMapper<CCustomerServicePeriodEmployee>
{
    /**
     * 查询账期服务人员
     * 
     * @param id 账期服务人员主键
     * @return 账期服务人员
     */
    public CCustomerServicePeriodEmployee selectCCustomerServicePeriodEmployeeById(Long id);

    /**
     * 查询账期服务人员列表
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 账期服务人员集合
     */
    public List<CCustomerServicePeriodEmployee> selectCCustomerServicePeriodEmployeeList(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 新增账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    public int insertCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 修改账期服务人员
     * 
     * @param cCustomerServicePeriodEmployee 账期服务人员
     * @return 结果
     */
    public int updateCCustomerServicePeriodEmployee(CCustomerServicePeriodEmployee cCustomerServicePeriodEmployee);

    /**
     * 删除账期服务人员
     * 
     * @param id 账期服务人员主键
     * @return 结果
     */
    public int deleteCCustomerServicePeriodEmployeeById(Long id);

    /**
     * 批量删除账期服务人员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCCustomerServicePeriodEmployeeByIds(Long[] ids);

    void saveNewPeriodAdvisorEmployee(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodAccountingEmployee(@Param("nowPeriod") Integer nowPeriod);

    void saveNewPeriodAdvisorEmployeeByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                         @Param("periodStart") Integer periodStart,
                                                         @Param("periodEnd") Integer periodEnd);

    void saveNewPeriodAccountingEmployeeByCustomerServiceIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                                                            @Param("periodStart") Integer periodStart,
                                                            @Param("periodEnd") Integer periodEnd);
}
