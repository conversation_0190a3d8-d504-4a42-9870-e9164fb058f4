package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerOtherInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferSettlementPaymentInfoDTO;

/**
 * 新户流转其他信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerOtherInfoService extends IService<NewCustomerOtherInfo>
{
    /**
     * 查询新户流转其他信息
     * 
     * @param id 新户流转其他信息主键
     * @return 新户流转其他信息
     */
    public NewCustomerOtherInfo selectNewCustomerOtherInfoById(Long id);

    /**
     * 查询新户流转其他信息列表
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 新户流转其他信息集合
     */
    public List<NewCustomerOtherInfo> selectNewCustomerOtherInfoList(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 新增新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    public int insertNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 修改新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    public int updateNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 批量删除新户流转其他信息
     * 
     * @param ids 需要删除的新户流转其他信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerOtherInfoByIds(Long[] ids);

    /**
     * 删除新户流转其他信息信息
     * 
     * @param id 新户流转其他信息主键
     * @return 结果
     */
    public int deleteNewCustomerOtherInfoById(Long id);

    NewCustomerOtherInfo selectByCustomerId(Long customerId);

    void updateByOtherInfo(Long customerId, NewCustomerTransferSettlementPaymentInfoDTO settlementPaymentInfo);

    Map<Long, NewCustomerOtherInfo> selectMapByCustomerIds(List<Long> newCustomerTransferIds);
}
