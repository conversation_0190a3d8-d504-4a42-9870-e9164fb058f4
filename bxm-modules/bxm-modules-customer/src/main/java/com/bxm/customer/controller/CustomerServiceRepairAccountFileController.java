package com.bxm.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceRepairAccountFile;
import com.bxm.customer.service.ICustomerServiceRepairAccountFileService;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.page.TableDataInfo;

/**
 * 补账 附件Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/repairAccountFile")
@Api(tags = "补账 附件")
public class CustomerServiceRepairAccountFileController extends BaseController
{
    @Autowired
    private ICustomerServiceRepairAccountFileService customerServiceRepairAccountFileService;

    /**
     * 查询补账 附件列表
     */
    @RequiresPermissions("customer:file:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询补账 附件列表", notes = "查询补账 附件列表")
    public TableDataInfo list(CustomerServiceRepairAccountFile customerServiceRepairAccountFile)
    {
        startPage();
        List<CustomerServiceRepairAccountFile> list = customerServiceRepairAccountFileService.selectCustomerServiceRepairAccountFileList(customerServiceRepairAccountFile);
        return getDataTable(list);
    }

    /**
     * 导出补账 附件列表
     */
    @RequiresPermissions("customer:file:export")
    @Log(title = "补账 附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出补账 附件列表", notes = "导出补账 附件列表")
    public void export(HttpServletResponse response, CustomerServiceRepairAccountFile customerServiceRepairAccountFile)
    {
        List<CustomerServiceRepairAccountFile> list = customerServiceRepairAccountFileService.selectCustomerServiceRepairAccountFileList(customerServiceRepairAccountFile);
        ExcelUtil<CustomerServiceRepairAccountFile> util = new ExcelUtil<CustomerServiceRepairAccountFile>(CustomerServiceRepairAccountFile.class);
        util.exportExcel(response, list, "补账 附件数据");
    }

    /**
     * 获取补账 附件详细信息
     */
    @RequiresPermissions("customer:file:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取补账 附件详细信息", notes = "获取补账 附件详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customerServiceRepairAccountFileService.selectCustomerServiceRepairAccountFileById(id));
    }

    /**
     * 新增补账 附件
     */
    @RequiresPermissions("customer:file:add")
    @Log(title = "补账 附件", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增补账 附件", notes = "新增补账 附件")
    public AjaxResult add(@RequestBody CustomerServiceRepairAccountFile customerServiceRepairAccountFile)
    {
        return toAjax(customerServiceRepairAccountFileService.insertCustomerServiceRepairAccountFile(customerServiceRepairAccountFile));
    }

    /**
     * 修改补账 附件
     */
    @RequiresPermissions("customer:file:edit")
    @Log(title = "补账 附件", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改补账 附件", notes = "修改补账 附件")
    public AjaxResult edit(@RequestBody CustomerServiceRepairAccountFile customerServiceRepairAccountFile)
    {
        return toAjax(customerServiceRepairAccountFileService.updateCustomerServiceRepairAccountFile(customerServiceRepairAccountFile));
    }

    /**
     * 删除补账 附件
     */
    @RequiresPermissions("customer:file:remove")
    @Log(title = "补账 附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除补账 附件", notes = "删除补账 附件")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(customerServiceRepairAccountFileService.deleteCustomerServiceRepairAccountFileByIds(ids));
    }
}
