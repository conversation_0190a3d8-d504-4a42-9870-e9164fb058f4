package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceAnnualReportInfoMapper;
import com.bxm.customer.domain.CustomerServiceAnnualReportInfo;
import com.bxm.customer.service.ICustomerServiceAnnualReportInfoService;

/**
 * 客户服务工商年报信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceAnnualReportInfoServiceImpl extends ServiceImpl<CustomerServiceAnnualReportInfoMapper, CustomerServiceAnnualReportInfo> implements ICustomerServiceAnnualReportInfoService
{
    @Autowired
    private CustomerServiceAnnualReportInfoMapper customerServiceAnnualReportInfoMapper;

    /**
     * 查询客户服务工商年报信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 客户服务工商年报信息
     */
    @Override
    public CustomerServiceAnnualReportInfo selectCustomerServiceAnnualReportInfoById(Long id)
    {
        return customerServiceAnnualReportInfoMapper.selectCustomerServiceAnnualReportInfoById(id);
    }

    /**
     * 查询客户服务工商年报信息列表
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 客户服务工商年报信息
     */
    @Override
    public List<CustomerServiceAnnualReportInfo> selectCustomerServiceAnnualReportInfoList(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo)
    {
        return customerServiceAnnualReportInfoMapper.selectCustomerServiceAnnualReportInfoList(customerServiceAnnualReportInfo);
    }

    /**
     * 新增客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo)
    {
        return customerServiceAnnualReportInfoMapper.insertCustomerServiceAnnualReportInfo(customerServiceAnnualReportInfo);
    }

    /**
     * 修改客户服务工商年报信息
     * 
     * @param customerServiceAnnualReportInfo 客户服务工商年报信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceAnnualReportInfo(CustomerServiceAnnualReportInfo customerServiceAnnualReportInfo)
    {
        return customerServiceAnnualReportInfoMapper.updateCustomerServiceAnnualReportInfo(customerServiceAnnualReportInfo);
    }

    /**
     * 批量删除客户服务工商年报信息
     * 
     * @param ids 需要删除的客户服务工商年报信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceAnnualReportInfoByIds(Long[] ids)
    {
        return customerServiceAnnualReportInfoMapper.deleteCustomerServiceAnnualReportInfoByIds(ids);
    }

    /**
     * 删除客户服务工商年报信息信息
     * 
     * @param id 客户服务工商年报信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceAnnualReportInfoById(Long id)
    {
        return customerServiceAnnualReportInfoMapper.deleteCustomerServiceAnnualReportInfoById(id);
    }
}
