package com.bxm.customer.validation;

import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * 社保基数验证器
 * 
 * 验证逻辑：
 * 1. 当bizType为1（社医保）或2（个税明细）时，socialInsuranceBase字段必须符合金额格式要求
 * 2. 当bizType为3（国税账号）或4（个税账号）时，socialInsuranceBase字段可以为空
 * 3. 如果socialInsuranceBase不为空，必须是非负数且格式正确（最多8位整数2位小数）
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class SocialInsuranceBaseValidator implements ConstraintValidator<SocialInsuranceBase, ValueAddedEmployeeVO> {

    private boolean allowEmpty;

    @Override
    public void initialize(SocialInsuranceBase constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(ValueAddedEmployeeVO value, ConstraintValidatorContext context) {
        // 如果整个对象为空，跳过验证
        if (value == null) {
            return true;
        }

        Integer bizType = value.getBizType();
        BigDecimal socialInsuranceBase = value.getSocialInsuranceBase();

        // 检查是否为需要验证社保基数的业务类型（1-社医保，2-个税明细）
        boolean isRequiredBizType = bizType != null && (bizType == 1 || bizType == 2);

        if (isRequiredBizType) {
            // 对于社医保和个税明细业务，社保基数不能为空
            if (socialInsuranceBase == null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("社保基数不能为空（社医保和个税明细业务必填）")
                        .addPropertyNode("socialInsuranceBase")
                        .addConstraintViolation();
                return false;
            }

            // 验证金额格式：非负数
            if (socialInsuranceBase.compareTo(BigDecimal.ZERO) < 0) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("社保基数不能为负数")
                        .addPropertyNode("socialInsuranceBase")
                        .addConstraintViolation();
                return false;
            }

            // 验证金额格式：最多8位整数2位小数
            if (!isValidDecimalFormat(socialInsuranceBase)) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("社保基数格式不正确，最多8位整数2位小数")
                        .addPropertyNode("socialInsuranceBase")
                        .addConstraintViolation();
                return false;
            }
        } else {
            // 对于其他业务类型（国税账号、个税账号），如果提供了社保基数，也需要验证格式
            if (socialInsuranceBase != null) {
                // 验证金额格式：非负数
                if (socialInsuranceBase.compareTo(BigDecimal.ZERO) < 0) {
                    context.disableDefaultConstraintViolation();
                    context.buildConstraintViolationWithTemplate("社保基数不能为负数")
                            .addPropertyNode("socialInsuranceBase")
                            .addConstraintViolation();
                    return false;
                }

                // 验证金额格式：最多8位整数2位小数
                if (!isValidDecimalFormat(socialInsuranceBase)) {
                    context.disableDefaultConstraintViolation();
                    context.buildConstraintViolationWithTemplate("社保基数格式不正确，最多8位整数2位小数")
                            .addPropertyNode("socialInsuranceBase")
                            .addConstraintViolation();
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证BigDecimal格式是否符合要求（最多8位整数2位小数）
     * 
     * @param value 待验证的值
     * @return 是否符合格式要求
     */
    private boolean isValidDecimalFormat(BigDecimal value) {
        if (value == null) {
            return true;
        }

        // 获取整数部分和小数部分的位数
        String valueStr = value.toPlainString();
        String[] parts = valueStr.split("\\.");
        
        // 检查整数部分位数（最多8位）
        String integerPart = parts[0];
        if (integerPart.length() > 8) {
            return false;
        }

        // 检查小数部分位数（最多2位）
        if (parts.length > 1) {
            String decimalPart = parts[1];
            if (decimalPart.length() > 2) {
                return false;
            }
        }

        return true;
    }
}
