package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.DownloadRecord;
import com.bxm.customer.domain.dto.download.DownloadRecordDTO;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface IDownloadRecordService extends IService<DownloadRecord>
{
    void updateFailWhenDestroy();

    Long createRecord(String title, Long dataCount, Long attachmentCount, Object obj, DownloadType downloadType);

    IPage<DownloadRecordDTO> downloadRecordList(Integer pageNum, Integer pageSize);

    void deleteDownloadRecord(CommonIdVO vo);

    void retryDownloadRecord(CommonIdVO vo);

    Boolean checkHasDoingRecord(Long userId);

    void stopDownloadRecord(CommonIdVO vo);

    void deleteDownloadFile(String jobParam);

    String getDownloadUlr(Long id);

    void updateDataCount(Long recordId, Long dataCount);

    void updateDownloadError(Long recordId, String errorMsg);
}
