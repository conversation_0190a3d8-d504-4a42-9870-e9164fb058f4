package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.BillSettlementOrderRelationMapper;
import com.bxm.customer.domain.BillSettlementOrderRelation;
import com.bxm.customer.service.IBillSettlementOrderRelationService;
import org.springframework.util.ObjectUtils;

/**
 * 账单结算单关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@Service
public class BillSettlementOrderRelationServiceImpl extends ServiceImpl<BillSettlementOrderRelationMapper, BillSettlementOrderRelation> implements IBillSettlementOrderRelationService
{
    @Autowired
    private BillSettlementOrderRelationMapper billSettlementOrderRelationMapper;

    /**
     * 查询账单结算单关系
     * 
     * @param id 账单结算单关系主键
     * @return 账单结算单关系
     */
    @Override
    public BillSettlementOrderRelation selectBillSettlementOrderRelationById(Long id)
    {
        return billSettlementOrderRelationMapper.selectBillSettlementOrderRelationById(id);
    }

    /**
     * 查询账单结算单关系列表
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 账单结算单关系
     */
    @Override
    public List<BillSettlementOrderRelation> selectBillSettlementOrderRelationList(BillSettlementOrderRelation billSettlementOrderRelation)
    {
        return billSettlementOrderRelationMapper.selectBillSettlementOrderRelationList(billSettlementOrderRelation);
    }

    /**
     * 新增账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    @Override
    public int insertBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation)
    {
        billSettlementOrderRelation.setCreateTime(DateUtils.getNowDate());
        return billSettlementOrderRelationMapper.insertBillSettlementOrderRelation(billSettlementOrderRelation);
    }

    /**
     * 修改账单结算单关系
     * 
     * @param billSettlementOrderRelation 账单结算单关系
     * @return 结果
     */
    @Override
    public int updateBillSettlementOrderRelation(BillSettlementOrderRelation billSettlementOrderRelation)
    {
        billSettlementOrderRelation.setUpdateTime(DateUtils.getNowDate());
        return billSettlementOrderRelationMapper.updateBillSettlementOrderRelation(billSettlementOrderRelation);
    }

    /**
     * 批量删除账单结算单关系
     * 
     * @param ids 需要删除的账单结算单关系主键
     * @return 结果
     */
    @Override
    public int deleteBillSettlementOrderRelationByIds(Long[] ids)
    {
        return billSettlementOrderRelationMapper.deleteBillSettlementOrderRelationByIds(ids);
    }

    /**
     * 删除账单结算单关系信息
     * 
     * @param id 账单结算单关系主键
     * @return 结果
     */
    @Override
    public int deleteBillSettlementOrderRelationById(Long id)
    {
        return billSettlementOrderRelationMapper.deleteBillSettlementOrderRelationById(id);
    }

    @Override
    public List<BillSettlementOrderRelation> selectBatchByBillIds(List<Long> billIds) {
        if (ObjectUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BillSettlementOrderRelation>().in(BillSettlementOrderRelation::getBillId, billIds));
    }

    @Override
    public void deleteByBillId(Long billId) {
        if (Objects.isNull(billId)) {
            return;
        }
        remove(new LambdaQueryWrapper<BillSettlementOrderRelation>().eq(BillSettlementOrderRelation::getBillId, billId));
    }

    @Override
    public List<BillSettlementOrderRelation> selectByBillId(Long billId) {
        if (Objects.isNull(billId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BillSettlementOrderRelation>().eq(BillSettlementOrderRelation::getBillId, billId));
    }

    @Override
    public BillSettlementOrderRelation selectByBillIdAndSettlementOrderId(Long billId, Long settlementOrderId) {
        if (Objects.isNull(billId) || Objects.isNull(settlementOrderId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<BillSettlementOrderRelation>().eq(BillSettlementOrderRelation::getBillId, billId)
                .eq(BillSettlementOrderRelation::getSettlementOrderId, settlementOrderId), false);
    }

    @Override
    public void deleteBySettlementOrderId(Long settlementOrderId) {
        if (Objects.isNull(settlementOrderId)) {
            return;
        }
        remove(new LambdaQueryWrapper<BillSettlementOrderRelation>().eq(BillSettlementOrderRelation::getSettlementOrderId, settlementOrderId));
    }
}
