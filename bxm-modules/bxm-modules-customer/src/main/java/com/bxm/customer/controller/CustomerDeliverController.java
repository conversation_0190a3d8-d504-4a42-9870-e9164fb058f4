package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.businessTask.BusinessTaskItemType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.domain.BusinessTask;
import com.bxm.customer.domain.CustomerDeliver;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.batchDeliver.*;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.service.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 交付Controller
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@RestController
@RequestMapping("/deliver")
@Api(tags = "交付")
public class CustomerDeliverController extends BaseController
{
    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private ICustomerRpaRecordService customerRpaRecordService;

    @Autowired
    private ICustomerRpaRecordDetailService customerRpaRecordDetailService;

    @Autowired
    private ICustomerRpaDetailFileService customerRpaDetailFileService;

    @Autowired
    private ICustomerDeliverTemplateService customerDeliverTemplateService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IBusinessTaskService businessTaskService;

    /**
     * 查询交付列表
     */
//    @RequiresPermissions(value = {"customer:deliver:socialSecurityList", "customer:deliver:personalTaxList", "customer:deliver:preAuthList", "customer:deliver:countryTaxList", "customer:deliver:operatingIncomeTaxList"}, logical = Logical.OR)
    @GetMapping("/list")
    @ApiOperation(value = "查询交付列表")
    public Result<IPage<CustomerDeliverDTO>> list(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        return Result.ok(customerDeliverService.deliverList(vo, deptId));
    }

    /**
     * 查询交付列表
     */
    @PostMapping("/remoteList")
    @ApiIgnore
    public Result<List<CustomerDeliverDTO>> remoteList(@RequestBody CustomerDeliverSearchVO vo)
    {
        return Result.ok(customerDeliverService.remoteDeliverList(vo, vo.getDeptId(), vo.getUserId()));
    }

    /**
     * 客户管理 -> 客户详情 -> 交付记录 -> 获取客户服务的交付记录列表
     * mmnAPI
     */
    @GetMapping("/listByCustomerServiceId/{customerServiceId}")
    @ApiOperation(value = "客户管理 -> 客户详情 -> 交付记录 -> 获取客户服务的交付记录列表",
            notes = "获取客户服务的交付记录列表")
    public Result<List<CustomerDeliverForCustomerServiceDetailDTO>> listByCustomerServiceId(@PathVariable("customerServiceId") Long customerServiceId) {
        return Result.ok(customerDeliverService.deliverListByCustomerServiceId(customerServiceId));
    }

    /**
     * 导出医社保交付列表
     */
    @PostMapping("/socialSecurityExport")
    @ApiOperation(value = "导出医社保交付列表，权限字符：customer:deliver:socialSecurityExport", notes = "导出医社保交付列表")
    public void socialSecurityExport(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        vo.setTabType(2);
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerDeliverDTO> list = customerDeliverService.deliverList(vo, deptId).getRecords();
        List<CustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(row -> {
                    CustomerDeliverSocialSecurityExportDTO dto = new CustomerDeliverSocialSecurityExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList());
        ExcelUtil<CustomerDeliverSocialSecurityExportDTO> util = new ExcelUtil<>(CustomerDeliverSocialSecurityExportDTO.class);
        util.exportExcel(response, exports, "医社保交付单数据");
    }

    @PostMapping("/socialSecurityExportAndUpload")
    public Result socialSecurityExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-医社保" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(2);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_MEDICAL_INSURANCE);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverSocialSecurityExportDTO dto = new CustomerDeliverSocialSecurityExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverSocialSecurityExportDTO> util = new ExcelUtil<>(CustomerDeliverSocialSecurityExportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出个税（工资薪金）交付列表
     */
    @PostMapping("/personalTaxExport")
    @ApiOperation(value = "导出个税（工资薪金）交付列表，权限字符：customer:deliver:personalTaxExport", notes = "导出个税（工资薪金）交付列表")
    public void personalTaxExport(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        vo.setTabType(3);
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerDeliverDTO> list = customerDeliverService.deliverList(vo, deptId).getRecords();
        List<CustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(row -> {
                    CustomerDeliverPersonalTaxExportDTO dto = new CustomerDeliverPersonalTaxExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList());
        ExcelUtil<CustomerDeliverPersonalTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverPersonalTaxExportDTO.class);
        util.exportExcel(response, exports, "个税（工资薪金）交付单数据");
    }

    @PostMapping("/personalTaxExportUpload")
    public Result<String> personalTaxExportUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-个税（工资薪金）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(3);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_INCOME_TAX);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverPersonalTaxExportDTO dto = new CustomerDeliverPersonalTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverPersonalTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverPersonalTaxExportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出个税（经营所得）交付列表
     */
    @PostMapping("/operatingIncomeTaxExport")
    @ApiOperation(value = "导出个税（经营所得）交付列表，权限字符：customer:deliver:operatingIncomeTaxExport", notes = "导出个税（经营所得）交付列表")
    public void operatingIncomeTaxExport(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        vo.setTabType(6);
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerDeliverDTO> list = customerDeliverService.deliverList(vo, deptId).getRecords();
        List<CustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(row -> {
                    CustomerDeliverOperatingIncomePersonTaxExportDTO dto = new CustomerDeliverOperatingIncomePersonTaxExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    return dto;
                }).collect(Collectors.toList());
        ExcelUtil<CustomerDeliverOperatingIncomePersonTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverOperatingIncomePersonTaxExportDTO.class);
        util.exportExcel(response, exports, "个税（经营所得）交付单数据");
    }

    /**
     * 导出个税（经营所得）交付列表
     */
    @PostMapping("/operatingIncomeTaxExportAndUpload")
    public Result operatingIncomeTaxExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-个税（经营所得）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(6);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.INCOME_TAX_MANAGEMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverOperatingIncomePersonTaxExportDTO dto = new CustomerDeliverOperatingIncomePersonTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverOperatingIncomePersonTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverOperatingIncomePersonTaxExportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出国税交付列表
     */
    @PostMapping("/countryTaxExport")
    @ApiOperation(value = "导出国税交付列表，权限字符：customer:deliver:countryTaxExport", notes = "导出国税交付列表")
    public void countryTaxExport(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        vo.setTabType(4);
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerDeliverDTO> list = customerDeliverService.deliverList(vo, deptId).getRecords();
        List<CustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(row -> {
                    CustomerDeliverCountryTaxExportDTO dto = new CustomerDeliverCountryTaxExportDTO();
                    BeanUtils.copyProperties(row, dto);
                    dto.setTotalTaxAmount(Objects.isNull(row.getTotalTaxAmount()) ? "-" : row.getTotalTaxAmount());
                    return dto;
                }).collect(Collectors.toList());
        ExcelUtil<CustomerDeliverCountryTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverCountryTaxExportDTO.class);
        util.exportExcel(response, exports, "国税交付单数据");
    }

    /**
     * 导出国税交付列表
     */
    @PostMapping("/countryTaxExportAndUpload")
    public Result countryTaxExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-国税" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(4);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_NATIONAL_TAX);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverCountryTaxExportDTO dto = new CustomerDeliverCountryTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            dto.setTotalTaxAmount(Objects.isNull(row.getTotalTaxAmount()) ? "-" : row.getTotalTaxAmount());
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverCountryTaxExportDTO> util = new ExcelUtil<>(CustomerDeliverCountryTaxExportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出预认证交付列表
     */
    @PostMapping("/preAuthExport")
    @ApiOperation(value = "导出预认证交付列表，权限字符：customer:deliver:preAuthExport", notes = "导出预认证交付列表")
    public void preAuthExport(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        vo.setTabType(5);
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<CustomerDeliverDTO> list = customerDeliverService.deliverList(vo, deptId).getRecords();
        List<CustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                list.stream().map(row -> {
                    PreAuthInfoDTO preAuthInfoDTO = row.getPreAuthInfoDTO();
                    CustomerDeliverPreAuthExportDTO dto = new CustomerDeliverPreAuthExportDTO();
                    BeanUtils.copyProperties(preAuthInfoDTO, dto);
                    BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(PreAuthInfoDTO.class).toArray(new String[0]));
                    return dto;
                }).collect(Collectors.toList());
        ExcelUtil<CustomerDeliverPreAuthExportDTO> util = new ExcelUtil<>(CustomerDeliverPreAuthExportDTO.class);
        util.exportExcel(response, exports, "预认证交付单数据");
    }

    @PostMapping("/preAuthExportAndUpload")
    @ApiOperation(value = "导出预认证交付列表", notes = "导出预认证交付列表")
    public Result preAuthExportAndUpload(HttpServletResponse response, CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-预认证" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(5);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_PRE_AUTH);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            PreAuthInfoDTO preAuthInfoDTO = row.getPreAuthInfoDTO();
                            CustomerDeliverPreAuthExportDTO dto = new CustomerDeliverPreAuthExportDTO();
                            BeanUtils.copyProperties(preAuthInfoDTO, dto);
                            BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(PreAuthInfoDTO.class).toArray(new String[0]));
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverPreAuthExportDTO> util = new ExcelUtil<>(CustomerDeliverPreAuthExportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/settleAccountsExportAndUpload")
    @ApiOperation(value = "导出汇算交付列表", notes = "导出汇算交付列表")
    public Result settleAccountsExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-汇算" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(7);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_SETTLE_ACCOUNTS);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverSettleAccountsDTO dto = new CustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverSettleAccountsDTO> util = new ExcelUtil<>(CustomerDeliverSettleAccountsDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/annualReportExportAndUpload")
    @ApiOperation(value = "导出年报交付列表", notes = "导出年报交付列表")
    public Result annualReportExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-年报" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(8);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_ANNUAL_REPORT);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverAnnualReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverAnnualReportDTO dto = new CustomerDeliverAnnualReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverAnnualReportDTO> util = new ExcelUtil<>(CustomerDeliverAnnualReportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/residualBenefitsExportAndUpload")
    @ApiOperation(value = "导出残保金交付列表", notes = "导出残保金交付列表")
    public Result residualBenefitsExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-残保金" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(9);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_RESIDUAL_BENEFITS);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverSettleAccountsDTO dto = new CustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverSettleAccountsDTO> util = new ExcelUtil<>(CustomerDeliverSettleAccountsDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/timesReportExportAndUpload")
    @ApiOperation(value = "导出次报交付列表", notes = "导出次报交付列表")
    public Result timesReportExportAndUpload(CustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-次报" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(10);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_TIMES_REPORT);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerDeliverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerDeliverDTO> l = customerDeliverService.deliverList(vo, deptId).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                List<CustomerDeliverTimesReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            CustomerDeliverTimesReportDTO dto = new CustomerDeliverTimesReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                ExcelUtil<CustomerDeliverTimesReportDTO> util = new ExcelUtil<>(CustomerDeliverTimesReportDTO.class);
                asyncService.uploadExport(util, exports, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    private List<String> getPreAuthAllFieldNames(Class<PreAuthInfoDTO> preAuthInfoDTOClass) {
        List<String> fieldNames = new ArrayList<>();
        Field[] fields = preAuthInfoDTOClass.getDeclaredFields();
        for (Field field : fields) {
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    /**
     * 获取交付详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "交付单详情/编辑回显", notes = "获取交付详细信息")
    public Result<CustomerDeliverDetailDTO> deliverDetail(@PathVariable("id") Long id)
    {
        return Result.ok(customerDeliverService.deliverDetail(id));
    }

    /**
     * 新增交付
     */
//    @RequiresPermissions(value = {"customer:deliver:add"}, logical = Logical.OR)
    @PostMapping
    @ApiOperation(value = "新增交付，权限字符：customer:deliver:add", notes = "新增交付")
    public Result add(@RequestBody CustomerDeliverVO vo,
                      @RequestHeader("deptId") Long deptId)
    {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:customer:deliver:add:" + vo.getCustomerServicePeriodMonthId() + ":" + vo.getDeliverType(), userId.toString(), 5L)) {
            try {
                customerDeliverService.add(vo, deptId);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:customer:deliver:add:" + vo.getCustomerServicePeriodMonthId() + ":" + vo.getDeliverType(), userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    /**
     * 新增交付
     */
//    @RequiresPermissions(value = "customer:deliver:add")
    @PostMapping("/saveDeliver")
    @ApiOperation(value = "新增交付（仅保存）", notes = "新增交付（仅保存）")
    public Result saveDeliver(@RequestBody CustomerDeliverVO vo,
                      @RequestHeader("deptId") Long deptId)
    {
        Long userId = SecurityUtils.getUserId();
        if (redisService.lockNotWait("lock:customer:deliver:saveDeliver:" + vo.getCustomerServicePeriodMonthId() + ":" + vo.getDeliverType(), userId.toString(), 5L)) {
            try {
                customerDeliverService.saveDeliver(vo, deptId);
                return Result.ok();
            } catch (ServiceException e) {
                return Result.fail(e.getMessage());
            } finally {
                redisService.unlock("lock:customer:deliver:saveDeliver:" + vo.getCustomerServicePeriodMonthId() + ":" + vo.getDeliverType(), userId.toString());
            }
        } else {
            return Result.fail("请勿重复提交");
        }
    }

    @PostMapping("/submitNewDeliver")
    @ApiOperation("新建待提交-提交，批量操作，传ids，权限字符：customer:deliver:submitNewDeliver")
//    @RequiresPermissions(value = "customer:deliver:submitNewDeliver")
    public Result<CommonDeliverOperateDTO> submitNewDeliver(@RequestBody CommonIdVO vo) {
        return Result.ok(customerDeliverService.submitNewDeliver(vo));
    }

    @PostMapping("/remoteCreate")
    @ApiOperation("内部接口")
    public Result remoteCreate(@RequestBody RemoteCustomerDeliverCreateVO vo)
    {
        customerDeliverService.remoteCreate(vo);
        return Result.ok();
    }

    /**
     * 修改交付
     */
//    @RequiresPermissions(value = {"customer:deliver:edit"}, logical = Logical.OR)
    @PutMapping
    @ApiOperation(value = "修改交付，权限字符：customer:deliver:edit", notes = "修改交付")
    public Result edit(@RequestBody CustomerDeliverVO vo)
    {
        customerDeliverService.edit(vo);
        return Result.ok();
    }

    @PostMapping("/saveEdit")
    @ApiOperation(value = "编辑保存", notes = "编辑保存")
    public Result saveEdit(@RequestBody CustomerDeliverVO vo)
    {
        customerDeliverService.saveEdit(vo);
        return Result.ok();
    }

    /**
     * 提交交付
     */
//    @RequiresPermissions(value = {"customer:deliver:submit"}, logical = Logical.OR)
    @PostMapping("/submit")
    @ApiOperation(value = "提交交付，权限字符：customer:deliver:submit", notes = "提交交付")
    public Result<CommonDeliverOperateDTO> submit(@RequestBody CommonIdVO vo)
    {
        return Result.ok(customerDeliverService.submit(vo));
    }

    /**
     * 删除交付
     */
//    @RequiresPermissions(value = {"customer:deliver:delete", "customer:deliver:preAuthDelete"}, logical = Logical.OR)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除交付，权限字符：customer:deliver:delete", notes = "删除交付")
    public Result<CommonDeliverOperateDTO> delete(@PathVariable Long[] ids)
    {
        return Result.ok(customerDeliverService.delete(ids));
    }

    /**
     * 确认交付
     */
//    @RequiresPermissions(value = {"customer:deliver:confirm", "customer:deliver:preAuthConfirm"}, logical = Logical.OR)
    @PostMapping("/confirm")
    @ApiOperation(value = "确认交付，权限字符：customer:deliver:confirm", notes = "确认交付")
    public Result<CommonDeliverOperateDTO> confirm(@RequestBody CommonIdVO vo)
    {
        return Result.ok(customerDeliverService.confirm(vo));
    }

    @PostMapping("/remoteConfirm")
    @ApiIgnore
    public Result<CommonDeliverOperateDTO> remoteConfirm(@RequestBody List<RemoteConfirmDeliverVO> voList)
    {
        return Result.ok(customerDeliverService.remoteConfirm(voList));
    }

    @PostMapping("/remoteConfirmSingle")
    @ApiIgnore
    public Result remoteConfirmSingle(@RequestBody RemoteConfirmDeliverVO vo)
    {
        customerDeliverService.remoteConfirmSingle(vo);
        return Result.ok();
    }

    @PostMapping("/preAuthConfirm")
    @ApiOperation("预认证-确认")
    public Result preAuthConfirm(@RequestBody PreAuthConfirmVO vo) {
        customerDeliverService.preAuthConfirm(vo);
        return Result.ok();
    }

    @PostMapping("/remotePreAuthConfirm")
    @ApiIgnore
    public Result remotePreAuthConfirm(@RequestBody PreAuthConfirmVO vo) {
        customerDeliverService.remotePreAuthConfirm(vo);
        return Result.ok();
    }

    /**
     * 退回交付
     */
//    @RequiresPermissions(value = {"customer:deliver:saveCreateReBack","customer:deliver:submitReBack", "customer:deliver:reportReBack", "customer:deliver:confirmReBack", "customer:deliver:completeReBack", "customer:deliver:preAuthAuthCompleteReBack", "customer:deliver:preAuthWaitConfirmReBack", "customer:deliver:preAuthWaitAuthReBack", "customer:deliver:preAuthWaitSupplementReBack", "customer:deliver:notReportReBack", "customer:deliver:notDeductionReBack"}, logical = Logical.OR)
    @PostMapping("/reBack")
    @ApiOperation(value = "退回交付，权限字符：customer:deliver:submitReBack(已提交待申报退回)，customer:deliver:reportReBack(已申报待确认退回)，customer:deliver:confirmReBack(已确认待扣款退回)，customer:deliver:completeReBack(已扣款退回)", notes = "退回交付")
    public Result<CommonDeliverOperateDTO> reBack(@RequestBody CommonIdVO vo)
    {
        return Result.ok(customerDeliverService.reBack(vo));
    }

    /**
     * 申报
     */
//    @RequiresPermissions("customer:deliver:report")
    @PostMapping("/report")
    @ApiOperation(value = "保存/提交申报，权限字符：customer:deliver:report", notes = "保存/提交申报")
    public Result report(@RequestBody CustomerDeliverReportVO vo)
    {
        customerDeliverService.report(vo);
        return Result.ok();
    }

    @PostMapping("/remoteReport")
    @ApiOperation(value = "内部接口")
    public Result remoteReport(@RequestBody RemoteCustomerDeliverReportVO vo)
    {
        customerDeliverService.remoteReport(vo);
        return Result.ok();
    }

    /**
     * 扣款
     */
//    @RequiresPermissions("customer:deliver:deduction")
    @PostMapping("/deduction")
    @ApiOperation(value = "保存/提交扣款，权限字符：customer:deliver:deduction", notes = "交付扣款")
    public Result deduction(@RequestBody CustomerDeliverDeductionVO vo)
    {
        customerDeliverService.deduction(vo);
        return Result.ok();
    }

    @PostMapping("/remoteDeduction")
    @ApiOperation(value = "内部接口")
    public Result remoteDeduction(@RequestBody RemoteCustomerDeliverDeductionVO vo)
    {
        customerDeliverService.remoteDeduction(vo);
        return Result.ok();
    }

    @PostMapping("/reportDeductionSubmit")
    @ApiOperation("申报已保存/扣款已保存提交，参数传ids，权限字符：customer:deliver:reportDeductionSubmit")
//    @RequiresPermissions(value = {"customer:deliver:waitRepeatSubmit", "customer:deliver:reportSubmit", "customer:deliver:deductionSubmit", "customer:deliver:advisorBatchSubmit", "customer:deliver:accountingBatchSubmit"}, logical = Logical.OR)
    public Result<CommonDeliverOperateDTO> reportDeductionSubmit(@RequestBody CommonIdVO vo) {
        return Result.ok(customerDeliverService.reportDeductionSubmit(vo));
    }

    /**
     * 异常处理
     */
//    @RequiresPermissions(value = {"customer:deliver:reportExceptionDeal", "customer:deliver:deductionExceptionDeal", "customer:deliver:authExceptionDeal"}, logical = Logical.OR)
    @PostMapping("/exceptionDeal")
    @ApiOperation(value = "异常处理，权限字符：customer:deliver:exceptionDeal", notes = "异常处理")
    public Result exceptionDeal(@RequestBody CustomerDeliverExceptionVO vo)
    {
        customerDeliverService.exceptionDeal(vo);
        return Result.ok();
    }

    @PostMapping("/remoteExceptionDeal")
    @ApiOperation(value = "内部接口")
    public Result remoteExceptionDeal(@RequestBody RemoteCustomerDeliverExceptionVO vo)
    {
        customerDeliverService.remoteExceptionDeal(vo);
        return Result.ok();
    }

    /**
     * 预认证补充
     */
//    @RequiresPermissions("customer:deliver:supplement")
    @PostMapping("/supplement")
    @ApiOperation(value = "预认证补充(废弃)，权限字符：customer:deliver:supplement", notes = "预认证补充")
    public Result supplement(@RequestBody CustomerDeliverVO vo)
    {
        customerDeliverService.supplement(vo);
        return Result.ok();
    }

    @PostMapping("/remoteSupplement")
    @ApiIgnore
    public Result remoteSupplement(@RequestBody RemoteCustomerDeliverCreateVO vo)
    {
        customerDeliverService.remoteSupplement(vo);
        return Result.ok();
    }

    /**
     * 预认证认证
     */
//    @RequiresPermissions("customer:deliver:auth")
    @PostMapping("/auth")
    @ApiOperation(value = "预认证认证，权限字符：customer:deliver:auth", notes = "预认证认证")
    public Result auth(@RequestBody CustomerDeliverAuthVO vo)
    {
        customerDeliverService.auth(vo);
        return Result.ok();
    }

    @PostMapping("/remoteAuth")
    @ApiOperation(value = "内部接口")
    public Result remoteAuth(@RequestBody RemoteCustomerDeliverAuthVO vo)
    {
        customerDeliverService.remoteAuth(vo);
        return Result.ok();
    }

    @PostMapping("/submitAuthBatch")
    @ApiOperation("预认证-待认证提交，传ids")
    public Result<CommonDeliverOperateDTO> submitAuthBatch(@RequestBody CommonIdVO vo) {
        return Result.ok(customerDeliverService.submitAuthBatch(vo));
    }

    @PostMapping("/getDeliverFiles")
    @ApiOperation("通用交付单查看附件，权限字符：customer:deliver:getFiles")
    public Result<List<DeliverFileCommonDTO>> getDeliverFiles(@RequestBody DeliverFileGetVO vo) {
        return Result.ok(customerDeliverService.getDeliverFiles(vo));
    }

    @GetMapping("/rpaRecordList")
    @ApiOperation("rpa上传记录,权限字符：customer:deliver:rpaRecordList")
//    @RequiresPermissions("customer:deliver:rpaRecordList")
    public Result<IPage<CustomerDeliverRpaDTO>> rpaRecordList(@RequestParam(value = "customerName", required = false) @ApiParam("客户名") String customerName,
                                                              @RequestParam(value = "rpaType", required = false) @ApiParam("rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-预认证，5-收入") Integer rpaType,
                                                              @RequestParam("pageNum") @ApiParam("页码") Integer pageNum,
                                                              @RequestParam("pageSize") @ApiParam("每页数量") Integer pageSize,
                                                              @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerRpaRecordService.rpaRecordList(customerName, rpaType, deptId, pageNum, pageSize));
    }

    @PostMapping("/rpaConfirm")
    @ApiOperation("rpa记录确认，权限字符：customer:deliver:rpaConfirm")
//    @RequiresPermissions("customer:deliver:rpaConfirm")
    public Result rpaConfirm(@RequestBody RpaRecordConfirmVO vo,
                             @RequestHeader("deptId") Long deptId) {
        customerRpaRecordService.rpaConfirm(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/rpaRecordRetry")
    @ApiOperation("rpa记录重试，权限字符：customer:deliver:rpaRecordRetry")
//    @RequiresPermissions("customer:deliver:rpaRecordRetry")
    public Result rpaRecordRetry(@RequestBody CommonIdVO vo,
                                 @RequestHeader("deptId") Long deptId) {
        customerRpaRecordService.rpaRecordRetry(vo, deptId);
        return Result.ok();
    }

    @GetMapping("/rpaDetailList")
    @ApiOperation("rpa明细列表，权限字符：customer:deliver:rpaDetailList")
//    @RequiresPermissions("customer:deliver:rpaDetailList")
    public Result<IPage<CustomerDeliverRpaDetailDTO>> rpaDetailList(@RequestParam(value = "rpaRecordId") @ApiParam("rpa上传记录id") Long rpaRecordId,
                                                                    @RequestParam(value = "customerName", required = false) @ApiParam("客户名") String customerName,
                                                                    @RequestParam(value = "status", required = false) @ApiParam("状态，0-待确认，1-处理中，2-成功，3-失败") Integer status,
                                                                    @RequestParam("pageNum") @ApiParam("页码") Integer pageNum,
                                                                    @RequestParam("pageSize") @ApiParam("每页数量") Integer pageSize) {
        return Result.ok(customerRpaRecordDetailService.rpaDetailList(rpaRecordId, customerName, status, pageNum, pageSize));
    }

    @PostMapping("/rpaDetailRetry")
    @ApiOperation("rpa明细重试，权限字符：customer:deliver:rpaDetailRetry")
//    @RequiresPermissions("customer:deliver:rpaDetailRetry")
    public Result rpaDetailRetry(@RequestBody CommonIdVO vo,
                                 @RequestHeader("deptId") Long deptId) {
        customerRpaRecordService.rpaDetailRetry(vo, deptId);
        return Result.ok();
    }

    @GetMapping("/rpaDetailFileList")
    @ApiOperation("查看明细附件")
    public Result<List<CommonFileVO>> rpaDetailFileList(@RequestParam(value = "rpaDetailId") @ApiParam("rpa明细id") Long rpaDetailId) {
        return Result.ok(customerRpaDetailFileService.rpaDetailFileList(rpaDetailId));
    }

    @GetMapping("/getDeliverTemplate")
    @ApiOperation("获取批量交付模板下载地址")
    public Result<String> getDeliverTemplate(@RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-入账") Integer deliverType,
                                                  @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常，9-补充附件，10-入账，11-确认,17-完结，18-解除完结异常") Integer operType,
                                             @RequestParam(value = "period", required = false) Integer period) {
        return Result.ok(customerDeliverTemplateService.getDeliverTemplate(deliverType, operType, period));
    }

    @GetMapping("/remoteCustomerDeliverMiniList")
    @ApiOperation("内部接口")
    public Result<List<CustomerDeliverMiniDTO>> remoteCustomerDeliverMiniList(@RequestParam("deptId") Long deptId,
                                                                                    @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得），7-汇算，8-年报") Integer deliverType,
                                                                                    @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常，17-完结，18-解除完结异常") Integer operType,
                                                                                    @RequestParam("period") Integer period) {
        if (deliverType != 51) {
            if (Lists.newArrayList(1, 2).contains(deliverType) && operType == 7) {
                return Result.ok(customerServiceService.updateFileWaitDealList(deptId, operType, period, deliverType));
            }
            if (deliverType == 55) {
                deliverType = DeliverType.SETTLE_ACCOUNTS.getCode();
            } else if (deliverType == 56) {
                deliverType = DeliverType.ANNUAL_REPORT.getCode();
            } else if (deliverType == 63) {
                deliverType = DeliverType.RESIDUAL_BENEFITS.getCode();
            } else if (deliverType == 66) {
                deliverType = DeliverType.TIMES_REPORT.getCode();
            }
            Integer deliverStatus = null;
            if (operType == 1) {
                deliverStatus = -1;
            } else if (operType == 2) {
                deliverStatus = 0;
            } else if (operType == 3) {
                deliverStatus = 6;
            } else if (operType == 4) {
                deliverStatus = 3;
            } else if (operType == 5) {
                deliverStatus = 7;
            } else if (operType == 6) {
                if (Objects.equals(deliverType, DeliverType.SETTLE_ACCOUNTS.getCode()) || Objects.equals(deliverType, DeliverType.ANNUAL_REPORT.getCode()) || Objects.equals(deliverType, DeliverType.RESIDUAL_BENEFITS.getCode()) || Objects.equals(deliverType, DeliverType.TIMES_REPORT.getCode())) {
                    deliverStatus = 12;
                } else {
                    deliverStatus = 9;
                }
            } else if (operType == 7) {
                deliverStatus = 10;
            } else if (operType == 8) {
                if (deliverType == 1 || deliverType == 2) {
                    deliverStatus = 12;
                } else {
                    deliverStatus = 11;
                }
            } else if (operType == 11) {
                if (Objects.equals(deliverType, DeliverType.PRE_AUTH.getCode())) {
                    deliverStatus = 2;
                } else {
                    deliverStatus = 12;
                }
            } else if (operType == 17) {
                deliverStatus = 14;
            } else if (operType == 18) {
                deliverStatus = 15;
            }
            if (Objects.isNull(deliverStatus)) {
                throw new ServiceException("参数错误");
            }
            return Result.ok(customerServiceService.remoteCustomerDeliverMiniList(deptId, null, deliverType, deliverStatus, null, null, null, period, null, null, null, null, null, null, null, null, null, null, null, null, null));
        } else {
            return Result.ok(customerServiceService.medicalSocialWaitDealList(deptId, operType, period));
        }
    }

    @PostMapping("/downloadDeliverTemplate")
    @ApiOperation("下载批量交付模板")
    public void downloadDeliverTemplate(@RequestHeader("deptId") Long deptId,
                                        @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营收入），7-入账，8-结算") Integer deliverType,
                                        @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常，9-补充申报附件，10-入账，11-确认，12-新入账编辑，13-入账RPA更新，14-入账结算，15-新户预收结算，16-驳回，17-完结，18-处理完结异常") Integer operType,
                                        @RequestParam("period") Integer period,
                                        HttpServletResponse httpServletResponse) {
        if (deliverType != 51) {
            if (deliverType == 52) {
                List<BusinessTask> waitFinishTask = businessTaskService.selectWaitFinishTask(BusinessTaskItemType.BANK_PAYMENT.getCode(), SecurityUtils.getUserId());
                List<BusinessTaskBankFinishTemplateDTO> templateDTOS = waitFinishTask.stream().map(row -> BusinessTaskBankFinishTemplateDTO.builder()
                        .enterpriseName(row.getCustomerName())
                        .creditCode(row.getCreditCode())
                        .period(row.getPeriod().toString())
                        .build()).collect(Collectors.toList());
                ExcelUtil<BusinessTaskBankFinishTemplateDTO> util = new ExcelUtil<>(BusinessTaskBankFinishTemplateDTO.class);
                util.exportExcel(httpServletResponse, templateDTOS, "任务完成模板");
            } else {
                if (deliverType == 55) {
                    deliverType = DeliverType.SETTLE_ACCOUNTS.getCode();
                } else if (deliverType == 56) {
                    deliverType = DeliverType.ANNUAL_REPORT.getCode();
                } else if (deliverType == 63) {
                    deliverType = DeliverType.RESIDUAL_BENEFITS.getCode();
                } else if (deliverType == 66) {
                    deliverType = DeliverType.TIMES_REPORT.getCode();
                }
                Integer deliverStatus = null;
                if (operType == 1) {
                    deliverStatus = -1;
                } else if (operType == 2) {
                    deliverStatus = 0;
                } else if (operType == 3) {
                    deliverStatus = 6;
                } else if (operType == 4) {
                    deliverStatus = 3;
                } else if (operType == 5) {
                    deliverStatus = 7;
                } else if (operType == 6) {
                    if (Objects.equals(deliverType, DeliverType.SETTLE_ACCOUNTS.getCode()) || Objects.equals(deliverType, DeliverType.ANNUAL_REPORT.getCode()) || Objects.equals(deliverType, DeliverType.RESIDUAL_BENEFITS.getCode()) || Objects.equals(deliverType, DeliverType.TIMES_REPORT.getCode())) {
                        deliverStatus = 12;
                    } else {
                        deliverStatus = 9;
                    }
                } else if (operType == 7) {
                    deliverStatus = 10;
                } else if (operType == 8) {
                    deliverStatus = 11;
                } else if (operType == 11) {
                    if (Objects.equals(deliverType, DeliverType.PRE_AUTH.getCode())) {
                        deliverStatus = 2;
                    } else {
                        deliverStatus = 12;
                    }
                } else if (operType == 17) {
                    deliverStatus = 14;
                } else if (operType == 18) {
                    deliverStatus = 15;
                }
//        if (Objects.isNull(deliverStatus)) {
//            throw new ServiceException("参数错误");
//        }
                List<CustomerDeliverMiniDTO> records = customerServiceService.remoteCustomerDeliverMiniList(deptId, null, deliverType, deliverStatus, null, null, null, period, null, null, null, null, null, null, null, null, null, null, null, null, null);
                String dataType = deliverType + "-" + operType;
                switch (dataType) {
                    case "1-1":
                        ExcelUtil<TaxCreateTemplateDTO> createUtil = new ExcelUtil<>(TaxCreateTemplateDTO.class);
                        createUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxCreateTemplateDTO dto = new TaxCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "医保创建模板");
                        break;
                    case "1-2":
                        ExcelUtil<TaxReportTemplateDTO> reportUtil = new ExcelUtil<>(TaxReportTemplateDTO.class);
                        reportUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxReportTemplateDTO dto = new TaxReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "医保申报模板");
                        break;
                    case "1-3":
                        ExcelUtil<ReportExceptionTemplateDTO> reportExceptionUtil = new ExcelUtil<>(ReportExceptionTemplateDTO.class);
                        reportExceptionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            ReportExceptionTemplateDTO dto = new ReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "医保解除申报异常模板");
                        break;
                    case "1-4":
                        ExcelUtil<DeductionTemplateDTO> deductionUtil = new ExcelUtil<>(DeductionTemplateDTO.class);
                        deductionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionTemplateDTO dto = new DeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "医保扣款模板");
                        break;
                    case "1-5":
                        ExcelUtil<DeductionExceptionTemplateDTO> deductionExceptionUtil = new ExcelUtil<>(DeductionExceptionTemplateDTO.class);
                        deductionExceptionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionExceptionTemplateDTO dto = new DeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "医保解除扣款异常模板");
                        break;
                    case "1-11":
                    case "2-11":
                    case "3-11":
                    case "4-11":
                    case "5-11":
                    case "6-11":
                        ExcelUtil<ConfirmDeliverDTO> confirmUtil = new ExcelUtil<>(ConfirmDeliverDTO.class);
                        confirmUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            ConfirmDeliverDTO dto = new ConfirmDeliverDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "确认模板");
                        break;
                    case "2-1":
                        ExcelUtil<TaxCreateTemplateDTO> createUtil1 = new ExcelUtil<>(TaxCreateTemplateDTO.class);
                        createUtil1.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxCreateTemplateDTO dto = new TaxCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "社保创建模板");
                        break;
                    case "2-2":
                        ExcelUtil<TaxReportTemplateDTO> reportUtil1 = new ExcelUtil<>(TaxReportTemplateDTO.class);
                        reportUtil1.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxReportTemplateDTO dto = new TaxReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "社保申报模板");
                        break;
                    case "2-3":
                        ExcelUtil<ReportExceptionTemplateDTO> reportExceptionUtil1 = new ExcelUtil<>(ReportExceptionTemplateDTO.class);
                        reportExceptionUtil1.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            ReportExceptionTemplateDTO dto = new ReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "社保解除申报异常模板");
                        break;
                    case "2-4":
                        ExcelUtil<DeductionTemplateDTO> deductionUtil1 = new ExcelUtil<>(DeductionTemplateDTO.class);
                        deductionUtil1.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionTemplateDTO dto = new DeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "社保扣款模板");
                        break;
                    case "2-5":
                        ExcelUtil<DeductionExceptionTemplateDTO> deductionExceptionUtil1 = new ExcelUtil<>(DeductionExceptionTemplateDTO.class);
                        deductionExceptionUtil1.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionExceptionTemplateDTO dto = new DeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "社保解除扣款异常模板");
                        break;
                    case "3-1":
                        ExcelUtil<TaxCreateTemplateDTO> createUtil2 = new ExcelUtil<>(TaxCreateTemplateDTO.class);
                        createUtil2.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxCreateTemplateDTO dto = new TaxCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（工资薪金）创建模板");
                        break;
                    case "3-2":
                        ExcelUtil<TaxReportTemplateDTO> reportUtil2 = new ExcelUtil<>(TaxReportTemplateDTO.class);
                        reportUtil2.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxReportTemplateDTO dto = new TaxReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（工资薪金）申报模板");
                        break;
                    case "3-3":
                        ExcelUtil<PersonTaxReportExceptionTemplateDTO> reportExceptionUtil2 = new ExcelUtil<>(PersonTaxReportExceptionTemplateDTO.class);
                        reportExceptionUtil2.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            PersonTaxReportExceptionTemplateDTO dto = new PersonTaxReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（工资薪金）解除申报异常模板");
                        break;
                    case "3-4":
                        ExcelUtil<DeductionTemplateDTO> deductionUtil2 = new ExcelUtil<>(DeductionTemplateDTO.class);
                        deductionUtil2.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionTemplateDTO dto = new DeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（工资薪金）扣款模板");
                        break;
                    case "3-5":
                        ExcelUtil<DeductionExceptionTemplateDTO> deductionExceptionUtil2 = new ExcelUtil<>(DeductionExceptionTemplateDTO.class);
                        deductionExceptionUtil2.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionExceptionTemplateDTO dto = new DeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（工资薪金）解除扣款异常模板");
                        break;
                    case "4-1":
                        if (DateUtils.getNowPeriod() % 100 == 12) {
                            ExcelUtil<CountryTaxCreateTemplate12MonthDTO> createUtil3 = new ExcelUtil<>(CountryTaxCreateTemplate12MonthDTO.class);
                            createUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                                CountryTaxCreateTemplate12MonthDTO dto = new CountryTaxCreateTemplate12MonthDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList()), "国税创建模板");
                        } else {
                            ExcelUtil<CountryTaxCreateTemplateDTO> createUtil3 = new ExcelUtil<>(CountryTaxCreateTemplateDTO.class);
                            createUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                                CountryTaxCreateTemplateDTO dto = new CountryTaxCreateTemplateDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList()), "国税创建模板");
                        }
                        break;
                    case "4-2":
                        ExcelUtil<CountryTaxReportTemplateDTO> reportUtil3 = new ExcelUtil<>(CountryTaxReportTemplateDTO.class);
                        reportUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            CountryTaxReportTemplateDTO dto = new CountryTaxReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "国税申报模板");
                        break;
                    case "4-3":
                        ExcelUtil<CountryTaxReportExceptionTemplateDTO> reportExceptionUtil3 = new ExcelUtil<>(CountryTaxReportExceptionTemplateDTO.class);
                        reportExceptionUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            CountryTaxReportExceptionTemplateDTO dto = new CountryTaxReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "国税解除申报异常模板");
                        break;
                    case "4-4":
                        ExcelUtil<DeductionTemplateDTO> deductionUtil3 = new ExcelUtil<>(DeductionTemplateDTO.class);
                        deductionUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionTemplateDTO dto = new DeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "国税扣款模板");
                        break;
                    case "4-5":
                        ExcelUtil<DeductionExceptionTemplateDTO> deductionExceptionUtil3 = new ExcelUtil<>(DeductionExceptionTemplateDTO.class);
                        deductionExceptionUtil3.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionExceptionTemplateDTO dto = new DeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "国税解除扣款异常模板");
                        break;
                    case "5-1":
                        ExcelUtil<PreAuthCreateTemplateDTO> preAuthCreateUtil = new ExcelUtil<>(PreAuthCreateTemplateDTO.class);
                        preAuthCreateUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            PreAuthCreateTemplateDTO dto = new PreAuthCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证新建模板");
                        break;
                    case "5-6":
                        ExcelUtil<PreAuthSupplementTemplateDTO> preAuthSupplementUtil = new ExcelUtil<>(PreAuthSupplementTemplateDTO.class);
                        preAuthSupplementUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            PreAuthSupplementTemplateDTO dto = new PreAuthSupplementTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证补充模板");
                        break;
                    case "5-7":
                        ExcelUtil<PreAuthAuthTemplateDTO> preAuthAuthUtil = new ExcelUtil<>(PreAuthAuthTemplateDTO.class);
                        preAuthAuthUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            PreAuthAuthTemplateDTO dto = new PreAuthAuthTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证认证模板");
                        break;
                    case "5-8":
                        ExcelUtil<PreAuthAuthExceptionTemplateDTO> preAuthAuthExceptionUtil = new ExcelUtil<>(PreAuthAuthExceptionTemplateDTO.class);
                        preAuthAuthExceptionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            PreAuthAuthExceptionTemplateDTO dto = new PreAuthAuthExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证解除认证异常模板");
                        break;
                    case "5-17":
                        ExcelUtil<OverTemplateDTO> overTemplateDTOExcelUtil = new ExcelUtil<>(OverTemplateDTO.class);
                        overTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            OverTemplateDTO dto = new OverTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证完结模板");
                        break;
                    case "5-18":
                        ExcelUtil<OverExceptionTemplateDTO> overExceptionTemplateDTOExcelUtil = new ExcelUtil<>(OverExceptionTemplateDTO.class);
                        overExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            OverExceptionTemplateDTO dto = new OverExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "预认证解除完结异常模板");
                        break;
                    case "6-1":
                        ExcelUtil<CountryTaxCreateTemplateDTO> createUtil6 = new ExcelUtil<>(CountryTaxCreateTemplateDTO.class);
                        createUtil6.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            CountryTaxCreateTemplateDTO dto = new CountryTaxCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（经营所得）创建模板");
                        break;
                    case "6-2":
                        ExcelUtil<TaxReportTemplateDTO> reportUtil6 = new ExcelUtil<>(TaxReportTemplateDTO.class);
                        reportUtil6.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TaxReportTemplateDTO dto = new TaxReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（经营所得）申报模板");
                        break;
                    case "6-3":
                        ExcelUtil<ReportExceptionTemplateDTO> reportExceptionUtil6 = new ExcelUtil<>(ReportExceptionTemplateDTO.class);
                        reportExceptionUtil6.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            ReportExceptionTemplateDTO dto = new ReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（经营所得）解除申报异常模板");
                        break;
                    case "6-4":
                        ExcelUtil<DeductionTemplateDTO> deductionUtil6 = new ExcelUtil<>(DeductionTemplateDTO.class);
                        deductionUtil6.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionTemplateDTO dto = new DeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（经营所得）扣款模板");
                        break;
                    case "6-5":
                        ExcelUtil<DeductionExceptionTemplateDTO> deductionExceptionUtil6 = new ExcelUtil<>(DeductionExceptionTemplateDTO.class);
                        deductionExceptionUtil6.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            DeductionExceptionTemplateDTO dto = new DeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "个税（经营所得）解除扣款异常模板");
                        break;
                    case "7-1":
                        ExcelUtil<SettleAccountsCreateTemplateDTO> settleAccountsCreateTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsCreateTemplateDTO.class);
                        settleAccountsCreateTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsCreateTemplateDTO dto = new SettleAccountsCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算创建模板");
                        break;
                    case "7-2":
                        ExcelUtil<SettleAccountsReportTemplateDTO> settleAccountsReportTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsReportTemplateDTO.class);
                        settleAccountsReportTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsReportTemplateDTO dto = new SettleAccountsReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算申报模板");
                        break;
                    case "7-3":
                        ExcelUtil<SettleAccountsReportExceptionTemplateDTO> settleAccountsReportExceptionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsReportExceptionTemplateDTO.class);
                        settleAccountsReportExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsReportExceptionTemplateDTO dto = new SettleAccountsReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算解除申报异常模板");
                        break;
                    case "7-4":
                        ExcelUtil<SettleAccountsDeductionTemplateDTO> settleAccountsDeductionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsDeductionTemplateDTO.class);
                        settleAccountsDeductionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsDeductionTemplateDTO dto = new SettleAccountsDeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算扣款模板");
                        break;
                    case "7-5":
                        ExcelUtil<SettleAccountsDeductionExceptionTemplateDTO> settleAccountsDeductionExceptionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsDeductionExceptionTemplateDTO.class);
                        settleAccountsDeductionExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsDeductionExceptionTemplateDTO dto = new SettleAccountsDeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算解除扣款异常模板");
                        break;
                    case "7-6":
                        ExcelUtil<SettleAccountsConfirmTemplateDTO> settleAccountsConfirmTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsConfirmTemplateDTO.class);
                        settleAccountsConfirmTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsConfirmTemplateDTO dto = new SettleAccountsConfirmTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "汇算确认模板");
                        break;
                    case "8-1":
                        ExcelUtil<AnnualReportCreateTemplateDTO> annualReportCreateTemplateDTOExcelUtil = new ExcelUtil<>(AnnualReportCreateTemplateDTO.class);
                        annualReportCreateTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            AnnualReportCreateTemplateDTO dto = new AnnualReportCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "年报创建模板");
                        break;
                    case "8-2":
                        ExcelUtil<AnnualReportReportTemplateDTO> annualReportReportTemplateDTOExcelUtil = new ExcelUtil<>(AnnualReportReportTemplateDTO.class);
                        annualReportReportTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            AnnualReportReportTemplateDTO dto = new AnnualReportReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "年报申报模板");
                        break;
                    case "8-3":
                        ExcelUtil<AnnualReportReportExceptionTemplateDTO> annualReportReportExceptionTemplateDTOExcelUtil = new ExcelUtil<>(AnnualReportReportExceptionTemplateDTO.class);
                        annualReportReportExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            AnnualReportReportExceptionTemplateDTO dto = new AnnualReportReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "年报解除申报异常模板");
                        break;
                    case "9-1":
                        ExcelUtil<SettleAccountsCreateTemplateDTO> residualBenefitsCreateTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsCreateTemplateDTO.class);
                        residualBenefitsCreateTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsCreateTemplateDTO dto = new SettleAccountsCreateTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金创建模板");
                        break;
                    case "9-2":
                        ExcelUtil<SettleAccountsReportTemplateDTO> residualBenefitsReportTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsReportTemplateDTO.class);
                        residualBenefitsReportTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsReportTemplateDTO dto = new SettleAccountsReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金申报模板");
                        break;
                    case "9-3":
                        ExcelUtil<SettleAccountsReportExceptionTemplateDTO> residualBenefitsReportExceptionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsReportExceptionTemplateDTO.class);
                        residualBenefitsReportExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsReportExceptionTemplateDTO dto = new SettleAccountsReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金解除申报异常模板");
                        break;
                    case "9-4":
                        ExcelUtil<SettleAccountsDeductionTemplateDTO> residualBenefitsDeductionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsDeductionTemplateDTO.class);
                        residualBenefitsDeductionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsDeductionTemplateDTO dto = new SettleAccountsDeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金扣款模板");
                        break;
                    case "9-5":
                        ExcelUtil<SettleAccountsDeductionExceptionTemplateDTO> residualBenefitsDeductionExceptionTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsDeductionExceptionTemplateDTO.class);
                        residualBenefitsDeductionExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsDeductionExceptionTemplateDTO dto = new SettleAccountsDeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金解除扣款异常模板");
                        break;
                    case "9-6":
                        ExcelUtil<SettleAccountsConfirmTemplateDTO> residualBenefitsConfirmTemplateDTOExcelUtil = new ExcelUtil<>(SettleAccountsConfirmTemplateDTO.class);
                        residualBenefitsConfirmTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            SettleAccountsConfirmTemplateDTO dto = new SettleAccountsConfirmTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "残保金确认模板");
                        break;
                    case "10-2":
                        ExcelUtil<TimesReportReportTemplateDTO> timesReportReportTemplateDTOExcelUtil = new ExcelUtil<>(TimesReportReportTemplateDTO.class);
                        timesReportReportTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TimesReportReportTemplateDTO dto = new TimesReportReportTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "次报申报模板");
                        break;
                    case "10-3":
                        ExcelUtil<TimesReportReportExceptionTemplateDTO> timesReportReportExceptionTemplateDTOExcelUtil = new ExcelUtil<>(TimesReportReportExceptionTemplateDTO.class);
                        timesReportReportExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TimesReportReportExceptionTemplateDTO dto = new TimesReportReportExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "次报解除申报异常模板");
                        break;
                    case "10-4":
                        ExcelUtil<TimesReportDeductionTemplateDTO> timesReportDeductionTemplateDTOExcelUtil = new ExcelUtil<>(TimesReportDeductionTemplateDTO.class);
                        timesReportDeductionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TimesReportDeductionTemplateDTO dto = new TimesReportDeductionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "次报扣款模板");
                        break;
                    case "10-5":
                        ExcelUtil<TimesReportDeductionExceptionTemplateDTO> timesReportDeductionExceptionTemplateDTOExcelUtil = new ExcelUtil<>(TimesReportDeductionExceptionTemplateDTO.class);
                        timesReportDeductionExceptionTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TimesReportDeductionExceptionTemplateDTO dto = new TimesReportDeductionExceptionTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "次报解除扣款异常模板");
                        break;
                    case "10-6":
                        ExcelUtil<TimesReportConfirmTemplateDTO> timesReportConfirmTemplateDTOExcelUtil = new ExcelUtil<>(TimesReportConfirmTemplateDTO.class);
                        timesReportConfirmTemplateDTOExcelUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                            TimesReportConfirmTemplateDTO dto = new TimesReportConfirmTemplateDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList()), "次报确认模板");
                        break;
                    default:
                        throw new ServiceException("不支持的类型");
                }
            }
        } else {
            List<CustomerDeliverMiniDTO> records = customerServiceService.medicalSocialWaitDealList(deptId, operType, period);
            String dataType = deliverType + "-" + operType;
            switch (dataType) {
                case "51-1":
                    ExcelUtil<MedicalSocialSecurityCreateTemplateDTO> createUtil = new ExcelUtil<>(MedicalSocialSecurityCreateTemplateDTO.class);
                    createUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityCreateTemplateDTO dto = new MedicalSocialSecurityCreateTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保创建模板");
                    break;
                case "51-2":
                    ExcelUtil<MedicalSocialSecurityReportTemplateDTO> reportUtil = new ExcelUtil<>(MedicalSocialSecurityReportTemplateDTO.class);
                    reportUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityReportTemplateDTO dto = new MedicalSocialSecurityReportTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保申报模板");
                    break;
                case "51-3":
                    ExcelUtil<MedicalSocialSecurityDealReportExceptionTemplateDTO> reportExceptionUtil = new ExcelUtil<>(MedicalSocialSecurityDealReportExceptionTemplateDTO.class);
                    reportExceptionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityDealReportExceptionTemplateDTO dto = new MedicalSocialSecurityDealReportExceptionTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保解除申报异常模板");
                    break;
                case "51-4":
                    ExcelUtil<MedicalSocialSecurityDeductionTemplateDTO> deductionUtil = new ExcelUtil<>(MedicalSocialSecurityDeductionTemplateDTO.class);
                    deductionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityDeductionTemplateDTO dto = new MedicalSocialSecurityDeductionTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保扣款模板");
                    break;
                case "51-5":
                    ExcelUtil<MedicalSocialSecurityDealDeductionExceptionTemplateDTO> deductionExceptionUtil = new ExcelUtil<>(MedicalSocialSecurityDealDeductionExceptionTemplateDTO.class);
                    deductionExceptionUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityDealDeductionExceptionTemplateDTO dto = new MedicalSocialSecurityDealDeductionExceptionTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保解除扣款异常模板");
                    break;
                case "51-6":
                    ExcelUtil<MedicalSocialSecurityConfirmTemplateDTO> confirmUtil = new ExcelUtil<>(MedicalSocialSecurityConfirmTemplateDTO.class);
                    confirmUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityConfirmTemplateDTO dto = new MedicalSocialSecurityConfirmTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保确认模板");
                    break;
                case "51-7":
                    ExcelUtil<MedicalSocialSecurityUpdateFilesTemplateDTO> updateFileUtil = new ExcelUtil<>(MedicalSocialSecurityUpdateFilesTemplateDTO.class);
                    updateFileUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityUpdateFilesTemplateDTO dto = new MedicalSocialSecurityUpdateFilesTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保更新附件模板");
                    break;
                case "51-8":
                    ExcelUtil<MedicalSocialSecurityRejectTemplateDTO> rejectUtil = new ExcelUtil<>(MedicalSocialSecurityRejectTemplateDTO.class);
                    rejectUtil.exportExcel(httpServletResponse, ObjectUtils.isEmpty(records) ? Lists.newArrayList() : records.stream().map(row -> {
                        MedicalSocialSecurityRejectTemplateDTO dto = new MedicalSocialSecurityRejectTemplateDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList()), "医社保驳回模板");
                    break;
                default:
                    throw new ServiceException("不支持的类型");
            }
        }
    }

    @GetMapping("/getByCustomerServiceIdAndPeriodIdAndDeliverType")
    @ApiOperation("内部接口")
    public Result<CustomerDeliver> getByCustomerServiceIdAndPeriodIdAndDeliverType(@RequestParam("periodId") Long periodId,
                                                                                   @RequestParam("customerServiceId") Long customerServiceId,
                                                                                   @RequestParam("deliverType") Integer deliverType) {
        return Result.ok(customerDeliverService.getByCustomerServiceIdAndPeriodIdAndDeliverType(periodId, customerServiceId, deliverType));
    }

    @PostMapping("/correctionsReport")
    @ApiOperation("重新申报，权限字符：customer:deliver:correctionsReport")
//    @RequiresPermissions("customer:deliver:correctionsReport")
    public Result correctionsReport(@RequestBody CustomerDeliverVO vo) {
        customerDeliverService.correctionsReport(vo);
        return Result.ok();
    }

    @PostMapping("/correctionsReportV2")
    @ApiOperation("重新申报，权限字符：customer:deliver:correctionsReport")
//    @RequiresPermissions("customer:deliver:correctionsReport")
    public Result correctionsReportV2(@RequestBody CustomerDeliverVO vo) {
        customerDeliverService.correctionsReportV2(vo);
        return Result.ok();
    }

    @PostMapping("/receiveDeliverChanged")
    @ApiOperation("接收变更，批量操作，传ids，权限字符：customer:deliver:receiveDeliverChanged")
//    @RequiresPermissions("customer:deliver:receiveDeliverChanged")
    public Result<CommonDeliverOperateDTO> receiveDeliverChanged(@RequestBody CommonIdVO vo) {
        return Result.ok(customerDeliverService.receiveDeliverChanged(vo));
    }

    @PostMapping("/correctionsReportResult")
    @ApiOperation("更正申报结果，权限字符：customer:deliver:correctionsReportResult")
//    @RequiresPermissions("customer:deliver:correctionsReportResult")
    public Result correctionsReportResult(@RequestBody CustomerDeliverReportVO vo) {
        customerDeliverService.correctionsReportResult(vo);
        return Result.ok();
    }

    @PostMapping("/supplementReportFiles")
    @ApiOperation("补充申报附件，单个操作，传id和files，权限字符：customer:deliver:supplementReportFiles")
//    @RequiresPermissions("customer:deliver:supplementReportFiles")
    public Result supplementReportFiles(@RequestBody CommonIdVO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.supplementReportFiles(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteSupplementReportFiles")
    @ApiIgnore
    public Result remoteSupplementReportFiles(@RequestBody List<RemoteSupplementReportFilesVO> voList) {
        customerDeliverService.remoteSupplementReportFiles(voList);
        return Result.ok();
    }

    @PostMapping("/remoteSupplementFiles")
    @ApiIgnore
    @InnerAuth
    public Result remoteSupplementFiles(@RequestBody RemoteSupplementReportFilesVO vo) {
        customerDeliverService.remoteSupplementFiles(vo);
        return Result.ok();
    }

    @PostMapping("/remoteSupplementAuthFiles")
    @ApiIgnore
    public Result remoteSupplementAuthFiles(@RequestBody List<RemoteSupplementReportFilesVO> voList) {
        customerDeliverService.remoteSupplementAuthFiles(voList);
        return Result.ok();
    }

    @PostMapping("/batchDeliverCheckResult")
    @ApiOperation("内部接口")
    public Result batchDeliverCheckResult(@RequestBody RemoteCustomerDeliverCheckResultVO vo) {
        customerDeliverService.batchDeliverCheckResult(vo);
        return Result.ok();
    }

    @GetMapping("/getSamePeriodPersonChangeInfo")
    @ApiOperation("获取相同账期医社保人员变动信息")
    public Result<DeliverPersonChangeInfoDTO> getSamePeriodPersonChangeInfo(@RequestParam("customerPeriodMonthId") Long customerPeriodMonthId) {
        return Result.ok(customerDeliverService.getSamePeriodPersonChangeInfo(customerPeriodMonthId));
    }

    @PostMapping("/preAuthCorrectionsAuth")
    @ApiOperation("预认证-重新认证")
    public Result preAuthCorrectionsAuth(@RequestBody CustomerDeliverVO vo) {
        customerDeliverService.preAuthCorrectionsAuth(vo);
        return Result.ok();
    }

    @PostMapping("/preAuthReceiveDeliverChanged")
    @ApiOperation("预认证-接收变更，批量操作，传ids")
    public Result<CommonDeliverOperateDTO> preAuthReceiveDeliverChanged(@RequestBody CommonIdVO vo) {
        return Result.ok(customerDeliverService.preAuthReceiveDeliverChanged(vo));
    }

    @PostMapping("/rejectDeliver")
    @ApiOperation("交付单-驳回,单个操作，传id，files，reason")
    public Result rejectDeliver(@RequestBody CommonIdVO vo) {
        customerDeliverService.rejectDeliver(vo);
        return Result.ok();
    }

    @PostMapping("/remoteReject")
    @ApiIgnore
    @InnerAuth
    public Result remoteReject(@RequestBody RemoteRejectVO vo) {
        customerDeliverService.remoteReject(vo);
        return Result.ok();
    }

    @PostMapping("/check")
    @ApiOperation("交付单-检查")
    public Result check(@RequestBody CustomerDeliverCheckVO vo) {
        customerDeliverService.check(vo);
        return Result.ok();
    }

    @PostMapping("/overDeliver")
    @ApiOperation("预认证-完结交付")
    public Result overDeliver(@RequestBody RemoteOverDeliverVO vo) {
        customerDeliverService.overDeliver(vo);
        return Result.ok();
    }

    @PostMapping("/remoteOverDeliver")
    @ApiIgnore
    @InnerAuth
    public Result remoteOverDeliver(@RequestBody RemoteOverDeliverVO vo) {
        customerDeliverService.remoteOverDeliver(vo);
        return Result.ok();
    }

    @PostMapping("/dealOverException")
    @ApiOperation("预认证-处理完结异常")
    public Result dealOverException(@RequestBody RemoteDealOverExceptionDeliverVO vo) {
        customerDeliverService.dealOverException(vo);
        return Result.ok();
    }

    @PostMapping("/remoteDealOverException")
    @ApiIgnore
    @InnerAuth
    public Result remoteDealOverException(@RequestBody RemoteDealOverExceptionDeliverVO vo) {
        customerDeliverService.remoteDealOverException(vo);
        return Result.ok();
    }

    @GetMapping("/getPreAuthRemind")
    @ApiOperation("获取认证提醒")
    public Result<String> getPreAuthRemind(@RequestParam("id") @ApiParam("交付单id") Long id) {
        return Result.ok(customerDeliverService.getPreAuthRemind(id));
    }

    @PostMapping("/getByPeriodIdsAndDeliverTypes")
    @ApiIgnore
    public Result<List<CustomerDeliver>> getByPeriodIdsAndDeliverTypes(@RequestBody RemoteDeliverSearchVO vo) {
        return Result.ok(customerDeliverService.getByPeriodIdsAndDeliverTypes(vo));
    }

    @PostMapping("/commentDeliver")
    @ApiOperation("交付单评论")
    public Result commentDeliver(@RequestBody CustomerDeliverCommentVO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.commentDeliver(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/modifyMattersNotes")
    @ApiOperation("修改事项备忘")
    public Result modifyMattersNotes(@RequestBody MattersNotesModifyVO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.modifyMattersNotes(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/commonSubmit")
    @ApiOperation("统一提交交付单（单个提交和批量提交都改成这个，因为批量操作会返回操作结果反馈，所以单个操作和批量操作传参需要区分一下）")
    public Result<CommonDeliverOperateDTO> commonSubmit(@RequestBody SubmitOrReBackVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.commonSubmit(vo, deptId));
    }

    @PostMapping("/commonReBack")
    @ApiOperation("统一退回交付单（单个退回和批量退回都改成这个，因为批量操作会返回操作结果反馈，所以单个操作和批量操作传参需要区分一下）")
    public Result<CommonDeliverOperateDTO> commonReBack(@RequestBody SubmitOrReBackVO vo, @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.commonReBack(vo, deptId));
    }

    @ApiOperation("申报（当前针对汇算和年报，后续可能会都用这个）")
    @PostMapping("/reportV2")
    public Result reportV2(@RequestBody ReportV2VO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.reportV2(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteReportV2")
    @ApiIgnore
    public Result remoteReportV2(@RequestBody ReportV2VO vo) {
        customerDeliverService.remoteReportV2(vo, vo.getDeptId());
        return Result.ok();
    }

    @ApiOperation("扣款（当前针对汇算和年报，后续可能会都用这个）")
    @PostMapping("/deductionV2")
    public Result deductionV2(@RequestBody DeductionV2VO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.deductionV2(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteDeductionV2")
    @ApiIgnore
    public Result remoteDeductionV2(@RequestBody DeductionV2VO vo) {
        customerDeliverService.remoteDeductionV2(vo, vo.getDeptId());
        return Result.ok();
    }

    @ApiOperation("补充附件（当前针对汇算和年报，后续可能会都用这个）")
    @PostMapping("/supplementReportFilesV2")
    public Result supplementReportFilesV2(@RequestBody SupplementReportFilesV2VO vo, @RequestHeader("deptId") Long deptId) {
        customerDeliverService.supplementReportFilesV2(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/remoteSupplementReportFilesV2")
    @ApiIgnore
    public Result remoteSupplementReportFilesV2(@RequestBody SupplementReportFilesV2VO vo) {
        customerDeliverService.remoteSupplementReportFilesV2(vo, vo.getDeptId());
        return Result.ok();
    }

    @PostMapping("/remoteUpdateTaxReportTotalAmount")
    @ApiIgnore
    public Result remoteUpdateTaxReportTotalAmount(@RequestBody RemoteUpdateTaxReportTotalAmountVO vo) {
        customerDeliverService.remoteUpdateTaxReportTotalAmount(vo, vo.getDeptId());
        return Result.ok();
    }

    @PostMapping("/autoReport")
    @ApiOperation("自动申报")
    public Result<CommonDeliverOperateDTO> autoReport(@RequestBody List<Long> ids,
                                                      @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.autoReport(ids, deptId));
    }

    @PostMapping("/autoDeduction")
    @ApiOperation("自动扣款")
    public Result<CommonDeliverOperateDTO> autoDeduction(@RequestBody List<Long> ids,
                                                         @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.autoDeduction(ids, deptId));
    }

    @PostMapping("/autoCheck")
    @ApiOperation("自动检查")
    public Result<CommonDeliverOperateDTO> autoCheck(@RequestBody List<Long> ids,
                                                         @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.autoCheck(ids, deptId));
    }

    @PostMapping("/modifyDdl")
    @ApiOperation("批量修改ddl，传选中的id值")
    public Result<CommonDeliverOperateDTO> modifyDdl(@RequestBody CustomerDeliverModifyDdlVO vo,
                                                     @RequestHeader("deptId") Long deptId) {
        return Result.ok(customerDeliverService.modifyDdl(vo, deptId));
    }

    @GetMapping("/getDeliverFiles")
    @ApiIgnore
    @InnerAuth
    public Result<List<CommonFileVO>> getPersonTaxDeliverFiles(@RequestParam("taxNumber") String taxNumber,
                                                      @RequestParam("period") Integer period,
                                                      @RequestParam("deptId") Long deptId) {
        return Result.ok(customerDeliverService.getPersonTaxDeliverFiles(taxNumber, period, deptId));
    }
}
