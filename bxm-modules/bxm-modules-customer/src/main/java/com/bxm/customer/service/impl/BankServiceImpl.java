package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.Bank;
import com.bxm.customer.mapper.BankMapper;
import com.bxm.customer.service.IBankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
public class BankServiceImpl extends ServiceImpl<BankMapper, Bank> implements IBankService
{
    @Autowired
    private BankMapper bankMapper;

    @Override
    public List<Bank> bankSelect(String bankName) {
        return list(new LambdaQueryWrapper<Bank>().like(!StringUtils.isEmpty(bankName), Bank::getBankName, bankName));
    }

    @Override
    public List<String> getAllBankNames() {
        return list(new LambdaQueryWrapper<Bank>().select(Bank::getBankName)).stream().map(Bank::getBankName).collect(Collectors.toList());
    }
}
