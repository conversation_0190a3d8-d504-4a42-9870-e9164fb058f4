package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.dto.bill.BillDTO;
import com.bxm.customer.domain.dto.bill.BillDetailDTO;
import com.bxm.customer.domain.dto.bill.BillReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewOrderDTO;
import com.bxm.customer.domain.vo.bill.RejectBillVO;
import com.bxm.customer.domain.vo.bill.RemovePushVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderPushVO;
import com.bxm.customer.service.BillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/bill")
@Api(tags = "账单相关")
public class BillController {

    @Autowired
    private BillService billService;

    @GetMapping("/billList")
    @ApiOperation("账单列表")
    public Result<IPage<BillDTO>> billList(@RequestHeader("deptId") Long deptId,
                                           @RequestParam(value = "billTitle", required = false) @ApiParam("账单标题") String billTitle,
                                           @RequestParam(value = "businessDeptId", required = false) @ApiParam("业务组织id") Long businessDeptId,
                                           @RequestParam(value = "status", required = false) @ApiParam("状态,0-已推送待确认，1-已驳回，2-已撤回，3-已确认") Integer status,
                                           @RequestParam(value = "createTimeStart", required = false) @ApiParam("创建时间开始") String createTimeStart,
                                           @RequestParam(value = "createTimeEnd", required = false) @ApiParam("创建时间结束") String createTimeEnd,
                                           @RequestParam(value = "pageNum", defaultValue = "1") @ApiParam("当前页") Integer pageNum,
                                           @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页条数") Integer pageSize) {
        return Result.ok(billService.billList(deptId, billTitle, businessDeptId, status, createTimeStart, createTimeEnd, pageNum, pageSize));
    }

    @PostMapping("/billListExport")
    @ApiOperation("账单列表导出")
    public void billListExport(HttpServletResponse response, @RequestHeader("deptId") Long deptId,
                               @RequestParam(value = "billTitle", required = false) @ApiParam("账单标题") String billTitle,
                               @RequestParam(value = "businessDeptId", required = false) @ApiParam("业务组织id") Long businessDeptId,
                               @RequestParam(value = "status", required = false) @ApiParam("状态,0-已推送待确认，1-已驳回，2-已撤回，3-已确认") Integer status,
                               @RequestParam(value = "createTimeStart", required = false) @ApiParam("创建时间开始") String createTimeStart,
                               @RequestParam(value = "createTimeEnd", required = false) @ApiParam("创建时间结束") String createTimeEnd) {
        List<BillDTO> export = billService.billList(deptId, billTitle, businessDeptId, status, createTimeStart, createTimeEnd, 1, -1).getRecords();
        ExcelUtil<BillDTO> util = new ExcelUtil<>(BillDTO.class);
        util.exportExcel(response, export, "账单列表");
    }

    @GetMapping("/billDetail")
    @ApiOperation("账单详情")
    public Result<BillDetailDTO> billDetail(@RequestParam("id") @ApiParam("账单id") Long id) {
        return Result.ok(billService.billDetail(id));
    }

    @PostMapping("/delete")
    @ApiOperation("删除账单，单个删除，传id")
    public Result delete(@RequestBody CommonIdVO vo) {
        billService.delete(vo);
        return Result.ok();
    }

    @PostMapping("/rePushReview")
    @ApiOperation("重新推送预览（获取各业务公司结算单情况），单个操作，传id（账单id）")
    public Result<List<SettlementPushReviewDTO>> rePushReview(@RequestBody CommonIdVO vo) {
        return Result.ok(billService.rePushReview(vo));
    }

    @PostMapping("/confirmRePush")
    @ApiOperation("确认重新推送")
    public Result confirmRePush(@RequestBody SettlementOrderPushVO vo) {
        billService.confirmRePush(vo);
        return Result.ok();
    }

    @PostMapping("/removePush")
    @ApiOperation("移除推送")
    public Result removePush(@RequestBody RemovePushVO vo) {
        billService.removePush(vo);
        return Result.ok();
    }

    @PostMapping("/revokeBill")
    @ApiOperation("撤回账单，单个操作，传id（账单id）")
    public Result revokeBill(@RequestBody CommonIdVO vo) {
        billService.revokeBill(vo);
        return Result.ok();
    }

    @GetMapping("/settlementListByBillId")
    @ApiOperation("驳回账单获取结算单列表")
    public Result<List<SettlementPushReviewOrderDTO>> settlementListByBillId(@RequestParam("billId") @ApiParam("账单id") Long billId) {
        return Result.ok(billService.settlementListByBillId(billId));
    }

    @PostMapping("/rejectBill")
    @ApiOperation("驳回账单")
    public Result rejectBill(@RequestBody RejectBillVO vo) {
        billService.rejectBill(vo);
        return Result.ok();
    }

    @PostMapping("/confirmBill")
    @ApiOperation("确认账单，单个操作，传id（账单id）")
    public Result confirmBill(@RequestBody CommonIdVO vo) {
        billService.confirmBill(vo);
        return Result.ok();
    }

    @GetMapping("/billReview")
    @ApiOperation("导出账单文件的h5接口")
    public Result<BillReviewDTO> billReview(@RequestParam("billId") @ApiParam("账单id") Long billId) {
        return Result.ok(billService.billReview(billId));
    }
}
