package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerBankAccountNumberSearchVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import org.apache.ibatis.annotations.Param;

/**
 * 服务银行账号Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface CustomerServiceBankAccountMapper extends BaseMapper<CustomerServiceBankAccount>
{
    /**
     * 查询服务银行账号
     * 
     * @param id 服务银行账号主键
     * @return 服务银行账号
     */
    public CustomerServiceBankAccount selectCustomerServiceBankAccountById(Long id);

    /**
     * 查询服务银行账号列表
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 服务银行账号集合
     */
    public List<CustomerServiceBankAccount> selectCustomerServiceBankAccountList(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 新增服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    public int insertCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 修改服务银行账号
     * 
     * @param customerServiceBankAccount 服务银行账号
     * @return 结果
     */
    public int updateCustomerServiceBankAccount(CustomerServiceBankAccount customerServiceBankAccount);

    /**
     * 删除服务银行账号
     * 
     * @param id 服务银行账号主键
     * @return 结果
     */
    public int deleteCustomerServiceBankAccountById(Long id);

    /**
     * 批量删除服务银行账号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceBankAccountByIds(Long[] ids);

    /**
     * 获取集团下相同银行账号的数量
     * @param businessTopDeptId
     * @param account
     * @param id
     * @return
     */
    Integer getBusinessTopDeptBankAccountCount(@Param("businessTopDeptId") Long businessTopDeptId, @Param("account") String account, @Param("id") Long id, @Param("creditCode") String creditCode);

    List<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumbers(@Param("vo") RemoteCustomerBankAccountNumberSearchVO vo);

    RemoteCustomerBankAccountDTO getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(@Param("bankAccountNumber") String bankAccountNumber,
                                                                                              @Param("businessTopDeptId") Long businessTopDeptId);
}
