package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.SettlementOrderData;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataCountDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;

import java.util.List;

/**
 * 结算单关联数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface ISettlementOrderDataService extends IService<SettlementOrderData>
{
    /**
     * 查询结算单关联数据
     * 
     * @param id 结算单关联数据主键
     * @return 结算单关联数据
     */
    public SettlementOrderData selectSettlementOrderDataById(Long id);

    /**
     * 查询结算单关联数据列表
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结算单关联数据集合
     */
    public List<SettlementOrderData> selectSettlementOrderDataList(SettlementOrderData settlementOrderData);

    /**
     * 新增结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    public int insertSettlementOrderData(SettlementOrderData settlementOrderData);

    /**
     * 修改结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    public int updateSettlementOrderData(SettlementOrderData settlementOrderData);

    /**
     * 批量删除结算单关联数据
     * 
     * @param ids 需要删除的结算单关联数据主键集合
     * @return 结果
     */
    public int deleteSettlementOrderDataByIds(Long[] ids);

    /**
     * 删除结算单关联数据信息
     * 
     * @param id 结算单关联数据主键
     * @return 结果
     */
    public int deleteSettlementOrderDataById(Long id);

    void removeAndSaveByBatchNoAndBusinessId(String batchNo, Long businessDeptId, Long settlementOrderId, Integer settlementType, Boolean isSupplement);

    IPage<SettlementOrderDataDTO> settlementOrderDataListBySettlementOrderId(SettlementOrderDataSearchVO vo);

    List<SettlementOrderDataCountDTO> selectBatchSettlementOrderDataCount(List<Long> settlementOrderIds);
}
