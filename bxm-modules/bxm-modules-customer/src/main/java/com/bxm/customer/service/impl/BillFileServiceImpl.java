package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BillFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.BillFile;
import com.bxm.customer.mapper.BillFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.IBillFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算单附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Service
public class BillFileServiceImpl extends ServiceImpl<BillFileMapper, BillFile> implements IBillFileService
{
    @Autowired
    private BillFileMapper billFileMapper;

    @Autowired
    private FileService fileService;

    @Override
    public List<CommonFileVO> selectBillFileByBillId(Long billId, Integer fileType) {
        if (Objects.isNull(billId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BillFile>()
                .eq(BillFile::getBillId, billId)
                .eq(!Objects.isNull(fileType), BillFile::getFileType, fileType)
                .eq(BillFile::getIsDel, false)).stream().map(billFile -> CommonFileVO.builder().fileUrl(billFile.getFileUrl())
                .fileName(billFile.getFileName()).fullFileUrl(fileService.getFullFileUrl(billFile.getFileUrl())).build()).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void removeAndSaveNewFile(Long billId, List<CommonFileVO> files, Integer fileType) {
        update(new LambdaUpdateWrapper<BillFile>().eq(BillFile::getBillId, billId)
                .eq(BillFile::getFileType, fileType).eq(BillFile::getIsDel, false)
                .set(BillFile::getIsDel, true));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(f -> new BillFile().setBillId(billId).setFileName(f.getFileName()).setFileUrl(f.getFileUrl()).setFileType(BillFileType.CREATE.getCode())).collect(Collectors.toList()));
        }
    }
}
