package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.MaterialDeliverFileInventoryFile;

/**
 * 文件清单附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-12
 */
@Mapper
public interface MaterialDeliverFileInventoryFileMapper extends BaseMapper<MaterialDeliverFileInventoryFile>
{
    /**
     * 查询文件清单附件
     * 
     * @param id 文件清单附件主键
     * @return 文件清单附件
     */
    public MaterialDeliverFileInventoryFile selectMaterialDeliverFileInventoryFileById(Long id);

    /**
     * 查询文件清单附件列表
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 文件清单附件集合
     */
    public List<MaterialDeliverFileInventoryFile> selectMaterialDeliverFileInventoryFileList(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 新增文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    public int insertMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 修改文件清单附件
     * 
     * @param materialDeliverFileInventoryFile 文件清单附件
     * @return 结果
     */
    public int updateMaterialDeliverFileInventoryFile(MaterialDeliverFileInventoryFile materialDeliverFileInventoryFile);

    /**
     * 删除文件清单附件
     * 
     * @param id 文件清单附件主键
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryFileById(Long id);

    /**
     * 批量删除文件清单附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverFileInventoryFileByIds(Long[] ids);
}
