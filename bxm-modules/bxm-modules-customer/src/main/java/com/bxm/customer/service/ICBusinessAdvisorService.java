package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.AdvisorBusinessType;
import com.bxm.customer.domain.CBusinessAdvisor;

/**
 * 顾问Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface ICBusinessAdvisorService extends IService<CBusinessAdvisor>
{
    /**
     * 查询顾问
     * 
     * @param id 顾问主键
     * @return 顾问
     */
    public CBusinessAdvisor selectCBusinessAdvisorById(Long id);

    /**
     * 查询顾问列表
     * 
     * @param cBusinessAdvisor 顾问
     * @return 顾问集合
     */
    public List<CBusinessAdvisor> selectCBusinessAdvisorList(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 新增顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    public int insertCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 修改顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    public int updateCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 批量删除顾问
     * 
     * @param ids 需要删除的顾问主键集合
     * @return 结果
     */
    public int deleteCBusinessAdvisorByIds(Long[] ids);

    /**
     * 删除顾问信息
     * 
     * @param id 顾问主键
     * @return 结果
     */
    public int deleteCBusinessAdvisorById(Long id);

    Long getAdvisorByBusinessIdAndBusinessType(Long businessId, AdvisorBusinessType advisorBusinessType);
}
