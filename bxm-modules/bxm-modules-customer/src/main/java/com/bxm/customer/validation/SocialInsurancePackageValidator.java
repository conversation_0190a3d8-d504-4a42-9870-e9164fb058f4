package com.bxm.customer.validation;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.alibaba.fastjson.JSON;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 社保套餐信息验证器
 *
 * 支持验证 SocialInsuranceVO 对象和 JSON 字符串两种格式
 * 提供向后兼容性支持
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class SocialInsurancePackageValidator implements ConstraintValidator<SocialInsurancePackage, Object> {

    private boolean allowEmpty;

    @Override
    public void initialize(SocialInsurancePackage constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && value == null) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && value == null) {
            return false;
        }

        try {
            // 处理 SocialInsuranceVO 对象
            if (value instanceof SocialInsuranceVO) {
                SocialInsuranceVO socialInsurance = (SocialInsuranceVO) value;
                return socialInsurance.isValid();
            }

            // 处理 JSON 字符串（向后兼容）
            if (value instanceof String) {
                String jsonStr = (String) value;

                // 如果允许为空且字符串为空，则验证通过
                if (allowEmpty && StringUtils.isEmpty(jsonStr)) {
                    return true;
                }

                // 如果不允许为空且字符串为空，则验证失败
                if (!allowEmpty && StringUtils.isEmpty(jsonStr)) {
                    return false;
                }

                // 尝试解析为 SocialInsuranceVO 对象进行验证
                SocialInsuranceVO socialInsurance = JSON.parseObject(jsonStr, SocialInsuranceVO.class);
                return socialInsurance != null && socialInsurance.isValid();
            }

            // 不支持的类型
            return false;
        } catch (Exception e) {
            return false;
        }
    }
}
