package com.bxm.customer.domain.vo.newCustomer;

import com.bxm.customer.domain.dto.TagDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerCreateVO {

    @ApiModelProperty("新户流转ID，编辑时传入")
    private Long id;

    @ApiModelProperty("基础信息")
    private NewCustomerCreateBaseInfoVO baseInfo;

    @ApiModelProperty("标签,只传选中的标签")
    private List<TagDTO> tags;
}
