package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.vo.customerServiceOperate.AddCustomerServiceOperateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceOperateMapper;
import com.bxm.customer.domain.CustomerServiceOperate;
import com.bxm.customer.service.ICustomerServiceOperateService;

/**
 * 客户服务操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class CustomerServiceOperateServiceImpl extends ServiceImpl<CustomerServiceOperateMapper, CustomerServiceOperate> implements ICustomerServiceOperateService
{
    @Autowired
    private CustomerServiceOperateMapper customerServiceOperateMapper;

    /**
     * 查询客户服务操作记录
     * 
     * @param id 客户服务操作记录主键
     * @return 客户服务操作记录
     */
    @Override
    public CustomerServiceOperate selectCustomerServiceOperateById(Long id)
    {
        return customerServiceOperateMapper.selectCustomerServiceOperateById(id);
    }

    /**
     * 查询客户服务操作记录列表
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 客户服务操作记录
     */
    @Override
    public List<CustomerServiceOperate> selectCustomerServiceOperateList(CustomerServiceOperate customerServiceOperate)
    {
        return customerServiceOperateMapper.selectCustomerServiceOperateList(customerServiceOperate);
    }

    /**
     * 新增客户服务操作记录
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 结果
     */
    @Override
    public int insertCustomerServiceOperate(CustomerServiceOperate customerServiceOperate)
    {
        customerServiceOperate.setCreateTime(DateUtils.getNowDate());
        return customerServiceOperateMapper.insertCustomerServiceOperate(customerServiceOperate);
    }

    /**
     * 修改客户服务操作记录
     * 
     * @param customerServiceOperate 客户服务操作记录
     * @return 结果
     */
    @Override
    public int updateCustomerServiceOperate(CustomerServiceOperate customerServiceOperate)
    {
        customerServiceOperate.setUpdateTime(DateUtils.getNowDate());
        return customerServiceOperateMapper.updateCustomerServiceOperate(customerServiceOperate);
    }

    /**
     * 批量删除客户服务操作记录
     * 
     * @param ids 需要删除的客户服务操作记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceOperateByIds(Long[] ids)
    {
        return customerServiceOperateMapper.deleteCustomerServiceOperateByIds(ids);
    }

    /**
     * 删除客户服务操作记录信息
     * 
     * @param id 客户服务操作记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceOperateById(Long id)
    {
        return customerServiceOperateMapper.deleteCustomerServiceOperateById(id);
    }

    @Override
    public void addSimple(AddCustomerServiceOperateVO vo) {

    }

    @Override
    public void addSimpleBatch(List<AddCustomerServiceOperateVO> vo) {

    }
}
