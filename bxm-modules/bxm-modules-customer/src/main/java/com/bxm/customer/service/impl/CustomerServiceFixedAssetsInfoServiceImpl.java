package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceFixedAssetsInfoMapper;
import com.bxm.customer.domain.CustomerServiceFixedAssetsInfo;
import com.bxm.customer.service.ICustomerServiceFixedAssetsInfoService;

/**
 * 客户服务固定资产信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceFixedAssetsInfoServiceImpl extends ServiceImpl<CustomerServiceFixedAssetsInfoMapper, CustomerServiceFixedAssetsInfo> implements ICustomerServiceFixedAssetsInfoService
{
    @Autowired
    private CustomerServiceFixedAssetsInfoMapper customerServiceFixedAssetsInfoMapper;

    /**
     * 查询客户服务固定资产信息
     * 
     * @param id 客户服务固定资产信息主键
     * @return 客户服务固定资产信息
     */
    @Override
    public CustomerServiceFixedAssetsInfo selectCustomerServiceFixedAssetsInfoById(Long id)
    {
        return customerServiceFixedAssetsInfoMapper.selectCustomerServiceFixedAssetsInfoById(id);
    }

    /**
     * 查询客户服务固定资产信息列表
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 客户服务固定资产信息
     */
    @Override
    public List<CustomerServiceFixedAssetsInfo> selectCustomerServiceFixedAssetsInfoList(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo)
    {
        return customerServiceFixedAssetsInfoMapper.selectCustomerServiceFixedAssetsInfoList(customerServiceFixedAssetsInfo);
    }

    /**
     * 新增客户服务固定资产信息
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceFixedAssetsInfo(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo)
    {
        return customerServiceFixedAssetsInfoMapper.insertCustomerServiceFixedAssetsInfo(customerServiceFixedAssetsInfo);
    }

    /**
     * 修改客户服务固定资产信息
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceFixedAssetsInfo(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo)
    {
        return customerServiceFixedAssetsInfoMapper.updateCustomerServiceFixedAssetsInfo(customerServiceFixedAssetsInfo);
    }

    /**
     * 批量删除客户服务固定资产信息
     * 
     * @param ids 需要删除的客户服务固定资产信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceFixedAssetsInfoByIds(Long[] ids)
    {
        return customerServiceFixedAssetsInfoMapper.deleteCustomerServiceFixedAssetsInfoByIds(ids);
    }

    /**
     * 删除客户服务固定资产信息信息
     * 
     * @param id 客户服务固定资产信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceFixedAssetsInfoById(Long id)
    {
        return customerServiceFixedAssetsInfoMapper.deleteCustomerServiceFixedAssetsInfoById(id);
    }
}
