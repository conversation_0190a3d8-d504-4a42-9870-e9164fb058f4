package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * 客户发票信息对象 c_invoice_data
 * 
 * <AUTHOR>
 * @date 2024-10-23
 */
@Data
@ApiModel("客户发票信息对象")
@Accessors(chain = true)
@TableName("c_open_api_invoice_data")
public class OpenApiInvoiceData extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 同步记录id
     */
    @Excel(name = "同步记录id")
    @TableField("syc_record_id")
    @ApiModelProperty(value = "同步记录id")
    private Long sycRecordId;

    /**
     * 同步客户id
     */
    @Excel(name = "同步客户id")
    @TableField("sync_customer_id")
    @ApiModelProperty(value = "同步客户id")
    private Long syncCustomerId;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private String customerId;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 纳税人资格 */
    @Excel(name = "纳税人资格")
    @TableField("taxpayer_qualification")
    @ApiModelProperty(value = "纳税人资格")
    private String taxpayerQualification;

    /** 开票期起 */
    @Excel(name = "开票期起")
    @TableField("invoice_start_period")
    @ApiModelProperty(value = "开票期起")
    private String invoiceStartPeriod;

    /** 开票期止 */
    @Excel(name = "开票期止")
    @TableField("invoice_end_period")
    @ApiModelProperty(value = "开票期止")
    private String invoiceEndPeriod;

    /** 销项不含税金额合计 */
    @Excel(name = "销项不含税金额合计")
    @TableField("output_amount_excluding_tax")
    @ApiModelProperty(value = "销项不含税金额合计")
    private String outputAmountExcludingTax;

    /** 销项税额合计 */
    @Excel(name = "销项税额合计")
    @TableField("output_tax_amount")
    @ApiModelProperty(value = "销项税额合计")
    private String outputTaxAmount;

    /** 销项价税合计 */
    @Excel(name = "销项价税合计")
    @TableField("output_total_amount")
    @ApiModelProperty(value = "销项价税合计")
    private String outputTotalAmount;

    /** 销项开票张数 */
    @Excel(name = "销项开票张数")
    @TableField("output_invoice_count")
    @ApiModelProperty(value = "销项开票张数")
    private String outputInvoiceCount;

    /** 销项发票列表 */
    @Excel(name = "销项发票列表")
    @TableField("output_invoice_list")
    @ApiModelProperty(value = "销项发票列表")
    private String outputInvoiceList;

    /** 进项不含税金额合计 */
    @Excel(name = "进项不含税金额合计")
    @TableField("input_amount_excluding_tax")
    @ApiModelProperty(value = "进项不含税金额合计")
    private String inputAmountExcludingTax;

    /** 进项税额合计 */
    @Excel(name = "进项税额合计")
    @TableField("input_tax_amount")
    @ApiModelProperty(value = "进项税额合计")
    private String inputTaxAmount;

    /** 进项价税合计 */
    @Excel(name = "进项价税合计")
    @TableField("input_total_amount")
    @ApiModelProperty(value = "进项价税合计")
    private String inputTotalAmount;

    /** 进项发票张数 */
    @Excel(name = "进项发票张数")
    @TableField("input_invoice_count")
    @ApiModelProperty(value = "进项发票张数")
    private String inputInvoiceCount;

    /** 进项发票列表 */
    @Excel(name = "进项发票列表")
    @TableField("input_invoice_list")
    @ApiModelProperty(value = "进项发票列表")
    private String inputInvoiceList;

    /** 截止月发票下载日期 */
    @Excel(name = "截止月发票下载日期")
    @TableField("last_download_date")
    @ApiModelProperty(value = "截止月发票下载日期")
    private String lastDownloadDate;
}
