package com.bxm.customer.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "season.excess")
public class SeasonExcessProperties
{

    private BigDecimal yellowLevel;

    private BigDecimal redLevel;

    public BigDecimal getYellowLevel() {
        return yellowLevel;
    }

    public void setYellowLevel(BigDecimal yellowLevel) {
        this.yellowLevel = yellowLevel;
    }

    public BigDecimal getRedLevel() {
        return redLevel;
    }

    public void setRedLevel(BigDecimal redLevel) {
        this.redLevel = redLevel;
    }
}
