package com.bxm.customer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument;
import com.bxm.customer.service.ICustomerServiceDocHandoverTaxInstrumentService;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.page.TableDataInfo;

/**
 * 材料交接票据Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@RestController
@RequestMapping("/handoverTaxInstrument")
@Api(tags = "材料交接票据")
public class CustomerServiceDocHandoverTaxInstrumentController extends BaseController
{
    @Autowired
    private ICustomerServiceDocHandoverTaxInstrumentService customerServiceDocHandoverTaxInstrumentService;

    /**
     * 查询材料交接票据列表
     */
    @RequiresPermissions("customer:instrument:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询材料交接票据列表", notes = "查询材料交接票据列表")
    public TableDataInfo list(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        startPage();
        List<CustomerServiceDocHandoverTaxInstrument> list = customerServiceDocHandoverTaxInstrumentService.selectCustomerServiceDocHandoverTaxInstrumentList(customerServiceDocHandoverTaxInstrument);
        return getDataTable(list);
    }

    /**
     * 导出材料交接票据列表
     */
    @RequiresPermissions("customer:instrument:export")
    @Log(title = "材料交接票据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出材料交接票据列表", notes = "导出材料交接票据列表")
    public void export(HttpServletResponse response, CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        List<CustomerServiceDocHandoverTaxInstrument> list = customerServiceDocHandoverTaxInstrumentService.selectCustomerServiceDocHandoverTaxInstrumentList(customerServiceDocHandoverTaxInstrument);
        ExcelUtil<CustomerServiceDocHandoverTaxInstrument> util = new ExcelUtil<CustomerServiceDocHandoverTaxInstrument>(CustomerServiceDocHandoverTaxInstrument.class);
        util.exportExcel(response, list, "材料交接票据数据");
    }

    /**
     * 获取材料交接票据详细信息
     */
    @RequiresPermissions("customer:instrument:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取材料交接票据详细信息", notes = "获取材料交接票据详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(customerServiceDocHandoverTaxInstrumentService.selectCustomerServiceDocHandoverTaxInstrumentById(id));
    }

    /**
     * 新增材料交接票据
     */
    @RequiresPermissions("customer:instrument:add")
    @Log(title = "材料交接票据", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增材料交接票据", notes = "新增材料交接票据")
    public AjaxResult add(@RequestBody CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        return toAjax(customerServiceDocHandoverTaxInstrumentService.insertCustomerServiceDocHandoverTaxInstrument(customerServiceDocHandoverTaxInstrument));
    }

    /**
     * 修改材料交接票据
     */
    @RequiresPermissions("customer:instrument:edit")
    @Log(title = "材料交接票据", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改材料交接票据", notes = "修改材料交接票据")
    public AjaxResult edit(@RequestBody CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        return toAjax(customerServiceDocHandoverTaxInstrumentService.updateCustomerServiceDocHandoverTaxInstrument(customerServiceDocHandoverTaxInstrument));
    }

    /**
     * 删除材料交接票据
     */
    @RequiresPermissions("customer:instrument:remove")
    @Log(title = "材料交接票据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除材料交接票据", notes = "删除材料交接票据")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(customerServiceDocHandoverTaxInstrumentService.deleteCustomerServiceDocHandoverTaxInstrumentByIds(ids));
    }
}
