package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.BusinessTask;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.OpenApiCheckFileRecord;
import com.bxm.thirdpart.api.domain.CheckFilesVO;

/**
 * 检验文件定时轮询Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-23
 */
public interface IOpenApiCheckFileRecordService extends IService<OpenApiCheckFileRecord>
{
    /**
     * 查询检验文件定时轮询
     * 
     * @param id 检验文件定时轮询主键
     * @return 检验文件定时轮询
     */
    public OpenApiCheckFileRecord selectOpenApiCheckFileRecordById(Long id);

    /**
     * 查询检验文件定时轮询列表
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 检验文件定时轮询集合
     */
    public List<OpenApiCheckFileRecord> selectOpenApiCheckFileRecordList(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 新增检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    public int insertOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 修改检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    public int updateOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 批量删除检验文件定时轮询
     * 
     * @param ids 需要删除的检验文件定时轮询主键集合
     * @return 结果
     */
    public int deleteOpenApiCheckFileRecordByIds(Long[] ids);

    /**
     * 删除检验文件定时轮询信息
     * 
     * @param id 检验文件定时轮询主键
     * @return 结果
     */
    public int deleteOpenApiCheckFileRecordById(Long id);

    /**
     * 创建定时检验文件任务
     * @param customerServiceCashierAccounting 交付单
     * @param businessTask 任务单
     * @param checkFileVO 校验任务参数
     * @param afterTime 过多久后进行查询
     */
    void createCheckFileTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, BusinessTask businessTask, CheckFilesVO checkFileVO, Long afterTime);

    void createCheckFileTask(Long customerServiceCashierAccountingId, Long businessTaskId, CheckFilesVO checkFileVO, Long afterTime);

    void closeCheckFileTask(Long searchTaskId);

    void overTimeClose(Long searchTaskId);

    void continueCheckFileTask(Long taskId);
}
