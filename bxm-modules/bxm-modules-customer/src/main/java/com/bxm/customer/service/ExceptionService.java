package com.bxm.customer.service;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.ErrorCodeEnum;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.system.api.RemoteLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExceptionService {

    @Autowired
    private RemoteLogService remoteLogService;

    public void throwConfigException(ErrorCodeEnum errorCode) {
        String errorMsg = remoteLogService.getErrorMsgByErrorCode(errorCode.getCode(), SecurityConstants.INNER).getDataThrowException();
        if (StringUtils.isEmpty(errorMsg)) {
            throw new ServiceException("未知错误");
        } else {
            throw new ServiceException(errorMsg);
        }
    }

    public String getErrorMsgByErrorCode(ErrorCodeEnum errorCode) {
        String errorMsg = remoteLogService.getErrorMsgByErrorCode(errorCode.getCode(), SecurityConstants.INNER).getDataThrowException();
        if (StringUtils.isEmpty(errorMsg)) {
            return "未知错误";
        } else {
            return errorMsg;
        }
    }
}
