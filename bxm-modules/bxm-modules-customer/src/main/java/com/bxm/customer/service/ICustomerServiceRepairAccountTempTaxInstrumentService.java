package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceRepairAccountTempTaxInstrument;

/**
 * 补账临时的 材料交接票据Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface ICustomerServiceRepairAccountTempTaxInstrumentService extends IService<CustomerServiceRepairAccountTempTaxInstrument>
{
    /**
     * 查询补账临时的 材料交接票据
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 补账临时的 材料交接票据
     */
    public CustomerServiceRepairAccountTempTaxInstrument selectCustomerServiceRepairAccountTempTaxInstrumentById(Long id);

    /**
     * 查询补账临时的 材料交接票据列表
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 补账临时的 材料交接票据集合
     */
    public List<CustomerServiceRepairAccountTempTaxInstrument> selectCustomerServiceRepairAccountTempTaxInstrumentList(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 新增补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    public int insertCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 修改补账临时的 材料交接票据
     * 
     * @param customerServiceRepairAccountTempTaxInstrument 补账临时的 材料交接票据
     * @return 结果
     */
    public int updateCustomerServiceRepairAccountTempTaxInstrument(CustomerServiceRepairAccountTempTaxInstrument customerServiceRepairAccountTempTaxInstrument);

    /**
     * 批量删除补账临时的 材料交接票据
     * 
     * @param ids 需要删除的补账临时的 材料交接票据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentByIds(Long[] ids);

    /**
     * 删除补账临时的 材料交接票据信息
     * 
     * @param id 补账临时的 材料交接票据主键
     * @return 结果
     */
    public int deleteCustomerServiceRepairAccountTempTaxInstrumentById(Long id);
}
