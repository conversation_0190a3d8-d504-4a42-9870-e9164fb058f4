package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceDocHandoverFile;

/**
 * 材料、资料 附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface CustomerServiceDocHandoverFileMapper extends BaseMapper<CustomerServiceDocHandoverFile>
{
    /**
     * 查询材料、资料 附件
     * 
     * @param id 材料、资料 附件主键
     * @return 材料、资料 附件
     */
    public CustomerServiceDocHandoverFile selectCustomerServiceDocHandoverFileById(Long id);

    /**
     * 查询材料、资料 附件列表
     * 
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 材料、资料 附件集合
     */
    public List<CustomerServiceDocHandoverFile> selectCustomerServiceDocHandoverFileList(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 新增材料、资料 附件
     * 
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    public int insertCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 修改材料、资料 附件
     * 
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    public int updateCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 删除材料、资料 附件
     * 
     * @param id 材料、资料 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverFileById(Long id);

    /**
     * 批量删除材料、资料 附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverFileByIds(Long[] ids);
}
