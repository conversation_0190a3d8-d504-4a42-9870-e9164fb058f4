package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceDocHandoverTaxInstrumentMapper;
import com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument;
import com.bxm.customer.service.ICustomerServiceDocHandoverTaxInstrumentService;

/**
 * 材料交接票据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
public class CustomerServiceDocHandoverTaxInstrumentServiceImpl extends ServiceImpl<CustomerServiceDocHandoverTaxInstrumentMapper, CustomerServiceDocHandoverTaxInstrument> implements ICustomerServiceDocHandoverTaxInstrumentService
{
    @Autowired
    private CustomerServiceDocHandoverTaxInstrumentMapper customerServiceDocHandoverTaxInstrumentMapper;

    /**
     * 查询材料交接票据
     * 
     * @param id 材料交接票据主键
     * @return 材料交接票据
     */
    @Override
    public CustomerServiceDocHandoverTaxInstrument selectCustomerServiceDocHandoverTaxInstrumentById(Long id)
    {
        return customerServiceDocHandoverTaxInstrumentMapper.selectCustomerServiceDocHandoverTaxInstrumentById(id);
    }

    /**
     * 查询材料交接票据列表
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 材料交接票据
     */
    @Override
    public List<CustomerServiceDocHandoverTaxInstrument> selectCustomerServiceDocHandoverTaxInstrumentList(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        return customerServiceDocHandoverTaxInstrumentMapper.selectCustomerServiceDocHandoverTaxInstrumentList(customerServiceDocHandoverTaxInstrument);
    }

    /**
     * 新增材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    @Override
    public int insertCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        customerServiceDocHandoverTaxInstrument.setCreateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverTaxInstrumentMapper.insertCustomerServiceDocHandoverTaxInstrument(customerServiceDocHandoverTaxInstrument);
    }

    /**
     * 修改材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    @Override
    public int updateCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument)
    {
        customerServiceDocHandoverTaxInstrument.setUpdateTime(DateUtils.getNowDate());
        return customerServiceDocHandoverTaxInstrumentMapper.updateCustomerServiceDocHandoverTaxInstrument(customerServiceDocHandoverTaxInstrument);
    }

    /**
     * 批量删除材料交接票据
     * 
     * @param ids 需要删除的材料交接票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverTaxInstrumentByIds(Long[] ids)
    {
        return customerServiceDocHandoverTaxInstrumentMapper.deleteCustomerServiceDocHandoverTaxInstrumentByIds(ids);
    }

    /**
     * 删除材料交接票据信息
     * 
     * @param id 材料交接票据主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceDocHandoverTaxInstrumentById(Long id)
    {
        return customerServiceDocHandoverTaxInstrumentMapper.deleteCustomerServiceDocHandoverTaxInstrumentById(id);
    }
}
