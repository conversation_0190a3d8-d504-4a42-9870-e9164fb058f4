package com.bxm.customer.domain.vo.repairAccount;

import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据以往代码封装
 *
 * <AUTHOR>
 * @date 2024/7/27 17:31
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperateUserInfoDTO {
    //获取操作人员信息

    @ApiModelProperty("操作者登录ID")
    private Long userId;

    @ApiModelProperty("操作者部门ID")
    private Long deptId;

    @ApiModelProperty("部门信息")
    private SysDept sysDept;

    @ApiModelProperty("根据操作者部门和ID得出操作员工列表")
    private List<SysEmployee> employees;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty("操作员名称")
    private String operName;
}
