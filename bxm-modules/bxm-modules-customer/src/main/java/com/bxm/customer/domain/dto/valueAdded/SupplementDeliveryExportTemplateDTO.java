package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 补充交付附件操作导出模板DTO
 *
 * 用于导出补充交付附件操作的Excel模板，继承基类的所有字段，无额外特有字段
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@ApiModel("补充交付附件操作导出模板DTO")
public class SupplementDeliveryExportTemplateDTO extends BaseExportTemplateDTO {
}
