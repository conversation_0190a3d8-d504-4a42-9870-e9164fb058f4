package com.bxm.customer.domain.vo.workBench;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncItemSearchVO extends BaseVO {

    @ApiModelProperty("选择的组织id")
    private Long deptId;

    @ApiModelProperty("类型，1-待申报（按税种），2-待扣款（按税种）")
    private Integer type;

    @ApiModelProperty(hidden = true)
    private String reportPeriod;

    @ApiModelProperty(hidden = true)
    private Integer period;

    @ApiModelProperty(hidden = true)
    private String periodStr;

    @ApiModelProperty("关键词")
    private String keyWord;

    @ApiModelProperty("批量查询的批次号")
    private String batchNo;

    @ApiModelProperty("标签名")
    private String periodTagName;

    @ApiModelProperty("标签是否包含，0-否，1-是")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期顾问小组")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期会计小组")
    private Long periodAccountingDeptId;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long headDeptId;
}
