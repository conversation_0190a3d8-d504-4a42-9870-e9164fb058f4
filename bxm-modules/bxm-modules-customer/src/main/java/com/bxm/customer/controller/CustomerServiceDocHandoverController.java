package com.bxm.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerDeliverCountryTaxExportDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceDeliverDTO;
import com.bxm.customer.domain.CustomerServiceDocHandover;
import com.bxm.customer.domain.dto.CustomerDeliverDTO;
import com.bxm.customer.domain.dto.docHandover.*;
import com.bxm.customer.domain.vo.docHandover.*;
import com.bxm.customer.service.AsyncService;
import com.bxm.customer.service.ICustomerServiceDocHandoverService;
import com.bxm.customer.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 材料、资料交接Controller
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@RestController
@RequestMapping("/handover")
@Api(tags = "材料、资料交接")
public class CustomerServiceDocHandoverController extends BaseController {
    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    /**
     * 查询材料、资料交接列表
     */
    @RequiresPermissions("customer:handover:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询材料、资料交接列表", notes = "查询材料、资料交接列表")
    public TableDataInfo list(CustomerServiceDocHandover customerServiceDocHandover) {
        startPage();
        List<CustomerServiceDocHandover> list = customerServiceDocHandoverService.selectCustomerServiceDocHandoverList(customerServiceDocHandover);
        return getDataTable(list);
    }

    /**
     * 导出材料、资料交接列表
     */
    @RequiresPermissions("customer:handover:export")
    @Log(title = "材料、资料交接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出材料、资料交接列表", notes = "导出材料、资料交接列表")
    public void export(HttpServletResponse response, CustomerServiceDocHandover customerServiceDocHandover) {
        List<CustomerServiceDocHandover> list = customerServiceDocHandoverService.selectCustomerServiceDocHandoverList(customerServiceDocHandover);
        ExcelUtil<CustomerServiceDocHandover> util = new ExcelUtil<CustomerServiceDocHandover>(CustomerServiceDocHandover.class);
        util.exportExcel(response, list, "材料、资料交接数据");
    }

    /**
     * 获取材料、资料交接详细信息
     */
    @RequiresPermissions("customer:handover:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取材料、资料交接详细信息", notes = "获取材料、资料交接详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(customerServiceDocHandoverService.selectCustomerServiceDocHandoverById(id));
    }

    /**
     * 新增材料、资料交接
     */
    @RequiresPermissions("customer:handover:add")
    @Log(title = "材料、资料交接", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增材料、资料交接", notes = "新增材料、资料交接")
    public AjaxResult add(@RequestBody CustomerServiceDocHandover customerServiceDocHandover) {
        return toAjax(customerServiceDocHandoverService.insertCustomerServiceDocHandover(customerServiceDocHandover));
    }

    /**
     * 修改材料、资料交接
     */
    @RequiresPermissions("customer:handover:edit")
    @Log(title = "材料、资料交接", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改材料、资料交接", notes = "修改材料、资料交接")
    public AjaxResult edit(@RequestBody CustomerServiceDocHandover customerServiceDocHandover) {
        return toAjax(customerServiceDocHandoverService.updateCustomerServiceDocHandover(customerServiceDocHandover));
    }

    /**
     * 删除材料、资料交接
     */
    @RequiresPermissions("customer:handover:remove")
    @Log(title = "材料、资料交接", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除材料、资料交接", notes = "删除材料、资料交接")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(customerServiceDocHandoverService.deleteCustomerServiceDocHandoverByIds(ids));
    }

    //****** start self method ******  批量操作: 删除、提交、核验、退回、承验转交，点击时打开对应弹框
    //@RequiresPermissions("customer:handover:docHandoverList")
    @GetMapping("/docHandoverList")
    @ApiOperation(value = "分页获取材料交接列表", notes = "分页获取材料交接列表")
    @RequiresPermissions("customer:handover:docHandoverList")
    public Result<IPage<DocHandoverDTO>> docHandoverList(@RequestHeader("deptId") Long deptId, CustomerServiceDocHandoverVO vo) {
        return Result.ok(customerServiceDocHandoverService.docHandoverList(deptId, vo));
    }

    @GetMapping("/getVoucherEntry")
    @ApiOperation(value = "获取凭票入账", notes = "获取凭票入账")
    public Result<Integer> getVoucherEntry(@RequestHeader("deptId") Long deptId, @RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId) {
        return Result.ok(customerServiceDocHandoverService.getVoucherEntry(customerServicePeriodMonthId));
    }

    //@RequiresPermissions("customer:customerService:exportCustomerServicePeriodMonthList")
    @Log(title = "材料、资料交接", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDocHandoverList")
    @ApiOperation(value = "导出材料交接列表", notes = "导出材料交接列表")
    public Result exportDocHandoverList(HttpServletResponse response, @RequestHeader("deptId") Long deptId, CustomerServiceDocHandoverVO vo) {
        String title = "材料-交接" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.MATERIAL_HANDOVER);
        CompletableFuture.runAsync(() -> {
            try {
                List<DocHandoverDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<DocHandoverDTO> l = customerServiceDocHandoverService.docHandoverList(deptId, vo).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                ExcelUtil<DocHandoverDTO> util = new ExcelUtil<>(DocHandoverDTO.class);
                asyncService.uploadExport(util, list, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @Log(title = "材料、资料交接", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDocHandoverListAndUpload")
    @ApiOperation(value = "导出材料交接列表", notes = "导出材料交接列表")
    public void exportDocHandoverListAndUpload(HttpServletResponse response, @RequestHeader("deptId") Long deptId, CustomerServiceDocHandoverVO vo) {
        vo.setPageNum(1);
        vo.setPageSize(-1);
        List<DocHandoverDTO> list = customerServiceDocHandoverService.docHandoverList(deptId, vo).getRecords();
        ExcelUtil<DocHandoverDTO> util = new ExcelUtil<>(DocHandoverDTO.class);
        util.exportExcel(response, list, "材料交接");
    }

    //@RequiresPermissions("customer:handover:addDocHandoverBase")
    @Log(title = "材料交接-基础信息", businessType = BusinessType.INSERT)
    @PostMapping("/addDocHandoverBase")
    @ApiOperation(value = "新增，基础信息", notes = "新增，基础信息")
    public Result<Long> addDocHandoverBase(@RequestHeader("deptId") Long deptId, @RequestBody @Valid AddDocHandoverBaseVO vo) {
        return Result.ok(customerServiceDocHandoverService.addDocHandoverBase(deptId, vo));
    }

    @GetMapping("/getDocHandoverBase")
    @ApiOperation(value = "获取基础信息", notes = "获取基础信息")
    public Result<DocHandoverBaseDTO> getDocHandoverBase(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceDocHandoverService.getDocHandoverBase(id));
    }

    @Log(title = "材料交接-基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateDocHandoverBase")
    @ApiOperation(value = "编辑基础信息", notes = "编辑基础信息")
    public Result<Boolean> updateDocHandoverBase(@RequestHeader("deptId") Long deptId, @RequestBody @Valid UpdateDocHandoverBaseVO vo) {
        customerServiceDocHandoverService.updateDocHandoverBase(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getDocHandoverInstrument")
    @ApiOperation(value = "获取交接材料相关信息，票据信息", notes = "获取交接材料相关信息，票据信息")
    public Result<DocHandoverInstrumentDTO> getDocHandoverInstrument(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceDocHandoverService.getDocHandoverInstrument(id));
    }

    @PostMapping("/checkCanSubmit")
    @ApiOperation(value = "校验可提交规则", notes = "校验可提交规则")
    public Result<Boolean> checkCanSubmit(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DocHandoverInstrumentDTO vo) {
        return Result.ok(customerServiceDocHandoverService.checkCanSubmit(vo));
    }

    @PostMapping("/checkCanSubmitV2")
    @ApiOperation(value = "校验可提交规则-V2", notes = "校验可提交规则-V2")
    public Result<Boolean> checkCanSubmitV2(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DocHandoverInstrumentDTO vo) {
        return Result.ok(customerServiceDocHandoverService.checkCanSubmitV2(vo));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/saveDocHandoverInstrument")
    @ApiOperation(value = "当新增材料交接、编辑材料交接时，保存", notes = "当新增材料交接、编辑材料交接时，保存")
    public Result<Boolean> saveDocHandoverInstrument(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DocHandoverInstrumentDTO vo) {
        customerServiceDocHandoverService.saveDocHandoverInstrument(deptId, vo, false);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/submitDocHandoverInstrument")
    @ApiOperation(value = "当新增材料交接、编辑材料交接时，提交", notes = "当新增材料交接、编辑材料交接时，提交")
    public Result<Boolean> submitDocHandoverInstrument(@RequestHeader("deptId") Long deptId, @RequestBody @Valid DocHandoverInstrumentDTO vo) {
        customerServiceDocHandoverService.submitDocHandoverInstrument(deptId, vo);
        return Result.ok();
    }

    @GetMapping("/getDocHandoverFull")
    @ApiOperation(value = "获取材料交接详情", notes = "获取材料交接详情")
    public Result<DocHandoverFullDTO> getDocHandoverFull(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceDocHandoverService.getDocHandoverFull(id));
    }

    @GetMapping("/getDocHandoverOfPeriodByCustomerServiceId")
    @ApiOperation(value = "获取客户服务某个账期月份的所有材料交接标题", notes = "获取客户服务某个账期月份的所有材料交接标题")
    public Result<List<DocHandoverSimpleDTO>> getDocHandoverOfPeriodByCustomerServiceId(@RequestHeader("deptId") Long deptId,
                                                                                        @RequestParam("customerServiceId") Long customerServiceId,
                                                                                        @RequestParam("period") Integer period
    ) {
        return Result.ok(customerServiceDocHandoverService.getDocHandoverOfPeriodByCustomerServiceId(customerServiceId, period));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateSubmit")
    @ApiOperation(value = "提交", notes = "提交")
    public Result<Boolean> operateSubmit(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        customerServiceDocHandoverService.operateSubmit(deptId, id);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateSubmitBatch")
    @ApiOperation(value = "提交-批量", notes = "提交-批量")
    public Result<Integer> operateSubmitBatch(@RequestHeader("deptId") Long deptId, @RequestBody List<Long> ids) {
        return Result.ok(customerServiceDocHandoverService.operateSubmitBatch(deptId, ids));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateVerification")
    @ApiOperation(value = "核验", notes = "核验")
    public Result<Boolean> operateVerification(@RequestHeader("deptId") Long deptId, @RequestBody @Valid OperateVerificationVO vo) {
        customerServiceDocHandoverService.operateVerification(deptId, vo);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateVerificationBatch")
    @ApiOperation(value = "核验-批量", notes = "核验-批量")
    public Result<Integer> operateVerificationBatch(@RequestHeader("deptId") Long deptId, @RequestBody OperateVerificationBatchVO vo) {
        return Result.ok(customerServiceDocHandoverService.operateVerificationBatch(deptId, vo));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateBack")
    @ApiOperation(value = "退回", notes = "退回")
    public Result<Boolean> operateBack(@RequestHeader("deptId") Long deptId, @RequestBody @Valid OperateBackVO vo) {
        customerServiceDocHandoverService.operateBack(deptId, vo);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateBackBatch")
    @ApiOperation(value = "退回-批量", notes = "退回-批量")
    public Result<Integer> operateBackBatch(@RequestHeader("deptId") Long deptId, @RequestBody OperateBackBatchVO vo) {
        return Result.ok(customerServiceDocHandoverService.operateBackBatch(deptId, vo));
    }

    @GetMapping("/getChangeVerificationInfo")
    @ApiOperation(value = "获取成员转交的信息-会计信息", notes = "获取成员转交的信息-会计信息")
    public Result<ChangeVerificationInfoDTO> getChangeVerificationInfo(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        return Result.ok(customerServiceDocHandoverService.getChangeVerificationInfo(id));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateChangeVerification")
    @ApiOperation(value = "承验转交", notes = "承验转交")
    public Result<Boolean> operateChangeVerification(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        customerServiceDocHandoverService.operateChangeVerification(deptId, id);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateChangeVerificationBatch")
    @ApiOperation(value = "承验转交-批量", notes = "承验转交-批量")
    public Result<Integer> operateChangeVerificationBatch(@RequestHeader("deptId") Long deptId, @RequestBody List<Long> ids) {
        return Result.ok(customerServiceDocHandoverService.operateChangeVerificationBatch(deptId, ids));
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateDelete")
    @ApiOperation(value = "删除", notes = "删除")
    public Result<Boolean> operateDelete(@RequestHeader("deptId") Long deptId, @RequestParam("id") Long id) {
        customerServiceDocHandoverService.operateDelete(deptId, id);
        return Result.ok();
    }

    @Log(title = "材料交接", businessType = BusinessType.UPDATE)
    @PostMapping("/operateDeleteBatch")
    @ApiOperation(value = "删除-批量", notes = "删除-批量")
    public Result<Integer> operateDeleteBatch(@RequestHeader("deptId") Long deptId, @RequestBody List<Long> ids) {
        return Result.ok(customerServiceDocHandoverService.operateDeleteBatch(deptId, ids));
    }

    @GetMapping("/updateBankInstrumentUnHasCount")
    @ApiOperation(value = "unHasBank历史数据处理", notes = "unHasBank历史数据处理")
    public Result<Boolean> updateBankInstrumentUnHasCount(@RequestHeader("deptId") Long deptId) {
        customerServiceDocHandoverService.updateBankInstrumentUnHasCount();
        return Result.ok();
    }
}
