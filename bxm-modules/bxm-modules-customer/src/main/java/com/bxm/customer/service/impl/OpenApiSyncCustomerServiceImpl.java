package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.OpenApiSyncCustomerMapper;
import com.bxm.customer.domain.OpenApiSyncCustomer;
import com.bxm.customer.service.IOpenApiSyncCustomerService;

/**
 * 第三方申报同步客户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class OpenApiSyncCustomerServiceImpl extends ServiceImpl<OpenApiSyncCustomerMapper, OpenApiSyncCustomer> implements IOpenApiSyncCustomerService
{
    @Autowired
    private OpenApiSyncCustomerMapper openApiSyncCustomerMapper;

    /**
     * 查询第三方申报同步客户
     * 
     * @param id 第三方申报同步客户主键
     * @return 第三方申报同步客户
     */
    @Override
    public OpenApiSyncCustomer selectOpenApiSyncCustomerById(Long id)
    {
        return openApiSyncCustomerMapper.selectOpenApiSyncCustomerById(id);
    }

    /**
     * 查询第三方申报同步客户列表
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 第三方申报同步客户
     */
    @Override
    public List<OpenApiSyncCustomer> selectOpenApiSyncCustomerList(OpenApiSyncCustomer openApiSyncCustomer)
    {
        return openApiSyncCustomerMapper.selectOpenApiSyncCustomerList(openApiSyncCustomer);
    }

    /**
     * 新增第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    @Override
    public int insertOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer)
    {
        openApiSyncCustomer.setCreateTime(DateUtils.getNowDate());
        return openApiSyncCustomerMapper.insertOpenApiSyncCustomer(openApiSyncCustomer);
    }

    /**
     * 修改第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    @Override
    public int updateOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer)
    {
        openApiSyncCustomer.setUpdateTime(DateUtils.getNowDate());
        return openApiSyncCustomerMapper.updateOpenApiSyncCustomer(openApiSyncCustomer);
    }

    /**
     * 批量删除第三方申报同步客户
     * 
     * @param ids 需要删除的第三方申报同步客户主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncCustomerByIds(Long[] ids)
    {
        return openApiSyncCustomerMapper.deleteOpenApiSyncCustomerByIds(ids);
    }

    /**
     * 删除第三方申报同步客户信息
     * 
     * @param id 第三方申报同步客户主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiSyncCustomerById(Long id)
    {
        return openApiSyncCustomerMapper.deleteOpenApiSyncCustomerById(id);
    }

    @Override
    public List<OpenApiSyncCustomer> selectBySyncRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<OpenApiSyncCustomer>()
                .eq(OpenApiSyncCustomer::getSycRecordId, recordId));
    }
}
