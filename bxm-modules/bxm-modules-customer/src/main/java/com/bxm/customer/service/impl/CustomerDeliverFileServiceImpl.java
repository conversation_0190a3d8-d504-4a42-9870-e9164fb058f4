package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.DeliverFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.vo.RemoteSupplementReportFilesVO;
import com.bxm.customer.domain.vo.CustomerDeliverBatchFileVO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerDeliverFileMapper;
import com.bxm.customer.domain.CustomerDeliverFile;
import com.bxm.customer.service.ICustomerDeliverFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 交付附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Service
public class CustomerDeliverFileServiceImpl extends ServiceImpl<CustomerDeliverFileMapper, CustomerDeliverFile> implements ICustomerDeliverFileService
{
    private static final String STATEMENT_DETAIL_FILE_NAME = "社保个人明细_系统生成";

    @Autowired
    private CustomerDeliverFileMapper customerDeliverFileMapper;

    /**
     * 查询交付附件
     * 
     * @param id 交付附件主键
     * @return 交付附件
     */
    @Override
    public CustomerDeliverFile selectCustomerDeliverFileById(Long id)
    {
        return customerDeliverFileMapper.selectCustomerDeliverFileById(id);
    }

    /**
     * 查询交付附件列表
     * 
     * @param customerDeliverFile 交付附件
     * @return 交付附件
     */
    @Override
    public List<CustomerDeliverFile> selectCustomerDeliverFileList(CustomerDeliverFile customerDeliverFile)
    {
        return customerDeliverFileMapper.selectCustomerDeliverFileList(customerDeliverFile);
    }

    /**
     * 新增交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    @Override
    public int insertCustomerDeliverFile(CustomerDeliverFile customerDeliverFile)
    {
        customerDeliverFile.setCreateTime(DateUtils.getNowDate());
        return customerDeliverFileMapper.insertCustomerDeliverFile(customerDeliverFile);
    }

    /**
     * 修改交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    @Override
    public int updateCustomerDeliverFile(CustomerDeliverFile customerDeliverFile)
    {
        customerDeliverFile.setUpdateTime(DateUtils.getNowDate());
        return customerDeliverFileMapper.updateCustomerDeliverFile(customerDeliverFile);
    }

    /**
     * 批量删除交付附件
     * 
     * @param ids 需要删除的交付附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerDeliverFileByIds(Long[] ids)
    {
        return customerDeliverFileMapper.deleteCustomerDeliverFileByIds(ids);
    }

    /**
     * 删除交付附件信息
     * 
     * @param id 交付附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerDeliverFileById(Long id)
    {
        return customerDeliverFileMapper.deleteCustomerDeliverFileById(id);
    }

    @Override
    public List<CustomerDeliverFile> selectByBatchDeliverId(List<Long> deliverIds) {
        if (ObjectUtils.isEmpty(deliverIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .in(CustomerDeliverFile::getCustomerDeliverId, deliverIds));
    }

    @Override
    public List<CustomerDeliverFile> selectByDeliverId(Long deliverId) {
        if (Objects.isNull(deliverId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId));
    }

    @Override
    @Transactional
    public void removeAndSaveNewFiles(Long deliverId, List<CommonFileVO> files, Integer fileType) {
        update(new LambdaUpdateWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getFileType, fileType)
                .set(CustomerDeliverFile::getIsDel, Boolean.TRUE));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(file -> new CustomerDeliverFile()
                    .setCustomerDeliverId(deliverId)
                    .setFileType(fileType)
                    .setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName())
                    .setOfficalFilename(file.getOfficalFilename())
                    .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional
    public void saveNewFiles(Long deliverId, List<CommonFileVO> files, Integer fileType) {
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(file -> new CustomerDeliverFile()
                    .setCustomerDeliverId(deliverId)
                    .setFileType(fileType)
                    .setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName())
                    .setOfficalFilename(file.getOfficalFilename())
                    .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
        }
    }

    @Override
    public void removeAndSaveNewFiles(List<RemoteSupplementReportFilesVO> voList, Integer fileType) {
        if (ObjectUtils.isEmpty(voList)) {
            return;
        }
        List<CustomerDeliverFile> files = Lists.newArrayList();
        voList.forEach(vo -> {
            if (!ObjectUtils.isEmpty(vo.getFiles())) {
                files.addAll(vo.getFiles().stream().map(file -> new CustomerDeliverFile()
                        .setCustomerDeliverId(vo.getId())
                        .setFileType(fileType)
                        .setFileUrl(file.getFileUrl())
                        .setFileName(file.getFileName())
                        .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
            }
        });
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files);
        }
    }

    @Override
    @Transactional
    public void saveNewFile(RemoteSupplementReportFilesVO vo) {
        if (ObjectUtils.isEmpty(vo.getFiles())) {
            return;
        }
        List<CustomerDeliverFile> files = vo.getFiles().stream().map(file -> new CustomerDeliverFile()
                .setCustomerDeliverId(vo.getId())
                .setFileType(vo.getDeliverFileType())
                .setFileUrl(file.getFileUrl())
                .setFileName(file.getFileName())
                .setIsDel(Boolean.FALSE)).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files);
        }
    }

    @Override
    public List<CustomerDeliverFile> selectByDeliverIdAndFileTypes(Long deliverId, List<Integer> fileTypes) {
        if (Objects.isNull(deliverId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerDeliverFile>().eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .in(!ObjectUtils.isEmpty(fileTypes), CustomerDeliverFile::getFileType, fileTypes));
    }

    @Override
    public List<CustomerDeliverFile> selectByDeliverIdsAndFileType(List<Long> deliverIds, Integer fileType) {
        if (ObjectUtils.isEmpty(deliverIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerDeliverFile>().in(CustomerDeliverFile::getCustomerDeliverId, deliverIds)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(!Objects.isNull(fileType), CustomerDeliverFile::getFileType, fileType));
    }

    @Override
    public Boolean checkFileExist(Long deliverId, Integer fileType) {
        return count(new LambdaQueryWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getFileType, fileType)) > 0;
    }

    @Override
    @Transactional
    public void batchRemoveAndSaveNewFiles(List<CustomerDeliverBatchFileVO> voList, DeliverFileType fileType) {
        if (ObjectUtils.isEmpty(voList)) {
            return;
        }
        List<Long> deliverIds = voList.stream().map(CustomerDeliverBatchFileVO::getDeliverId).collect(Collectors.toList());
        update(new LambdaUpdateWrapper<CustomerDeliverFile>()
                .in(CustomerDeliverFile::getCustomerDeliverId, deliverIds)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getFileType, fileType.getCode())
                .set(CustomerDeliverFile::getIsDel, Boolean.TRUE));
        List<CustomerDeliverFile> files = Lists.newArrayList();
        voList.forEach(vo -> {
            if (!ObjectUtils.isEmpty(vo.getFiles())) {
                files.addAll(vo.getFiles().stream().map(f -> new CustomerDeliverFile()
                        .setCustomerDeliverId(vo.getDeliverId())
                        .setFileType(fileType.getCode())
                        .setFileUrl(f.getFileUrl())
                        .setFileName(f.getFileName())
                        .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
            }
        });
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files);
        }
    }

    @Override
    @Transactional
    public void removeAndSaveStatementDetailFile(Long deliverId, CommonFileVO statementDetailFile, Integer fileType) {
        update(new LambdaUpdateWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getFileType, fileType)
                .eq(CustomerDeliverFile::getOfficalFilename, STATEMENT_DETAIL_FILE_NAME)
                .set(CustomerDeliverFile::getIsDel, Boolean.TRUE));
        if (!ObjectUtils.isEmpty(statementDetailFile)) {
            save(new CustomerDeliverFile()
                    .setCustomerDeliverId(deliverId)
                    .setFileType(fileType)
                    .setFileUrl(statementDetailFile.getFileUrl())
                    .setFileName(statementDetailFile.getFileName())
                    .setOfficalFilename(STATEMENT_DETAIL_FILE_NAME)
                    .setIsDel(Boolean.FALSE));
        }
    }

    @Override
    @Transactional
    public void removeAndSaveNewReportFiles(Long deliverId, List<CommonFileVO> huisuanqingjiaoFiles, List<CommonFileVO> yijiaokuanFiles, List<CommonFileVO> daijiaokuanFiles, List<CommonFileVO> otherDeliverFiles) {
        update(new LambdaUpdateWrapper<CustomerDeliverFile>()
                .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                .eq(CustomerDeliverFile::getIsDel, Boolean.FALSE)
                .eq(CustomerDeliverFile::getFileType, DeliverFileType.DELIVER_FILE.getCode())
                .set(CustomerDeliverFile::getIsDel, Boolean.TRUE));
        List<CustomerDeliverFile> files = Lists.newArrayList();
        buildReportFiles(deliverId, huisuanqingjiaoFiles, files);
        buildReportFiles(deliverId, yijiaokuanFiles, files);
        buildReportFiles(deliverId, daijiaokuanFiles, files);
        buildReportFiles(deliverId, otherDeliverFiles, files);
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files);
        }
    }

    @Override
    @Transactional
    public void saveNewReportFile(Long deliverId, List<CommonFileVO> huisuanqingjiaoFiles, List<CommonFileVO> yijiaokuanFiles, List<CommonFileVO> daijiaokuanFiles, List<CommonFileVO> otherDeliverFiles) {
        // 标准附件走更新逻辑 标准附件实际每个类型只会有一个，就简单处理了
        List<CustomerDeliverFile> customerDeliverFiles = selectByDeliverIdsAndFileType(Collections.singletonList(deliverId), DeliverFileType.DELIVER_FILE.getCode());
        List<CustomerDeliverFile> files = Lists.newArrayList();
        buildUpdateReportFiles(deliverId, huisuanqingjiaoFiles, files, CustomerDeliverServiceImpl.HUISUANQINGJIAO_FILE_TYPE, customerDeliverFiles);
        buildUpdateReportFiles(deliverId, yijiaokuanFiles, files, CustomerDeliverServiceImpl.YIJIAOKUAN_FILE_TYPE, customerDeliverFiles);
        buildUpdateReportFiles(deliverId, daijiaokuanFiles, files, CustomerDeliverServiceImpl.DAIJIAOKUAN_FILE_TYPE, customerDeliverFiles);
        buildReportFiles(deliverId, otherDeliverFiles, files);
        if (!ObjectUtils.isEmpty(files)) {
            saveOrUpdateBatch(files);
        }
    }

    @Override
    @Transactional
    public void removeFileExceptOfficalFileName(Long deliverId, Integer fileType, String officalFileName) {
        if (!Objects.isNull(deliverId)) {
            List<CustomerDeliverFile> waitRemoveFiles = list(new LambdaQueryWrapper<CustomerDeliverFile>()
                    .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                    .eq(CustomerDeliverFile::getFileType, fileType)
                    .ne(CustomerDeliverFile::getOfficalFilename, officalFileName)
                    .eq(CustomerDeliverFile::getIsDel, false));
            if (!ObjectUtils.isEmpty(waitRemoveFiles)) {
                updateBatchById(waitRemoveFiles.stream().map(f -> new CustomerDeliverFile().setId(f.getId()).setIsDel(true)).collect(Collectors.toList()));
            }
        }
    }

    @Override
    @Transactional
    public void removeFileIncludeOfficalFileName(Long deliverId, Integer fileType, String officalFileName) {
        if (!Objects.isNull(deliverId)) {
            List<CustomerDeliverFile> waitRemoveFiles = list(new LambdaQueryWrapper<CustomerDeliverFile>()
                    .eq(CustomerDeliverFile::getCustomerDeliverId, deliverId)
                    .eq(CustomerDeliverFile::getFileType, fileType)
                    .eq(CustomerDeliverFile::getOfficalFilename, officalFileName)
                    .eq(CustomerDeliverFile::getIsDel, false));
            if (!ObjectUtils.isEmpty(waitRemoveFiles)) {
                updateBatchById(waitRemoveFiles.stream().map(f -> new CustomerDeliverFile().setId(f.getId()).setIsDel(true)).collect(Collectors.toList()));
            }
        }
    }

    @Override
    @Transactional
    public void removeAndSaveReportTable(Long deliverId, List<CommonFileVO> files) {
        List<CustomerDeliverFile> currentReportFiles = selectByDeliverIdAndFileTypes(deliverId, Collections.singletonList(DeliverFileType.REPORT.getCode()));
        List<CommonFileVO> reportTableFiles = files.stream().filter(f -> Objects.equals("个税申报表", f.getOfficalFilename())).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(reportTableFiles)) {
            List<String> reportTableFileNames = reportTableFiles.stream().map(CommonFileVO::getFileName).collect(Collectors.toList());
            List<CustomerDeliverFile> sameReportFiles = currentReportFiles.stream().filter(f -> Objects.equals(f.getOfficalFilename(), "个税申报表") && reportTableFileNames.contains(f.getFileName())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(sameReportFiles)) {
                updateBatchById(sameReportFiles.stream().map(f -> new CustomerDeliverFile().setId(f.getId()).setIsDel(true)).collect(Collectors.toList()));
            }
        }
        removeFileExceptOfficalFileName(deliverId, DeliverFileType.REPORT.getCode(), "个税申报表");
        saveNewFiles(deliverId, files, DeliverFileType.REPORT.getCode());
    }

    private void buildReportFiles(Long deliverId, List<CommonFileVO> reportFiles, List<CustomerDeliverFile> files) {
        if (!ObjectUtils.isEmpty(reportFiles)) {
            files.addAll(reportFiles.stream().map(file -> new CustomerDeliverFile()
                    .setCustomerDeliverId(deliverId)
                    .setFileType(DeliverFileType.DELIVER_FILE.getCode())
                    .setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName())
                    .setOfficalFilename(file.getOfficalFilename())
                    .setIsDel(Boolean.FALSE)).collect(Collectors.toList()));
        }
    }

    private void buildUpdateReportFiles(Long deliverId, List<CommonFileVO> reportFiles, List<CustomerDeliverFile> files, String officalFilename, List<CustomerDeliverFile> oldFiles) {
        if (!ObjectUtils.isEmpty(reportFiles)) {
            CommonFileVO newReportFile = reportFiles.get(0);
            List<CustomerDeliverFile> oldReportFiles = oldFiles.stream().filter(row -> Objects.equals(row.getOfficalFilename(), officalFilename)).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(oldReportFiles)) {
                CustomerDeliverFile oldReportFile = oldReportFiles.get(0);
                files.add(new CustomerDeliverFile().setId(oldReportFile.getId())
                        .setCustomerDeliverId(deliverId)
                        .setFileUrl(newReportFile.getFileUrl())
                        .setFileName(newReportFile.getFileName())
                        .setFileType(DeliverFileType.DELIVER_FILE.getCode()));
            } else {
                files.add(new CustomerDeliverFile()
                        .setCustomerDeliverId(deliverId)
                        .setFileUrl(newReportFile.getFileUrl())
                        .setFileName(newReportFile.getFileName())
                        .setFileType(DeliverFileType.DELIVER_FILE.getCode())
                        .setIsDel(false)
                        .setOfficalFilename(officalFilename));
            }
        }
    }
}
