package com.bxm.customer.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 社保基数验证注解
 * 
 * 用于验证社保基数字段，只有当业务类型为1-社医保或2-个税明细时才进行验证。
 * 其他业务类型（3-国税账号，4-个税账号）时该字段可以为空。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = SocialInsuranceBaseValidator.class)
public @interface SocialInsuranceBase {

    String message() default "社保基数格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 是否允许为空（当业务类型不是1或2时）
     */
    boolean allowEmpty() default true;
}
