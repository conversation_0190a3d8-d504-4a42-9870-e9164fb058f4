package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.*;
import com.bxm.common.core.enums.accountingCashier.*;
import com.bxm.common.core.enums.businessTask.BusinessTaskFinishResult;
import com.bxm.common.core.enums.businessTask.BusinessTaskStatus;
import com.bxm.common.core.enums.inAccount.InAccountRpaExeResult;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteAccountingCashierSimpleDTO;
import com.bxm.customer.api.domain.vo.RemoteAccountingCashierPeriodTypeVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.accoutingCashier.*;
import com.bxm.customer.domain.dto.businessTask.BizIdBankAccountDTO;
import com.bxm.customer.domain.dto.inAccount.CustomerInAccountMaxPeriodDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialRepeatDTO;
import com.bxm.customer.domain.vo.CommonInAccountVO;
import com.bxm.customer.domain.vo.CommonNoticeVO;
import com.bxm.customer.domain.vo.accoutingCashier.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.properties.BusinessGroupProperties;
import com.bxm.customer.properties.SpecialDeptIdProperties;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户账务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Service
@Slf4j
public class CustomerServiceCashierAccountingServiceImpl extends ServiceImpl<CustomerServiceCashierAccountingMapper, CustomerServiceCashierAccounting> implements ICustomerServiceCashierAccountingService
{

    private static final Integer THREAD_POOL_SIZE = 10;

    private static final Long HOURS_12_MILL_SECONDS = 60 * 60 * 12 * 1000L;

    private static final LocalDate DEFAULT_LOCAL_DATE = LocalDate.of(2025, 3, 18);

    private static final LocalDate DEFAULT_PERIOD_CREATE_DATE = LocalDate.of(2025, 1, 1);

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private ICCustomerServicePeriodEmployeeService customerServicePeriodEmployeeService;

    @Autowired
    private ICustomerServiceCashierAccountingFileService customerServiceCashierAccountingFileService;

    @Autowired
    private ICustomerMattersNotesService customerMattersNotesService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private IBusinessTaskService businessTaskService;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    @Lazy
    private ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    @Autowired
    @Lazy
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private BusinessGroupProperties businessGroupProperties;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @Autowired
    private IOpenApiCheckFileRecordService openApiCheckFileRecordService;

    @Autowired
    private FileService fileService;

    @Autowired
    private SpecialDeptIdProperties specialDeptIdProperties;

    @Autowired
    private ExceptionService exceptionService;

    @Autowired
    private IBusinessDdlRecordService businessDdlRecordService;

    /**
     * 查询客户账务
     * 
     * @param id 客户账务主键
     * @return 客户账务
     */
    @Override
    public CustomerServiceCashierAccounting selectCustomerServiceCashierAccountingById(Long id)
    {
        return customerServiceCashierAccountingMapper.selectCustomerServiceCashierAccountingById(id);
    }

    /**
     * 查询客户账务列表
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 客户账务
     */
    @Override
    public List<CustomerServiceCashierAccounting> selectCustomerServiceCashierAccountingList(CustomerServiceCashierAccounting customerServiceCashierAccounting)
    {
        return customerServiceCashierAccountingMapper.selectCustomerServiceCashierAccountingList(customerServiceCashierAccounting);
    }

    /**
     * 新增客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    @Override
    public int insertCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting)
    {
        customerServiceCashierAccounting.setCreateTime(DateUtils.getNowDate());
        return customerServiceCashierAccountingMapper.insertCustomerServiceCashierAccounting(customerServiceCashierAccounting);
    }

    /**
     * 修改客户账务
     * 
     * @param customerServiceCashierAccounting 客户账务
     * @return 结果
     */
    @Override
    public int updateCustomerServiceCashierAccounting(CustomerServiceCashierAccounting customerServiceCashierAccounting)
    {
        customerServiceCashierAccounting.setUpdateTime(DateUtils.getNowDate());
        return customerServiceCashierAccountingMapper.updateCustomerServiceCashierAccounting(customerServiceCashierAccounting);
    }

    /**
     * 批量删除客户账务
     * 
     * @param ids 需要删除的客户账务主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceCashierAccountingByIds(Long[] ids)
    {
        return customerServiceCashierAccountingMapper.deleteCustomerServiceCashierAccountingByIds(ids);
    }

    /**
     * 删除客户账务信息
     * 
     * @param id 客户账务主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceCashierAccountingById(Long id)
    {
        return customerServiceCashierAccountingMapper.deleteCustomerServiceCashierAccountingById(id);
    }

    @Override
    public IPage<AccountingCashierDTO> accountingCashierList(AccountingCashierSearchVO vo) {
        IPage<AccountingCashierDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        List<Long> ids = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            ids = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(ids)) {
                return result;
            }
        }
        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getCustomerServiceTagName())) {
            if (Objects.isNull(vo.getCustomerServiceTagIncludeFlag())) {
                vo.setCustomerServiceTagIncludeFlag(1);
            }
            customerServiceIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getCustomerServiceTagName(), TagBusinessType.CUSTOMER_SERVICE);
            if (ObjectUtils.isEmpty(customerServiceIds) && vo.getCustomerServiceTagIncludeFlag() == 1) {
                return result;
            }
        }
        List<Long> customerServicePeriodMonthIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            if (Objects.isNull(vo.getPeriodTagIncludeFlag())) {
                vo.setPeriodTagIncludeFlag(1);
            }
            customerServicePeriodMonthIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getPeriodTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            if (ObjectUtils.isEmpty(customerServicePeriodMonthIds) && vo.getPeriodTagIncludeFlag() == 1) {
                return result;
            }
        }
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(vo.getUserId(), vo.getDeptId()).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        if (!StringUtils.isEmpty(vo.getCompleteTimeStart())) {
            vo.setCompleteTimeStart(vo.getCompleteTimeStart() + " 00:00:00");
        }
        if (!StringUtils.isEmpty(vo.getCompleteTimeEnd())) {
            vo.setCompleteTimeEnd(vo.getCompleteTimeEnd() + " 23:59:59");
        }
        Integer start = (vo.getPageNum() - 1) * vo.getPageSize();
        Integer limit = vo.getPageSize();
        List<AccountingCashierDTO> data = baseMapper.accountingCashierListPage(vo, userDeptDTO, ids, customerServiceIds, customerServicePeriodMonthIds, start, limit);
        if (!ObjectUtils.isEmpty(data)) {
            buildAccountingCashierDTO(data);
        }
        result.setRecords(data);
        result.setTotal(baseMapper.selectAccountCashierCount(vo, userDeptDTO, ids, customerServiceIds, customerServicePeriodMonthIds));
        result.setCurrent(vo.getPageNum());
        result.setSize(vo.getPageSize());
        result.setPages(result.getTotal() % result.getSize() == 0 ? result.getTotal() / result.getSize() : result.getTotal() / result.getSize() + 1);
        return result;
    }

    @Override
    public List<CommonFileVO> getAccountingCashierFiles(Long accountingCashierId, Integer fileType) {
        if (Objects.isNull(accountingCashierId)) {
            return Collections.emptyList();
        }
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(accountingCashierId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            return Collections.emptyList();
        }
        return customerServiceCashierAccountingFileService.selectByAccountingCashierIdAndFileType(accountingCashierId, fileType)
                .stream().map(row -> CommonFileVO.builder().fileUrl(row.getFileUrl()).fileSize(row.getFileSize()).fileName(row.getFileName()).build()).collect(Collectors.toList());
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierPeriodAccountingDeptCountList(UserDeptDTO userDeptDTO, Integer type) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.accountingCashierPeriodAccountingDeptCountList(type, userDeptDTO);
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierCustomerAdvisorDeptCountList(UserDeptDTO userDeptDTO, Integer type) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.accountingCashierCustomerAdvisorDeptCountList(type, userDeptDTO);
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierPeriodAdvisorDeptCountList(UserDeptDTO userDeptDTO, Integer type) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.accountingCashierPeriodAdvisorDeptCountList(type, userDeptDTO);
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierCustomerAccountingDeptCountList(UserDeptDTO userDeptDTO, Integer type) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        return baseMapper.accountingCashierCustomerAccountingDeptCountList(type, userDeptDTO);
    }

    @Override
    public AccountingCashierDetailDTO detail(Long accountingCashierId) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(accountingCashierId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceCashierAccounting.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户不存在");
        }
        List<CustomerServiceCashierAccountingFile> files = customerServiceCashierAccountingFileService.selectByAccountingCashierIdAndFileType(accountingCashierId, null);
        AccountingCashierDetailDTO detail = new AccountingCashierDetailDTO();
        BeanUtils.copyProperties(customerServiceCashierAccounting, detail);
        detail.setCustomerServiceId(customerService.getId());
        detail.setCustomerName(customerService.getCustomerName());
        Integer mattersNotesItemType = AccountingCashierType.convertToItemType(customerServiceCashierAccounting.getType());
        detail.setMattersNotes(Objects.isNull(mattersNotesItemType) ? null :
                customerMattersNotesService.getMattersNotesByCustomerServiceIdAndItemType(customerServiceCashierAccounting.getCustomerServiceId(), mattersNotesItemType));
        detail.setMaterialFiles(files.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode())).map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            detail.setCheckFiles(files.stream().filter(f -> f.getSubFileType() == 1).map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
            detail.setReceiptFiles(files.stream().filter(f -> f.getSubFileType() == 2).map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
        }
        detail.setIsClose(customerServiceCashierAccounting.getIsClose());
        detail.setDeliverStatusStr(Objects.isNull(customerServiceCashierAccounting.getDeliverStatus()) ? "" : AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
        detail.setDeliverResultStr(Objects.isNull(customerServiceCashierAccounting.getDeliverResult()) ? "" : AccountingCashierDeliverResult.getNameByCode(customerServiceCashierAccounting.getDeliverResult(), customerServiceCashierAccounting.getType()));
        detail.setCompleteTime(Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) ? "" : customerServiceCashierAccounting.getCompleteTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        detail.setDeliverFiles(files.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.DELIVER_ATTACHMENT.getCode())).map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            detail.setBankPaymentResult(customerServiceCashierAccounting.getBankPaymentResult());
            detail.setBankPaymentResultStr(Objects.isNull(customerServiceCashierAccounting.getBankPaymentResult()) ? "" : BankPaymentResult.getByCode(customerServiceCashierAccounting.getBankPaymentResult()).getDesc());
            detail.setAccountingCashierInAccountInfo(getInAccountInfo(customerServiceCashierAccounting, files));
            detail.setSettleAccountStatus(customerServiceCashierAccounting.getSettleAccountStatus());
            detail.setSettleAccountStatusStr(Objects.isNull(customerServiceCashierAccounting.getSettleAccountStatus()) ? "" : InAccountStatus.getByCode(customerServiceCashierAccounting.getSettleAccountStatus()).getName());
        } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            detail.setBankPaymentTaskList(businessTaskService.getBankPaymentTaskListByPeriodIdAndBankAccountNumber(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), customerServiceCashierAccounting.getBankAccountNumber()));
        } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
            detail.setRelationAccountingCashierList(getSamePeriodAccountingCashierList(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), accountingCashierId));
        }
        detail.setCustomerServiceHasTicketTag(businessTagRelationService.existsTagId(customerServiceCashierAccounting.getCustomerServiceId(), TagBusinessType.CUSTOMER_SERVICE.getCode(), specialTagProperties.getPprz()));
        detail.setPeriodHasTicketTag(businessTagRelationService.existsTagId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode(), specialTagProperties.getPprz()));
        detail.setMaterialIntegrityStr(getMaterialIntegrityStr(customerServiceCashierAccounting.getMaterialIntegrity(), customerServiceCashierAccounting.getMaterialSupplementStatus()));
        detail.setDdl(Objects.isNull(customerServiceCashierAccounting.getDdl()) ? null : DateUtils.localDateToStr(customerServiceCashierAccounting.getDdl(), DateUtils.YYYY_MM_DD));
        return detail;
    }

    private List<AccountingCashierSimpleDTO> getSamePeriodAccountingCashierList(Long customerServicePeriodMonthId, Long exclusionId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServicePeriodMonthId)
                .ne(CustomerServiceCashierAccounting::getId, exclusionId)
                .orderByAsc(CustomerServiceCashierAccounting::getType)
                .orderByDesc(CustomerServiceCashierAccounting::getId))
                .stream().map(row ->
                        AccountingCashierSimpleDTO.builder()
                                .id(row.getId())
                                .title(row.getTitle())
                                .deliverStatus(row.getDeliverStatus())
                                .type(row.getType())
                                .deliverStatusStr(AccountingCashierDeliverStatus.getByCode(row.getDeliverStatus()).getName())
                                .build()).collect(Collectors.toList());
    }

    private AccountingCashierInAccountDTO getInAccountInfo(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CustomerServiceCashierAccountingFile> files) {
        AccountingCashierInAccountDTO dto = new AccountingCashierInAccountDTO();
        dto.setMajorIncomeTotal(Objects.isNull(customerServiceCashierAccounting.getMajorIncomeTotal()) ? "" : customerServiceCashierAccounting.getMajorIncomeTotal().stripTrailingZeros().toPlainString());
        dto.setMajorCostTotal(Objects.isNull(customerServiceCashierAccounting.getMajorCostTotal()) ? "" : customerServiceCashierAccounting.getMajorCostTotal().stripTrailingZeros().toPlainString());
        dto.setProfitTotal(Objects.isNull(customerServiceCashierAccounting.getProfitTotal()) ? "" : customerServiceCashierAccounting.getProfitTotal().stripTrailingZeros().toPlainString());
        dto.setPriorYearExpenseIncrease(customerServiceCashierAccounting.getPriorYearExpenseIncrease());
        dto.setTaxReportCount(customerServiceCashierAccounting.getTaxReportCount());
        dto.setTaxReportSalaryTotal(Objects.isNull(customerServiceCashierAccounting.getTaxReportSalaryTotal()) ? "" : customerServiceCashierAccounting.getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
        dto.setTableStatusBalance(customerServiceCashierAccounting.getTableStatusBalance());
        dto.setTableStatusBalanceStr(customerServiceCashierAccounting.getTableStatusBalance());
        dto.setRpaExeResult(customerServiceCashierAccounting.getRpaExeResult());
        dto.setRpaExeResultStr(Objects.isNull(customerServiceCashierAccounting.getRpaExeResult()) ? "" : InAccountRpaExeResult.getByCode(customerServiceCashierAccounting.getRpaExeResult()).getName());
        dto.setRpaSearchTimeStr(Objects.isNull(customerServiceCashierAccounting.getRpaSearchTime()) ? "" : customerServiceCashierAccounting.getRpaSearchTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        dto.setRpaRemark(customerServiceCashierAccounting.getRpaRemark());
        dto.setRpaFiles(files.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.RPA_ATTACHMENT.getCode())).map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).build()).collect(Collectors.toList()));
        return dto;
    }

    @Override
    @Transactional
    public void modifyMattersNotes(AccountingCashierMattersNotesModifyVO vo, Long deptId) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getAccountingCashierId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        Integer itemType = AccountingCashierType.convertToItemType(customerServiceCashierAccounting.getType());
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
        if (!Objects.isNull(itemType)) {
            customerMattersNotesService.saveOrUpdateMattersNotes(customerServiceCashierAccounting.getCustomerServiceId(), itemType, vo.getMattersNotes());
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getCustomerServiceId())
                        .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE.getCode())
                        .setDeptId(deptId)
                        .setOperType("编辑事项备忘（" + AccountingCashierType.getByCode(customerServiceCashierAccounting.getType()).getName() + "）")
                        .setOperName(operateUserInfoDTO.getOperName())
                        .setCreateTime(LocalDateTime.now())
                        .setOperUserId(operateUserInfoDTO.getUserId())
                        .setOperContent(vo.getMattersNotes()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    @Override
    @Transactional
    public void createAccountingCashier(AccountingCashierCreateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "新建";
        vo.setIsInner(false);
        createAccountingCashier(vo, operTime, operType);
    }

    @Override
    @Transactional
    public CustomerServiceCashierAccounting createAccountingCashierByMaterial(AccountingCashierCreateVO vo, OperateUserInfoDTO operateUserInfoDTO) {
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = Objects.isNull(vo.getId()) ? "材料交接新建" : "材料交接补充";
        vo.setIsInner(false);
        return createAccountingCashierByMaterial(vo, operTime, operType);
    }

    private CustomerServiceCashierAccounting createAccountingCashierByMaterial(AccountingCashierCreateVO vo, LocalDateTime operTime, String operType) {
        if (Objects.isNull(vo.getId())) {
            checkAccountingCashierExists(vo);
            CustomerServiceCashierAccounting customerServiceCashierAccounting = new CustomerServiceCashierAccounting();
            customerServiceCashierAccounting.setTitle(getAccountingCashierTitle(vo.getType(), vo.getPeriod(), vo.getBankName(), vo.getBankAccountNumber()));
            customerServiceCashierAccounting.setCustomerServiceId(vo.getCustomerServiceId());
            customerServiceCashierAccounting.setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId());
            customerServiceCashierAccounting.setPeriod(vo.getPeriod());
            customerServiceCashierAccounting.setType(vo.getType());
            customerServiceCashierAccounting.setHasTicket(Objects.isNull(vo.getHasTicket()) ? null : vo.getHasTicket() == 1);
            customerServiceCashierAccounting.setBankName(vo.getBankName());
            customerServiceCashierAccounting.setBankAccountNumber(vo.getBankAccountNumber());
            customerServiceCashierAccounting.setHasBankPayment(Objects.isNull(vo.getHasBankPayment()) ? null : vo.getHasBankPayment() == 1);
            customerServiceCashierAccounting.setDeliverRequire(vo.getDeliverRequire());
            customerServiceCashierAccounting.setMaterialMedia(vo.getMaterialMedia());
            customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
            customerServiceCashierAccounting.setLastOperName(vo.getOperName());
            customerServiceCashierAccounting.setLastOperTime(operTime);
            customerServiceCashierAccounting.setLastOperType(operType);
            customerServiceCashierAccounting.setLastOperRemark(vo.getDeliverRequire());
            customerServiceCashierAccounting.setCreateBy(vo.getOperName());
            customerServiceCashierAccounting.setCreateOperType(operType);
            save(customerServiceCashierAccounting);
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles()) && Objects.equals(vo.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
            if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(vo.getCustomerServicePeriodMonthId()).setInAccountStatus(customerServiceCashierAccounting.getDeliverStatus()));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "");
            } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单创建");
            }
            Map<String, String> operContent = new LinkedHashMap<>();
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                if (!Objects.isNull(vo.getHasBankPayment())) {
                    operContent.put("流水情况", vo.getHasBankPayment() == 1 ? "有流水" : "无流水");
                }
            } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode()) || Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
                if (!Objects.isNull(vo.getHasTicket())) {
                    operContent.put("凭票入账", vo.getHasTicket() == 1 ? "是" : "否");
                }
            }
            if (!Objects.isNull(vo.getMaterialMedia())) {
                operContent.put("材料介质", AccountingCashierMaterialMedia.getByCode(vo.getMaterialMedia()).getDesc());
            }
            operContent.put("交付要求", StringUtils.isEmpty(vo.getDeliverRequire()) ? "" : vo.getDeliverRequire());
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType(operType)
                        .setOperName(vo.getOperName())
                        .setCreateTime(operTime)
                        .setOperContent(JSONObject.toJSONString(operContent))
                        .setOperUserId(vo.getUserId())
                        .setOperRemark(vo.getDeliverRequire())
                        .setOperImages(ObjectUtils.isEmpty(vo.getMaterialFiles()) ? "" : JSONArray.toJSONString(vo.getMaterialFiles())));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return customerServiceCashierAccounting;
        } else {
            CustomerServiceCashierAccounting customerServiceCashierAccounting = new CustomerServiceCashierAccounting();
            customerServiceCashierAccounting.setId(vo.getId());
            customerServiceCashierAccounting.setLastOperName(vo.getOperName());
            customerServiceCashierAccounting.setLastOperTime(operTime);
            customerServiceCashierAccounting.setLastOperType(operType);
            customerServiceCashierAccounting.setLastOperRemark(vo.getDeliverRequire());
            updateById(customerServiceCashierAccounting);
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }

            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType(operType)
                        .setOperName(vo.getOperName())
                        .setCreateTime(operTime)
                        .setOperUserId(vo.getUserId())
                        .setOperImages(ObjectUtils.isEmpty(vo.getMaterialFiles()) ? "" : JSONArray.toJSONString(vo.getMaterialFiles())));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            return null;
        }
    }

    // 纸质上传
    private void bankReceiptPaperUpload(Long customerServiceId, Long customerServicePeriodMonthId, Integer period, String bankAccountNumber, Long deliverId, List<CommonFileVO> files, Long deptId, Long userId, String operName, Long businessTaskId) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
        if (!Objects.isNull(customerService)) {
            String groupId = Objects.isNull(customerService.getBusinessDeptId()) ? "" : businessGroupProperties.getMap().get(customerService.getBusinessDeptId());
            String groupName = StringUtils.isEmpty(groupId) ? "" : GroupMap.getGroupNameByGroupId(groupId);
            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(Collections.singletonList(customerService.getId()), TagBusinessType.CUSTOMER_SERVICE);
            String platType = customerServiceTagMap.getOrDefault(customerService.getId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                    .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
            CommonFileVO bankReceiptFile = files.stream().filter(f -> Objects.equals(f.getDeliverFileType(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode())).findFirst().orElse(null);
            CommonFileVO statementFile = files.stream().filter(f -> Objects.equals(f.getDeliverFileType(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode())).findFirst().orElse(null);
            remoteThirdpartService.bankReceiptPaperFileUpload(BankReceiptPaperFileUploadVO.builder()
                    .groupName(groupName)
                    .period(period)
                    .creditCode(customerService.getCreditCode())
                    .groupId(groupId)
                    .taxNumber(customerService.getTaxNumber())
                    .statementFileLink(Objects.isNull(statementFile) ? "" : fileService.getFullFileUrlValidTime(statementFile.getFileUrl(), 60 * 60 * 48L))
                    .platType(platType)
                    .bankReceiptFileLink(Objects.isNull(bankReceiptFile) ? "" : fileService.getFullFileUrlValidTime(bankReceiptFile.getFileUrl(), 60 * 60 * 48L))
                    .customerName(customerService.getCustomerName())
                    .operator(operName)
                    .bankNumber(bankAccountNumber)
                    .deliverId(deliverId)
                    .businessTaskId(businessTaskId)
                    .userId(userId)
                    .deptId(deptId)
                    .customerServiceId(customerServiceId)
                    .customerServicePeriodMonthId(customerServicePeriodMonthId)
                    .build(), SecurityConstants.INNER);
//            if (R.SUCCESS != resp.getCode()) {
//                // 交付单异常交付
//                accountingCashierExceptionDealByPaperUpload(customerServiceCashierAccounting, operTime, resp);
//            } else {
//                createWaitInAccountBusinessTask(customerServiceCashierAccounting, operTime, resp, groupName, groupId, customerService);
//            }
        }
    }

    private void accountingCashierExceptionDealByPaperUpload(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R<BankReceiptPaperFileUploadDTO> resp) {
        String operType = "RPA异常交付";
        // 流水交付单异常交付
        //异常交付
        //
        //结果：异常
        //状态：异常
        //操作记录：
        //oper_type：RPA异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccounting.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                .setLastOperName("系统")
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperType(operType));
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", AccountingCashierDeliverStatus.WAIT_DELIVER.getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void remoteCreateAccountingCashier(AccountingCashierCreateVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "新建";
        vo.setIsInner(true);
        createAccountingCashier(vo, operTime, operType);
    }

    @Override
    @Transactional
    public void modifyAccountingCashier(AccountingCashierModifyVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "编辑";
        vo.setIsCoverFiles(true);
        modifyAccountingCashier(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> submit(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "提交";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            submitSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (AccountingCashierDeliverStatus.canSubmitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                    successList.add(customerServiceCashierAccounting);
                } else {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())
                        .setHasChanged(false)
                        .setLastOperType(operType)
                        .setLastOperRemark("")
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    customerServicePeriodMonthService.updateBatchById(
                            successList.stream().map(row -> new CustomerServicePeriodMonth().setId(row.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())).collect(Collectors.toList()));
                }
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> submitV2(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            submitSingleV2(vo, operTime);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            if (Objects.isNull(vo.getPreStatus())) {
                throw new ServiceException("提交前状态不能为空");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getInTime, CustomerServiceCashierAccounting::getDeliverResult, CustomerServiceCashierAccounting::getDdl, CustomerServiceCashierAccounting::getIsClose));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            Map<Long, Integer> afterDeliverStatusMap = new HashMap<>();
            LocalDate now = LocalDate.now();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), vo.getPreStatus())) {
                    Integer deliverStatus = AccountingCashierDeliverStatus.getAfterSubmitDeliverStatus(vo.getPreStatus(), customerServiceCashierAccounting.getDeliverResult(), customerServiceCashierAccounting.getIsClose());
                    if (Objects.isNull(deliverStatus)) {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "提交后状态有误");
                    } else {
                        if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode()) && !Objects.isNull(customerServiceCashierAccounting.getDdl()) && !customerServiceCashierAccounting.getDdl().isAfter(now)) {
                            failList.add(customerServiceCashierAccounting);
                            failIds.add(customerServiceCashierAccounting.getId());
                            errorMagMap.put(customerServiceCashierAccounting.getId(), exceptionService.getErrorMsgByErrorCode(ErrorCodeEnum.DDL_ERROR));
                        } else {
                            successList.add(customerServiceCashierAccounting);
                            afterDeliverStatusMap.put(customerServiceCashierAccounting.getId(), deliverStatus);
                        }
                    }
                } else {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            boolean isReSubmit = Objects.equals(vo.getPreStatus(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode());
            String operType = isReSubmit ? "提交交付单" : "交付结果提交";
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> {
                    Integer afterStatus = afterDeliverStatusMap.get(row.getId());
                    boolean isComplete = Objects.equals(afterStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                    return new CustomerServiceCashierAccounting()
                            .setId(row.getId())
                            .setDeliverStatus(afterStatus)
                            .setDeliverResult(!Objects.isNull(row.getIsClose()) && row.getIsClose() == 1 ? AccountingCashierDeliverResult.NO_DELIVER.getCode() : null)
                            .setCompleteTime(Objects.isNull(row.getCompleteTime()) && isComplete ? operTime : null)
                            .setCompleteUserId(Objects.isNull(row.getCompleteTime()) && isComplete ? vo.getUserId() : null)
                            .setCompleteUserName(Objects.isNull(row.getCompleteTime()) && isComplete ? vo.getOperName() : null)
                            .setInTime(Objects.isNull(row.getInTime()) && isComplete ? operTime.toLocalDate() : null)
                            .setLastInTime(isComplete ? operTime.toLocalDate() : null)
                            .setLastCompleteUserId(isComplete ? vo.getUserId() : null)
                            .setHasChanged(false)
                            .setLastOperType(operType)
                            .setLastOperRemark("")
                            .setLastOperTime(operTime)
                            .setLastOperName(vo.getOperName());
                }).collect(Collectors.toList()));
                if (isReSubmit) {
                    if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                        customerServicePeriodMonthService.updateBatchById(
                                successList.stream().map(row -> new CustomerServicePeriodMonth().setId(row.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())).collect(Collectors.toList()));
                    }
                } else {
                    List<Long> customerServicePeriodMonthIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
                    if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                        customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                                updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单提交"));
                    } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                        customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                                updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单提交"));
                        customerServicePeriodMonthService.updateBatchById(successList.stream().map(row -> new CustomerServicePeriodMonth().setId(row.getCustomerServicePeriodMonthId()).setInAccountStatus(afterDeliverStatusMap.get(row.getId())))
                                .collect(Collectors.toList()));
                    }
                }
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    private void submitSingleV2(AccountingCashierOperateVO vo, LocalDateTime operTime) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canSubmitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode()) && !Objects.isNull(customerServiceCashierAccounting.getDdl())) {
            LocalDate now = LocalDate.now();
            if (!customerServiceCashierAccounting.getDdl().isAfter(now)) {
                exceptionService.throwConfigException(ErrorCodeEnum.DDL_ERROR);
            }
        }
        Integer deliverStatus = AccountingCashierDeliverStatus.getAfterSubmitDeliverStatus(customerServiceCashierAccounting.getDeliverStatus(), customerServiceCashierAccounting.getDeliverResult(), customerServiceCashierAccounting.getIsClose());
        if (Objects.isNull(deliverStatus)) {
            throw new ServiceException("提交后状态有误");
        }
        boolean isReSubmit = Objects.equals(deliverStatus, AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
        String operType = isReSubmit ? "提交交付单" : "交付结果提交";
        CustomerServiceCashierAccounting update = new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setDeliverStatus(deliverStatus)
                .setDeliverResult(customerServiceCashierAccounting.getIsClose() == 1 ? AccountingCashierDeliverResult.NO_DELIVER.getCode() : null)
                .setHasChanged(false)
                .setLastOperType(operType)
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName());
        if (Objects.equals(deliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) {
            update.setCompleteTime(Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) ? operTime : null)
                .setCompleteUserId(Objects.isNull(customerServiceCashierAccounting.getInTime()) ? vo.getUserId() : null)
                .setCompleteUserName(Objects.isNull(customerServiceCashierAccounting.getInTime()) ? vo.getOperName() : null)
                .setInTime(Objects.isNull(customerServiceCashierAccounting.getInTime()) ? operTime.toLocalDate() : null)
                .setLastInTime(operTime.toLocalDate())
                .setLastCompleteUserId(vo.getUserId());
        }
        updateById(update);
        if (isReSubmit) {
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
                customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()));
            }
        } else {
            if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单提交");
            } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单提交");
                customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(deliverStatus));
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> waitSubmitSubmit(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "提交";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            waitSubmitSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (AccountingCashierDeliverStatus.canWaitSubmitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                    successList.add(customerServiceCashierAccounting);
                } else {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())
                        .setHasChanged(false)
                        .setLastOperType(operType)
                        .setLastOperRemark("")
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                List<Long> customerServicePeriodMonthIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
                if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单提交"));
                } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单提交"));
                    customerServicePeriodMonthService.updateBatchById(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId).setInAccountStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()))
                            .collect(Collectors.toList()));
                }
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    private void waitSubmitSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canWaitSubmitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        updateById(new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())
                .setHasChanged(false)
                .setLastOperType(operType)
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName()));
        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单提交");
        } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单提交");
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void submitSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canSubmitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        updateById(new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())
                .setHasChanged(false)
                .setLastOperType(operType)
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName()));
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> delete(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "删除";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            deleteSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getBankAccountNumber));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            Boolean isBank = Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode());
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (AccountingCashierDeliverStatus.canDeleteStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                    if (isBank && businessTaskService.count(new LambdaQueryWrapper<BusinessTask>()
                            .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                            .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                            .eq(BusinessTask::getIsDel, false)) > 0) {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "存在任务，不允许删除");
                    } else {
                        successList.add(customerServiceCashierAccounting);
                    }
                } else {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setIsDel(true)
                        .setLastOperType(operType)
                        .setLastOperRemark("")
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                List<Long> customerServicePeriodMonthIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
                if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单删除"));
                } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单删除"));
                    customerServicePeriodMonthService.updateBatchById(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_CREATE.getCode()))
                            .collect(Collectors.toList()));
                }
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                    if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                        customerServiceService.updateLastInAccountIdByDelete(row.getCustomerServiceId(), row.getId(), row.getPeriod().toString());
                    }
                });
            }
            return result;
        }
    }

    private void deleteSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canDeleteStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode()) && businessTaskService.count(new LambdaQueryWrapper<BusinessTask>()
                .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
                .eq(BusinessTask::getIsDel, false)) > 0) {
            throw new ServiceException("存在任务，不允许删除");
        }
        updateById(new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setIsDel(true)
                .setLastOperType(operType)
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName()));
        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单删除");
        } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单删除");
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_CREATE.getCode()));
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServiceService.updateLastInAccountIdByDelete(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> deliver(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "交付";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            vo.setIsCoverFiles(true);
            vo.setIsInner(false);
            deliverSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getCustomerServiceId, CustomerServiceCashierAccounting::getPeriod, CustomerServiceCashierAccounting::getBankAccountNumber, CustomerServiceCashierAccounting::getInTime));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            Map<String, List<BusinessTask>> needCheckBusinessTaskMap = Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode()) ? businessTaskService.getBatchNeedCheckBusinessTaskByAccountingCashier(totalList) : Maps.newHashMap();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (customerServiceCashierAccounting.getHasChanged()) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付变更待接收");
                } else {
                    if (AccountingCashierDeliverStatus.canDeliverStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode()) && needCheckBusinessTaskMap.containsKey(customerServiceCashierAccounting.getCustomerServicePeriodMonthId() + "_" + customerServiceCashierAccounting.getBankAccountNumber())) {
                            failList.add(customerServiceCashierAccounting);
                            failIds.add(customerServiceCashierAccounting.getId());
                            errorMagMap.put(customerServiceCashierAccounting.getId(), "有任务单待审核");
                        } else {
                            successList.add(customerServiceCashierAccounting);
                        }
                    } else {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                    }
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                vo.setIsInner(false);
                vo.setIsCoverFiles(true);
                successList.forEach(row -> {
                    vo.setId(row.getId());
                    deliverSingle(vo, operTime, operType);
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public void remoteDeliver(AccountingCashierOperateVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "交付";
        // 单个操作
        vo.setIsCoverFiles(false);
        vo.setIsInner(true);
        deliverSingle(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> dealException(AccountingCashierDealExceptionVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "处理异常";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            vo.setIsInner(false);
            dealExceptionSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (AccountingCashierDeliverStatus.canDealExceptionStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                    successList.add(customerServiceCashierAccounting);
                } else {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                vo.setIsInner(false);
                successList.forEach(customerServiceCashierAccounting -> {
                    dealExceptionSingle(vo, operTime, operType);
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public void remoteDealException(AccountingCashierDealExceptionVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "处理异常";
        // 单个操作
        vo.setIsInner(true);
        dealExceptionSingle(vo, operTime, operType);
    }

    @Override
    @Transactional
    public void changeAccountingCashier(AccountingCashierChangeDeliverVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "变更交付";
        vo.setIsCoverFiles(true);
        changeAccountingCashier(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> reBackAccountingCashier(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "退回";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            reBackSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要提交的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (customerServiceCashierAccounting.getHasChanged()) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付变更待接收");
                } else {
                    if (AccountingCashierDeliverStatus.canReBackStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                        successList.add(customerServiceCashierAccounting);
                    } else {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                    }
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                Integer deliverStatus = AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode();
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setDeliverStatus(deliverStatus)
                        .setLastOperType(operType)
                        .setLastOperRemark(vo.getRemark())
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                List<Long> customerServicePeriodMonthIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
                if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单退回"));
                } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                            updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单退回"));
                    customerServicePeriodMonthService.updateBatchById(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new CustomerServicePeriodMonth()
                            .setId(customerServicePeriodMonthId).setInAccountStatus(deliverStatus)).collect(Collectors.toList()));
                }
                Map<String, Object> operContent = Maps.newLinkedHashMap();
                operContent.put("前置状态", AccountingCashierDeliverStatus.WAIT_DELIVER.getName());
                operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_RESUBMIT.getName());
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperUserId(vo.getUserId())
                                .setOperRemark(vo.getRemark())
                                .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public void rpaUpdate(AccountingCashierRpaUpdateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "RPA更新";
        vo.setIsCoverFiles(true);
        rpaUpdate(vo, operTime, operType);
    }

    @Override
    @Transactional
    public void remoteRpaUpdate(AccountingCashierRpaUpdateVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "RPA更新";
        vo.setIsCoverFiles(false);
        rpaUpdate(vo, operTime, operType);
    }

    private void rpaUpdate(AccountingCashierRpaUpdateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canRpaUpdateStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, StringUtils.isEmpty(vo.getRpaRemark()) ? "" : vo.getRpaRemark())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName())
                .set(CustomerServiceCashierAccounting::getRpaExeResult, vo.getRpaExeResult());
        InAccountDeliverVO inAccountDeliverInfo = vo.getInAccountDeliverInfo();
        if (vo.getIsCoverFiles()) {
            updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal())
                    .set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal())
                    .set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal())
                    .set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease())
                    .set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount())
                    .set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal())
                    .set(CustomerServiceCashierAccounting::getTableStatusBalance, inAccountDeliverInfo.getTableStatusBalance())
                    .set(CustomerServiceCashierAccounting::getRpaSearchTime, StringUtils.isEmpty(vo.getRpaSearchTime()) ? null : LocalDateTime.parse(vo.getRpaSearchTime(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)))
                    .set(CustomerServiceCashierAccounting::getRpaRemark, vo.getRpaRemark());
        } else {
            if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getMajorCostTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getProfitTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal());
            }
            if (!StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getTaxReportCount())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal());
            }
            if (!StringUtils.isEmpty(inAccountDeliverInfo.getTableStatusBalance())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTableStatusBalance, inAccountDeliverInfo.getTableStatusBalance());
            }
            if (!StringUtils.isEmpty(vo.getRpaSearchTime())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getRpaSearchTime, LocalDateTime.parse(vo.getRpaSearchTime(), DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            }
            if (!StringUtils.isEmpty(vo.getRpaRemark())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getRpaRemark, vo.getRpaRemark());
            }
        }
        updateWrapper.set(CustomerServiceCashierAccounting::getProfitGetTime, LocalDateTime.now());
        update(updateWrapper);
        if (vo.getIsCoverFiles()) {
            customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.RPA_ATTACHMENT);
        }
        if (!ObjectUtils.isEmpty(vo.getFiles())) {
            customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getFiles(), AccountingCashierFileType.RPA_ATTACHMENT);
        }
        Map<String, String> operContent = Maps.newLinkedHashMap();
        if (!Objects.isNull(vo.getInAccountDeliverInfo())) {
            if (vo.getIsCoverFiles()) {
                operContent.put("RPA执行结果", InAccountRpaExeResult.getByCode(vo.getRpaExeResult()).getName());
                operContent.put("本年累计主营收入", Objects.isNull(vo.getInAccountDeliverInfo().getMajorIncomeTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年累计主营成本", Objects.isNull(vo.getInAccountDeliverInfo().getMajorCostTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年累计净利润", Objects.isNull(vo.getInAccountDeliverInfo().getProfitTotal()) ? "null" : vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年费用调增", StringUtils.isEmpty(vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease()) ? "null" : vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                operContent.put("个税申报人数", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportCount()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                operContent.put("本年个税申报工资总额", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportSalaryTotal()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                operContent.put("报表平衡", StringUtils.isEmpty(vo.getInAccountDeliverInfo().getTableStatusBalance()) ? "null" : vo.getInAccountDeliverInfo().getTableStatusBalance());
                operContent.put("RPA查询时间", StringUtils.isEmpty(vo.getRpaSearchTime()) ? "null" : vo.getRpaSearchTime());
            } else {
                operContent.put("RPA执行结果", InAccountRpaExeResult.getByCode(vo.getRpaExeResult()).getName());
                if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal())) {
                    operContent.put("本年累计主营收入", vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getMajorCostTotal())) {
                    operContent.put("本年累计主营成本", vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getProfitTotal())) {
                    operContent.put("本年累计净利润", vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                }
                if (!StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease())) {
                    operContent.put("本年费用调增", vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getTaxReportCount())) {
                    operContent.put("个税申报人数", vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                    operContent.put("本年个税申报工资总额", vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                }
                if (!StringUtils.isEmpty(inAccountDeliverInfo.getTableStatusBalance())) {
                    operContent.put("报表平衡", vo.getInAccountDeliverInfo().getTableStatusBalance());
                }
                if (!StringUtils.isEmpty(vo.getRpaSearchTime())) {
                    operContent.put("RPA查询时间", vo.getRpaSearchTime());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRpaRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServiceService.updateLastInAccountId(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
        }
    }

    @Override
    @Transactional
    public void updateProfit(AccountingCashierUpdateProfitVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "更新利润";
        vo.setIsCoverFiles(true);
        updateProfit(vo, operTime, operType);
    }

    @Override
    @Transactional
    public void remoteUpdateProfit(AccountingCashierUpdateProfitVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "更新利润";
        vo.setIsCoverFiles(false);
        updateProfit(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> supplementFiles(AccountingCashierSupplementFileVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "补充材料";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            supplementFiles(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要补充的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getType, CustomerServiceCashierAccounting::getMaterialSupplementStatus));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单类型错误");
                } else {
                    successList.add(customerServiceCashierAccounting);
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setMaterialSupplementStatus(MaterialSupplementStatus.TO_BE_CHECKED.getCode())
                        .setLastOperType(operType)
                        .setLastOperRemark(vo.getRemark())
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                List<Long> successIds = successList.stream().map(CustomerServiceCashierAccounting::getId).collect(Collectors.toList());
                if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    if (vo.getCoverFileType() != 3) {
                        if (vo.getCoverFileType() == 2) {
                            customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdsAndFileType(successIds, AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                        }
                        if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                            customerServiceCashierAccountingFileService.saveAccountingCashierFileBatch(successIds, vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                        }
                    }
                    if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                        if (vo.getReceiptCoverFileType() != 3) {
                            if (vo.getReceiptCoverFileType() == 2) {
                                customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdsAndFileType(successIds, AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                            }
                            if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                                customerServiceCashierAccountingFileService.saveAccountingCashierFileBatch(successIds, vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                            }
                        }
                    }
                } else {
                    if (vo.getCoverFileType() != 3) {
                        if (vo.getCoverFileType() == 2) {
                            customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdsAndFileType(successIds, AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                        }
                        if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                            customerServiceCashierAccountingFileService.saveAccountingCashierFileBatch(successIds, vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                        }
                    }
                }
                successList.forEach(row -> {
                    try {
                        Map<String, Object> operContent = Maps.newLinkedHashMap();
                        operContent.put("前置状态", MaterialSupplementStatus.getByCode(row.getMaterialSupplementStatus()).getDesc());
                        operContent.put("后置状态", MaterialSupplementStatus.TO_BE_CHECKED.getDesc());
                        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                            if (!Objects.isNull(vo.getCoverFileType())) {
                                operContent.put("对账单补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                                    operContent.put("对账单补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                                }
                            }
                            if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                                operContent.put("回单补充方式", vo.getReceiptCoverFileType() == 1 ? "追加" : vo.getReceiptCoverFileType() == 2 ? "覆盖" : "无需补充");
                                if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                                    operContent.put("回单补充附件", JSONArray.toJSONString(vo.getReceiptFiles()));
                                }
                            }
                        } else {
                            if (!Objects.isNull(vo.getCoverFileType())) {
                                operContent.put("补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                                    operContent.put("补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                                }
                            }
                        }
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperUserId(vo.getUserId())
                                .setOperRemark(vo.getRemark()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public void remoteSupplementFiles(AccountingCashierSupplementFileVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "补充材料";
        supplementFiles(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> checkFiles(AccountingCashierCheckFileVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "核对材料";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            checkFilesSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要核对的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getType, CustomerServiceCashierAccounting::getMaterialSupplementStatus));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单类型错误");
                } else {
                    if (!Objects.equals(customerServiceCashierAccounting.getMaterialSupplementStatus(), MaterialSupplementStatus.TO_BE_CHECKED.getCode())) {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "材料补充状态不为待核对");
                    } else {
                        successList.add(customerServiceCashierAccounting);
                    }
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setMaterialSupplementStatus(MaterialSupplementStatus.ALREADY_CHECKED.getCode())
                        .setMaterialIntegrity(vo.getMaterialIntegrity())
                        .setLastOperType(operType)
                        .setLastOperRemark(vo.getRemark())
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                Map<String, Object> operContent = Maps.newLinkedHashMap();
                operContent.put("前置状态", MaterialSupplementStatus.TO_BE_CHECKED.getDesc());
                operContent.put("后置状态", MaterialSupplementStatus.ALREADY_CHECKED.getDesc());
                operContent.put("核对结果", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
                successList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperUserId(vo.getUserId())
                                .setOperRemark(vo.getRemark())
                                .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    private void checkFilesSingle(AccountingCashierCheckFileVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
            throw new ServiceException("交付单类型错误");
        }
        if (!Objects.equals(customerServiceCashierAccounting.getMaterialSupplementStatus(), MaterialSupplementStatus.TO_BE_CHECKED.getCode())) {
            throw new ServiceException("材料补充状态不为待核对");
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getMaterialSupplementStatus, MaterialSupplementStatus.ALREADY_CHECKED.getCode())
                .set(CustomerServiceCashierAccounting::getMaterialIntegrity, vo.getMaterialIntegrity())
                .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName());
        update(updateWrapper);
        Map<String, Object> operContent = Maps.newLinkedHashMap();
        operContent.put("前置状态", MaterialSupplementStatus.TO_BE_CHECKED.getDesc());
        operContent.put("后置状态", MaterialSupplementStatus.ALREADY_CHECKED.getDesc());
        operContent.put("核对结果", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void remoteCheckFiles(AccountingCashierCheckFileVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "核对材料";
        checkFilesSingle(vo, operTime, operType);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> receiveChange(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "接收变更";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            receiveChangeSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要接收变更的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                    .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getCreateTime));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (!customerServiceCashierAccounting.getHasChanged()) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "非变更待接收");
                } else {
                    successList.add(customerServiceCashierAccounting);
                }
            }
            result.setSuccess(successList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(successList)) {
                successList.forEach(row -> {
                    AccountingCashierOperateVO singleVO = new AccountingCashierOperateVO();
                    BeanUtils.copyProperties(vo, singleVO);
                    singleVO.setId(row.getId());
                    receiveChangeSingle(singleVO, operTime, operType);
                });
            }
            return result;
        }
    }

    private void receiveChangeSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!customerServiceCashierAccounting.getHasChanged()) {
            throw new ServiceException("非变更待接收");
        }
        CCustomerService customerService = customerServiceService.getById(customerServiceCashierAccounting.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }
        Integer deliverStatus = AccountingCashierDeliverStatus.WAIT_DELIVER.getCode();
        Integer deliverResult = null;
        String operRemark = "";
        String lastOperType = operType;
        LocalDateTime lastOperTime = operTime;
        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode()) && !Objects.isNull(customerServiceCashierAccounting.getHasBankPayment())) {
            if (!customerServiceCashierAccounting.getHasBankPayment()) {
                deliverStatus = AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode();
                deliverResult = AccountingCashierDeliverResult.NO_ACCOUNT.getCode();
                lastOperType = "交付";
                lastOperTime = operTime.plusSeconds(1L);
            }
        }
        update(new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getHasChanged, false)
                .set(CustomerServiceCashierAccounting::getDeliverStatus, deliverStatus)
                .set(CustomerServiceCashierAccounting::getDeliverResult, deliverResult)
                .set(CustomerServiceCashierAccounting::getDeliverRemark, null)
                .set(CustomerServiceCashierAccounting::getLastOperType, lastOperType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, operRemark)
                .set(CustomerServiceCashierAccounting::getLastOperTime, lastOperTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName())
        );
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(deliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) {
            Map<String, String> operContent = Maps.newLinkedHashMap();
            operContent.put("交付内容", "无流水自动完结");
            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType("交付")
                        .setOperName(vo.getOperName())
                        .setCreateTime(operTime.plusSeconds(1L))
                        .setOperContent(JSONObject.toJSONString(operContent))
                        .setOperUserId(vo.getUserId()));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        // 是银行流水交付单 && 有银行流水 && 是银企 && 账期>=202501 发起RPA自动化流程
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())
                && Objects.equals(deliverStatus, AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())
                && !Objects.isNull(customerServiceCashierAccounting.getHasBankPayment())
                && customerServiceCashierAccounting.getHasBankPayment()) {
//            if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
//                autoFlowRpaDeal(customerServiceCashierAccounting, vo.getOperName(), customerServiceCashierAccounting.getCustomerServiceId(), vo.getUserId(), vo.getDeptId());
//            } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
//                List<CommonFileVO> files = customerServiceCashierAccountingFileService.selectByAccountingCashierIdAndFileType(customerServiceCashierAccounting.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode())
//                        .stream().map(row -> {
//                            CommonFileVO commonFileVO = CommonFileVO.builder().fileSize(row.getFileSize()).fileUrl(row.getFileUrl()).fileName(row.getFileName()).build();
//                            if (Objects.equals(row.getSubFileType(), 1)) {
//                                commonFileVO.setDeliverFileType(MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode());
//                            } else {
//                                commonFileVO.setDeliverFileType(MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode());
//                            }
//                            return commonFileVO;
//                        }).collect(Collectors.toList());
//                if (!ObjectUtils.isEmpty(files)) {
//                    autoRpaBankPaperUpload(customerServiceCashierAccounting, files, vo.getOperName(), vo.getUserId(), vo.getDeptId());
//                }
//            } else {
//                accountingCashierFlowAutoDeal(customerServiceCashierAccounting, customerService, operTime, vo.getDeptId(), vo.getUserId(), vo.getOperName());
//            }
            accountingCashierFlowAutoDeal(customerServiceCashierAccounting, customerService, operTime, vo.getDeptId(), vo.getUserId(), vo.getOperName());
        }
        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单变更交付");
        } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单变更交付");
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                    .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(deliverStatus));
        }
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> createBankTask(AccountingCashierBankCreateTaskVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "创建任务";
        if (!Objects.isNull(vo.getId())) {
            // 单个操作
            createBankTaskSingle(vo, operTime, operType);
            return null;
        } else {
            if (ObjectUtils.isEmpty(vo.getIds())) {
                throw new ServiceException("请选择要创建任务的数据");
            }
            TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
            List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .in(CustomerServiceCashierAccounting::getId, vo.getIds()));
            result.setTotal(totalList);
            if (ObjectUtils.isEmpty(totalList)) {
                result.setSuccess(Collections.emptyList());
                result.setFail(Collections.emptyList());
                return result;
            }
            List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
            List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
            List<Long> failIds = Lists.newArrayList();
            Map<Long, String> errorMagMap = new HashMap<>();
            for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
                if (!Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "非银行流水交付单");
                } else {
                    if (!AccountingCashierDeliverStatus.canCreateTaskStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
                    } else {
                        successList.add(customerServiceCashierAccounting);
                    }
                }
            }
            List<CustomerServiceCashierAccounting> finalSuccessList = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(successList)) {
                Map<Long, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingFileService.selectByAccountingCashierIdsAndFileType(successList.stream().map(CustomerServiceCashierAccounting::getId).collect(Collectors.toList()), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode())
                        .stream().collect(Collectors.groupingBy(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId));
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE); // 创建一个线程池
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                successList.forEach(customerServiceCashierAccounting -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            businessTaskService.createBankTask(customerServiceCashierAccounting, fileMap.getOrDefault(customerServiceCashierAccounting.getId(), Lists.newArrayList()), vo.getDeptId(), vo.getUserId(), vo.getOperName(), operTime, vo.getDdl(), vo.getAdminUserId());
                            finalSuccessList.add(customerServiceCashierAccounting);
                        } catch (Exception e) {
                            failList.add(customerServiceCashierAccounting);
                            failIds.add(customerServiceCashierAccounting.getId());
                            errorMagMap.put(customerServiceCashierAccounting.getId(), e.getMessage());
                        }
                    }, executorService);

                    futures.add(future);
                });
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                executorService.shutdown();
            }
            result.setSuccess(finalSuccessList);
            result.setFail(failList);
            if (!ObjectUtils.isEmpty(failIds)) {
                String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
                buildErrorDataList(failIds, batchNo, errorMagMap, AccountingCashierType.FLOW.getCode());
                result.setErrorDataBatchNo(batchNo);
            }
            if (!ObjectUtils.isEmpty(finalSuccessList)) {
                updateBatchById(finalSuccessList.stream().map(row -> new CustomerServiceCashierAccounting()
                        .setId(row.getId())
                        .setLastOperType(operType)
                        .setLastOperRemark("")
                        .setLastOperTime(operTime)
                        .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
                finalSuccessList.forEach(row -> {
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType(operType)
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime)
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                });
            }
            return result;
        }
    }

    @Override
    @Transactional
    public void updateByCommonNotice(OpenApiData openApiData, CommonInAccountVO commonInAccountVO, CustomerServiceCashierAccounting customerServiceCashierAccounting) {
        Map<String, String> operContent = new HashMap<>();
        CustomerServiceCashierAccounting update = new CustomerServiceCashierAccounting();
        update.setId(customerServiceCashierAccounting.getId());
        update.setRpaSearchTime(LocalDateTime.now());
        update.setProfitGetTime(LocalDateTime.now());
        boolean dealResult;
        String failReason = "";
        if (StringUtils.isEmpty(commonInAccountVO.getEmployees()) || StringUtils.isEmpty(commonInAccountVO.getTotalTax()) || StringUtils.isEmpty(commonInAccountVO.getNetProfit())
                || StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) || StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
            // 只要有一个值是空值，不处理交付单的值，并rpa执行结果设置为（异常），将回传数字写入备注中。key:value，key:value，key:value，key:value，key:value
            update.setRpaExeResult(InAccountRpaExeResult.F.getCode());
            String rpaRemark = "个税申报人数：" + (StringUtils.isEmpty(commonInAccountVO.getEmployees()) ? "null" : commonInAccountVO.getEmployees()) + "，"
                    + "本年个税申报工资总额：" + (StringUtils.isEmpty(commonInAccountVO.getTotalTax()) ? "null" : commonInAccountVO.getTotalTax()) + "，"
                    + "本年累计会计利润：" + (StringUtils.isEmpty(commonInAccountVO.getNetProfit()) ? "null" : commonInAccountVO.getNetProfit()) + "，"
                    + "本年累计主营成本：" + (StringUtils.isEmpty(commonInAccountVO.getOperatingCosts()) ? "null" : commonInAccountVO.getOperatingCosts()) + "，"
                    + "本年累计主营收入：" + (StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue()) ? "null" : commonInAccountVO.getOperatingRevenue())
                    + "利润：" + (StringUtils.isEmpty(commonInAccountVO.getProfit()) ? "null" : commonInAccountVO.getProfit())
                    + "暂估：" + (StringUtils.isEmpty(commonInAccountVO.getTempesti()) ? "null" : commonInAccountVO.getTempesti())
                    + "福利费：" + (StringUtils.isEmpty(commonInAccountVO.getWelfare()) ? "null" : commonInAccountVO.getWelfare())
                    + "招待费：" + (StringUtils.isEmpty(commonInAccountVO.getEnterain()) ? "null" : commonInAccountVO.getEnterain())
                    + "原材料取数：" + (StringUtils.isEmpty(commonInAccountVO.getRawMaterial()) ? "null" : commonInAccountVO.getRawMaterial())
                    + "人工工资取数：" + (StringUtils.isEmpty(commonInAccountVO.getLaborWages()) ? "null" : commonInAccountVO.getLaborWages())
                    + "费用取数：" + (StringUtils.isEmpty(commonInAccountVO.getCost()) ? "null" : commonInAccountVO.getCost())
                    + "期间费用取数：" + (StringUtils.isEmpty(commonInAccountVO.getPeriodCost()) ? "null" : commonInAccountVO.getPeriodCost())
                    ;
            update.setRpaRemark(rpaRemark);
            updateById(update);
            operContent.put("RPA执行结果", "失败");
            try {
                SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException(false);
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(null)
                                .setOperType(openApiData.getOperType() == 1 ? "发起取数请求" : "定时取数")
                                .setOperName(openApiData.getCreateBy())
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperRemark(rpaRemark)
                                .setOperUserId(Objects.isNull(user) ? null : user.getUserId())
                                .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            customerServiceService.updateLastInAccountId(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
            dealResult = false;
            failReason = "有值为空";
        } else {
            operContent.put("RPA执行结果", "成功");
            if (!StringUtils.isEmpty(commonInAccountVO.getEmployees())) {
                update.setTaxReportCount(Integer.parseInt(commonInAccountVO.getEmployees()));
                operContent.put("个税申报人数", commonInAccountVO.getEmployees());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getTotalTax())) {
                update.setTaxReportSalaryTotal(new BigDecimal(commonInAccountVO.getTotalTax()));
                operContent.put("本年个税申报工资总额", commonInAccountVO.getTotalTax());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getNetProfit())) {
                update.setProfitTotal(new BigDecimal(commonInAccountVO.getNetProfit()));
                operContent.put("本年累计会计利润", commonInAccountVO.getNetProfit());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getOperatingCosts())) {
                update.setMajorCostTotal(new BigDecimal(commonInAccountVO.getOperatingCosts()));
                operContent.put("本年累计主营成本", commonInAccountVO.getOperatingCosts());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getOperatingRevenue())) {
                update.setMajorIncomeTotal(new BigDecimal(commonInAccountVO.getOperatingRevenue()));
                operContent.put("本年累计主营收入", commonInAccountVO.getOperatingRevenue());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getProfit())) {
                update.setProfit(commonInAccountVO.getProfit());
                operContent.put("利润", commonInAccountVO.getProfit());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getTempesti())) {
                update.setTempesti(commonInAccountVO.getTempesti());
                operContent.put("暂估", commonInAccountVO.getTempesti());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getWelfare())) {
                update.setWelfare(commonInAccountVO.getWelfare());
                operContent.put("福利费", commonInAccountVO.getWelfare());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getEnterain())) {
                update.setEnterain(commonInAccountVO.getEnterain());
                operContent.put("招待费", commonInAccountVO.getEnterain());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getRawMaterial())) {
                update.setRawMaterial(commonInAccountVO.getRawMaterial());
                operContent.put("原材料", commonInAccountVO.getRawMaterial());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getLaborWages())) {
                update.setTempesti(commonInAccountVO.getLaborWages());
                operContent.put("人工工资", commonInAccountVO.getLaborWages());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getCost())) {
                update.setWelfare(commonInAccountVO.getCost());
                operContent.put("费用", commonInAccountVO.getCost());
            }
            if (!StringUtils.isEmpty(commonInAccountVO.getPeriodCost())) {
                update.setEnterain(commonInAccountVO.getPeriodCost());
                operContent.put("期间费用", commonInAccountVO.getPeriodCost());
            }
            update.setRpaExeResult(InAccountRpaExeResult.S.getCode());
            update.setRpaRemark("");
            updateById(update);
            try {
                SysUser user = remoteUserService.getUserByNickName(openApiData.getCreateBy(), openApiData.getDeptId(), SecurityConstants.INNER).getDataThrowException(false);
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(null)
                                .setOperType(openApiData.getOperType() == 1 ? "发起取数请求" : "定时取数")
                                .setOperName(openApiData.getCreateBy())
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setOperUserId(Objects.isNull(user) ? null : user.getUserId())
                                .setCreateBy(Constants.YSB_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
            customerServiceService.updateLastInAccountId(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
            dealResult = true;
        }
        if (!StringUtils.isEmpty(openApiData.getUuid())) {
            openApiNoticeRecordService.updateRpaDealResultAndSysDealResult(openApiData.getUuid(), 1, dealResult, failReason);
        }
    }

    @Override
    public void updateBankPaymentResultSettleAccountStatus(Long customerServicePeriodMonthId, LocalDateTime operTime, String operName, Long deptId, Long userId, String operType) {
        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServicePeriodMonthId);
        if (Objects.isNull(customerServicePeriodMonth)) {
            return;
        }
        // 所有入账交付单和银行流水交付单
        Map<Integer, List<CustomerServiceCashierAccounting>> accountingCashierMap = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>().eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServicePeriodMonthId)
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .in(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode(), AccountingCashierType.INCOME.getCode())).stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getType));
        List<CustomerServiceCashierAccounting> inAccountDeliverList = accountingCashierMap.get(AccountingCashierType.INCOME.getCode());
        if (ObjectUtils.isEmpty(inAccountDeliverList)) {
            CustomerServiceCashierAccounting inAccountDeliver = new CustomerServiceCashierAccounting();
            Integer period = customerServicePeriodMonth.getPeriod();
            List<CustomerServiceBankAccount> bankAccounts = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                            .eq(CustomerServiceBankAccount::getCustomerServiceId, customerServicePeriodMonth.getCustomerServiceId()))
                    .stream().filter(row -> (Objects.isNull(row.getAccountOpenDate()) || Integer.parseInt(row.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) <= period)
                            && (Objects.isNull(row.getAccountCloseDate()) || Integer.parseInt(row.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) >= period)).collect(Collectors.toList());
            // 银行流水交付单
            List<CustomerServiceCashierAccounting> bankDeliverList = accountingCashierMap.getOrDefault(AccountingCashierType.FLOW.getCode(), Lists.newArrayList());
            if (ObjectUtils.isEmpty(bankDeliverList)) {
                if (ObjectUtils.isEmpty(bankAccounts)) {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.UNOPENED.getCode());
                } else {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.WAIT_CREATE.getCode());
                }
            } else {
                if (ObjectUtils.isEmpty(bankAccounts)) {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.UNOPENED.getCode());
                } else {
                    List<String> bankAccountNumbers = bankAccounts.stream().map(CustomerServiceBankAccount::getBankAccountNumber).distinct().collect(Collectors.toList());
                    List<String> existsDeliverBankAccountNumbers = bankDeliverList.stream().map(CustomerServiceCashierAccounting::getBankAccountNumber).distinct().collect(Collectors.toList());
                    if (existsDeliverBankAccountNumbers.stream().noneMatch(bankAccountNumbers::contains)) {
                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.WAIT_CREATE.getCode());
                    } else {
                        if (bankAccountNumbers.stream().anyMatch(row -> !existsDeliverBankAccountNumbers.contains(row))) {
                            inAccountDeliver.setBankPaymentResult(BankPaymentResult.BANK_PARTIAL_MISSING.getCode());
                        } else {
                            if (bankDeliverList.stream().anyMatch(row -> Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode()))) {
                                inAccountDeliver.setBankPaymentResult(BankPaymentResult.EXCEPTION.getCode());
                            } else {
                                if (bankDeliverList.stream().anyMatch(row -> Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()) || Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode()) || Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()))) {
                                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.DELIVERY_IN_PROGRESS.getCode());
                                } else {
                                    if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NO_DELIVER.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NO_DELIVERY_REQUIRED.getCode());
                                    } else if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NO_ACCOUNT.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NO_FLOW.getCode());
                                    } else if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NORMAL.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NORMAL_COMPLETION.getCode());
                                    } else {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.COMPLETED.getCode());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            inAccountDeliver.setSettleAccountStatus(InAccountStatus.UN_IN.getCode());
            customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId).setBankPaymentResult(inAccountDeliver.getBankPaymentResult())
                    .setSettleAccountStatus(inAccountDeliver.getSettleAccountStatus()));
        } else {
            CustomerServiceCashierAccounting oldInAccountDeliver = inAccountDeliverList.get(0);
            CustomerServiceCashierAccounting inAccountDeliver = new CustomerServiceCashierAccounting();
            inAccountDeliver.setId(oldInAccountDeliver.getId());
            Integer period = customerServicePeriodMonth.getPeriod();
            List<CustomerServiceBankAccount> bankAccounts = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                            .eq(CustomerServiceBankAccount::getCustomerServiceId, customerServicePeriodMonth.getCustomerServiceId()))
                    .stream().filter(row -> (Objects.isNull(row.getAccountOpenDate()) || Integer.parseInt(row.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) <= period)
                            && (Objects.isNull(row.getAccountCloseDate()) || Integer.parseInt(row.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) >= period)).collect(Collectors.toList());
            // 银行流水交付单
            List<CustomerServiceCashierAccounting> bankDeliverList = accountingCashierMap.getOrDefault(AccountingCashierType.FLOW.getCode(), Lists.newArrayList());
            if (ObjectUtils.isEmpty(bankDeliverList)) {
                if (ObjectUtils.isEmpty(bankAccounts)) {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.UNOPENED.getCode());
                } else {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.WAIT_CREATE.getCode());
                }
            } else {
                if (ObjectUtils.isEmpty(bankAccounts)) {
                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.UNOPENED.getCode());
                } else {
                    List<String> bankAccountNumbers = bankAccounts.stream().map(CustomerServiceBankAccount::getBankAccountNumber).distinct().collect(Collectors.toList());
                    List<String> existsDeliverBankAccountNumbers = bankDeliverList.stream().map(CustomerServiceCashierAccounting::getBankAccountNumber).distinct().collect(Collectors.toList());
                    if (existsDeliverBankAccountNumbers.stream().noneMatch(bankAccountNumbers::contains)) {
                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.WAIT_CREATE.getCode());
                    } else {
                        if (bankAccountNumbers.stream().anyMatch(row -> !existsDeliverBankAccountNumbers.contains(row))) {
                            inAccountDeliver.setBankPaymentResult(BankPaymentResult.BANK_PARTIAL_MISSING.getCode());
                        } else {
                            if (bankDeliverList.stream().anyMatch(row -> Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode()))) {
                                inAccountDeliver.setBankPaymentResult(BankPaymentResult.EXCEPTION.getCode());
                            } else {
                                if (bankDeliverList.stream().anyMatch(row -> Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()) || Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode()) || Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()))) {
                                    inAccountDeliver.setBankPaymentResult(BankPaymentResult.DELIVERY_IN_PROGRESS.getCode());
                                } else {
                                    if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NO_DELIVER.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NO_DELIVERY_REQUIRED.getCode());
                                    } else if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NO_ACCOUNT.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NO_FLOW.getCode());
                                    } else if (bankDeliverList.stream().allMatch(row -> Objects.equals(row.getDeliverResult(), AccountingCashierDeliverResult.NORMAL.getCode()))) {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.NORMAL_COMPLETION.getCode());
                                    } else {
                                        inAccountDeliver.setBankPaymentResult(BankPaymentResult.COMPLETED.getCode());
                                    }
                                }
                            }
                        }
                    }
                }
            }
//            if (!Objects.equals(oldInAccountDeliver.getSettleAccountStatus(), InAccountStatus.DONE.getCode())) {
//                if (Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode()).contains(oldInAccountDeliver.getDeliverStatus())) {
//                    inAccountDeliver.setSettleAccountStatus(InAccountStatus.UN_IN.getCode());
//                } else {
//                    if (Lists.newArrayList(BankPaymentResult.WAIT_CREATE.getCode(), BankPaymentResult.BANK_PARTIAL_MISSING.getCode(), BankPaymentResult.EXCEPTION.getCode(), BankPaymentResult.DELIVERY_IN_PROGRESS.getCode()).contains(inAccountDeliver.getBankPaymentResult())) {
//                        inAccountDeliver.setSettleAccountStatus(InAccountStatus.IN_UN_DO.getCode());
//                    } else {
//                        inAccountDeliver.setSettleAccountStatus(InAccountStatus.DONE.getCode());
//                        inAccountDeliver.setEndTime(LocalDate.now());
//                    }
//                }
//            } else {
//                inAccountDeliver.setSettleAccountStatus(InAccountStatus.DONE.getCode());
//            }
            if (Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()).contains(oldInAccountDeliver.getDeliverStatus())) {
                inAccountDeliver.setSettleAccountStatus(InAccountStatus.UN_IN.getCode());
            } else {
                if (Lists.newArrayList(BankPaymentResult.WAIT_CREATE.getCode(), BankPaymentResult.BANK_PARTIAL_MISSING.getCode(), BankPaymentResult.EXCEPTION.getCode(), BankPaymentResult.DELIVERY_IN_PROGRESS.getCode()).contains(inAccountDeliver.getBankPaymentResult())) {
                    inAccountDeliver.setSettleAccountStatus(InAccountStatus.IN_UN_DO.getCode());
                } else {
                    inAccountDeliver.setSettleAccountStatus(InAccountStatus.DONE.getCode());
                    if (Objects.isNull(oldInAccountDeliver.getEndTime())) {
                        inAccountDeliver.setEndTime(LocalDate.now());
                        inAccountDeliver.setEndUserId(userId);
                    }
                    inAccountDeliver.setLastEndTime(LocalDate.now());
                    inAccountDeliver.setLastEndUserId(userId);
                }
            }
            if (!Objects.equals(oldInAccountDeliver.getBankPaymentResult(), inAccountDeliver.getBankPaymentResult()) || !Objects.equals(oldInAccountDeliver.getSettleAccountStatus(), inAccountDeliver.getSettleAccountStatus())) {
                if (!StringUtils.isEmpty(operType)) {
                    inAccountDeliver.setLastOperTime(operTime);
                    inAccountDeliver.setLastOperType(operType);
                    inAccountDeliver.setLastOperName(operName);
                    inAccountDeliver.setLastOperRemark("");
                }
                updateById(inAccountDeliver);
                customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId).setBankPaymentResult(inAccountDeliver.getBankPaymentResult())
                        .setSettleAccountStatus(inAccountDeliver.getSettleAccountStatus()));
                if (!StringUtils.isEmpty(operType)) {
                    try {
                        Map<String, String> operContent = new LinkedHashMap<>();
                        operContent.put("交付单标题", inAccountDeliver.getTitle());
                        operContent.put("银行流水结果", BankPaymentResult.getByCode(inAccountDeliver.getBankPaymentResult()).getDesc());
                        operContent.put("结账状态", InAccountStatus.getByCode(inAccountDeliver.getSettleAccountStatus()).getName());
                        asyncLogService.saveBusinessLog(
                                new BusinessLogDTO()
                                        .setBusinessId(inAccountDeliver.getId())
                                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                        .setDeptId(deptId)
                                        .setOperType(operType)
                                        .setOperName(operName)
                                        .setCreateTime(operTime)
                                        .setOperContent(JSONObject.toJSONString(operContent))
                                        .setOperUserId(userId));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void remoteUpdateBankPaymentResultSettleAccountStatus(UpdateBankPaymentResultSettleAccountStatusVO vo) {
        if (ObjectUtils.isEmpty(vo.getCustomerServicePeriodMonthIds())) {
            return;
        }
        LocalDateTime operTime = LocalDateTime.now();
        for (Long customerServicePeriodMonthId : vo.getCustomerServicePeriodMonthIds()) {
            try {
                updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime, vo.getOperName(), vo.getDeptId(), vo.getUserId(), vo.getOperType());
            } catch (Exception e) {
                log.error("处理更新银行流水结果和结账状态异常:{}", e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public void updateBankAccountingCashierStatus(List<Long> ids, Integer deliverStatus, String operName, String operRemark, LocalDateTime operTime, String operType, Long deptId, Long userId) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>().eq(CustomerServiceCashierAccounting::getIsDel, false)
                .in(CustomerServiceCashierAccounting::getId, ids));
        if (ObjectUtils.isEmpty(customerServiceCashierAccountings)) {
            return;
        }
        updateBatchById(customerServiceCashierAccountings.stream().map(row -> new CustomerServiceCashierAccounting()
                .setId(row.getId())
                .setDeliverStatus(deliverStatus)
                .setLastOperRemark(operRemark)
                .setLastOperName(operName)
                .setLastOperType(operType)
                .setLastOperTime(operTime)).collect(Collectors.toList()));
        List<Long> customerServicePeriodMonthIds = customerServiceCashierAccountings.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId -> updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime, operName, deptId, userId, "银行流水任务处理异常修改"));
        }
    }

    @Override
    @Transactional
    public void updateBankAccountingCashierStatusBatch(List<CustomerServiceCashierAccounting> cashierAccountings, Integer deliverStatus, String operName, String operRemark, LocalDateTime operTime, String operType, Long deptId, Long userId, Integer materialIntegrity) {
        if (ObjectUtils.isEmpty(cashierAccountings)) {
            return;
        }
        updateBatchById(cashierAccountings.stream().map(row -> new CustomerServiceCashierAccounting()
                .setId(row.getId())
                .setIsAssistantFinish(true)
                .setInTime(Objects.isNull(row.getInTime()) ? LocalDate.now() : null)
                .setCompleteTime(Objects.isNull(row.getCompleteTime()) ? LocalDateTime.now() : null)
                .setCompleteUserId(Objects.isNull(row.getCompleteTime()) ? userId : null)
                .setCompleteUserName(Objects.isNull(row.getCompleteTime()) ? operName : null)
                .setDeliverStatus(deliverStatus)
                .setDeliverResult(row.getDeliverResult())
                .setMaterialIntegrity(materialIntegrity)
                .setLastInTime(LocalDate.now())
                .setLastCompleteUserId(userId)
                .setLastOperRemark(operRemark)
                .setLastOperName(operName)
                .setLastOperType(operType)
                .setLastOperTime(operTime)).collect(Collectors.toList()));
        List<Long> customerServicePeriodMonthIds = cashierAccountings.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
            customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId -> updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime, operName, deptId, userId, "银行流水任务审核通过修改"));
        }
    }

    @Override
    public boolean checkInAccountIsEnd(Long customerServiceId, Integer period) {
        return count(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceCashierAccounting::getPeriod, period)
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getSettleAccountStatus, InAccountStatus.DONE.getCode())) > 0;
    }

    @Override
    public boolean checkInAccountIsEnd(Long customerServiceId, Integer startPeriod, Integer endPeriod) {
        LambdaQueryWrapper<CustomerServiceCashierAccounting> queryWrapper = new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .ne(CustomerServiceCashierAccounting::getSettleAccountStatus, InAccountStatus.DONE.getCode());
        if (!Objects.isNull(startPeriod) && !Objects.isNull(endPeriod)) {
            queryWrapper.le(CustomerServiceCashierAccounting::getPeriod, endPeriod)
                    .ge(CustomerServiceCashierAccounting::getPeriod, startPeriod);
        }
        return count(queryWrapper) == 0;
    }

    @Override
    public List<AccountingCashierBankSimpleVO> bankPaymentByPeriodId(Long customerServicePeriodMonthId) {
        if (Objects.isNull(customerServicePeriodMonthId)) {
            return Collections.emptyList();
        }
        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServicePeriodMonthId);
        if (Objects.isNull(customerServicePeriodMonth)) {
            return Collections.emptyList();
        }
        List<CustomerServiceBankAccount> allBankList = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>().eq(CustomerServiceBankAccount::getCustomerServiceId, customerServicePeriodMonth.getCustomerServiceId()));
        List<CustomerServiceBankAccount> bankList = allBankList.stream().filter(row -> (Objects.isNull(row.getAccountOpenDate()) || Integer.parseInt(row.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) <= customerServicePeriodMonth.getPeriod())
                && (Objects.isNull(row.getAccountCloseDate()) || Integer.parseInt(row.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) >= customerServicePeriodMonth.getPeriod())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(bankList)) {
            return Collections.emptyList();
        }
        List<AccountingCashierBankSimpleVO> result = Lists.newArrayList();
        Map<String, CustomerServiceCashierAccounting> cashierMap = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>().eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServicePeriodMonthId)
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode()))
                .stream().collect(Collectors.toMap(CustomerServiceCashierAccounting::getBankAccountNumber, Function.identity(), (v1, v2) -> v1));
        bankList.sort(Comparator.comparing(CustomerServiceBankAccount::getCreateTime));
        bankList.forEach(row -> {
            CustomerServiceCashierAccounting customerServiceCashierAccounting = cashierMap.get(row.getBankAccountNumber());
            result.add(AccountingCashierBankSimpleVO.builder()
                    .id(Objects.isNull(customerServiceCashierAccounting) ? null : customerServiceCashierAccounting.getId())
                    .bankName(row.getBankName())
                    .bankAccountNumber(row.getBankAccountNumber())
                    .materialMedia(Objects.isNull(customerServiceCashierAccounting) ? null : customerServiceCashierAccounting.getMaterialMedia())
                    .materialMediaStr(Objects.isNull(customerServiceCashierAccounting) ? null : AccountingCashierMaterialMedia.getByCode(customerServiceCashierAccounting.getMaterialMedia()).getDesc())
                    .deliverStatus(Objects.isNull(customerServiceCashierAccounting) ? 0 : customerServiceCashierAccounting.getDeliverStatus())
                    .deliverStatusStr(Objects.isNull(customerServiceCashierAccounting) ? "待创建" : AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName())
                    .materialIntegrity(Objects.isNull(customerServiceCashierAccounting) ? null : customerServiceCashierAccounting.getMaterialIntegrity())
                    .materialIntegrityStr(Objects.isNull(customerServiceCashierAccounting) ? "-" : getMaterialIntegrityStr(customerServiceCashierAccounting.getMaterialIntegrity(), customerServiceCashierAccounting.getMaterialSupplementStatus()))
                    .materialSupplementStatus(Objects.isNull(customerServiceCashierAccounting) ? null : customerServiceCashierAccounting.getMaterialSupplementStatus())
                    .build());
            if (!Objects.isNull(customerServiceCashierAccounting)) {
                cashierMap.remove(row.getBankAccountNumber());
            }
        });
        if (!ObjectUtils.isEmpty(cashierMap)) {
            cashierMap.forEach((k, v) ->
                result.add(AccountingCashierBankSimpleVO.builder()
                        .id(v.getId())
                        .bankName(v.getBankName())
                        .bankAccountNumber(v.getBankAccountNumber())
                        .materialMedia(v.getMaterialMedia())
                        .materialMediaStr(AccountingCashierMaterialMedia.getByCode(v.getMaterialMedia()).getDesc())
                        .deliverStatus(v.getDeliverStatus())
                        .deliverStatusStr(AccountingCashierDeliverStatus.getByCode(v.getDeliverStatus()).getName())
                        .materialIntegrity(v.getMaterialIntegrity())
                        .materialIntegrityStr(getMaterialIntegrityStr(v.getMaterialIntegrity(), v.getMaterialSupplementStatus()))
                        .materialSupplementStatus(v.getMaterialSupplementStatus())
                        .build()
                )
            );
        }
        return result;
    }

    @Override
    public List<AccountingCashierInAccountSimpleVO> inAccountByPeriodId(Long customerServicePeriodMonthId) {
        if (ObjectUtils.isEmpty(customerServicePeriodMonthId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>().eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServicePeriodMonthId)
                .eq(CustomerServiceCashierAccounting::getIsDel, false).in(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode(), AccountingCashierType.CHANGE.getCode())
                .orderByAsc(CustomerServiceCashierAccounting::getType).orderByAsc(CustomerServiceCashierAccounting::getId))
                .stream().map(row -> AccountingCashierInAccountSimpleVO.builder()
                        .id(row.getId())
                        .type(AccountingCashierType.getByCode(row.getType()).getName())
                        .materialMedia(row.getMaterialMedia())
                        .materialMediaStr(AccountingCashierMaterialMedia.getByCode(row.getMaterialMedia()).getDesc())
                        .deliverStatus(row.getDeliverStatus())
                        .deliverStatusStr(AccountingCashierDeliverStatus.getByCode(row.getDeliverStatus()).getName())
                        .materialIntegrity(row.getMaterialIntegrity())
                        .materialIntegrityStr(getMaterialIntegrityStr(row.getMaterialIntegrity(), row.getMaterialSupplementStatus()))
                        .materialSupplementStatus(row.getMaterialSupplementStatus())
                        .build()).collect(Collectors.toList());
    }

    @Override
    public List<MaterialFileSimpleVO> materialFilesByPeriodId(Long customerServicePeriodMonthId, Integer fileType, String bankName, String fileRemark) {
        return baseMapper.materialFilesByPeriodId(customerServicePeriodMonthId, fileType, bankName, fileRemark);
    }

    @Override
    public List<String> materialFileBankSelect(Long customerServicePeriodMonthId) {
        return baseMapper.materialFileBankSelect(customerServicePeriodMonthId);
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccountingFile> deleteMaterialFiles(List<Long> ids, Long deptId) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new ServiceException("请选择要删除的文件");
        }
        TCommonOperateDTO<CustomerServiceCashierAccountingFile> result = new TCommonOperateDTO<>();
        List<CustomerServiceCashierAccountingFile> totalMaterialFiles = customerServiceCashierAccountingFileService.list(new LambdaQueryWrapper<CustomerServiceCashierAccountingFile>()
                .eq(CustomerServiceCashierAccountingFile::getIsDel, false).in(CustomerServiceCashierAccountingFile::getId, ids));
        if (ObjectUtils.isEmpty(totalMaterialFiles)) {
            throw new ServiceException("文件不存在");
        }
        result.setTotal(totalMaterialFiles);
        List<CustomerServiceCashierAccountingFile> successList = totalMaterialFiles;
        List<CustomerServiceCashierAccountingFile> failList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(successList)) {
            OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(deptId, SecurityUtils.getUserId());
            LocalDateTime operTime = LocalDateTime.now();
            Map<Long, List<CustomerServiceCashierAccountingFile>> accountingCashierMap = totalMaterialFiles.stream().collect(Collectors.groupingBy(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId));
            accountingCashierMap.forEach((k, v) -> {
                List<MaterialFileSimpleErrorVO> exports = materialFileBankError(baseMapper.materialFilesByIds(v.stream().map(CustomerServiceCashierAccountingFile::getId).collect(Collectors.toList())));
                ExcelUtil<MaterialFileSimpleErrorVO> util = new ExcelUtil<>(MaterialFileSimpleErrorVO.class);
                String fileUrl = util.exportExcelAndUpload(exports, "删除文件明细");
                CommonFileVO fileVO = CommonFileVO.builder().fileName("删除文件明细").fileUrl(fileUrl).build();
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(k)
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("删除文件")
                                    .setOperName(operateUserInfo.getOperName())
                                    .setCreateTime(operTime)
                                    .setOperUserId(operateUserInfo.getUserId())
                                    .setOperImages(JSONArray.toJSONString(Collections.singletonList(fileVO))));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
            customerServiceCashierAccountingFileService.updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccountingFile().setId(row.getId())
                    .setIsDel(true)).collect(Collectors.toList()));
//
//            CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(accountingCashierId);
//            if (!Objects.isNull(customerServiceCashierAccounting)) {
//                Long customerServicePeriodMonthId = customerServiceCashierAccounting.getCustomerServicePeriodMonthId();
//                try {
//                    asyncLogService.saveBusinessLog(
//                            new BusinessLogDTO()
//                                    .setBusinessId(customerServicePeriodMonthId)
//                                    .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_PERIOD.getCode())
//                                    .setDeptId(deptId)
//                                    .setOperType("删除文件")
//                                    .setOperName(operateUserInfo.getOperName())
//                                    .setCreateTime(operTime)
//                                    .setOperUserId(operateUserInfo.getUserId())
//                                    .setOperImages(JSONArray.toJSONString(Collections.singletonList(fileVO))));
//                } catch (Exception e) {
//                    log.error("新增业务日志失败:{}", e.getMessage());
//                    throw new ServiceException("操作记录写入失败，请稍后重试");
//                }
//            }
        }
        result.setSuccess(successList);
        result.setFail(failList);
        return result;
    }

    @Override
    public List<MaterialFileSimpleErrorVO> getMaterialFilesByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return materialFileBankError(baseMapper.materialFilesByIds(ids));
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierMiniListAccountingDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        List<CommonDeptCountDTO> data;
        if (Objects.equals(accountingCashierType, AccountingCashierType.FLOW.getCode())) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            if (statisticType == 0) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankUnOpenAccountingDeptList(userDeptDTO, queryDeptIds, DateUtils.getPrePeriod());
            } else if (statisticType == 1) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankPatrialMisssAccountingDeptList(userDeptDTO, queryDeptIds, nowPeriod);
            } else if (statisticType == 2 || statisticType == 9 || statisticType == 10 || statisticType == 11) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankWaitCreateAccountingDeptList(userDeptDTO, queryDeptIds, nowPeriod, statisticType);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAccountingDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.FLOW.getCode());
            } else if (statisticType == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierLakOfMaterialDeptList(userDeptDTO, queryDeptIds, AccountingCashierType.FLOW.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.FLOW.getCode());
            }
        } else if (Objects.equals(accountingCashierType, AccountingCashierType.INCOME.getCode())) {
            if (statisticType == 2) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierInAccountWaitCreateAccountingDeptList(userDeptDTO, queryDeptIds);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAccountingDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.INCOME.getCode());
            } else if (statisticType == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierLakOfMaterialDeptList(userDeptDTO, queryDeptIds, AccountingCashierType.INCOME.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.INCOME.getCode());
            }
        } else {
            Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
            data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.CHANGE.getCode());
        }
        return data;
    }

    @Override
    public List<CommonDeptCountDTO> accountingCashierMiniListAdvisorDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        List<CommonDeptCountDTO> data;
        if (Objects.equals(accountingCashierType, AccountingCashierType.FLOW.getCode())) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            if (statisticType == 0) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankUnOpenAdvisorDeptList(userDeptDTO, queryDeptIds, DateUtils.getPrePeriod());
            } else if (statisticType == 1) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankPatrialMisssAdvisorDeptList(userDeptDTO, queryDeptIds, nowPeriod);
            } else if (statisticType == 2 || statisticType == 9 || statisticType == 10 || statisticType == 11) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankWaitCreateAdvisorDeptList(userDeptDTO, queryDeptIds, nowPeriod, statisticType);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAdvisorDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.FLOW.getCode());
            } else if (statisticType == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierLakOfMaterialAdvisorDeptList(userDeptDTO, queryDeptIds, AccountingCashierType.FLOW.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAdvisorDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.FLOW.getCode());
            }
        } else if (Objects.equals(accountingCashierType, AccountingCashierType.INCOME.getCode())) {
            if (statisticType == 2) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierInAccountWaitCreateAdvisorDeptList(userDeptDTO, queryDeptIds);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAdvisorDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.INCOME.getCode());
            } else if (statisticType == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierLakOfMaterialAdvisorDeptList(userDeptDTO, queryDeptIds, AccountingCashierType.INCOME.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAdvisorDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.INCOME.getCode());
            }
        } else {
            Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
            data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverAdvisorDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.CHANGE.getCode());
        }
        return data;
    }

    @Override
    public List<String> getBankAccountNumberByListBankAccountNumber(List<String> bankAccountNumbers) {
        if (ObjectUtils.isEmpty(bankAccountNumbers)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())
                .in(CustomerServiceCashierAccounting::getBankAccountNumber, bankAccountNumbers)
                .select(CustomerServiceCashierAccounting::getBankAccountNumber))
                .stream().map(CustomerServiceCashierAccounting::getBankAccountNumber).distinct().collect(Collectors.toList());
    }

    @Override
    public void rpaInAccountTask() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime yesterdayMin = LocalDateTime.of(yesterday, LocalTime.of(0, 0, 0));
        LocalDateTime yesterdayMax = LocalDateTime.of(yesterday, LocalTime.of(23, 59, 59));
        List<Long> needRpaCustomerServiceIds = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                        .le(CustomerServiceCashierAccounting::getCompleteTime, yesterdayMax).ge(CustomerServiceCashierAccounting::getCompleteTime, yesterdayMin)
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                .select(CustomerServiceCashierAccounting::getCustomerServiceId))
                .stream().map(CustomerServiceCashierAccounting::getCustomerServiceId).distinct().collect(Collectors.toList());
        if (ObjectUtils.isEmpty(needRpaCustomerServiceIds)) {
            return;
        }
        List<CustomerInAccountMaxPeriodDTO> customerMaxPeriodList = baseMapper.selectCustomerInAccountMaxPeriod(needRpaCustomerServiceIds);
        if (ObjectUtils.isEmpty(customerMaxPeriodList)) {
            return;
        }
        List<CustomerServicePeriodMonth> periodList = customerServicePeriodMonthMapper.selectBatchByCustomerServiceIdAndPeriod(customerMaxPeriodList);
        Map<String, CustomerServicePeriodMonth> periodMap = periodList.stream().collect(Collectors.toMap(row -> row.getCustomerServiceId() + "_" + row.getPeriod(), Function.identity(), (v1, v2) -> v1));
        Map<Long, CCustomerService> customerServiceMap = customerServiceMapper.selectBatchIds(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerMaxPeriodList.stream().map(CustomerInAccountMaxPeriodDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
        customerMaxPeriodList.forEach(row -> {
            CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
            if (!Objects.isNull(customerService) && !Objects.isNull(customerService.getBusinessDeptId())) {
                Long businessDeptId = customerService.getBusinessDeptId();
                String groupId = businessGroupProperties.getMap().get(businessDeptId);
                if (!StringUtils.isEmpty(groupId)) {
                    GroupMap groupMap = GroupMap.getByGroupId(groupId);
                    if (!Objects.isNull(groupMap)) {
                        String platType = customerServiceTagMap.getOrDefault(row.getCustomerServiceId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                                .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
                        CustomerServicePeriodMonth customerServicePeriodMonth = periodMap.get(row.getCustomerServiceId() + "_" + row.getMaxPeriod());
                        RpaInAccountVO vo = new RpaInAccountVO();
                        vo.setCreditCode(customerService.getCreditCode());
                        vo.setPeriod(row.getMaxPeriod());
                        vo.setCustomerName(customerService.getCustomerName());
                        vo.setOperType(2);
                        vo.setGroupId(groupId);
                        vo.setGroupName(groupMap.getGroupName());
                        vo.setPlatType(platType);
                        vo.setTaxNumber(customerService.getTaxNumber());
                        vo.setCustomerServiceId(customerService.getId());
                        vo.setCustomerServicePeriodMonthId(Objects.isNull(customerServicePeriodMonth) ? null : customerServicePeriodMonth.getId());
                        vo.setOperator("系统");
                        vo.setUserId(1L);
                        remoteThirdpartService.accountingCashierGetProfit(vo, SecurityConstants.INNER);
                    }
                }
            }
        });
    }

    @Override
    public List<CommonDeptCountDTO> customerAccountingCashierMiniListAccountingDeptCountList(UserDeptDTO userDeptDTO, List<Long> queryDeptIds, Integer accountingCashierType, Integer statisticType) {
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        List<CommonDeptCountDTO> data;
        if (Objects.equals(accountingCashierType, AccountingCashierType.FLOW.getCode())) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            if (statisticType == 0) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankUnOpenAccountingDeptList(userDeptDTO, queryDeptIds, DateUtils.getPrePeriod());
            } else if (statisticType == 1) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankPatrialMisssCustomerAccountingDeptList(userDeptDTO, queryDeptIds, nowPeriod);
            } else if (statisticType == 2) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankWaitCreateCustomerAccountingDeptList(userDeptDTO, queryDeptIds, nowPeriod);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverCustomerAccountingDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.FLOW.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverCustomerAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.FLOW.getCode());
            }
        } else if (Objects.equals(accountingCashierType, AccountingCashierType.INCOME.getCode())) {
            if (statisticType == 2) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierInAccountWaitCreateCustomerAccountingDeptList(userDeptDTO, queryDeptIds);
            } else if (statisticType == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverCustomerAccountingDeptList(userDeptDTO, queryDeptIds, null, 1, AccountingCashierType.INCOME.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverCustomerAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.INCOME.getCode());
            }
        } else {
            Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(statisticType);
            data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverCustomerAccountingDeptList(userDeptDTO, queryDeptIds, deliverStatus, 0, AccountingCashierType.CHANGE.getCode());
        }
        return data;
    }

    @Override
    public List<MaterialRepeatDTO> repeatFile(Long customerServiceId, String fileName) {
        if (Objects.isNull(customerServiceId) || StringUtils.isEmpty(fileName)) {
            return Collections.emptyList();
        }
        List<MaterialRepeatDTO> data = baseMapper.selectRepeatFile(customerServiceId, fileName);
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d ->
                d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）"))));
        }
        return data;
    }

    @Override
    public List<RemoteAccountingCashierSimpleDTO> getAccountingCashierByPeriodIdsAndAccountingCashierType(RemoteAccountingCashierPeriodTypeVO vo) {
        if (ObjectUtils.isEmpty(vo.getPeriodIds())) {
            return Collections.emptyList();
        }
        List<RemoteAccountingCashierSimpleDTO> cashierList = baseMapper.getAccountingCashierByPeriodIdsAndAccountingCashierType(vo);
        if (!ObjectUtils.isEmpty(cashierList)) {
            Map<Long, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingFileService.selectByAccountingCashierIdsAndFileType(cashierList.stream().map(RemoteAccountingCashierSimpleDTO::getId).collect(Collectors.toList()),
                    AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode()).stream().collect(Collectors.groupingBy(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId));
            Map<String, List<BusinessTask>> needCheckBusinessTaskMap = Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.FLOW.getCode()) ?
                    businessTaskService.getBatchNeedCheckBusinessTaskByAccountingCashier(cashierList.stream().map(row ->
                            new CustomerServiceCashierAccounting().setCustomerServicePeriodMonthId(row.getCustomerServicePeriodMonthId()).setBankAccountNumber(row.getBankAccountNumber())).collect(Collectors.toList())) :
                    Maps.newHashMap();
            cashierList.forEach(row -> {
                List<CustomerServiceCashierAccountingFile> fileList = fileMap.getOrDefault(row.getId(), Lists.newArrayList());
                CustomerServiceCashierAccountingFile statementFile = fileList.stream().filter(f -> Objects.equals(f.getSubFileType(), 1)).findFirst().orElse(null);
                CustomerServiceCashierAccountingFile receiptFile = fileList.stream().filter(f -> Objects.equals(f.getSubFileType(), 2)).findFirst().orElse(null);
                row.setStatementFile(Objects.isNull(statementFile) ? null : CommonFileVO.builder().fileName(statementFile.getFileName()).fileUrl(statementFile.getFileUrl()).build());
                row.setReceiptFile(Objects.isNull(receiptFile) ? null : CommonFileVO.builder().fileName(receiptFile.getFileName()).fileUrl(receiptFile.getFileUrl()).build());
                row.setHasNeedCheckBusinessTask(Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.FLOW.getCode()) && needCheckBusinessTaskMap.containsKey(row.getCustomerServicePeriodMonthId() + "_" + row.getBankAccountNumber()));
            });
        }
        return cashierList;
    }

    @Override
    public List<CustomerServiceCashierAccounting> selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(List<BizIdBankAccountDTO> dtoList) {
        if (ObjectUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchByCustomerServicePeriodMonthIdAndBankAccountNumber(dtoList);
    }

    @Override
    public void saveNewPeriodInAccount(Integer period) {
        customerServicePeriodMonthMapper.updateNewInAccountStatus(period);
        baseMapper.saveNewPeriodInAccount(period);
    }

    @Override
    public void saveExceptionPeriodInAccount(Integer period) {
        customerServicePeriodMonthMapper.updateExceptionInAccountStatus(period);
        baseMapper.saveExceptionPeriodInAccount(period);
    }

    @Override
    public void addInAccountFromPeriod(CCustomerService customerService, CustomerServicePeriodMonth monthPeriod, Long deptId, Long userId, String operName) {
        if (customerService == null) {
            throw new ServiceException("客户服务 必传");
        }
        if (monthPeriod == null) {
            throw new ServiceException("账期 必传");
        }
        if (!Objects.equals(customerService.getId(), monthPeriod.getCustomerServiceId())) {
            throw new ServiceException("数据不一致");
        }

        CustomerServiceCashierAccounting newEntry = covCustomerServiceInAccountByCustomerAndPeriod(customerService, monthPeriod, deptId, userId, operName);

        save(newEntry);

        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(newEntry.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(deptId)
                            .setOperType(Objects.equals(2, monthPeriod.getAddFromType()) ? "补账分派创建" : "新客户创建")
                            .setOperName(operName)
                            .setCreateTime(LocalDateTime.now())
                            .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }

        customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(monthPeriod.getId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()));
    }

    @Override
    @Transactional
    public void updateBankInfoByCustomerServiceId(Long customerServiceId, CustomerServiceBankAccount oldBankAccount, String bankAccountNumber, String bankName, Long deptId, Long userId, String operName) {
        List<CustomerServiceCashierAccounting> cashierAccountingList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceCashierAccounting::getBankAccountNumber, oldBankAccount.getBankAccountNumber()));
        if (ObjectUtils.isEmpty(cashierAccountingList)) {
            return;
        }
        LocalDateTime operTime = LocalDateTime.now();
        updateBatchById(cashierAccountingList.stream().map(row -> new CustomerServiceCashierAccounting().setId(row.getId())
                .setBankName(bankName)
                .setBankAccountNumber(bankAccountNumber)
                .setLastOperTime(operTime)
                .setLastOperType("银行信息修改")
                .setLastOperName(operName)
                .setTitle(getAccountingCashierTitle(AccountingCashierType.FLOW.getCode(), row.getPeriod(), bankName, bankAccountNumber))).collect(Collectors.toList()));
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("修改前", String.format("%s，%s", oldBankAccount.getBankName(), oldBankAccount.getBankAccountNumber()));
        map.put("修改后", String.format("%s，%s", bankName, bankAccountNumber));
        cashierAccountingList.forEach(row -> {
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(row.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(deptId)
                                .setOperType("银行信息修改")
                                .setOperName(operName)
                                .setOperContent(JSONObject.toJSONString(map))
                                .setCreateTime(operTime)
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        });
    }

    @Override
    public void createFlowAccountingCashier(String jobParam) {
        Integer period = null;
        Long customerServicePeriodMonthId = null;
        LocalDateTime insertTime = LocalDateTime.now();
        if (StringUtils.isEmpty(jobParam)) {
            period = DateUtils.getPrePeriod();
        } else {
            if (jobParam.startsWith("period")) {
                period = Integer.parseInt(jobParam.split(":")[1]);
            } else {
                customerServicePeriodMonthId = Long.parseLong(jobParam.split(":")[1]);
            }
        }
        baseMapper.createFlowAccountingCashier(period, customerServicePeriodMonthId, insertTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 查询到刚刚插入的所有数据
        List<CustomerServiceCashierAccounting> autoCreateCashierList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCreateTime, insertTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .eq(CustomerServiceCashierAccounting::getMaterialMedia, AccountingCashierMaterialMedia.BANK_CARD.getCode())
                .eq(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())
                .eq(CustomerServiceCashierAccounting::getHasBankPayment, true)
                .eq(CustomerServiceCashierAccounting::getLastOperType, "系统创建"));
        if (!ObjectUtils.isEmpty(autoCreateCashierList)) {
            // 走RPA的逻辑
            List<Long> customerServiceIds = autoCreateCashierList.stream().map(CustomerServiceCashierAccounting::getCustomerServiceId).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerMap = customerServiceService.list(new LambdaQueryWrapper<CCustomerService>()
                            .in(CCustomerService::getId, customerServiceIds))
                    .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
            ExecutorService executorService = Executors.newFixedThreadPool(5); // 创建一个线程池
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            autoCreateCashierList.forEach(row -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> autoFlowRpaDeal(row, "系统", customerMap, 1L, null), executorService);

                futures.add(future);
            });
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            executorService.shutdown();
        }
    }

    @Override
    public void rpaCheckFileTask() {
        LocalDateTime now = LocalDateTime.now();
        Long nowTimeStamp = DateUtils.localDateToMillSecond(now);
        List<OpenApiCheckFileRecord> waitCheckFileRecords = openApiCheckFileRecordService.list(new LambdaQueryWrapper<OpenApiCheckFileRecord>()
                .eq(OpenApiCheckFileRecord::getStatus, 1)
                .le(OpenApiCheckFileRecord::getSearchTime, now));
        if (ObjectUtils.isEmpty(waitCheckFileRecords)) {
            return;
        }
        if (!ObjectUtils.isEmpty(waitCheckFileRecords)) {
            ExecutorService executorService = Executors.newFixedThreadPool(5); // 创建一个线程池
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            waitCheckFileRecords.forEach(row -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    String checkFileKey = "checkFile:checking:" + row.getId();
                    if (!redisService.hasKey(checkFileKey)) {
                        redisService.setCacheObject(checkFileKey, 1L, 60 * 60 * 12L, TimeUnit.SECONDS);
                        openApiCheckFileRecordService.updateById(new OpenApiCheckFileRecord().setId(row.getId()).setStatus(4));
                        CheckFilesVO vo = JSONObject.parseObject(row.getCheckFileParam(), CheckFilesVO.class);
                        vo.setTaskId(row.getId());
                        remoteThirdpartService.checkBankReceiptFile(vo, SecurityConstants.INNER);
                    }
                }, executorService);

                futures.add(future);
            });
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            executorService.shutdown();
        }
    }

    @Override
    @Async
    public void autoFlowRpaDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, Long customerServiceId, Long userId, Long deptId) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceId);
        if (Objects.isNull(customerService)) {
            return;
        }
        Map<Long, CCustomerService> customerMap = new HashMap<>();
        customerMap.put(customerServiceId, customerService);
        autoFlowRpaDeal(customerServiceCashierAccounting, operator, customerMap, userId, deptId);
    }

    @Override
    public void autoFlowRpaDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, Map<Long, CCustomerService> customerMap, Long userId, Long deptId) {
        CCustomerService customerService = customerMap.get(customerServiceCashierAccounting.getCustomerServiceId());
        String groupId = Objects.isNull(customerService) || Objects.isNull(customerService.getBusinessDeptId()) ? "" : businessGroupProperties.getMap().get(customerService.getBusinessDeptId());
        String groupName = StringUtils.isEmpty(groupId) ? "" : GroupMap.getGroupNameByGroupId(groupId);
        LocalDateTime operTime = LocalDateTime.now();
        BusinessTask businessTask = businessTaskService.createBankTaskV4(customerServiceCashierAccounting, Lists.newArrayList(), deptId, userId, operator, operTime, null, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode());
        BanksEnterprisesExtractVO vo = BanksEnterprisesExtractVO.builder()
                .groupName(groupName)
                .period(customerServiceCashierAccounting.getPeriod())
                .creditCode(Objects.isNull(customerService) ? "" : customerService.getCreditCode())
                .groupId(groupId)
                .taxNumber(Objects.isNull(customerService) ? "" : customerService.getTaxNumber())
                .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                .operator(operator)
                .bankNumber(customerServiceCashierAccounting.getBankAccountNumber())
                .deliverId(customerServiceCashierAccounting.getId())
                .businessTaskId(Objects.isNull(businessTask) ? null : businessTask.getId())
                .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .userId(userId)
                .deptId(deptId)
                .build();
        remoteThirdpartService.banksEnterprisesExtract(vo, SecurityConstants.INNER);
//        if (R.SUCCESS != banksEnterprisesExtractResp.getCode()) {
//            // 入账处理异常交付
//            accountingCashierExceptionDeal(customerServiceCashierAccounting, operTime, banksEnterprisesExtractResp);
//        } else {
//            BanksEnterprisesExtractDTO dto = banksEnterprisesExtractResp.getData();
//            if (dto.getCount() == 0) {
//                if (!dto.getStatementFileExist()) {
//                    // 入账处理异常交付
//                    accountingCashierExceptionDeal(customerServiceCashierAccounting, operTime, banksEnterprisesExtractResp);
//                } else {
//                    if (dto.getBankReceiptFileExist() == 0) {
//                        // 无流水交付
//                        accountingCashierNoBankPaymentDeal(customerServiceCashierAccounting, operTime, banksEnterprisesExtractResp);
//                    } else {
//                        // 创建待完成/待审核流水任务单
//                        createWaitCompleteBusinessTask(customerServiceCashierAccounting, operTime, banksEnterprisesExtractResp, groupName, groupId, customerService);
//                    }
//                }
//            } else {
//                createWaitInAccountBusinessTask(customerServiceCashierAccounting, operTime, banksEnterprisesExtractResp, groupName, groupId, customerService);
//            }
//        }
    }

    @Override
    public void autoRpaBankPaperUpload(CustomerServiceCashierAccounting customerServiceCashierAccounting, List<CommonFileVO> files, String operator, Long userId, Long deptId) {
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceCashierAccounting.getCustomerServiceId());
        if (!Objects.isNull(customerService)) {
            String groupId = Objects.isNull(customerService.getBusinessDeptId()) ? "" : businessGroupProperties.getMap().get(customerService.getBusinessDeptId());
            String groupName = StringUtils.isEmpty(groupId) ? "" : GroupMap.getGroupNameByGroupId(groupId);
            Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(Collections.singletonList(customerService.getId()), TagBusinessType.CUSTOMER_SERVICE);
            String platType = customerServiceTagMap.getOrDefault(customerService.getId(), Lists.newArrayList()).stream().map(TagDTO::getId).collect(Collectors.toList())
                    .contains(specialTagProperties.getYq()) ? "亿企赢" : "易捷账";
            LocalDateTime operTime = LocalDateTime.now();
            CommonFileVO bankReceiptFile = files.stream().filter(f -> Objects.equals(f.getDeliverFileType(), MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode())).findFirst().orElse(null);
            CommonFileVO statementFile = files.stream().filter(f -> Objects.equals(f.getDeliverFileType(), MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode())).findFirst().orElse(null);
            BusinessTask businessTask = businessTaskService.createBankTaskV4(customerServiceCashierAccounting, Lists.newArrayList(), deptId, userId, operator, operTime, null, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode());
            remoteThirdpartService.bankReceiptPaperFileUpload(BankReceiptPaperFileUploadVO.builder()
                    .groupName(groupName)
                    .period(customerServiceCashierAccounting.getPeriod())
                    .creditCode(customerService.getCreditCode())
                    .groupId(groupId)
                    .taxNumber(customerService.getTaxNumber())
                    .statementFileLink(Objects.isNull(statementFile) ? "" : fileService.getFullFileUrlValidTime(statementFile.getFileUrl(), 60 * 60 * 48L))
                    .platType(platType)
                    .bankReceiptFileLink(Objects.isNull(bankReceiptFile) ? "" : fileService.getFullFileUrlValidTime(bankReceiptFile.getFileUrl(), 60 * 60 * 48L))
                    .customerName(customerService.getCustomerName())
                    .operator(operator)
                    .bankNumber(customerServiceCashierAccounting.getBankAccountNumber())
                    .deliverId(customerServiceCashierAccounting.getId())
                    .businessTaskId(Objects.isNull(businessTask) ? null : businessTask.getId())
                    .userId(userId)
                    .deptId(deptId)
                    .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                    .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                    .build(), SecurityConstants.INNER);
        }
    }

    public Boolean dealBanksEnterprisesExtractResp(CommonNoticeVO commonNoticeVO) {
        BanksEnterprisesExtractDTO dto = BanksEnterprisesExtractDTO.builder().uuid(commonNoticeVO.getUuid()).count(commonNoticeVO.getCount()).bankReceiptFileExist(commonNoticeVO.getBankReceiptFileExist()).statementFileExist(commonNoticeVO.getStatementFileExist()).build();
        R<BanksEnterprisesExtractDTO> banksEnterprisesExtractResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (R.SUCCESS != banksEnterprisesExtractResp.getCode()) {
            if (banksEnterprisesExtractResp.getMsg().contains("银行卡未授权") || banksEnterprisesExtractResp.getMsg().contains("没有银行对账单文件")) {
                // 入账处理异常交付
                accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, banksEnterprisesExtractResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                // 任务单异常处理
                if (!Objects.isNull(businessTaskId)) {
                    businessTaskService.exceptionDealByExtract(businessTaskId, operTime, banksEnterprisesExtractResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                }
            } else {
                // 入账处理异常交付
                CustomerServiceCashierAccounting entity = accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, banksEnterprisesExtractResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                if (checkOverTimeClose(entity)) {
                    accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                    }
                } else {
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.exceptionWaitDeal(businessTaskId, operTime, banksEnterprisesExtractResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    }
                }
            }
            return false;
        } else {
            // 交付单不处理
            accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
            // 任务单待入账处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.waitInAccountDeal(businessTaskId, operTime, banksEnterprisesExtractResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            }
            return true;
        }
    }

    private Boolean checkOverTimeClose(CustomerServiceCashierAccounting entity) {
        return !Objects.isNull(entity)
                && !entity.getIsDel()
                && entity.getPeriod() <= 202412
                && entity.getCreateTime().toLocalDate().isAfter(DEFAULT_LOCAL_DATE)
                && (entity.getServiceType() == 1 || entity.getPeriodCreateDate().isBefore(DEFAULT_PERIOD_CREATE_DATE))
                && !Objects.equals(specialDeptIdProperties.getYt(), entity.getPeriodBusinessDeptId())
                && !Objects.equals(specialDeptIdProperties.getFd(), entity.getPeriodBusinessDeptId())
                ;
    }

    private Long accountingCashierNoDealById(Long customerServiceCashierAccountingId, String uuid) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
            return null;
        } else {
            openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
            return customerServiceCashierAccountingId;
        }
    }

    private CustomerServiceCashierAccounting accountingCashierNoDealByIdReturnEntity(Long customerServiceCashierAccountingId, String uuid) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
            return null;
        } else {
            openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
            return customerServiceCashierAccounting;
        }
    }

    @Override
    public void createWaitInAccountBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R resp, String groupName, String groupId, CCustomerService customerService) {
        BusinessTask businessTask = businessTaskService.createBankTaskV3(customerServiceCashierAccounting, Lists.newArrayList(), null, 1L, "系统", operTime, null, BusinessTaskStatus.WAIT_IN_ACCOUNT.getCode(), resp);
        // 创建定时任务
        CheckFilesVO checkFileVO = CheckFilesVO.builder()
                .groupName(groupName)
                .period(customerServiceCashierAccounting.getPeriod())
                .creditCode(Objects.isNull(customerService) ? "" : customerService.getCreditCode())
                .groupId(groupId)
                .taxNumber(Objects.isNull(customerService) ? "" : customerService.getTaxNumber())
                .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                .operator("系统")
                .checkType(1)
                .build();
        openApiCheckFileRecordService.createCheckFileTask(customerServiceCashierAccounting, businessTask, checkFileVO, 60 * 60 * 2L);
        // 凭证生成调用
        remoteThirdpartService.generateVoucher(GenerateVoucherVO.builder()
                        .groupName(groupName)
                        .period(customerServiceCashierAccounting.getPeriod())
                        .creditCode(Objects.isNull(customerService) ? "" : customerService.getCreditCode())
                        .groupId(groupId)
                        .taxNumber(Objects.isNull(customerService) ? "" : customerService.getTaxNumber())
                        .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                        .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                        .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                        .checkType(1)
                        .operator("系统")
                .build(), SecurityConstants.INNER);
    }

    @Override
    public void createWaitCompleteBusinessTask(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R<BanksEnterprisesExtractDTO> banksEnterprisesExtractResp, String groupName, String groupId, CCustomerService customerService) {
        BusinessTask businessTask = businessTaskService.createBankTaskV3(customerServiceCashierAccounting, Lists.newArrayList(), null, 1L, "系统", operTime, BusinessTaskFinishResult.OK.getCode(), BusinessTaskStatus.NEED_FINISH.getCode(), banksEnterprisesExtractResp);
        CheckFilesVO checkFileVO = CheckFilesVO.builder()
                .groupName(groupName)
                .period(customerServiceCashierAccounting.getPeriod())
                .creditCode(Objects.isNull(customerService) ? "" : customerService.getCreditCode())
                .groupId(groupId)
                .taxNumber(Objects.isNull(customerService) ? "" : customerService.getTaxNumber())
                .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                .operator("系统")
                .checkType(1)
                .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                .build();
        boolean needSearch = checkFile(customerServiceCashierAccounting, businessTask, checkFileVO);
        if (needSearch) {
            // 创建定时任务
            // 默认两小时后查询一次
            openApiCheckFileRecordService.createCheckFileTask(customerServiceCashierAccounting, businessTask, checkFileVO, 60 * 60 * 2L);
        }
    }

    @Override
    public void checkFile(Long customerServiceCashierAccountingId, Long businessTaskId, Long searchTaskId, CheckFilesVO vo) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = Objects.isNull(customerServiceCashierAccountingId) ? null : getById(customerServiceCashierAccountingId);
        BusinessTask businessTask = Objects.isNull(businessTaskId) ? null : businessTaskService.getById(businessTaskId);
        boolean needSearch = checkFile(customerServiceCashierAccounting, businessTask, vo);
        if (!needSearch) {
            // 关闭查询任务
            openApiCheckFileRecordService.closeCheckFileTask(searchTaskId);
        }
    }

    @Override
    public boolean checkFile(CustomerServiceCashierAccounting customerServiceCashierAccounting, BusinessTask businessTask, CheckFilesVO vo) {
        LocalDateTime operTime = LocalDateTime.now();
        R<CheckFilesDTO> checkFileResp = remoteThirdpartService.checkBankReceiptFile(vo, SecurityConstants.INNER);
        if (R.SUCCESS != checkFileResp.getCode()) {
            // 交付单异常处理
            accountingCashierExceptionDealByCheckFile(customerServiceCashierAccounting, operTime, vo.getCheckType(), checkFileResp);
            // 任务异常处理
            if (!Objects.isNull(businessTask)) {
                businessTaskService.exceptionDealByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
            }
        } else {
            CheckFilesDTO dto = checkFileResp.getData();
            if (vo.getCheckType() == 1) {
                if (!dto.getStatementFileExist()) {
                    // 交付单异常处理
                    accountingCashierExceptionDealByCheckFile(customerServiceCashierAccounting, operTime, vo.getCheckType(), checkFileResp);
                    // 任务异常处理
                    if (!Objects.isNull(businessTask)) {
                        businessTaskService.exceptionDealByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                    }
                } else {
                    if (dto.getBankReceiptFileExist() == 1) {
                        // 交付单不处理
                        // 任务单无流水完成
                        if (!Objects.isNull(businessTask)) {
                            businessTaskService.noFlowByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                        }
                    } else {
                        // 交付单不处理
                        // 任务单待入账处理
                        if (!Objects.isNull(businessTask)) {
                            businessTaskService.waitInAccountDeal(businessTask.getId(), operTime, checkFileResp, "系统", 1L, null, null, vo.getCheckType());
                        }
                    }
                }
            } else {
                if (-1 == dto.getBankReceiptFileExist()) {
                    // 交付单不处理
                    // 任务单待入账处理
                    if (!Objects.isNull(businessTask)) {
                        businessTaskService.waitInAccountByCheckFile(businessTask, operTime);
                    }
                    return true;
                } else if (0 == dto.getBankReceiptFileExist()) {
                    // 交付单异常处理
                    accountingCashierExceptionDealByCheckFile(customerServiceCashierAccounting, operTime, vo.getCheckType(), checkFileResp);
                    // 任务异常处理
                    if (!Objects.isNull(businessTask)) {
                        businessTaskService.exceptionDealByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                    }
                } else {
                    if (!dto.getStatementFileExist()) {
                        // 交付单异常处理
                        accountingCashierExceptionDealByCheckFile(customerServiceCashierAccounting, operTime, vo.getCheckType(), checkFileResp);
                        // 任务异常处理
                        if (!Objects.isNull(businessTask)) {
                            businessTaskService.exceptionDealByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                        }
                    } else {
                        if (dto.getBankReceiptSuccessCount() > 0) {
                            // 交付单不处理
                            // 任务单正常完成
                            if (!Objects.isNull(businessTask)) {
                                businessTaskService.normalCompleteByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                            }
                        } else {
                            // 交付单不处理
                            // 任务单无流水完成
                            if (!Objects.isNull(businessTask)) {
                                businessTaskService.noFlowByCheckFile(businessTask, operTime, vo.getCheckType(), checkFileResp);
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public void accountingCashierExceptionDealByCheckFile(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, Integer checkType, R<CheckFilesDTO> resp) {
        String operType = "RPA异常交付";
        // 流水交付单异常交付
        //异常交付
        //
        //结果：异常
        //状态：异常
        //操作记录：
        //oper_type：入账处理异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccounting.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                .setLastOperName("系统")
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperType(operType));
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        if (isSuccess) {
            if (checkType == 1) {
                CheckFilesDTO dto = resp.getData();
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                if (!Objects.isNull(dto.getBankReceiptFileCount())) {
                    operContent.put("回单数量", dto.getBankReceiptFileCount());
                }
            } else {
                CheckFilesDTO dto = resp.getData();
                String bankReceiptFileExistContent = getBankReceiptFileExistContent(dto.getBankReceiptFileExist());
                if (!StringUtils.isEmpty(bankReceiptFileExistContent)) {
                    operContent.put("识别状态", bankReceiptFileExistContent);
                }
                operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                if (!Objects.isNull(dto.getBankReceiptFileCount())) {
                    operContent.put("回单数量", dto.getBankReceiptFileCount());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private String getBankReceiptFileExistContent(Integer bankReceiptFileExist) {
        if (null == bankReceiptFileExist) {
            return "";
        }
        // -1 识别中，0 失败，1 成功，2 部分成功
        switch (bankReceiptFileExist) {
            case -1:
                return "识别中";
            case 0:
                return "失败";
            case 1:
                return "成功";
            case 2:
                return "部分成功";
            default:
                return "";
        }
    }

    private String getBankReceiptFileCheckStatusContent(Integer bankReceiptFileCheckStatus) {
        if (null == bankReceiptFileCheckStatus) {
            return "";
        }
        // 1=待再次查询，2=正常交付（有流水），3=正常交付（无流水），4=异常待会计核实，5=异常待顾问处理
        switch (bankReceiptFileCheckStatus) {
            case 1:
                return "待再次查询";
            case 2:
                return "正常交付（有流水）";
            case 3:
                return "正常交付（无流水）";
            case 4:
                return "异常待会计核实";
            case 5:
                return "异常待顾问处理";
            default:
                return "";
        }
    }

    @Override
    public void accountingCashierExceptionDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, R<BanksEnterprisesExtractDTO> resp) {
        String operType = "RPA异常交付";
        // 流水交付单异常交付
        //异常交付
        //
        //结果：异常
        //状态：异常
        //操作记录：
        //oper_type：入账处理异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccounting.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                .setLastOperName("系统")
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperType(operType));
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
        Map<String, Object> operContent = new LinkedHashMap<>();
        operContent.put("结果", "异常");
        operContent.put("前置状态", AccountingCashierDeliverStatus.WAIT_DELIVER.getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getName());
        boolean isSuccess = R.SUCCESS == resp.getCode();
        operContent.put("查询结果", isSuccess ? "成功" : "失败");
        if (isSuccess) {
            BanksEnterprisesExtractDTO dto = resp.getData();
            operContent.put("待下载回单文件数", dto.getCount());
            operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
            operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName("系统")
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperRemark(resp.getMsg())
                            .setOperUserId(1L));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private Boolean checkCanRpaUpdateStatus(Integer deliverStatus) {
        return Objects.equals(deliverStatus, AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()) || Objects.equals(deliverStatus, AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode());
    }

    private void accountingCashierExceptionDealById(Long customerServiceCashierAccountingId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA异常交付";
        // 流水交付单异常交付
        //异常交付
        //
        //结果：异常
        //状态：异常
        //操作记录：
        //oper_type：入账处理异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
        } else {
            if (!checkCanRpaUpdateStatus(customerServiceCashierAccounting.getDeliverStatus())) {
                openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，状态为%s，不满足前置条件", customerServiceCashierAccountingId, AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName()));
                return;
            }
            if (!Objects.isNull(resp)) {
                openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
                boolean isSuccess = R.SUCCESS == resp.getCode();
                updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                        .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                        .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                        .setLastOperName(operName)
                        .setLastOperRemark(resp.getMsg())
                        .setLastOperTime(operTime)
                        .setLastOperType(operType));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
                operContent.put("后置状态", AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getName());
                operContent.put("查询结果", isSuccess ? "成功" : "失败");
                if (isSuccess) {
                    if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                        BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                        operContent.put("待下载回单文件数", dto.getCount());
                        operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                        operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                    } else if (resp.getData() instanceof GenerateVoucherDTO) {
                        GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                        if (!Objects.isNull(data.getSuccessCount())) {
                            operContent.put("生成凭证成功条数", data.getSuccessCount());
                        }
                        if (!Objects.isNull(data.getFailCount())) {
                            operContent.put("生成凭证失败条数", data.getFailCount());
                        }
                    } else if (resp.getData() instanceof CheckFilesDTO) {
                        CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                            operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                            operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                            operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                            operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                            operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                        }
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(customerServiceCashierAccounting.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark(resp.getMsg())
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            } else {
                updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                        .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                        .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                        .setLastOperName(operName)
                        .setLastOperRemark("超时关闭")
                        .setLastOperTime(operTime)
                        .setLastOperType(operType));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
                operContent.put("后置状态", AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getName());
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(customerServiceCashierAccounting.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark("超时关闭")
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
    }

    private CustomerServiceCashierAccounting accountingCashierExceptionWaitSubmitDealById(Long customerServiceCashierAccountingId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理异常交付";
        // 流水交付单异常交付
        //异常交付
        //
        //结果：异常
        //状态：异常
        //操作记录：
        //oper_type：入账处理异常交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
        } else {
            if (!checkCanRpaUpdateStatus(customerServiceCashierAccounting.getDeliverStatus())) {
                openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，状态为%s，不满足前置条件", customerServiceCashierAccountingId, AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName()));
                return customerServiceCashierAccounting;
            }
            if (!Objects.isNull(resp)) {
                openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
                boolean isSuccess = R.SUCCESS == resp.getCode();
                updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                        .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                        .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                        .setLastOperName(operName)
                        .setLastOperRemark(resp.getMsg())
                        .setLastOperTime(operTime)
                        .setLastOperType(operType));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付待定");
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
                operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
                operContent.put("查询结果", isSuccess ? "成功" : "失败");
                if (isSuccess) {
                    if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                        BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                        operContent.put("待下载回单文件数", dto.getCount());
                        operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                        operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                    } else if (resp.getData() instanceof GenerateVoucherDTO) {
                        GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                        if (!Objects.isNull(data.getSuccessCount())) {
                            operContent.put("生成凭证成功条数", data.getSuccessCount());
                        }
                        if (!Objects.isNull(data.getFailCount())) {
                            operContent.put("生成凭证失败条数", data.getFailCount());
                        }
                    } else if (resp.getData() instanceof CheckFilesDTO) {
                        CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                            operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                            operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                            operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                            operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                        }
                        if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                            operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                        }
                    }
                }
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(customerServiceCashierAccounting.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark(resp.getMsg())
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            } else {
                updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                        .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                        .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                        .setLastOperName(operName)
                        .setLastOperRemark("超时关闭")
                        .setLastOperTime(operTime)
                        .setLastOperType(operType));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA异常交付");
                Map<String, Object> operContent = new LinkedHashMap<>();
                operContent.put("结果", "异常");
                operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
                operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
                try {
                    asyncLogService.saveBusinessLog(
                            new BusinessLogDTO()
                                    .setBusinessId(customerServiceCashierAccounting.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType(operType)
                                    .setOperName(operName)
                                    .setOperContent(JSONObject.toJSONString(operContent))
                                    .setCreateTime(operTime)
                                    .setOperRemark("超时关闭")
                                    .setOperUserId(userId)
                                    .setCreateBy(Constants.RPA_CREATE_BY));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
        if (!Objects.isNull(customerServiceCashierAccounting)) {
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthService.getById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
            customerServiceCashierAccounting.setServiceType(Objects.isNull(customerServicePeriodMonth) ? 1 : customerServicePeriodMonth.getServiceType());
            customerServiceCashierAccounting.setPeriodCreateDate(Objects.isNull(customerServicePeriodMonth) ? LocalDate.now() : customerServicePeriodMonth.getCreateTime().toLocalDate());
            customerServiceCashierAccounting.setPeriodBusinessDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessDeptId());
            customerServiceCashierAccounting.setPeriodBusinessTopDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessTopDeptId());
        }
        return customerServiceCashierAccounting;
    }

    @Override
    public CustomerServiceCashierAccounting accountingCashierNoBankPaymentDeal(Long customerServiceCashierAccountingId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理交付";
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
        } else {
            if (!checkCanRpaUpdateStatus(customerServiceCashierAccounting.getDeliverStatus())) {
                openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，状态为%s，不满足前置条件", customerServiceCashierAccountingId, AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName()));
                return customerServiceCashierAccounting;
            }
            openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                    .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                    .setDeliverResult(AccountingCashierDeliverResult.NO_ACCOUNT.getCode())
                    .setLastOperName(operName)
                    .setLastOperRemark(resp.getMsg())
                    .setLastOperTime(operTime)
                    .setLastOperType(operType));
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), operName, deptId, userId, "RPA入账处理交付");
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", "无流水");
            operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
            operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                } else if (resp.getData() instanceof CheckFilesDTO) {
                    CheckFilesDTO checkFilesDTO = (CheckFilesDTO) resp.getData();
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileExist())) {
                        operContent.put("识别状态", getBankReceiptFileExistContent(checkFilesDTO.getBankReceiptFileExist()));
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCount())) {
                        operContent.put("总数", checkFilesDTO.getBankReceiptFileCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFailCount())) {
                        operContent.put("失败数", checkFilesDTO.getBankReceiptFailCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptSuccessCount())) {
                        operContent.put("成功数", checkFilesDTO.getBankReceiptSuccessCount());
                    }
                    if (!Objects.isNull(checkFilesDTO.getBankReceiptFileCheckStatus())) {
                        operContent.put("识别结果", getBankReceiptFileCheckStatusContent(checkFilesDTO.getBankReceiptFileCheckStatus()));
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operName)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        if (!Objects.isNull(customerServiceCashierAccounting)) {
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthService.getById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
            customerServiceCashierAccounting.setServiceType(Objects.isNull(customerServicePeriodMonth) ? 1 : customerServicePeriodMonth.getServiceType());
            customerServiceCashierAccounting.setPeriodCreateDate(Objects.isNull(customerServicePeriodMonth) ? LocalDate.now() : customerServicePeriodMonth.getCreateTime().toLocalDate());
            customerServiceCashierAccounting.setPeriodBusinessDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessDeptId());
            customerServiceCashierAccounting.setPeriodBusinessTopDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessTopDeptId());
        }
        return customerServiceCashierAccounting;
    }

    @Override
    @Transactional
    public void dealBanksEnterprisesExtract(CommonNoticeVO commonNoticeVO) {
        Boolean needSearch = dealBanksEnterprisesExtractResp(commonNoticeVO);
        if (needSearch) {
            // 创建定时任务
            // 默认两小时后查询一次
            CheckFilesVO checkFileVO = CheckFilesVO.builder()
                    .groupName(commonNoticeVO.getGroupName())
                    .period(commonNoticeVO.getPeriod())
                    .creditCode(commonNoticeVO.getCreditCode())
                    .groupId(commonNoticeVO.getGroupId())
                    .taxNumber(commonNoticeVO.getTaxNumber())
                    .customerName(commonNoticeVO.getCustomerName())
                    .operator(commonNoticeVO.getOperator())
                    .checkType(1)
                    .deliverId(commonNoticeVO.getDeliverId())
                    .businessTaskId(commonNoticeVO.getBusinessTaskId())
                    .userId(commonNoticeVO.getUserId())
                    .deptId(commonNoticeVO.getDeptId())
                    .bankNumber(commonNoticeVO.getBankNumber())
                    .customerServiceId(commonNoticeVO.getCustomerServiceId())
                    .customerServicePeriodMonthId(commonNoticeVO.getCustomerServicePeriodMonthId())
                    .build();
            openApiCheckFileRecordService.createCheckFileTask(commonNoticeVO.getDeliverId(), commonNoticeVO.getBusinessTaskId(), checkFileVO, 60 * 60 * 2L);
        }
    }

    @Override
    @Transactional
    public void dealFileCheck(CommonNoticeVO commonNoticeVO) {
        Boolean needSearch = dealCheckFileRespV2(commonNoticeVO);
        if (needSearch) {
            if (Objects.isNull(commonNoticeVO.getTaskId())) {
                // 创建定时任务
                // 默认两小时后查询一次
                CheckFilesVO checkFileVO = CheckFilesVO.builder()
                        .groupName(commonNoticeVO.getGroupName())
                        .period(commonNoticeVO.getPeriod())
                        .creditCode(commonNoticeVO.getCreditCode())
                        .groupId(commonNoticeVO.getGroupId())
                        .taxNumber(commonNoticeVO.getTaxNumber())
                        .customerName(commonNoticeVO.getCustomerName())
                        .operator(commonNoticeVO.getOperator())
                        .checkType(commonNoticeVO.getCheckType())
                        .deliverId(commonNoticeVO.getDeliverId())
                        .businessTaskId(commonNoticeVO.getBusinessTaskId())
                        .userId(commonNoticeVO.getUserId())
                        .deptId(commonNoticeVO.getDeptId())
                        .bankNumber(commonNoticeVO.getBankNumber())
                        .customerServiceId(commonNoticeVO.getCustomerServiceId())
                        .customerServicePeriodMonthId(commonNoticeVO.getCustomerServicePeriodMonthId())
                        .build();
                openApiCheckFileRecordService.createCheckFileTask(commonNoticeVO.getDeliverId(), commonNoticeVO.getBusinessTaskId(), checkFileVO, 60 * 60 * 2L);
            } else {
                openApiCheckFileRecordService.continueCheckFileTask(commonNoticeVO.getTaskId());
            }
        } else {
            if (!Objects.isNull(commonNoticeVO.getTaskId())) {
                openApiCheckFileRecordService.closeCheckFileTask(commonNoticeVO.getTaskId());
            }
        }
    }

    @Override
    @Transactional
    public void dealGenerateVoucher(CommonNoticeVO commonNoticeVO) {
        GenerateVoucherDTO dto = GenerateVoucherDTO.builder().uuid(commonNoticeVO.getUuid()).failCount(commonNoticeVO.getFailCount()).successCount(commonNoticeVO.getSuccessCount()).build();
        R<GenerateVoucherDTO> generateVoucherResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (R.SUCCESS != generateVoucherResp.getCode()) {
            // 入账处理异常交付
            accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            // 任务单异常处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            }
        } else {
            if (dto.getFailCount() == 0) {
                // 交付单正常交付待提交
                accountingCashierNormalWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                // 任务单正常完成处理
                if (!Objects.isNull(businessTaskId)) {
                    businessTaskService.normalCompleteDeal(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                }
            } else {
                // 交付单异常交付待提交
                CustomerServiceCashierAccounting entity = accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                if (checkOverTimeClose(entity)) {
                    accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                    }
                } else {
                    // 任务单异常待处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.exceptionWaitDeal(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public void dealGenerateVoucherV2(CommonNoticeVO commonNoticeVO) {
        GenerateVoucherDTO dto = GenerateVoucherDTO.builder().uuid(commonNoticeVO.getUuid()).failCount(commonNoticeVO.getFailCount()).successCount(commonNoticeVO.getSuccessCount()).build();
        R<GenerateVoucherDTO> generateVoucherResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (Objects.isNull(commonNoticeVO.getVoucherGenerationStatus())) {
            // 入账处理异常交付
            accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            // 任务单异常处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            }
        } else {
            CustomerServiceCashierAccounting entity;
            switch (commonNoticeVO.getVoucherGenerationStatus()) {
                case 2:
                    // 交付单正常交付待提交
                    entity = accountingCashierNormalWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    if (checkOverTimeClose(entity)) {
                        accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        }
                    } else {
                        // 任务单正常完成处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.normalCompleteDeal(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                        }
                    }
                    break;
                case 4:
                    entity = accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    if (checkOverTimeClose(entity)) {
                        accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        }
                    } else {
                        // 任务单异常待处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.exceptionWaitDeal(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                        }
                    }
                    break;
                default:
                    // 入账处理异常交付
                    accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    // 任务单异常处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.exceptionDealByExtract(businessTaskId, operTime, generateVoucherResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    }
                    break;
            }
        }
    }

    private void accountingCashierGenerateVoucher(Long customerServiceCashierAccountingId, LocalDateTime operTime, R<GenerateVoucherDTO> resp, String operator, Long userId, Long deptId, String uuid, Object o) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
        } else {
            openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", isSuccess ? "成功" : "失败");
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(deptId)
                                .setOperType("生成凭证")
                                .setOperName(operator)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private CustomerServiceCashierAccounting accountingCashierNormalWaitSubmitDealById(Long customerServiceCashierAccountingId, LocalDateTime operTime, R resp, String operName, Long userId, Long deptId, String uuid, Integer checkType) {
        String operType = "RPA入账处理交付";
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(customerServiceCashierAccountingId);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，不存在", customerServiceCashierAccountingId));
        } else {
            if (!checkCanRpaUpdateStatus(customerServiceCashierAccounting.getDeliverStatus())) {
                openApiNoticeRecordService.updateSysDealFailByUuid(uuid, String.format("交付单id：%s，状态为%s，不满足前置条件", customerServiceCashierAccountingId, AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName()));
                return customerServiceCashierAccounting;
            }
            openApiNoticeRecordService.updateSysDealSuccessByUuid(uuid);
            boolean isSuccess = R.SUCCESS == resp.getCode();
            updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccountingId)
                    .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                    .setDeliverResult(AccountingCashierDeliverResult.NORMAL.getCode())
                    .setLastOperName(operName)
                    .setLastOperRemark(resp.getMsg())
                    .setLastOperTime(operTime)
                    .setLastOperType(operType));
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), "系统", null, 1L, "RPA入账处理交付");
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("结果", "正常");
            operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
            operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
            operContent.put("查询结果", isSuccess ? "成功" : "失败");
            if (isSuccess) {
                if (resp.getData() instanceof BanksEnterprisesExtractDTO) {
                    BanksEnterprisesExtractDTO dto = (BanksEnterprisesExtractDTO) resp.getData();
                    operContent.put("待下载回单文件数", dto.getCount());
                    operContent.put("对账单", !Objects.isNull(dto.getStatementFileExist()) && dto.getStatementFileExist() ? "有" : "无");
                    operContent.put("银行回单", !Objects.isNull(dto.getBankReceiptFileExist()) && dto.getBankReceiptFileExist() == 1 ? "有" : "无");
                } else if (resp.getData() instanceof GenerateVoucherDTO) {
                    GenerateVoucherDTO data = (GenerateVoucherDTO) resp.getData();
                    if (!Objects.isNull(data.getSuccessCount())) {
                        operContent.put("生成凭证成功条数", data.getSuccessCount());
                    }
                    if (!Objects.isNull(data.getFailCount())) {
                        operContent.put("生成凭证失败条数", data.getFailCount());
                    }
                }
            }
            try {
                asyncLogService.saveBusinessLog(
                        new BusinessLogDTO()
                                .setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(deptId)
                                .setOperType(operType)
                                .setOperName(operName)
                                .setOperContent(JSONObject.toJSONString(operContent))
                                .setCreateTime(operTime)
                                .setOperRemark(resp.getMsg())
                                .setOperUserId(userId)
                                .setCreateBy(Constants.RPA_CREATE_BY));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
        if (!Objects.isNull(customerServiceCashierAccounting)) {
            CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthService.getById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
            customerServiceCashierAccounting.setServiceType(Objects.isNull(customerServicePeriodMonth) ? 1 : customerServicePeriodMonth.getServiceType());
            customerServiceCashierAccounting.setPeriodCreateDate(Objects.isNull(customerServicePeriodMonth) ? LocalDate.now() : customerServicePeriodMonth.getCreateTime().toLocalDate());
            customerServiceCashierAccounting.setPeriodBusinessDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessDeptId());
            customerServiceCashierAccounting.setPeriodBusinessTopDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessTopDeptId());
        }
        return customerServiceCashierAccounting;
    }

    @Override
    @Transactional
    public void dealBankReceiptPaperFileUpload(CommonNoticeVO commonNoticeVO) {
        BankReceiptPaperFileUploadDTO dto = BankReceiptPaperFileUploadDTO.builder().uuid(commonNoticeVO.getUuid()).build();
        R<BankReceiptPaperFileUploadDTO> bankReceiptPaperFileUploadResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (R.SUCCESS != bankReceiptPaperFileUploadResp.getCode()) {
            // 入账处理异常交付
            accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, bankReceiptPaperFileUploadResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            // 任务单异常处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, bankReceiptPaperFileUploadResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            }
        } else {
            // 交付单不处理
            accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
            // 任务单待入账处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.waitInAccountDeal(businessTaskId, operTime, bankReceiptPaperFileUploadResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
            }
            // 创建定时任务
            // 默认两小时后查询一次
            CheckFilesVO checkFileVO = CheckFilesVO.builder()
                    .groupName(commonNoticeVO.getGroupName())
                    .period(commonNoticeVO.getPeriod())
                    .creditCode(commonNoticeVO.getCreditCode())
                    .groupId(commonNoticeVO.getGroupId())
                    .taxNumber(commonNoticeVO.getTaxNumber())
                    .customerName(commonNoticeVO.getCustomerName())
                    .operator(commonNoticeVO.getOperator())
                    .checkType(2)
                    .deliverId(commonNoticeVO.getDeliverId())
                    .businessTaskId(commonNoticeVO.getBusinessTaskId())
                    .userId(commonNoticeVO.getUserId())
                    .deptId(commonNoticeVO.getDeptId())
                    .bankNumber(commonNoticeVO.getBankNumber())
                    .customerServiceId(commonNoticeVO.getCustomerServiceId())
                    .customerServicePeriodMonthId(commonNoticeVO.getCustomerServicePeriodMonthId())
                    .rpaTaskId(commonNoticeVO.getRpaTaskId())
                    .build();
            openApiCheckFileRecordService.createCheckFileTask(commonNoticeVO.getDeliverId(), commonNoticeVO.getBusinessTaskId(), checkFileVO, 60 * 60 * 2L);
        }
    }

    @Override
    @Transactional
    public void updateWaitSubmitDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, CommonNoticeVO commonNoticeVO, AccountingCashierDeliverResult deliverResult) {
        //case1：异常交付
        //
        //结果：异常
        //状态：交付待提交
        //操作记录：
        //oper_type：入账处理异常
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        //case2：交付
        //
        //结果：正常
        //状态：交付待提交
        //操作记录：
        //oper_type：交付
        //oper_content：结果、前置状态、后置状态、参数写入（如果有）
        String operType = Objects.equals(deliverResult, AccountingCashierDeliverResult.EXCEPTION) ? "RPA入账异常待核" : "RPA交付完成待核";
        LocalDateTime operTime = LocalDateTime.now();
        Long userId = null;
        if (!StringUtils.isEmpty(commonNoticeVO.getOperator())) {
            SysUser user = remoteUserService.getUserByNickName(commonNoticeVO.getOperator(), commonNoticeVO.getDeptId(), SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(user)) {
                userId = user.getUserId();
            }
        }
        updateById(new CustomerServiceCashierAccounting().setId(customerServiceCashierAccounting.getId())
                .setDeliverStatus(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())
                .setDeliverResult(deliverResult.getCode())
                .setLastOperType(operType)
                .setLastOperTime(operTime)
                .setLastOperName(commonNoticeVO.getOperator())
                .setLastOperRemark(""));
        customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()));
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), commonNoticeVO.getOperator(), null, userId, operType);
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("结果", deliverResult.getName());
        operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_SUBMIT.getName());
        if (!Objects.isNull(commonNoticeVO.getIncomeResult())) {
            operContent.put("进项导入结果", commonNoticeVO.getIncomeResult() ? "成功" : "失败");
            if (!commonNoticeVO.getIncomeResult()) {
                operContent.put("进项失败原因", commonNoticeVO.getIncomeMessage());
            }
        }
        if (!Objects.isNull(commonNoticeVO.getOutputResult())) {
            operContent.put("销项导入结果", commonNoticeVO.getOutputResult() ? "成功" : "失败");
            if (!commonNoticeVO.getOutputResult()) {
                operContent.put("销项失败原因", commonNoticeVO.getOutputMessage());
            }
        }
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName(commonNoticeVO.getOperator())
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setCreateTime(operTime)
                            .setOperUserId(userId)
                            .setCreateBy(Constants.RPA_CREATE_BY));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public void updateByInvoiceUpdate(CustomerServiceCashierAccounting customerServiceCashierAccounting, String operator, AccountingCashierDeliverResult deliverResult, AccountingCashierDeliverStatus deliverStatus, String sourceName, Long deptId) {
        String operType = "交付";
        LocalDateTime operTime = LocalDateTime.now();
        Long userId = null;
        if (!StringUtils.isEmpty(operator)) {
            SysUser user = remoteUserService.getUserByNickName(operator, deptId, SecurityConstants.INNER).getDataThrowException();
            if (!Objects.isNull(user)) {
                userId = user.getUserId();
            }
        }
        CustomerServiceCashierAccounting update = new CustomerServiceCashierAccounting().setId(customerServiceCashierAccounting.getId())
                .setDeliverStatus(deliverStatus.getCode())
                .setDeliverResult(deliverResult.getCode())
                .setLastOperType(operType)
                .setLastOperTime(operTime)
                .setLastOperName(operator)
                .setLastOperRemark("RPA无进销项自动入账");
        updateById(update);
        customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(Objects.equals(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode(), deliverStatus.getCode()) ? AccountingCashierDeliverStatus.WAIT_DELIVER.getCode() : deliverStatus.getCode()));
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), operator, null, userId, operType);
        Map<String, String> operContent = new LinkedHashMap<>();
        operContent.put("结果", deliverResult.getName());
        operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.getByCode(update.getDeliverStatus()).getName());
        try {
            asyncLogService.saveBusinessLog(
                    new BusinessLogDTO()
                            .setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(null)
                            .setOperType(operType)
                            .setOperName(operator)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperRemark("RPA无进销项自动入账")
                            .setCreateTime(operTime)
                            .setOperUserId(userId)
                            .setCreateBy(sourceName));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    @Override
    @Async
    public void bankReceiptPaperUpload(List<AccountingCashierCreateVO> createVOList) {
        createVOList.forEach(vo -> bankReceiptPaperUpload(vo.getCustomerServiceId(), vo.getCustomerServicePeriodMonthId(), vo.getPeriod(), vo.getBankAccountNumber(), vo.getId(), vo.getMaterialFiles(), vo.getDeptId(), vo.getUserId(), vo.getOperName(), vo.getBusinessTaskId()));
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> batchUpdateBankAccountNumber(BatchUpdateBankAccountNumberVO vo, Long deptId) {
        Long userId = SecurityUtils.getUserId();
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(deptId, userId);
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "编辑银行账号";
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要编辑的数据");
        }
        TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
        List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .in(CustomerServiceCashierAccounting::getId, vo.getIds()));
        result.setTotal(totalList);
        if (ObjectUtils.isEmpty(totalList)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
        List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<Long, String> errorMagMap = new HashMap<>();
        Map<Long, List<CustomerServiceBankAccount>> bankMap = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .in(CustomerServiceBankAccount::getCustomerServiceId, totalList.stream().map(CustomerServiceCashierAccounting::getCustomerServiceId).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(CustomerServiceBankAccount::getCustomerServiceId));
        Map<Long, String> bankNameMap = new HashMap<>();
        for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
            if (!Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                failList.add(customerServiceCashierAccounting);
                failIds.add(customerServiceCashierAccounting.getId());
                errorMagMap.put(customerServiceCashierAccounting.getId(), "非银行流水交付单");
            } else {
                List<CustomerServiceBankAccount> bankList = bankMap.get(customerServiceCashierAccounting.getCustomerServiceId());
                if (ObjectUtils.isEmpty(bankList)) {
                    failList.add(customerServiceCashierAccounting);
                    failIds.add(customerServiceCashierAccounting.getId());
                    errorMagMap.put(customerServiceCashierAccounting.getId(), "非当前客户下的银行账号");
                } else {
                    CustomerServiceBankAccount bankAccount = bankList.stream().filter(row -> Objects.equals(row.getBankAccountNumber(), vo.getBankAccountNumber())).findFirst().orElse(null);
                    if (Objects.isNull(bankAccount)) {
                        failList.add(customerServiceCashierAccounting);
                        failIds.add(customerServiceCashierAccounting.getId());
                        errorMagMap.put(customerServiceCashierAccounting.getId(), "非当前客户下的银行账号");
                    } else {
                        if ((!Objects.isNull(bankAccount.getAccountOpenDate()) && customerServiceCashierAccounting.getPeriod() < Integer.parseInt(bankAccount.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))
                                || (!Objects.isNull(bankAccount.getAccountCloseDate()) && customerServiceCashierAccounting.getPeriod() > Integer.parseInt(bankAccount.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))))) {
                            failList.add(customerServiceCashierAccounting);
                            failIds.add(customerServiceCashierAccounting.getId());
                            errorMagMap.put(customerServiceCashierAccounting.getId(), "当前账期不满足开户销户时间范围");
                        } else {
                            successList.add(customerServiceCashierAccounting);
                            bankNameMap.put(customerServiceCashierAccounting.getId(), bankAccount.getBankName());
                        }
                    }
                }
            }
        }
        result.setSuccess(successList);
        result.setFail(failList);
        if (!ObjectUtils.isEmpty(failIds)) {
            String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            buildErrorDataList(failIds, batchNo, errorMagMap, AccountingCashierType.FLOW.getCode());
            result.setErrorDataBatchNo(batchNo);
        }
        if (!ObjectUtils.isEmpty(successList)) {
            successList.forEach(customerServiceCashierAccounting -> {
                String bankName = bankNameMap.get(customerServiceCashierAccounting.getId());
                update(new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getId, customerServiceCashierAccounting.getId())
                        .set(CustomerServiceCashierAccounting::getBankAccountNumber, vo.getBankAccountNumber())
                        .set(CustomerServiceCashierAccounting::getBankName, bankName)
                        .set(CustomerServiceCashierAccounting::getTitle, getAccountingCashierTitle(AccountingCashierType.FLOW.getCode(), customerServiceCashierAccounting.getPeriod(), bankName, vo.getBankAccountNumber()))
                        .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                        .set(CustomerServiceCashierAccounting::getLastOperRemark, "")
                        .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                        .set(CustomerServiceCashierAccounting::getLastOperName, operateUserInfoDTO.getOperName())
                );
//                businessTaskService.update(new LambdaUpdateWrapper<BusinessTask>()
//                        .set(BusinessTask::getBankAccountNumber, vo.getBankAccountNumber())
//                        .set(BusinessTask::getBankName, bankName)
//                        .set(BusinessTask::getTitle, String.format("流水任务单【%s-%s-%s】", bankName, vo.getBankAccountNumber(), customerServiceCashierAccounting.getPeriod()))
//                        .set(BusinessTask::getLastOperateType, BusinessTaskOperateType.MODIFY_BANK_ACCOUNT_NUMBER.getCode())
//                        .set(BusinessTask::getLastOperateUserId, operateUserInfoDTO.getUserId())
//                        .set(BusinessTask::getLastOperateTime, operTime)
//                        .set(BusinessTask::getLastOperateUserName, operateUserInfoDTO.getOperName())
//                        .eq(BusinessTask::getCustomerServiceId, customerServiceCashierAccounting.getCustomerServiceId())
//                        .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber()));
                Map<String, String> operContent = Maps.newLinkedHashMap();
                operContent.put("原账号", customerServiceCashierAccounting.getBankAccountNumber());
                operContent.put("现账号", vo.getBankAccountNumber());
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(operateUserInfoDTO.getDeptId())
                            .setOperType(operType)
                            .setOperName(operateUserInfoDTO.getOperName())
                            .setCreateTime(operTime)
                            .setOperUserId(operateUserInfoDTO.getUserId())
                            .setOperContent(JSONObject.toJSONString(operContent)));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            });
            List<Long> customerServiceIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServiceId).distinct().collect(Collectors.toList());
            customerServiceIds.forEach(customerServiceId -> {
                customerServiceService.updatePeriodBankPaymentByCustomerServiceId(customerServiceId, null, null, operateUserInfoDTO.getUserId(), operateUserInfoDTO.getDeptId(), operateUserInfoDTO.getOperName(), 1);
            });
        }
        return result;
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> managerDelete(AccountingCashierOperateVO vo) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "管理删除";
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要删除的数据");
        }
        TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
        List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getBankAccountNumber));
        result.setTotal(totalList);
        if (ObjectUtils.isEmpty(totalList)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
        List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<Long, String> errorMagMap = new HashMap<>();
//        Boolean isBank = Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode());
        for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
//            if (AccountingCashierDeliverStatus.canDeleteStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
//                if (isBank && businessTaskService.count(new LambdaQueryWrapper<BusinessTask>()
//                        .eq(BusinessTask::getBizId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
//                        .eq(BusinessTask::getBankAccountNumber, customerServiceCashierAccounting.getBankAccountNumber())
//                        .eq(BusinessTask::getIsDel, false)) > 0) {
//                    failList.add(customerServiceCashierAccounting);
//                    failIds.add(customerServiceCashierAccounting.getId());
//                    errorMagMap.put(customerServiceCashierAccounting.getId(), "存在任务，不允许删除");
//                } else {
//                    successList.add(customerServiceCashierAccounting);
//                }
//            } else {
//                failList.add(customerServiceCashierAccounting);
//                failIds.add(customerServiceCashierAccounting.getId());
//                errorMagMap.put(customerServiceCashierAccounting.getId(), "交付单状态不符");
//            }
            successList.add(customerServiceCashierAccounting);
        }
        result.setSuccess(successList);
        result.setFail(failList);
        if (!ObjectUtils.isEmpty(failIds)) {
            String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
            result.setErrorDataBatchNo(batchNo);
        }
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                    .setId(row.getId())
                    .setIsDel(true)
                    .setLastOperType(operType)
                    .setLastOperRemark("")
                    .setLastOperTime(operTime)
                    .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
            List<Long> customerServicePeriodMonthIds = successList.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
            if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                        updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单删除"));
            } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                customerServicePeriodMonthIds.forEach(customerServicePeriodMonthId ->
                        updateBankPaymentResultSettleAccountStatus(customerServicePeriodMonthId, operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单删除"));
                customerServicePeriodMonthService.updateBatchById(customerServicePeriodMonthIds.stream().map(customerServicePeriodMonthId -> new CustomerServicePeriodMonth().setId(customerServicePeriodMonthId).setInAccountStatus(AccountingCashierDeliverStatus.WAIT_CREATE.getCode()))
                        .collect(Collectors.toList()));
            }
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setCreateTime(operTime)
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    customerServiceService.updateLastInAccountIdByDelete(row.getCustomerServiceId(), row.getId(), row.getPeriod().toString());
                }
            });
        }
        return result;
    }

    @Override
    public Map<String, List<CustomerServiceCashierAccountingFile>> selectBatchByPeriodIdAndBankAccountNumber(List<PeriodBankAccountNumberVO> voList) {
        if (ObjectUtils.isEmpty(voList)) {
            return Collections.emptyMap();
        }
        return baseMapper.selectBatchByPeriodIdAndBankAccountNumber(voList).stream().collect(Collectors.groupingBy(row -> row.getCustomerServicePeriodMonthId() + "_" + row.getBankAccountNumber()));
    }

    @Override
    @Transactional
    public TCommonOperateDTO<CustomerServiceCashierAccounting> modifyDdl(CustomerServiceCashierAccountingModifyDdlVO vo, Long deptId) {
        OperateUserInfoDTO operateUserInfoDTO = customerServiceDocHandoverService.getOperateUserInfo(vo.getDeptId(), vo.getUserId());
        vo.setOperName(operateUserInfoDTO.getOperName());
        LocalDateTime operTime = LocalDateTime.now();
        String operType = "修改DDL";
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要修改的数据");
        }
        TCommonOperateDTO<CustomerServiceCashierAccounting> result = new TCommonOperateDTO<>();
        List<CustomerServiceCashierAccounting> totalList = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .in(CustomerServiceCashierAccounting::getId, vo.getIds())
                .select(CustomerServiceCashierAccounting::getId, CustomerServiceCashierAccounting::getDeliverStatus, CustomerServiceCashierAccounting::getHasChanged, CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, CustomerServiceCashierAccounting::getBankAccountNumber));
        result.setTotal(totalList);
        if (ObjectUtils.isEmpty(totalList)) {
            result.setSuccess(Collections.emptyList());
            result.setFail(Collections.emptyList());
            return result;
        }
        List<CustomerServiceCashierAccounting> successList = Lists.newArrayList();
        List<CustomerServiceCashierAccounting> failList = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<Long, String> errorMagMap = new HashMap<>();
        for (CustomerServiceCashierAccounting customerServiceCashierAccounting : totalList) {
            successList.add(customerServiceCashierAccounting);
        }
        result.setSuccess(successList);
        result.setFail(failList);
        if (!ObjectUtils.isEmpty(failIds)) {
            String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
            buildErrorDataList(failIds, batchNo, errorMagMap, vo.getType());
            result.setErrorDataBatchNo(batchNo);
        }
        if (!ObjectUtils.isEmpty(successList)) {
            updateBatchById(successList.stream().map(row -> new CustomerServiceCashierAccounting()
                    .setId(row.getId())
                    .setDdl(DateUtils.strToLocalDate(vo.getDdl(), DateUtils.YYYY_MM_DD))
                    .setLastOperType(operType)
                    .setLastOperRemark("")
                    .setLastOperTime(operTime)
                    .setLastOperName(vo.getOperName())).collect(Collectors.toList()));
            Map<String, Object> operContent = new LinkedHashMap<>();
            operContent.put("DDL", vo.getDdl());
            successList.forEach(row -> {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setCreateTime(operTime)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                if (!StringUtils.isEmpty(vo.getDdl())) {
                    businessDdlRecordService.saveDdlRecord(row.getId(), BusinessLogBusinessType.ACCOUNTING_CASHIER, vo.getUserId(), vo.getDdl());
                }
            });
        }
        return result;
    }

    private Boolean dealCheckFileResp(CommonNoticeVO commonNoticeVO) {
        CheckFilesDTO dto = CheckFilesDTO.builder().uuid(commonNoticeVO.getUuid()).bankReceiptFileExist(commonNoticeVO.getBankReceiptFileExist()).statementFileExist(commonNoticeVO.getStatementFileExist())
                .bankReceiptFailCount(commonNoticeVO.getBankReceiptFailCount()).bankReceiptFileCount(commonNoticeVO.getBankReceiptFileCount()).bankReceiptSuccessCount(commonNoticeVO.getBankReceiptSuccessCount()).build();
        R<CheckFilesDTO> checkFileResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (R.SUCCESS != checkFileResp.getCode()) {
            // 入账处理异常交付
            accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
            // 任务单异常处理
            if (!Objects.isNull(businessTaskId)) {
                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
            }
        } else {
            if (commonNoticeVO.getCheckType() == 1) {
                if (dto.getBankReceiptFileCount() == 0) {
                    // 交付单无流水交付
                    accountingCashierNoBankPaymentDeal(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    // 任务单无流水完成处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.noFlowDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    }
                } else {
                    if (null != dto.getStatementFileExist() && dto.getStatementFileExist() && dto.getBankReceiptFileExist() == 1) {
                        // 交付单不处理
                        CustomerServiceCashierAccounting customerServiceCashierAccounting = accountingCashierNoDealByIdReturnEntity(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                        // 任务单待入账处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskId = businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                        }
                        // 调用凭证生成
                        if (!Objects.isNull(customerServiceCashierAccounting)) {
                            remoteThirdpartService.generateVoucher(GenerateVoucherVO.builder()
                                    .groupName(commonNoticeVO.getGroupName())
                                    .period(commonNoticeVO.getPeriod())
                                    .creditCode(commonNoticeVO.getCreditCode())
                                    .groupId(commonNoticeVO.getGroupId())
                                    .taxNumber(commonNoticeVO.getTaxNumber())
                                    .customerName(commonNoticeVO.getCustomerName())
                                    .businessTaskId(businessTaskId)
                                    .deliverId(customerServiceCashierAccounting.getId())
                                    .deptId(commonNoticeVO.getDeptId())
                                    .userId(commonNoticeVO.getUserId())
                                    .operator(commonNoticeVO.getOperator())
                                    .bankNumber(commonNoticeVO.getBankNumber())
                                    .checkType(commonNoticeVO.getCheckType())
                                    .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                                    .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                                    .build(), SecurityConstants.INNER);
                        }
                    } else {
                        // 交付单不处理
                        accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                        // 任务单待入账处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                        }
                        return true;
                    }
                }
            } else {
                if (-1 == dto.getBankReceiptFileExist()) {
                    // 交付单不处理
                    accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                    // 任务单待入账处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    }
                    return true;
                } else if (0 == dto.getBankReceiptFileExist()) {
                    // 入账处理异常交付
                    accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    // 任务单异常处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.exceptionDealByExtract(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    }
                } else {
                    if (dto.getBankReceiptFileCount() == 0) {
                        if (dto.getBankReceiptFileExist() == 1) {
                            // 交付单无流水处理
                            accountingCashierNoBankPaymentDeal(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            // 任务单无流水完成处理
                            if (!Objects.isNull(businessTaskId)) {
                                businessTaskService.noFlowDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                        } else {
                            // 入账处理异常交付
                            accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            // 任务单异常处理
                            if (!Objects.isNull(businessTaskId)) {
                                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                        }
                    } else {
                        if (null != dto.getStatementFileExist() && dto.getStatementFileExist()) {
                            // 交付单不处理
                            CustomerServiceCashierAccounting customerServiceCashierAccounting = accountingCashierNoDealByIdReturnEntity(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                            // 任务单待入账处理
                            if (!Objects.isNull(businessTaskId)) {
                                businessTaskId = businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                            // 调用凭证生成
                            if (!Objects.isNull(customerServiceCashierAccounting)) {
                                remoteThirdpartService.generateVoucher(GenerateVoucherVO.builder()
                                        .groupName(commonNoticeVO.getGroupName())
                                        .period(commonNoticeVO.getPeriod())
                                        .creditCode(commonNoticeVO.getCreditCode())
                                        .groupId(commonNoticeVO.getGroupId())
                                        .taxNumber(commonNoticeVO.getTaxNumber())
                                        .customerName(commonNoticeVO.getCustomerName())
                                        .businessTaskId(businessTaskId)
                                        .deliverId(customerServiceCashierAccounting.getId())
                                        .deptId(commonNoticeVO.getDeptId())
                                        .userId(commonNoticeVO.getUserId())
                                        .operator(commonNoticeVO.getOperator())
                                        .bankNumber(commonNoticeVO.getBankNumber())
                                        .checkType(commonNoticeVO.getCheckType())
                                        .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                                        .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                                        .build(), SecurityConstants.INNER);
                            }
                        } else {
                            // 入账处理异常交付
                            accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            // 任务单异常处理
                            if (!Objects.isNull(businessTaskId)) {
                                businessTaskService.exceptionDealByExtract(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private Boolean dealCheckFileRespV2(CommonNoticeVO commonNoticeVO) {
        CheckFilesDTO dto = CheckFilesDTO.builder().uuid(commonNoticeVO.getUuid()).bankReceiptFileExist(commonNoticeVO.getBankReceiptFileExist()).statementFileExist(commonNoticeVO.getStatementFileExist())
                .bankReceiptFailCount(commonNoticeVO.getBankReceiptFailCount()).bankReceiptFileCount(commonNoticeVO.getBankReceiptFileCount()).bankReceiptSuccessCount(commonNoticeVO.getBankReceiptSuccessCount())
                .bankReceiptFileCheckStatus(commonNoticeVO.getBankReceiptFileCheckStatus()).build();
        R<CheckFilesDTO> checkFileResp = commonNoticeVO.getSuccess() ? R.ok(dto, commonNoticeVO.getMessage()) : R.fail(dto, commonNoticeVO.getMessage());
        Long customerServiceCashierAccountingId = commonNoticeVO.getDeliverId();
        Long businessTaskId = commonNoticeVO.getBusinessTaskId();
        LocalDateTime operTime = LocalDateTime.now();
        if (Objects.isNull(commonNoticeVO.getBankReceiptFileCheckStatus())) {
            // 交付单不处理
            accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
            if (!Objects.isNull(businessTaskId)) {
                // 任务单待入账处理
                businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
            }
            return true;
        } else {
            CustomerServiceCashierAccounting customerServiceCashierAccounting;
            switch (commonNoticeVO.getBankReceiptFileCheckStatus()) {
                case 1:
                    // 交付单不处理
                    accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                    if (!Objects.isNull(businessTaskId)) {
                        // 任务单待入账处理
                        businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    }
                    return true;
                case 2:
                    // 交付单不处理
                    customerServiceCashierAccounting = accountingCashierNoDealByIdReturnEntity(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                    // 生成凭证
                    // 调用凭证生成
                    if (!Objects.isNull(customerServiceCashierAccounting)) {
                        if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()) || Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode())) {
                            remoteThirdpartService.generateVoucher(GenerateVoucherVO.builder()
                                    .groupName(commonNoticeVO.getGroupName())
                                    .period(commonNoticeVO.getPeriod())
                                    .creditCode(commonNoticeVO.getCreditCode())
                                    .groupId(commonNoticeVO.getGroupId())
                                    .taxNumber(commonNoticeVO.getTaxNumber())
                                    .customerName(commonNoticeVO.getCustomerName())
                                    .businessTaskId(businessTaskId)
                                    .deliverId(customerServiceCashierAccounting.getId())
                                    .deptId(commonNoticeVO.getDeptId())
                                    .userId(commonNoticeVO.getUserId())
                                    .operator(commonNoticeVO.getOperator())
                                    .bankNumber(commonNoticeVO.getBankNumber())
                                    .checkType(commonNoticeVO.getCheckType())
                                    .customerServiceId(customerServiceCashierAccounting.getCustomerServiceId())
                                    .customerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                                    .build(), SecurityConstants.INNER);
                            if (!Objects.isNull(businessTaskId)) {
                                // 任务单待入账处理
                                businessTaskService.waitInAccountDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                        } else {
                            if (!Objects.isNull(businessTaskId)) {
                                // 任务单关闭处理
                                businessTaskService.checkFileClose(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                            }
                        }
                    } else {
                        if (!Objects.isNull(businessTaskId)) {
                            // 任务单关闭处理
                            businessTaskService.checkFileClose(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                        }
                    }
                    break;
                case 3:
                    // 交付单无流水交付
                    customerServiceCashierAccounting = accountingCashierNoBankPaymentDeal(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    if (checkOverTimeClose(customerServiceCashierAccounting)) {
                        accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        }
                    } else {
                        // 任务单无流水完成处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.noFlowDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                        }
                    }
                    break;
                case 4:
                    // 交付单异常交付待提交
                    customerServiceCashierAccounting = accountingCashierExceptionWaitSubmitDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                    if (checkOverTimeClose(customerServiceCashierAccounting)) {
                        accountingCashierOverTimeClose(customerServiceCashierAccountingId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.overTimeClose(businessTaskId, operTime, commonNoticeVO.getDeptId(), commonNoticeVO.getUserId(), commonNoticeVO.getOperator());
                        }
                    } else {
                        // 任务单异常待处理
                        if (!Objects.isNull(businessTaskId)) {
                            businessTaskService.exceptionWaitDeal(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), null);
                        }
                    }
                    break;
                case 5:
                    // 入账处理异常交付
                    accountingCashierExceptionDealById(customerServiceCashierAccountingId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    // 任务单异常处理
                    if (!Objects.isNull(businessTaskId)) {
                        businessTaskService.exceptionDealByExtract(businessTaskId, operTime, checkFileResp, commonNoticeVO.getOperator(), commonNoticeVO.getUserId(), commonNoticeVO.getDeptId(), commonNoticeVO.getUuid(), commonNoticeVO.getCheckType());
                    }
                    break;
                default:
                    // 交付单不处理
                    accountingCashierNoDealById(customerServiceCashierAccountingId, commonNoticeVO.getUuid());
                    break;
            }
        }
        return false;
    }

    private CustomerServiceCashierAccounting covCustomerServiceInAccountByCustomerAndPeriod(CCustomerService customerService, CustomerServicePeriodMonth monthPeriod, Long deptId, Long userId, String operName) {
        CustomerServiceCashierAccounting newEntry = new CustomerServiceCashierAccounting();
        newEntry.setCustomerServiceId(customerService.getId());
        newEntry.setCustomerServicePeriodMonthId(monthPeriod.getId());
        newEntry.setPeriod(monthPeriod.getPeriod());
        newEntry.setTitle(getAccountingCashierTitle(AccountingCashierType.INCOME.getCode(), monthPeriod.getPeriod(), null, null));
        newEntry.setType(AccountingCashierType.INCOME.getCode());
        newEntry.setHasTicket(false);
        newEntry.setMaterialMedia(AccountingCashierMaterialMedia.NONE.getCode());
        newEntry.setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
        newEntry.setBankPaymentResult(monthPeriod.getBankPaymentResult());
        newEntry.setSettleAccountStatus(InAccountStatus.UN_IN.getCode());
        newEntry.setLastOperType(Objects.equals(2, monthPeriod.getAddFromType()) ? "补账分派创建" : "新客户创建");
        newEntry.setLastOperName(operName);
        newEntry.setLastOperTime(LocalDateTime.now());
        newEntry.setIsDel(false);
        newEntry.setCreateOperType(newEntry.getLastOperType());
        newEntry.setCreateBy(operName);
        return newEntry;
    }

    private List<MaterialFileSimpleErrorVO> materialFileBankError(List<MaterialFileSimpleVO> materialFiles) {
        if (ObjectUtils.isEmpty(materialFiles)) {
            return Lists.newArrayList();
        }
        return materialFiles.stream().map(row -> MaterialFileSimpleErrorVO.builder()
                .fileName(row.getFileName())
                .fileType(row.getFileType() == 1 ? "入账材料" : row.getFileType() == 2 ? "银行流水" : "改账材料")
                .fileUrl(row.getFileUrl())
                .bankInfo((StringUtils.isEmpty(row.getBankName()) ? "" : row.getBankName()) + (StringUtils.isEmpty(row.getBankAccountNumber()) ? "" : ("（" + row.getBankAccountNumber() + "）")))
                .fileNo(row.getFileNo())
                .fileRemark(row.getFileRemark())
                .build()).collect(Collectors.toList());
    }

    private void createBankTaskSingle(AccountingCashierBankCreateTaskVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            throw new ServiceException("交付单类型错误");
        }
        if (!AccountingCashierDeliverStatus.canCreateTaskStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        List<CustomerServiceCashierAccountingFile> files = customerServiceCashierAccountingFileService.selectByAccountingCashierIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode());
        businessTaskService.createBankTask(customerServiceCashierAccounting, files, vo.getDeptId(), vo.getUserId(), vo.getOperName(), operTime, vo.getDdl(), vo.getAdminUserId());
        updateById(new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setLastOperType(operType)
                .setLastOperRemark("")
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName()));
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void supplementFiles(AccountingCashierSupplementFileVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
            throw new ServiceException("交付单类型错误");
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getMaterialSupplementStatus, MaterialSupplementStatus.TO_BE_CHECKED.getCode())
                .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName());
        update(updateWrapper);
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (vo.getCoverFileType() != 3) {
                if (vo.getCoverFileType() == 2) {
                    customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                }
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                }
            }
            if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                if (vo.getReceiptCoverFileType() != 3) {
                    if (vo.getReceiptCoverFileType() == 2) {
                        customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                    }
                    if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                    }
                }
            }
        } else {
            if (vo.getCoverFileType() != 3) {
                if (vo.getCoverFileType() == 2) {
                    customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                }
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                }
            }
        }
        Map<String, Object> operContent = Maps.newLinkedHashMap();
        operContent.put("前置状态", MaterialSupplementStatus.getByCode(customerServiceCashierAccounting.getMaterialSupplementStatus()).getDesc());
        operContent.put("后置状态", MaterialSupplementStatus.TO_BE_CHECKED.getDesc());
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (!Objects.isNull(vo.getCoverFileType())) {
                operContent.put("对账单补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    operContent.put("对账单补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                }
            }
            if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                operContent.put("回单补充方式", vo.getReceiptCoverFileType() == 1 ? "追加" : vo.getReceiptCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                    operContent.put("回单补充附件", JSONArray.toJSONString(vo.getReceiptFiles()));
                }
            }
        } else {
            if (!Objects.isNull(vo.getCoverFileType())) {
                operContent.put("补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    operContent.put("补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRemark()));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void updateProfit(AccountingCashierUpdateProfitVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canUpdateProfitStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName());
        InAccountDeliverVO inAccountDeliverInfo = vo.getInAccountDeliverInfo();
        if (vo.getIsCoverFiles()) {
            updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal())
                    .set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal())
                    .set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal())
                    .set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease())
                    .set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount())
                    .set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal())
                    .set(CustomerServiceCashierAccounting::getTableStatusBalance, inAccountDeliverInfo.getTableStatusBalance());
        } else {
            if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getMajorCostTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getProfitTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal());
            }
            if (!StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getTaxReportCount())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount());
            }
            if (!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal());
            }
            if (!StringUtils.isEmpty(inAccountDeliverInfo.getTableStatusBalance())) {
                updateWrapper.set(CustomerServiceCashierAccounting::getTableStatusBalance, inAccountDeliverInfo.getTableStatusBalance());
            }
        }
        updateWrapper.set(CustomerServiceCashierAccounting::getProfitGetTime, LocalDateTime.now());
        update(updateWrapper);
        Map<String, String> operContent = Maps.newLinkedHashMap();
        if (!Objects.isNull(vo.getInAccountDeliverInfo())) {
            if (vo.getIsCoverFiles()) {
                operContent.put("本年累计主营收入", Objects.isNull(vo.getInAccountDeliverInfo().getMajorIncomeTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年累计主营成本", Objects.isNull(vo.getInAccountDeliverInfo().getMajorCostTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年累计净利润", Objects.isNull(vo.getInAccountDeliverInfo().getProfitTotal()) ? "null" : vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                operContent.put("本年费用调增", StringUtils.isEmpty(vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease()) ? "null" : vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                operContent.put("个税申报人数", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportCount()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                operContent.put("本年个税申报工资总额", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportSalaryTotal()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                operContent.put("报表平衡", StringUtils.isEmpty(vo.getInAccountDeliverInfo().getTableStatusBalance()) ? "null" : vo.getInAccountDeliverInfo().getTableStatusBalance());
            } else {
                if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal())) {
                    operContent.put("本年累计主营收入", vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getMajorCostTotal())) {
                    operContent.put("本年累计主营成本", vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getProfitTotal())) {
                    operContent.put("本年累计净利润", vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                }
                if (!StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease())) {
                    operContent.put("本年费用调增", vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getTaxReportCount())) {
                    operContent.put("个税申报人数", vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                }
                if (!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                    operContent.put("本年个税申报工资总额", vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                }
                if (!StringUtils.isEmpty(inAccountDeliverInfo.getTableStatusBalance())) {
                    operContent.put("报表平衡", vo.getInAccountDeliverInfo().getTableStatusBalance());
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServiceService.updateLastInAccountId(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
        }
    }

    private void reBackSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (customerServiceCashierAccounting.getHasChanged()) {
            throw new ServiceException("交付变更待接收");
        }
        if (!AccountingCashierDeliverStatus.canReBackStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        Integer deliverStatus = AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode();
        updateById(new CustomerServiceCashierAccounting()
                .setId(vo.getId())
                .setDeliverStatus(deliverStatus)
                .setLastOperType(operType)
                .setLastOperRemark(vo.getRemark())
                .setLastOperTime(operTime)
                .setLastOperName(vo.getOperName()));
        if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单退回");
        } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单退回");
        }
        if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                    .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(deliverStatus));
        }
        Map<String, Object> operContent = Maps.newLinkedHashMap();
        operContent.put("前置状态", AccountingCashierDeliverStatus.WAIT_DELIVER.getName());
        operContent.put("后置状态", AccountingCashierDeliverStatus.WAIT_RESUBMIT.getName());
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void changeAccountingCashier(AccountingCashierChangeDeliverVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getAccountingCashierId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canChangeDeliverStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceCashierAccounting.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户不存在");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            LocalDate now = LocalDate.now();
            LocalDate ddlDate = LocalDate.parse(vo.getDdl(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (!ddlDate.isAfter(now)) {
                exceptionService.throwConfigException(ErrorCodeEnum.DDL_ERROR);
            }
        }
        Integer thisPeriod = DateUtils.getNowPeriod();
        String lastOperType = operType;
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getAccountingCashierId())
                .set(CustomerServiceCashierAccounting::getHasTicket, Objects.isNull(vo.getHasTicket()) ? null : vo.getHasTicket() == 1)
                .set(CustomerServiceCashierAccounting::getHasBankPayment, Objects.isNull(vo.getHasBankPayment()) ? null : vo.getHasBankPayment() == 1)
                .set(CustomerServiceCashierAccounting::getMaterialMedia, vo.getMaterialMedia())
                .set(CustomerServiceCashierAccounting::getDeliverRequire, vo.getDeliverRequire())
                .set(CustomerServiceCashierAccounting::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl())
                .set(CustomerServiceCashierAccounting::getIsClose, Objects.isNull(vo.getIsClose()) ? customerServiceCashierAccounting.getIsClose() : vo.getIsClose())
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, vo.getDeliverRequire());
        boolean clearCompleteTime = false;
        if (!Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) && Objects.equals(customerServiceCashierAccounting.getCompleteTime().format(DateTimeFormatter.ofPattern("yyyyMM")), thisPeriod.toString())) {
            updateWrapper.set(CustomerServiceCashierAccounting::getCompleteTime, null)
                    .set(CustomerServiceCashierAccounting::getCompleteUserId, null)
                    .set(CustomerServiceCashierAccounting::getCompleteUserName, null)
                    .set(CustomerServiceCashierAccounting::getInTime, null)
                    .set(CustomerServiceCashierAccounting::getLastCompleteUserId, null)
                    .set(CustomerServiceCashierAccounting::getLastInTime, null);
            clearCompleteTime = true;
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
                if (!Objects.isNull(customerServiceCashierAccounting.getEndTime()) && Objects.equals(customerServiceCashierAccounting.getEndTime().format(DateTimeFormatter.ofPattern("yyyyMM")), thisPeriod.toString())) {
                    updateWrapper.set(CustomerServiceCashierAccounting::getEndTime, null)
                            .set(CustomerServiceCashierAccounting::getEndUserId, null)
                            .set(CustomerServiceCashierAccounting::getLastEndTime, null)
                            .set(CustomerServiceCashierAccounting::getLastEndUserId, null);
                }
            }
        }
        if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
            updateWrapper.set(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
            updateWrapper.set(CustomerServiceCashierAccounting::getDeliverResult, AccountingCashierDeliverResult.NO_DELIVER.getCode());
            lastOperType = "变更交付并关闭交付";
        } else {
            updateWrapper.set(CustomerServiceCashierAccounting::getHasChanged, true);
        }
        updateWrapper.set(CustomerServiceCashierAccounting::getLastOperType, lastOperType);
        update(updateWrapper);
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            // 找同账期的入账交付单，如果同账期的入账交付单的首次结账时间和操作时间是同一个月，则也清空首次结账时间和最后结账时间
            CustomerServiceCashierAccounting incomeCashier = getOne(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServiceCashierAccounting.getCustomerServicePeriodMonthId())
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                    .last("limit 1"));
            if (!Objects.isNull(incomeCashier) && !Objects.isNull(incomeCashier.getEndTime()) && Objects.equals(incomeCashier.getEndTime().format(DateTimeFormatter.ofPattern("yyyyMM")), thisPeriod.toString())) {
                update(new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                        .eq(CustomerServiceCashierAccounting::getId, incomeCashier.getId())
                        .set(CustomerServiceCashierAccounting::getEndTime, null)
                        .set(CustomerServiceCashierAccounting::getEndUserId, null)
                        .set(CustomerServiceCashierAccounting::getLastEndTime, null)
                        .set(CustomerServiceCashierAccounting::getLastEndUserId, null));
            }
        }
        List<CommonFileVO> operFiles = Lists.newArrayList();
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (vo.getIsCoverFiles()) {
                customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getAccountingCashierId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                vo.getMaterialFiles().forEach(row -> {
                    row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-对账单-" + row.getFileName());
                });
                operFiles.addAll(vo.getMaterialFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
            }
            if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                vo.getReceiptFiles().forEach(row -> {
                    row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-回单-" + row.getFileName());
                });
                operFiles.addAll(vo.getReceiptFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
            }
            businessTaskService.dealBusinessTask(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), customerServiceCashierAccounting, vo.getOperName(), vo.getUserId(), vo.getDeptId(), "交付变更关闭");
        } else {
            if (vo.getIsCoverFiles()) {
                customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getAccountingCashierId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                operFiles.addAll(vo.getMaterialFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
        }
        if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单变更交付并关闭交付");
            } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单变更交付并关闭交付");
                customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                        .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()));
            }
        }
        Map<String, String> operContent = Maps.newLinkedHashMap();
        operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode()) && !Objects.isNull(vo.getHasTicket())) {
            operContent.put("凭票入账", vo.getHasTicket() == 1 ? "是" : "否");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode()) && !Objects.isNull(vo.getHasBankPayment())) {
            operContent.put("流水情况", vo.getHasBankPayment() == 1 ? "有流水" : "无流水");
        }
        if (!Objects.isNull(vo.getMaterialMedia())) {
            operContent.put("材料介质", AccountingCashierMaterialMedia.getByCode(vo.getMaterialMedia()).getDesc());
        }
        if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
            operContent.put("关闭交付", "是");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            operContent.put("ddl", vo.getDdl());
        }
        String operRemark = null;
        if (!StringUtils.isEmpty(vo.getDeliverRequire())) {
            operRemark = vo.getDeliverRequire();
        }
        if (clearCompleteTime) {
            if (StringUtils.isEmpty(operRemark)) {
                operRemark = "变更交付清空完成时间";
            } else {
                operRemark = operRemark + "\r\n变更交付清空完成时间";
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(lastOperType)
                    .setOperName(vo.getOperName())
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(operRemark)
                    .setOperImages(ObjectUtils.isEmpty(operFiles) ? "" : JSONArray.toJSONString(operFiles)));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(customerServiceCashierAccounting.getId(), BusinessLogBusinessType.ACCOUNTING_CASHIER, vo.getUserId(), vo.getDdl());
        }
    }

    private void dealExceptionSingle(AccountingCashierDealExceptionVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canDealExceptionStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        CCustomerService customerService = customerServiceService.getById(customerServiceCashierAccounting.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户服务不存在");
        }
        Integer deliverStatus = vo.getDealResult() == 1 ? AccountingCashierDeliverStatus.WAIT_DELIVER.getCode() : AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode();
        Integer deliverResult = null;
        String lastOperType = operType;
        LocalDateTime lastOperTime = operTime;
        boolean isComplete = false;
        if (vo.getDealResult() == 1) {
            if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode()) && !Objects.isNull(customerServiceCashierAccounting.getHasBankPayment())) {
                if (!customerServiceCashierAccounting.getHasBankPayment()) {
                    deliverStatus = AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode();
                    deliverResult = AccountingCashierDeliverResult.NO_ACCOUNT.getCode();
                    lastOperType = "交付";
                    lastOperTime = operTime.plusSeconds(1L);
                    isComplete = true;
                }
            }
        } else {
            deliverResult = AccountingCashierDeliverResult.NO_DELIVER.getCode();
            isComplete = true;
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>().eq(CustomerServiceCashierAccounting::getId, vo.getId())
                .set(CustomerServiceCashierAccounting::getDeliverStatus, deliverStatus)
                .set(CustomerServiceCashierAccounting::getDeliverResult, deliverResult)
                .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, vo.getRemark())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName());
        if (!StringUtils.isEmpty(vo.getRemark())) {
            updateWrapper.set(CustomerServiceCashierAccounting::getDeliverRequire, StringUtils.isEmpty(customerServiceCashierAccounting.getDeliverRequire()) ? vo.getRemark() : (customerServiceCashierAccounting.getDeliverRequire() + "\r\n" + vo.getRemark()));
        }
        update(updateWrapper);
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (!Objects.isNull(vo.getCoverFileType())) {
                if (vo.getCoverFileType() != 3) {
                    if (vo.getCoverFileType() == 2) {
                        customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                    }
                    if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                    }
                }
            }
            if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                if (vo.getReceiptCoverFileType() != 3) {
                    if (vo.getReceiptCoverFileType() == 2) {
                        customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                    }
                    if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                    }
                }
            }
        } else {
            if (!Objects.isNull(vo.getCoverFileType())) {
                if (vo.getCoverFileType() != 3) {
                    if (vo.getCoverFileType() == 2) {
                        customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                    }
                    if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getSupplementFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                    }
                }
            }
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                    .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(deliverStatus));
        }
        Map<String, Object> operContent = Maps.newLinkedHashMap();
        operContent.put("处理结果", vo.getDealResult() == 1 ? "解除异常" : "关闭交付");
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (!Objects.isNull(vo.getCoverFileType())) {
                operContent.put("对账单补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    operContent.put("对账单补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                }
            }
            if (!Objects.isNull(vo.getReceiptCoverFileType())) {
                operContent.put("回单补充方式", vo.getReceiptCoverFileType() == 1 ? "追加" : vo.getReceiptCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                    operContent.put("回单补充附件", JSONArray.toJSONString(vo.getReceiptFiles()));
                }
            }
        } else {
            if (!Objects.isNull(vo.getCoverFileType())) {
                operContent.put("补充方式", vo.getCoverFileType() == 1 ? "追加" : vo.getCoverFileType() == 2 ? "覆盖" : "无需补充");
                if (!ObjectUtils.isEmpty(vo.getSupplementFiles())) {
                    operContent.put("补充附件", JSONArray.toJSONString(vo.getSupplementFiles()));
                }
            }
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getRemark())
                    .setOperImages(ObjectUtils.isEmpty(vo.getOperateFiles()) ? "" : JSONArray.toJSONString(vo.getOperateFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(deliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) {
            if (Objects.equals(deliverResult, AccountingCashierDeliverResult.NO_ACCOUNT.getCode())) {
                Map<String, String> content = Maps.newLinkedHashMap();
                content.put("交付内容", "无流水自动完结");
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(lastOperType)
                            .setOperName(vo.getOperName())
                            .setCreateTime(lastOperTime)
                            .setOperContent(JSONObject.toJSONString(content))
                            .setOperUserId(vo.getUserId()));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
        // 是银行流水交付单 && 有银行流水 && 是银企 && 账期>=202501 发起RPA自动化流程
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())
                && Objects.equals(deliverStatus, AccountingCashierDeliverStatus.WAIT_DELIVER.getCode())
                && !Objects.isNull(customerServiceCashierAccounting.getHasBankPayment())
                && customerServiceCashierAccounting.getHasBankPayment()) {
//            if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
//                autoFlowRpaDeal(customerServiceCashierAccounting, vo.getOperName(), customerServiceCashierAccounting.getCustomerServiceId(), vo.getUserId(), vo.getDeptId());
//            } else if (Objects.equals(customerServiceCashierAccounting.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
//                List<CommonFileVO> files = customerServiceCashierAccountingFileService.selectByAccountingCashierIdAndFileType(customerServiceCashierAccounting.getId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode())
//                        .stream().map(row -> {
//                            CommonFileVO commonFileVO = CommonFileVO.builder().fileSize(row.getFileSize()).fileUrl(row.getFileUrl()).fileName(row.getFileName()).build();
//                            if (Objects.equals(row.getSubFileType(), 1)) {
//                                commonFileVO.setDeliverFileType(MaterialDeliverFileInventoryFileType.CHECK_FILE_TYPE.getCode());
//                            } else {
//                                commonFileVO.setDeliverFileType(MaterialDeliverFileInventoryFileType.RECEIPT_FILE_TYPE.getCode());
//                            }
//                            return commonFileVO;
//                        }).collect(Collectors.toList());
//                if (!ObjectUtils.isEmpty(files)) {
//                    autoRpaBankPaperUpload(customerServiceCashierAccounting, files, vo.getOperName(), vo.getUserId(), vo.getDeptId());
//                }
//            } else {
//                accountingCashierFlowAutoDeal(customerServiceCashierAccounting, customerService, operTime, vo.getDeptId(), vo.getUserId(), vo.getOperName());
//            }
            accountingCashierFlowAutoDeal(customerServiceCashierAccounting, customerService, operTime, vo.getDeptId(), vo.getUserId(), vo.getOperName());
        }
        if (!vo.getIsInner()) {
            if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单处理异常");
            } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单处理异常");
            }
        }
    }

    private void accountingCashierFlowAutoDeal(CustomerServiceCashierAccounting customerServiceCashierAccounting, CCustomerService customerService, LocalDateTime operTime, Long deptId, Long userId, String operName) {
        // 有流水且不是银企 走自动分单或自动关闭
        BusinessTask businessTask = businessTaskService.createBankTaskV4(customerServiceCashierAccounting, Lists.newArrayList(), deptId, userId, operName, operTime, null, BusinessTaskStatus.NEED_FINISH.getCode());
        CustomerServicePeriodMonth customerServicePeriodMonth = customerServicePeriodMonthMapper.selectById(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
        customerServiceCashierAccounting.setServiceType(Objects.isNull(customerServicePeriodMonth) ? 1 : customerServicePeriodMonth.getServiceType());
        customerServiceCashierAccounting.setPeriodCreateDate(Objects.isNull(customerServicePeriodMonth) ? LocalDate.now() : customerServicePeriodMonth.getCreateTime().toLocalDate());
        customerServiceCashierAccounting.setPeriodBusinessDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessDeptId());
        customerServiceCashierAccounting.setPeriodBusinessTopDeptId(Objects.isNull(customerServicePeriodMonth) ? 0L : customerServicePeriodMonth.getBusinessTopDeptId());
        customerServiceCashierAccounting.setIsDel(false);
        if (checkOverTimeClose(customerServiceCashierAccounting)) {
            accountingCashierOverTimeClose(customerServiceCashierAccounting.getId(), operTime, deptId, userId, operName);
            businessTaskService.overTimeClose(businessTask.getId(), operTime, deptId, userId, operName);
        } else {
            if (!businessTaskService.checkBusinessTaskClose(customerService, customerServicePeriodMonth)) {
                if (!Objects.isNull(businessTask.getId())) {
                    businessTaskService.autoDispatchBusinessTask(businessTask.getId(), customerService);
                }
            } else {
                businessTaskService.noDispatchClose(businessTask.getId(), operTime, deptId, userId, operName);
            }
        }
    }

    private void createSamePeriodBankAccountingCashier(List<CustomerServiceCashierAccounting> customerServiceCashierAccountings, String operName, LocalDateTime operTime, Long deptId, Long userId) {
        if (ObjectUtils.isEmpty(customerServiceCashierAccountings)) {
            return;
        }
        Map<Long, List<CustomerServiceBankAccount>> customerBankMap = customerServiceBankAccountMapper.selectList(new LambdaQueryWrapper<CustomerServiceBankAccount>()
                        .in(CustomerServiceBankAccount::getCustomerServiceId, customerServiceCashierAccountings.stream().map(CustomerServiceCashierAccounting::getCustomerServiceId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(CustomerServiceBankAccount::getCustomerServiceId));
        Map<Long, List<CustomerServiceCashierAccounting>> existsBankAccountingCashierMap = list(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .in(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, customerServiceCashierAccountings.stream().map(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId).collect(Collectors.toList()))
                .eq(CustomerServiceCashierAccounting::getIsDel, false).eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode()))
                .stream().collect(Collectors.groupingBy(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId));
        for (CustomerServiceCashierAccounting customerServiceCashierAccounting : customerServiceCashierAccountings) {
            List<CustomerServiceBankAccount> customerServiceBanks = customerBankMap.get(customerServiceCashierAccounting.getCustomerServiceId());
            if (ObjectUtils.isEmpty(customerServiceBanks)) {
                continue;
            }
            List<CustomerServiceCashierAccounting> bankCashierList = existsBankAccountingCashierMap.get(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
            if (!ObjectUtils.isEmpty(bankCashierList)) {
                List<CustomerServiceCashierAccounting> notCompleteBankList = bankCashierList.stream().filter(row -> !Objects.equals(row.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(notCompleteBankList)) {
                    update(new LambdaUpdateWrapper<CustomerServiceCashierAccounting>().in(CustomerServiceCashierAccounting::getId, notCompleteBankList.stream().map(CustomerServiceCashierAccounting::getId).collect(Collectors.toList()))
                            .set(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())
                            .set(CustomerServiceCashierAccounting::getDeliverResult, AccountingCashierDeliverResult.NO_DELIVER.getCode())
//                            .set(CustomerServiceCashierAccounting::getCompleteTime, operTime)
//                            .set(CustomerServiceCashierAccounting::getCompleteUserId, userId)
//                            .set(CustomerServiceCashierAccounting::getCompleteUserName,  operName)
//                            .set(CustomerServiceCashierAccounting::getInTime, operTime.toLocalDate())
                            .set(CustomerServiceCashierAccounting::getLastOperName, operName)
                            .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                            .set(CustomerServiceCashierAccounting::getLastOperType, "完结")
                            .set(CustomerServiceCashierAccounting::getLastOperRemark, "凭票入账同步完成"));
                    notCompleteBankList.forEach(row -> {
                        try {
                            Map<String, String> operContent = Maps.newLinkedHashMap();
                            operContent.put("交付结果", AccountingCashierDeliverResult.NO_DELIVER.getName());
                            operContent.put("交付备注", "凭票入账同步完成");
                            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(row.getId())
                                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                    .setDeptId(deptId)
                                    .setOperType("完结")
                                    .setOperName(operName)
                                    .setCreateTime(operTime)
                                    .setOperUserId(userId)
                                    .setOperContent(JSONObject.toJSONString(operContent)));
                        } catch (Exception e) {
                            log.error("新增业务日志失败:{}", e.getMessage());
                            throw new ServiceException("操作记录写入失败，请稍后重试");
                        }
                    });
                }
            }
            List<String> existsBankAccountingCashierAccountNumbers = (ObjectUtils.isEmpty(bankCashierList) ? new ArrayList<CustomerServiceCashierAccounting>() : bankCashierList)
                    .stream().map(CustomerServiceCashierAccounting::getBankAccountNumber).collect(Collectors.toList());

            for (CustomerServiceBankAccount customerServiceBank : customerServiceBanks) {
                Integer period = customerServiceCashierAccounting.getPeriod();
                Integer bankStart = Objects.isNull(customerServiceBank.getAccountOpenDate()) ? null : Integer.parseInt(customerServiceBank.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM")));
                Integer bankEnd = Objects.isNull(customerServiceBank.getAccountCloseDate()) ? null : Integer.parseInt(customerServiceBank.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM")));
                if ((!Objects.isNull(bankStart) && period < bankStart) || (!Objects.isNull(bankEnd) && period > bankEnd)) {
                    continue;
                }
                if (existsBankAccountingCashierAccountNumbers.contains(customerServiceBank.getBankAccountNumber())) {
                    continue;
                }
                CustomerServiceCashierAccounting bankAccountingCashier = new CustomerServiceCashierAccounting();
                bankAccountingCashier.setCustomerServiceId(customerServiceCashierAccounting.getCustomerServiceId());
                bankAccountingCashier.setCustomerServicePeriodMonthId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId());
                bankAccountingCashier.setPeriod(period);
                bankAccountingCashier.setTitle(getAccountingCashierTitle(AccountingCashierType.FLOW.getCode(), period, customerServiceBank.getBankName(), customerServiceBank.getBankAccountNumber()));
                bankAccountingCashier.setBankName(customerServiceBank.getBankName());
                bankAccountingCashier.setBankAccountNumber(customerServiceBank.getBankAccountNumber());
                bankAccountingCashier.setType(AccountingCashierType.FLOW.getCode());
                bankAccountingCashier.setHasBankPayment(true);
                bankAccountingCashier.setMaterialMedia(AccountingCashierMaterialMedia.OTHER.getCode());
                bankAccountingCashier.setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                bankAccountingCashier.setDeliverResult(AccountingCashierDeliverResult.NO_DELIVER.getCode());
                bankAccountingCashier.setCompleteTime(operTime);
                bankAccountingCashier.setCompleteUserId(userId);
                bankAccountingCashier.setCompleteUserName(operName);
                bankAccountingCashier.setInTime(operTime.toLocalDate());
                bankAccountingCashier.setLastInTime(operTime.toLocalDate());
                bankAccountingCashier.setLastCompleteUserId(userId);
                bankAccountingCashier.setLastOperName(operName);
                bankAccountingCashier.setLastOperTime(operTime);
                bankAccountingCashier.setLastOperType("完结");
                bankAccountingCashier.setLastOperRemark("凭票入账同步完成");
                save(bankAccountingCashier);

                try {
                    Map<String, String> operContent = Maps.newLinkedHashMap();
                    operContent.put("交付结果", AccountingCashierDeliverResult.NO_DELIVER.getName());
                    operContent.put("交付备注", "凭票入账同步完成");
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(bankAccountingCashier.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(deptId)
                            .setOperType("完结")
                            .setOperName(operName)
                            .setCreateTime(operTime)
                            .setOperUserId(userId)
                            .setOperContent(JSONObject.toJSONString(operContent)));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            }
        }
    }

    private void deliverSingle(AccountingCashierOperateVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (customerServiceCashierAccounting.getHasChanged()) {
            throw new ServiceException("交付变更待接收");
        }
        if (!AccountingCashierDeliverStatus.canDeliverStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode()) && businessTaskService.existsNeedCheckTask(customerServiceCashierAccounting)) {
            throw new ServiceException("有任务单待审核");
        }
        Boolean isDeliverComplete = false;
//        Boolean isDeliverComplete = Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
        Integer deliverStatus = isDeliverComplete ? customerServiceCashierAccounting.getDeliverStatus() :
                (Objects.equals(vo.getDeliverResult(), AccountingCashierDeliverResult.EXCEPTION.getCode()) ? AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode() : AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
        Integer materialIntegrity = isDeliverComplete ? customerServiceCashierAccounting.getMaterialIntegrity() : vo.getMaterialIntegrity();
        LocalDateTime completeTime = isDeliverComplete ? customerServiceCashierAccounting.getCompleteTime() :
                (Objects.equals(vo.getDeliverResult(), AccountingCashierDeliverResult.EXCEPTION.getCode()) ? null : operTime);
        Integer deliverResult = isDeliverComplete ? customerServiceCashierAccounting.getDeliverResult() : vo.getDeliverResult();
        if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getId, vo.getId())
                    .set(CustomerServiceCashierAccounting::getDeliverResult, deliverResult)
                    .set(CustomerServiceCashierAccounting::getDeliverStatus, deliverStatus)
                    .set(CustomerServiceCashierAccounting::getDeliverRemark, StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                    .set(CustomerServiceCashierAccounting::getMaterialIntegrity, materialIntegrity)
                    .set(CustomerServiceCashierAccounting::getLastOperType, operType)
                    .set(CustomerServiceCashierAccounting::getLastOperRemark, StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                    .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                    .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName());
            if (!Objects.isNull(completeTime)) {
                updateWrapper.set(CustomerServiceCashierAccounting::getLastInTime, completeTime.toLocalDate());
                updateWrapper.set(CustomerServiceCashierAccounting::getLastCompleteUserId, vo.getUserId());
                if (Objects.isNull(customerServiceCashierAccounting.getInTime())) {
                    updateWrapper.set(CustomerServiceCashierAccounting::getInTime, completeTime.toLocalDate());
                }
                if (Objects.isNull(customerServiceCashierAccounting.getCompleteTime())) {
                    updateWrapper.set(CustomerServiceCashierAccounting::getCompleteTime, completeTime)
                            .set(CustomerServiceCashierAccounting::getCompleteUserId, vo.getUserId())
                            .set(CustomerServiceCashierAccounting::getCompleteUserName, vo.getOperName());
                }
            }
            InAccountDeliverVO inAccountDeliverInfo = vo.getInAccountDeliverInfo();
            if (!Objects.isNull(inAccountDeliverInfo)) {
                if (vo.getIsCoverFiles()) {
                    updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal())
                            .set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal())
                            .set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal())
                            .set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease())
                            .set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount())
                            .set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal())
                            .set(CustomerServiceCashierAccounting::getTableStatusBalance, inAccountDeliverInfo.getTableStatusBalance());
                } else {
                    if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getMajorIncomeTotal, inAccountDeliverInfo.getMajorIncomeTotal());
                    }
                    if (!Objects.isNull(inAccountDeliverInfo.getMajorCostTotal())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getMajorCostTotal, inAccountDeliverInfo.getMajorCostTotal());
                    }
                    if (!Objects.isNull(inAccountDeliverInfo.getProfitTotal())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getProfitTotal, inAccountDeliverInfo.getProfitTotal());
                    }
                    if (!StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getPriorYearExpenseIncrease, inAccountDeliverInfo.getPriorYearExpenseIncrease());
                    }
                    if (!Objects.isNull(inAccountDeliverInfo.getTaxReportCount())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportCount, inAccountDeliverInfo.getTaxReportCount());
                    }
                    if (!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                        updateWrapper.set(CustomerServiceCashierAccounting::getTaxReportSalaryTotal, inAccountDeliverInfo.getTaxReportSalaryTotal());
                    }
                }
                if (!Objects.isNull(inAccountDeliverInfo.getMajorIncomeTotal()) || !Objects.isNull(inAccountDeliverInfo.getMajorCostTotal()) || !Objects.isNull(inAccountDeliverInfo.getProfitTotal()) || !StringUtils.isEmpty(inAccountDeliverInfo.getPriorYearExpenseIncrease()) || !Objects.isNull(inAccountDeliverInfo.getTaxReportCount()) ||!Objects.isNull(inAccountDeliverInfo.getTaxReportSalaryTotal())) {
                    updateWrapper.set(CustomerServiceCashierAccounting::getProfitGetTime, LocalDateTime.now());
                }
            }
            update(updateWrapper);
            if (!isDeliverComplete && customerServiceCashierAccounting.getHasTicket() && !Objects.isNull(inAccountDeliverInfo) && !Objects.isNull(inAccountDeliverInfo.getIsSettleAccount()) && inAccountDeliverInfo.getIsSettleAccount() == 1) {
                createSamePeriodBankAccountingCashier(Collections.singletonList(customerServiceCashierAccounting), vo.getOperName(), operTime, vo.getDeptId(), vo.getUserId());
            }
        } else {
            CustomerServiceCashierAccounting update = new CustomerServiceCashierAccounting()
                    .setId(vo.getId())
                    .setDeliverResult(deliverResult)
                    .setDeliverStatus(deliverStatus)
                    .setCompleteTime(!Objects.isNull(completeTime) && Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) ? completeTime : null)
                    .setCompleteUserId(!Objects.isNull(completeTime) && Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) ? vo.getUserId() : null)
                    .setCompleteUserName(!Objects.isNull(completeTime) && Objects.isNull(customerServiceCashierAccounting.getCompleteTime()) ? vo.getOperName() : null)
                    .setInTime(!Objects.isNull(completeTime) && Objects.isNull(customerServiceCashierAccounting.getInTime()) ? completeTime.toLocalDate() : null)
                    .setLastInTime(!Objects.isNull(completeTime) ? completeTime.toLocalDate() : null)
                    .setLastCompleteUserId(vo.getUserId())
                    .setDeliverRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                    .setMaterialIntegrity(vo.getMaterialIntegrity())
                    .setLastOperType(operType)
                    .setLastOperRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark())
                    .setLastOperTime(operTime)
                    .setLastOperName(vo.getOperName());
            updateById(update);
            businessTaskService.dealBusinessTask(deliverStatus, customerServiceCashierAccounting, vo.getOperName(), vo.getUserId(), vo.getDeptId(), "异常交付关闭");
        }
        if (vo.getIsCoverFiles()) {
            customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getId(), AccountingCashierFileType.DELIVER_ATTACHMENT);
        }
        if (!ObjectUtils.isEmpty(vo.getFiles())) {
            customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getFiles(), AccountingCashierFileType.DELIVER_ATTACHMENT);
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServicePeriodMonthService.updateById(new CustomerServicePeriodMonth()
                    .setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(deliverStatus));
        }
        if (!vo.getIsInner()) {
            if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付");
            } else if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付");
            }
        }
        try {
            Map<String, String> operContent = Maps.newLinkedHashMap();
            if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode()) || Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()) || Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) {
                operContent.put("交付结果", AccountingCashierDeliverResult.getNameByCode(vo.getDeliverResult(), vo.getType()));
                if (!Objects.isNull(vo.getMaterialIntegrity())) {
                    operContent.put("材料完整", MaterialIntegrity.getByCode(vo.getMaterialIntegrity()).getDesc());
                }
            }
            if (!Objects.isNull(vo.getInAccountDeliverInfo())) {
                if (vo.getIsCoverFiles()) {
                    operContent.put("本年累计主营收入", Objects.isNull(vo.getInAccountDeliverInfo().getMajorIncomeTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                    operContent.put("本年累计主营成本", Objects.isNull(vo.getInAccountDeliverInfo().getMajorCostTotal()) ? "null" : vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                    operContent.put("本年累计净利润", Objects.isNull(vo.getInAccountDeliverInfo().getProfitTotal()) ? "null" : vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                    operContent.put("本年费用调增", StringUtils.isEmpty(vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease()) ? "null" : vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                    operContent.put("个税申报人数", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportCount()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                    operContent.put("本年个税申报工资总额", Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportSalaryTotal()) ? "null" : vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                    if (!StringUtils.isEmpty(vo.getInAccountDeliverInfo().getTableStatusBalance())) {
                        operContent.put("报表平衡", vo.getInAccountDeliverInfo().getTableStatusBalance());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getIsSettleAccount())) {
                        operContent.put("是否结账", vo.getInAccountDeliverInfo().getIsSettleAccount() == 1 ? "是" : "否");
                    }
                } else {
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getMajorIncomeTotal())) {
                        operContent.put("本年累计主营收入", vo.getInAccountDeliverInfo().getMajorIncomeTotal().stripTrailingZeros().toPlainString());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getMajorCostTotal())) {
                        operContent.put("本年累计主营成本", vo.getInAccountDeliverInfo().getMajorCostTotal().stripTrailingZeros().toPlainString());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getProfitTotal())) {
                        operContent.put("本年累计净利润", vo.getInAccountDeliverInfo().getProfitTotal().stripTrailingZeros().toPlainString());
                    }
                    if (!StringUtils.isEmpty(vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease())) {
                        operContent.put("本年费用调增", vo.getInAccountDeliverInfo().getPriorYearExpenseIncrease());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportCount())) {
                        operContent.put("个税申报人数", vo.getInAccountDeliverInfo().getTaxReportCount().toString());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getTaxReportSalaryTotal())) {
                        operContent.put("本年个税申报工资总额", vo.getInAccountDeliverInfo().getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
                    }
                    if (!Objects.isNull(vo.getInAccountDeliverInfo().getIsSettleAccount())) {
                        operContent.put("是否结账", vo.getInAccountDeliverInfo().getIsSettleAccount() == 1 ? "是" : "否");
                    }
                }
            }
            if (!StringUtils.isEmpty(vo.getRemark())) {
                operContent.put("交付备注", vo.getRemark());
            }
            operContent.put("前置状态", AccountingCashierDeliverStatus.getByCode(customerServiceCashierAccounting.getDeliverStatus()).getName());
            operContent.put("后置状态", AccountingCashierDeliverStatus.getByCode(deliverStatus).getName());
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId())
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles())));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
            customerServiceService.updateLastInAccountId(customerServiceCashierAccounting.getCustomerServiceId(), customerServiceCashierAccounting.getId(), customerServiceCashierAccounting.getPeriod().toString());
        }
    }

    private void buildErrorDataList(List<Long> failIdList, String batchNo, Map<Long, String> errorMagMap, Integer type) {
        List<AccountingCashierDTO> accountingCashierDTOS = baseMapper.accountingCashierListByIds(failIdList);
        if (!ObjectUtils.isEmpty(accountingCashierDTOS)) {
            buildAccountingCashierDTO(accountingCashierDTOS);
            buildOperateErrorDataList(accountingCashierDTOS, batchNo, errorMagMap, type);
        }
    }

    private void buildAccountingCashierDTO(List<AccountingCashierDTO> data) {
        // 部门信息
        List<Long> deptIdList = Lists.newArrayList();
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getCustomerServiceBusinessDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getCustomerServiceAccountingTopDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getCustomerServiceAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getCustomerServiceAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getPeriodAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIdList.addAll(data.stream().map(AccountingCashierDTO::getPeriodAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIdList) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));

        List<Long> customerServiceIdList = data.stream().map(AccountingCashierDTO::getCustomerServiceId).distinct().collect(Collectors.toList());
        List<Long> customerServicePeriodMonthIdList = data.stream().map(AccountingCashierDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
        List<Long> accountingCashierIds = data.stream().map(AccountingCashierDTO::getId).collect(Collectors.toList());
        // 标签信息
        Map<Long, List<TagDTO>> customerServiceTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIdList, TagBusinessType.CUSTOMER_SERVICE);
        Map<Long, List<TagDTO>> periodTagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServicePeriodMonthIdList, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);

        // 员工信息
        Map<Long, List<SysEmployee>> employeeMap = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));

        // 交付单附件
        Map<Long, List<CustomerServiceCashierAccountingFile>> fileMap = customerServiceCashierAccountingFileService.selectBatchByCashierAccountingIds(accountingCashierIds)
                .stream().collect(Collectors.groupingBy(CustomerServiceCashierAccountingFile::getCustomerServiceCashierAccountingId));
        data.forEach(d -> buildAccountingCashierDTO(d, customerServiceTagMap, periodTagMap, deptNameMap, employeeMap, fileMap));
    }

    private void buildOperateErrorDataList(List<AccountingCashierDTO> errorList, String batchNo, Map<Long, String> errorMagMap, Integer type) {
        if (ObjectUtils.isEmpty(errorList)) {
            return;
        }
        if (Objects.equals(type, AccountingCashierType.INCOME.getCode())) {
            List<AccountingCashierInAccountErrorExportDTO> errorDTOList = errorList.stream().map(e -> {
                AccountingCashierInAccountErrorExportDTO dto = new AccountingCashierInAccountErrorExportDTO();
                BeanUtils.copyProperties(e, dto);
                dto.setErrorMsg(errorMagMap.getOrDefault(e.getId(), ""));
                return dto;
            }).collect(Collectors.toList());
            redisService.setLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, errorDTOList, 500, 60 * 60, TimeUnit.SECONDS);
        } else if (Objects.equals(type, AccountingCashierType.FLOW.getCode())) {
            List<AccountingCashierBankErrorExportDTO> errorDTOList = errorList.stream().map(e -> {
                AccountingCashierBankErrorExportDTO dto = new AccountingCashierBankErrorExportDTO();
                BeanUtils.copyProperties(e, dto);
                dto.setErrorMsg(errorMagMap.getOrDefault(e.getId(), ""));
                return dto;
            }).collect(Collectors.toList());
            redisService.setLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, errorDTOList, 500, 60 * 60, TimeUnit.SECONDS);
        } else if (Objects.equals(type, AccountingCashierType.CHANGE.getCode())) {
            List<AccountingCashierModifyAccountErrorExportDTO> errorDTOList = errorList.stream().map(e -> {
                AccountingCashierModifyAccountErrorExportDTO dto = new AccountingCashierModifyAccountErrorExportDTO();
                BeanUtils.copyProperties(e, dto);
                dto.setErrorMsg(errorMagMap.getOrDefault(e.getId(), ""));
                return dto;
            }).collect(Collectors.toList());
            redisService.setLargeCacheList(CacheConstants.ACCOUNTING_CASHIER_OPERATE_ERROR_RECORD + batchNo, errorDTOList, 500, 60 * 60, TimeUnit.SECONDS);
        }
    }

    private void modifyAccountingCashier(AccountingCashierModifyVO vo, LocalDateTime operTime, String operType) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(vo.getAccountingCashierId());
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            throw new ServiceException("交付单不存在");
        }
        if (!AccountingCashierDeliverStatus.canModifyStatus().contains(customerServiceCashierAccounting.getDeliverStatus())) {
            throw new ServiceException("交付单状态不符");
        }
        CCustomerService customerService = customerServiceMapper.selectById(customerServiceCashierAccounting.getCustomerServiceId());
        if (Objects.isNull(customerService) || customerService.getIsDel()) {
            throw new ServiceException("客户不存在");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            LocalDate now = LocalDate.now();
            LocalDate ddlDate = LocalDate.parse(vo.getDdl(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (!ddlDate.isAfter(now)) {
                exceptionService.throwConfigException(ErrorCodeEnum.DDL_ERROR);
            }
        }
        LambdaUpdateWrapper<CustomerServiceCashierAccounting> updateWrapper = new LambdaUpdateWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getId, vo.getAccountingCashierId())
                .set(CustomerServiceCashierAccounting::getHasTicket, Objects.isNull(vo.getHasTicket()) ? null : vo.getHasTicket() == 1)
                .set(CustomerServiceCashierAccounting::getHasBankPayment, Objects.isNull(vo.getHasBankPayment()) ? null : vo.getHasBankPayment() == 1)
                .set(CustomerServiceCashierAccounting::getMaterialMedia, vo.getMaterialMedia())
                .set(CustomerServiceCashierAccounting::getDeliverRequire, vo.getDeliverRequire())
                .set(CustomerServiceCashierAccounting::getDdl, StringUtils.isEmpty(vo.getDdl()) ? null : vo.getDdl())
                .set(CustomerServiceCashierAccounting::getLastOperName, vo.getOperName())
                .set(CustomerServiceCashierAccounting::getLastOperTime, operTime)
                .set(CustomerServiceCashierAccounting::getLastOperRemark, vo.getDeliverRequire());
        if (!Objects.isNull(vo.getIsClose())) {
            updateWrapper.set(CustomerServiceCashierAccounting::getIsClose, vo.getIsClose());
            if (vo.getIsClose() == 1) {
                operType = "编辑且关闭交付";
                updateWrapper.set(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                updateWrapper.set(CustomerServiceCashierAccounting::getDeliverResult, AccountingCashierDeliverResult.NO_DELIVER.getCode());
                updateWrapper.set(CustomerServiceCashierAccounting::getLastOperType, operType);
            }
        }
        update(updateWrapper);
        List<CommonFileVO> operFiles = Lists.newArrayList();
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (vo.getIsCoverFiles()) {
                customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getAccountingCashierId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                vo.getMaterialFiles().forEach(row -> {
                    row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-对账单-" + row.getFileName());
                });
                operFiles.addAll(vo.getMaterialFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
            }
            if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                vo.getReceiptFiles().forEach(row -> {
                    row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-回单-" + row.getFileName());
                });
                operFiles.addAll(vo.getReceiptFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
            }
        } else {
            if (vo.getIsCoverFiles()) {
                customerServiceCashierAccountingFileService.logicDeleteByCashierAccountingIdAndFileType(vo.getAccountingCashierId(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                operFiles.addAll(vo.getMaterialFiles());
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }
        }
        if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode())) {
                customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(customerServiceCashierAccounting.getCustomerServicePeriodMonthId()).setInAccountStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "入账交付单编辑且关闭交付");
            } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单编辑且关闭交付");
            }
        }
        Map<String, String> operContent = new LinkedHashMap<>();
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (!Objects.isNull(vo.getHasBankPayment())) {
                operContent.put("流水情况", vo.getHasBankPayment() == 1 ? "有流水" : "无流水");
            }
        } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode()) || Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
            if (!Objects.isNull(vo.getHasTicket())) {
                operContent.put("凭票入账", vo.getHasTicket() == 1 ? "是" : "否");
            }
        }
        if (!Objects.isNull(vo.getMaterialMedia())) {
            operContent.put("材料介质", AccountingCashierMaterialMedia.getByCode(vo.getMaterialMedia()).getDesc());
        }
        operContent.put("交付要求", StringUtils.isEmpty(vo.getDeliverRequire()) ? "" : vo.getDeliverRequire());
        if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
            operContent.put("关闭交付", "是");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            operContent.put("ddl", vo.getDdl());
        }
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(vo.getDeptId())
                    .setOperType(operType)
                    .setOperName(vo.getOperName())
                    .setOperContent(JSONObject.toJSONString(operContent))
                    .setCreateTime(operTime)
                    .setOperUserId(vo.getUserId())
                    .setOperRemark(vo.getDeliverRequire())
                    .setOperImages(ObjectUtils.isEmpty(operFiles) ? "" : JSONArray.toJSONString(operFiles)));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
        if (!StringUtils.isEmpty(vo.getDdl())) {
            businessDdlRecordService.saveDdlRecord(customerServiceCashierAccounting.getId(), BusinessLogBusinessType.ACCOUNTING_CASHIER, vo.getUserId(), vo.getDdl());
        }
    }

    private void createAccountingCashier(AccountingCashierCreateVO vo, LocalDateTime operTime, String operType) {
        if (Objects.isNull(vo.getId())) {
            CCustomerService customerService = customerServiceMapper.selectById(vo.getCustomerServiceId());
            if (Objects.isNull(customerService) || customerService.getIsDel()) {
                throw new ServiceException("客户不存在");
            }
            if (!StringUtils.isEmpty(vo.getDdl())) {
                LocalDate now = LocalDate.now();
                LocalDate ddlDate = LocalDate.parse(vo.getDdl(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                if (!ddlDate.isAfter(now)) {
                    exceptionService.throwConfigException(ErrorCodeEnum.DDL_ERROR);
                }
            }
            checkAccountingCashierExists(vo);
            CustomerServiceCashierAccounting customerServiceCashierAccounting = new CustomerServiceCashierAccounting();
            customerServiceCashierAccounting.setTitle(getAccountingCashierTitle(vo.getType(), vo.getPeriod(), vo.getBankName(), vo.getBankAccountNumber()));
            customerServiceCashierAccounting.setCustomerServiceId(vo.getCustomerServiceId());
            customerServiceCashierAccounting.setCustomerServicePeriodMonthId(vo.getCustomerServicePeriodMonthId());
            customerServiceCashierAccounting.setPeriod(vo.getPeriod());
            customerServiceCashierAccounting.setType(vo.getType());
            customerServiceCashierAccounting.setHasTicket(Objects.isNull(vo.getHasTicket()) ? null : vo.getHasTicket() == 1);
            customerServiceCashierAccounting.setBankName(vo.getBankName());
            customerServiceCashierAccounting.setBankAccountNumber(vo.getBankAccountNumber());
            customerServiceCashierAccounting.setHasBankPayment(Objects.isNull(vo.getHasBankPayment()) ? null : vo.getHasBankPayment() == 1);
            customerServiceCashierAccounting.setDeliverRequire(vo.getDeliverRequire());
            customerServiceCashierAccounting.setMaterialMedia(vo.getMaterialMedia());
            customerServiceCashierAccounting.setIsClose(vo.getIsClose());
            if (!StringUtils.isEmpty(vo.getDdl())) {
                customerServiceCashierAccounting.setDdl(DateUtils.strToLocalDate(vo.getDdl(), DateUtils.YYYY_MM_DD));
            }
            setCustomerDeliverStatusAndDeliverResult(customerServiceCashierAccounting, operTime, vo.getDeliverRequire(), operType, vo.getIsClose());
            customerServiceCashierAccounting.setLastOperName(vo.getOperName());
            customerServiceCashierAccounting.setCreateBy(vo.getOperName());
            customerServiceCashierAccounting.setCreateOperType(operType);
            customerServiceCashierAccounting.setCreateTime(operTime);
            save(customerServiceCashierAccounting);
            List<CommonFileVO> operFiles = Lists.newArrayList();
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles()) && Objects.equals(vo.getMaterialMedia(), AccountingCashierMaterialMedia.ELECTRONIC.getCode())) {
                if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                        vo.getMaterialFiles().forEach(row -> {
                            row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-对账单-" + row.getFileName());
                        });
                        operFiles.addAll(vo.getMaterialFiles());
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 1);
                    }
                    if (!ObjectUtils.isEmpty(vo.getReceiptFiles())) {
                        vo.getReceiptFiles().forEach(row -> {
                            row.setFileName(customerService.getCustomerName() + "-" + customerServiceCashierAccounting.getBankAccountNumber() + "-" + customerServiceCashierAccounting.getPeriod() + "-" + customerServiceCashierAccounting.getPeriod() + "-回单-" + row.getFileName());
                        });
                        operFiles.addAll(vo.getReceiptFiles());
                        customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getReceiptFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT, 2);
                    }
                } else {
                    if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                        operFiles.addAll(vo.getMaterialFiles());
                    }
                    customerServiceCashierAccountingFileService.saveAccountingCashierFile(customerServiceCashierAccounting.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
                }
            }
            if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                customerServicePeriodMonthMapper.updateById(new CustomerServicePeriodMonth().setId(vo.getCustomerServicePeriodMonthId()).setInAccountStatus(customerServiceCashierAccounting.getDeliverStatus()));
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "");
            } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), vo.getOperName(), vo.getDeptId(), vo.getUserId(), "银行流水交付单创建");
                if (!Objects.isNull(vo.getHasBankPayment()) && vo.getHasBankPayment() == 1 && vo.getIsClose() == 0) {
                    if (Objects.equals(vo.getMaterialMedia(), AccountingCashierMaterialMedia.BANK_CARD.getCode())) {
                        // 有流水且是银企 走RPA
                        autoFlowRpaDeal(customerServiceCashierAccounting, vo.getOperName(), customerServiceCashierAccounting.getCustomerServiceId(), vo.getUserId(), vo.getDeptId());
                    } else {
                        accountingCashierFlowAutoDeal(customerServiceCashierAccounting, customerService, operTime, vo.getDeptId(), vo.getUserId(), vo.getOperName());
                    }
                }
            }
            Map<String, String> operContent = new LinkedHashMap<>();
            if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
                if (!Objects.isNull(vo.getHasBankPayment())) {
                    operContent.put("流水情况", vo.getHasBankPayment() == 1 ? "有流水" : "无流水");
                }
            } else if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.INCOME.getCode()) || Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.CHANGE.getCode())) {
                if (!Objects.isNull(vo.getHasTicket())) {
                    operContent.put("凭票入账", vo.getHasTicket() == 1 ? "是" : "否");
                }
            }
            if (!Objects.isNull(vo.getMaterialMedia())) {
                operContent.put("材料介质", AccountingCashierMaterialMedia.getByCode(vo.getMaterialMedia()).getDesc());
            }
            operContent.put("交付要求", StringUtils.isEmpty(vo.getDeliverRequire()) ? "" : vo.getDeliverRequire());
            if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
                operContent.put("关闭交付", "是");
            }
            if (!StringUtils.isEmpty(vo.getDdl())) {
                operContent.put("ddl", vo.getDdl());
            }
            if (!Objects.isNull(vo.getIsClose()) && vo.getIsClose() == 1) {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(customerServiceCashierAccounting.getLastOperType())
                            .setOperName(vo.getOperName())
                            .setCreateTime(operTime)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperUserId(vo.getUserId())
                            .setOperRemark(vo.getDeliverRequire())
                            .setOperImages(ObjectUtils.isEmpty(operFiles) ? "" : JSONArray.toJSONString(operFiles)));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
            } else {
                try {
                    asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                            .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                            .setDeptId(vo.getDeptId())
                            .setOperType(operType)
                            .setOperName(vo.getOperName())
                            .setCreateTime(operTime)
                            .setOperContent(JSONObject.toJSONString(operContent))
                            .setOperUserId(vo.getUserId())
                            .setOperRemark(vo.getDeliverRequire())
                            .setOperImages(ObjectUtils.isEmpty(operFiles) ? "" : JSONArray.toJSONString(operFiles)));
                } catch (Exception e) {
                    log.error("新增业务日志失败:{}", e.getMessage());
                    throw new ServiceException("操作记录写入失败，请稍后重试");
                }
                if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) {
                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("交付内容", "无流水自动完结");
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType("交付")
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime.plusSeconds(1L))
                                .setOperContent(JSONObject.toJSONString(content))
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                } else if (Objects.equals(customerServiceCashierAccounting.getDeliverStatus(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())) {
                    Map<String, String> content = new LinkedHashMap<>();
                    content.put("结果", "异常");
                    content.put("后置状态", "异常");
                    try {
                        asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(customerServiceCashierAccounting.getId())
                                .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                                .setDeptId(vo.getDeptId())
                                .setOperType("交付")
                                .setOperName(vo.getOperName())
                                .setCreateTime(operTime.plusSeconds(1L))
                                .setOperContent(JSONObject.toJSONString(content))
                                .setOperRemark("超期未交接，自行负责流水录入，录完之后操作处理异常——关闭交付，并创建双报交付单。顾问如果认为有异议，处理异常——解除异常，并备注什么时候交接给材料组")
                                .setOperUserId(vo.getUserId()));
                    } catch (Exception e) {
                        log.error("新增业务日志失败:{}", e.getMessage());
                        throw new ServiceException("操作记录写入失败，请稍后重试");
                    }
                }
            }
            if (!StringUtils.isEmpty(vo.getDdl())) {
                businessDdlRecordService.saveDdlRecord(customerServiceCashierAccounting.getId(), BusinessLogBusinessType.ACCOUNTING_CASHIER, vo.getUserId(), vo.getDdl());
            }
        } else {
            CustomerServiceCashierAccounting customerServiceCashierAccounting = new CustomerServiceCashierAccounting();
            customerServiceCashierAccounting.setId(vo.getId());
            customerServiceCashierAccounting.setLastOperName(vo.getOperName());
            customerServiceCashierAccounting.setLastOperTime(operTime);
            customerServiceCashierAccounting.setLastOperType(operType);
            customerServiceCashierAccounting.setLastOperRemark(vo.getDeliverRequire());
            updateById(customerServiceCashierAccounting);
            if (!ObjectUtils.isEmpty(vo.getMaterialFiles())) {
                customerServiceCashierAccountingFileService.saveAccountingCashierFile(vo.getId(), vo.getMaterialFiles(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT);
            }

            try {
                asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(vo.getId())
                        .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                        .setDeptId(vo.getDeptId())
                        .setOperType(operType)
                        .setOperName(vo.getOperName())
                        .setCreateTime(operTime)
                        .setOperUserId(vo.getUserId())
                        .setOperImages(ObjectUtils.isEmpty(vo.getMaterialFiles()) ? "" : JSONArray.toJSONString(vo.getMaterialFiles())));
            } catch (Exception e) {
                log.error("新增业务日志失败:{}", e.getMessage());
                throw new ServiceException("操作记录写入失败，请稍后重试");
            }
        }
    }

    private void accountingCashierOverTimeClose(Long id, LocalDateTime operTime, Long deptId, Long userId, String operName) {
        CustomerServiceCashierAccounting customerServiceCashierAccounting = getById(id);
        if (Objects.isNull(customerServiceCashierAccounting) || customerServiceCashierAccounting.getIsDel()) {
            return;
        }
        updateById(new CustomerServiceCashierAccounting().setId(id)
                    .setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode())
                    .setDeliverResult(AccountingCashierDeliverResult.EXCEPTION.getCode())
                    .setLastOperTime(operTime.plusSeconds(1L))
                    .setLastOperType("交付")
                    .setLastOperRemark("超期未交接，自行负责流水录入，录完之后操作处理异常——关闭交付，并创建双报交付单。顾问如果认为有异议，处理异常——解除异常，并备注什么时候交接给材料组。")
        );
        updateBankPaymentResultSettleAccountStatus(customerServiceCashierAccounting.getCustomerServicePeriodMonthId(), operTime.plusSeconds(1), operName, deptId, userId, "流水超期未交接");
        Map<String, String> content = new LinkedHashMap<>();
        content.put("结果", "异常");
        content.put("后置状态", "异常");
        try {
            asyncLogService.saveBusinessLog(new BusinessLogDTO().setBusinessId(id)
                    .setBusinessType(BusinessLogBusinessType.ACCOUNTING_CASHIER.getCode())
                    .setDeptId(deptId)
                    .setOperType("交付")
                    .setOperName(operName)
                    .setCreateTime(operTime.plusSeconds(1L))
                    .setOperContent(JSONObject.toJSONString(content))
                    .setOperRemark("超期未交接，自行负责流水录入，录完之后操作处理异常——关闭交付，并创建双报交付单。顾问如果认为有异议，处理异常——解除异常，并备注什么时候交接给材料组")
                    .setOperUserId(userId));
        } catch (Exception e) {
            log.error("新增业务日志失败:{}", e.getMessage());
            throw new ServiceException("操作记录写入失败，请稍后重试");
        }
    }

    private void setCustomerDeliverStatusAndDeliverResult(CustomerServiceCashierAccounting customerServiceCashierAccounting, LocalDateTime operTime, String deliverRequire, String operType, Integer isClose) {
        if (Objects.equals(customerServiceCashierAccounting.getType(), AccountingCashierType.FLOW.getCode())) {
            if (isClose == 1) {
                customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                customerServiceCashierAccounting.setDeliverResult(AccountingCashierDeliverResult.NO_DELIVER.getCode());
                customerServiceCashierAccounting.setLastOperTime(operTime);
                customerServiceCashierAccounting.setLastOperType("新建且关闭交付");
                customerServiceCashierAccounting.setLastOperRemark(deliverRequire);
            } else {
                if (!Objects.isNull(customerServiceCashierAccounting.getHasBankPayment())) {
                    if (!customerServiceCashierAccounting.getHasBankPayment()) {
                        customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                        customerServiceCashierAccounting.setDeliverResult(AccountingCashierDeliverResult.NO_ACCOUNT.getCode());
                        customerServiceCashierAccounting.setLastOperTime(operTime.plusSeconds(1L));
                        customerServiceCashierAccounting.setLastOperType("交付");
                        customerServiceCashierAccounting.setLastOperRemark("");
                    } else {
                        customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
                        customerServiceCashierAccounting.setLastOperTime(operTime);
                        customerServiceCashierAccounting.setLastOperType(operType);
                        customerServiceCashierAccounting.setLastOperRemark(deliverRequire);
                    }
                } else {
                    customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
                    customerServiceCashierAccounting.setLastOperTime(operTime);
                    customerServiceCashierAccounting.setLastOperType(operType);
                    customerServiceCashierAccounting.setLastOperRemark(deliverRequire);
                }
            }
        } else {
            if (!Objects.isNull(isClose) && isClose == 1) {
                customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode());
                customerServiceCashierAccounting.setDeliverResult(AccountingCashierDeliverResult.NO_ACCOUNT.getCode());
                customerServiceCashierAccounting.setLastOperTime(operTime.plusSeconds(1L));
                customerServiceCashierAccounting.setLastOperType("新建且关闭交付");
                customerServiceCashierAccounting.setLastOperRemark("");
            } else {
                customerServiceCashierAccounting.setDeliverStatus(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode());
                customerServiceCashierAccounting.setLastOperTime(operTime);
                customerServiceCashierAccounting.setLastOperType(operType);
                customerServiceCashierAccounting.setLastOperRemark(deliverRequire);
            }
        }
    }

    private void checkAccountingCashierExists(AccountingCashierCreateVO vo) {
        if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
            // 入账 按客户+账期唯一
            if (count(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                    .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())) > 0) {
                throw new ServiceException("该账期已存在入账交付单");
            }
        } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
            // 流水 按客户+账期+银行唯一
            if (count(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                    .eq(CustomerServiceCashierAccounting::getBankAccountNumber, vo.getBankAccountNumber())
                    .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.FLOW.getCode())) > 0) {
                throw new ServiceException("该银行该账期已存在流水交付单");
            }
        } else if (Objects.equals(vo.getType(), AccountingCashierType.CHANGE.getCode())) {
            // 存在同账期的入账交付单,且已完成;账期的结账事项是已结账;同账期不存在未完成的改账交付单
            if (count(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.INCOME.getCode())
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                    .eq(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())) == 0) {
                throw new ServiceException("不存在已完成的入账交付单");
            }
            if (customerServicePeriodMonthService.count(new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getId, vo.getCustomerServicePeriodMonthId())
                    .eq(CustomerServicePeriodMonth::getSettleAccountStatus, InAccountStatus.DONE.getCode())) == 0) {
                throw new ServiceException("结账事项不为已结账");
            }
            if (count(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                    .eq(CustomerServiceCashierAccounting::getIsDel, false)
                    .eq(CustomerServiceCashierAccounting::getCustomerServicePeriodMonthId, vo.getCustomerServicePeriodMonthId())
                    .ne(CustomerServiceCashierAccounting::getDeliverStatus, AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode())
                    .eq(CustomerServiceCashierAccounting::getType, AccountingCashierType.CHANGE.getCode())) > 0) {
                throw new ServiceException("该账期存在未完成的改账交付单");
            }
        }
    }

    private void buildAccountingCashierDTO(AccountingCashierDTO d, Map<Long, List<TagDTO>> customerServiceTagMap, Map<Long, List<TagDTO>> periodTagMap, Map<Long, String> deptNameMap,
                                           Map<Long, List<SysEmployee>> employeeMap, Map<Long, List<CustomerServiceCashierAccountingFile>> fileMap) {
        List<TagDTO> customerServiceTagList = customerServiceTagMap.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList());
        List<TagDTO> periodTagList = periodTagMap.getOrDefault(d.getCustomerServicePeriodMonthId(), Lists.newArrayList());
        List<CustomerServiceCashierAccountingFile> fileList = fileMap.getOrDefault(d.getId(), Lists.newArrayList());
        d.setCustomerServiceTaxTypeStr(TaxType.getByCode(d.getCustomerServiceTaxType()).getDesc());
        d.setPeriodTaxTypeStr(TaxType.getByCode(d.getPeriodTaxType()).getDesc());
        d.setCustomerServiceTagNames(ObjectUtils.isEmpty(customerServiceTagList) ? "" : customerServiceTagList.stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
        d.setPeriodTagNames(ObjectUtils.isEmpty(periodTagList) ? "" : periodTagList.stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
        d.setCustomerServiceBusinessDeptName(deptNameMap.getOrDefault(d.getCustomerServiceBusinessDeptId(), ""));
        d.setCustomerServiceAccountingTopDeptName(deptNameMap.getOrDefault(d.getCustomerServiceAccountingTopDeptId(), ""));
        List<SysEmployee> customerServiceAdvisorEmployeeList = employeeMap.getOrDefault(d.getCustomerServiceAdvisorDeptId(), Lists.newArrayList());
        List<SysEmployee> periodAdvisorEmployeeList = employeeMap.getOrDefault(d.getPeriodAdvisorDeptId(), Lists.newArrayList());
        List<SysEmployee> customerServiceAccountingEmployeeList = employeeMap.getOrDefault(d.getCustomerServiceAccountingDeptId(), Lists.newArrayList());
        List<SysEmployee> periodAccountingEmployeeList = employeeMap.getOrDefault(d.getPeriodAccountingDeptId(), Lists.newArrayList());
        d.setCustomerServiceAdvisorInfo(deptNameMap.getOrDefault(d.getCustomerServiceAdvisorDeptId(), "") + (ObjectUtils.isEmpty(customerServiceAdvisorEmployeeList) ? "" : ("（ " + customerServiceAdvisorEmployeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）")));
        d.setCustomerServiceAccountingInfo(deptNameMap.getOrDefault(d.getCustomerServiceAccountingDeptId(), "") + (ObjectUtils.isEmpty(customerServiceAccountingEmployeeList) ? "" : ("（ " + customerServiceAccountingEmployeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）")));
        d.setPeriodAccountingInfo(deptNameMap.getOrDefault(d.getPeriodAccountingDeptId(), "") + (ObjectUtils.isEmpty(periodAccountingEmployeeList) ? "" : ("（ " + periodAccountingEmployeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）")));
        d.setPeriodAdvisorInfo(deptNameMap.getOrDefault(d.getPeriodAdvisorDeptId(), "") + (ObjectUtils.isEmpty(periodAdvisorEmployeeList) ? "" : ("（ " + periodAdvisorEmployeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）")));
        d.setHasBankPaymentStr(Objects.isNull(d.getHasBankPayment()) ? "" : (d.getHasBankPayment() ? "有" : "无"));
        d.setHasTicketStr(Objects.isNull(d.getHasTicket()) ? "" : (d.getHasTicket() ? "是" : "否"));
        d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
        d.setMattersNotes(d.getHasMattersNotes() ? "是" : "否");
        d.setMaterialMediaStr(AccountingCashierMaterialMedia.getByCode(d.getMaterialMedia()).getDesc());
        d.setMaterialMediaFileCount(fileList.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.MEDIA_MATERIAL_ATTACHMENT.getCode())).count());
        d.setTaskStatusStr(Objects.isNull(d.getTaskStatus()) ? "" : BusinessTaskStatus.getByCode(d.getTaskStatus()).getName());
        d.setDeliverStatusStr(Objects.isNull(d.getDeliverStatus()) ? "" : AccountingCashierDeliverStatus.getByCode(d.getDeliverStatus()).getName());
        d.setDeliverResultStr(Objects.isNull(d.getDeliverResult()) ? "" : AccountingCashierDeliverResult.getNameByCode(d.getDeliverResult(), d.getType()));
        d.setCompleteTimeStr(Objects.isNull(d.getCompleteTime()) ? "" : d.getCompleteTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        d.setDeliverFileCount(fileList.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.DELIVER_ATTACHMENT.getCode())).count());
        d.setProfitGetTimeStr(Objects.isNull(d.getProfitGetTime()) ? "" : d.getProfitGetTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        d.setMajorIncomeTotalStr(Objects.isNull(d.getMajorIncomeTotal()) ? "" : d.getMajorIncomeTotal().stripTrailingZeros().toPlainString());
        d.setMajorCostTotalStr(Objects.isNull(d.getMajorCostTotal()) ? "" : d.getMajorCostTotal().stripTrailingZeros().toPlainString());
        d.setProfitTotalStr(Objects.isNull(d.getProfitTotal()) ? "" : d.getProfitTotal().stripTrailingZeros().toPlainString());
        d.setTaxReportSalaryTotalStr(Objects.isNull(d.getTaxReportSalaryTotal()) ? "" : d.getTaxReportSalaryTotal().stripTrailingZeros().toPlainString());
        d.setTableStatusBalanceStr(StringUtils.isEmpty(d.getTableStatusBalance()) ? "" : d.getTableStatusBalance());
        d.setRpaExeResultStr(Objects.isNull(d.getRpaExeResult()) ? "" : InAccountRpaExeResult.getByCode(d.getRpaExeResult()).getName());
        d.setRpaSearchTimeStr(Objects.isNull(d.getRpaSearchTime()) ? "" : d.getRpaSearchTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        d.setRpaFileCount(fileList.stream().filter(f -> Objects.equals(f.getFileType(), AccountingCashierFileType.RPA_ATTACHMENT.getCode())).count());
        d.setFiles(fileList.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName())
                .fileUrl(f.getFileUrl()).fileSize(f.getFileSize()).deliverFileType(f.getFileType()).build()).collect(Collectors.toList()));
        d.setBankPaymentResultStr(Objects.isNull(d.getBankPaymentResult()) ? "" : BankPaymentResult.getByCode(d.getBankPaymentResult()).getDesc());
        d.setSettleAccountStatusStr(Objects.isNull(d.getSettleAccountStatus()) ? "" : InAccountStatus.getByCode(d.getSettleAccountStatus()).getName());
        d.setEndTimeStr(Objects.isNull(d.getEndTime()) ? "" : d.getEndTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        // 材料完整度为null时，显示-，不为null但材料补充状态为待核对时，显示待核对，其他显示材料完整度
        d.setMaterialIntegrityStr(getMaterialIntegrityStr(d.getMaterialIntegrity(), d.getMaterialSupplementStatus()));
        d.setLastInTimeStr(Objects.isNull(d.getLastInTime()) ? "" : d.getLastInTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        d.setLastEndTimeStr(Objects.isNull(d.getLastEndTime()) ? "" : d.getLastEndTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD)));
        d.setCreateTimeStr(Objects.isNull(d.getCreateTime()) ? "" : d.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        d.setDdlStr(Objects.isNull(d.getDdl()) ? null : DateUtils.localDateToStr(d.getDdl(), DateUtils.YYYY_MM_DD));
    }

    private String getMaterialIntegrityStr(Integer materialIntegrity, Integer materialSupplementStatus) {
        if (Objects.isNull(materialIntegrity)) {
            return "-";
        } else if (Objects.equals(materialSupplementStatus, MaterialSupplementStatus.TO_BE_CHECKED.getCode())) {
            return MaterialSupplementStatus.TO_BE_CHECKED.getDesc();
        } else {
            return MaterialIntegrity.getByCode(materialIntegrity).getDesc();
        }
    }

    private String getAccountingCashierTitle(Integer type, Integer period, String bankName, String bankAccountNumber) {
        if (Objects.equals(type, AccountingCashierType.INCOME.getCode())) {
            return String.format("入账交付单【%s】", period);
        } else if (Objects.equals(type, AccountingCashierType.FLOW.getCode())) {
            return String.format("流水交付单【%s-%s-%s】", bankName, bankAccountNumber, period);
        } else if (Objects.equals(type, AccountingCashierType.CHANGE.getCode())) {
            return String.format("改账交付单【%s】", period);
        } else {
            return "";
        }
    }
}
