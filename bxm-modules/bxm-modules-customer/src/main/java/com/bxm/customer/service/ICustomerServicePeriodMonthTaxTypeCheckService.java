package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;

/**
 * 服务月账期税种核定Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface ICustomerServicePeriodMonthTaxTypeCheckService extends IService<CustomerServicePeriodMonthTaxTypeCheck>
{
    /**
     * 查询服务月账期税种核定
     * 
     * @param id 服务月账期税种核定主键
     * @return 服务月账期税种核定
     */
    public CustomerServicePeriodMonthTaxTypeCheck selectCustomerServicePeriodMonthTaxTypeCheckById(Long id);

    /**
     * 查询服务月账期税种核定列表
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 服务月账期税种核定集合
     */
    public List<CustomerServicePeriodMonthTaxTypeCheck> selectCustomerServicePeriodMonthTaxTypeCheckList(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 新增服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    public int insertCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 修改服务月账期税种核定
     * 
     * @param customerServicePeriodMonthTaxTypeCheck 服务月账期税种核定
     * @return 结果
     */
    public int updateCustomerServicePeriodMonthTaxTypeCheck(CustomerServicePeriodMonthTaxTypeCheck customerServicePeriodMonthTaxTypeCheck);

    /**
     * 批量删除服务月账期税种核定
     * 
     * @param ids 需要删除的服务月账期税种核定主键集合
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthTaxTypeCheckByIds(Long[] ids);

    /**
     * 删除服务月账期税种核定信息
     * 
     * @param id 服务月账期税种核定主键
     * @return 结果
     */
    public int deleteCustomerServicePeriodMonthTaxTypeCheckById(Long id);

    void saveByCheckVOList(Long customerServicePeriodMonthId, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList);

    void deleteByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId);

    List<CustomerServiceTaxTypeCheckVO> selectTaxTypeCheckByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId);

    List<CustomerServicePeriodMonthTaxTypeCheck> selectByCustomerServicePeriodMonthId(Long customerServicePeriodMonthId);

    void saveNewPeriodTaxCheck(Integer nowPeriod);

    void saveNewPeriodTaxCheckByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd);

    void removeAndCreateByXqy(List<Long> periodIds, List<OpenApiSyncItem> itemList);

    void deleteAndSaveNewPeriodTaxCheckByCustomerServiceIds(List<Long> customerServiceIds, Integer periodStart, Integer periodEnd, List<Long> customerServicePeriodMonthIds);
}
