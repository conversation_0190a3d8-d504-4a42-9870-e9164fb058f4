package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.customer.domain.SettlementOrder;
import com.bxm.customer.domain.dto.settlementOrder.AddDataResultDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDeptPriceDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementBusinessPriceGetVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderCreateVO;
import com.bxm.customer.domain.vo.settlementOrder.task.BusinessDeptPeriodListForTaskVO;
import com.bxm.customer.domain.vo.settlementOrder.task.DeleteBusinessPeriodListForTaskVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 结算单Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface ISettlementOrderService extends IService<SettlementOrder>
{
    /**
     * 查询结算单
     * 
     * @param id 结算单主键
     * @return 结算单
     */
    public SettlementOrder selectSettlementOrderById(Long id);

    /**
     * 查询结算单列表
     * 
     * @param settlementOrder 结算单
     * @return 结算单集合
     */
    public List<SettlementOrder> selectSettlementOrderList(SettlementOrder settlementOrder);

    /**
     * 新增结算单
     * 
     * @param settlementOrder 结算单
     * @return 结果
     */
    public int insertSettlementOrder(SettlementOrder settlementOrder);

    /**
     * 修改结算单
     * 
     * @param settlementOrder 结算单
     * @return 结果
     */
    public int updateSettlementOrder(SettlementOrder settlementOrder);

    /**
     * 批量删除结算单
     * 
     * @param ids 需要删除的结算单主键集合
     * @return 结果
     */
    public int deleteSettlementOrderByIds(Long[] ids);

    /**
     * 删除结算单信息
     * 
     * @param id 结算单主键
     * @return 结果
     */
    public int deleteSettlementOrderById(Long id);

    SettlementOrderDeptPriceDTO businessDeptPriceList(SettlementBusinessPriceGetVO vo);

    //******** START 仿照 结算单 的 任务用的几个接口
    BusinessPeriodListForTaskDTO businessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo);

    BusinessPeriodListForTaskDTO freshBusinessPeriodListForTask(BusinessDeptPeriodListForTaskVO vo);

    void deleteBusinessPeriodListForTask(DeleteBusinessPeriodListForTaskVO vo);
    //******** END 仿照 结算单 的 任务用的几个接口

    SettlementOrderDeptPriceDTO freshBusinessDeptPriceList(SettlementBusinessPriceGetVO vo);

    void createSettlementOrder(SettlementOrderCreateVO vo);

    void modifySettlementOrder(SettlementOrderCreateVO vo);

    String uploadDetailFile(MultipartFile file, Integer settlementType, Integer isSupplement, String batchNo, Long settlementOrderId, Long businessDeptId);

    AddDataResultDTO confirmAddData(String uploadBatchNo, String batchNo, Long settlementOrderId, Integer settlementType, Long businessDeptId);

    List<SettlementPushReviewDTO> pushReview(List<Long> ids);

    void deleteSettlementOrder(Long id);

    void deleteSettlementOrderByBill(List<Long> settlementOrderIds);

    void reBackSettlementOrderByBill(List<Long> settlementOrderIds);

    CommonOperateResultDTO deleteSettlementOrderBatch(List<Long> ids);
}
