package com.bxm.customer.service;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class CuOssService {

    private static final String ACCESS_KEY = "LTAI5t9TQPHCxG8vU9XXjfWa";

    private static final String SECRET_KEY = "******************************";

    private static final String ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";

    private static final String INNER_ENDPOINT = "oss-cn-hangzhou-internal.aliyuncs.com";

    private static final String BUCKET_NAME = "bxm410";

    private OSS ossClient;

    public CuOssService() {
        this.ossClient = new OSSClientBuilder().build(INNER_ENDPOINT, ACCESS_KEY, SECRET_KEY);
    }

    /**
     * 上传Excel文件到OSS
     *
     * @param file Excel文件
     * @param key  文件在OSS中的路径
     * @return 文件访问URL
     */
    public String uploadExcelFile(File file, String key) throws IOException {
        // 将文件上传到OSS
        try (InputStream inputStream = new FileInputStream(file)) {
            // 上传文件到OSS
            ossClient
                    .putObject(BUCKET_NAME, key, inputStream);
        }


        // 返回文件的访问URL
        return "https://" + BUCKET_NAME + "." + ENDPOINT.toString().replace("http://", "") + "/" + key;
    }

    /**
     * 上传 ZIP 文件并解压到 OSS
     *
     * @param file    ZIP 文件
     * @param baseKey 解压后的文件存储路径前缀
     * @return 解压后的文件名列表
     */
    public List<String> uploadAndUnzip(File file, String baseKey) throws IOException {
        List<String> unzippedFileNames = new ArrayList<>();

        // 上传 ZIP 文件到 OSS
        String zipKey = baseKey + "/" + file.getName();
        try (InputStream inputStream = new FileInputStream(file)) {
            ossClient.putObject(BUCKET_NAME, zipKey, inputStream);
        }

        // 解压 ZIP 文件并上传内容到 OSS
        try (FileInputStream fis = new FileInputStream(file);
             ZipInputStream zipInputStream = new ZipInputStream(fis)) {

            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                try {
                    if (!entry.isDirectory()) {
                        // 构造解压文件的路径，并确保路径安全
                        String entryName = entry.getName();
                        String entryKey = baseKey + "/" + file.getName().toUpperCase().replace(".ZIP","") + "/" + normalizePath(entryName);
                        unzippedFileNames.add(entryKey);

                        // 将解压后的内容写入临时文件
                        File tempFile = File.createTempFile("unzip-", ".tmp");
                        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = zipInputStream.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                        }

                        // 上传临时文件到 OSS
                        try (InputStream tempFileInputStream = new FileInputStream(tempFile)) {
                            ossClient.putObject(BUCKET_NAME, entryKey, tempFileInputStream);
                        } catch (Exception e) {
                            // 记录日志并继续处理下一个文件
                            System.err.println("上传文件失败: " + entryKey + ", 错误: " + e.getMessage());
                        } finally {
                            // 删除临时文件
                            tempFile.delete();
                        }
                    }
                } finally {
                    // 确保关闭当前条目
                    zipInputStream.closeEntry();
                }
            }
        } catch (IOException e) {
            // 记录日志并重新抛出异常
            throw new IOException("解压或上传文件失败: " + e.getMessage(), e);
        }

        return unzippedFileNames;
    }

    // 规范化路径，防止路径遍历攻击
    private String normalizePath(String path) {
        return Paths.get(path).normalize().toString();
    }



    public String downloadFilesAndGenerateZiByFilesAndUpload(List<CommonFileVO> files, Map<String, List<?>> dataMap, Map<String, Class<?>> sheetClassMap, String fileName) throws IOException {
        File zipFile = File.createTempFile("temp", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            // 下载所有文件并添加到zip
            for (CommonFileVO file : files) {
                try {
                    String baseDir = file.getBaseDir();
                    addFileToZip(file.getFileUrl(), baseDir, file.getFileName(), zos);
                } catch (Exception e) {
                    log.error("文件下载失败，file:{}", JSONObject.toJSONString(file));
                }
            }

            // 生成并添加Excel文件到zip
            Workbook workbook = ExcelUtils.exportExcelZip(dataMap, sheetClassMap);
            ZipEntry excelEntry = new ZipEntry(fileName + ".xlsx");
            zos.putNextEntry(excelEntry);
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                workbook.write(baos);
                zos.write(baos.toByteArray());
            }
        }

        // 上传到OSS
        String ossUrl = uploadToOSS(zipFile);

        // 上传成功后，删除临时文件
        if (zipFile.exists()) {
            zipFile.delete();
        }

        return ossUrl;
    }

    public String uploadToOSS(File file) {
        try {
            // 上传文件
            String objectName = "export/" + file.getName(); // 根据需求生成文件路径
            ossClient.putObject(BUCKET_NAME, objectName, new FileInputStream(file));

            // 返回文件的OSS URL
            return objectName;
        } catch (Exception e) {
//            e.printStackTrace();
            return null;
        }
    }

    private void addFileToZip(String key, String baseDir, String fileName, ZipOutputStream zos) throws IOException {
        if (StringUtils.isEmpty(baseDir)) {
            zos.putNextEntry(new ZipEntry(fileName));
        } else {
            zos.putNextEntry(new ZipEntry(baseDir + "/" + fileName));
        }
        if (!StringUtils.isEmpty(key)) {
            InputStream is = ossClient.getObject(BUCKET_NAME, key).getObjectContent();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                zos.write(buffer, 0, len);
            }
            is.close();
        }
    }
}
