package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.dto.workOrder.WorkOrderAccountingCashierDTO;
import com.bxm.customer.mapper.CustomerServiceCashierAccountingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.WorkOrderAccountingCashierRelationMapper;
import com.bxm.customer.domain.WorkOrderAccountingCashierRelation;
import com.bxm.customer.service.IWorkOrderAccountingCashierRelationService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 工单账务交付单关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
@Service
public class WorkOrderAccountingCashierRelationServiceImpl extends ServiceImpl<WorkOrderAccountingCashierRelationMapper, WorkOrderAccountingCashierRelation> implements IWorkOrderAccountingCashierRelationService
{
    @Autowired
    private WorkOrderAccountingCashierRelationMapper workOrderAccountingCashierRelationMapper;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    /**
     * 查询工单账务交付单关联
     * 
     * @param id 工单账务交付单关联主键
     * @return 工单账务交付单关联
     */
    @Override
    public WorkOrderAccountingCashierRelation selectWorkOrderAccountingCashierRelationById(Long id)
    {
        return workOrderAccountingCashierRelationMapper.selectWorkOrderAccountingCashierRelationById(id);
    }

    /**
     * 查询工单账务交付单关联列表
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 工单账务交付单关联
     */
    @Override
    public List<WorkOrderAccountingCashierRelation> selectWorkOrderAccountingCashierRelationList(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation)
    {
        return workOrderAccountingCashierRelationMapper.selectWorkOrderAccountingCashierRelationList(workOrderAccountingCashierRelation);
    }

    /**
     * 新增工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    @Override
    public int insertWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation)
    {
        workOrderAccountingCashierRelation.setCreateTime(DateUtils.getNowDate());
        return workOrderAccountingCashierRelationMapper.insertWorkOrderAccountingCashierRelation(workOrderAccountingCashierRelation);
    }

    /**
     * 修改工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    @Override
    public int updateWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation)
    {
        workOrderAccountingCashierRelation.setUpdateTime(DateUtils.getNowDate());
        return workOrderAccountingCashierRelationMapper.updateWorkOrderAccountingCashierRelation(workOrderAccountingCashierRelation);
    }

    /**
     * 批量删除工单账务交付单关联
     * 
     * @param ids 需要删除的工单账务交付单关联主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderAccountingCashierRelationByIds(Long[] ids)
    {
        return workOrderAccountingCashierRelationMapper.deleteWorkOrderAccountingCashierRelationByIds(ids);
    }

    /**
     * 删除工单账务交付单关联信息
     * 
     * @param id 工单账务交付单关联主键
     * @return 结果
     */
    @Override
    public int deleteWorkOrderAccountingCashierRelationById(Long id)
    {
        return workOrderAccountingCashierRelationMapper.deleteWorkOrderAccountingCashierRelationById(id);
    }

    @Override
    @Transactional
    public void saveWorkOrderAccountingCashierRelation(Long workOrderId, Long customerServiceId, Integer periodStart, Integer periodEnd) {
        if (Objects.isNull(workOrderId) || Objects.isNull(customerServiceId) || Objects.isNull(periodStart) || Objects.isNull(periodEnd)) {
            return;
        }
        List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, customerServiceId)
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .le(CustomerServiceCashierAccounting::getPeriod, periodEnd)
                .ge(CustomerServiceCashierAccounting::getPeriod, periodStart));
        if (ObjectUtils.isEmpty(customerServiceCashierAccountings)) {
            return;
        }
        saveBatch(customerServiceCashierAccountings.stream().map(row -> new WorkOrderAccountingCashierRelation().setWorkOrderId(workOrderId)
                .setCashierAccountingId(row.getId())
                .setCashierAccountingDeliverStatus(row.getDeliverStatus())
                .setCashierAccountingDeliverResult(row.getDeliverResult())
                .setIsDel(false))
                .collect(Collectors.toList()));
    }

    @Override
    public List<WorkOrderAccountingCashierDTO> selectAccountingCashierList(WorkOrder workOrder) {
        if (Objects.isNull(workOrder)) {
            return Collections.emptyList();
        }
        if (Objects.isNull(workOrder.getCustomerServiceId())) {
            return Collections.emptyList();
        }
        List<CustomerServiceCashierAccounting> customerServiceCashierAccountings = customerServiceCashierAccountingMapper.selectList(new LambdaQueryWrapper<CustomerServiceCashierAccounting>()
                .eq(CustomerServiceCashierAccounting::getCustomerServiceId, workOrder.getCustomerServiceId())
                .eq(CustomerServiceCashierAccounting::getIsDel, false)
                .le(!Objects.isNull(workOrder.getPeriodEnd()), CustomerServiceCashierAccounting::getPeriod, workOrder.getPeriodEnd())
                .ge(!Objects.isNull(workOrder.getPeriodStart()), CustomerServiceCashierAccounting::getPeriod, workOrder.getPeriodStart())
                .orderByAsc(CustomerServiceCashierAccounting::getPeriod)
                .orderByAsc(CustomerServiceCashierAccounting::getType)
                .orderByAsc(CustomerServiceCashierAccounting::getId));
        if (ObjectUtils.isEmpty(customerServiceCashierAccountings)) {
            return Collections.emptyList();
        }
        Map<Long, WorkOrderAccountingCashierRelation> relationMap = list(new LambdaQueryWrapper<WorkOrderAccountingCashierRelation>()
                .eq(WorkOrderAccountingCashierRelation::getWorkOrderId, workOrder.getId())
                .eq(WorkOrderAccountingCashierRelation::getIsDel, false)).stream().collect(Collectors.toMap(WorkOrderAccountingCashierRelation::getCashierAccountingId, Function.identity()));
        return customerServiceCashierAccountings.stream().map(row -> {
            WorkOrderAccountingCashierRelation initialAccountingCashier = relationMap.get(row.getId());
            return WorkOrderAccountingCashierDTO.builder()
                    .accountingCashierId(row.getId())
                    .accountingCashierType(row.getType())
                    .title(row.getTitle())
                    .initialDeliverStatus(Objects.isNull(initialAccountingCashier) ? null : initialAccountingCashier.getCashierAccountingDeliverStatus())
                    .initialDeliverStatusStr(Objects.isNull(initialAccountingCashier) ? "-" : AccountingCashierDeliverStatus.getByCode(initialAccountingCashier.getCashierAccountingDeliverStatus()).getName())
                    .nowDeliverStatus(row.getDeliverStatus())
                    .nowDeliverStatusStr(AccountingCashierDeliverStatus.getByCode(row.getDeliverStatus()).getName())
                    .build();
        }).collect(Collectors.toList());
    }
}
