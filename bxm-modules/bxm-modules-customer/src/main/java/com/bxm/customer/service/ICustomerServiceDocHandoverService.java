package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.customer.domain.CustomerServiceDocHandover;
import com.bxm.customer.domain.dto.AccountingInfoSourceDTO;
import com.bxm.customer.domain.dto.AccountingTopInfoSourceDTO;
import com.bxm.customer.domain.dto.docHandover.*;
import com.bxm.customer.domain.dto.workBench.DocHandoverWorkBenchDTO;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.docHandover.*;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * 材料、资料交接Service接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface ICustomerServiceDocHandoverService extends IService<CustomerServiceDocHandover> {
    /**
     * 查询材料、资料交接
     *
     * @param id 材料、资料交接主键
     * @return 材料、资料交接
     */
    public CustomerServiceDocHandover selectCustomerServiceDocHandoverById(Long id);

    /**
     * 查询材料、资料交接列表
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 材料、资料交接集合
     */
    public List<CustomerServiceDocHandover> selectCustomerServiceDocHandoverList(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 新增材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    public int insertCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 修改材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    public int updateCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 批量删除材料、资料交接
     *
     * @param ids 需要删除的材料、资料交接主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverByIds(Long[] ids);

    /**
     * 删除材料、资料交接信息
     *
     * @param id 材料、资料交接主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverById(Long id);


    //****** start self method ******  批量操作: 删除、提交、核验、退回、承验转交，点击时打开对应弹框
    //分页获取材料交接列表
    IPage<DocHandoverDTO> docHandoverList(Long deptId, CustomerServiceDocHandoverVO vo);

    //获取凭票入账
    Integer getVoucherEntry(Long customerServicePeriodMonthId);

    //新增，基础信息
    Long addDocHandoverBase(Long deptId, AddDocHandoverBaseVO vo);

    //获取基础信息
    DocHandoverBaseDTO getDocHandoverBase(Long id);

    //编辑基础信息
    void updateDocHandoverBase(Long deptId, UpdateDocHandoverBaseVO vo);

    //获取交接材料相关信息，票据信息
    DocHandoverInstrumentDTO getDocHandoverInstrument(Long id);

    //校验可提交规则
    //false 校验不通过
    Boolean checkCanSubmit(DocHandoverInstrumentDTO vo);

    Boolean checkCanSubmitV2(DocHandoverInstrumentDTO vo);

    //当新增材料交接、编辑材料交接时，保存
    //isSubmit：这个接口逻辑，和 submitDocHandoverInstrument 逻辑很像，所以底层代码共用了，用 isSubmit 字段来区分具体的动作，然后在几个细节上做不同处理
    void saveDocHandoverInstrument(Long deptId, DocHandoverInstrumentDTO vo, Boolean isSubmit);

    //当新增材料交接、编辑材料交接时，提交
    void submitDocHandoverInstrument(Long deptId, DocHandoverInstrumentDTO vo);

    //获取材料交接详情
    DocHandoverFullDTO getDocHandoverFull(Long id);

    //获取客户服务某个账期月份的所有材料交接标题
    List<DocHandoverSimpleDTO> getDocHandoverOfPeriodByCustomerServiceId(Long customerServiceId, Integer period);

    //****** 列表上的操作按钮
    //提交
    void operateSubmit(Long deptId, Long id);

    //提交-批量
    Integer operateSubmitBatch(Long deptId, List<Long> ids);

    //核验
    void operateVerification(Long deptId, OperateVerificationVO vo);

    //核验-批量
    Integer operateVerificationBatch(Long deptId, OperateVerificationBatchVO vo);

    //退回
    void operateBack(Long deptId, OperateBackVO vo);

    //退回-批量
    Integer operateBackBatch(Long deptId, OperateBackBatchVO vo);

    //获取成员转交的信息-会计信息
    ChangeVerificationInfoDTO getChangeVerificationInfo(Long id);

    //承验转交
    void operateChangeVerification(Long deptId, Long id);

    //承验转交-批量
    Integer operateChangeVerificationBatch(Long deptId, List<Long> ids);

    //删除
    void operateDelete(Long deptId, Long id);

    //删除-批量
    Integer operateDeleteBatch(Long deptId, List<Long> ids);

    //****** 从其他地方来的交接单的处理API
    //根据addFromType获取交接材料相关标题
    List<DocHandoverSimpleDTO> getDocHandoverTitleByAddFromType(Integer addFromType, Long addFromId);

    //根据addFromType获取交接材料相关信息票据信息
    List<DocHandoverInstrumentDTO> getDocHandoverInstrumentByAddFromType(Integer addFromType, Long addFromId);

    //根据addFromType获取交接材料
    List<CustomerServiceDocHandover> getDocHandoverByAddFromType(Integer addFromType, Long addFromId);

    //根据addFromType删除材料交接单直接删除
    void operateDeleteByAddFromType(Long deptId, Integer addFromType, Long addFromId);

    //根据addFromType使材料交接单生效
    void operateEffectByAddFromType(Long deptId, Integer addFromType, Long addFromId, Integer isVoucherEntry);

    //根据addFromType使材料交接单生效
    //内部逻辑会捞一把刚才生成的账期，设置到材料交接单上
    void operateEffectByAddFromTypeV2(Long deptId, Integer addFromType, Long addFromId, Integer isVoucherEntry);

    //根据addFromType失效材料交接单
    void operateUnEffectByAddFromType(Long deptId, Integer addFromType, Long addFromId);

    //根据addFromType提交材料交接单
    void operateSubmitByAddFromType(Long deptId, Integer addFromType, Long addFromId);

    //****** 其他可共用的逻辑API
    //处理，标签搜索
    TagSearchVO tagSearch(Integer tagIncludeFlag, String tagName, TagBusinessType tagBusinessType);

    //处理，会计搜索
    CommonIdsSearchVO accountingSearch(String accountingEmployee);

    //获取会计信息
    Map<Long, List<AccountingInfoSourceDTO>> getAccountingInfoSource(List<Long> customerServicePeriodMonthIds);

    Map<Long, List<AccountingTopInfoSourceDTO>> getAccountingTopInfoSource(List<Long> customerServicePeriodMonthIds);

    //根据材料IDs批量获取完整度数据
    Map<Long, List<CustomerServiceDocHandover>> getMapByPeriodMonthIds(List<Long> customerServicePeriodMonthIds);

    //获取初始的银行票据信息
    List<DocHandoverInstrumentBankDTO> handleInitBankInstrument(Long customerServiceId, Integer period);

    DocHandoverWorkBenchDTO docHandoverStatistic(Long deptId);

    //获取操作信息，用于记录日志等
    OperateUserInfoDTO getOperateUserInfo(Long deptId, Long userId);

    CommonIdsSearchVO deptSearch(Long queryDeptId);

    //unHasBank历史数据处理
    void updateBankInstrumentUnHasCount();
}
