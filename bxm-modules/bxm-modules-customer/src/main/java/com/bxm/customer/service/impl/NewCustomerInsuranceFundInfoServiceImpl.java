package com.bxm.customer.service.impl;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.NewCustomerFinanceTaxInfo;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.NewCustomerInsuranceFundStatus;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceInfoDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceMonthDTO;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.INewCustomerInsuranceFundStatusService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.NewCustomerInsuranceFundInfoMapper;
import com.bxm.customer.domain.NewCustomerInsuranceFundInfo;
import com.bxm.customer.service.INewCustomerInsuranceFundInfoService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 新户流转五险一金信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerInsuranceFundInfoServiceImpl extends ServiceImpl<NewCustomerInsuranceFundInfoMapper, NewCustomerInsuranceFundInfo> implements INewCustomerInsuranceFundInfoService
{
    @Autowired
    private NewCustomerInsuranceFundInfoMapper newCustomerInsuranceFundInfoMapper;

    @Autowired
    private INewCustomerInsuranceFundStatusService newCustomerInsuranceFundStatusService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    /**
     * 查询新户流转五险一金信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 新户流转五险一金信息
     */
    @Override
    public NewCustomerInsuranceFundInfo selectNewCustomerInsuranceFundInfoById(Long id)
    {
        return newCustomerInsuranceFundInfoMapper.selectNewCustomerInsuranceFundInfoById(id);
    }

    /**
     * 查询新户流转五险一金信息列表
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 新户流转五险一金信息
     */
    @Override
    public List<NewCustomerInsuranceFundInfo> selectNewCustomerInsuranceFundInfoList(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo)
    {
        return newCustomerInsuranceFundInfoMapper.selectNewCustomerInsuranceFundInfoList(newCustomerInsuranceFundInfo);
    }

    /**
     * 新增新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    @Override
    public int insertNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo)
    {
        return newCustomerInsuranceFundInfoMapper.insertNewCustomerInsuranceFundInfo(newCustomerInsuranceFundInfo);
    }

    /**
     * 修改新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    @Override
    public int updateNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo)
    {
        return newCustomerInsuranceFundInfoMapper.updateNewCustomerInsuranceFundInfo(newCustomerInsuranceFundInfo);
    }

    /**
     * 批量删除新户流转五险一金信息
     * 
     * @param ids 需要删除的新户流转五险一金信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInsuranceFundInfoByIds(Long[] ids)
    {
        return newCustomerInsuranceFundInfoMapper.deleteNewCustomerInsuranceFundInfoByIds(ids);
    }

    /**
     * 删除新户流转五险一金信息信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInsuranceFundInfoById(Long id)
    {
        return newCustomerInsuranceFundInfoMapper.deleteNewCustomerInsuranceFundInfoById(id);
    }

    @Override
    public NewCustomerInsuranceFundInfo selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<NewCustomerInsuranceFundInfo>()
                .eq(NewCustomerInsuranceFundInfo::getCustomerId, customerId), false);
    }

    @Override
    public NewCustomerTransferInsuranceInfoDTO buildInsuranceInfo(NewCustomerInfo newCustomerInfo, List<TagDTO> tagList) {
        NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo = selectByCustomerId(newCustomerInfo.getId());
        List<Long> selectTagIds = ObjectUtils.isEmpty(tagList) ? Lists.newArrayList() :
                tagList.stream().filter(TagDTO::getIsSelected).map(TagDTO::getId).collect(Collectors.toList());
        boolean hasInsurance = Objects.nonNull(newCustomerInsuranceFundInfo);
        List<NewCustomerInsuranceFundStatus> insuranceFundStatusList = newCustomerInsuranceFundStatusService.selectByCustomerId(newCustomerInfo.getId());
        Map<Integer, Map<Integer, NewCustomerInsuranceFundStatus>> map = insuranceFundStatusList.stream().collect(
                Collectors.groupingBy(NewCustomerInsuranceFundStatus::getType,
                        Collectors.toMap(NewCustomerInsuranceFundStatus::getMonth, Function.identity(), (k1, k2) -> k1)));
        int registerDate = Integer.parseInt(newCustomerInfo.getRegistrationDate().replaceAll("-", "").substring(0, 4));
        int thisYearStart = Integer.parseInt(LocalDate.now().getYear() + "01");
        int thisYearEnd = Integer.parseInt(LocalDate.now().getYear() + "12");
        Integer startPeriod = Math.max(registerDate, thisYearStart);
        Integer endPeriod = DateUtils.getNowPeriod();
        List<Integer> eidtYearMonthList = getYearMonthListForEdit(startPeriod, thisYearEnd);
        List<Integer> detailYearMonthList = getYearMonthListForDetail(startPeriod, endPeriod);
        Map<Integer, NewCustomerInsuranceFundStatus> medicalMonthMap = map.get(1);
        Map<Integer, NewCustomerInsuranceFundStatus> socialMonthMap = map.get(2);
        Map<Integer, NewCustomerInsuranceFundStatus> fundMonthMap = map.get(3);
        return NewCustomerTransferInsuranceInfoDTO.builder()
                .newCustomerTransferId(newCustomerInfo.getId())
                .medicalSecurity(!selectTagIds.contains(specialTagProperties.getYibao()) ? null : NewCustomerTransferInsuranceDTO.builder()
                        .base(hasInsurance ? newCustomerInsuranceFundInfo.getMedicalBase() : null)
                        .peopleInfo(hasInsurance ? newCustomerInsuranceFundInfo.getMedicalContact() : null)
                        .peopleCount(hasInsurance ? newCustomerInsuranceFundInfo.getMedicalPeople() : null)
                        .rate(hasInsurance ? newCustomerInsuranceFundInfo.getMedicalRate() : null)
                        .totalContribution(hasInsurance ? newCustomerInsuranceFundInfo.getMedicalFee() : null)
                        .monthStatusForEdit(getYearMonthStatus(eidtYearMonthList, medicalMonthMap, endPeriod))
                        .monthStatusForDetail(getYearMonthStatus(detailYearMonthList, medicalMonthMap, endPeriod))
                        .build())
                .socialSecurity(!selectTagIds.contains(specialTagProperties.getShebao()) ? null : NewCustomerTransferInsuranceDTO.builder()
                        .base(hasInsurance ? newCustomerInsuranceFundInfo.getSocialSecurityBase() : null)
                        .peopleInfo(hasInsurance ? newCustomerInsuranceFundInfo.getSocialSecurityContact() : null)
                        .peopleCount(hasInsurance ? newCustomerInsuranceFundInfo.getSocialSecurityPeople() : null)
                        .injuryRate(hasInsurance ? newCustomerInsuranceFundInfo.getInjuryRate() : null)
                        .injuryFee(hasInsurance ? newCustomerInsuranceFundInfo.getInjuryFee() : null)
                        .totalContribution(hasInsurance ? newCustomerInsuranceFundInfo.getTotalContribution() : null)
                        .monthStatusForEdit(getYearMonthStatus(eidtYearMonthList, socialMonthMap, endPeriod))
                        .monthStatusForDetail(getYearMonthStatus(detailYearMonthList, socialMonthMap, endPeriod))
                        .build())
                .accumulationFund(!selectTagIds.contains(specialTagProperties.getGongjijin()) ? null : NewCustomerTransferInsuranceDTO.builder()
                        .base(hasInsurance ? newCustomerInsuranceFundInfo.getFundBase() : null)
                        .peopleInfo(hasInsurance ? newCustomerInsuranceFundInfo.getFundContact() : null)
                        .peopleCount(hasInsurance ? newCustomerInsuranceFundInfo.getFundPeople() : null)
                        .rate(hasInsurance ? newCustomerInsuranceFundInfo.getFundRate() : null)
                        .totalContribution(hasInsurance ? newCustomerInsuranceFundInfo.getFundFee() : null)
                        .monthStatusForEdit(getYearMonthStatus(eidtYearMonthList, fundMonthMap, endPeriod))
                        .monthStatusForDetail(getYearMonthStatus(detailYearMonthList, fundMonthMap, endPeriod))
                        .build())
                .build();
    }

    @Override
    @Transactional
    public void updateByInsuranceInfo(NewCustomerTransferInsuranceInfoDTO insuranceInfoDTO) {
        NewCustomerInsuranceFundInfo oldInsuranceInfo = selectByCustomerId(insuranceInfoDTO.getNewCustomerTransferId());
        if (Objects.isNull(oldInsuranceInfo)) {
            NewCustomerInsuranceFundInfo insuranceInfo = new NewCustomerInsuranceFundInfo();
            insuranceInfo.setCustomerId(insuranceInfoDTO.getNewCustomerTransferId());
            if (!Objects.isNull(insuranceInfoDTO.getMedicalSecurity())) {
                insuranceInfo.setMedicalBase(insuranceInfoDTO.getMedicalSecurity().getBase());
                insuranceInfo.setMedicalFee(insuranceInfoDTO.getMedicalSecurity().getTotalContribution());
                insuranceInfo.setMedicalContact(insuranceInfoDTO.getMedicalSecurity().getPeopleInfo());
                insuranceInfo.setMedicalPeople(insuranceInfoDTO.getMedicalSecurity().getPeopleCount());
                insuranceInfo.setMedicalRate(insuranceInfoDTO.getMedicalSecurity().getRate());
            }
            if (!Objects.isNull(insuranceInfoDTO.getSocialSecurity())) {
                insuranceInfo.setSocialSecurityBase(insuranceInfoDTO.getSocialSecurity().getBase());
                insuranceInfo.setTotalContribution(insuranceInfoDTO.getSocialSecurity().getTotalContribution());
                insuranceInfo.setSocialSecurityContact(insuranceInfoDTO.getSocialSecurity().getPeopleInfo());
                insuranceInfo.setSocialSecurityPeople(insuranceInfoDTO.getSocialSecurity().getPeopleCount());
                insuranceInfo.setInjuryFee(insuranceInfoDTO.getSocialSecurity().getInjuryFee());
                insuranceInfo.setInjuryRate(insuranceInfoDTO.getSocialSecurity().getInjuryRate());
            }
            if (!Objects.isNull(insuranceInfoDTO.getAccumulationFund())) {
                insuranceInfo.setFundBase(insuranceInfoDTO.getAccumulationFund().getBase());
                insuranceInfo.setFundFee(insuranceInfoDTO.getAccumulationFund().getTotalContribution());
                insuranceInfo.setFundContact(insuranceInfoDTO.getAccumulationFund().getPeopleInfo());
                insuranceInfo.setFundPeople(insuranceInfoDTO.getAccumulationFund().getPeopleCount());
                insuranceInfo.setFundRate(insuranceInfoDTO.getAccumulationFund().getRate());
            }
            save(insuranceInfo);
        } else {
            update(new LambdaUpdateWrapper<NewCustomerInsuranceFundInfo>()
                    .eq(NewCustomerInsuranceFundInfo::getId, oldInsuranceInfo.getId())
                    .set(NewCustomerInsuranceFundInfo::getMedicalPeople, Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) ? null : insuranceInfoDTO.getMedicalSecurity().getPeopleCount())
                    .set(NewCustomerInsuranceFundInfo::getMedicalContact, Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) ? null : insuranceInfoDTO.getMedicalSecurity().getPeopleInfo())
                    .set(NewCustomerInsuranceFundInfo::getMedicalRate, Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) ? null : insuranceInfoDTO.getMedicalSecurity().getRate())
                    .set(NewCustomerInsuranceFundInfo::getMedicalFee, Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) ? null : insuranceInfoDTO.getMedicalSecurity().getTotalContribution())
                    .set(NewCustomerInsuranceFundInfo::getMedicalBase, Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) ? null : insuranceInfoDTO.getMedicalSecurity().getBase())
                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityContact, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getPeopleInfo())
                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityBase, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getBase())
                    .set(NewCustomerInsuranceFundInfo::getSocialSecurityPeople, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getPeopleCount())
                    .set(NewCustomerInsuranceFundInfo::getTotalContribution, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getTotalContribution())
                    .set(NewCustomerInsuranceFundInfo::getInjuryRate, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getInjuryRate())
                    .set(NewCustomerInsuranceFundInfo::getInjuryFee, Objects.isNull(insuranceInfoDTO.getSocialSecurity()) ? null : insuranceInfoDTO.getSocialSecurity().getInjuryFee())
                    .set(NewCustomerInsuranceFundInfo::getFundPeople, Objects.isNull(insuranceInfoDTO.getAccumulationFund()) ? null : insuranceInfoDTO.getAccumulationFund().getPeopleCount())
                    .set(NewCustomerInsuranceFundInfo::getFundContact, Objects.isNull(insuranceInfoDTO.getAccumulationFund()) ? null : insuranceInfoDTO.getAccumulationFund().getPeopleInfo())
                    .set(NewCustomerInsuranceFundInfo::getFundRate, Objects.isNull(insuranceInfoDTO.getAccumulationFund()) ? null : insuranceInfoDTO.getAccumulationFund().getRate())
                    .set(NewCustomerInsuranceFundInfo::getFundFee, Objects.isNull(insuranceInfoDTO.getAccumulationFund()) ? null : insuranceInfoDTO.getAccumulationFund().getTotalContribution())
                    .set(NewCustomerInsuranceFundInfo::getFundBase, Objects.isNull(insuranceInfoDTO.getAccumulationFund()) ? null : insuranceInfoDTO.getAccumulationFund().getBase()));
        }
        newCustomerInsuranceFundStatusService.removeAndSaveNew(insuranceInfoDTO.getNewCustomerTransferId(), insuranceInfoDTO);
    }

    @Override
    public Map<Long, NewCustomerInsuranceFundInfo> selectMapByCustomerIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerInsuranceFundInfo>()
                .in(NewCustomerInsuranceFundInfo::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.toMap(NewCustomerInsuranceFundInfo::getCustomerId, Function.identity(), (k1, k2) -> k1));
    }

    private List<Integer> getYearMonthListForEdit(Integer startPeriod, Integer endPeriod) {
        List<Integer> result = Lists.newArrayList();
        for (int i = startPeriod; i <= endPeriod; i++) {
            result.add(i);
        }
        return result;
    }

    private List<Integer> getYearMonthListForDetail(Integer startPeriod, Integer nowPeriod) {
        List<Integer> result = Lists.newArrayList();
        for (int i = startPeriod; i <= nowPeriod; i++) {
            result.add(i);
        }
        return result;
    }

    private List<NewCustomerTransferInsuranceMonthDTO> getYearMonthStatus(List<Integer> yearMonthList, Map<Integer, NewCustomerInsuranceFundStatus> map, Integer nowPeriod) {
        return yearMonthList.stream().map(yearMonth -> {
            NewCustomerInsuranceFundStatus thisYearMonthStatus = ObjectUtils.isEmpty(map) ? null : map.get(yearMonth);
            return NewCustomerTransferInsuranceMonthDTO.builder()
                    .yearMonth(yearMonth)
                    .monthName(DateUtils.getYearMonthName(yearMonth))
                    .status(Objects.isNull(thisYearMonthStatus) ? null : thisYearMonthStatus.getStatus())
                    .statusStr(Objects.isNull(thisYearMonthStatus) ? null : thisYearMonthStatus.getStatusStr())
                    .enabled(yearMonth <= nowPeriod)
                    .build();
        }).collect(Collectors.toList());
    }
}
