package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.dto.RemoteSendQualityCheckingTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteQualityCheckingCreateVO;
import com.bxm.customer.api.domain.vo.RemoteSendQualityCheckingTaskVO;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.QualityCheckingResult;
import com.bxm.customer.domain.dto.TCommonOperateDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDetailDTO;
import com.bxm.customer.domain.vo.qualityChecking.*;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class QualityCheckingService {

    @Autowired
    private IQualityCheckingResultService qualityCheckingResultService;

    @Autowired
    private IQualityCheckingRecordService qualityCheckingRecordService;

    public IPage<QualityCheckingResultDTO> qualityCheckingResultPageList(QualityCheckingResultVO vo) {
        return qualityCheckingResultService.qualityCheckResultPageList(vo);
    }

    public List<CommonFileVO> buildQualityCheckingResultFiles(String exportTypes, List<QualityCheckingResultDTO> list) {
        if (StringUtils.isEmpty(exportTypes)) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(exportTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir;
            if (downloadFileType == 1) {
                baseDir = "质检附件";
            } else {
                baseDir = "未知";
            }
            for (QualityCheckingResultDTO resultDTO : list) {
                String dirPath = baseDir + "/" + resultDTO.getCustomerName() + "-" + resultDTO.getCreditCode() + "-" + resultDTO.getPeriod() + "/" + resultDTO.getQualityCheckingItemName();
                List<CommonFileVO> fileList = resultDTO.getFiles().stream().filter(f -> Objects.equals(f.getDeliverFileType(), downloadFileType)).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(fileList)) {
                    for (CommonFileVO file : fileList) {
                        file.setBaseDir(dirPath);
                    }
                    files.addAll(fileList);
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            StringUtils.dealFileNames(files);
        }
        return files;
    }

    public List<CommonFileVO> buildQualityCheckingRecordFiles(String exportTypes, List<QualityCheckingRecordDTO> list) {
        if (StringUtils.isEmpty(exportTypes)) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(exportTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir;
            if (downloadFileType == 1) {
                baseDir = "质检附件";
            } else {
                baseDir = "未知";
            }
            for (QualityCheckingRecordDTO resultDTO : list) {
                String dirPath = baseDir + "/" + resultDTO.getCustomerName() + "-" + resultDTO.getCreditCode() + "-" + resultDTO.getPeriod() + "/" + resultDTO.getQualityCheckingItemName();
                List<CommonFileVO> fileList = resultDTO.getFiles().stream().filter(f -> Objects.equals(f.getDeliverFileType(), downloadFileType)).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(fileList)) {
                    for (CommonFileVO file : fileList) {
                        file.setBaseDir(dirPath);
                    }
                    files.addAll(fileList);
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            StringUtils.dealFileNames(files);
        }
        return files;
    }

    public IPage<QualityCheckingRecordDTO> qualityCheckingRecordPageList(QualityCheckingRecordVO vo) {
        return qualityCheckingRecordService.qualityCheckingRecordPageList(vo);
    }

    public TCommonOperateDTO<QualityCheckingResult> modifyQualityResult(Long deptId, QualityCheckingResultModifyVO vo) {
        return qualityCheckingResultService.modifyQualityResult(deptId, vo);
    }

    public TCommonOperateDTO<QualityCheckingRecord> closeQualityRecord(Long deptId, QualityCheckingResultCloseVO vo) {
        return qualityCheckingRecordService.closeQualityRecord(deptId, vo);
    }

    public TCommonOperateDTO<QualityCheckingResult> checkQualityResult(Long deptId, QualityCheckingResultCheckVO vo) {
        return qualityCheckingResultService.checkQualityResult(deptId, vo);
    }

    public QualityCheckingResultDetailDTO qualityCheckingResultDetail(Long deptId, Long qualityCheckingResultId) {
        return qualityCheckingResultService.qualityCheckingResultDetail(deptId, qualityCheckingResultId);
    }

    public List<QualityCheckingRecord> getCheckingRecordList(List<Long> customerServicePeriodMonthIds) {
        return qualityCheckingRecordService.getCheckingRecordList(customerServicePeriodMonthIds);
    }

    public RemoteCustomerPeriodDTO remoteCreateQualityChecking(RemoteQualityCheckingCreateVO vo) {
        return qualityCheckingResultService.remoteCreateQualityCheckingV2(vo);
    }

    public void remoteSendQualityCheckingTask(RemoteSendQualityCheckingTaskVO vo) {
        qualityCheckingResultService.remoteSendQualityCheckingTask(vo);
    }
}
