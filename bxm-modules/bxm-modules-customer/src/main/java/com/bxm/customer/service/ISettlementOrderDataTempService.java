package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.SettlementOrderDataTemp;
import com.bxm.customer.domain.dto.settlementOrder.SettlementBusinessDeptPriceDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.dto.settlementOrder.task.BusinessPeriodListForTaskItemDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import com.bxm.system.api.domain.SysDept;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算单关联数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
public interface ISettlementOrderDataTempService extends IService<SettlementOrderDataTemp>
{

    List<SettlementBusinessDeptPriceDTO> getBusinessDeptPriceDataByBatchNo(String batchNo, List<SysDept> businessDeptList, String unit, BigDecimal price);

    List<BusinessPeriodListForTaskItemDTO> getResultByBatchNo(String batchNo);

    IPage<SettlementOrderDataDTO> settlementOrderDataListByBatchNo(SettlementOrderDataSearchVO vo);

    void deleteSettlementDataTemp(String jobParam);
}
