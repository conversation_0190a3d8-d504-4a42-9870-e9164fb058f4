package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;

/**
 * 增值员工upsert策略接口
 * 定义不同业务类型的upsert处理策略
 *
 * 策略直接处理 VO 对象，负责完整的业务流程：
 * 1. 验证业务特定字段
 * 2. 数据预处理和转换
 * 3. 查找现有记录
 * 4. 执行新增或更新操作
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ValueAddedEmployeeUpsertStrategy {

    /**
     * 获取策略支持的业务类型
     *
     * @return 业务类型代码
     */
    Integer getSupportedBizType();

    /**
     * 执行完整的 upsert 操作
     *
     * 包含完整的业务流程：
     * 1. 业务特定字段验证
     * 2. 数据预处理
     * 3. VO 转 Entity
     * 4. 查找现有记录
     * 5. 执行新增或更新
     *
     * @param employeeVO 员工信息VO对象
     * @return 员工ID，新增或更新成功后返回员工的主键ID
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当业务处理失败时抛出
     */
    Long upsert(ValueAddedEmployeeVO employeeVO);
}
