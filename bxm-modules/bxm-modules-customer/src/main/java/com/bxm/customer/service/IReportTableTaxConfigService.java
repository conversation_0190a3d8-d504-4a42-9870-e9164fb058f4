package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiSyncItem;
import com.bxm.customer.domain.ReportTableTaxConfig;
import com.bxm.thirdpart.api.domain.KouKuanDTO;
import com.bxm.thirdpart.api.domain.ShenBaoDTO;

/**
 * 申报税种对应Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface IReportTableTaxConfigService extends IService<ReportTableTaxConfig>
{
    /**
     * 查询申报税种对应
     * 
     * @param id 申报税种对应主键
     * @return 申报税种对应
     */
    public ReportTableTaxConfig selectReportTableTaxConfigById(Long id);

    /**
     * 查询申报税种对应列表
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 申报税种对应集合
     */
    public List<ReportTableTaxConfig> selectReportTableTaxConfigList(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 新增申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    public int insertReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 修改申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    public int updateReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 批量删除申报税种对应
     * 
     * @param ids 需要删除的申报税种对应主键集合
     * @return 结果
     */
    public int deleteReportTableTaxConfigByIds(Long[] ids);

    /**
     * 删除申报税种对应信息
     * 
     * @param id 申报税种对应主键
     * @return 结果
     */
    public int deleteReportTableTaxConfigById(Long id);

    List<OpenApiSyncItem> dealReportList(List<ShenBaoDTO> shenBaoList, List<OpenApiSyncItem> itemList);

    List<OpenApiSyncItem> dealDeductionList(List<KouKuanDTO> kouKuanList, List<OpenApiSyncItem> itemList);
}
