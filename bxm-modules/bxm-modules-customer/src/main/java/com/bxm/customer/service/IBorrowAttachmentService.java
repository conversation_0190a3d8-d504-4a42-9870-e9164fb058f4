package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.BorrowOrderFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.BorrowAttachment;

/**
 * 借阅附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
public interface IBorrowAttachmentService extends IService<BorrowAttachment>
{
    /**
     * 查询借阅附件
     * 
     * @param id 借阅附件主键
     * @return 借阅附件
     */
    public BorrowAttachment selectBorrowAttachmentById(Long id);

    /**
     * 查询借阅附件列表
     * 
     * @param borrowAttachment 借阅附件
     * @return 借阅附件集合
     */
    public List<BorrowAttachment> selectBorrowAttachmentList(BorrowAttachment borrowAttachment);

    /**
     * 新增借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    public int insertBorrowAttachment(BorrowAttachment borrowAttachment);

    /**
     * 修改借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    public int updateBorrowAttachment(BorrowAttachment borrowAttachment);

    /**
     * 批量删除借阅附件
     * 
     * @param ids 需要删除的借阅附件主键集合
     * @return 结果
     */
    public int deleteBorrowAttachmentByIds(Long[] ids);

    /**
     * 删除借阅附件信息
     * 
     * @param id 借阅附件主键
     * @return 结果
     */
    public int deleteBorrowAttachmentById(Long id);

    List<BorrowAttachment> selectByBorrowOrderIdAndFileType(Long borrowOrderId, BorrowOrderFileType fileType);

    void removeAndSaveFile(Long borrowOrderId, BorrowOrderFileType borrowOrderFileType, List<CommonFileVO> files);
}
