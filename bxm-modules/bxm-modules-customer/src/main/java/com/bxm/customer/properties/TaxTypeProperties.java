package com.bxm.customer.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "tax.type")
public class TaxTypeProperties
{

    public String getList() {
        return list;
    }

    public void setList(String list) {
        this.list = list;
    }

    private String list;
}
