package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.QualityCheckingFileMapper;
import com.bxm.customer.domain.QualityCheckingFile;
import com.bxm.customer.service.IQualityCheckingFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 质检附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class QualityCheckingFileServiceImpl extends ServiceImpl<QualityCheckingFileMapper, QualityCheckingFile> implements IQualityCheckingFileService
{
    @Autowired
    private QualityCheckingFileMapper qualityCheckingFileMapper;
    @Autowired
    private TransactionAutoConfiguration transactionAutoConfiguration;

    /**
     * 查询质检附件
     * 
     * @param id 质检附件主键
     * @return 质检附件
     */
    @Override
    public QualityCheckingFile selectQualityCheckingFileById(Long id)
    {
        return qualityCheckingFileMapper.selectQualityCheckingFileById(id);
    }

    /**
     * 查询质检附件列表
     * 
     * @param qualityCheckingFile 质检附件
     * @return 质检附件
     */
    @Override
    public List<QualityCheckingFile> selectQualityCheckingFileList(QualityCheckingFile qualityCheckingFile)
    {
        return qualityCheckingFileMapper.selectQualityCheckingFileList(qualityCheckingFile);
    }

    /**
     * 新增质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    @Override
    public int insertQualityCheckingFile(QualityCheckingFile qualityCheckingFile)
    {
        qualityCheckingFile.setCreateTime(DateUtils.getNowDate());
        return qualityCheckingFileMapper.insertQualityCheckingFile(qualityCheckingFile);
    }

    /**
     * 修改质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    @Override
    public int updateQualityCheckingFile(QualityCheckingFile qualityCheckingFile)
    {
        qualityCheckingFile.setUpdateTime(DateUtils.getNowDate());
        return qualityCheckingFileMapper.updateQualityCheckingFile(qualityCheckingFile);
    }

    /**
     * 批量删除质检附件
     * 
     * @param ids 需要删除的质检附件主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingFileByIds(Long[] ids)
    {
        return qualityCheckingFileMapper.deleteQualityCheckingFileByIds(ids);
    }

    /**
     * 删除质检附件信息
     * 
     * @param id 质检附件主键
     * @return 结果
     */
    @Override
    public int deleteQualityCheckingFileById(Long id)
    {
        return qualityCheckingFileMapper.deleteQualityCheckingFileById(id);
    }

    @Override
    public Map<Long, List<CommonFileVO>> getBatchByBusinessIdAndBusinessType(Integer businessType, List<Long> businessIds) {
        if (Objects.isNull(businessType) || ObjectUtils.isEmpty(businessIds)) {
            return Collections.emptyMap();
        }
        List<QualityCheckingFile> files = list(new LambdaQueryWrapper<QualityCheckingFile>()
                .eq(QualityCheckingFile::getBusinessType, businessType)
                .eq(QualityCheckingFile::getIsDel, false)
                .in(QualityCheckingFile::getBusinessId, businessIds));
        if (ObjectUtils.isEmpty(files)) {
            return Collections.emptyMap();
        }
        return files.stream()
                .collect(Collectors.groupingBy(
                        QualityCheckingFile::getBusinessId,
                        Collectors.mapping(file ->
                                        CommonFileVO.builder()
                                                .fileSize(file.getFileSize())
                                                .deliverFileType(file.getFileType())
                                                .fileUrl(file.getFileUrl())
                                                .fileName(file.getFileName())
                                                .build(),
                                Collectors.toList())
                ));
    }

    @Override
    public List<CommonFileVO> getByBusinessIdAndBusinessType(Integer businessType, Long businessId, Integer fileType) {
        if (Objects.isNull(businessType) || Objects.isNull(businessId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<QualityCheckingFile>()
                .eq(QualityCheckingFile::getIsDel, false)
                .eq(QualityCheckingFile::getBusinessType, businessType)
                .eq(QualityCheckingFile::getBusinessId, businessId)
                .eq(!Objects.isNull(fileType), QualityCheckingFile::getFileType, fileType))
                .stream().map(row ->
                        CommonFileVO.builder()
                                .fileUrl(row.getFileUrl())
                                .fileName(row.getFileName())
                                .fileSize(row.getFileSize())
                                .build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void removeAndSaveNewFiles(Integer businessType, Long businessId, List<CommonFileVO> files, Integer fileType) {
        if (Objects.isNull(businessType) || Objects.isNull(businessId) || ObjectUtils.isEmpty(fileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<QualityCheckingFile>()
                .set(QualityCheckingFile::getIsDel, true)
                .eq(QualityCheckingFile::getBusinessType, businessType)
                .eq(QualityCheckingFile::getBusinessId, businessId)
                .eq(QualityCheckingFile::getFileType, fileType));
        if (!ObjectUtils.isEmpty(files)) {
            saveBatch(files.stream().map(file -> new QualityCheckingFile().setBusinessId(businessId)
                    .setBusinessType(businessType)
                    .setFileType(fileType)
                    .setFileUrl(file.getFileUrl())
                    .setFileSize(file.getFileSize())
                    .setFileName(file.getFileName())
                    .setIsDel(false)).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional
    public void removeAndSaveNewFiles(Integer businessType, List<Long> businessIds, List<CommonFileVO> files, Integer fileType) {
        if (Objects.isNull(businessType) || ObjectUtils.isEmpty(files) || ObjectUtils.isEmpty(fileType)) {
            return;
        }
        update(new LambdaUpdateWrapper<QualityCheckingFile>()
                .set(QualityCheckingFile::getIsDel, true)
                .eq(QualityCheckingFile::getBusinessType, businessType)
                .in(QualityCheckingFile::getBusinessId, businessIds)
                .eq(QualityCheckingFile::getFileType, fileType));
        if (!ObjectUtils.isEmpty(files)) {
            List<QualityCheckingFile> qualityCheckingFiles = Lists.newArrayList();
            for (Long businessId : businessIds) {
                qualityCheckingFiles.addAll(files.stream().map(file -> new QualityCheckingFile().setBusinessId(businessId)
                        .setBusinessType(businessType)
                        .setFileType(fileType)
                        .setFileUrl(file.getFileUrl())
                        .setFileSize(file.getFileSize())
                        .setFileName(file.getFileName())
                        .setIsDel(false)).collect(Collectors.toList()));
            }
            saveBatch(qualityCheckingFiles);
        }
    }
}
