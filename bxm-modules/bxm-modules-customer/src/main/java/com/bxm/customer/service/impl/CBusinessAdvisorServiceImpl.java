package com.bxm.customer.service.impl;

import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.AdvisorBusinessType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.CBusinessAdvisor;
import com.bxm.customer.service.ICBusinessAdvisorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CBusinessAdvisorMapper;

/**
 * 顾问Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Service
public class CBusinessAdvisorServiceImpl extends ServiceImpl<CBusinessAdvisorMapper, CBusinessAdvisor> implements ICBusinessAdvisorService
{
    @Autowired
    private CBusinessAdvisorMapper cBusinessAdvisorMapper;

    /**
     * 查询顾问
     * 
     * @param id 顾问主键
     * @return 顾问
     */
    @Override
    public CBusinessAdvisor selectCBusinessAdvisorById(Long id)
    {
        return cBusinessAdvisorMapper.selectCBusinessAdvisorById(id);
    }

    /**
     * 查询顾问列表
     * 
     * @param cBusinessAdvisor 顾问
     * @return 顾问
     */
    @Override
    public List<CBusinessAdvisor> selectCBusinessAdvisorList(CBusinessAdvisor cBusinessAdvisor)
    {
        return cBusinessAdvisorMapper.selectCBusinessAdvisorList(cBusinessAdvisor);
    }

    /**
     * 新增顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    @Override
    public int insertCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor)
    {
        cBusinessAdvisor.setCreateTime(DateUtils.getNowDate());
        return cBusinessAdvisorMapper.insertCBusinessAdvisor(cBusinessAdvisor);
    }

    /**
     * 修改顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    @Override
    public int updateCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor)
    {
        cBusinessAdvisor.setUpdateTime(DateUtils.getNowDate());
        return cBusinessAdvisorMapper.updateCBusinessAdvisor(cBusinessAdvisor);
    }

    /**
     * 批量删除顾问
     * 
     * @param ids 需要删除的顾问主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessAdvisorByIds(Long[] ids)
    {
        return cBusinessAdvisorMapper.deleteCBusinessAdvisorByIds(ids);
    }

    /**
     * 删除顾问信息
     * 
     * @param id 顾问主键
     * @return 结果
     */
    @Override
    public int deleteCBusinessAdvisorById(Long id)
    {
        return cBusinessAdvisorMapper.deleteCBusinessAdvisorById(id);
    }

    @Override
    public Long getAdvisorByBusinessIdAndBusinessType(Long businessId, AdvisorBusinessType advisorBusinessType) {
        if (Objects.isNull(businessId)) {
            return null;
        }
        CBusinessAdvisor advisor = getOne(new LambdaQueryWrapper<CBusinessAdvisor>()
                .eq(CBusinessAdvisor::getBusinessId, businessId).eq(CBusinessAdvisor::getBusinessType, advisorBusinessType.getCode())
                .last("limit 1"));
        return Objects.isNull(advisor) ? null : advisor.getEmployeeId();
    }
}
