package com.bxm.customer.domain.vo.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountDocHandoverStatisticVO {

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("账期类型，1-小规模，2-一般纳税人，3-0申报")
    private Integer statisticTaxType;

    @ApiModelProperty("统计类型，1-完成率，2-未完成数")
    private Integer statisticType;

    @ApiModelProperty("选择的组织id")
    private Long queryDeptId;
}
