package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceDocHandoverTaxInstrument;

/**
 * 材料交接票据Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface ICustomerServiceDocHandoverTaxInstrumentService extends IService<CustomerServiceDocHandoverTaxInstrument>
{
    /**
     * 查询材料交接票据
     * 
     * @param id 材料交接票据主键
     * @return 材料交接票据
     */
    public CustomerServiceDocHandoverTaxInstrument selectCustomerServiceDocHandoverTaxInstrumentById(Long id);

    /**
     * 查询材料交接票据列表
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 材料交接票据集合
     */
    public List<CustomerServiceDocHandoverTaxInstrument> selectCustomerServiceDocHandoverTaxInstrumentList(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 新增材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    public int insertCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 修改材料交接票据
     * 
     * @param customerServiceDocHandoverTaxInstrument 材料交接票据
     * @return 结果
     */
    public int updateCustomerServiceDocHandoverTaxInstrument(CustomerServiceDocHandoverTaxInstrument customerServiceDocHandoverTaxInstrument);

    /**
     * 批量删除材料交接票据
     * 
     * @param ids 需要删除的材料交接票据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverTaxInstrumentByIds(Long[] ids);

    /**
     * 删除材料交接票据信息
     * 
     * @param id 材料交接票据主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverTaxInstrumentById(Long id);
}
