package com.bxm.customer.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.customer.domain.dto.AddPeriodMonthDTO;
import com.bxm.customer.domain.dto.CustomerServiceImportDTO;
import com.bxm.customer.service.ICCustomerServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/import")
@Api(tags = "导入数据服务")
@Slf4j
public class ImportController {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @PostMapping("/importCustomerService")
    @ApiOperation("导入客户服务")
    public Result<List<CustomerServiceImportDTO>> importCustomerService(MultipartFile file) {
        try {
            ExcelUtil<CustomerServiceImportDTO> util = new ExcelUtil<>(CustomerServiceImportDTO.class);
            List<CustomerServiceImportDTO> data = util.importExcel(file.getInputStream());
            return Result.ok(customerServiceService.importDatas(data));
        } catch (Exception e) {
            log.error("文件解析失败：{}", e.getMessage());
            return Result.fail("解析失败");
        }
    }

    @PostMapping("/importCustomerServiceTag")
    @ApiOperation("导入客户服务标签")
    public Result<List<CustomerServiceImportDTO>> importCustomerServiceTag(MultipartFile file) {
        try {
            ExcelUtil<CustomerServiceImportDTO> util = new ExcelUtil<>(CustomerServiceImportDTO.class);
            List<CustomerServiceImportDTO> data = util.importExcel(file.getInputStream());
            return Result.ok(customerServiceService.importCustomerServiceTag(data));
        } catch (Exception e) {
            log.error("文件解析失败：{}", e.getMessage());
            return Result.fail("解析失败");
        }
    }

    @PostMapping("/importCustomerServiceTaxCheck")
    @ApiOperation("导入客户服务税种")
    public Result<List<CustomerServiceImportDTO>> importCustomerServiceTaxCheck(MultipartFile file) {
        try {
            ExcelUtil<CustomerServiceImportDTO> util = new ExcelUtil<>(CustomerServiceImportDTO.class);
            List<CustomerServiceImportDTO> data = util.importExcel(file.getInputStream());
            return Result.ok(customerServiceService.importCustomerServiceTaxCheck(data));
        } catch (Exception e) {
            log.error("文件解析失败：{}", e.getMessage());
            return Result.fail("解析失败");
        }
    }

    @PostMapping("/importCustomerServiceSysAccount")
    @ApiOperation("导入客户服务系统账号")
    public Result<List<CustomerServiceImportDTO>> importCustomerServiceSysAccount(MultipartFile file) {
        try {
            ExcelUtil<CustomerServiceImportDTO> util = new ExcelUtil<>(CustomerServiceImportDTO.class);
            List<CustomerServiceImportDTO> data = util.importExcel(file.getInputStream());
            return Result.ok(customerServiceService.importCustomerServiceSysAccount(data));
        } catch (Exception e) {
            log.error("文件解析失败：{}", e.getMessage());
            return Result.fail("解析失败");
        }
    }

    @PostMapping("/importPeriodMonth")
    @ApiOperation("补账")
    public Result<List<AddPeriodMonthDTO>> importPeriodMonth(MultipartFile file) {
        try {
            ExcelUtil<AddPeriodMonthDTO> util = new ExcelUtil<>(AddPeriodMonthDTO.class);
            List<AddPeriodMonthDTO> data = util.importExcel(file.getInputStream());
            return Result.ok(customerServiceService.importPeriodMonth(data));
        } catch (Exception e) {
            log.error("文件解析失败：{}", e.getMessage());
            return Result.fail("解析失败");
        }
    }
}
