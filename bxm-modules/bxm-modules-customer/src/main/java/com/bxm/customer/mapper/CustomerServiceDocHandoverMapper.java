package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.CustomerServiceDocHandover;
import com.bxm.customer.domain.dto.docHandover.DocHandoverDTO;
import com.bxm.customer.domain.vo.CommonIdsSearchVO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.docHandover.CustomerServiceDocHandoverVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 材料、资料交接Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Mapper
public interface CustomerServiceDocHandoverMapper extends BaseMapper<CustomerServiceDocHandover> {
    /**
     * 查询材料、资料交接
     *
     * @param id 材料、资料交接主键
     * @return 材料、资料交接
     */
    public CustomerServiceDocHandover selectCustomerServiceDocHandoverById(Long id);

    /**
     * 查询材料、资料交接列表
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 材料、资料交接集合
     */
    public List<CustomerServiceDocHandover> selectCustomerServiceDocHandoverList(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 新增材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    public int insertCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 修改材料、资料交接
     *
     * @param customerServiceDocHandover 材料、资料交接
     * @return 结果
     */
    public int updateCustomerServiceDocHandover(CustomerServiceDocHandover customerServiceDocHandover);

    /**
     * 删除材料、资料交接
     *
     * @param id 材料、资料交接主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverById(Long id);

    /**
     * 批量删除材料、资料交接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverByIds(Long[] ids);

    List<DocHandoverDTO> selectDocHandoverList(
            IPage<DocHandoverDTO> result,
            @Param("vo") CustomerServiceDocHandoverVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("commonIdsSearchVO") CommonIdsSearchVO commonIdsSearchVO
    );

    List<DocHandoverDTO> selectDocHandoverListV2(
            IPage<DocHandoverDTO> result,
            @Param("vo") CustomerServiceDocHandoverVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("commonIdsSearchVO") CommonIdsSearchVO commonIdsSearchVO,
            @Param("userDept") UserDeptDTO userDept
    );

    @Deprecated
    void insertCustomerServiceDocHandoverWithBatchNum(CustomerServiceDocHandover customerServiceDocHandover);

    List<Long> searchWholeLevelTotal(@Param("wholeLevel") Integer wholeLevel);

    /*
     * 账期材料完整度，是根据账期下关联的多个材料交接单逻辑计算的，逻辑和状态展示顺序如下：
     *
     * 未提交材料：无材料交接单
     * 待核验：有至少一条交接单是待核验
     * 无材料：所有交接单都是已核验，且都是无材料
     * 有缺失：所有交接单都是已核验，且有一条是有缺失
     * 缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
     * 已完整：所有交接单都是已核验，且所有交接单都是已完整
     *
     * 材料缺失：1-已完整、2-缺但齐、3-有缺失，  -2是未提交材料  4是待核验  5是无材料
     */
    List<Long> searchWholeLevelTotalV2(@Param("wholeLevel") Integer wholeLevel);

    @Select("select max(batch_num) as maxId from c_customer_service_doc_handover where is_del = 0 and customer_service_id = #{customerServiceId} and period = #{period}")
    @ResultType(Integer.class)
    Integer selectMaxBatchNum(@Param("customerServiceId") Long customerServiceId, @Param("period") Integer period);

    List<CustomerServiceDocHandover> selectDocHandoverStatisticByYearAndUserDept(@Param("year") Integer year,
                                                                                 @Param("statisticTaxType") Integer statisticTaxType,
                                                                                 @Param("userDeptDTO") UserDeptDTO userDeptDTO, @Param("queryDeptIds") List<Long> queryDeptIds);

    @Select("select id" +
            " from c_customer_service_period_month" +
            " where id not in (select distinct customer_service_period_month_id from c_customer_service_doc_handover where is_del = 0 and is_effect = 1)")
    @ResultType(Long.class)
    List<Long> searchWholeLevel_UN_SUBMIT_DATA();

    @Select("select distinct c_customer_service_period_month.id" +
            " from c_customer_service_period_month" +
            "         left join c_customer_service_doc_handover as d on d.customer_service_period_month_id = c_customer_service_period_month.id" +
            " where d.is_del = 0" +
            "  and d.is_effect = 1" +
            "  and d.status = 3")
    @ResultType(Long.class)
    List<Long> searchWholeLevel_WAIT_CHECK();
}
