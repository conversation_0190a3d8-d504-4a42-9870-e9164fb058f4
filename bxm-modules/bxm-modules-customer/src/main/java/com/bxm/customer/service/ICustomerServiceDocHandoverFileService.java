package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.enums.docHandover.DocHandoverFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceDocHandoverFile;

import java.util.List;
import java.util.Map;

/**
 * 材料、资料 附件Service接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface ICustomerServiceDocHandoverFileService extends IService<CustomerServiceDocHandoverFile> {
    /**
     * 查询材料、资料 附件
     *
     * @param id 材料、资料 附件主键
     * @return 材料、资料 附件
     */
    public CustomerServiceDocHandoverFile selectCustomerServiceDocHandoverFileById(Long id);

    /**
     * 查询材料、资料 附件列表
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 材料、资料 附件集合
     */
    public List<CustomerServiceDocHandoverFile> selectCustomerServiceDocHandoverFileList(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 新增材料、资料 附件
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    public int insertCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 修改材料、资料 附件
     *
     * @param customerServiceDocHandoverFile 材料、资料 附件
     * @return 结果
     */
    public int updateCustomerServiceDocHandoverFile(CustomerServiceDocHandoverFile customerServiceDocHandoverFile);

    /**
     * 批量删除材料、资料 附件
     *
     * @param ids 需要删除的材料、资料 附件主键集合
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverFileByIds(Long[] ids);

    /**
     * 删除材料、资料 附件信息
     *
     * @param id 材料、资料 附件主键
     * @return 结果
     */
    public int deleteCustomerServiceDocHandoverFileById(Long id);

    //****** start self method ******

    //根据 材料ID 获取附件
    List<CustomerServiceDocHandoverFile> selectByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes);

    //根据 材料ID 获取附件，并根据类型转成MAP
    Map<Integer, List<CustomerServiceDocHandoverFile>> selectMapByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes);

    //根据 材料ID 获取附件，MAP，key是主类型+子类型
    Map<String, List<CustomerServiceDocHandoverFile>> selectMapSubKeyByDocHandover(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes);

    //根据 材料ID 删除附件
    void deleteByDocHandoverId(Long customerServiceDocHandoverId, List<DocHandoverFileType> docHandoverFileTypes);

    //保存附件
    void saveFile(Long customerServiceDocId, List<CommonFileVO> files, DocHandoverFileType docHandoverFileType, String subFileType);

    //原始附件处理成前端可用数据
    List<CommonFileVO> covToCommonFileVO(List<CustomerServiceDocHandoverFile> files);
}
