package com.bxm.customer.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.NewCustomerInsuranceFundInfo;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceInfoDTO;

/**
 * 新户流转五险一金信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerInsuranceFundInfoService extends IService<NewCustomerInsuranceFundInfo>
{
    /**
     * 查询新户流转五险一金信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 新户流转五险一金信息
     */
    public NewCustomerInsuranceFundInfo selectNewCustomerInsuranceFundInfoById(Long id);

    /**
     * 查询新户流转五险一金信息列表
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 新户流转五险一金信息集合
     */
    public List<NewCustomerInsuranceFundInfo> selectNewCustomerInsuranceFundInfoList(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 新增新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    public int insertNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 修改新户流转五险一金信息
     * 
     * @param newCustomerInsuranceFundInfo 新户流转五险一金信息
     * @return 结果
     */
    public int updateNewCustomerInsuranceFundInfo(NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo);

    /**
     * 批量删除新户流转五险一金信息
     * 
     * @param ids 需要删除的新户流转五险一金信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundInfoByIds(Long[] ids);

    /**
     * 删除新户流转五险一金信息信息
     * 
     * @param id 新户流转五险一金信息主键
     * @return 结果
     */
    public int deleteNewCustomerInsuranceFundInfoById(Long id);

    NewCustomerInsuranceFundInfo selectByCustomerId(Long customerId);

    NewCustomerTransferInsuranceInfoDTO buildInsuranceInfo(NewCustomerInfo newCustomerInfo, List<TagDTO> tagList);

    void updateByInsuranceInfo(NewCustomerTransferInsuranceInfoDTO insuranceInfo);

    Map<Long, NewCustomerInsuranceFundInfo> selectMapByCustomerIds(List<Long> newCustomerTransferIds);
}
