package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceIncomeInfoMapper;
import com.bxm.customer.domain.CustomerServiceIncomeInfo;
import com.bxm.customer.service.ICustomerServiceIncomeInfoService;

/**
 * 客户服务收入信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceIncomeInfoServiceImpl extends ServiceImpl<CustomerServiceIncomeInfoMapper, CustomerServiceIncomeInfo> implements ICustomerServiceIncomeInfoService
{
    @Autowired
    private CustomerServiceIncomeInfoMapper customerServiceIncomeInfoMapper;

    /**
     * 查询客户服务收入信息
     * 
     * @param id 客户服务收入信息主键
     * @return 客户服务收入信息
     */
    @Override
    public CustomerServiceIncomeInfo selectCustomerServiceIncomeInfoById(Long id)
    {
        return customerServiceIncomeInfoMapper.selectCustomerServiceIncomeInfoById(id);
    }

    /**
     * 查询客户服务收入信息列表
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 客户服务收入信息
     */
    @Override
    public List<CustomerServiceIncomeInfo> selectCustomerServiceIncomeInfoList(CustomerServiceIncomeInfo customerServiceIncomeInfo)
    {
        return customerServiceIncomeInfoMapper.selectCustomerServiceIncomeInfoList(customerServiceIncomeInfo);
    }

    /**
     * 新增客户服务收入信息
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 结果
     */
    @Override
    public int insertCustomerServiceIncomeInfo(CustomerServiceIncomeInfo customerServiceIncomeInfo)
    {
        return customerServiceIncomeInfoMapper.insertCustomerServiceIncomeInfo(customerServiceIncomeInfo);
    }

    /**
     * 修改客户服务收入信息
     * 
     * @param customerServiceIncomeInfo 客户服务收入信息
     * @return 结果
     */
    @Override
    public int updateCustomerServiceIncomeInfo(CustomerServiceIncomeInfo customerServiceIncomeInfo)
    {
        return customerServiceIncomeInfoMapper.updateCustomerServiceIncomeInfo(customerServiceIncomeInfo);
    }

    /**
     * 批量删除客户服务收入信息
     * 
     * @param ids 需要删除的客户服务收入信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceIncomeInfoByIds(Long[] ids)
    {
        return customerServiceIncomeInfoMapper.deleteCustomerServiceIncomeInfoByIds(ids);
    }

    /**
     * 删除客户服务收入信息信息
     * 
     * @param id 客户服务收入信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceIncomeInfoById(Long id)
    {
        return customerServiceIncomeInfoMapper.deleteCustomerServiceIncomeInfoById(id);
    }
}
