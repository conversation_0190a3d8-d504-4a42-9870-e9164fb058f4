package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.repairAccount.RepairAccountFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerServiceRepairAccountFile;
import com.bxm.customer.mapper.CustomerServiceRepairAccountFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.ICustomerServiceRepairAccountFileService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 补账 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@Service
public class CustomerServiceRepairAccountFileServiceImpl extends ServiceImpl<CustomerServiceRepairAccountFileMapper, CustomerServiceRepairAccountFile> implements ICustomerServiceRepairAccountFileService {
    @Autowired
    private CustomerServiceRepairAccountFileMapper customerServiceRepairAccountFileMapper;

    @Autowired
    private FileService fileService;

    /**
     * 查询补账 附件
     *
     * @param id 补账 附件主键
     * @return 补账 附件
     */
    @Override
    public CustomerServiceRepairAccountFile selectCustomerServiceRepairAccountFileById(Long id) {
        return customerServiceRepairAccountFileMapper.selectCustomerServiceRepairAccountFileById(id);
    }

    /**
     * 查询补账 附件列表
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 补账 附件
     */
    @Override
    public List<CustomerServiceRepairAccountFile> selectCustomerServiceRepairAccountFileList(CustomerServiceRepairAccountFile customerServiceRepairAccountFile) {
        return customerServiceRepairAccountFileMapper.selectCustomerServiceRepairAccountFileList(customerServiceRepairAccountFile);
    }

    /**
     * 新增补账 附件
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    @Override
    public int insertCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile) {
        customerServiceRepairAccountFile.setCreateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountFileMapper.insertCustomerServiceRepairAccountFile(customerServiceRepairAccountFile);
    }

    /**
     * 修改补账 附件
     *
     * @param customerServiceRepairAccountFile 补账 附件
     * @return 结果
     */
    @Override
    public int updateCustomerServiceRepairAccountFile(CustomerServiceRepairAccountFile customerServiceRepairAccountFile) {
        customerServiceRepairAccountFile.setUpdateTime(DateUtils.getNowDate());
        return customerServiceRepairAccountFileMapper.updateCustomerServiceRepairAccountFile(customerServiceRepairAccountFile);
    }

    /**
     * 批量删除补账 附件
     *
     * @param ids 需要删除的补账 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountFileByIds(Long[] ids) {
        return customerServiceRepairAccountFileMapper.deleteCustomerServiceRepairAccountFileByIds(ids);
    }

    /**
     * 删除补账 附件信息
     *
     * @param id 补账 附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceRepairAccountFileById(Long id) {
        return customerServiceRepairAccountFileMapper.deleteCustomerServiceRepairAccountFileById(id);
    }

    @Override
    public List<CustomerServiceRepairAccountFile> selectByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes) {
        List<CustomerServiceRepairAccountFile> result = Collections.emptyList();
        if (customerServiceRepairAccountId == null) {
            return result;
        }

        if (ObjectUtils.isEmpty(repairAccountFileTypes)) {
            result = list(new LambdaQueryWrapper<CustomerServiceRepairAccountFile>()
                    .eq(CustomerServiceRepairAccountFile::getCustomerServiceRepairAccountId, customerServiceRepairAccountId)
            );
        } else {
            result = list(new LambdaQueryWrapper<CustomerServiceRepairAccountFile>()
                    .eq(CustomerServiceRepairAccountFile::getCustomerServiceRepairAccountId, customerServiceRepairAccountId)
                    .in(CustomerServiceRepairAccountFile::getFileType, repairAccountFileTypes.stream().map(RepairAccountFileType::getCode).distinct().collect(Collectors.toList()))
            );
        }

        return result;
    }

    @Override
    public Map<Integer, List<CustomerServiceRepairAccountFile>> selectMapByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes) {
        List<CustomerServiceRepairAccountFile> files = selectByRepairAccount(customerServiceRepairAccountId, repairAccountFileTypes);
        return files.stream().collect(Collectors.groupingBy(CustomerServiceRepairAccountFile::getFileType));
    }

    @Override
    public Map<String, List<CustomerServiceRepairAccountFile>> selectMapSubKeyByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes) {
        List<CustomerServiceRepairAccountFile> files = selectByRepairAccount(customerServiceRepairAccountId, repairAccountFileTypes);
        return files.stream().collect(Collectors.groupingBy(row -> key(row.getFileType(), row.getSubFileType())));
    }

    @Override
    public void deleteByRepairAccount(Long customerServiceRepairAccountId, List<RepairAccountFileType> repairAccountFileTypes) {
        if (customerServiceRepairAccountId != null) {
            if (ObjectUtils.isEmpty(repairAccountFileTypes)) {
                remove(new LambdaQueryWrapper<CustomerServiceRepairAccountFile>()
                        .eq(CustomerServiceRepairAccountFile::getCustomerServiceRepairAccountId, customerServiceRepairAccountId));
            } else {
                remove(new LambdaQueryWrapper<CustomerServiceRepairAccountFile>()
                        .eq(CustomerServiceRepairAccountFile::getCustomerServiceRepairAccountId, customerServiceRepairAccountId)
                        .in(CustomerServiceRepairAccountFile::getFileType, repairAccountFileTypes.stream().map(RepairAccountFileType::getCode).distinct().collect(Collectors.toList()))
                );
            }
        }
    }

    @Override
    public void saveFile(Long customerServiceRepairAccountId, List<CommonFileVO> files, RepairAccountFileType repairAccountFileType, String subFileType) {
        if (!ObjectUtils.isEmpty(files)) {
            Integer fileType = repairAccountFileType.getCode();
            saveBatch(
                    files.stream().map(f -> new CustomerServiceRepairAccountFile()
                            .setCustomerServiceRepairAccountId(customerServiceRepairAccountId)
                            .setFileName(f.getFileName())
                            .setFileType(fileType)
                            .setSubFileType(subFileType)
                            .setFileUrl(f.getFileUrl()))
                            .collect(Collectors.toList())
            );
        }
    }

    @Override
    public List<CommonFileVO> covToCommonFileVO(List<CustomerServiceRepairAccountFile> files) {
        return ObjectUtils.isEmpty(files) ? Lists.newArrayList()
                : files.stream()
                .map(f -> CommonFileVO.builder()
                        .fileName(f.getFileName())
                        .fileUrl(f.getFileUrl())
                        .fullFileUrl(fileService.getFullFileUrl(f.getFileUrl()))
                        .build())
                .collect(Collectors.toList());
    }

    public static String key(Integer fileType, String subFileType) {
        return fileType + "_" + subFileType;
    }
}
