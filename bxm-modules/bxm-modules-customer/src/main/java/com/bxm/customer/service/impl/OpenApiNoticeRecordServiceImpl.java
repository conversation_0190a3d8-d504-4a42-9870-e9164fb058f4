package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.enums.RpaTaskType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.OpenApiCheckFileRecord;
import com.bxm.customer.domain.OpenApiNoticeRecord;
import com.bxm.customer.domain.dto.OpenApiRecordDTO;
import com.bxm.customer.domain.dto.rpa.RpaDTO;
import com.bxm.customer.domain.vo.OpenApiRecordSearchVO;
import com.bxm.customer.domain.vo.rpa.RpaSearchVO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.OpenApiNoticeRecordMapper;
import com.bxm.customer.service.IOpenApiNoticeRecordService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.CheckFilesVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 第三方通知/被通知记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
@Service
public class OpenApiNoticeRecordServiceImpl extends ServiceImpl<OpenApiNoticeRecordMapper, OpenApiNoticeRecord> implements IOpenApiNoticeRecordService
{
    @Autowired
    private OpenApiNoticeRecordMapper openApiNoticeRecordMapper;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private RemoteUserService remoteUserService;

    /**
     * 查询第三方通知/被通知记录
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 第三方通知/被通知记录
     */
    @Override
    public OpenApiNoticeRecord selectOpenApiNoticeRecordById(Long id)
    {
        return openApiNoticeRecordMapper.selectOpenApiNoticeRecordById(id);
    }

    /**
     * 查询第三方通知/被通知记录列表
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 第三方通知/被通知记录
     */
    @Override
    public List<OpenApiNoticeRecord> selectOpenApiNoticeRecordList(OpenApiNoticeRecord openApiNoticeRecord)
    {
        return openApiNoticeRecordMapper.selectOpenApiNoticeRecordList(openApiNoticeRecord);
    }

    /**
     * 新增第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    @Override
    public int insertOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord)
    {
        openApiNoticeRecord.setCreateTime(DateUtils.getNowDate());
        return openApiNoticeRecordMapper.insertOpenApiNoticeRecord(openApiNoticeRecord);
    }

    /**
     * 修改第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    @Override
    public int updateOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord)
    {
        openApiNoticeRecord.setUpdateTime(DateUtils.getNowDate());
        return openApiNoticeRecordMapper.updateOpenApiNoticeRecord(openApiNoticeRecord);
    }

    /**
     * 批量删除第三方通知/被通知记录
     * 
     * @param ids 需要删除的第三方通知/被通知记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiNoticeRecordByIds(Long[] ids)
    {
        return openApiNoticeRecordMapper.deleteOpenApiNoticeRecordByIds(ids);
    }

    /**
     * 删除第三方通知/被通知记录信息
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiNoticeRecordById(Long id)
    {
        return openApiNoticeRecordMapper.deleteOpenApiNoticeRecordById(id);
    }

    @Override
    public IPage<OpenApiRecordDTO> openapiRecordList(OpenApiRecordSearchVO vo) {
        IPage<OpenApiRecordDTO> result = new Page<>();
        IPage<OpenApiNoticeRecord> iPage = page(new Page<>(vo.getPageNum(), vo.getPageSize()), new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .like(!StringUtils.isEmpty(vo.getNoticeSource()), OpenApiNoticeRecord::getNoticeSource, vo.getNoticeSource())
                .like(!StringUtils.isEmpty(vo.getNoticeTarget()), OpenApiNoticeRecord::getNoticeTarget, vo.getNoticeTarget())
                .like(!StringUtils.isEmpty(vo.getNoticeFunction()), OpenApiNoticeRecord::getNoticeFunction, vo.getNoticeFunction())
                .le(!StringUtils.isEmpty(vo.getCreateTimeEnd()), OpenApiNoticeRecord::getCreateTime, vo.getCreateTimeEnd())
                .ge(!StringUtils.isEmpty(vo.getCreateTimeStart()), OpenApiNoticeRecord::getCreateTime, vo.getCreateTimeStart())
                .eq(OpenApiNoticeRecord::getIsDel, false)
                .like(!StringUtils.isEmpty(vo.getNoticeContent()), OpenApiNoticeRecord::getNoticeContent, vo.getNoticeContent())
                .orderByDesc(OpenApiNoticeRecord::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            result.setRecords(iPage.getRecords().stream().map(row -> {
                OpenApiRecordDTO dto = new OpenApiRecordDTO();
                BeanUtils.copyProperties(row, dto);
                return dto;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public void updateRpaDealResultAndSysDealResult(String uuid, Integer rpaDealResult, Boolean dealResult, String failReason) {
        OpenApiNoticeRecord noticeRecord = getByUuid(uuid);
        if (Objects.isNull(noticeRecord)) {
            return;
        }
        updateById(new OpenApiNoticeRecord().setId(noticeRecord.getId())
                .setRpaDealResult(rpaDealResult)
                .setSysDeliverResult(dealResult ? 1 : 2)
                .setSysDeliverFailReason(!StringUtils.isEmpty(failReason) && failReason.length() > 500 ? failReason.substring(0, 500) : failReason)
                .setDeliverTime(LocalDateTime.now()));
    }

    @Override
    public OpenApiNoticeRecord getByUuid(String uuid) {
        if (StringUtils.isEmpty(uuid)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .eq(OpenApiNoticeRecord::getUuid, uuid)
                .eq(OpenApiNoticeRecord::getIsDel, false));
    }

    @Override
    public void updateSysDealFailByUuid(String uuid, String failReason) {
        OpenApiNoticeRecord noticeRecord = getByUuid(uuid);
        if (Objects.isNull(noticeRecord)) {
            return;
        }
        updateById(new OpenApiNoticeRecord().setId(noticeRecord.getId())
                .setSysDeliverResult(2)
                .setSysDeliverFailReason(!StringUtils.isEmpty(failReason) && failReason.length() > 500 ? failReason.substring(0, 500) : failReason)
                .setDeliverTime(LocalDateTime.now()));
    }

    @Override
    public void updateSysDealSuccessByUuid(String uuid) {
        OpenApiNoticeRecord noticeRecord = getByUuid(uuid);
        if (Objects.isNull(noticeRecord)) {
            return;
        }
        updateById(new OpenApiNoticeRecord().setId(noticeRecord.getId())
                .setSysDeliverResult(1)
                .setDeliverTime(LocalDateTime.now()));
    }

    @Override
    @Transactional
    public void rpaTaskStatusSearchTask() {
        LocalDateTime searchTime = LocalDateTime.now().minusHours(2);
        // 两小时还未收到回调则主动查询
        List<OpenApiNoticeRecord> list = list(new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .eq(OpenApiNoticeRecord::getIsDel, false)
                .eq(OpenApiNoticeRecord::getIsRpa, true)
                .eq(OpenApiNoticeRecord::getNoticeCondition, true)
                .eq(OpenApiNoticeRecord::getRpaDealResult, 0)
                .isNull(OpenApiNoticeRecord::getSysDeliverResult)
                .isNotNull(OpenApiNoticeRecord::getUuid)
                .ne(OpenApiNoticeRecord::getUuid, "")
                .le(OpenApiNoticeRecord::getCreateTime, searchTime));
        if (!ObjectUtils.isEmpty(list)) {
            List<OpenApiNoticeRecord> updates = Lists.newArrayList();
            list.forEach(row -> {
                String taskStatus = remoteThirdpartService.searchTaskStatus(row.getUuid(), SecurityConstants.INNER).getDataThrowException(false);
                if (!StringUtils.isEmpty(taskStatus)) {
                    OpenApiNoticeRecord update = new OpenApiNoticeRecord().setId(row.getId());
                    if ("task_callback_success".equals(taskStatus)) {
                        update.setRpaDealResult(1);
                    } else if ("task_not_exit".equals(taskStatus)) {
                        update.setRpaDealResult(2);
                    } else if ("task_callback_fail".equals(taskStatus)) {
                        update.setRpaDealResult(2);
                    }
                    updates.add(update);
                }
            });
            if (!ObjectUtils.isEmpty(updates)) {
                updateBatchById(updates);
            }
        }
    }

    @Override
    public void rePushTask(String jobParam) {
        if (StringUtils.isEmpty(jobParam)) {
            return;
        }
        List<String> uuidList = Arrays.asList(jobParam.split(","));
        List<OpenApiNoticeRecord> recordList = list(new LambdaQueryWrapper<OpenApiNoticeRecord>()
                .in(OpenApiNoticeRecord::getUuid, uuidList)
                .isNotNull(OpenApiNoticeRecord::getNoticeContent)
                .ne(OpenApiNoticeRecord::getNoticeContent, ""));
        if (ObjectUtils.isEmpty(recordList)) {
            return;
        }
        ExecutorService executorService = Executors.newFixedThreadPool(5); // 创建一个线程池
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        recordList.forEach(row -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> remoteThirdpartService.rePush(row.getNoticeContent(), SecurityConstants.INNER), executorService);

            futures.add(future);
        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();
    }

    @Override
    public IPage<RpaDTO> rpaList(RpaSearchVO vo) {
        IPage<RpaDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        List<Long> customerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            customerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(customerServiceIds)) {
                return result;
            }
        }
        UserDeptDTO userDeptDTO = null;
        if (vo.getTabType() != 1) {
            userDeptDTO = remoteDeptService.userDeptList(vo.getUserId(), vo.getDeptId()).getDataThrowException();
            if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                return result;
            }
        }
        Integer totalCount = openApiNoticeRecordMapper.selectRpaListCount(vo, customerServiceIds, userDeptDTO, vo.getUserId());
        if (totalCount == 0) {
            return result;
        }
        result.setTotal(totalCount);
        Integer start = (vo.getPageNum() - 1) * vo.getPageSize();
        List<OpenApiNoticeRecord> records = openApiNoticeRecordMapper.selectRpaList(vo, customerServiceIds, userDeptDTO, vo.getUserId(), start, vo.getPageSize());
        List<RpaDTO> data = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(records)) {
            List<Long> customerIds = records.stream().map(OpenApiNoticeRecord::getCustomerServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, CCustomerService> customerServiceMap = ObjectUtils.isEmpty(customerIds) ? Maps.newHashMap() : customerServiceMapper.selectList(new LambdaQueryWrapper<CCustomerService>()
                            .eq(CCustomerService::getIsDel, false).in(CCustomerService::getId, customerIds))
                    .stream().collect(Collectors.toMap(CCustomerService::getId, Function.identity()));
            List<Long> userIds = records.stream().map(OpenApiNoticeRecord::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, SysUser> userMap = ObjectUtils.isEmpty(userIds) ? Maps.newHashMap() :
                    remoteUserService.getBatchUserByIds(userIds).getDataThrowException();
            data = records.stream().map(row -> {
                CCustomerService customerService = customerServiceMap.get(row.getCustomerServiceId());
                SysUser sysUser = userMap.get(row.getUserId());
                return RpaDTO.builder()
                        .id(row.getId())
                        .customerName(Objects.isNull(customerService) ? "" : customerService.getCustomerName())
                        .period(row.getPeriod())
                        .taskName(RpaTaskType.getTypeNameByCode(row.getTaskType()))
                        .source(Objects.isNull(sysUser) ? "" : sysUser.getNickName())
                        .rpaDealResult(row.getRpaDealResult())
                        .sysDeliverResult(row.getSysDeliverResult())
                        .createTime(row.getCreateTime())
                        .deliverTime(row.getDeliverTime())
                        .build();
            }).collect(Collectors.toList());
        }
        result.setRecords(data);
        result.setSize(vo.getPageSize());
        result.setCurrent(vo.getPageNum());
        result.setPages(totalCount % vo.getPageSize() == 0 ? totalCount / vo.getPageSize() : totalCount / vo.getPageSize() + 1);
        return result;
    }
}
