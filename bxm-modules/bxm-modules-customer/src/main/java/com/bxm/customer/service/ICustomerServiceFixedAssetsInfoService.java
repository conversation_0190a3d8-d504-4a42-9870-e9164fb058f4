package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.CustomerServiceFixedAssetsInfo;

/**
 * 客户服务固定资产信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICustomerServiceFixedAssetsInfoService extends IService<CustomerServiceFixedAssetsInfo>
{
    /**
     * 查询客户服务固定资产信息
     * 
     * @param id 客户服务固定资产信息主键
     * @return 客户服务固定资产信息
     */
    public CustomerServiceFixedAssetsInfo selectCustomerServiceFixedAssetsInfoById(Long id);

    /**
     * 查询客户服务固定资产信息列表
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 客户服务固定资产信息集合
     */
    public List<CustomerServiceFixedAssetsInfo> selectCustomerServiceFixedAssetsInfoList(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo);

    /**
     * 新增客户服务固定资产信息
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 结果
     */
    public int insertCustomerServiceFixedAssetsInfo(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo);

    /**
     * 修改客户服务固定资产信息
     * 
     * @param customerServiceFixedAssetsInfo 客户服务固定资产信息
     * @return 结果
     */
    public int updateCustomerServiceFixedAssetsInfo(CustomerServiceFixedAssetsInfo customerServiceFixedAssetsInfo);

    /**
     * 批量删除客户服务固定资产信息
     * 
     * @param ids 需要删除的客户服务固定资产信息主键集合
     * @return 结果
     */
    public int deleteCustomerServiceFixedAssetsInfoByIds(Long[] ids);

    /**
     * 删除客户服务固定资产信息信息
     * 
     * @param id 客户服务固定资产信息主键
     * @return 结果
     */
    public int deleteCustomerServiceFixedAssetsInfoById(Long id);
}
