package com.bxm.customer.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceInfoDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInsuranceMonthDTO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.NewCustomerInsuranceFundStatusMapper;
import com.bxm.customer.domain.NewCustomerInsuranceFundStatus;
import com.bxm.customer.service.INewCustomerInsuranceFundStatusService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 新户流转五险一金月状态Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class NewCustomerInsuranceFundStatusServiceImpl extends ServiceImpl<NewCustomerInsuranceFundStatusMapper, NewCustomerInsuranceFundStatus> implements INewCustomerInsuranceFundStatusService
{
    @Autowired
    private NewCustomerInsuranceFundStatusMapper newCustomerInsuranceFundStatusMapper;

    /**
     * 查询新户流转五险一金月状态
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 新户流转五险一金月状态
     */
    @Override
    public NewCustomerInsuranceFundStatus selectNewCustomerInsuranceFundStatusById(Long id)
    {
        return newCustomerInsuranceFundStatusMapper.selectNewCustomerInsuranceFundStatusById(id);
    }

    /**
     * 查询新户流转五险一金月状态列表
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 新户流转五险一金月状态
     */
    @Override
    public List<NewCustomerInsuranceFundStatus> selectNewCustomerInsuranceFundStatusList(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus)
    {
        return newCustomerInsuranceFundStatusMapper.selectNewCustomerInsuranceFundStatusList(newCustomerInsuranceFundStatus);
    }

    /**
     * 新增新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    @Override
    public int insertNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus)
    {
        return newCustomerInsuranceFundStatusMapper.insertNewCustomerInsuranceFundStatus(newCustomerInsuranceFundStatus);
    }

    /**
     * 修改新户流转五险一金月状态
     * 
     * @param newCustomerInsuranceFundStatus 新户流转五险一金月状态
     * @return 结果
     */
    @Override
    public int updateNewCustomerInsuranceFundStatus(NewCustomerInsuranceFundStatus newCustomerInsuranceFundStatus)
    {
        return newCustomerInsuranceFundStatusMapper.updateNewCustomerInsuranceFundStatus(newCustomerInsuranceFundStatus);
    }

    /**
     * 批量删除新户流转五险一金月状态
     * 
     * @param ids 需要删除的新户流转五险一金月状态主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInsuranceFundStatusByIds(Long[] ids)
    {
        return newCustomerInsuranceFundStatusMapper.deleteNewCustomerInsuranceFundStatusByIds(ids);
    }

    /**
     * 删除新户流转五险一金月状态信息
     * 
     * @param id 新户流转五险一金月状态主键
     * @return 结果
     */
    @Override
    public int deleteNewCustomerInsuranceFundStatusById(Long id)
    {
        return newCustomerInsuranceFundStatusMapper.deleteNewCustomerInsuranceFundStatusById(id);
    }

    @Override
    public List<NewCustomerInsuranceFundStatus> selectByCustomerId(Long customerId) {
        if (Objects.isNull(customerId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<NewCustomerInsuranceFundStatus>().eq(NewCustomerInsuranceFundStatus::getCustomerId, customerId));
    }

    @Override
    public void deleteByCustomerIdAndType(Long customerId, Integer type) {
        if (Objects.isNull(customerId)) {
            return;
        }
        remove(new LambdaQueryWrapper<NewCustomerInsuranceFundStatus>()
                .eq(NewCustomerInsuranceFundStatus::getCustomerId, customerId)
                .eq(!Objects.isNull(type), NewCustomerInsuranceFundStatus::getType, type));
    }

    @Override
    @Transactional
    public void removeAndSaveNew(Long newCustomerTransferId, NewCustomerTransferInsuranceInfoDTO insuranceInfoDTO) {
        deleteByCustomerIdAndType(newCustomerTransferId, null);
        List<NewCustomerInsuranceFundStatus> statusList = Lists.newArrayList();
        if (!Objects.isNull(insuranceInfoDTO.getMedicalSecurity()) && !ObjectUtils.isEmpty(insuranceInfoDTO.getMedicalSecurity().getMonthStatusForEdit())) {
            List<NewCustomerTransferInsuranceMonthDTO> waitSaveItems = insuranceInfoDTO.getMedicalSecurity().getMonthStatusForEdit().stream().filter(item -> Objects.nonNull(item.getStatus()))
                    .sorted(Comparator.comparing(NewCustomerTransferInsuranceMonthDTO::getYearMonth)).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitSaveItems)) {
                String lastMonthStatusStr = "";
                for (NewCustomerTransferInsuranceMonthDTO monthDTO : waitSaveItems) {
                    String statusStr = getStatusStr(monthDTO.getStatus(), lastMonthStatusStr);
                    lastMonthStatusStr = statusStr;
                    statusList.add(new NewCustomerInsuranceFundStatus().setCustomerId(newCustomerTransferId)
                            .setType(1).setMonth(monthDTO.getYearMonth()).setStatus(monthDTO.getStatus()).setStatusStr(statusStr));
                }
            }
        }
        if (!Objects.isNull(insuranceInfoDTO.getSocialSecurity()) && !ObjectUtils.isEmpty(insuranceInfoDTO.getSocialSecurity().getMonthStatusForEdit())) {
            List<NewCustomerTransferInsuranceMonthDTO> waitSaveItems = insuranceInfoDTO.getSocialSecurity().getMonthStatusForEdit().stream().filter(item -> Objects.nonNull(item.getStatus()))
                    .sorted(Comparator.comparing(NewCustomerTransferInsuranceMonthDTO::getYearMonth)).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitSaveItems)) {
                String lastMonthStatusStr = "";
                for (NewCustomerTransferInsuranceMonthDTO monthDTO : waitSaveItems) {
                    String statusStr = getStatusStr(monthDTO.getStatus(), lastMonthStatusStr);
                    lastMonthStatusStr = statusStr;
                    statusList.add(new NewCustomerInsuranceFundStatus().setCustomerId(newCustomerTransferId)
                            .setType(2).setMonth(monthDTO.getYearMonth()).setStatus(monthDTO.getStatus()).setStatusStr(statusStr));
                }
            }
        }
        if (!Objects.isNull(insuranceInfoDTO.getAccumulationFund()) && !ObjectUtils.isEmpty(insuranceInfoDTO.getAccumulationFund().getMonthStatusForEdit())) {
            List<NewCustomerTransferInsuranceMonthDTO> waitSaveItems = insuranceInfoDTO.getAccumulationFund().getMonthStatusForEdit().stream().filter(item -> Objects.nonNull(item.getStatus()))
                    .sorted(Comparator.comparing(NewCustomerTransferInsuranceMonthDTO::getYearMonth)).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(waitSaveItems)) {
                String lastMonthStatusStr = "";
                for (NewCustomerTransferInsuranceMonthDTO monthDTO : waitSaveItems) {
                    String statusStr = getStatusStr(monthDTO.getStatus(), lastMonthStatusStr);
                    lastMonthStatusStr = statusStr;
                    statusList.add(new NewCustomerInsuranceFundStatus().setCustomerId(newCustomerTransferId)
                            .setType(3).setMonth(monthDTO.getYearMonth()).setStatus(monthDTO.getStatus()).setStatusStr(statusStr));
                }
            }
        }
        if (!ObjectUtils.isEmpty(statusList)) {
            saveBatch(statusList);
        }
    }

    @Override
    public Map<Long, List<NewCustomerInsuranceFundStatus>> selectMapByCustomerIds(List<Long> newCustomerTransferIds) {
        if (ObjectUtils.isEmpty(newCustomerTransferIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<NewCustomerInsuranceFundStatus>().in(NewCustomerInsuranceFundStatus::getCustomerId, newCustomerTransferIds))
                .stream().collect(Collectors.groupingBy(NewCustomerInsuranceFundStatus::getCustomerId));
    }

    private String getStatusStr(Integer status, String lastMonthStatusStr) {
        if (status == 1) return "待申报";
        if (status == 2) return "待扣款";
        if (status == 3) return "已扣款";
        if (status == 4) return lastMonthStatusStr;
        return "";
    }
}
