package com.bxm.customer.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerServiceInsuranceFundStatusMapper;
import com.bxm.customer.domain.CustomerServiceInsuranceFundStatus;
import com.bxm.customer.service.ICustomerServiceInsuranceFundStatusService;

/**
 * 客户服务五险一金月状态Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CustomerServiceInsuranceFundStatusServiceImpl extends ServiceImpl<CustomerServiceInsuranceFundStatusMapper, CustomerServiceInsuranceFundStatus> implements ICustomerServiceInsuranceFundStatusService
{
    @Autowired
    private CustomerServiceInsuranceFundStatusMapper customerServiceInsuranceFundStatusMapper;

    /**
     * 查询客户服务五险一金月状态
     * 
     * @param id 客户服务五险一金月状态主键
     * @return 客户服务五险一金月状态
     */
    @Override
    public CustomerServiceInsuranceFundStatus selectCustomerServiceInsuranceFundStatusById(Long id)
    {
        return customerServiceInsuranceFundStatusMapper.selectCustomerServiceInsuranceFundStatusById(id);
    }

    /**
     * 查询客户服务五险一金月状态列表
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 客户服务五险一金月状态
     */
    @Override
    public List<CustomerServiceInsuranceFundStatus> selectCustomerServiceInsuranceFundStatusList(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus)
    {
        return customerServiceInsuranceFundStatusMapper.selectCustomerServiceInsuranceFundStatusList(customerServiceInsuranceFundStatus);
    }

    /**
     * 新增客户服务五险一金月状态
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 结果
     */
    @Override
    public int insertCustomerServiceInsuranceFundStatus(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus)
    {
        return customerServiceInsuranceFundStatusMapper.insertCustomerServiceInsuranceFundStatus(customerServiceInsuranceFundStatus);
    }

    /**
     * 修改客户服务五险一金月状态
     * 
     * @param customerServiceInsuranceFundStatus 客户服务五险一金月状态
     * @return 结果
     */
    @Override
    public int updateCustomerServiceInsuranceFundStatus(CustomerServiceInsuranceFundStatus customerServiceInsuranceFundStatus)
    {
        return customerServiceInsuranceFundStatusMapper.updateCustomerServiceInsuranceFundStatus(customerServiceInsuranceFundStatus);
    }

    /**
     * 批量删除客户服务五险一金月状态
     * 
     * @param ids 需要删除的客户服务五险一金月状态主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInsuranceFundStatusByIds(Long[] ids)
    {
        return customerServiceInsuranceFundStatusMapper.deleteCustomerServiceInsuranceFundStatusByIds(ids);
    }

    /**
     * 删除客户服务五险一金月状态信息
     * 
     * @param id 客户服务五险一金月状态主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServiceInsuranceFundStatusById(Long id)
    {
        return customerServiceInsuranceFundStatusMapper.deleteCustomerServiceInsuranceFundStatusById(id);
    }
}
