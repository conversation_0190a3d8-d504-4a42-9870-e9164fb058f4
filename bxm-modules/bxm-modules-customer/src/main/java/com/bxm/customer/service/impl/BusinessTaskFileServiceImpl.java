package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.businessTask.BusinessTaskFileType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.BusinessTaskFile;
import com.bxm.customer.mapper.BusinessTaskFileMapper;
import com.bxm.customer.service.FileService;
import com.bxm.customer.service.IBusinessTaskFileService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 业务任务的附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class BusinessTaskFileServiceImpl extends ServiceImpl<BusinessTaskFileMapper, BusinessTaskFile> implements IBusinessTaskFileService {
    @Autowired
    private BusinessTaskFileMapper businessTaskFileMapper;

    @Autowired
    private FileService fileService;

    /**
     * 查询业务任务的附件
     *
     * @param id 业务任务的附件主键
     * @return 业务任务的附件
     */
    @Override
    public BusinessTaskFile selectBusinessTaskFileById(Long id) {
        return businessTaskFileMapper.selectBusinessTaskFileById(id);
    }

    /**
     * 查询业务任务的附件列表
     *
     * @param businessTaskFile 业务任务的附件
     * @return 业务任务的附件
     */
    @Override
    public List<BusinessTaskFile> selectBusinessTaskFileList(BusinessTaskFile businessTaskFile) {
        return businessTaskFileMapper.selectBusinessTaskFileList(businessTaskFile);
    }

    /**
     * 新增业务任务的附件
     *
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    @Override
    public int insertBusinessTaskFile(BusinessTaskFile businessTaskFile) {
        businessTaskFile.setCreateTime(DateUtils.getNowDate());
        return businessTaskFileMapper.insertBusinessTaskFile(businessTaskFile);
    }

    /**
     * 修改业务任务的附件
     *
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    @Override
    public int updateBusinessTaskFile(BusinessTaskFile businessTaskFile) {
        businessTaskFile.setUpdateTime(DateUtils.getNowDate());
        return businessTaskFileMapper.updateBusinessTaskFile(businessTaskFile);
    }

    /**
     * 批量删除业务任务的附件
     *
     * @param ids 需要删除的业务任务的附件主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTaskFileByIds(Long[] ids) {
        return businessTaskFileMapper.deleteBusinessTaskFileByIds(ids);
    }

    /**
     * 删除业务任务的附件信息
     *
     * @param id 业务任务的附件主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTaskFileById(Long id) {
        return businessTaskFileMapper.deleteBusinessTaskFileById(id);
    }

    @Override
    public List<BusinessTaskFile> selectBatchByMainIdsAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes) {
        List<BusinessTaskFile> result = Collections.emptyList();

        if (ObjectUtils.isEmpty(mainIds)) {
            return result;
        }

        if (ObjectUtils.isEmpty(fileTypes)) {
            result = list(
                    new LambdaQueryWrapper<BusinessTaskFile>()
                            .in(BusinessTaskFile::getMainId, mainIds)
            );
        } else {
            result = list(
                    new LambdaQueryWrapper<BusinessTaskFile>()
                            .in(BusinessTaskFile::getMainId, mainIds)
                            .in(BusinessTaskFile::getFileType, fileTypes.stream().map(BusinessTaskFileType::getCode).distinct().collect(Collectors.toList()))
            );
        }

        return result;
    }

    @Override
    public Map<Long, Map<Integer, List<BusinessTaskFile>>> selectMapBatchByMainIdsAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes) {
        List<BusinessTaskFile> files = selectBatchByMainIdsAndFileTypes(mainIds, fileTypes);

        return files.stream()
                .collect(
                        Collectors.groupingBy(BusinessTaskFile::getMainId,
                                Collectors.groupingBy(BusinessTaskFile::getFileType))
                );
    }

    @Override
    public Map<Long, Map<String, List<BusinessTaskFile>>> selectMapSubKeyByMainIdAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes) {
        List<BusinessTaskFile> files = selectBatchByMainIdsAndFileTypes(mainIds, fileTypes);

        return files.stream().collect(
                Collectors.groupingBy(BusinessTaskFile::getMainId,
                        Collectors.groupingBy(row -> key(row.getFileType(), row.getSubFileType())))
        );
    }

    @Override
    public void deleteByMainIdAndFileTypes(List<Long> mainIds, List<BusinessTaskFileType> fileTypes) {
        if (!ObjectUtils.isEmpty(mainIds)) {
            if (ObjectUtils.isEmpty(fileTypes)) {
                remove(
                        new LambdaQueryWrapper<BusinessTaskFile>()
                                .in(BusinessTaskFile::getMainId, mainIds)
                );
            } else {
                remove(
                        new LambdaQueryWrapper<BusinessTaskFile>()
                                .in(BusinessTaskFile::getMainId, mainIds)
                                .in(BusinessTaskFile::getFileType, fileTypes.stream().map(BusinessTaskFileType::getCode).distinct().collect(Collectors.toList()))
                );
            }
        }
    }

    @Override
    public void saveFile(Long mainId, List<CommonFileVO> files, BusinessTaskFileType fileType, String subFileType) {
        if (mainId != null && !ObjectUtils.isEmpty(files) && fileType != null && !StringUtils.isEmpty(subFileType)) {
            Integer fileTypeCode = fileType.getCode();

            saveBatch(
                    files.stream().map(f -> new BusinessTaskFile()
                            .setMainId(mainId)
                            .setFileName(f.getFileName())
                            .setFileType(fileTypeCode)
                            .setSubFileType(subFileType)
                            .setFileUrl(f.getFileUrl()))
                            .collect(Collectors.toList())
            );
        }
    }

    @Override
    public List<CommonFileVO> covToCommonFileVO(List<BusinessTaskFile> files) {
        return ObjectUtils.isEmpty(files) ? Lists.newArrayList()
                : files.stream()
                .map(f -> CommonFileVO.builder()
                        .fileName(f.getFileName())
                        .fileUrl(f.getFileUrl())
                        .fullFileUrl(fileService.getFullFileUrl(f.getFileUrl()))
                        .build())
                .collect(Collectors.toList());
    }

    public static String key(Integer fileType, String subFileType) {
        return fileType + "_" + subFileType;
    }
}
