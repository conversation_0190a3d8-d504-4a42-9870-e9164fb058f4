package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.domain.Bill;
import com.bxm.customer.domain.dto.bill.BillDTO;
import com.bxm.customer.domain.dto.bill.BillDetailDTO;
import com.bxm.customer.domain.dto.bill.BillReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewOrderDTO;
import com.bxm.customer.domain.vo.bill.RejectBillVO;
import com.bxm.customer.domain.vo.bill.RemovePushVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementAppendBillVO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderPushVO;

import java.util.List;

/**
 * 账单Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface IBillService extends IService<Bill>
{
    /**
     * 查询账单
     * 
     * @param id 账单主键
     * @return 账单
     */
    public Bill selectBillById(Long id);

    /**
     * 查询账单列表
     * 
     * @param bill 账单
     * @return 账单集合
     */
    public List<Bill> selectBillList(Bill bill);

    /**
     * 新增账单
     * 
     * @param bill 账单
     * @return 结果
     */
    public int insertBill(Bill bill);

    /**
     * 修改账单
     * 
     * @param bill 账单
     * @return 结果
     */
    public int updateBill(Bill bill);

    /**
     * 批量删除账单
     * 
     * @param ids 需要删除的账单主键集合
     * @return 结果
     */
    public int deleteBillByIds(Long[] ids);

    /**
     * 删除账单信息
     * 
     * @param id 账单主键
     * @return 结果
     */
    public int deleteBillById(Long id);

    void createBillBySettlementOrderPush(SettlementOrderPushVO vo);

    IPage<BillDTO> billList(Long deptId, String billTitle, Long businessDeptId, Integer status, String createTimeStart, String createTimeEnd, Integer pageNum, Integer pageSize);

    BillDetailDTO billDetail(Long id);

    void delete(CommonIdVO vo);

    List<SettlementPushReviewDTO> rePushReview(CommonIdVO vo);

    void confirmRePush(SettlementOrderPushVO vo);

    void removePush(RemovePushVO vo);

    void revokeBill(CommonIdVO vo);

    List<SettlementPushReviewOrderDTO> settlementListByBillId(Long billId);

    void rejectBill(RejectBillVO vo);

    void confirmBill(CommonIdVO vo);

    BillReviewDTO billReview(Long billId);

    void settlementAppendToBill(SettlementAppendBillVO vo);
}
