package com.bxm.customer.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.customer.domain.NewCustomerInfo;
import com.bxm.customer.domain.dto.newCustomerTransfer.*;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerCreateVO;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerServiceTaxTypeCheckVO;
import com.bxm.customer.domain.vo.newCustomer.NewCustomerTransferVO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 新户流转客户信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface INewCustomerInfoService extends IService<NewCustomerInfo>
{
    /**
     * 查询新户流转客户信息
     * 
     * @param id 新户流转客户信息主键
     * @return 新户流转客户信息
     */
    public NewCustomerInfo selectNewCustomerInfoById(Long id);

    /**
     * 查询新户流转客户信息列表
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 新户流转客户信息集合
     */
    public List<NewCustomerInfo> selectNewCustomerInfoList(NewCustomerInfo newCustomerInfo);

    /**
     * 新增新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    public int insertNewCustomerInfo(NewCustomerInfo newCustomerInfo);

    /**
     * 修改新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    public int updateNewCustomerInfo(NewCustomerInfo newCustomerInfo);

    /**
     * 批量删除新户流转客户信息
     * 
     * @param ids 需要删除的新户流转客户信息主键集合
     * @return 结果
     */
    public int deleteNewCustomerInfoByIds(Long[] ids);

    /**
     * 删除新户流转客户信息信息
     * 
     * @param id 新户流转客户信息主键
     * @return 结果
     */
    public int deleteNewCustomerInfoById(Long id);

    IPage<NewCustomerTransferDTO> selectNewCustomerTransferList(Long deptId, int pageNum, int pageSize, String keyWord, Integer status, Integer taxType, Long queryDeptId, String tagName, Integer tagIncludeFlag, String businessDeptIds, Integer firstPeriodStart, Integer firstPeriodEnd);

    List<NewCustomerInfo> selectByIds(List<Long> ids);

    CommonOperateResultDTO deleteNewCustomerTransfer(List<Long> ids);

    CommonOperateResultDTO submitNewCustomerTransfer(List<Long> ids);

    CommonOperateResultDTO reBackNewCustomerTransfer(List<Long> ids);

    Long createNewCustomerBaseInfo(Long deptId, NewCustomerCreateVO vo);

    Long modifyNewCustomerBaseInfo(Long deptId, NewCustomerCreateVO vo);

    List<NewCustomerTransferBankAccountDTO> getNewCustomerTransferBankAccountList(Long id);

    void deleteNewCustomerTransferBankAccount(CommonIdVO vo);

    void addNewCustomerTransferBankAccount(NewCustomerTransferBankAccountDTO vo);

    void modifyNewCustomerTransferAccount(NewCustomerTransferBankAccountDTO vo);

    List<NewCustomerTransferSysAccountDTO> getNewCustomerTransferSysAccount(Long id);

    void addNewCustomerTransferSysAccount(NewCustomerTransferSysAccountDTO dto);

    void modifyNewCustomerTransferSysAccount(NewCustomerTransferSysAccountDTO dto);

    void deleteNewCustomerTransferSysAccount(CommonIdVO vo);

    NewCustomerServiceFinanceTaxInfoDTO newCustomerServiceFinanceTaxInfo(Long id);

    void editNewCustomerServiceTaxTypeCheck(NewCustomerServiceTaxTypeCheckDTO dto);

    List<NewCustomerServiceTaxTypeCheckVO> newCustomerTransferTaxTypeCheckList(Long id);

    void modifyNewCustomerServiceFinanceTaxInfo(NewCustomerServiceFinanceTaxInfoDTO vo);

    NewCustomerTransferDetailInfoDTO detailInfo(Long id);

    Long saveCustomerInfo(NewCustomerTransferDetailInfoDTO vo, Long deptId);

    Long submitCustomerInfo(NewCustomerTransferDetailInfoDTO vo, Long deptId);

    ServiceNumberRepeatCheckResultDTO serviceNumberCheckRepeat(Long deptId, String serviceNumber);

    void singleTransfer(Long deptId, NewCustomerTransferVO vo);

    BatchTransferCheckResultDTO checkFile(MultipartFile file);

    void downloadErrorData(String batchNo, HttpServletResponse response);

    void downloadExistServiceNumberData(String batchNo, HttpServletResponse response);

    void confirmBatchTransfer(String batchNo, Long accountingTopDeptId, Long deptId);

    NextCheckDTO nextCheck(Long deptId, NewCustomerCreateVO vo);

    NewCustomerOtherInfoDTO getEndCustomerServiceInfo(Long deptId, NewCustomerCreateVO vo);

    void newCustomerAutoTransferTask();
}
