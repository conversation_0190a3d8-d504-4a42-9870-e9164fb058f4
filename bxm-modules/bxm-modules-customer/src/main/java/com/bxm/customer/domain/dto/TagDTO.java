package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TagDTO {

    @ApiModelProperty("标签id")
    private Long id;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("完整标签名称")
    private String fullTagName;

    @ApiModelProperty("标签类型，1-系统标签不带参数，2-系统标签带参数，3-自定义标签")
    private Integer tagType;

    @ApiModelProperty("是否选中")
    private Boolean isSelected;

    @ApiModelProperty("标签参数值")
    private String paramValue;
}
