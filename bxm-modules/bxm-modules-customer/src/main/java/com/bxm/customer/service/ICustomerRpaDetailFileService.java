package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.CustomerRpaDetailFile;

/**
 * 交付明细附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface ICustomerRpaDetailFileService extends IService<CustomerRpaDetailFile>
{
    /**
     * 查询交付明细附件
     * 
     * @param id 交付明细附件主键
     * @return 交付明细附件
     */
    public CustomerRpaDetailFile selectCustomerRpaDetailFileById(Long id);

    /**
     * 查询交付明细附件列表
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 交付明细附件集合
     */
    public List<CustomerRpaDetailFile> selectCustomerRpaDetailFileList(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 新增交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    public int insertCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 修改交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    public int updateCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile);

    /**
     * 批量删除交付明细附件
     * 
     * @param ids 需要删除的交付明细附件主键集合
     * @return 结果
     */
    public int deleteCustomerRpaDetailFileByIds(Long[] ids);

    /**
     * 删除交付明细附件信息
     * 
     * @param id 交付明细附件主键
     * @return 结果
     */
    public int deleteCustomerRpaDetailFileById(Long id);

    List<CustomerRpaDetailFile> selectByRpaDetailId(Long rpaDetailId);

    List<CustomerRpaDetailFile> selectByBatchRpaDetailId(List<Long> detailIds);

    List<CommonFileVO> rpaDetailFileList(Long rpaDetailId);
}
