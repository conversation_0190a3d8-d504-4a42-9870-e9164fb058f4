package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedStock;

import java.util.List;

/**
 * 增值库存Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface IValueAddedStockService extends IService<ValueAddedStock> {



    /**
     * 根据交付单编号查询库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @return 库存记录列表
     */
    List<ValueAddedStock> getByDeliveryOrderNo(String deliveryOrderNo);

    /**
     * 根据交付单编号和账期查询库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @param period 账期
     * @return 库存记录，如果不存在则返回null
     */
    ValueAddedStock getByDeliveryOrderNoAndPeriod(String deliveryOrderNo, String period);

    /**
     * 批量保存或更新库存记录
     *
     * @param stockList 库存记录列表
     * @return 是否保存成功
     */
    boolean batchSaveOrUpdate(List<ValueAddedStock> stockList);

    /**
     * 删除指定交付单的所有库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @return 是否删除成功
     */
    boolean deleteByDeliveryOrderNo(String deliveryOrderNo);
}
