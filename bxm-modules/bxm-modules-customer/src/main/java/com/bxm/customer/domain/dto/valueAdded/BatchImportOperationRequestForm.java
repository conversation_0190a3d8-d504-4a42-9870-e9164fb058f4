package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.enums.ValueAddedExportOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Arrays;
import java.util.List;

/**
 * 批量导入操作表单请求DTO
 *
 * 用于接收前端表单提交的批量导入操作参数
 * 支持multipart/form-data格式的文件上传
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导入操作表单请求DTO")
public class BatchImportOperationRequestForm {

    /**
     * 交付单编号列表（逗号分隔的字符串）
     */
    @NotEmpty(message = "Delivery order number list cannot be empty")
    @Size(max = 10000, message = "Delivery order numbers string length cannot exceed 10000 characters")
    @ApiModelProperty(value = "交付单编号列表，逗号分隔", required = true, 
                     example = "VAD2508051430001A1C,VAD2508051430002A1C")
    private String deliveryOrderNos;

    /**
     * 操作类型字符串
     */
    @NotEmpty(message = "Operation type cannot be empty")
    @ApiModelProperty(value = "操作类型", required = true,
                     allowableValues = "DELIVERY,SUPPLEMENT_DELIVERY,DEDUCTION",
                     example = "DELIVERY")
    private String operation;

    /**
     * 交付单模板Excel文件（batchExportImportOperationTemplate导出的模板）
     */
    @NotNull(message = "Template file cannot be null")
    @ApiModelProperty(value = "交付单模板Excel文件", required = true)
    private MultipartFile templateFile;

    /**
     * 附件压缩文件（支持zip包、rar包）
     */
    @ApiModelProperty(value = "附件压缩文件，支持zip包、rar包")
    private MultipartFile attachmentFile;

    /**
     * 转换为BatchImportOperationRequest
     */
    public BatchImportOperationRequest toBatchImportOperationRequest() {
        // 解析交付单编号列表
        List<String> orderNoList = Arrays.asList(deliveryOrderNos.split(","));
        
        // 解析操作类型
        ValueAddedExportOperationType operationType = ValueAddedExportOperationType.getByCode(operation);
        if (operationType == null) {
            throw new IllegalArgumentException("Invalid operation type: " + operation);
        }

        return BatchImportOperationRequest.builder()
                .deliveryOrderNoList(orderNoList)
                .operation(operationType)
                .templateFile(templateFile)
                .attachmentFile(attachmentFile)
                .build();
    }

    /**
     * 验证表单参数
     */
    public void validate() {
        if (deliveryOrderNos == null || deliveryOrderNos.trim().isEmpty()) {
            throw new IllegalArgumentException("Delivery order numbers cannot be empty");
        }

        if (operation == null || operation.trim().isEmpty()) {
            throw new IllegalArgumentException("Operation type cannot be empty");
        }

        if (templateFile == null || templateFile.isEmpty()) {
            throw new IllegalArgumentException("Template file cannot be empty");
        }

        // 验证交付单编号格式
        String[] orderNos = deliveryOrderNos.split(",");
        if (orderNos.length == 0) {
            throw new IllegalArgumentException("At least one delivery order number is required");
        }

        if (orderNos.length > 1000) {
            throw new IllegalArgumentException("Maximum 1000 delivery orders allowed per batch");
        }

        // 验证操作类型
        try {
            ValueAddedExportOperationType operationType = ValueAddedExportOperationType.getByCode(operation);
            if (operationType == null) {
                throw new IllegalArgumentException("Invalid operation type: " + operation);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid operation type: " + operation);
        }

        // 验证文件格式
        validateFiles();
    }

    /**
     * 验证文件参数
     */
    private void validateFiles() {
        if (templateFile == null || templateFile.isEmpty()) {
            throw new IllegalArgumentException("Template file cannot be empty");
        }

        // 验证模板文件格式
        String templateFileName = templateFile.getOriginalFilename();
        if (templateFileName == null || 
            (!templateFileName.toLowerCase().endsWith(".xlsx") && 
             !templateFileName.toLowerCase().endsWith(".xls"))) {
            throw new IllegalArgumentException("Template file must be Excel format (.xlsx or .xls)");
        }

        // 如果有附件文件，验证格式
        if (attachmentFile != null && !attachmentFile.isEmpty()) {
            String attachmentFileName = attachmentFile.getOriginalFilename();
            if (attachmentFileName == null || 
                (!attachmentFileName.toLowerCase().endsWith(".zip") && 
                 !attachmentFileName.toLowerCase().endsWith(".rar"))) {
                throw new IllegalArgumentException("Attachment file must be compressed format (.zip or .rar)");
            }
        }
    }

    /**
     * 获取交付单数量
     */
    public int getOrderCount() {
        if (deliveryOrderNos == null || deliveryOrderNos.trim().isEmpty()) {
            return 0;
        }
        return deliveryOrderNos.split(",").length;
    }

    /**
     * 判断是否有附件文件
     */
    public boolean hasAttachmentFile() {
        return attachmentFile != null && !attachmentFile.isEmpty();
    }
}
