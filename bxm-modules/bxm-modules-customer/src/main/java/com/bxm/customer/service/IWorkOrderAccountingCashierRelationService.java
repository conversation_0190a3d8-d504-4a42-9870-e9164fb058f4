package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.WorkOrderAccountingCashierRelation;
import com.bxm.customer.domain.dto.workOrder.WorkOrderAccountingCashierDTO;

/**
 * 工单账务交付单关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
public interface IWorkOrderAccountingCashierRelationService extends IService<WorkOrderAccountingCashierRelation>
{
    /**
     * 查询工单账务交付单关联
     * 
     * @param id 工单账务交付单关联主键
     * @return 工单账务交付单关联
     */
    public WorkOrderAccountingCashierRelation selectWorkOrderAccountingCashierRelationById(Long id);

    /**
     * 查询工单账务交付单关联列表
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 工单账务交付单关联集合
     */
    public List<WorkOrderAccountingCashierRelation> selectWorkOrderAccountingCashierRelationList(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 新增工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    public int insertWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 修改工单账务交付单关联
     * 
     * @param workOrderAccountingCashierRelation 工单账务交付单关联
     * @return 结果
     */
    public int updateWorkOrderAccountingCashierRelation(WorkOrderAccountingCashierRelation workOrderAccountingCashierRelation);

    /**
     * 批量删除工单账务交付单关联
     * 
     * @param ids 需要删除的工单账务交付单关联主键集合
     * @return 结果
     */
    public int deleteWorkOrderAccountingCashierRelationByIds(Long[] ids);

    /**
     * 删除工单账务交付单关联信息
     * 
     * @param id 工单账务交付单关联主键
     * @return 结果
     */
    public int deleteWorkOrderAccountingCashierRelationById(Long id);

    void saveWorkOrderAccountingCashierRelation(Long workOrderId, Long customerServiceId, Integer periodStart, Integer periodEnd);

    List<WorkOrderAccountingCashierDTO> selectAccountingCashierList(WorkOrder workOrder);
}
