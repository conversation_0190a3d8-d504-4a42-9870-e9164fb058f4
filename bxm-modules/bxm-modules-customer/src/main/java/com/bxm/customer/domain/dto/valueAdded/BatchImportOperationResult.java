package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量导入操作结果DTO
 *
 * 用于返回增值交付单批量导入操作的执行结果
 * 包含成功数量、失败数量、错误详情等信息
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量导入操作结果DTO")
public class BatchImportOperationResult {

    /**
     * 操作类型描述
     */
    @ApiModelProperty(value = "操作类型描述", example = "交付")
    private String operationDescription;

    /**
     * 总处理数量
     */
    @ApiModelProperty(value = "总处理数量", example = "100")
    private Integer totalCount;

    /**
     * 成功数量
     */
    @ApiModelProperty(value = "成功数量", example = "95")
    private Integer successCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量", example = "5")
    private Integer errorCount;

    /**
     * 成功的交付单编号列表
     */
    @ApiModelProperty(value = "成功的交付单编号列表")
    private List<String> successOrderNos;

    /**
     * 错误详情列表
     */
    @ApiModelProperty(value = "错误详情列表")
    private List<BatchOperationErrorDTO> errors;

    /**
     * 异常数据批次号（用于查询详细错误信息）
     */
    @ApiModelProperty(value = "异常数据批次号", example = "abc123def456")
    private String errorBatchNo;

    /**
     * 处理开始时间
     */
    @ApiModelProperty(value = "处理开始时间")
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    @ApiModelProperty(value = "处理结束时间")
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    @ApiModelProperty(value = "处理耗时（毫秒）", example = "5000")
    private Long processingTimeMs;

    /**
     * 文件处理结果
     */
    @ApiModelProperty(value = "文件处理结果")
    private FileProcessingResult fileProcessingResult;

    /**
     * 判断是否有错误
     */
    public boolean hasErrors() {
        return errorCount != null && errorCount > 0;
    }

    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccess() {
        return errorCount != null && errorCount == 0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (successCount != null ? successCount : 0) / totalCount * 100;
    }

    /**
     * 文件处理结果内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("文件处理结果")
    public static class FileProcessingResult {

        /**
         * 模板文件处理状态
         */
        @ApiModelProperty(value = "模板文件处理状态", example = "SUCCESS")
        private String templateFileStatus;

        /**
         * 模板文件处理消息
         */
        @ApiModelProperty(value = "模板文件处理消息", example = "模板文件解析成功")
        private String templateFileMessage;

        /**
         * 附件文件处理状态
         */
        @ApiModelProperty(value = "附件文件处理状态", example = "SUCCESS")
        private String attachmentFileStatus;

        /**
         * 附件文件处理消息
         */
        @ApiModelProperty(value = "附件文件处理消息", example = "附件文件解压成功")
        private String attachmentFileMessage;

        /**
         * 解压后的文件数量
         */
        @ApiModelProperty(value = "解压后的文件数量", example = "50")
        private Integer extractedFileCount;

        /**
         * 成功保存的文件数量
         */
        @ApiModelProperty(value = "成功保存的文件数量", example = "48")
        private Integer savedFileCount;

        /**
         * 文件保存失败的数量
         */
        @ApiModelProperty(value = "文件保存失败的数量", example = "2")
        private Integer failedFileCount;

        /**
         * 文件处理错误详情
         */
        @ApiModelProperty(value = "文件处理错误详情")
        private List<String> fileErrors;
    }
}
