package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.OpenApiDownloadDeliverRelation;

/**
 * 个税申报下载轮询Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-06
 */
public interface IOpenApiDownloadDeliverRelationService extends IService<OpenApiDownloadDeliverRelation>
{
    /**
     * 查询个税申报下载轮询
     * 
     * @param id 个税申报下载轮询主键
     * @return 个税申报下载轮询
     */
    public OpenApiDownloadDeliverRelation selectOpenApiDownloadDeliverRelationById(Long id);

    /**
     * 查询个税申报下载轮询列表
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 个税申报下载轮询集合
     */
    public List<OpenApiDownloadDeliverRelation> selectOpenApiDownloadDeliverRelationList(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 新增个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    public int insertOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 修改个税申报下载轮询
     * 
     * @param openApiDownloadDeliverRelation 个税申报下载轮询
     * @return 结果
     */
    public int updateOpenApiDownloadDeliverRelation(OpenApiDownloadDeliverRelation openApiDownloadDeliverRelation);

    /**
     * 批量删除个税申报下载轮询
     * 
     * @param ids 需要删除的个税申报下载轮询主键集合
     * @return 结果
     */
    public int deleteOpenApiDownloadDeliverRelationByIds(Long[] ids);

    /**
     * 删除个税申报下载轮询信息
     * 
     * @param id 个税申报下载轮询主键
     * @return 结果
     */
    public int deleteOpenApiDownloadDeliverRelationById(Long id);

    void createRelation(Long deliverId, String downloadId);

    void updateStatusByDownloadId(String downloadId, String downloadUrl, String status);

    void rpaReportTableDownloadTask();
}
