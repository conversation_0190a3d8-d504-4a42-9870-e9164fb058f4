### ========================================
### 批量导入操作API测试
### ========================================

### 环境变量配置
@baseUrl = http://localhost:8080/customer
@authorization = Bearer your_token_here

### ========================================
### 1. 批量交付导入操作测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C,VAD2508051430002A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

DELIVERY
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="templateFile"; filename="delivery_template.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-files/delivery_template.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="attachmentFile"; filename="attachments.zip"
Content-Type: application/zip

< ./test-files/attachments.zip
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 2. 批量补充附件导入操作测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C,VAD2508051430002A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

SUPPLEMENT_DELIVERY
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="templateFile"; filename="supplement_template.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-files/supplement_template.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="attachmentFile"; filename="supplement_attachments.rar"
Content-Type: application/x-rar-compressed

< ./test-files/supplement_attachments.rar
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 3. 批量扣款导入操作测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C,VAD2508051430002A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

DEDUCTION
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="templateFile"; filename="deduction_template.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-files/deduction_template.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="attachmentFile"; filename="deduction_vouchers.zip"
Content-Type: application/zip

< ./test-files/deduction_vouchers.zip
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 4. 参数验证测试 - 缺少必填参数
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

DELIVERY
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 5. 参数验证测试 - 无效操作类型
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

INVALID_OPERATION
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="templateFile"; filename="test.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

test content
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========================================
### 6. 只有模板文件，无附件文件的测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/batchImportOperation
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNos"

VAD2508051430001A1C
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="operation"

SUPPLEMENT_DELIVERY
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="templateFile"; filename="template_only.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-files/template_only.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--
