package com.bxm.customer.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class SettlementOrderServiceTest {

    @Autowired
    private SettlementOrderService settlementOrderService;

    @Test
    void confirmAddData() {
        settlementOrderService.confirmAddData("e5f59a61293d402eb98f3ff7c2cb582d", "67eb0f000e95406dbfd002e6f3c45060", null, 1, 61L);
    }
}