package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TaxCheckReportType;
import com.bxm.customer.domain.CBusinessTagRelation;
import com.bxm.customer.domain.CustomerDeliver;
import com.bxm.customer.domain.CustomerServicePeriodMonthTaxTypeCheck;
import com.bxm.customer.mapper.CustomerServicePeriodMonthTaxTypeCheckMapper;
import com.bxm.customer.properties.SpecialTagProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ICustomerServiceInAccountServiceTest {

    @Autowired
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private CustomerServicePeriodMonthTaxTypeCheckMapper customerServicePeriodMonthTaxTypeCheckMapper;

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Test
    void addInAccountFromPeriod() {
    }

    @Test
    void addInAccountForHistory() {
        customerServiceInAccountService.addInAccountForHistory();
    }

    @Test
    void testAutoConfirm() {
        CustomerDeliver customerDeliver = customerDeliverService.getById(975L);
        CustomerDeliver update = new CustomerDeliver();
        update.setTotalTaxAmount(BigDecimal.ZERO);
        if (Objects.equals(customerDeliver.getDeliverType(), DeliverType.NATIONAL_TAX.getCode())) {
            Integer month = customerDeliver.getPeriod() % 100;
            List<CustomerServicePeriodMonthTaxTypeCheck> taxTypeCheckList = customerServicePeriodMonthTaxTypeCheckMapper.selectList(new LambdaQueryWrapper<CustomerServicePeriodMonthTaxTypeCheck>()
                    .eq(CustomerServicePeriodMonthTaxTypeCheck::getCustomerServicePeriodMonthId, customerDeliver.getCustomerServicePeriodMonthId()));
            Map<Integer, List<CustomerServicePeriodMonthTaxTypeCheck>> taxCheckMap = taxTypeCheckList.stream().collect(Collectors.groupingBy(CustomerServicePeriodMonthTaxTypeCheck::getReportType));
            List<CBusinessTagRelation> relations = businessTagRelationService.selectByBusinessIdAndBusinessType(customerDeliver.getCustomerServicePeriodMonthId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD.getCode());
            Boolean isAutoConfirm = Boolean.FALSE;
            if (Constants.NATIONAL_TAX_NEED_CREATE_MONTH.contains(month)) {
                // 没有增值税 或 没有无票收入
                if (relations.stream().noneMatch(r -> Objects.equals(r.getTagId(), specialTagProperties.getWupiaoshouru())) ||
                        taxTypeCheckList.stream().noneMatch(taxCheck -> Objects.equals(taxCheck.getTaxType(), Constants.ZENGZHISHUI))) {
                    isAutoConfirm = Boolean.TRUE;
                }
            } else {
                // 月报税种有非包含 个人所得税 的税种
                // 没有增值税 或 没有无票收入 或 （有季报增值税且有无票收入标签）
                if (taxCheckMap.containsKey(TaxCheckReportType.MONTH.getCode()) && taxCheckMap.get(TaxCheckReportType.MONTH.getCode()).stream().anyMatch(taxCheck -> !taxCheck.getTaxType().contains(Constants.PERSON_TAX_CHECK_TYPE))) {
                    if (relations.stream().noneMatch(r -> Objects.equals(r.getTagId(), specialTagProperties.getWupiaoshouru())) ||
                            taxTypeCheckList.stream().noneMatch(taxCheck -> Objects.equals(taxCheck.getTaxType(), Constants.ZENGZHISHUI)) ||
                            (taxCheckMap.containsKey(TaxCheckReportType.QUARTER.getCode()) && taxCheckMap.get(TaxCheckReportType.QUARTER.getCode()).stream().anyMatch(q -> Objects.equals(q.getTaxType(), Constants.ZENGZHISHUI)) && relations.stream().anyMatch(r -> Objects.equals(r.getTagId(), specialTagProperties.getWupiaoshouru())))) {
                        isAutoConfirm = Boolean.TRUE;
                    }
                }
            }
            if (update.getTotalTaxAmount().compareTo(BigDecimal.ZERO) == 0 && isAutoConfirm) {
                System.out.println(true);
            }
        }
    }
}