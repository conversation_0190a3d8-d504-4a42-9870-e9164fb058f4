package com.bxm.customer.service;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.vo.valueAdded.DeliveryOrderVO;
import com.bxm.customer.service.impl.ValueAddedDeliveryOrderServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 增值交付单服务缓存功能测试
 * 
 * 验证缓存机制是否正确工作，确保性能优化效果
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
class ValueAddedDeliveryOrderServiceCacheTest {

    @Mock
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @InjectMocks
    private ValueAddedDeliveryOrderServiceImpl deliveryOrderService;

    private ValueAddedItemType mockItemType;
    private ValueAddedDeliveryOrder mockOrder;

    @BeforeEach
    void setUp() {
        // 创建模拟的增值事项类型
        mockItemType = new ValueAddedItemType();
        mockItemType.setId(1L);
        mockItemType.setItemName("社医保");
        mockItemType.setItemType("税务服务");
        mockItemType.setItemTypeCode("TAX_SERVICE");
        mockItemType.setItemCode("TAX_SOCIAL_INSURANCE");
        mockItemType.setIsDel(false);

        // 创建模拟的交付单
        mockOrder = new ValueAddedDeliveryOrder();
        mockOrder.setId(1L);
        mockOrder.setDeliveryOrderNo("DO20250819001");
        mockOrder.setValueAddedItemTypeId(1);
        mockOrder.setCustomerName("测试客户");
    }

    @Test
    void testCachePerformance() {
        // 模拟数据库查询
        when(valueAddedItemTypeService.getById(1L)).thenReturn(mockItemType);

        // 第一次调用 - 应该查询数据库并缓存
        ValueAddedItemType result1 = (ValueAddedItemType) ReflectionTestUtils.invokeMethod(
                deliveryOrderService, "getValueAddedItemTypeWithCache", 1);
        
        // 第二次调用 - 应该从缓存获取
        ValueAddedItemType result2 = (ValueAddedItemType) ReflectionTestUtils.invokeMethod(
                deliveryOrderService, "getValueAddedItemTypeWithCache", 1);

        // 验证结果
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(mockItemType.getItemName(), result1.getItemName());
        assertEquals(mockItemType.getItemName(), result2.getItemName());

        // 验证数据库只被查询了一次
        verify(valueAddedItemTypeService, times(1)).getById(1L);
    }

    @Test
    void testCacheClear() {
        // 模拟数据库查询
        when(valueAddedItemTypeService.getById(1L)).thenReturn(mockItemType);

        // 第一次调用
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 1);

        // 清理缓存
        deliveryOrderService.clearItemTypeCache(1);

        // 再次调用 - 应该重新查询数据库
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 1);

        // 验证数据库被查询了两次
        verify(valueAddedItemTypeService, times(2)).getById(1L);
    }

    @Test
    void testCacheClearAll() {
        // 模拟数据库查询
        when(valueAddedItemTypeService.getById(any())).thenReturn(mockItemType);

        // 调用多个不同的ID
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 1);
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 2);

        // 清理所有缓存
        deliveryOrderService.clearItemTypeCache(null);

        // 再次调用 - 应该重新查询数据库
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 1);
        ReflectionTestUtils.invokeMethod(deliveryOrderService, "getValueAddedItemTypeWithCache", 2);

        // 验证数据库被查询了4次（每个ID各2次）
        verify(valueAddedItemTypeService, times(4)).getById(any());
    }

    @Test
    void testNullValueHandling() {
        // 测试null值处理
        ValueAddedItemType result = (ValueAddedItemType) ReflectionTestUtils.invokeMethod(
                deliveryOrderService, "getValueAddedItemTypeWithCache", (Integer) null);
        
        assertNull(result);
        
        // 验证没有调用数据库
        verify(valueAddedItemTypeService, never()).getById(any());
    }
}
