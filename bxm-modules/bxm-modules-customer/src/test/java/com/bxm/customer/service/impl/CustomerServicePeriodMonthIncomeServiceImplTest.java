package com.bxm.customer.service.impl;

import com.bxm.customer.domain.CustomerServicePeriodMonthIncome;
import com.bxm.customer.service.ICustomerServicePeriodMonthIncomeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class CustomerServicePeriodMonthIncomeServiceImplTest {

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Test
    void updateCustomerServiceIncome() {
        CustomerServicePeriodMonthIncome income = customerServicePeriodMonthIncomeService.getById(3958L);
        customerServicePeriodMonthIncomeService.updateCustomerServiceIncome(income);
    }
}