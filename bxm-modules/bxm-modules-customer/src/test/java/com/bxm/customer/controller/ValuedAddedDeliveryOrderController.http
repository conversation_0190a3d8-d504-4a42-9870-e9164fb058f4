### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController核心业务流程
### 基于DeliveryOrderUpsertReq参数的主流程测试场景
###
### 字段说明：
### - valueAddedItemTypeId: 增值事项类型 (1-6)
### - taxpayerType: 纳税性质 (1-小规模纳税人, 2-一般纳税人)
### - accountingInfo: 账务类型信息 (STANDARD-标准账务, NON_STANDARD-非标账务)
### - nationalTaxAccount: 国税账号对象
### - personalTaxAccount: 个税账号对象
###
### 核心测试用例覆盖：
### 1-20: 核心业务流程测试（新增、更新、验证、业务场景）

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjFkNWI3OWQwLThhZWEtNDU1NS1hMWNlLWE5ZGVmNzk4ZWNiNSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.eYd0eC7jKwkVj7mlpWsax3KHffnW6rUDVYFC2gt10DeW1VBI11qtuLjaVOesnZzyZwA0M0KHbaygW3dfGKn_Nw

### ========================================
### 1. 正常新增场景 - 小规模纳税人 + 标准账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "提供银行流水、进销票、财务报表等材料",
  "ddl": "2025-12-31",
  "customerId": 1001
}

### ========================================
### 2. 正常新增场景 - 一般纳税人 + 非标账务
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "一般纳税人测试公司",
  "creditCode": "91110000123456789X",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "110000199002022345",
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "高新技术企业账务处理",
  "ddl": "2025-06-30",
  "customerId": 1002
}

### ========================================
### 3. 正常更新场景 - 包含ID的更新操作
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "VAD2508051430003E3F",
  "customerName": "更新测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 3,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH"]
  },
  "requirements": "更新后的交付要求",
  "ddl": "2025-11-30",
  "status": "DELIVERY_COMPLETED"
}

### ========================================
### 4. 必填字段验证 - 客户名称为空（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 5. 必填字段验证 - 信用代码格式错误（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "格式错误测试公司",
  "creditCode": "invalid_credit_code",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1
}

### ========================================
### 6. 必填字段验证 - 纳税性质超出范围（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "纳税性质错误测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 5,
  "valueAddedItemTypeId": 1
}

### ========================================
### 7. 账务类型验证 - 标准账务包含子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "账务类型验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingInfo": {
    "mainType": "STANDARD",
    "subTypes": ["HIGH_TECH"]
  }
}

### ========================================
### 8. 账务类型验证 - 非标账务缺少子类型（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "非标账务验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "accountingInfo": {
    "mainType": "NON_STANDARD"
  }
}

### ========================================
### 9. 账期验证 - 开始时间晚于结束时间（应失败）
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "账期验证测试公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 1,
  "accountingPeriodStart": 202512,
  "accountingPeriodEnd": 202501
}

### ========================================
### 10. 国税账号业务场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "国税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 5,
  "itemName": "国税账号",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "nationalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "NationalTax123!",
    "loginMethod": "账号密码登录",
    "realNameAgent": "张三",
    "mobile": "***********",
    "idNumber": "350105199001011234",
    "remark": "国税账号测试备注",
    "operationType": 1
  }
}

### ========================================
### 11. 个税账号业务场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "个税账号测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 6,
  "itemName": "个税账号",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199002022345",
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "personalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "PersonalTax456!",
    "loginMethod": "手机验证码登录",
    "realNameAgent": "李四",
    "mobile": "***********",
    "idNumber": "350105199002022345",
    "remark": "个税账号测试备注",
    "operationType": 1
  }
}

### ========================================
### 12. 双账号组合测试
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "双账号组合测试企业",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "综合税务服务",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199003033456",
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH"]
  },
  "nationalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "NationalTax789!",
    "loginMethod": "CA证书登录",
    "realNameAgent": "王五",
    "mobile": "***********",
    "idNumber": "350105199003033456",
    "operationType": 1
  },
  "personalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "PersonalTax012!",
    "loginMethod": "人脸识别登录",
    "realNameAgent": "赵六",
    "mobile": "***********",
    "idNumber": "350105199004044567",
    "operationType": 1
  }
}

### ========================================
### 13. saveStatus 方法测试 - 正常场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430003E3F",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "totalWithholdingAmount": 1500.00,
  "remark": "待交付状态保存测试"
}

### ========================================
### 14. saveStatus 方法测试 - 验证失败场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/saveStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "remark": "交付单编号为空测试"
}

### ========================================
### 15. 查询接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?customerName=测试企业&pageNum=1&pageSize=10
Authorization: {{authorization}}

### ========================================
### 16. 生成交付单编号接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Authorization: {{authorization}}

### ========================================
### 17. 根据编号查询接口测试
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getByOrderNo/VAD2508051430003E3F
Authorization: {{authorization}}

### ========================================
### 18. 完整业务场景 - 包含所有主要字段
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
"deliveryOrderNo": "VAD2508051430003E3F",
  "title": "完整业务场景测试交付单",
  "customerId": 1001,
  "customerName": "完整业务场景测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxNo": "91350105MA2Y9DXW8L",
  "taxpayerType": 2,
  "valueAddedItemTypeId": 4,
  "itemName": "完整业务测试",
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "syncHandlingFee": true,
  "syncReassignment": true,
  "syncContactPerson": true,
  "syncAccountChange": true,
  "modifyDueDate": true,
  "accountingInfo": {
    "mainType": "NON_STANDARD",
    "subTypes": ["HIGH_TECH", "VOUCHER_BASED"]
  },
  "requirements": "完整业务场景的交付要求，包含所有必要的材料和流程说明",
  "taxRequirement": "增值税税负率控制在3%以内",
  "ddl": "2025-12-31",
  "initiateDeptId": 1,
  "businessDeptId": 111,
  "businessTopDeptId": 100,
  "status": "DRAFT",
  "createUid": 1001,
  "nationalTaxAccount": {
    "accountNumber": "91350105MA2Y9DXW8L",
    "password": "CompleteBusiness789!",
    "loginMethod": "企业级登录",
    "realNameAgent": "完整业务测试专员",
    "mobile": "***********",
    "idNumber": "350105199001011234",
    "remark": "完整业务场景国税账号测试",
    "operationType": 1
  }
}

### ========================================
### 19. 改账业务场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "改账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "改账",
  "customerId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199001011234",
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "改账场景测试：需要根据客户服务ID更新相关字段信息",
  "ddl": "2025-12-31"
}

### ========================================
### 20. 补账业务场景
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "customerName": "补账测试企业有限公司",
  "creditCode": "91350105MA2Y9DXW8L",
  "taxpayerType": 1,
  "valueAddedItemTypeId": 2,
  "itemName": "补账",
  "customerId": 1,
  "accountingPeriodStart": 202501,
  "accountingPeriodEnd": 202512,
  "contactMobile": "***********",
  "contactIdNumber": "350105199002022345",
  "accountingInfo": {
    "mainType": "STANDARD"
  },
  "requirements": "补账场景测试：自动补全202501-202512期间缺失的账期记录",
  "ddl": "2025-12-31"
}
