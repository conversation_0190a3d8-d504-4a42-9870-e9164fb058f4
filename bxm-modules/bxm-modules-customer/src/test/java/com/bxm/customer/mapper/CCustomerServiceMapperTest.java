package com.bxm.customer.mapper;

import com.bxm.common.core.utils.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class CCustomerServiceMapperTest {

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Test
    void updateCustomerServiceIncomeTest() {
        Integer thisMonth = DateUtils.getNowPeriod();
        Map<String, LocalDate> thisYearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
        Map<String, LocalDate> thisSeasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
        Map<String, LocalDate> this12MonthMap = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
        customerServiceMapper.updateCustomerServiceIncome(thisMonth,
                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("endDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("endDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("endDate"), DateUtils.YYYYMM)), null);
    }
}