### 增值交付单Controller HTTP测试文件
### 测试ValuedAddedDeliveryOrderController的query方法
### 基于DeliveryOrderQuery参数完善的真实测试场景
###

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImE5ZDZjNjc3LTFjN2ItNGQ0Zi05YjQ4LWI4MDFjZjZlNDRlYyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.Wp4DvfW6fcrplAmTD9bqkeEKcnbV2fpDYva-EIyzBFfPyY_fW2tu10UCLJj-110XM_xxOiLzORqq8xBPY6haCg
### ========================================
### 1. 测试查询所有增值交付单（无条件分页查询）
### 功能验证：分页总数正确获取、null字段过滤、状态描述转换
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?pageNum=1&pageSize=5
Authorization: {{authorization}}

### ========================================
### 2. 根据交付单编号查询详细信息
### 功能验证：根据交付单编号查询增值交付单详情，包含国税账号、个税账号、员工信息列表、交付文件等扩展数据
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getDeliveryOrder/*******************
Authorization: {{authorization}}

### 使用场景：需要认证的环境下测试
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType
Authorization: {{authorization}}
