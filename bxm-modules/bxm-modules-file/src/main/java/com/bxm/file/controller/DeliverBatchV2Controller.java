package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.annotation.Logical;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.file.bean.dto.BatchDeliverConfirmResultDTO;
import com.bxm.file.bean.dto.CheckResult;
import com.bxm.file.bean.dto.CheckV2Result;
import com.bxm.file.bean.dto.ConfirmResult;
import com.bxm.file.service.DeliverBatchService;
import com.bxm.file.service.DeliverBatchV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/v2/deliver/batch")
@Api(tags = "批量交付(新)")
public class DeliverBatchV2Controller {

    @Autowired
    private DeliverBatchV2Service deliverBatchV2Service;

    @PostMapping("/batchDeliverCheck")
    @ApiOperation("上传模板校验，返回批次号")
    public Result<String> batchDeliverCheck(@RequestParam("deliverType") @ApiParam("交付类型，51-医社保，52-任务，53-入账，54-客户，55-汇算，56-年报，57-银行流水，58-入账，59-改账，60-材料交接（银行流水），61-材料交接（普通入账），62-材料交接（凭票入账），63-残保金，64-账务，65-税务，66-次报，67-员工账号，68-质检") Integer deliverType,
                                            @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-确认，7-更新附件，8-驳回，9-银行流水任务新建，10-银行流水任务完成，11-更新利润取数，12-更新银行账户，13-新建（账务），14-交付（账务），15-（处理异常），16-RPA更新，17-更新利润，18-利润取数，19-补充材料，20-创建材料交接，21-系统账号，22-服务标签，23-核对材料，24-利润取数，25-发票入账，26-银行流水银企提取，27-银行流水纸质上传，28-银行流水文件检验，29-银行流水凭证生成，30-材料入账通知，31-个税申报，32-个税扣款，33-个税检查，34-个税申报表下载，35-个税申报结果查询，36-个税扣款结果查询，37-个税检查结果查询") Integer operType,
                                            @RequestParam("excelFile") @ApiParam("excl文件") MultipartFile excelFile,
                                            @RequestParam(value = "excelFileUrl", required = false) @ApiParam("excl文件短链接") String excelFileUrl,
                                            @RequestParam(value = "excelFileName", required = false) @ApiParam("excl文件名称") String excelFileName,
                                            @RequestParam(value = "medicalZipFile", required = false) @ApiParam("医保附件文件/通用附件") MultipartFile medicalZipFile,
                                            @RequestParam(value = "medicalZipFileUrl", required = false) @ApiParam("医保附件文件/通用附件短链接") String medicalZipFileUrl,
                                            @RequestParam(value = "medicalZipFileName", required = false) @ApiParam("医保附件文件/通用附件名称") String medicalZipFileName,
                                            @RequestParam(value = "socialZipFile", required = false) @ApiParam("社保附件文件") MultipartFile socialZipFile,
                                            @RequestParam(value = "zipFile", required = false) @ApiParam("附件文件") MultipartFile zipFile,
                                            @RequestParam(value = "period", required = false) @ApiParam("账期") Integer period,
                                            @RequestParam(value = "adminUserId", required = false) @ApiParam("监管人用户id， 仅任务需要") Long adminUserId,
                                            @RequestParam(value = "commitDeptId", required = false) @ApiParam("提交小组id") Long commitDeptId,
                                            @RequestParam(value = "title", required = false) @ApiParam("材料交接单的标题") String title,
                                            @RequestParam(value = "qualityCheckingItemIds", required = false) @ApiParam("质检事项id，多个用逗号隔开，若选择所有项目，这个字段传空") String qualityCheckingItemIds,
                                            @RequestHeader("deptId") Long deptId) throws Exception {
        return Result.ok(deliverBatchV2Service.uploadFilesV2(excelFile, medicalZipFile, socialZipFile, deliverType, operType, period, deptId, adminUserId, commitDeptId, excelFileUrl, excelFileName, medicalZipFileUrl, medicalZipFileName, zipFile, title, qualityCheckingItemIds));
    }

    @GetMapping("/progress/{batchNo}")
    @ApiOperation("查询校验进度")
    public Result<CheckV2Result> getProgress(@PathVariable String batchNo) {
        return Result.ok(deliverBatchV2Service.getProgress(batchNo));
    }

    @PostMapping("/confirm/{batchNo}")
    @ApiOperation("确认数据，返回处理结果文案")
    public Result confirmData(@PathVariable @ApiParam("批次号") String batchNo,
                                                            @RequestHeader("deptId") Long deptId) {
        deliverBatchV2Service.confirmData(batchNo, deptId);
        return Result.ok();
    }

    @GetMapping("/getConfirmResult/{batchNo}")
    @ApiOperation("查询处理进度")
    public Result<ConfirmResult> getConfirmResult(@PathVariable String batchNo) {
        return Result.ok(deliverBatchV2Service.getConfirmResult(batchNo));
    }

    @PostMapping("/downloadErrorFile")
    @ApiOperation("下载异常文件")
    public void downloadErrorFile(@RequestParam("batchNo") @ApiParam("批次号") String batchNo, HttpServletResponse response) throws IOException {
        deliverBatchV2Service.downloadErrorFile(batchNo, response);
    }

    @GetMapping("/startThread")
    public void startThread(String taskId) {
        deliverBatchV2Service.startThread(taskId);
    }

    @GetMapping("/stopThread")
    public void stopThread(String taskId) {
        deliverBatchV2Service.stopThread(taskId);
    }

    @GetMapping("/stopAnalysisTask")
    @InnerAuth
    public Result stopAnalysisTask(@RequestParam("taskId") String taskId) {
        deliverBatchV2Service.stopAnalysisTask(taskId);
        return Result.ok();
    }
}
