<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.openapi.mapper.OpenApiNoticeRecordMapper">
    
    <resultMap type="com.bxm.openapi.domain.OpenApiNoticeRecord" id="OpenApiNoticeRecordResult">
        <result property="id"    column="id"    />
        <result property="noticeSource"    column="notice_source"    />
        <result property="noticeTarget"    column="notice_target"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="noticeFunction"    column="notice_function"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOpenApiNoticeRecordVo">
        select id, notice_source, notice_target, notice_content, notice_function, is_del, create_by, create_time, update_by, update_time from c_open_api_notice_record
    </sql>

    <select id="selectOpenApiNoticeRecordList" parameterType="com.bxm.openapi.domain.OpenApiNoticeRecord" resultMap="OpenApiNoticeRecordResult">
        <include refid="selectOpenApiNoticeRecordVo"/>
        <where>  
            <if test="noticeSource != null  and noticeSource != ''"> and notice_source = #{noticeSource}</if>
            <if test="noticeTarget != null  and noticeTarget != ''"> and notice_target = #{noticeTarget}</if>
            <if test="noticeContent != null  and noticeContent != ''"> and notice_content = #{noticeContent}</if>
            <if test="noticeFunction != null  and noticeFunction != ''"> and notice_function = #{noticeFunction}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectOpenApiNoticeRecordById" parameterType="Long" resultMap="OpenApiNoticeRecordResult">
        <include refid="selectOpenApiNoticeRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOpenApiNoticeRecord" parameterType="com.bxm.openapi.domain.OpenApiNoticeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into c_open_api_notice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">notice_source,</if>
            <if test="noticeTarget != null and noticeTarget != ''">notice_target,</if>
            <if test="noticeContent != null and noticeContent != ''">notice_content,</if>
            <if test="noticeFunction != null and noticeFunction != ''">notice_function,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">#{noticeSource},</if>
            <if test="noticeTarget != null and noticeTarget != ''">#{noticeTarget},</if>
            <if test="noticeContent != null and noticeContent != ''">#{noticeContent},</if>
            <if test="noticeFunction != null and noticeFunction != ''">#{noticeFunction},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOpenApiNoticeRecord" parameterType="com.bxm.openapi.domain.OpenApiNoticeRecord">
        update c_open_api_notice_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeSource != null and noticeSource != ''">notice_source = #{noticeSource},</if>
            <if test="noticeTarget != null and noticeTarget != ''">notice_target = #{noticeTarget},</if>
            <if test="noticeContent != null and noticeContent != ''">notice_content = #{noticeContent},</if>
            <if test="noticeFunction != null and noticeFunction != ''">notice_function = #{noticeFunction},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiNoticeRecordById" parameterType="Long">
        delete from c_open_api_notice_record where id = #{id}
    </delete>

    <delete id="deleteOpenApiNoticeRecordByIds" parameterType="String">
        delete from c_open_api_notice_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>