package com.bxm.system.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.system.api.domain.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.domain.dto.CustomerBusinessLogDTO;
import com.bxm.system.mapper.SysEmployeeMapper;
import com.bxm.system.mapper.SysUserMapper;
import com.bxm.system.service.ISysDeptService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.system.mapper.BusinessLogMapper;
import com.bxm.system.domain.BusinessLog;
import com.bxm.system.service.IBusinessLogService;
import org.springframework.util.ObjectUtils;

/**
 * 业务统一操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class BusinessLogServiceImpl extends ServiceImpl<BusinessLogMapper, BusinessLog> implements IBusinessLogService
{
    @Autowired
    private BusinessLogMapper businessLogMapper;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private SysEmployeeMapper sysEmployeeMapper;

    /**
     * 查询业务统一操作记录
     * 
     * @param id 业务统一操作记录主键
     * @return 业务统一操作记录
     */
    @Override
    public BusinessLog selectBusinessLogById(Long id)
    {
        return businessLogMapper.selectBusinessLogById(id);
    }

    /**
     * 查询业务统一操作记录列表
     * 
     * @param businessLog 业务统一操作记录
     * @return 业务统一操作记录
     */
    @Override
    public List<BusinessLog> selectBusinessLogList(BusinessLog businessLog)
    {
        return businessLogMapper.selectBusinessLogList(businessLog);
    }

    /**
     * 新增业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    @Override
    public int insertBusinessLog(BusinessLog businessLog)
    {
        if (Objects.isNull(businessLog.getCreateTime())) {
            businessLog.setCreateTime(DateUtils.getNowDate());
        }
        BusinessLog lastLog = businessLogMapper.selectOne(new LambdaQueryWrapper<BusinessLog>()
                .eq(BusinessLog::getBusinessType, businessLog.getBusinessType())
                .eq(BusinessLog::getBusinessId, businessLog.getBusinessId())
                .orderByDesc(BusinessLog::getCreateTime).last("limit 1"));
        if (!Objects.isNull(lastLog)) {
            businessLog.setOperTimeDifference(DateUtils.localDateToSecond(businessLog.getCreateTime()) - DateUtils.localDateToSecond(lastLog.getCreateTime()));
        }
        if (!Objects.isNull(businessLog.getOperTimeDifference())) {
            if (businessLog.getOperTimeDifference() < 0L) {
                businessLog.setOperTimeDifference(0L);
            }
        }
        Set<Long> topDeptIds = new HashSet<>();
        Set<Long> companyDeptIds = new HashSet<>();
        Set<Long> lastDeptIds = new HashSet<>();
        Long deptId = null;
        List<SysEmployee> employees = Lists.newArrayList();
        if (!Objects.isNull(businessLog.getOperUserId()) && !Objects.equals(0L, businessLog.getOperUserId())) {
            employees = sysEmployeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .eq(SysEmployee::getUserId, businessLog.getOperUserId()));
            if (!ObjectUtils.isEmpty(employees)) {
                List<Long> deptIds = employees.stream().map(SysEmployee::getDeptId).collect(Collectors.toList());
                List<SysDept> depts = sysDeptService.getBaseMapper().selectBatchIds(deptIds);
                if (!ObjectUtils.isEmpty(depts)) {
                    for (SysDept dept : depts) {
                        if (dept.getLevel() == 4) {
                            topDeptIds.add(Long.parseLong(dept.getAncestors().split(",")[1]));
                            companyDeptIds.add(Long.parseLong(dept.getAncestors().split(",")[2]));
                            lastDeptIds.add(dept.getDeptId());
                        } else if (dept.getLevel() == 3) {
                            topDeptIds.add(Long.parseLong(dept.getAncestors().split(",")[1]));
                            companyDeptIds.add(Long.parseLong(dept.getAncestors().split(",")[2]));
                        } else if (dept.getLevel() == 2) {
                            topDeptIds.add(dept.getParentId());
                            companyDeptIds.add(dept.getDeptId());
                        } else {
                            topDeptIds.add(dept.getDeptId());
                        }
                    }
                }
            }
        }
        businessLog.setTopDeptId(ObjectUtils.isEmpty(topDeptIds) ? "" : topDeptIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        businessLog.setCompanyDeptId(ObjectUtils.isEmpty(companyDeptIds) ? "" : companyDeptIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        businessLog.setLastDeptId(ObjectUtils.isEmpty(lastDeptIds) ? "" : lastDeptIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        if (!Objects.isNull(businessLog.getDeptId()) && !Objects.equals(0L, businessLog.getDeptId())) {
            deptId = businessLog.getDeptId();
        } else {
            if (!Objects.isNull(businessLog.getOperUserId()) && !Objects.equals(0L, businessLog.getOperUserId())) {
                if (!ObjectUtils.isEmpty(employees)) {
                    deptId = employees.get(0).getDeptId();
                }
            }
        }
        businessLog.setOperDeptType(2);
        if (!Objects.isNull(deptId)) {
            SysDept sysDept = sysDeptService.getById(deptId);
            if (!Objects.isNull(sysDept)) {
                businessLog.setOperDeptType(sysDept.getDeptType());
            }
        }
        return businessLogMapper.insertBusinessLog(businessLog);
    }

    /**
     * 修改业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    @Override
    public int updateBusinessLog(BusinessLog businessLog)
    {
        businessLog.setUpdateTime(DateUtils.getNowDate());
        return businessLogMapper.updateBusinessLog(businessLog);
    }

    /**
     * 批量删除业务统一操作记录
     * 
     * @param ids 需要删除的业务统一操作记录主键
     * @return 结果
     */
    @Override
    public int deleteBusinessLogByIds(Long[] ids)
    {
        return businessLogMapper.deleteBusinessLogByIds(ids);
    }

    /**
     * 删除业务统一操作记录信息
     * 
     * @param id 业务统一操作记录主键
     * @return 结果
     */
    @Override
    public int deleteBusinessLogById(Long id)
    {
        return businessLogMapper.deleteBusinessLogById(id);
    }

    @Override
    public IPage<BusinessLog> getByBusinessIdAndBusinessType(Integer businessType, Long businessId, Integer pageNum, Integer pageSize) {
        Page<BusinessLog> page = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<BusinessLog>()
                .eq(BusinessLog::getBusinessId, businessId)
                .eq(BusinessLog::getBusinessType, businessType)
                .orderByDesc(BusinessLog::getCreateTime, BusinessLog::getId));
        if (!ObjectUtils.isEmpty(page)) {
            page.getRecords().forEach(p -> {
                if (!StringUtils.isEmpty(p.getOperImages()) && (p.getOperImages().startsWith("{") || p.getOperImages().startsWith("["))) {
                    List<CommonFileVO> files = JSONArray.parseArray(p.getOperImages(), CommonFileVO.class);
                    files.forEach(f -> f.setFullFileUrl(remoteFileService.getFullFileUrl(f.getFileUrl()).getDataThrowException(false)));
                    p.setOperImages(JSONArray.toJSONString(files));
                }
            });
        }
        return page;
    }

    @Override
    public IPage<CustomerBusinessLogDTO> getByCustomerBusinessLog(String operName, String keyWord, String operTimeStart, String operTimeEnd, String operType, Long deptId, String operContent, Integer pageNum, Integer pageSize) {
        IPage<CustomerBusinessLogDTO> iPage = new Page<>(pageNum, pageSize);
        UserDeptDTO deptDTO = sysDeptService.userDeptList(SecurityUtils.getUserId(), deptId);
        if (!deptDTO.getIsAdmin() && ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
            return iPage;
        }
        if (!StringUtils.isEmpty(operTimeStart)) {
            operTimeStart = operTimeStart + " 00:00:00";
        }
        if (!StringUtils.isEmpty(operTimeEnd)) {
            operTimeEnd = operTimeEnd + " 23:59:59";
        }
        List<CustomerBusinessLogDTO> data = baseMapper.selectCustomerBusinessLog(iPage, operName, keyWord, operTimeStart, operTimeEnd, operType, deptDTO, operContent);
        iPage.setRecords(data);
        return iPage;
    }

    @Override
    public List<BusinessLogDTO> getByBatchBusinessIdAndBusinessType(BatchGetBusinessLogVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessIds()) || Objects.isNull(vo.getBusinessType())) {
            return Collections.emptyList();
        }
        List<BusinessLog> businessLogs = list(new LambdaQueryWrapper<BusinessLog>()
                .eq(BusinessLog::getBusinessType, vo.getBusinessType())
                .in(BusinessLog::getBusinessId, vo.getBusinessIds())
                .orderByDesc(BusinessLog::getCreateTime));
        return businessLogs.stream().map(p -> {
            BusinessLogDTO dto = new BusinessLogDTO()
                    .setId(p.getId())
                    .setBusinessId(p.getBusinessId())
                    .setBusinessType(p.getBusinessType())
                    .setOperType(p.getOperType())
                    .setOperName(p.getOperName())
                    .setOperRemark(p.getOperRemark())
                    .setOperImages(p.getOperImages())
                    .setOperContent(p.getOperContent())
                    .setCreateTime(p.getCreateTime());
            if (!StringUtils.isEmpty(p.getOperImages())) {
                List<CommonFileVO> files = JSONArray.parseArray(p.getOperImages(), CommonFileVO.class);
                Map<String, String> urlMap = remoteFileService.batchGetFileInfo(files.stream().map(CommonFileVO::getFileUrl).collect(Collectors.toList())).getDataThrowException()
                        .stream().collect(Collectors.toMap(RemoteAliFileDTO::getUrl, RemoteAliFileDTO::getFullUrl, (k1, k2) -> k1));
                files.forEach(f -> f.setFullFileUrl(urlMap.getOrDefault(f.getFileUrl(), "")));
                dto.setFiles(files);
            }
            return dto;
        }).collect(Collectors.toList());
    }
}
