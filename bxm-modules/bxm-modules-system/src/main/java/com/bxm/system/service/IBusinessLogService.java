package com.bxm.system.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.api.domain.BatchGetBusinessLogVO;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.domain.BusinessLog;
import com.bxm.system.domain.dto.CustomerBusinessLogDTO;

/**
 * 业务统一操作记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface IBusinessLogService extends IService<BusinessLog>
{
    /**
     * 查询业务统一操作记录
     * 
     * @param id 业务统一操作记录主键
     * @return 业务统一操作记录
     */
    public BusinessLog selectBusinessLogById(Long id);

    /**
     * 查询业务统一操作记录列表
     * 
     * @param businessLog 业务统一操作记录
     * @return 业务统一操作记录集合
     */
    public List<BusinessLog> selectBusinessLogList(BusinessLog businessLog);

    /**
     * 新增业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    public int insertBusinessLog(BusinessLog businessLog);

    /**
     * 修改业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    public int updateBusinessLog(BusinessLog businessLog);

    /**
     * 批量删除业务统一操作记录
     * 
     * @param ids 需要删除的业务统一操作记录主键集合
     * @return 结果
     */
    public int deleteBusinessLogByIds(Long[] ids);

    /**
     * 删除业务统一操作记录信息
     * 
     * @param id 业务统一操作记录主键
     * @return 结果
     */
    public int deleteBusinessLogById(Long id);

    IPage<BusinessLog> getByBusinessIdAndBusinessType(Integer businessType, Long businessId, Integer pageNum, Integer pageSize);

    IPage<CustomerBusinessLogDTO> getByCustomerBusinessLog(String operName, String keyWord, String operTimeStart, String operTimeEnd, String operType, Long deptId, String operContent, Integer pageNum, Integer pageSize);

    List<BusinessLogDTO> getByBatchBusinessIdAndBusinessType(BatchGetBusinessLogVO vo);
}
