package com.bxm.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.system.api.domain.BatchGetBusinessLogVO;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.RemoteCustomerServiceLogSearchVO;
import com.bxm.system.domain.BusinessLog;
import com.bxm.system.domain.dto.CustomerBusinessLogDTO;
import com.bxm.system.domain.vo.CustomerServiceLogSearchVO;
import com.bxm.system.service.AsyncService;
import com.bxm.system.service.IBusinessLogService;
import com.bxm.system.service.IDownloadRecordService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 业务统一操作记录Controller
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@RestController
@RequestMapping("/businessLog")
@Api(tags = "业务统一操作记录")
public class BusinessLogController extends BaseController
{
    @Autowired
    private IBusinessLogService businessLogService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    /**
     * 查询业务统一操作记录列表
     */
    @RequiresPermissions("system:businessLog:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询业务统一操作记录列表", notes = "查询业务统一操作记录列表")
    public TableDataInfo list(BusinessLog businessLog)
    {
        startPage();
        List<BusinessLog> list = businessLogService.selectBusinessLogList(businessLog);
        return getDataTable(list);
    }

    /**
     * 导出业务统一操作记录列表
     */
    @RequiresPermissions("system:businessLog:export")
    @Log(title = "业务统一操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出业务统一操作记录列表", notes = "导出业务统一操作记录列表")
    public void export(HttpServletResponse response, BusinessLog businessLog)
    {
        List<BusinessLog> list = businessLogService.selectBusinessLogList(businessLog);
        ExcelUtil<BusinessLog> util = new ExcelUtil<BusinessLog>(BusinessLog.class);
        util.exportExcel(response, list, "业务统一操作记录数据");
    }

    /**
     * 获取业务统一操作记录详细信息
     */
    @RequiresPermissions("system:businessLog:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取业务统一操作记录详细信息", notes = "获取业务统一操作记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(businessLogService.selectBusinessLogById(id));
    }

    /**
     * 新增业务统一操作记录
     */
    @RequiresPermissions("system:businessLog:add")
    @Log(title = "业务统一操作记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增业务统一操作记录", notes = "新增业务统一操作记录")
    public AjaxResult add(@RequestBody BusinessLog businessLog)
    {
        return toAjax(businessLogService.insertBusinessLog(businessLog));
    }

    /**
     * 修改业务统一操作记录
     */
    @RequiresPermissions("system:businessLog:edit")
    @Log(title = "业务统一操作记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改业务统一操作记录", notes = "修改业务统一操作记录")
    public AjaxResult edit(@RequestBody BusinessLog businessLog)
    {
        return toAjax(businessLogService.updateBusinessLog(businessLog));
    }

    /**
     * 删除业务统一操作记录
     */
    @RequiresPermissions("system:businessLog:remove")
    @Log(title = "业务统一操作记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation(value = "删除业务统一操作记录", notes = "删除业务统一操作记录")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessLogService.deleteBusinessLogByIds(ids));
    }

    @PostMapping("/addLog")
    public AjaxResult addLog(@RequestBody BusinessLog businessLog)
    {
        return toAjax(businessLogService.insertBusinessLog(businessLog));
    }

    @PostMapping("/batchAddLog")
    public AjaxResult batchAddLog(@RequestBody List<BusinessLog> businessLog)
    {
        businessLog.forEach(p -> {
            if (Objects.isNull(p.getCreateTime())) {
                p.setCreateTime(DateUtils.getNowDate());
            }
        });
        return toAjax(businessLogService.saveBatch(businessLog));
    }

    @GetMapping("/getByBusinessIdAndBusinessType")
    @ApiOperation("统一操作记录，businessType：1-服务，2-交付单")
    public AjaxResult getByBusinessIdAndBusinessType(@RequestParam("businessType") Integer businessType,
                                                     @RequestParam("businessId") Long businessId,
                                                     @RequestParam("pageNum") Integer pageNum,
                                                     @RequestParam("pageSize") Integer pageSize) {
        return success(businessLogService.getByBusinessIdAndBusinessType(businessType, businessId, pageNum, pageSize));
    }

    @GetMapping("/getCustomerBusinessLog")
    @ApiOperation("获取客户操作记录,权限字符：sys:businessLog:customerBusinessLog")
    @RequiresPermissions("sys:businessLog:customerBusinessLog")
    public Result<IPage<CustomerBusinessLogDTO>> getByCustomerBusinessLog(@RequestParam(value = "operName", required = false) @ApiParam("操作人") String operName,
                                                                          @RequestParam(value = "keyWord", required = false) @ApiParam("关键词") String keyWord,
                                                                          @RequestParam(value = "operTimeStart", required = false) @ApiParam("操作时间开始，yyyy-MM-dd") String operTimeStart,
                                                                          @RequestParam(value = "operTimeEnd", required = false) @ApiParam("操作时间结束，yyyy-MM-dd") String operTimeEnd,
                                                                          @RequestParam(value = "operType", required = false) @ApiParam("操作类型") String operType,
                                                                          @RequestParam(value = "operContent", required = false) @ApiParam("操作内容") String operContent,
                                                                          @RequestParam("pageNum") Integer pageNum,
                                                                          @RequestParam("pageSize") Integer pageSize,
                                                                          @RequestHeader("deptId") Long deptId) {
        return Result.ok(businessLogService.getByCustomerBusinessLog(operName, keyWord, operTimeStart, operTimeEnd, operType, deptId, operContent, pageNum, pageSize));
    }

    @PostMapping("/exportCustomerBusinessLog")
    @ApiOperation("导出客户操作记录")
    public void exportCustomerBusinessLog(@RequestParam(value = "operName", required = false) @ApiParam("操作人") String operName,
                                          @RequestParam(value = "keyWord", required = false) @ApiParam("关键词") String keyWord,
                                          @RequestParam(value = "operTimeStart", required = false) @ApiParam("操作时间开始，yyyy-MM-dd") String operTimeStart,
                                          @RequestParam(value = "operTimeEnd", required = false) @ApiParam("操作时间结束，yyyy-MM-dd") String operTimeEnd,
                                          @RequestParam(value = "operType", required = false) @ApiParam("操作类型") String operType,
                                          @RequestParam(value = "operContent", required = false) @ApiParam("操作内容") String operContent,
                                          @RequestHeader("deptId") Long deptId,
                                          HttpServletResponse response) {
        ExcelUtil<CustomerBusinessLogDTO> util = new ExcelUtil<>(CustomerBusinessLogDTO.class);
        List<CustomerBusinessLogDTO> records = businessLogService.getByCustomerBusinessLog(operName, keyWord, operTimeStart, operTimeEnd, operType, deptId, operContent, 1, 10000).getRecords();
        if (!ObjectUtils.isEmpty(records)) {
            records.forEach(r -> r.setOperateTime(r.getOperTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }
        util.exportExcel(response, records, "客户操作记录");
    }

    @PostMapping("/exportCustomerBusinessLogAndUpload")
    @ApiOperation("导出客户操作记录(上传oss)")
    public Result exportCustomerBusinessLogAndUpload(@RequestParam(value = "operName", required = false) @ApiParam("操作人") String operName,
                                          @RequestParam(value = "keyWord", required = false) @ApiParam("关键词") String keyWord,
                                          @RequestParam(value = "operTimeStart", required = false) @ApiParam("操作时间开始，yyyy-MM-dd") String operTimeStart,
                                          @RequestParam(value = "operTimeEnd", required = false) @ApiParam("操作时间结束，yyyy-MM-dd") String operTimeEnd,
                                          @RequestParam(value = "operType", required = false) @ApiParam("操作类型") String operType,
                                          @RequestParam(value = "operContent", required = false) @ApiParam("操作内容") String operContent,
                                          @RequestHeader("deptId") Long deptId) {
        CustomerServiceLogSearchVO vo = new CustomerServiceLogSearchVO();
        vo.setKeyWord(keyWord);
        vo.setOperContent(operContent);
        vo.setOperTimeStart(operTimeStart);
        vo.setOperTimeEnd(operTimeEnd);
        vo.setOperType(operType);
        vo.setOperName(operName);
        vo.setDeptId(deptId);
        String title = "服务-操作记录" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.SERVICE_OPERATION_RECORD);
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerBusinessLogDTO> records = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerBusinessLogDTO> l = businessLogService.getByCustomerBusinessLog(vo.getOperName(), vo.getKeyWord(), vo.getOperTimeStart(), vo.getOperTimeEnd(), vo.getOperType(), vo.getDeptId(), vo.getOperContent(), vo.getPageNum(), vo.getPageSize()).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        records.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) records.size());
                if (!ObjectUtils.isEmpty(records)) {
                    records.forEach(r -> r.setOperateTime(r.getOperTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                }
                ExcelUtil<CustomerBusinessLogDTO> util = new ExcelUtil<>(CustomerBusinessLogDTO.class);
                asyncService.uploadExport(util, records, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @PostMapping("/exportCustomerBusinessLogAndUploadRetry")
    @ApiIgnore
    public Result exportCustomerBusinessLogAndUploadRetry(@RequestBody RemoteCustomerServiceLogSearchVO vo) {
        CompletableFuture.runAsync(() -> {
            try {
                List<CustomerBusinessLogDTO> records = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<CustomerBusinessLogDTO> l = businessLogService.getByCustomerBusinessLog(vo.getOperName(), vo.getKeyWord(), vo.getOperTimeStart(), vo.getOperTimeEnd(), vo.getOperType(), vo.getDeptId(), vo.getOperContent(), vo.getPageNum(), vo.getPageSize()).getRecords();
                    if (!ObjectUtils.isEmpty(l)) {
                        records.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) records.size());
                if (!ObjectUtils.isEmpty(records)) {
                    records.forEach(r -> r.setOperateTime(r.getOperTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                }
                ExcelUtil<CustomerBusinessLogDTO> util = new ExcelUtil<>(CustomerBusinessLogDTO.class);
                asyncService.uploadExport(util, records, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/getByBatchBusinessIdAndBusinessType")
    @ApiOperation("内部接口")
    public Result<List<BusinessLogDTO>> getByBatchBusinessIdAndBusinessType(@RequestBody BatchGetBusinessLogVO vo) {
        return Result.ok(businessLogService.getByBatchBusinessIdAndBusinessType(vo));
    }
}
