package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.UserConstants;
import com.bxm.common.core.enums.AllotType;
import com.bxm.common.core.enums.DataScopeTypeEnum;
import com.bxm.common.core.enums.DeptTypeEnum;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.text.Convert;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.SpringUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.datascope.annotation.DataScope;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysRole;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.domain.SysUserRole;
import com.bxm.system.domain.UserCompany;
import com.bxm.system.domain.dto.DeptAccountBalanceDTO;
import com.bxm.system.domain.dto.dept.DeptDTO;
import com.bxm.system.domain.vo.*;
import com.bxm.system.mapper.SysDeptMapper;
import com.bxm.system.mapper.SysEmployeeMapper;
import com.bxm.system.mapper.SysRoleMapper;
import com.bxm.system.mapper.SysUserRoleMapper;
import com.bxm.system.service.ISysDeptAccountBalanceDetailService;
import com.bxm.system.service.ISysDeptService;
import com.bxm.system.service.IUserCompanyService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements ISysDeptService
{
    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysEmployeeMapper employeeMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private IUserCompanyService userCompanyService;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private ISysDeptAccountBalanceDetailService sysDeptAccountBalanceDetailService;

    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept)
    {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门树结构信息
     * 
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDept dept)
    {
        List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
        return buildDeptTreeSelect(depts);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts)
    {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        for (SysDept dept : depts)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
    {
        List<SysEmployee> employees = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(depts)) {
            employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList()))
                    .exists("select 1 from sys_user su where su.user_id = sys_employee.user_id and su.`status` = '0'"));
        }
        Map<Long, List<SysEmployee>> employesMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(t -> new TreeSelect(t, employesMap)).collect(Collectors.toList());
    }

    @Override
    public List<TreeSelect> buildDeptTreeSelectNoEmployeeName(List<SysDept> depts)
    {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public List<TreeSelect> buildDeptTreeSelectWithEmployeeCount(List<SysDept> depts) {
        List<SysEmployee> employees = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(depts)) {
            employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList())));
        }
        Map<Long, List<SysEmployee>> employesMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(t -> new TreeSelect(t, employesMap, 1)).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId)
    {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     * 
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId)
    {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId)
    {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId)
    {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDept dept)
    {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     * 
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts))
            {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDept dept)
    {
        SysDept info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
        {
            throw new ServiceException("部门停用，不允许新增");
        }
        if (!Objects.isNull(dept.getParentId()) && remoteCustomerService.checkHasCustomerService(dept.getParentId()).getDataThrowException()) {
            throw new ServiceException("上级部门已分配服务，无法创建子部门");
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        if (Objects.isNull(dept.getParentId()) || Objects.equals(dept.getParentId(), 0L)) {
            dept.setLevel(1);
        } else {
            if (info.getLevel() == 1) {
                dept.setLevel(2);
            } else {
                dept.setLevel(4);
            }
        }
        dept.setDeptType(info.getDeptType());
        int result = deptMapper.insertDept(dept);
        if (dept.getLevel() == 4 && info.getLevel() == 4) {
            SysDept parentDeptUpdate = new SysDept();
            parentDeptUpdate.setDeptId(info.getDeptId());
            parentDeptUpdate.setLevel(3);
            deptMapper.updateById(parentDeptUpdate);
        }
        return result;
    }

    /**
     * 修改保存部门信息
     * 
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept)
    {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (!Objects.isNull(dept.getParentId()) && remoteCustomerService.checkHasCustomerService(dept.getParentId()).getDataThrowException()) {
            throw new ServiceException("上级部门已分配服务，无法创建子部门");
        }
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        if (Objects.isNull(dept.getParentId()) || Objects.equals(dept.getParentId(), 0L)) {
            dept.setLevel(1);
        } else {
            if (newParentDept.getLevel() == 1) {
                dept.setLevel(2);
            } else {
                dept.setLevel(4);
            }
        }
        dept.setDeptType(Objects.isNull(newParentDept) ? 1 : newParentDept.getDeptType());
        int result = deptMapper.updateDept(dept);
        if (newParentDept.getLevel() == 4 && dept.getLevel() == 4) {
            SysDept parentDeptUpdate = new SysDept();
            parentDeptUpdate.setDeptId(newParentDept.getDeptId());
            parentDeptUpdate.setLevel(3);
            deptMapper.updateById(parentDeptUpdate);
        }
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors()))
        {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     * 
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept)
    {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     * 
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
    {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId)
    {
        if (!Objects.isNull(deptId) && remoteCustomerService.checkHasCustomerService(deptId).getDataThrowException()) {
            throw new ServiceException("当前部门已分配服务，无法删除");
        }
        return deptMapper.deleteDeptById(deptId);
    }

    @Override
    public List<TreeSelect> getDeptList(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        SysUserRole userRole = userRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, userId).last("limit 1"));
        if (!Objects.isNull(userRole)) {
            SysRole sysRole = roleMapper.selectById(userRole.getRoleId());
            if (!Objects.isNull(sysRole) && sysRole.getRoleKey().equalsIgnoreCase("admin")) {
                List<SysDept> sysDepts = baseMapper.selectList(new LambdaQueryWrapper<SysDept>().in(SysDept::getLevel, 1, 2)
                        .eq(SysDept::getDelFlag, "0"));
                return ObjectUtils.isEmpty(sysDepts) ? Lists.newArrayList() :
                        buildDeptTreeSelectNoEmployeeName(sysDepts);
            }
        }
        List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                .eq(SysEmployee::getUserId, userId));
        if (ObjectUtils.isEmpty(employees)) {
            return Collections.emptyList();
        }
        List<SysDept> sysDepts = baseMapper.selectList(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, employees.stream().map(SysEmployee::getDeptId).collect(Collectors.toList()))
                .eq(SysDept::getDelFlag, 0));
        if (ObjectUtils.isEmpty(sysDepts)) {
            return Collections.emptyList();
        }
        List<Long> deptIds = Lists.newArrayList();
        sysDepts.forEach(d -> {
            String ancestors = d.getAncestors();
            String[] deptAncestors = ancestors.split(",");
            if (deptAncestors.length <= 1) {
                deptIds.add(d.getDeptId());
                List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getParentId, d.getDeptId()).eq(SysDept::getDelFlag, "0"));
                if (!ObjectUtils.isEmpty(depts)) {
                    deptIds.addAll(depts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
                }
            } else if (deptAncestors.length == 2) {
                deptIds.add(Long.parseLong(deptAncestors[1]));
                deptIds.add(d.getDeptId());
            } else {
                deptIds.add(Long.parseLong(deptAncestors[1]));
                deptIds.add(Long.parseLong(deptAncestors[2]));
            }
        });
        List<SysDept> deptList = baseMapper.selectList(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, deptIds)
                .eq(SysDept::getDelFlag, "0"));
        return ObjectUtils.isEmpty(deptList) ? Lists.newArrayList() :
                buildDeptTreeSelectNoEmployeeName(deptList);
    }

    @Override
    public UserDeptDTO userDeptList(Long userId, Long deptId) {
        SysDept sysDept = getById(deptId);
        if (Objects.isNull(sysDept)) {
            return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptIds(Lists.newArrayList()).build();
        }
        SysUserRole userRole = userRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, userId).last("limit 1"));
        if (!Objects.isNull(userRole)) {
            SysRole sysRole = roleMapper.selectById(userRole.getRoleId());
            if (!Objects.isNull(sysRole) && sysRole.getRoleKey().equalsIgnoreCase("admin")) {
                return UserDeptDTO.builder().isAdmin(Boolean.TRUE).deptType(sysDept.getDeptType()).build();
            }
        }
        UserDeptDTO result = UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).build();
        if (sysDept.getDataScopeType() == 1 || sysDept.getDataScopeType() == 2) {
            // 是总部 或 职能部门 取总部所在集团下所有组织的数据
            SysDept parentDept = baseMapper.selectOne(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId, sysDept.getParentId()).eq(SysDept::getDelFlag, "0"));
            if (Objects.isNull(parentDept)) {
                result.setDeptIds(Lists.newArrayList());
            } else {
                result.setDeptIds(getAllChildrenDeptIds(parentDept.getDeptId(), Arrays.asList(2, 3, 4)));
            }
        } else {
            // 20240901 调整为 看当前集团下所有有权限的部门
            List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, sysDept.getDeptType()).eq(SysDept::getDelFlag, "0"));
            if (ObjectUtils.isEmpty(deptList)) {
                return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(Lists.newArrayList()).build();
            }
            Map<Long, SysDept> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .eq(SysEmployee::getUserId, userId)
                    .in(SysEmployee::getDeptId, deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList())));
            if (ObjectUtils.isEmpty(employees)) {
                return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(Lists.newArrayList()).build();
            }
            Set<Long> deptIds = Sets.newHashSet();
            employees.forEach(employee -> {
                deptIds.add(employee.getDeptId());
                SysDept dept = deptMap.get(employee.getDeptId());
                if (employee.getIsLeader() && dept.getLevel() < 4) {
                    List<Integer> levels;
                    if (dept.getLevel() == 1) {
                        levels = Arrays.asList(2, 3, 4);
                    } else if (dept.getLevel() == 2) {
                        levels = Arrays.asList(3, 4);
                    } else {
                        levels = Arrays.asList(4);
                    }
                    deptIds.addAll(getAllChildrenDeptIds(employee.getDeptId(), levels));
                }
            });
            return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(deptIds.stream().distinct().collect(Collectors.toList())).build();
        }
        return result;
    }

    @Override
    public List<TreeSelect> getDeptTree(Integer deptType) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(!Objects.isNull(deptType), SysDept::getDeptType, deptType)
                .eq(SysDept::getDelFlag, "0").orderByAsc(SysDept::getOrderNum));
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
        return buildDeptTreeSelect(depts);
    }

    @Override
    public List<TreeSelect> getDeptTreeNoInfo(Integer deptType) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(!Objects.isNull(deptType), SysDept::getDeptType, deptType)
                .eq(SysDept::getDelFlag, "0").orderByAsc(SysDept::getOrderNum));
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
        return buildDeptTreeSelectNoEmployeeName(depts);
    }

    @Override
    public List<TreeSelect> getDeptTreeWithEmployeeCount(Integer deptType) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(!Objects.isNull(deptType), SysDept::getDeptType, deptType)
                .eq(SysDept::getDelFlag, "0").orderByAsc(SysDept::getOrderNum));
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
        return buildDeptTreeSelectWithEmployeeCount(depts);
    }

    @Override
    public List<TreeSelect> getRoleDeptTree(Long deptId, Integer deptType) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            return getDeptTree(deptType);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> sysDepts = baseMapper.selectBatchIds(deptDTO.getDeptIds());
                if (ObjectUtils.isEmpty(sysDepts)) {
                    return Collections.emptyList();
                }
                return buildDeptTreeSelect(sysDepts);
            }
        }
    }

    @Override
    public List<SysDept> getDeptByUserDepts(UserDeptDTO dto) {
        List<SysDept> depts;
        if (dto.getIsAdmin()) {
            // 获取所有的叶子组织
            depts = list(new LambdaQueryWrapper<SysDept>().eq(!Objects.isNull(dto.getDeptType()), SysDept::getDeptType, dto.getDeptType()).eq(SysDept::getLevel, 4).eq(SysDept::getDelFlag, "0"));
        } else {
            if (ObjectUtils.isEmpty(dto.getDeptIds())) {
                depts = Lists.newArrayList();
            } else {
                depts = baseMapper.selectBatchIds(dto.getDeptIds()).stream().filter(row -> row.getLevel() == 4).collect(Collectors.toList());
            }
        }
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
//        List<Long> parentIds = depts.stream().map(SysDept::getParentId).distinct().collect(Collectors.toList());
        List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                .in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList()))
                .exists("select 1 from sys_user su where su.user_id = sys_employee.user_id and su.`status` = '0'"));
        Map<Long, List<SysEmployee>> employesMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        return depts.stream()
                .peek(dept -> {
                    List<SysEmployee> employeeList = employesMap.getOrDefault(dept.getDeptId(), Lists.newArrayList());
                    dept.setDeptName(dept.getDeptName() + (ObjectUtils.isEmpty(employeeList) ? "" : ("(" + employeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("、")) + ")")));
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<TreeSelect> businessCompanySelect(Long businessTopDeptId) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 1).eq(SysDept::getDelFlag, 0).eq(SysDept::getIsHeadquarters, false)
                .eq(SysDept::getIsFunctional, false).in(SysDept::getLevel, 1, 2));
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
        if (!Objects.isNull(businessTopDeptId)) {
            depts = depts.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(businessTopDeptId)).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(depts)) {
                return Collections.emptyList();
            }
        }
        return buildDeptTreeSelect(depts);
    }

    @Override
    public List<TreeSelect> advisorDeptSelect(Long deptId, Long businessDeptId) {
        List<SysDept> depts = list(new QueryWrapper<SysDept>().like("concat(',', ancestors, ',')", "," + businessDeptId + ",").eq("del_flag", "0").eq("dept_type", 1));
        return buildDeptTreeSelect(depts);
//        Long userId = SecurityUtils.getUserId();
//        UserDeptDTO deptDTO = userDeptList(userId, deptId);
//        if (deptDTO.getIsAdmin()) {
//            List<SysDept> depts = list(new QueryWrapper<SysDept>().like("concat(',', ancestors, ',')", "," + businessDeptId + ",").eq("del_flag", "0").eq("dept_type", 1));
//            return buildDeptTreeSelect(depts);
//        } else {
//            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
//                return Collections.emptyList();
//            } else {
//                List<SysDept> sysDepts = list(new QueryWrapper<SysDept>().like("concat(',', ancestors, ',')", "," + businessDeptId + ",").eq("del_flag", "0").eq("dept_type", 1)
//                        .in("dept_id", deptDTO.getDeptIds()));
//                if (ObjectUtils.isEmpty(sysDepts)) {
//                    return Collections.emptyList();
//                }
//                Set<Long> ids = new HashSet<>();
//                for (SysDept sysDept : sysDepts) {
//                    ids.addAll(Arrays.stream(sysDept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toSet()));
//                }
//                List<SysDept> deptList = list(new QueryWrapper<SysDept>().eq("del_flag", "0").eq("dept_type", 1)
//                        .in("dept_id", ids));
//                return buildDeptTreeSelect(deptList);
//            }
//        }
    }

    @Override
    public List<TreeSelect> accountingDeptSelect(Long deptId, Long accountingLeaderDeptId) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 2).eq(SysDept::getIsHeadquarters, false).eq(SysDept::getDelFlag, 0).in(SysDept::getLevel, 1, 2));
            if (!Objects.isNull(accountingLeaderDeptId)) {
                depts = depts.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(accountingLeaderDeptId)).collect(Collectors.toList());
            }
            return buildDeptTreeSelect(depts);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 2).eq(SysDept::getIsHeadquarters, false).eq(SysDept::getDelFlag, 0).in(SysDept::getLevel, 1, 2)
                        .in(SysDept::getDeptId, deptDTO.getDeptIds()));
                if (ObjectUtils.isEmpty(depts)) {
                    return Collections.emptyList();
                }
                Set<Long> ids = new HashSet<>(deptDTO.getDeptIds());
                for (SysDept sysDept : depts) {
                    ids.addAll(Arrays.stream(sysDept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toSet()));
                }
                List<SysDept> deptList = list(new QueryWrapper<SysDept>().eq("del_flag", "0").eq("dept_type", 2)
                        .eq("is_headquarters", false)
                        .in("dept_id", ids).in("level", 1, 2));
                if (!Objects.isNull(accountingLeaderDeptId)) {
                    deptList = deptList.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(accountingLeaderDeptId)).collect(Collectors.toList());
                }
                return buildDeptTreeSelect(deptList);
            }
        }
    }

    @Override
    public List<TreeSelect> businessCompanySelectNoDataScope(Long businessTopDeptId) {
        return businessCompanySelect(businessTopDeptId);
    }

    @Override
    public List<TreeSelect> accountingDeptSelectNoDataScope(Long accountingLeaderDeptId) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 2).eq(SysDept::getIsHeadquarters, false)
                .eq(SysDept::getIsFunctional, false).eq(SysDept::getDelFlag, 0).in(SysDept::getLevel, 1, 2));
        if (!Objects.isNull(accountingLeaderDeptId)) {
            depts = depts.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(accountingLeaderDeptId)).collect(Collectors.toList());
        }
        return buildDeptTreeSelect(depts);
    }

    @Override
    public List<SysEmployee> getEmployeesBySecondDeptIdAndUserId(Long deptId, Long userId) {
//        UserCompany userCompany = userCompanyService.getByUserIdAndCompanyId(userId, deptId);
//        if (!Objects.isNull(userCompany)) {
//            return Collections.singletonList(new SysEmployee().setUserId(userCompany.getUserId())
//                    .setDeptId(userCompany.getCompanyId()).setEmployeeName(userCompany.getEmployeeName())
//                    .setEmployeeMobile(userCompany.getEmployeeMobile()));
//        }
        SysDept sysDept = getById(deptId);
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            return Collections.emptyList();
        }
        List<Long> childrenDeptIds = getAllChildrenDeptIds(deptId, Arrays.asList(3, 4));
        if (ObjectUtils.isEmpty(childrenDeptIds)) {
            return Collections.emptyList();
        }
        List<Long> deptIds = Lists.newArrayList(deptId);
        deptIds.addAll(childrenDeptIds);
        return employeeMapper.selectByDeptIdsAndUserIds(deptIds, userId);
    }

    @Override
    public List<TreeSelect> accountingLastDeptSelect(Long deptId) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 2).eq(SysDept::getDelFlag, 0).in(SysDept::getLevel, 1, 2, 3, 4));
            return buildDeptTreeSelect(depts);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 2).eq(SysDept::getDelFlag, 0).in(SysDept::getLevel, 1, 2, 3, 4)
                        .in(SysDept::getDeptId, deptDTO.getDeptIds()));
                return buildDeptTreeSelect(depts);
            }
        }
    }

    @Override
    public List<TreeSelect> getRoleDeptTreeNoInfo(Long deptId, Integer deptType) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            return getDeptTreeNoInfo(deptType);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> sysDepts = baseMapper.selectBatchIds(deptDTO.getDeptIds());
                if (ObjectUtils.isEmpty(sysDepts)) {
                    return Collections.emptyList();
                }
                return buildDeptTreeSelectNoEmployeeName(sysDepts);
            }
        }
    }

    @Override
    public List<TreeSelect> getRoleDeptTreeWithName(Long deptId, Integer deptType) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            return getDeptTree(deptType);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> sysDepts = baseMapper.selectBatchIds(deptDTO.getDeptIds());
                if (ObjectUtils.isEmpty(sysDepts)) {
                    return Collections.emptyList();
                }
                return buildDeptTreeSelect(sysDepts);
            }
        }
    }

    @Override
    public List<TreeSelect> getRoleDeptTreeWithCount(Long deptId, Integer deptType) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO deptDTO = userDeptList(userId, deptId);
        if (deptDTO.getIsAdmin()) {
            return getDeptTreeWithEmployeeCount(deptType);
        } else {
            if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                return Collections.emptyList();
            } else {
                List<SysDept> sysDepts = baseMapper.selectBatchIds(deptDTO.getDeptIds());
                if (ObjectUtils.isEmpty(sysDepts)) {
                    return Collections.emptyList();
                }
                return buildDeptTreeSelectWithEmployeeCount(sysDepts);
            }
        }
    }

    @Override
    public List<SysDept> getAllDeptBySecondDeptId(Long deptId) {
        if (Objects.isNull(deptId)) {
            return Collections.emptyList();
        }
        SysDept sysDept = getById(deptId);
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag()) || sysDept.getLevel() != 2) {
            return Collections.emptyList();
        }
        List<Long> deptIds = Lists.newArrayList(deptId);
        deptIds.addAll(getAllChildrenDeptIds(deptId, Arrays.asList(3, 4)));
        return baseMapper.selectBatchIds(deptIds);
    }

    @Override
    public List<TreeSelect> commonDeptTreeSelect(Long deptId, Integer deptType, Integer selectType, Integer showType, Integer level, Long businessDeptId, Long topDeptId, Integer isFilterHead) {
        List<SysDept> depts;
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<SysDept>()
                .eq(!Objects.isNull(deptType), "dept_type", deptType)
                .eq("del_flag", "0");
        Set<Long> deptIds = Sets.newHashSet();
        switch (selectType) {
            case 2:
                SysDept sysDept = getById(deptId);
                if (!Objects.isNull(sysDept) && "0".equals(sysDept.getDelFlag())) {
                    List<Long> childrenDeptIds = getAllChildrenDeptIds(deptId, Arrays.asList(3, 4));
                    List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenDeptIds)
                            .eq(SysDept::getDelFlag, "0"));
                    if (!ObjectUtils.isEmpty(deptList)) {
                        for (SysDept dept : deptList) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                    }
                }
                if (ObjectUtils.isEmpty(deptIds)) {
                    return Collections.emptyList();
                }
                break;
            case 3:
                UserDeptDTO deptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
                if (deptDTO.getIsAdmin()) {
                    break;
                }
                if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                    return Collections.emptyList();
                }
                List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, deptDTO.getDeptIds())
                        .eq(SysDept::getDelFlag, "0"));
                if (!ObjectUtils.isEmpty(deptList)) {
                    for (SysDept dept : deptList) {
                        deptIds.add(dept.getDeptId());
                        deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                    }
                }
                if (ObjectUtils.isEmpty(deptIds)) {
                    return Collections.emptyList();
                }
                break;
        }
        queryWrapper.in(!ObjectUtils.isEmpty(deptIds), "dept_id", deptIds);
        if (!Objects.isNull(level)) {
            queryWrapper.le("level", level);
        }
        if (!Objects.isNull(businessDeptId)) {
            queryWrapper.like("concat(',', ancestors, ',')", "," + businessDeptId + ",");
        }
        if (!Objects.isNull(topDeptId)) {
            queryWrapper.like("concat(',', ancestors, ',')", "," + topDeptId + ",");
        }
        if (!Objects.isNull(isFilterHead) && isFilterHead == 1) {
            queryWrapper.eq("is_headquarters", 0);
        }
        depts = list(queryWrapper);
        switch (showType) {
            case 2:
                return buildDeptTreeSelect(depts);
            case 3:
                return buildDeptTreeSelectWithEmployeeCount(depts);
            default:
                return buildDeptTreeSelectNoEmployeeName(depts);
        }
    }

    @Override
    public List<TreeSelect> commonDeptEmployeeTreeSelect(Long deptId, Integer deptType, Integer selectType, Integer showType, Integer level, Long businessDeptId, Long topDeptId, Integer isFilterHead) {
        List<SysDept> depts;
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<SysDept>()
                .eq(!Objects.isNull(deptType), "dept_type", deptType)
                .eq("del_flag", "0");
        Set<Long> deptIds = Sets.newHashSet();
        List<SysEmployee> employees = Lists.newArrayList();
        switch (selectType) {
            case 1:
                employees = employeeMapper.selectList(null);
                break;
            case 2:
                SysDept sysDept = getById(deptId);
                if (!Objects.isNull(sysDept) && "0".equals(sysDept.getDelFlag())) {
                    List<Long> childrenDeptIds = getAllChildrenDeptIds(deptId, Arrays.asList(3, 4));
                    List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenDeptIds)
                            .eq(SysDept::getDelFlag, "0"));
                    if (!ObjectUtils.isEmpty(deptList)) {
                        for (SysDept dept : deptList) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                    }
                }
                if (ObjectUtils.isEmpty(deptIds)) {
                    return Collections.emptyList();
                }
                employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                        .in(SysEmployee::getDeptId, deptIds));
                break;
            case 3:
                UserDeptDTO deptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
                if (deptDTO.getIsAdmin()) {
                    employees = employeeMapper.selectList(null);
                    break;
                }
                if (ObjectUtils.isEmpty(deptDTO.getDeptIds())) {
                    return Collections.emptyList();
                }
                List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, deptDTO.getDeptIds())
                        .eq(SysDept::getDelFlag, "0"));
                if (!ObjectUtils.isEmpty(deptList)) {
                    for (SysDept dept : deptList) {
                        deptIds.add(dept.getDeptId());
                        deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                    }
                }
                if (ObjectUtils.isEmpty(deptIds)) {
                    return Collections.emptyList();
                }
                employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                        .in(SysEmployee::getDeptId, deptDTO.getDeptIds()));
                break;
            case 4:
                SysDept currentDept = getById(deptId);
                if (!Objects.isNull(currentDept) && "0".equals(currentDept.getDelFlag())) {
                    List<SysDept> allDeptList = Lists.newArrayList();
                    if (!currentDept.getIsHeadquarters() && !currentDept.getIsFunctional()) {
                        List<Long> childrenDeptIds = getAllChildrenDeptIds(deptId, Arrays.asList(3, 4));
                        allDeptList.add(currentDept);
                        List<SysDept> companyDeptList = ObjectUtils.isEmpty(childrenDeptIds) ? Lists.newArrayList() : list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenDeptIds)
                                .eq(SysDept::getDelFlag, "0"));
                        List<SysDept> headerDeptList = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getParentId, currentDept.getParentId())
                                .eq(SysDept::getIsHeadquarters, 1)
                                .eq(SysDept::getDelFlag, "0"));
                        List<SysDept> functionalDeptList = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getParentId, currentDept.getParentId())
                                .eq(SysDept::getIsFunctional, 1)
                                .eq(SysDept::getDelFlag, "0"));
                        if (!ObjectUtils.isEmpty(companyDeptList)) {
                            allDeptList.addAll(companyDeptList);
                        }
                        if (!ObjectUtils.isEmpty(headerDeptList)) {
                            allDeptList.addAll(headerDeptList);
                            for (SysDept dept : headerDeptList) {
                                List<Long> childrenList = getAllChildrenDeptIds(dept.getDeptId(), Arrays.asList(3, 4));
                                List<SysDept> sysDeptList = ObjectUtils.isEmpty(childrenList) ? Lists.newArrayList() :
                                        list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenList)
                                                .eq(SysDept::getDelFlag, "0"));
                                if (!ObjectUtils.isEmpty(sysDeptList)) {
                                    allDeptList.addAll(sysDeptList);
                                }
                            }
                        }
                        if (!ObjectUtils.isEmpty(functionalDeptList)) {
                            allDeptList.addAll(functionalDeptList);
                            for (SysDept dept : functionalDeptList) {
                                List<Long> childrenList = getAllChildrenDeptIds(dept.getDeptId(), Arrays.asList(3, 4));
                                List<SysDept> sysDeptList = ObjectUtils.isEmpty(childrenList) ? Lists.newArrayList() :
                                        list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenList)
                                                .eq(SysDept::getDelFlag, "0"));
                                if (!ObjectUtils.isEmpty(sysDeptList)) {
                                    allDeptList.addAll(sysDeptList);
                                }
                            }
                        }
                    } else {
                        List<SysDept> sysDeptList = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getParentId, currentDept.getParentId())
                                .eq(SysDept::getDelFlag, "0"));
                        if (!ObjectUtils.isEmpty(sysDeptList)) {
                            allDeptList.addAll(sysDeptList);
                            for (SysDept dept : sysDeptList) {
                                List<Long> childrenList = getAllChildrenDeptIds(dept.getDeptId(), Arrays.asList(3, 4));
                                List<SysDept> sysDepts = ObjectUtils.isEmpty(childrenList) ? Lists.newArrayList() :
                                        list(new LambdaQueryWrapper<SysDept>().in(SysDept::getDeptId, childrenList)
                                                .eq(SysDept::getDelFlag, "0"));
                                if (!ObjectUtils.isEmpty(sysDepts)) {
                                    allDeptList.addAll(sysDepts);
                                }
                            }
                        }
                    }
                    if (!ObjectUtils.isEmpty(allDeptList)) {
                        for (SysDept dept : allDeptList) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                    }
                }
                if (ObjectUtils.isEmpty(deptIds)) {
                    return Collections.emptyList();
                }
                employees = employeeMapper.selectList(null);
                break;
        }
        queryWrapper.in(!ObjectUtils.isEmpty(deptIds), "dept_id", deptIds);
        if (!Objects.isNull(level)) {
            queryWrapper.le("level", level);
        }
        if (!Objects.isNull(businessDeptId)) {
            queryWrapper.like("concat(',', ancestors, ',')", "," + businessDeptId + ",");
        }
        if (!Objects.isNull(topDeptId)) {
            queryWrapper.like("concat(',', ancestors, ',')", "," + topDeptId + ",");
        }
        if (!Objects.isNull(isFilterHead) && isFilterHead == 1) {
            queryWrapper.eq("is_headquarters", 0);
        }
        depts = list(queryWrapper);
        Map<Long, List<SysEmployee>> employeeMap = employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        switch (showType) {
            case 2:
                return buildDeptEmployeeTreeSelect(depts, employeeMap);
            case 3:
                return buildDeptEmployeeTreeSelectWithEmployeeCount(depts, employeeMap);
            default:
                return buildDeptEmployeeTreeSelectNoEmployeeName(depts, employeeMap);
        }
    }

    private List<TreeSelect> buildDeptEmployeeTreeSelectNoEmployeeName(List<SysDept> depts, Map<Long, List<SysEmployee>> employeeMap) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(t -> new TreeSelect(t, employeeMap, true)).collect(Collectors.toList());
    }

    private List<TreeSelect> buildDeptEmployeeTreeSelectWithEmployeeCount(List<SysDept> depts, Map<Long, List<SysEmployee>> employeeMap) {
        List<SysEmployee> employees = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(depts)) {
            employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList())));
        }
        Map<Long, List<SysEmployee>> employesMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(t -> new TreeSelect(t, employesMap, employeeMap, 1)).collect(Collectors.toList());
    }

    private List<TreeSelect> buildDeptEmployeeTreeSelect(List<SysDept> depts, Map<Long, List<SysEmployee>> employeeMap) {
        List<SysEmployee> employees = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(depts)) {
            employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList())));
        }
        Map<Long, List<SysEmployee>> employesMap = ObjectUtils.isEmpty(employees) ? Maps.newHashMap() :
                employees.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(t -> new TreeSelect(t, employesMap, employeeMap)).collect(Collectors.toList());
    }

    @Override
    public List<Long> getAllChildrenIdByTopDeptId(Long deptId) {
        if (Objects.isNull(deptId)) {
            return Collections.emptyList();
        }
        SysDept sysDept = getById(deptId);
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            return Collections.emptyList();
        }
        if (sysDept.getLevel() == 4) {
            return Collections.singletonList(deptId);
        }
        List<Integer> levels = Lists.newArrayList();
        for (int i = sysDept.getLevel() + 1; i < 5; i++) {
            levels.add(i);
        }
        List<Long> deptIds = Lists.newArrayList(deptId);
        deptIds.addAll(getAllChildrenDeptIds(deptId, levels));
        return deptIds;
    }

    @Override
    public List<Long> selectDeptIdsByDeptEmployeeName(String deptEmployeeName) {
        if (StringUtils.isEmpty(deptEmployeeName)) {
            return Collections.emptyList();
        }
        Set<Long> deptIds = Sets.newHashSet();
        List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>().like(SysEmployee::getEmployeeName, deptEmployeeName));
        if (!ObjectUtils.isEmpty(employees)) {
            deptIds.addAll(employees.stream().map(SysEmployee::getDeptId).distinct().collect(Collectors.toList()));
        }
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().like(SysDept::getDeptName, deptEmployeeName).eq(SysDept::getDelFlag, "0"));
        if (!ObjectUtils.isEmpty(depts)) {
            for (SysDept sysDept : depts) {
                deptIds.add(sysDept.getDeptId());
                if (sysDept.getLevel() == 4) {
                    continue;
                }
                List<Integer> levels = Lists.newArrayList();
                for (int i = sysDept.getLevel() + 1; i < 5; i++) {
                    levels.add(i);
                }
                deptIds.addAll(getAllChildrenDeptIds(sysDept.getDeptId(), levels));
            }
        }
        return new ArrayList<>(deptIds);
    }

    @Override
    public List<SysDept> getDeptOrderByName() {
        List<SysDept> depts = list();
        return depts.stream().sorted(Comparator.comparing(SysDept::getDeptName)).collect(Collectors.toList());
    }

    @Override
    public List<String> getAllUserDeptNames(SysUser user) {
        List<SysDept> depts = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDelFlag, "0"));
        Map<Long, SysDept> deptMap = depts.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        if (user.isAdmin()) {
            List<SysDept> lastLevelDepts = depts.stream().filter(d -> d.getLevel() == 4).collect(Collectors.toList());
            return lastLevelDepts.stream().map(dept -> {
                List<String> deptIdPath = new ArrayList<>(Arrays.asList(dept.getAncestors().split(",")));
                deptIdPath.remove("0");
                String allPathDeptName = "";
                for (String deptId : deptIdPath) {
                    allPathDeptName += deptMap.get(Long.parseLong(deptId)).getDeptName() + "/";
                }
                allPathDeptName += dept.getDeptName();
                return allPathDeptName;
            }).collect(Collectors.toList());
        } else {
            List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>().eq(SysEmployee::getUserId, user.getUserId()));
            if (ObjectUtils.isEmpty(employees)) {
                return Lists.newArrayList();
            }
            List<String> deptNames = Lists.newArrayList();
            for (SysEmployee employee : employees) {
                SysDept dept = deptMap.get(employee.getDeptId());
                if (!employee.getIsLeader()) {
                    List<String> deptIdPath = new ArrayList<>(Arrays.asList(dept.getAncestors().split(",")));
                    deptIdPath.remove("0");
                    StringBuilder allPathDeptName = new StringBuilder();
                    for (String deptId : deptIdPath) {
                        allPathDeptName.append(deptMap.get(Long.parseLong(deptId)).getDeptName()).append("/");
                    }
                    allPathDeptName.append(dept.getDeptName());
                    if (!deptNames.contains(allPathDeptName.toString())) {
                        deptNames.add(allPathDeptName.toString());
                    }
                } else {
                    List<SysDept> childrenDepts = depts.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(dept.getDeptId()) && d.getLevel() == 4).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(childrenDepts)) {
                        for (SysDept childrenDept : childrenDepts) {
                            List<String> deptIdPath = new ArrayList<>(Arrays.asList(childrenDept.getAncestors().split(",")));
                            deptIdPath.remove("0");
                            StringBuilder allPathDeptName = new StringBuilder();
                            for (String deptId : deptIdPath) {
                                allPathDeptName.append(deptMap.get(Long.parseLong(deptId)).getDeptName()).append("/");
                            }
                            allPathDeptName.append(childrenDept.getDeptName());
                            if (!deptNames.contains(allPathDeptName.toString())) {
                                deptNames.add(allPathDeptName.toString());
                            }
                        }
                    }
                }
            }
            return deptNames;
        }
    }

    @Override
    public List<SysDept> getByParentId(Long deptId) {
        if (Objects.isNull(deptId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getParentId, deptId).eq(SysDept::getDelFlag, "0"));
    }

    @Override
    public IPage<DeptAccountBalanceDTO> deptAccountBalance(Long deptId, String queryDeptId, Integer pageNum, Integer pageSize) {
        IPage<DeptAccountBalanceDTO> result = new Page<>(pageNum, pageSize);
        List<Long> businessDeptIds = Lists.newArrayList();
        UserDeptDTO userDeptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
        if (!userDeptDTO.getIsAdmin()) {
            SysDept currentDept = getById(deptId);
            if (!currentDept.getIsHeadquarters()) {
                if (ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                    return result;
                } else {
                    businessDeptIds = userDeptDTO.getDeptIds();
                }
            } else {
                if (currentDept.getDeptType() == 1) {
                    if (ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        return result;
                    } else {
                        businessDeptIds = userDeptDTO.getDeptIds();
                    }
                }
            }
        }
        LambdaQueryWrapper<SysDept> queryWrapper = new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, 1)
                .eq(SysDept::getDelFlag, 0).eq(SysDept::getIsHeadquarters, false).eq(SysDept::getLevel, 2)
                .in(!ObjectUtils.isEmpty(businessDeptIds), SysDept::getDeptId, businessDeptIds)
                .orderByAsc(SysDept::getOrderNum).orderByAsc(SysDept::getDeptId);
        if (!StringUtils.isEmpty(queryDeptId)) {
            queryWrapper.in(SysDept::getDeptId, StringUtils.stringToLongList(queryDeptId));
        }
//        if (!Objects.isNull(queryDeptId)) {
//            SysDept sysDept = getById(queryDeptId);
//            if (!Objects.isNull(sysDept) && Objects.equals("0", sysDept.getDelFlag()) && (sysDept.getLevel() == 1 || sysDept.getLevel() == 2)) {
//                if (sysDept.getLevel() == 2) {
//                    queryWrapper.eq(SysDept::getDeptId, queryDeptId);
//                } else {
//                    List<SysDept> children = list(new LambdaQueryWrapper<SysDept>()
//                            .eq(SysDept::getParentId, queryDeptId)
//                            .eq(SysDept::getDelFlag, 0)
//                            .eq(SysDept::getLevel, 2)
//                            .eq(SysDept::getDeptType, 1)
//                            .eq(SysDept::getIsHeadquarters, false)
//                            .select(SysDept::getDeptId));
//                    if (ObjectUtils.isEmpty(children)) {
//                        return result;
//                    }
//                    queryWrapper.in(SysDept::getDeptId, children.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
//                }
//            }
//        }
        IPage<SysDept> iPage = page(new Page<>(pageNum, pageSize), queryWrapper);
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            Map<Long, String> deptNameMap = list(new LambdaQueryWrapper<SysDept>()
                    .eq(SysDept::getDelFlag, "0")
                    .in(SysDept::getDeptId, iPage.getRecords().stream().map(SysDept::getParentId).distinct().collect(Collectors.toList()))).stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            result.setRecords(iPage.getRecords().stream().map(d -> DeptAccountBalanceDTO.builder()
                    .businessDeptId(d.getDeptId())
                    .businessDeptName(d.getDeptName())
                    .businessTopDeptName(deptNameMap.getOrDefault(d.getParentId(), ""))
                    .accountBalance(d.getAccountBalance())
                    .build()).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void allot(DeptAccountBalanceAllotVO vo) {

        SysDept sysDept = getById(vo.getBusinessDeptId());
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            throw new ServiceException("业务公司不存在");
        }
        if (Objects.equals(vo.getAllotType(), AllotType.OUT.getCode()) && sysDept.getAccountBalance().compareTo(vo.getAmount()) < 0) {
            throw new ServiceException("余额不足");
        }
        update(new LambdaUpdateWrapper<SysDept>().eq(SysDept::getDeptId, vo.getBusinessDeptId()).set(SysDept::getAccountBalance, Objects.equals(AllotType.OUT.getCode(), vo.getAllotType()) ? sysDept.getAccountBalance().subtract(vo.getAmount()) : sysDept.getAccountBalance().add(vo.getAmount())));
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        Long userId = SecurityUtils.getUserId();
        List<SysEmployee> employees = getEmployeesBySecondDeptIdAndUserId(deptId, userId);
        String employeeName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        sysDeptAccountBalanceDetailService.createBalanceDetail(vo, employeeName, 1);
    }

    @Override
    public UserDeptDTO workOrderUserDeptList(Long userId, Long deptId) {
        SysDept sysDept = getById(deptId);
        if (Objects.isNull(sysDept)) {
            return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptIds(Lists.newArrayList()).build();
        }
        SysUserRole userRole = userRoleMapper.selectOne(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getUserId, userId).last("limit 1"));
        if (!Objects.isNull(userRole)) {
            SysRole sysRole = roleMapper.selectById(userRole.getRoleId());
            if (!Objects.isNull(sysRole) && sysRole.getRoleKey().equalsIgnoreCase("admin")) {
                return UserDeptDTO.builder().isAdmin(Boolean.TRUE).deptType(sysDept.getDeptType()).build();
            }
        }
        UserDeptDTO result = UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).build();
        if (sysDept.getDataScopeType() == 1) {
            // 是总部 或 职能部门 取总部所在集团下所有组织的数据
            SysDept parentDept = baseMapper.selectOne(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptId, sysDept.getParentId()).eq(SysDept::getDelFlag, "0"));
            if (Objects.isNull(parentDept)) {
                result.setDeptIds(Lists.newArrayList());
            } else {
                result.setDeptIds(getAllChildrenDeptIds(parentDept.getDeptId(), Arrays.asList(2, 3, 4)));
            }
        } else {
            // 20240901 调整为 看当前集团下所有有权限的部门
            List<SysDept> deptList = list(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDeptType, sysDept.getDeptType()).eq(SysDept::getDelFlag, "0"));
            if (ObjectUtils.isEmpty(deptList)) {
                return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(Lists.newArrayList()).build();
            }
            Map<Long, SysDept> deptMap = deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            List<SysEmployee> employees = employeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .eq(SysEmployee::getUserId, userId)
                    .in(SysEmployee::getDeptId, deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList())));
            if (ObjectUtils.isEmpty(employees)) {
                return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(Lists.newArrayList()).build();
            }
            Set<Long> deptIds = Sets.newHashSet();
            employees.forEach(employee -> {
                deptIds.add(employee.getDeptId());
                SysDept dept = deptMap.get(employee.getDeptId());
                if (employee.getIsLeader() && dept.getLevel() < 4) {
                    List<Integer> levels;
                    if (dept.getLevel() == 1) {
                        levels = Arrays.asList(2, 3, 4);
                    } else if (dept.getLevel() == 2) {
                        levels = Arrays.asList(3, 4);
                    } else {
                        levels = Arrays.asList(4);
                    }
                    deptIds.addAll(getAllChildrenDeptIds(employee.getDeptId(), levels));
                }
            });
            return UserDeptDTO.builder().isAdmin(Boolean.FALSE).deptType(sysDept.getDeptType()).deptIds(deptIds.stream().distinct().collect(Collectors.toList())).build();
        }
        return result;
    }

    @Override
    public List<TreeSelect> deptTreeSelect(Long deptId, DeptTreeRequest request) {
        // 查询符合条件的部门
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<SysDept>()
                .eq("del_flag", "0")
                .le(!Objects.isNull(request.getEndLevel()), "level", request.getEndLevel())
                .ge(!Objects.isNull(request.getStartLevel()), "level", request.getStartLevel())
                .eq(!Objects.isNull(request.getDeptType()), "dept_type", request.getDeptType());
        if (!Objects.isNull(request.getFilterHeadquarters()) && request.getFilterHeadquarters()) {
            queryWrapper.eq("is_headquarters", false);
        }
        if (!Objects.isNull(request.getFilterFunction()) && request.getFilterFunction()) {
            queryWrapper.eq("is_functional", false);
        }
        if (!Objects.isNull(request.getTopDeptId())) {
            queryWrapper.and(wrapper -> wrapper.eq("dept_id", request.getTopDeptId())
                    .or().like("concat(',', ancestors, ',')", "," + request.getTopDeptId() + ","));
        }
        if (!Objects.isNull(request.getDataScopeType())) {
            SysDept sysDept = getById(deptId);
            if (request.getDataScopeType() == 2) {
                if (sysDept.getDataScopeType() == 1 || sysDept.getDataScopeType() == 2) {
                    queryWrapper.eq("dept_type", sysDept.getDeptType());
                    queryWrapper.and(wrapper -> wrapper.eq("dept_id", sysDept.getParentId())
                            .or().like("concat(',', ancestors, ',')", "," + sysDept.getParentId() + ","));
                } else if (sysDept.getDataScopeType() == 3) {
                    UserDeptDTO userDeptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
                    if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        return Lists.newArrayList();
                    }
                    if (!ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        Set<Long> deptIds = new HashSet<>();
                        List<SysDept> depts = deptMapper.selectBatchIds(userDeptDTO.getDeptIds());
                        for (SysDept dept : depts) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                        queryWrapper.in("dept_id", deptIds);
                        queryWrapper.eq("dept_type", sysDept.getDeptType());
                    }
                }
            }
        }
        List<SysDept> deptList = deptMapper.selectList(queryWrapper);
        if (ObjectUtils.isEmpty(deptList)) {
            return Lists.newArrayList();
        }
        // 查询所有员工
        LambdaQueryWrapper<SysEmployee> employeeLambdaQueryWrapper = new LambdaQueryWrapper<SysEmployee>().in(SysEmployee::getDeptId, deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        if (!Objects.isNull(request.getIsShowInValidEmployee()) && !request.getIsShowInValidEmployee()) {
            employeeLambdaQueryWrapper.inSql(SysEmployee::getUserId, "select user_id from sys_user where `status` = 0");
        }
        List<SysEmployee> employeeList = employeeMapper.selectList(employeeLambdaQueryWrapper);

        // 构建部门与员工的映射关系
        Map<Long, List<SysEmployee>> employeesMap = employeeList.stream()
                .collect(Collectors.groupingBy(SysEmployee::getDeptId));

        // 动态确定根节点
        Set<Long> allDeptIds = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        List<SysDept> rootDepts;
        if (request.getStartLevel() == null) {
            // 如果未传递 startLevel，则根节点为 parentId = 0L 的部门
            rootDepts = deptList.stream()
                    .filter(dept -> dept.getParentId() == 0L)
                    .collect(Collectors.toList());
        } else {
            // 如果传递了 startLevel，则根节点为 level = startLevel 且其父部门不在当前列表中的部门
            rootDepts = deptList.stream()
                    .filter(dept -> dept.getLevel() == request.getStartLevel())
                    .filter(dept -> !allDeptIds.contains(dept.getParentId()))
                    .collect(Collectors.toList());
        }

        // 构建部门树
        return buildDeptTree(deptList, rootDepts, employeesMap, request.getShowEmployees());
    }

    @Override
    public List<TreeSelect> deptEmployeeTreeSelect(Long deptId, DeptEmployeeTreeRequest request) {
        // 查询符合条件的部门
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<SysDept>()
                .eq("del_flag", "0")
                .le(!Objects.isNull(request.getEndLevel()), "level", request.getEndLevel())
                .ge(!Objects.isNull(request.getStartLevel()), "level", request.getStartLevel())
                .eq(!Objects.isNull(request.getDeptType()), "dept_type", request.getDeptType());
        if (!Objects.isNull(request.getFilterHeadquarters()) && request.getFilterHeadquarters()) {
            queryWrapper.eq("is_headquarters", false);
        }
        if (!Objects.isNull(request.getTopDeptId())) {
            queryWrapper.and(wrapper -> wrapper.eq("dept_id", request.getTopDeptId())
                    .or().like("concat(',', ancestors, ',')", "," + request.getTopDeptId() + ","));
        }
        if (!Objects.isNull(request.getDataScopeType())) {
            SysDept sysDept = getById(deptId);
            if (request.getDataScopeType() == 2) {
                if (sysDept.getDataScopeType() == 1 || sysDept.getDataScopeType() == 2) {
                    queryWrapper.eq("dept_type", sysDept.getDeptType());
                    queryWrapper.and(wrapper -> wrapper.eq("dept_id", sysDept.getParentId())
                            .or().like("concat(',', ancestors, ',')", "," + sysDept.getParentId() + ","));
                } else if (sysDept.getDataScopeType() == 3) {
                    UserDeptDTO userDeptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
                    if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        return Lists.newArrayList();
                    }
                    if (!ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        Set<Long> deptIds = new HashSet<>();
                        List<SysDept> depts = deptMapper.selectBatchIds(userDeptDTO.getDeptIds());
                        for (SysDept dept : depts) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                        queryWrapper.in("dept_id", deptIds);
                        queryWrapper.eq("dept_type", sysDept.getDeptType());
                    }
                }
            }
        }
        List<SysDept> deptList = deptMapper.selectList(queryWrapper);
        if (ObjectUtils.isEmpty(deptList)) {
            return Lists.newArrayList();
        }
        // 查询所有员工
        LambdaQueryWrapper<SysEmployee> employeeLambdaQueryWrapper = new LambdaQueryWrapper<SysEmployee>().in(SysEmployee::getDeptId, deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        if (!Objects.isNull(request.getIsShowInValidEmployee()) && !request.getIsShowInValidEmployee()) {
            employeeLambdaQueryWrapper.inSql(SysEmployee::getUserId, "select user_id from sys_user where `status` = 0");
        }
        List<SysEmployee> employeeList = employeeMapper.selectList(employeeLambdaQueryWrapper);

        // 构建部门与员工的映射关系
        Map<Long, List<SysEmployee>> employeesMap = employeeList.stream()
                .collect(Collectors.groupingBy(SysEmployee::getDeptId));

        // 动态确定根节点
        Set<Long> allDeptIds = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        List<SysDept> rootDepts;
        if (request.getStartLevel() == null) {
            // 如果未传递 startLevel，则根节点为 parentId = 0L 的部门
            rootDepts = deptList.stream()
                    .filter(dept -> dept.getParentId() == 0L)
                    .collect(Collectors.toList());
        } else {
            // 如果传递了 startLevel，则根节点为 level = startLevel 且其父部门不在当前列表中的部门
            rootDepts = deptList.stream()
                    .filter(dept -> dept.getLevel() == request.getStartLevel())
                    .filter(dept -> !allDeptIds.contains(dept.getParentId()))
                    .collect(Collectors.toList());
        }

        // 构建部门树
        return buildDeptEmployeeTree(deptList, rootDepts, employeesMap);
    }

    @Override
    public List<SysEmployee> deptEmployeeSelect(Long deptId, DeptEmployeeRequest request) {
        QueryWrapper<SysDept> queryWrapper = new QueryWrapper<SysDept>()
                .eq("del_flag", "0");
        queryWrapper.and(wrapper -> wrapper.eq("dept_id", request.getTopDeptId())
                .or().like("concat(',', ancestors, ',')", "," + request.getTopDeptId() + ","));
        if (!Objects.isNull(request.getDataScopeType())) {
            if (request.getDataScopeType() == 2) {
                SysDept sysDept = getById(deptId);
                if (sysDept.getDataScopeType() == 1 || sysDept.getDataScopeType() == 2) {
                    queryWrapper.eq("dept_type", sysDept.getDeptType());
                    queryWrapper.and(wrapper -> wrapper.eq("dept_id", sysDept.getParentId())
                            .or().like("concat(',', ancestors, ',')", "," + sysDept.getParentId() + ","));
                } else if (sysDept.getDataScopeType() == 3) {
                    UserDeptDTO userDeptDTO = userDeptList(SecurityUtils.getUserId(), deptId);
                    if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        return Lists.newArrayList();
                    }
                    if (!ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
                        Set<Long> deptIds = new HashSet<>();
                        List<SysDept> depts = deptMapper.selectBatchIds(userDeptDTO.getDeptIds());
                        for (SysDept dept : depts) {
                            deptIds.add(dept.getDeptId());
                            deptIds.addAll(Arrays.stream(dept.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()));
                        }
                        queryWrapper.in("dept_id", deptIds);
                        queryWrapper.eq("dept_type", sysDept.getDeptType());
                    }
                }
            }
        }
        List<SysDept> depts = list(queryWrapper);
        if (ObjectUtils.isEmpty(depts)) {
            return Collections.emptyList();
        }
        // 查询所有员工
        LambdaQueryWrapper<SysEmployee> employeeLambdaQueryWrapper = new LambdaQueryWrapper<SysEmployee>().in(SysEmployee::getDeptId, depts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        if (!Objects.isNull(request.getIsShowInValidEmployee()) && !request.getIsShowInValidEmployee()) {
            employeeLambdaQueryWrapper.inSql(SysEmployee::getUserId, "select user_id from sys_user where `status` = 0");
        }
        return employeeMapper.selectList(employeeLambdaQueryWrapper);
    }

    @Override
    public IPage<DeptDTO> deptList(String deptName, Long parentDeptId, Integer pageNum, Integer pageSize) {
        IPage<DeptDTO> result = new Page<>();
        IPage<SysDept> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDelFlag, "0").like(!StringUtils.isEmpty(deptName), SysDept::getDeptName, deptName)
                .eq(!Objects.isNull(parentDeptId), SysDept::getParentId, parentDeptId)
                .in(SysDept::getLevel, 1, 2)
                .orderByDesc(SysDept::getDeptId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<Long> parentIds = iPage.getRecords().stream().map(SysDept::getParentId).filter(id -> !Objects.equals(id, 0L)).distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = baseMapper.selectBatchIds(parentIds).stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            result.setRecords(iPage.getRecords().stream().map(row -> DeptDTO.builder()
                    .id(row.getDeptId())
                    .deptName(row.getDeptName())
                    .parentDeptId(row.getParentId())
                    .parentDeptName(deptNameMap.getOrDefault(row.getParentId(), ""))
                    .deptType(row.getDeptType())
                    .deptTypeStr(DeptTypeEnum.getNameByCode(row.getDeptType()))
                    .dataScopeType(row.getDataScopeType())
                    .dataScopeTypeStr(DataScopeTypeEnum.getNameByCode(row.getDataScopeType()))
                    .remark(row.getRemark())
                    .build()).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 递归构建部门树
     */
    private List<TreeSelect> buildDeptTree(List<SysDept> deptList, List<SysDept> rootDepts,
                                           Map<Long, List<SysEmployee>> employeesMap,
                                           Boolean showEmployees) {
        return rootDepts.stream()
                .map(rootDept -> {
                    TreeSelect treeSelect = new TreeSelect();
                    treeSelect.setId(rootDept.getDeptId());
                    treeSelect.setLabel(rootDept.getDeptName());

                    // 如果需要显示员工名
                    if (Boolean.TRUE.equals(showEmployees)) {
                        List<SysEmployee> employees = employeesMap.getOrDefault(rootDept.getDeptId(), Collections.emptyList());
//                        treeSelect.setEmployees(employees);
                        if (!employees.isEmpty()) {
                            String employeeNames = employees.stream()
                                    .map(SysEmployee::getEmployeeName)
                                    .collect(Collectors.joining("，"));
                            treeSelect.setLabel(rootDept.getDeptName() + " (" + employeeNames + ")");
                        }
                    }

                    // 递归构建子节点
                    List<SysDept> children = deptList.stream()
                            .filter(dept -> dept.getParentId().equals(rootDept.getDeptId()))
                            .collect(Collectors.toList());
                    treeSelect.setChildren(buildDeptTree(deptList, children, employeesMap, showEmployees));
                    return treeSelect;
                })
                .collect(Collectors.toList());
    }

    /**
     * 递归构建部门树
     */
    private List<TreeSelect> buildDeptEmployeeTree(List<SysDept> deptList, List<SysDept> rootDepts,
                                           Map<Long, List<SysEmployee>> employeesMap) {
        return rootDepts.stream()
                .map(rootDept -> {
                    TreeSelect treeSelect = new TreeSelect();
                    treeSelect.setId(rootDept.getDeptId());
                    treeSelect.setLabel(rootDept.getDeptName());

                    List<SysEmployee> employees = employeesMap.getOrDefault(rootDept.getDeptId(), Collections.emptyList());
                    treeSelect.setEmployees(employees);

                    // 递归构建子节点
                    List<SysDept> children = deptList.stream()
                            .filter(dept -> dept.getParentId().equals(rootDept.getDeptId()))
                            .collect(Collectors.toList());
                    treeSelect.setChildren(buildDeptEmployeeTree(deptList, children, employeesMap));
                    return treeSelect;
                })
                .collect(Collectors.toList());
    }

    private List<Long> getAllChildrenDeptIds(Long deptId) {
        List<SysDept> deptList = baseMapper.selectList(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDelFlag, "0"));
        return deptList.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(deptId)).map(SysDept::getDeptId).collect(Collectors.toList());
    }

    private List<Long> getAllChildrenDeptIds(Long deptId, List<Integer> levels) {
        List<SysDept> deptList = baseMapper.selectList(new LambdaQueryWrapper<SysDept>().eq(SysDept::getDelFlag, "0")
                .in(!ObjectUtils.isEmpty(levels), SysDept::getLevel, levels));
        return deptList.stream().filter(d -> Arrays.stream(d.getAncestors().split(",")).map(Long::parseLong).collect(Collectors.toList()).contains(deptId)).map(SysDept::getDeptId).collect(Collectors.toList());
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t)
    {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t)
    {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext())
        {
            SysDept n = (SysDept) it.next();
//            List<SysEmployee> employees = employesMap.getOrDefault(n.getDeptId(), Lists.newArrayList());
//            n.setEmployeeNames(ObjectUtils.isEmpty(employees) ? "" : employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("、")));
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
